<template>
    <div class="desktop-monitor" :style="{
        ...window_style,
        perspective: activeId === 1  ? '1000px' : 'none'
    }">
        <div class="dragLayer-locking" v-show="dragLayer_locking_show" :style="{'-webkit-app-region':dragLayer_locking_show ? 'drag':'no-drag'}">
            <div class="dragLayer1">
                <div class="drag">
                    <img src="./assets/img_drag.png" alt=""/>
                    <p>Currently dragging</p>
                </div>
            </div>
        </div>
        <img id="bgImg" :style="bg_img_style" v-if="window_settings.background.img && window_settings.background.type !== 'color'" :src="window_settings.background.img" alt="">
        <video id="bgVideo" :style="bg_video_style" v-if="window_settings.background.video && window_settings.background.type !== 'color'" :src="window_settings.background.video" muted loop autoplay></video>


        <div class="Screensize" :style="{width:window_settings.size.w+'px',height:window_settings.size.h+'px',
        //  transform: activeId === 1 ? 'translateY(-10px) rotateX(0deg) rotateY(-15deg)' : ''
          ...(activeId === 1 ? {
               transform: 'translateY(-10px) rotateX(0deg) rotateY(-15deg)',
               top: '30px',
               left: '-20px'
             } : {})
         }"  ref="Screensize1">
            <div v-for="(sensor, index) in sensors"  :key="index"
            :style="store.getWrapperStyle(sensor, index)"
            @mousedown="startDrag($event,  sensor.id)"
            @mouseup="stopDrag"
            @click="handleComponentClick(index,sensor)"
            >
            <MonitoringModule
                :nums="sensor.nums"
                :textShadow="store.getTextShadow(sensor)"
                :value="sensorValues[sensor.id]"
                :processvalue="sensorRawValues[sensor.id]"
                :text="sensor.remark"
                :unit='sensor.unit'
                :mediaSrc="sensor.mediaSrc"
                :custStyle="store.getModuleStyle(sensor)"
                :unitshow="sensor.unitshow"
                :show="sensor.showDetails"
                :sensor="sensor"
                :processedSvg="sensor.processedSvg"
                :is-hovered="hoveredId === sensor.id"
                :is-selected="selectedId === sensor.id"
                :switchcolorsshow="sensor.Switchcolorsshow"
                :transformShow ="sensor.transformShow"
                :customClass="sensor.class"
                :parameters="seriesData[sensor.id]"
                :id="sensor.id"
                @component-enter="handleComponentEnter"
                @component-leave="handleComponentLeave"
            >
            </MonitoringModule>
            </div>
        </div>


    </div>
</template>

<script setup lang="ts">
import {computed, onBeforeMount, reactive, ref,onMounted , onUnmounted } from "vue";
import {defalutDatas} from "@/modules/Game_DesktopMonitorSet/utils/monitor_config";
import idb from "@/uitls/indexedDB";
import useSensorData from '../Game_DesktopMonitorSet/shared/useSensorData'
import MonitoringModule from '@/components/MonitoringModule/Newmonitoring.vue'
import { storeToRefs } from 'pinia'
//for test
const store = useSensorData()
const {sensors,sensorValues,sensorRawValues,seriesData} = storeToRefs(store)
console.log(sensorValues.value,'sensors1111111111',seriesData.value)
const isDragging = ref(false)
const hasDragged = ref(false)
const dragStartX = ref(0)
const dragStartY = ref(0)
const initialLeft = ref(0)
const initialTop = ref(0)
const hoveredId = ref<Number | null>(null)
const selectedId = ref<Number | null>(null)
const Screensize1 = ref<HTMLElement | null>(null)
// @ts-ignore
const gamepp: any = window.gamepp;
const dragLayer_locking_show = ref(false);
const activeId = ref(-1)
const window_settings = reactive({
    background: {
        type: "color",
        color: "#000000",
        img: "",
        video: "",
        img_video_display_type: "填充", // 拉伸/填充/适应
        opacity: 1,
    },
    size: {
        w: 600,
        h: 600,
    },
    font: {
        font_family: "Microsoft Yahei",
    },
    position: {
        screen: "",  // 屏幕
        position: "upperRight",  // 位置
        x: -1,
        y: -1,
    }
})
const activeMonitorId2 = ref(1)
onMounted (async () => {
    const MonitorId2 = localStorage.getItem('activeMonitorId2')
    if(MonitorId2){
        activeMonitorId2.value = parseInt(MonitorId2)
    }
    console.log(activeId.value)
    const savedId = localStorage.getItem('activeMonitorId');
    console.log(savedId,'activeMonitorId')
    if (savedId) {
        activeId.value = parseInt(savedId);
        store.initializeSensors(activeId.value);
    }else{
        activeId.value = 1
        store.initializeSensors(1)
        localStorage.setItem('activeMonitorId', '1')
    }
    gamepp.webapp.onInternalAppEvent.addEventListener(async (value:any) => {
        console.log(value)
        if(value.type === 'hoveredId'){
            hoveredId.value = value.data;
        }else if(value === 'hoveredIdfalse'){
            hoveredId.value = null
        }
        if(value.type === 'selectedId'){
            selectedId.value = value.data;
            isDragging.value = true
            hasDragged.value = false
            sensors.value.forEach((sensor:any)=>{
                if(sensor.id === value.data){
                    sensor.style.zIndex = value.Zindex
                }else{
                    activeId.value = Number(localStorage.getItem('activeMonitorId'));
                    console.log(activeId.value,'activeId')
                    const sensorSettingsData = localStorage.getItem(`sensorSettings_${activeId.value}`);
                    if (sensorSettingsData) {
                        const sensorSettings = JSON.parse(sensorSettingsData);
                        sensorSettings.forEach((item:any)=>{
                            if(item.id === sensor.id){
                                sensor.style.zIndex = item.style.zIndex
                            }
                        })
                    }
                }
            })
        }
        else if(value === 'selectedIdfalse'){
            selectedId.value = null
            // 恢复层级关系
            activeId.value = Number(localStorage.getItem('activeMonitorId'));
            const sensorSettingsData = localStorage.getItem(`sensorSettings_${activeId.value}`);
            if (sensorSettingsData) {
                const sensorSettings = JSON.parse(sensorSettingsData);
                sensorSettings.forEach((item:any)=>{
                    sensors.value.forEach((sensor:any)=>{
                        if(item.id === sensor.id){
                            sensor.style.zIndex = item.style.zIndex
                        }
                    })
                //   sensors.value = item
                })
            }
        }
        if(value === 'setInnerFixed'){
            dragLayer_locking_show.value = false
        }
        else if(value === 'setInnerFixedcolse'){
            dragLayer_locking_show.value = true
            let handle = await gamepp.dialog.showdeskMonitor.promise();
            await gamepp.moveDestopWindow.promise(handle);

        }
    })
})

window.addEventListener('storage', (event) => {
    activeId.value = Number(localStorage.getItem('activeMonitorId'));
    if (activeId.value) {
        const currentStorageKey = `sensorSettings_${activeId.value}`;
        if (event.key === currentStorageKey) {
            console.log('监听的的:', currentStorageKey);
            try {
                sensors.value = JSON.parse(event.newValue || '[]');
            } catch (error) {
            }
        }
    }
    if(event.key === 'activeMonitorId'){
        console.log('监听的的111:', event.newValue);
        const activeMonitorId =Number(event.newValue);
        store.initializeSensors(activeMonitorId);
    }

    if(event.key === 'activeMonitorId2'){
        activeMonitorId2.value = Number(event.newValue)
    }
});

onUnmounted(() => {
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
})

const handleComponentEnter = (sensor:any)=> {
      hoveredId.value = sensor.id;
      const obj = {
        type: 'hoveredId',
        data: hoveredId.value,
    }
    gamepp.webapp.sendInternalAppEvent.promise('desktop_monitor_setting', obj);
}
const handleComponentLeave=() => {
    hoveredId.value = null;
    // gamepp.webapp.sendInternalAppEvent.promise('desktop_monitor_setting', 'hoveredIdfalse');
}
const startDrag = (event: MouseEvent, sensorId: Number) => {
  isDragging.value = true
  hasDragged.value = false
  const sensor = sensors.value.find((s: { id: Number; }) => s.id === sensorId)
  if (!sensor || selectedId.value !== sensor.id) return

  dragStartX.value = event.clientX
  dragStartY.value = event.clientY
  initialLeft.value = Number(sensor.style?.left) || 0
  initialTop.value = Number(sensor.style?.top) || 0

  document.addEventListener('mousemove', handleDrag)
  document.addEventListener('mouseup', stopDrag)
}

const handleDrag = (event: MouseEvent) => {
//   if (!isDragging.value) return
  const sensor = sensors.value.find((s: { id: Number; }) => s.id === selectedId.value)
  if (!sensor) return

  hasDragged.value = true
  const dx = event.clientX - dragStartX.value
  const dy = event.clientY - dragStartY.value

  // 边界检测
  const ulElement = Screensize1.value
  if (!ulElement) return
  const ulRect = ulElement.getBoundingClientRect()
  if (
    event.clientY < ulRect.top ||
    event.clientY > ulRect.bottom ||
    event.clientX < ulRect.left ||
    event.clientX > ulRect.right
  ) {
    stopDrag()
    return
  }

  // 更新传感器位置
  const newLeft = initialLeft.value + dx
  const newTop = initialTop.value + dy

  sensor.style = {
    ...sensor.style,
    left: newLeft,
    top: newTop
  }

  localStorage.setItem('sensorDragData', JSON.stringify({
    id: sensor.id,
    top: newTop,
    left: newLeft
  }))
}

const stopDrag = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
  if(!hasDragged.value) {
    return;
  }
 localStorage.setItem(`sensorSettings_${activeId.value}`, JSON.stringify(sensors.value))
}
const originalZIndices = reactive<Record<number, number>>({})
const handleComponentClick = (index: any,sensor: any)=> {
    console.log('组件被点击了', hasDragged.value);
    if (isDragging.value || hasDragged.value) return;
      const obj = {
        type: 'selectedId',
        sensor: sensor,
        index: index,
        TimeStands:'notAllowed'
    }
    gamepp.webapp.sendInternalAppEvent.promise('desktop_monitor_setting', JSON.parse(JSON.stringify(obj)));

    if (selectedId.value === sensor.id) {
        sensor.style.zIndex = originalZIndices[sensor.id] // 恢复原始值
        selectedId.value = null
        return
    }
  // 恢复前一个选中元素的 zIndex
  if (selectedId.value !== null) {
    const prevSensor = sensors.value.find((s: { id: { toString: (radix?: number) => string; toFixed: (fractionDigits?: number) => string; toExponential: (fractionDigits?: number) => string; toPrecision: (precision?: number) => string; valueOf: () => number; toLocaleString: { (locales?: string | string[], options?: Intl.NumberFormatOptions): string; (locales?: Intl.LocalesArgument, options?: Intl.NumberFormatOptions): string; }; } | null; }) => s.id === selectedId.value)
    if (prevSensor) {
      prevSensor.style.zIndex = originalZIndices[prevSensor.id]
    }
  }
  // 保存当前元素的原始 zIndex
  if (!(sensor.id in originalZIndices)) {
    originalZIndices[sensor.id] = sensor.style.zIndex
  }
  sensor.style.zIndex = 999
    //   this.handleSelect(index,sensor);
}

function hexToRgba(hex:string, opacity:number) {
    // 移除 # 号（如果存在）
    hex = hex.replace('#', '');

    // 解析红、绿、蓝值
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    // 返回 rgba 格式
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
}
const window_style = computed(() => {
    if (window_settings.background.type === "color") {
        const bg_color_with_opacity = window_settings.background.color;
        return {
            background: bg_color_with_opacity,
            width: window_settings.size.w + 'px',
            height: window_settings.size.h + 'px',
            fontFamily: window_settings.font.font_family,
        }
    }
    return {
        width: window_settings.size.w + 'px',
        height: window_settings.size.h + 'px',
        fontFamily: window_settings.font.font_family,
    }
})

const bg_img_style = computed(()=>{
    const obj:any = {
        opacity: window_settings.background.opacity,
        objectFit: "",
    }
    const img_video_display_type = window_settings.background.img_video_display_type
    if (img_video_display_type === "拉伸") {
        obj.objectFit = "fill"
    }else if (img_video_display_type === "适应") {
        obj.objectFit = "contain"
    }else if (img_video_display_type === "填充") {
        obj.objectFit = "cover"
    }
    return obj
})

const bg_video_style = computed(()=>{
    const obj:any = {
        opacity: window_settings.background.opacity,
        objectFit: "",
    }
    const img_video_display_type = window_settings.background.img_video_display_type
    if (img_video_display_type === "拉伸") {
        obj.objectFit = "fill"
    }else if (img_video_display_type === "适应") {
        obj.objectFit = "contain"
    }else if (img_video_display_type === "填充") {
        obj.objectFit = "cover"
    }
    return obj
})

onBeforeMount(async () => {
    gamepp.setting.onConfigChanged.addEventListener((type:any,id:number, value:any) => {ConfigChanged(type, id, value);});
    let is_lock = await gamepp.setting.getInteger.promise(311);
    let oled_switch = await gamepp.setting.getInteger.promise(495);
    let oled_minute = await gamepp.setting.getInteger.promise(496);
    let handle = await gamepp.dialog.showdeskMonitor.promise();
    if (!handle) return;
    if (is_lock === 0) {
        dragLayer_locking_show.value = true
        await gamepp.moveDestopWindow.promise(handle);
    } else {
        dragLayer_locking_show.value = false
        //使窗口不受WIN+D 最小化
        await gamepp.displayDestopWindow.promise(handle);
    }

    //兼容腾讯桌面助手
    await gamepp.utils.setDeskMonitorReg.promise()

    await checkLocal()
    setMonitorPosition()
    const  SensorInfoStr = gamepp.hardware.getSensorInfo.sync()
    if(SensorInfoStr){
        document.body.style.display = 'block'
    }
    window.addEventListener('storage', async (evt) => {
        if (evt.key === 'indexedDBChange') {
            await checkLocal()
            setMonitorPosition()
        } else if (evt.key === 'activeMonitorId') {
            await checkLocal()
            setMonitorPosition()
        }
    })
})

function ConfigChanged(type:any,id:number, value:any) {
    //桌面监控解锁锁定
    if (id === 311) {
        if (value === 0) {
            //解锁
            dragLayer_locking_show.value = true
        } else {
            //锁定
            dragLayer_locking_show.value = false
            gamepp.webapp.windows.setIgnoreMouseEvent.promise('desktop_monitor',true,{"forward":false});
        }
    }
}

async function checkLocal() {
    const localMonitorId = window.localStorage.getItem("activeMonitorId")
    if (localMonitorId) {
        activeId.value = Number(localMonitorId)
    } else {
        activeId.value = 1;
    }
    const monitor_data = defalutDatas.find((item) => {
        return item.id === activeId.value
    })
    if (monitor_data) {
        Object.assign(window_settings, monitor_data.window_settings)
        console.log('window_size', window_settings.size)
        const res = await idb.getItem(`custom_monitor_window_setting_${activeId.value}`)
        if (res) {
            Object.assign(window_settings, res)
        }
        gamepp.webapp.windows.resize.sync("desktop_monitor", window_settings.size.w, window_settings.size.h)
    }else{
        const res = await idb.getItem(`custom_monitor_window_setting_${activeId.value}`)
        if (res) {
            Object.assign(window_settings, res)
        }
        gamepp.webapp.windows.resize.sync("desktop_monitor", window_settings.size.w, window_settings.size.h)
    }
}

function setMonitorPosition() {
    let XPosition = window_settings.position.x;
    let YPosition = window_settings.position.y;
    let PositionStr = window_settings.position.position;
    if ((XPosition === -1 || XPosition === 0) && (YPosition === -1 || YPosition === 0)) {
        let IsPrimaryScreen = true
        // 非主屏幕的下x和y
        let SecondaryXPosition = 0;
        let SecondaryYPosition = 0;
        let SecondaryScreenWidth = 0;
        let SecondaryScreenHeight = 0;

        const displayCardInfo = (gamepp.hardware.getDisplayCardInfo.sync()).Element ?? [];
        if (window_settings.position.screen) {
            const displayCardInfo_ = displayCardInfo.find((item: any) => {
                return item.MonitorModal === window_settings.position.screen
            })
            if (displayCardInfo_) {
                // 选择了非主屏幕
                if (displayCardInfo_.IsPrimaryScreen !== 1) {
                    IsPrimaryScreen = false;
                    SecondaryXPosition = displayCardInfo_.x;
                    SecondaryYPosition = displayCardInfo_.y;
                    SecondaryScreenWidth = displayCardInfo_.PelsWidth
                    SecondaryScreenHeight = displayCardInfo_.PelsHeight
                }
            }
        }
        if (IsPrimaryScreen) {
            setPosition(PositionStr);
        }else{
            setPositionForSecondaryScreen(PositionStr,SecondaryXPosition,SecondaryYPosition,SecondaryScreenWidth,SecondaryScreenHeight);
        }
    }else{
        gamepp.webapp.windows.setPositionDesktopMonitor.sync("desktop_monitor", XPosition, YPosition, 2);
    }
}

async function setPosition(site: string) {
    //主屏幕宽高
    let PrimaryDisplay = gamepp.getPrimaryDisplayBounds.sync();
    //获取当前监控宽高
    let PresentBounds = gamepp.webapp.windows.getBounds.sync('desktop_monitor');
    if (!PresentBounds) {
        await gamepp.webapp.windows.close.promise();
        await gamepp.webapp.windows.show.promise();
        return false;
    }
    let DefaultWidth = Number(PrimaryDisplay['width'] - PresentBounds['width']);
    if (DefaultWidth < 0) {
        DefaultWidth = 0;
    }
    let DefaultHeight = Number(PrimaryDisplay['height'] - PresentBounds['height']);
    let halfWidth = Math.floor(Number(PrimaryDisplay['width'] / 2 - PresentBounds['width'] / 2));
    let halfHeight = Math.floor(Number(PrimaryDisplay['height'] / 2 - PresentBounds['height'] / 2));
    let x = 0, y = 0;
    switch (site) {
        case 'upperLeft':
            break;
        case 'upper':
            x = halfWidth;
            break;
        case 'upperRight':
            x = DefaultWidth;
            break;
        case 'left':
            y = halfHeight;
            break;
        case 'middle':
            x = halfWidth;
            y = halfHeight;
            break;
        case 'right':
            x = DefaultWidth;
            y = halfHeight;
            break;
        case 'lowerLeft':
            y = DefaultHeight - 40;
            break;
        case 'lower':
            x = halfWidth;
            y = DefaultHeight - 40;
            break;
        case 'lowerRight':
            x = DefaultWidth;
            y = DefaultHeight - 40;
            break;
        default:
            break;
    }
    //设置位置
    gamepp.webapp.windows.setPositionDesktopMonitor.sync("desktop_monitor", x, y, 1);
}

async function setPositionForSecondaryScreen(site: string, x1: number, y1: number,ScreenWidth: number,ScreenHeight: number) {
    //获取当前监控宽高
    let PresentBounds = gamepp.webapp.windows.getBounds.sync('desktop_monitor');
    if (!PresentBounds) {
        await gamepp.webapp.windows.close.promise();
        await gamepp.webapp.windows.show.promise();
        return false;
    }
    let DefaultWidth = Number(ScreenWidth - PresentBounds['width']);
    if (DefaultWidth < 0) {
        DefaultWidth = 0;
    }
    let DefaultHeight = Number(ScreenHeight - PresentBounds['height']);
    let halfWidth = Math.floor(Number(ScreenWidth / 2 - PresentBounds['width'] / 2));
    let halfHeight = Math.floor(Number(ScreenHeight / 2 - PresentBounds['height'] / 2));
    let x = 0, y = 0;
    switch (site) {
        case 'upperLeft':
            break;
        case 'upper':
            x = halfWidth;
            break;
        case 'upperRight':
            x = DefaultWidth;
            break;
        case 'left':
            y = halfHeight;
            break;
        case 'middle':
            x = halfWidth;
            y = halfHeight;
            break;
        case 'right':
            x = DefaultWidth;
            y = halfHeight;
            break;
        case 'lowerLeft':
            y = DefaultHeight - 40;
            break;
        case 'lower':
            x = halfWidth;
            y = DefaultHeight - 40;
            break;
        case 'lowerRight':
            x = DefaultWidth;
            y = DefaultHeight - 40;
            break;
        default:
            break;
    }
    //设置位置
    gamepp.webapp.windows.setPositionDesktopMonitor.sync("desktop_monitor", Math.floor(x+x1), Math.floor(y+y1), 1);
}
</script>

<style scoped lang="scss">
.desktop-monitor {
    width: 600px;
    height: 600px;
    overflow: hidden;
    position: absolute;

    #bgImg,#bgVideo {
        width: 100%;
        height: 100%;
    }
    .dragLayer1{background: rgba(0, 0, 0, .3);}
    .dragLayer1,.dragLayer-locking{
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        cursor: pointer;
        z-index: 999;
    }
    .drag {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        color: #fff;
    }
    .Screensize{
        position: absolute;
        top: 0;
    }
}
</style>
<style>
body {
    display: none;
}
</style>
