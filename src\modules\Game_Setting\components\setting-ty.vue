<template>
  <!--通用设置-->
  <div id="ty"></div>
  <!-- 通用设置 -->
  <el-collapse-item :title="$t('Setting.GeneralSetting')" name="1">
    <div class="setting-item">
      <section class="left-box">
        <!--软件版本-->
        <p class="setting-item-title">{{$t('Setting.softwareVersion')}}</p>
        <div class="flex-row-center">
          <!--检查更新-->
          <el-button
            type="primary"
            style="margin-right: 20px"
            @click="checkUpdate"
            v-show="!needUpdate"
            >{{$t('Setting.checkForUpdates')}}
          </el-button>
          <!--立即更新-->
          <div class="red-dot" v-show="needUpdate"></div>
          <el-button
            type="primary"
            style="margin-right: 20px"
            @click="updateGamepp"
            v-show="needUpdate"
            >{{$t('Setting.updateNow')}}
          </el-button>
          <!--当前版本-->
          <text>{{$t('Setting.currentVersion')}}：</text>
          <span>V{{ currentVersion }}</span>
          <div class="loading" v-show="loading_icon">
            <span class="iconfont icon-holding"></span>
          </div>
          <div v-show="needUpdate" style="margin-left: 10px;">
            <!--最新版本-->
            <text>{{$t('Setting.latestVersion')}}：</text>
            <span>V{{ UpdateInfoManaul }}</span>
          </div>
          <div v-show="isChecked" style="margin-left: 10px;">
            <!--当前版本已经是最新版本-->
            <text>{{$t('Setting.isLatestVersion')}}</text>
          </div>
        </div>

        <!--功能模块更新-->
        <p class="setting-item-title mt-25">{{$t('Setting.functionModuleUpdate')}}</p>
        <el-checkbox
            v-model="alwaysUpdateModules"
            @change="changeAlwaysUpdateModules"
        >
          <!--保持所有已经安装功能模块为最新版本-->
          <span style="font-size: 0.12rem">{{$t('Setting.alwaysUpdateModules')}}</span>
        </el-checkbox>

        <!--语言-->
        <p class="setting-item-title mt-25">{{$t('Setting.lang')}}</p>
        <el-select
          v-model="store.state.lang"
          @change="changeLang"
          filterable
          popper-class="storelang"
          style="width: 230px"
        >
        <template #header>
          <div>
            <el-input
              v-model="searchKeyword"
              :placeholder="$t('Setting.searchlanguage')"
              clearable
              size="small"
              @input="handleSearch"
            />
          </div>
        </template>
        <el-option
          v-for="option in filteredOptions"
          :key="option.value"
          :label="option.label"
          :value="option.value"
        ></el-option>
        </el-select>
      </section>
      <section class="right-box">
        <!--开机自启-->
        <p class="setting-item-title">{{$t('Setting.bootstrap')}}</p>
        <div class="flex-row-center">
          <el-checkbox
            v-model="store.state.startWithWindowState"
            @change="setStartWithWindowState"
          ></el-checkbox>
          <!--开机-->
          <span style="margin: 0 10px">{{$t('Setting.powerOn_m1')}}</span>
          <el-input-number
            v-model="store.state.startWithWindowDelay"
            :controls="false"
            :step="1"
            style="width: 55px"
            :min="1"
            :max="999"
            @change="setStartWithWindowState"
          ></el-input-number>
          <!--秒后自动启动-->
          <span style="margin: 0 10px">{{$t('Setting.powerOn_m2')}}</span>
          <!--默认为40秒-->
          <text>{{$t('Setting.defaultDelay')}}</text>
        </div>
        <el-checkbox v-model="store.state.scaleWithSystem" @change="store.actions.setScaleWithSystem" style="margin-top: 15px;">
          <!--跟随系统缩放-->
          <span style="font-size: .12rem">{{$t('Setting.followSystemScale')}}</span>
        </el-checkbox>
        <!--隐私设置-->
        <p class="setting-item-title mt-25">{{$t('Setting.privacySettings')}}</p>
        <el-checkbox
            v-model="store.state.CM_SETTING_EXPERIENCE_IMPROVE_SWITCH"
            @change="change266"
        >
          <!--加入GamePP用户体验改善计划-->
          <span style="font-size: 0.12rem">{{$t('Setting.JoinGamePPPlan')}}</span>
        </el-checkbox>
      </section>
    </div>
  </el-collapse-item>
</template>

<script setup lang="ts">
import { ref,onMounted, computed } from "vue";
import { gameppBaseSetting } from "@/modules/Game_Home/stores/index";
import { useScroll } from "../hooks/useScroll";
import { GPP_SendStatics } from "@/uitls/sendstatics"
import {useI18n} from "vue-i18n";
const { locale } = useI18n({ useScope: 'global' })
useScroll("ty");
const store = gameppBaseSetting();
const {} = useI18n()
let loading_icon = ref(false);
let needUpdate = ref(false);
let UpdateInfoManaul = ref("");
let currentVersion = ref("");
let isChecked = ref(false);
let alwaysUpdateModules = ref(false)

onMounted(() => {
  try {
    currentVersion.value = gamepp.getPlatformVersion.sync()
    checkAlwaysUpdateModules()
    const needUpdate = window.localStorage.getItem('needCheckUpdate')
    if (needUpdate === '1') checkUpdate()
    window.localStorage.removeItem('needCheckUpdate')
  }catch (e) {

  }
});

const allOptions = [
  { label: 'zh_CN_简体中文', value: 'CN' },
  { label: 'zh_HK_繁體中文', value: 'ZH' },
  { label: 'en_US_English', value: 'EN' },
  { label: 'ja_JP_日本語です', value: 'JA' },
  { label: 'cs_CZ_Čeština', value: 'CS' },
  { label: 'da_DK_Dansk', value: 'DA' },
  { label: 'de_DE_Deutsch', value: 'DE' },
  { label: 'el_GR_Ελληνικά', value: 'EL' },
  { label: 'es_ES_español', value: 'ES' },
  { label: 'fi_FI_Suomi', value: 'FI' },
  { label: 'fr_FR_Français', value: 'FR' },
  { label: 'ar_AR_العربية', value: 'AR' },
  { label: 'hu_HU_Magyar', value: 'HU' },
  { label: 'id_ID_Indonesia', value: 'ID' },
  { label: 'it_IT_Italiano', value: 'IT' },
  { label: 'ko_KR_한국어', value: 'KR' },
  { label: 'ms_MY_Malaysia', value: 'MS' },
  { label: 'nl_NL_Nederlands', value: 'NL' },
  { label: 'no_NO_Norsk', value: 'NO' },
  { label: 'pl_PL_Polski', value: 'PL' },
  { label: 'pt_PT_Português', value: 'PT' },
  { label: 'ru_RU_Русский', value: 'RU' },
  { label: 'sv_SE_Svenska', value: 'SV' },
  { label: 'th_TH_ไทย', value: 'TH' },
  { label: 'tl_PH_Pilipino', value: 'TL' },
  { label: 'tr_TR_Türkçe', value: 'TR' },
  { label: 'uk_UA_Українська', value: 'UK' },
  { label: 'vi_VN_Tiếng Việt', value: 'VI' }
]
const searchKeyword = ref('')
const filteredOptions = ref([...allOptions]);

// const filterOptions = (query: string) => {
//   searchKeyword.value = query
// }

const handleSearch = () => {
  if (!searchKeyword.value) {
    filteredOptions.value = [...allOptions];
    return;
  }
  
  filteredOptions.value = allOptions.filter(option => 
    option.label.toLowerCase().includes(searchKeyword.value.toLowerCase())
  );
};

function checkAlwaysUpdateModules() {
  const localData = window.localStorage.getItem('alwaysUpdateModules')
  if (localData) {
    alwaysUpdateModules.value = JSON.parse(localData)
  }
}

function changeAlwaysUpdateModules () {
  if (alwaysUpdateModules.value) {
    GPP_SendStatics(100704)
  }
  window.localStorage.setItem('alwaysUpdateModules', JSON.stringify(alwaysUpdateModules.value))
}

const changeLang = (value: string) => {
  if (value === 'CN') {
    GPP_SendStatics(100711)
  }
  if (value === 'AR') {
    document.body.setAttribute('dir', 'rtl');
  } else {
    document.body.removeAttribute('dir');
  }
  
  locale.value = value.toLowerCase()
  store.actions.changeLang(value);
};

const change266 = function (v: any) {
  if (typeof v == "boolean")
    store.actions.setCM_SETTING_EXPERIENCE_IMPROVE_SWITCH(v);
};

const setStartWithWindowState = function () {
  if (store.state.startWithWindowState) {
    GPP_SendStatics(100702)
  }else{
    GPP_SendStatics(100703)
  }
  store.actions.setStartWithWindowState(
    store.state.startWithWindowState,
    store.state.startWithWindowDelay
  );
};

function doTry(func: Function, ...args: any[]) {
  try {
    func(...args);
  } catch (e) {
    console.log("%c err: ", "background:red;font-size:16px", e);
  }
}
function updateGamepp () {
  gamepp.update.rebootToUpdate.promise()
}

async function checkUpdate() {
  GPP_SendStatics(100701)
  loading_icon.value = true;
  setTimeout(() => {
    doTry(async() => {
      const nUpdateStatus = await gamepp.update.checkUpdate.promise(2);
      // const nUpdateStatus = 0
      console.log("nUpdateStatus", nUpdateStatus);
      if (nUpdateStatus === 1) {
        //更新主程序
        // let UpdateInfoManaul =
        UpdateInfoManaul.value = await gamepp.update.getUpdateInfoManaul.promise();
        loading_icon.value = false
        needUpdate.value = true
      } else if (nUpdateStatus === 0) {
        isChecked.value = true
        //无需更新
        loading_icon.value = false
        needUpdate.value = false
      }
    });
  }, 200);
}
</script>

<style scoped lang="scss">
.red-dot {
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background: red;
    position: absolute;
    transform: translate(80px, -9px)
}
.loading {
  animation: loadingAni 3s infinite linear;
  color: #777777;
  display: inline-block;
  font-size: 0.14rem;
  transform-origin: center;
  margin-left: 5px;
}

@keyframes loadingAni {
  0% {
    color: antiquewhite;
    transform: rotate(0deg);
  }
  100% {
    color: aqua;
    transform: rotate(360deg);
  }
}

</style>
<style lang="scss">
.storelang{
  .el-select-dropdown__header{
    border: 1px solid transparent!important;
    // padding: 5px!important;
  }
  .el-input--small .el-input__wrapper{
    padding: 5px 7px!important;
  }
}

</style>
