import { createApp } from 'vue'
import { createPinia } from 'pinia'
import new_component from './new_component.vue';

import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
import '../Game_Home/reset.css'
import './Game_DMComponent_RTL.scss'
import '../../assets/iconfont/iconfont.css'
import '../../assets/iconfont/iconfont.js'
import i18n from '../../assets/lang'
const pinia = createPinia()
const app = createApp(new_component)

app.use(pinia)
app.use(i18n)
app.use(ElementPlus)
app.mount('#new_component')