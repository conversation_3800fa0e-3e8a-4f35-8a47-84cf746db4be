// vite.config.ts
import { defineConfig } from "file:///E:/desktop/WA-GamePP-MainUI-VUE3/node_modules/vite/dist/node/index.js";
import vue from "file:///E:/desktop/WA-GamePP-MainUI-VUE3/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import path from "path";
import AutoImport from "file:///E:/desktop/WA-GamePP-MainUI-VUE3/node_modules/unplugin-auto-import/dist/vite.js";
import Components from "file:///E:/desktop/WA-GamePP-MainUI-VUE3/node_modules/unplugin-vue-components/dist/vite.js";
import Icons from "file:///E:/desktop/WA-GamePP-MainUI-VUE3/node_modules/unplugin-icons/dist/vite.js";
import IconsResolver from "file:///E:/desktop/WA-GamePP-MainUI-VUE3/node_modules/unplugin-icons/dist/resolver.js";
import { ElementPlusResolver } from "file:///E:/desktop/WA-GamePP-MainUI-VUE3/node_modules/unplugin-vue-components/dist/resolvers.js";
import fs from "fs";
var __vite_injected_original_dirname = "E:\\desktop\\WA-GamePP-MainUI-VUE3";
var npm_config_page = process.env.npm_config_page || "";
var npm_config_base_folder = process.env.npm_config_base_folder || "";
var getEnterPages = () => {
  if (!npm_config_page) {
    return null;
  }
  const modulesPath = path.resolve(__vite_injected_original_dirname, `src/modules/${npm_config_page}/index.html`);
  const ingamePath = path.resolve(__vite_injected_original_dirname, `src/inGame/${npm_config_page}/index.html`);
  const DeskPath = path.resolve(__vite_injected_original_dirname, `src/onDesk/${npm_config_page}/index.html`);
  if (fs.existsSync(modulesPath)) {
    return {
      [npm_config_page]: modulesPath
    };
  } else if (fs.existsSync(ingamePath)) {
    return {
      [npm_config_page]: ingamePath
    };
  } else if (fs.existsSync(DeskPath)) {
    return {
      [npm_config_page]: DeskPath
    };
  } else {
  }
};
var getPageRoot = (pageName) => {
  const modulesPath = path.resolve(__vite_injected_original_dirname, `src/modules/${npm_config_page}/index.html`);
  const ingamePath = path.resolve(__vite_injected_original_dirname, `src/inGame/${npm_config_page}/index.html`);
  const DeskPath = path.resolve(__vite_injected_original_dirname, `src/onDesk/${npm_config_page}/index.html`);
  if (fs.existsSync(ingamePath)) {
    return path.resolve(__vite_injected_original_dirname, `./src/inGame/${pageName}`);
  } else if (fs.existsSync(modulesPath)) {
    return path.resolve(__vite_injected_original_dirname, `./src/modules/${pageName}`);
  } else if (fs.existsSync(DeskPath)) {
    return path.resolve(__vite_injected_original_dirname, `./src/onDesk/${pageName}`);
  } else {
    return null;
  }
};
var vite_config_default = defineConfig({
  root: getPageRoot(process.env.npm_config_page || ""),
  base: "./",
  envDir: path.resolve(__vite_injected_original_dirname),
  //用于加载 .env 文件的目录。可以是一个绝对路径，也可以是相对于项目根的路径。
  plugins: [
    vue(),
    AutoImport({
      // 自动导入 Vue 相关函数，如：ref, reactive, toRef 等
      imports: ["vue", "vue-router"],
      dts: path.resolve(__vite_injected_original_dirname, "./auto-import.d.ts"),
      eslintrc: {
        enabled: false,
        // 是否自动生成 eslint 规则，建议生成之后设置 false
        filepath: path.resolve(__vite_injected_original_dirname, "./.eslintrc-auto-import.json"),
        // 指定自动导入函数 eslint 规则的文件
        globalsPropValue: true
      },
      resolvers: [
        IconsResolver({ prefix: "Icon" }),
        // 自动导入图标组件
        ElementPlusResolver()
        // // 自动导入 Element Plus 相关函数，如：ElMessage, ElMessageBox...
      ]
      // vueTemplate: true,
      // dts: false, // 配置文件生成位置(false:关闭自动生成) 关闭dev时自动生成components.d.ts
      // dts: path.resolve(pathSrc, "types", "auto-imports.d.ts"), // 指定自动导入函数TS类型声明文件路径
    }),
    Components({
      resolvers: [
        IconsResolver({ enabledCollections: ["ep"] }),
        // 自动注册图标组件
        ElementPlusResolver()
        // 自动导入 Element Plus 组件
      ],
      dirs: ["src/**/components"],
      // 指定自定义组件位置(默认:src/components)
      dts: false
      // 配置文件位置(false:关闭自动生成)
      // dts: path.resolve(pathSrc, "types", "components.d.ts"), // 指定自动导入组件TS类型声明文件路径
    }),
    Icons({
      autoInstall: true
    })
    // gzip格式
    // compression({
    //   threshold: 1024 * 1000, // 体积大于 threshold 才会被压缩,单位 b
    //   ext: '.gz', // 压缩文件格式
    //   deleteOriginFile: true // 是否删除源文件
    // }),
  ],
  css: {
    preprocessorOptions: {
      scss: {}
    }
  },
  resolve: {
    alias: {
      "@": path.join(__vite_injected_original_dirname, "./src"),
      "@modules": path.join(__vite_injected_original_dirname, "./src/modules")
    }
  },
  server: {
    host: "localhost",
    // 指定服务器主机名
    port: 8080,
    // 指定服务器端口
    hmr: true,
    // 开启热更新
    open: true,
    // 在服务器启动时自动在浏览器中打开应用程序
    https: false
    // 是否开启 https
  },
  build: {
    // sourcemap: true,//开发调试时使用打印对应文件行数，提交打包注销此行
    outDir: path.resolve(__vite_injected_original_dirname, `webapp/windows/pages/${npm_config_page}`),
    // 指定输出路径
    assetsInlineLimit: 4096,
    //小于此阈值的导入或引用资源将内联为 base64 编码，以避免额外的 http 请求
    emptyOutDir: true,
    //Vite 会在构建时清空该目录
    cssCodeSplit: false,
    terserOptions: {
      compress: {
        keep_infinity: true,
        // 防止 Infinity 被压缩成 1/0，这可能会导致 Chrome 上的性能问题
        drop_console: true,
        // 生产环境去除 console
        drop_debugger: true
        // 生产环境去除 debugger
      },
      format: {
        comments: false
        // 删除注释
      }
    },
    rollupOptions: {
      //自定义底层的 Rollup 打包配置
      input: getEnterPages(),
      // buildEnd: buildEndFn(npm_config_page),
      output: {
        assetFileNames: (assetInfo) => {
          console.log("assetsInfo:", assetInfo);
          const { name } = assetInfo;
          if (name.endsWith(".css")) {
            if (npm_config_page) {
              return `css/${npm_config_page}.css`;
            }
            return "[ext]/[name].css";
          }
          return "[ext]/[name].[ext]";
        },
        //静态文件输出的文件夹名称
        // chunkFileNames: 'js/[name].js',  //chunk包输出的文件夹名称
        // cssFileName:'css/[name].css',
        entryFileNames: "js/[name].js",
        //入口文件输出的文件夹名称
        compact: true
        // manualChunks: (id) => {
        //   if (id.includes('node_modules')) {
        //     return id
        //         .toString()
        //         .split('node_modules/')[1]
        //         .split('/')[0]
        //         .toString() // 拆分多个vendors
        //   }
        // }
      }
    }
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZS5jb25maWcudHMiXSwKICAic291cmNlc0NvbnRlbnQiOiBbImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCJFOlxcXFxkZXNrdG9wXFxcXFdBLUdhbWVQUC1NYWluVUktVlVFM1wiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9maWxlbmFtZSA9IFwiRTpcXFxcZGVza3RvcFxcXFxXQS1HYW1lUFAtTWFpblVJLVZVRTNcXFxcdml0ZS5jb25maWcudHNcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfaW1wb3J0X21ldGFfdXJsID0gXCJmaWxlOi8vL0U6L2Rlc2t0b3AvV0EtR2FtZVBQLU1haW5VSS1WVUUzL3ZpdGUuY29uZmlnLnRzXCI7XHJcbi8vIGltcG9ydCBmcyBmcm9tIFwiZnNcIjtcclxuaW1wb3J0IHsgZGVmaW5lQ29uZmlnIH0gZnJvbSAndml0ZSdcclxuaW1wb3J0IHZ1ZSBmcm9tICdAdml0ZWpzL3BsdWdpbi12dWUnXHJcbmltcG9ydCBwYXRoLCB7cmVzb2x2ZX0gZnJvbSAncGF0aCdcclxuaW1wb3J0IGNvbXByZXNzaW9uIGZyb20gJ3ZpdGUtcGx1Z2luLWNvbXByZXNzaW9uJyAvL2d6aXAvYnIgXHU1MzhCXHU3RjI5XHJcbmltcG9ydCBBdXRvSW1wb3J0IGZyb20gJ3VucGx1Z2luLWF1dG8taW1wb3J0L3ZpdGUnIC8vIFx1ODFFQVx1NTJBOFx1NUYxNVx1NTE2NVxyXG5pbXBvcnQgQ29tcG9uZW50cyBmcm9tICd1bnBsdWdpbi12dWUtY29tcG9uZW50cy92aXRlJyAvLyBcdTdFQzRcdTRFRjZcdTgxRUFcdTUyQThcdTVGMTVcdTUxNjVcclxuaW1wb3J0IEljb25zIGZyb20gJ3VucGx1Z2luLWljb25zL3ZpdGUnXHJcbmltcG9ydCBJY29uc1Jlc29sdmVyIGZyb20gJ3VucGx1Z2luLWljb25zL3Jlc29sdmVyJ1xyXG5pbXBvcnQgeyBFbGVtZW50UGx1c1Jlc29sdmVyIH0gZnJvbSAndW5wbHVnaW4tdnVlLWNvbXBvbmVudHMvcmVzb2x2ZXJzJ1xyXG5cclxuLy8gaW1wb3J0IG9iZnVzY2F0b3IgZnJvbSAncm9sbHVwLXBsdWdpbi1vYmZ1c2NhdG9yJztcclxuLy8gaW1wb3J0IGNoYWxrIGZyb20gJ2NoYWxrJyAvLyBjb25zb2xlXHU5QUQ4XHU0RUFFXHJcbmltcG9ydCAgZnMgZnJvbSAnZnMnO1xyXG5cclxuLy8gXHU4M0I3XHU1M0Q2bnBtIHJ1biBkZXZcdTU0MEVcdTdGMDAgXHU5MTREXHU3RjZFXHU3Njg0XHU3M0FGXHU1ODgzXHU1M0Q4XHU5MUNGXHJcbmNvbnN0IG5wbV9jb25maWdfcGFnZSA9IHByb2Nlc3MuZW52Lm5wbV9jb25maWdfcGFnZSB8fCAnJ1xyXG5jb25zdCBucG1fY29uZmlnX2Jhc2VfZm9sZGVyID0gcHJvY2Vzcy5lbnYubnBtX2NvbmZpZ19iYXNlX2ZvbGRlciB8fCAnJztcclxuLy8gXHU1NDdEXHU0RUU0XHU4ODRDXHU2MkE1XHU5NTE5XHU2M0QwXHU3OTNBXHJcbi8vIGNvbnN0IGVycm9yTG9nID0gKGVycm9yOiBzdHJpbmcpID0+IGNvbnNvbGUubG9nKGNoYWxrLnJlZChgJHtlcnJvcn1gKSlcclxuXHJcblxyXG4vL1x1ODNCN1x1NTNENlx1NjMwN1x1NUI5QVx1NzY4NFx1NTM1NVx1OTg3NVx1OTc2Mlx1NTE2NVx1NTNFM1xyXG5jb25zdCBnZXRFbnRlclBhZ2VzID0gKCkgPT4ge1xyXG4gIGlmICghbnBtX2NvbmZpZ19wYWdlKSB7XHJcbiAgICAvLyBlcnJvckxvZyhcclxuICAgIC8vICAgICAnXHUyNkEwXHVGRTBGIFx1OEI2Nlx1NTQ0QSAtLSBcdThCRjdcdTU3MjhcdTU0N0RcdTRFRTRcdTg4NENcdTU0MEVcdTRFRTUgYC0tcGFnZT1cdTk4NzVcdTk3NjJcdTU0MERcdTc5RjBgIFx1NjgzQ1x1NUYwRlx1NjMwN1x1NUI5QVx1OTg3NVx1OTc2Mlx1NTQwRFx1NzlGMFx1RkYwMSdcclxuICAgIC8vICk7XHJcbiAgICByZXR1cm4gbnVsbDtcclxuICB9XHJcblxyXG4gIC8vIFx1NUMxRFx1OEJENVx1Njc4NFx1OTAyMFx1NEUyNFx1NEUyQVx1NTNFRlx1ODBGRFx1NzY4NFx1OERFRlx1NUY4NFxyXG4gIGNvbnN0IG1vZHVsZXNQYXRoID0gcGF0aC5yZXNvbHZlKF9fZGlybmFtZSwgYHNyYy9tb2R1bGVzLyR7bnBtX2NvbmZpZ19wYWdlfS9pbmRleC5odG1sYCk7XHJcbiAgY29uc3QgaW5nYW1lUGF0aCA9IHBhdGgucmVzb2x2ZShfX2Rpcm5hbWUsIGBzcmMvaW5HYW1lLyR7bnBtX2NvbmZpZ19wYWdlfS9pbmRleC5odG1sYCk7XHJcbiAgY29uc3QgRGVza1BhdGggPSBwYXRoLnJlc29sdmUoX19kaXJuYW1lLCBgc3JjL29uRGVzay8ke25wbV9jb25maWdfcGFnZX0vaW5kZXguaHRtbGApO1xyXG5cclxuICAvLyBcdTY4QzBcdTY3RTVcdTU0RUFcdTRFMkFcdThERUZcdTVGODRcdTVCNThcdTU3MjhcclxuICBpZiAoZnMuZXhpc3RzU3luYyhtb2R1bGVzUGF0aCkpIHtcclxuICAgIHJldHVybiB7XHJcbiAgICAgIFtucG1fY29uZmlnX3BhZ2VdOiBtb2R1bGVzUGF0aFxyXG4gICAgfTtcclxuICB9IGVsc2UgaWYgKGZzLmV4aXN0c1N5bmMoaW5nYW1lUGF0aCkpIHtcclxuICAgIHJldHVybiB7XHJcbiAgICAgIFtucG1fY29uZmlnX3BhZ2VdOiBpbmdhbWVQYXRoXHJcbiAgICB9O1xyXG4gIH0gIGVsc2UgaWYgKGZzLmV4aXN0c1N5bmMoRGVza1BhdGgpKSB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBbbnBtX2NvbmZpZ19wYWdlXTogRGVza1BhdGhcclxuICAgIH07XHJcbiAgfSBlbHNlIHtcclxuICAgIC8vIGVycm9yTG9nKGBcdTk4NzVcdTk3NjIgJHtucG1fY29uZmlnX3BhZ2V9IFx1NjVFMlx1NEUwRFx1NUI1OFx1NTcyOFx1NEU4RSBtb2R1bGVzIFx1NjU4N1x1NEVGNlx1NTkzOVx1RkYwQ1x1NEU1Rlx1NEUwRFx1NUI1OFx1NTcyOFx1NEU4RSBpbmdhbWUgXHU2NTg3XHU0RUY2XHU1OTM5XHU0RTJEXHVGRjAxYCk7XHJcbiAgICAvLyByZXR1cm4gbnVsbDtcclxuICB9XHJcbn07XHJcblxyXG5jb25zdCBnZXRQYWdlUm9vdCA9IChwYWdlTmFtZTogc3RyaW5nKSA9PiB7XHJcbiAgLy8gXHU1QzFEXHU4QkQ1XHU2Nzg0XHU5MDIwXHU0RTI0XHU0RTJBXHU1M0VGXHU4MEZEXHU3Njg0XHU4REVGXHU1Rjg0XHJcbiAgY29uc3QgbW9kdWxlc1BhdGggPSBwYXRoLnJlc29sdmUoX19kaXJuYW1lLCBgc3JjL21vZHVsZXMvJHtucG1fY29uZmlnX3BhZ2V9L2luZGV4Lmh0bWxgKTtcclxuICBjb25zdCBpbmdhbWVQYXRoID0gcGF0aC5yZXNvbHZlKF9fZGlybmFtZSwgYHNyYy9pbkdhbWUvJHtucG1fY29uZmlnX3BhZ2V9L2luZGV4Lmh0bWxgKTtcclxuICBjb25zdCBEZXNrUGF0aCA9IHBhdGgucmVzb2x2ZShfX2Rpcm5hbWUsIGBzcmMvb25EZXNrLyR7bnBtX2NvbmZpZ19wYWdlfS9pbmRleC5odG1sYCk7XHJcbiAgLy8gXHU1MDQ3XHU4QkJFXHU5ODc1XHU5NzYyXHU1NDBEXHU3OUYwXHU2ODNDXHU1RjBGXHU0RTNBaW5nYW1lXCJcdUZGMENcdTkwMUFcdThGQzdcdTU0MERcdTc5RjBcdTUyNERcdTdGMDBcdTUzM0FcdTUyMDZcclxuICBpZiAoZnMuZXhpc3RzU3luYyhpbmdhbWVQYXRoKSlcclxuICB7XHJcbiAgICByZXR1cm4gcGF0aC5yZXNvbHZlKF9fZGlybmFtZSwgYC4vc3JjL2luR2FtZS8ke3BhZ2VOYW1lfWApO1xyXG4gIH1cclxuICBlbHNlIGlmIChmcy5leGlzdHNTeW5jKG1vZHVsZXNQYXRoKSlcclxuICB7XHJcbiAgICAvLyBcdTlFRDhcdThCQTRcdTYyMTZcdTY3MkFcdTY2MEVcdTc4NkVcdTY4MDdcdThCQzZcdTRFM0FcdTZBMjFcdTU3NTdcdTc2ODRcdTk4NzVcdTk3NjJcdTg5QzZcdTRFM0Ftb2R1bGVOYW1lXHJcbiAgICByZXR1cm4gcGF0aC5yZXNvbHZlKF9fZGlybmFtZSwgYC4vc3JjL21vZHVsZXMvJHtwYWdlTmFtZX1gKTtcclxuICB9XHJcbiAgZWxzZSBpZihmcy5leGlzdHNTeW5jKERlc2tQYXRoKSlcclxuICB7XHJcbiAgICByZXR1cm4gcGF0aC5yZXNvbHZlKF9fZGlybmFtZSwgYC4vc3JjL29uRGVzay8ke3BhZ2VOYW1lfWApO1xyXG4gIH1cclxuICBlbHNlIHtcclxuICAgIC8vIGVycm9yTG9nKGBcdTk4NzVcdTk3NjIgJHtwYWdlTmFtZX0gXHU2NUUyXHU0RTBEXHU1QjU4XHU1NzI4XHU0RThFIG1vZHVsZXMgXHU2NTg3XHU0RUY2XHU1OTM5XHVGRjBDXHU0RTVGXHU0RTBEXHU1QjU4XHU1NzI4XHU0RThFIGluZ2FtZSBcdTY1ODdcdTRFRjZcdTU5MzlcdTRFMkRcdUZGMDFgKTtcclxuICAgIHJldHVybiBudWxsO1xyXG4gIH1cclxufTtcclxuXHJcbi8vIFx1NjI1M1x1NTMwNVx1NjNEMFx1NzkzQVxyXG4vLyBjb25zdCBidWlsZEVuZEZuID0gKG5hbWU6YW55KT0+e1xyXG4vLyAgIGNvbnNvbGUubG9nKGBcdUQ4M0RcdURFODBcdUQ4M0RcdURFODBcdUQ4M0RcdURFODAgJHtjaGFsay5ncmVlbi5ib2xkKCdcdTk4NzlcdTc2RUVcdTY3ODRcdTVFRkEnKX0gXHUyN0ExXHVGRTBGICAgJHtjaGFsay53aGl0ZS5iZ0dyZWVuLmJvbGQoYCAke25hbWV9IGApfSBcdUQ4M0NcdURERThcdUQ4M0NcdURERjNgKTtcclxuLy8gfVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZGVmaW5lQ29uZmlnKHtcclxuICByb290OmdldFBhZ2VSb290KHByb2Nlc3MuZW52Lm5wbV9jb25maWdfcGFnZSB8fCAnJyksXHJcbiAgYmFzZTogJy4vJyxcclxuICBlbnZEaXI6IHBhdGgucmVzb2x2ZShfX2Rpcm5hbWUpLCAvL1x1NzUyOFx1NEU4RVx1NTJBMFx1OEY3RCAuZW52IFx1NjU4N1x1NEVGNlx1NzY4NFx1NzZFRVx1NUY1NVx1MzAwMlx1NTNFRlx1NEVFNVx1NjYyRlx1NEUwMFx1NEUyQVx1N0VERFx1NUJGOVx1OERFRlx1NUY4NFx1RkYwQ1x1NEU1Rlx1NTNFRlx1NEVFNVx1NjYyRlx1NzZGOFx1NUJGOVx1NEU4RVx1OTg3OVx1NzZFRVx1NjgzOVx1NzY4NFx1OERFRlx1NUY4NFx1MzAwMlxyXG4gIHBsdWdpbnM6IFtcclxuICAgIHZ1ZSgpLFxyXG4gICAgQXV0b0ltcG9ydCh7XHJcbiAgICAgIC8vIFx1ODFFQVx1NTJBOFx1NUJGQ1x1NTE2NSBWdWUgXHU3NkY4XHU1MTczXHU1MUZEXHU2NTcwXHVGRjBDXHU1OTgyXHVGRjFBcmVmLCByZWFjdGl2ZSwgdG9SZWYgXHU3QjQ5XHJcbiAgICAgIGltcG9ydHM6IFsndnVlJywgJ3Z1ZS1yb3V0ZXInXSxcclxuICAgICAgZHRzOiBwYXRoLnJlc29sdmUoX19kaXJuYW1lLCAnLi9hdXRvLWltcG9ydC5kLnRzJyksXHJcbiAgICAgIGVzbGludHJjOiB7XHJcbiAgICAgICAgZW5hYmxlZDogZmFsc2UsIC8vIFx1NjYyRlx1NTQyNlx1ODFFQVx1NTJBOFx1NzUxRlx1NjIxMCBlc2xpbnQgXHU4OUM0XHU1MjE5XHVGRjBDXHU1RUZBXHU4QkFFXHU3NTFGXHU2MjEwXHU0RTRCXHU1NDBFXHU4QkJFXHU3RjZFIGZhbHNlXHJcbiAgICAgICAgZmlsZXBhdGg6IHBhdGgucmVzb2x2ZShfX2Rpcm5hbWUsICcuLy5lc2xpbnRyYy1hdXRvLWltcG9ydC5qc29uJyksIC8vIFx1NjMwN1x1NUI5QVx1ODFFQVx1NTJBOFx1NUJGQ1x1NTE2NVx1NTFGRFx1NjU3MCBlc2xpbnQgXHU4OUM0XHU1MjE5XHU3Njg0XHU2NTg3XHU0RUY2XHJcbiAgICAgICAgZ2xvYmFsc1Byb3BWYWx1ZTogdHJ1ZSxcclxuICAgICAgfSxcclxuICAgICAgcmVzb2x2ZXJzOiBbXHJcbiAgICAgICAgSWNvbnNSZXNvbHZlcih7IHByZWZpeDogJ0ljb24nLCB9KSwgLy8gXHU4MUVBXHU1MkE4XHU1QkZDXHU1MTY1XHU1NkZFXHU2ODA3XHU3RUM0XHU0RUY2XHJcbiAgICAgICAgRWxlbWVudFBsdXNSZXNvbHZlcigpIC8vIC8vIFx1ODFFQVx1NTJBOFx1NUJGQ1x1NTE2NSBFbGVtZW50IFBsdXMgXHU3NkY4XHU1MTczXHU1MUZEXHU2NTcwXHVGRjBDXHU1OTgyXHVGRjFBRWxNZXNzYWdlLCBFbE1lc3NhZ2VCb3guLi5cclxuICAgICAgXSxcclxuICAgICAgLy8gdnVlVGVtcGxhdGU6IHRydWUsXHJcbiAgICAgIC8vIGR0czogZmFsc2UsIC8vIFx1OTE0RFx1N0Y2RVx1NjU4N1x1NEVGNlx1NzUxRlx1NjIxMFx1NEY0RFx1N0Y2RShmYWxzZTpcdTUxNzNcdTk1RURcdTgxRUFcdTUyQThcdTc1MUZcdTYyMTApIFx1NTE3M1x1OTVFRGRldlx1NjVGNlx1ODFFQVx1NTJBOFx1NzUxRlx1NjIxMGNvbXBvbmVudHMuZC50c1xyXG4gICAgICAvLyBkdHM6IHBhdGgucmVzb2x2ZShwYXRoU3JjLCBcInR5cGVzXCIsIFwiYXV0by1pbXBvcnRzLmQudHNcIiksIC8vIFx1NjMwN1x1NUI5QVx1ODFFQVx1NTJBOFx1NUJGQ1x1NTE2NVx1NTFGRFx1NjU3MFRTXHU3QzdCXHU1NzhCXHU1OEYwXHU2NjBFXHU2NTg3XHU0RUY2XHU4REVGXHU1Rjg0XHJcbiAgICB9KSxcclxuICAgIENvbXBvbmVudHMoe1xyXG4gICAgICByZXNvbHZlcnM6IFtcclxuICAgICAgICBJY29uc1Jlc29sdmVyKHsgZW5hYmxlZENvbGxlY3Rpb25zOiBbJ2VwJ10sIH0pLCAvLyBcdTgxRUFcdTUyQThcdTZDRThcdTUxOENcdTU2RkVcdTY4MDdcdTdFQzRcdTRFRjZcclxuICAgICAgICBFbGVtZW50UGx1c1Jlc29sdmVyKCkgLy8gXHU4MUVBXHU1MkE4XHU1QkZDXHU1MTY1IEVsZW1lbnQgUGx1cyBcdTdFQzRcdTRFRjZcclxuICAgICAgXSxcclxuICAgICAgZGlyczogWydzcmMvKiovY29tcG9uZW50cyddLCAvLyBcdTYzMDdcdTVCOUFcdTgxRUFcdTVCOUFcdTRFNDlcdTdFQzRcdTRFRjZcdTRGNERcdTdGNkUoXHU5RUQ4XHU4QkE0OnNyYy9jb21wb25lbnRzKVxyXG4gICAgICBkdHM6IGZhbHNlLCAvLyBcdTkxNERcdTdGNkVcdTY1ODdcdTRFRjZcdTRGNERcdTdGNkUoZmFsc2U6XHU1MTczXHU5NUVEXHU4MUVBXHU1MkE4XHU3NTFGXHU2MjEwKVxyXG4gICAgICAvLyBkdHM6IHBhdGgucmVzb2x2ZShwYXRoU3JjLCBcInR5cGVzXCIsIFwiY29tcG9uZW50cy5kLnRzXCIpLCAvLyBcdTYzMDdcdTVCOUFcdTgxRUFcdTUyQThcdTVCRkNcdTUxNjVcdTdFQzRcdTRFRjZUU1x1N0M3Qlx1NTc4Qlx1NThGMFx1NjYwRVx1NjU4N1x1NEVGNlx1OERFRlx1NUY4NFxyXG4gICAgfSksXHJcbiAgICBJY29ucyh7XHJcbiAgICAgIGF1dG9JbnN0YWxsOiB0cnVlLFxyXG4gICAgfSksXHJcbiAgICAvLyBnemlwXHU2ODNDXHU1RjBGXHJcbiAgICAvLyBjb21wcmVzc2lvbih7XHJcbiAgICAvLyAgIHRocmVzaG9sZDogMTAyNCAqIDEwMDAsIC8vIFx1NEY1M1x1NzlFRlx1NTkyN1x1NEU4RSB0aHJlc2hvbGQgXHU2MjREXHU0RjFBXHU4OEFCXHU1MzhCXHU3RjI5LFx1NTM1NVx1NEY0RCBiXHJcbiAgICAvLyAgIGV4dDogJy5neicsIC8vIFx1NTM4Qlx1N0YyOVx1NjU4N1x1NEVGNlx1NjgzQ1x1NUYwRlxyXG4gICAgLy8gICBkZWxldGVPcmlnaW5GaWxlOiB0cnVlIC8vIFx1NjYyRlx1NTQyNlx1NTIyMFx1OTY2NFx1NkU5MFx1NjU4N1x1NEVGNlxyXG4gICAgLy8gfSksXHJcbiAgXSxcclxuICBjc3M6e1xyXG4gICAgcHJlcHJvY2Vzc29yT3B0aW9uczoge1xyXG4gICAgICBzY3NzOiB7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9LFxyXG4gIHJlc29sdmU6IHtcclxuICAgIGFsaWFzOiB7XHJcbiAgICAgICdAJzogcGF0aC5qb2luKF9fZGlybmFtZSwgJy4vc3JjJyksXHJcbiAgICAgICdAbW9kdWxlcyc6IHBhdGguam9pbihfX2Rpcm5hbWUsICcuL3NyYy9tb2R1bGVzJylcclxuICAgIH1cclxuICB9LFxyXG4gIHNlcnZlcjoge1xyXG4gICAgaG9zdDogJ2xvY2FsaG9zdCcsIC8vIFx1NjMwN1x1NUI5QVx1NjcwRFx1NTJBMVx1NTY2OFx1NEUzQlx1NjczQVx1NTQwRFxyXG4gICAgcG9ydDogODA4MCwgLy8gXHU2MzA3XHU1QjlBXHU2NzBEXHU1MkExXHU1NjY4XHU3QUVGXHU1M0UzXHJcbiAgICBobXI6IHRydWUsICAvLyBcdTVGMDBcdTU0MkZcdTcwRURcdTY2RjRcdTY1QjBcclxuICAgIG9wZW46IHRydWUsIC8vIFx1NTcyOFx1NjcwRFx1NTJBMVx1NTY2OFx1NTQyRlx1NTJBOFx1NjVGNlx1ODFFQVx1NTJBOFx1NTcyOFx1NkQ0Rlx1ODlDOFx1NTY2OFx1NEUyRFx1NjI1M1x1NUYwMFx1NUU5NFx1NzUyOFx1N0EwQlx1NUU4RlxyXG4gICAgaHR0cHM6IGZhbHNlIC8vIFx1NjYyRlx1NTQyNlx1NUYwMFx1NTQyRiBodHRwc1xyXG4gIH0sXHJcbiAgYnVpbGQ6IHtcclxuICAgIC8vIHNvdXJjZW1hcDogdHJ1ZSwvL1x1NUYwMFx1NTNEMVx1OEMwM1x1OEJENVx1NjVGNlx1NEY3Rlx1NzUyOFx1NjI1M1x1NTM3MFx1NUJGOVx1NUU5NFx1NjU4N1x1NEVGNlx1ODg0Q1x1NjU3MFx1RkYwQ1x1NjNEMFx1NEVBNFx1NjI1M1x1NTMwNVx1NkNFOFx1OTUwMFx1NkI2NFx1ODg0Q1xyXG4gICAgb3V0RGlyOiBwYXRoLnJlc29sdmUoX19kaXJuYW1lLCBgd2ViYXBwL3dpbmRvd3MvcGFnZXMvJHtucG1fY29uZmlnX3BhZ2V9YCksIC8vIFx1NjMwN1x1NUI5QVx1OEY5M1x1NTFGQVx1OERFRlx1NUY4NFxyXG4gICAgYXNzZXRzSW5saW5lTGltaXQ6IDQwOTYsIC8vXHU1QzBGXHU0RThFXHU2QjY0XHU5NjA4XHU1MDNDXHU3Njg0XHU1QkZDXHU1MTY1XHU2MjE2XHU1RjE1XHU3NTI4XHU4RDQ0XHU2RTkwXHU1QzA2XHU1MTg1XHU4MDU0XHU0RTNBIGJhc2U2NCBcdTdGMTZcdTc4MDFcdUZGMENcdTRFRTVcdTkwN0ZcdTUxNERcdTk4OURcdTU5MTZcdTc2ODQgaHR0cCBcdThCRjdcdTZDNDJcclxuICAgIGVtcHR5T3V0RGlyOiB0cnVlLCAvL1ZpdGUgXHU0RjFBXHU1NzI4XHU2Nzg0XHU1RUZBXHU2NUY2XHU2RTA1XHU3QTdBXHU4QkU1XHU3NkVFXHU1RjU1XHJcbiAgICBjc3NDb2RlU3BsaXQ6ZmFsc2UsXHJcbiAgICB0ZXJzZXJPcHRpb25zOiB7XHJcbiAgICAgIGNvbXByZXNzOiB7XHJcbiAgICAgICAga2VlcF9pbmZpbml0eTogdHJ1ZSwgLy8gXHU5NjMyXHU2QjYyIEluZmluaXR5IFx1ODhBQlx1NTM4Qlx1N0YyOVx1NjIxMCAxLzBcdUZGMENcdThGRDlcdTUzRUZcdTgwRkRcdTRGMUFcdTVCRkNcdTgxRjQgQ2hyb21lIFx1NEUwQVx1NzY4NFx1NjAyN1x1ODBGRFx1OTVFRVx1OTg5OFxyXG4gICAgICAgIGRyb3BfY29uc29sZTogdHJ1ZSwgLy8gXHU3NTFGXHU0RUE3XHU3M0FGXHU1ODgzXHU1M0JCXHU5NjY0IGNvbnNvbGVcclxuICAgICAgICBkcm9wX2RlYnVnZ2VyOiB0cnVlLCAvLyBcdTc1MUZcdTRFQTdcdTczQUZcdTU4ODNcdTUzQkJcdTk2NjQgZGVidWdnZXJcclxuICAgICAgfSxcclxuICAgICAgZm9ybWF0OiB7XHJcbiAgICAgICAgY29tbWVudHM6IGZhbHNlLCAvLyBcdTUyMjBcdTk2NjRcdTZDRThcdTkxQ0FcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgICByb2xsdXBPcHRpb25zOiB7ICAvL1x1ODFFQVx1NUI5QVx1NEU0OVx1NUU5NVx1NUM0Mlx1NzY4NCBSb2xsdXAgXHU2MjUzXHU1MzA1XHU5MTREXHU3RjZFXHJcbiAgICAgIGlucHV0OiBnZXRFbnRlclBhZ2VzKCksXHJcbiAgICAgIC8vIGJ1aWxkRW5kOiBidWlsZEVuZEZuKG5wbV9jb25maWdfcGFnZSksXHJcbiAgICAgIG91dHB1dDoge1xyXG4gICAgICAgIGFzc2V0RmlsZU5hbWVzOiAoYXNzZXRJbmZvKSA9PiB7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnYXNzZXRzSW5mbzonLGFzc2V0SW5mbyk7XHJcbiAgICAgICAgICBjb25zdCB7IG5hbWUgfSA9IGFzc2V0SW5mbztcclxuICAgICAgICAgIC8vIFx1NTk4Mlx1Njc5Q1x1NjYyRiBDU1MgXHU2NTg3XHU0RUY2XHVGRjBDXHU1MjE5XHU0RjdGXHU3NTI4XHU3Mjc5XHU1QjlBXHU3Njg0XHU1NDdEXHU1NDBEXHU4OUM0XHU1MjE5XHJcbiAgICAgICAgICBpZiAobmFtZS5lbmRzV2l0aCgnLmNzcycpKSB7XHJcbiAgICAgICAgICAgIGlmIChucG1fY29uZmlnX3BhZ2UpIHtcclxuICAgICAgICAgICAgICByZXR1cm4gYGNzcy8ke25wbV9jb25maWdfcGFnZX0uY3NzYDtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICByZXR1cm4gJ1tleHRdL1tuYW1lXS5jc3MnO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgLy8gXHU1QkY5XHU0RThFXHU1MTc2XHU0RUQ2XHU3QzdCXHU1NzhCXHU3Njg0XHU4RDQ0XHU0RUE3XHVGRjBDXHU0RjdGXHU3NTI4XHU5RUQ4XHU4QkE0XHU4OUM0XHU1MjE5XHJcbiAgICAgICAgICByZXR1cm4gJ1tleHRdL1tuYW1lXS5bZXh0XSc7XHJcbiAgICAgICAgfSwgLy9cdTk3NTlcdTYwMDFcdTY1ODdcdTRFRjZcdThGOTNcdTUxRkFcdTc2ODRcdTY1ODdcdTRFRjZcdTU5MzlcdTU0MERcdTc5RjBcclxuICAgICAgICAvLyBjaHVua0ZpbGVOYW1lczogJ2pzL1tuYW1lXS5qcycsICAvL2NodW5rXHU1MzA1XHU4RjkzXHU1MUZBXHU3Njg0XHU2NTg3XHU0RUY2XHU1OTM5XHU1NDBEXHU3OUYwXHJcbiAgICAgICAgLy8gY3NzRmlsZU5hbWU6J2Nzcy9bbmFtZV0uY3NzJyxcclxuICAgICAgICBlbnRyeUZpbGVOYW1lczogJ2pzL1tuYW1lXS5qcycsICAvL1x1NTE2NVx1NTNFM1x1NjU4N1x1NEVGNlx1OEY5M1x1NTFGQVx1NzY4NFx1NjU4N1x1NEVGNlx1NTkzOVx1NTQwRFx1NzlGMFxyXG4gICAgICAgIGNvbXBhY3Q6IHRydWUsXHJcbiAgICAgICAgLy8gbWFudWFsQ2h1bmtzOiAoaWQpID0+IHtcclxuICAgICAgICAvLyAgIGlmIChpZC5pbmNsdWRlcygnbm9kZV9tb2R1bGVzJykpIHtcclxuICAgICAgICAvLyAgICAgcmV0dXJuIGlkXHJcbiAgICAgICAgLy8gICAgICAgICAudG9TdHJpbmcoKVxyXG4gICAgICAgIC8vICAgICAgICAgLnNwbGl0KCdub2RlX21vZHVsZXMvJylbMV1cclxuICAgICAgICAvLyAgICAgICAgIC5zcGxpdCgnLycpWzBdXHJcbiAgICAgICAgLy8gICAgICAgICAudG9TdHJpbmcoKSAvLyBcdTYyQzZcdTUyMDZcdTU5MUFcdTRFMkF2ZW5kb3JzXHJcbiAgICAgICAgLy8gICB9XHJcbiAgICAgICAgLy8gfVxyXG4gICAgICB9XHJcbiAgICB9LFxyXG4gIH1cclxufSlcclxuIl0sCiAgIm1hcHBpbmdzIjogIjtBQUVBLFNBQVMsb0JBQW9CO0FBQzdCLE9BQU8sU0FBUztBQUNoQixPQUFPLFVBQXFCO0FBRTVCLE9BQU8sZ0JBQWdCO0FBQ3ZCLE9BQU8sZ0JBQWdCO0FBQ3ZCLE9BQU8sV0FBVztBQUNsQixPQUFPLG1CQUFtQjtBQUMxQixTQUFTLDJCQUEyQjtBQUlwQyxPQUFRLFFBQVE7QUFkaEIsSUFBTSxtQ0FBbUM7QUFpQnpDLElBQU0sa0JBQWtCLFFBQVEsSUFBSSxtQkFBbUI7QUFDdkQsSUFBTSx5QkFBeUIsUUFBUSxJQUFJLDBCQUEwQjtBQU1yRSxJQUFNLGdCQUFnQixNQUFNO0FBQzFCLE1BQUksQ0FBQyxpQkFBaUI7QUFJcEIsV0FBTztBQUFBLEVBQ1Q7QUFHQSxRQUFNLGNBQWMsS0FBSyxRQUFRLGtDQUFXLGVBQWUsZUFBZSxhQUFhO0FBQ3ZGLFFBQU0sYUFBYSxLQUFLLFFBQVEsa0NBQVcsY0FBYyxlQUFlLGFBQWE7QUFDckYsUUFBTSxXQUFXLEtBQUssUUFBUSxrQ0FBVyxjQUFjLGVBQWUsYUFBYTtBQUduRixNQUFJLEdBQUcsV0FBVyxXQUFXLEdBQUc7QUFDOUIsV0FBTztBQUFBLE1BQ0wsQ0FBQyxlQUFlLEdBQUc7QUFBQSxJQUNyQjtBQUFBLEVBQ0YsV0FBVyxHQUFHLFdBQVcsVUFBVSxHQUFHO0FBQ3BDLFdBQU87QUFBQSxNQUNMLENBQUMsZUFBZSxHQUFHO0FBQUEsSUFDckI7QUFBQSxFQUNGLFdBQVksR0FBRyxXQUFXLFFBQVEsR0FBRztBQUNuQyxXQUFPO0FBQUEsTUFDTCxDQUFDLGVBQWUsR0FBRztBQUFBLElBQ3JCO0FBQUEsRUFDRixPQUFPO0FBQUEsRUFHUDtBQUNGO0FBRUEsSUFBTSxjQUFjLENBQUMsYUFBcUI7QUFFeEMsUUFBTSxjQUFjLEtBQUssUUFBUSxrQ0FBVyxlQUFlLGVBQWUsYUFBYTtBQUN2RixRQUFNLGFBQWEsS0FBSyxRQUFRLGtDQUFXLGNBQWMsZUFBZSxhQUFhO0FBQ3JGLFFBQU0sV0FBVyxLQUFLLFFBQVEsa0NBQVcsY0FBYyxlQUFlLGFBQWE7QUFFbkYsTUFBSSxHQUFHLFdBQVcsVUFBVSxHQUM1QjtBQUNFLFdBQU8sS0FBSyxRQUFRLGtDQUFXLGdCQUFnQixRQUFRLEVBQUU7QUFBQSxFQUMzRCxXQUNTLEdBQUcsV0FBVyxXQUFXLEdBQ2xDO0FBRUUsV0FBTyxLQUFLLFFBQVEsa0NBQVcsaUJBQWlCLFFBQVEsRUFBRTtBQUFBLEVBQzVELFdBQ1EsR0FBRyxXQUFXLFFBQVEsR0FDOUI7QUFDRSxXQUFPLEtBQUssUUFBUSxrQ0FBVyxnQkFBZ0IsUUFBUSxFQUFFO0FBQUEsRUFDM0QsT0FDSztBQUVILFdBQU87QUFBQSxFQUNUO0FBQ0Y7QUFPQSxJQUFPLHNCQUFRLGFBQWE7QUFBQSxFQUMxQixNQUFLLFlBQVksUUFBUSxJQUFJLG1CQUFtQixFQUFFO0FBQUEsRUFDbEQsTUFBTTtBQUFBLEVBQ04sUUFBUSxLQUFLLFFBQVEsZ0NBQVM7QUFBQTtBQUFBLEVBQzlCLFNBQVM7QUFBQSxJQUNQLElBQUk7QUFBQSxJQUNKLFdBQVc7QUFBQTtBQUFBLE1BRVQsU0FBUyxDQUFDLE9BQU8sWUFBWTtBQUFBLE1BQzdCLEtBQUssS0FBSyxRQUFRLGtDQUFXLG9CQUFvQjtBQUFBLE1BQ2pELFVBQVU7QUFBQSxRQUNSLFNBQVM7QUFBQTtBQUFBLFFBQ1QsVUFBVSxLQUFLLFFBQVEsa0NBQVcsOEJBQThCO0FBQUE7QUFBQSxRQUNoRSxrQkFBa0I7QUFBQSxNQUNwQjtBQUFBLE1BQ0EsV0FBVztBQUFBLFFBQ1QsY0FBYyxFQUFFLFFBQVEsT0FBUSxDQUFDO0FBQUE7QUFBQSxRQUNqQyxvQkFBb0I7QUFBQTtBQUFBLE1BQ3RCO0FBQUE7QUFBQTtBQUFBO0FBQUEsSUFJRixDQUFDO0FBQUEsSUFDRCxXQUFXO0FBQUEsTUFDVCxXQUFXO0FBQUEsUUFDVCxjQUFjLEVBQUUsb0JBQW9CLENBQUMsSUFBSSxFQUFHLENBQUM7QUFBQTtBQUFBLFFBQzdDLG9CQUFvQjtBQUFBO0FBQUEsTUFDdEI7QUFBQSxNQUNBLE1BQU0sQ0FBQyxtQkFBbUI7QUFBQTtBQUFBLE1BQzFCLEtBQUs7QUFBQTtBQUFBO0FBQUEsSUFFUCxDQUFDO0FBQUEsSUFDRCxNQUFNO0FBQUEsTUFDSixhQUFhO0FBQUEsSUFDZixDQUFDO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUEsRUFPSDtBQUFBLEVBQ0EsS0FBSTtBQUFBLElBQ0YscUJBQXFCO0FBQUEsTUFDbkIsTUFBTSxDQUNOO0FBQUEsSUFDRjtBQUFBLEVBQ0Y7QUFBQSxFQUNBLFNBQVM7QUFBQSxJQUNQLE9BQU87QUFBQSxNQUNMLEtBQUssS0FBSyxLQUFLLGtDQUFXLE9BQU87QUFBQSxNQUNqQyxZQUFZLEtBQUssS0FBSyxrQ0FBVyxlQUFlO0FBQUEsSUFDbEQ7QUFBQSxFQUNGO0FBQUEsRUFDQSxRQUFRO0FBQUEsSUFDTixNQUFNO0FBQUE7QUFBQSxJQUNOLE1BQU07QUFBQTtBQUFBLElBQ04sS0FBSztBQUFBO0FBQUEsSUFDTCxNQUFNO0FBQUE7QUFBQSxJQUNOLE9BQU87QUFBQTtBQUFBLEVBQ1Q7QUFBQSxFQUNBLE9BQU87QUFBQTtBQUFBLElBRUwsUUFBUSxLQUFLLFFBQVEsa0NBQVcsd0JBQXdCLGVBQWUsRUFBRTtBQUFBO0FBQUEsSUFDekUsbUJBQW1CO0FBQUE7QUFBQSxJQUNuQixhQUFhO0FBQUE7QUFBQSxJQUNiLGNBQWE7QUFBQSxJQUNiLGVBQWU7QUFBQSxNQUNiLFVBQVU7QUFBQSxRQUNSLGVBQWU7QUFBQTtBQUFBLFFBQ2YsY0FBYztBQUFBO0FBQUEsUUFDZCxlQUFlO0FBQUE7QUFBQSxNQUNqQjtBQUFBLE1BQ0EsUUFBUTtBQUFBLFFBQ04sVUFBVTtBQUFBO0FBQUEsTUFDWjtBQUFBLElBQ0Y7QUFBQSxJQUNBLGVBQWU7QUFBQTtBQUFBLE1BQ2IsT0FBTyxjQUFjO0FBQUE7QUFBQSxNQUVyQixRQUFRO0FBQUEsUUFDTixnQkFBZ0IsQ0FBQyxjQUFjO0FBQzdCLGtCQUFRLElBQUksZUFBYyxTQUFTO0FBQ25DLGdCQUFNLEVBQUUsS0FBSyxJQUFJO0FBRWpCLGNBQUksS0FBSyxTQUFTLE1BQU0sR0FBRztBQUN6QixnQkFBSSxpQkFBaUI7QUFDbkIscUJBQU8sT0FBTyxlQUFlO0FBQUEsWUFDL0I7QUFDQSxtQkFBTztBQUFBLFVBQ1Q7QUFFQSxpQkFBTztBQUFBLFFBQ1Q7QUFBQTtBQUFBO0FBQUE7QUFBQSxRQUdBLGdCQUFnQjtBQUFBO0FBQUEsUUFDaEIsU0FBUztBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBLE1BVVg7QUFBQSxJQUNGO0FBQUEsRUFDRjtBQUNGLENBQUM7IiwKICAibmFtZXMiOiBbXQp9Cg==
