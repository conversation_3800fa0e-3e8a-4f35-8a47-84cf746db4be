<template>
  <div class="setting-main">
    <SettingMainHeader/>
    <div class="setting-content scroll">
      <el-collapse v-model="activeCollapse">
        <setting-ty/>
        <setting-gxh/>
        <setting-message/>
        <setting-sensor/>
        <setting-oled/>
        <setting-xn/>
        <setting-shortcut/>
        <setting-yx/>
        <setting-other/>
      </el-collapse>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref} from "vue";
import SettingMainHeader from "@/modules/Game_Setting/components/setting-main-header.vue";
import SettingTy from "@/modules/Game_Setting/components/setting-ty.vue";
import SettingGxh from "@/modules/Game_Setting/components/setting-gxh.vue";
import SettingMessage from "@/modules/Game_Setting/components/setting-message.vue";
import SettingSensor from "@/modules/Game_Setting/components/setting-sensor.vue";
import SettingOled from "@/modules/Game_Setting/components/setting-oled.vue";
import SettingXn from "@/modules/Game_Setting/components/setting-xn.vue";
import SettingShortcut from "@/modules/Game_Setting/components/setting-shortcut.vue";
import SettingYx from "@/modules/Game_Setting/components/setting-yx.vue";
import SettingOther from "@/modules/Game_Setting/components/setting-other.vue";

let activeCollapse = ref(['1','2','3','4','5','6','7','8','9'])
</script>

<style scoped lang="scss">
.setting-main {
  width: 100%;
  height: 100%;
    overflow: hidden;

  .setting-content {
    width: 99%;
    height: calc(100% - 40px);
    overflow-y: scroll;
    display: flex;
    flex-flow: column nowrap;
    padding: 0 20px 20px 20px;
  }
}
</style>

<style lang="scss">
.setting-item {
  width: 100%;
  color: var(--font-color);
  font-size: .12rem;
  padding: 10px 20px 0 20px;
  display: flex;
  flex-flow: row nowrap;

  .el-color-picker__trigger {
    border: 2px solid transparent;
    padding: 0;
    margin-left: 10px;
  }
  .el-color-picker__color {
    border: 0;
  }
  .el-color-picker:hover:not(.is-disabled,.is-focused) .el-color-picker__trigger {
    border: 2px solid #3579D5;
  }

  .setting-item-title {
    font-size: .12rem;
    color: var(--font-gray);
    margin-bottom: 15px;
    line-height: 1;

    &.mt-25 {
      margin-top: 25px;
    }
  }

  .left-box {
    width: 50%;
  }

  .right-box {
    width: 50%;
    border-left: 2px solid var(--setting-item-border-color);
    padding-left: 20px;

    .el-input-number .el-input__inner {
      color: var(--font-color);
    }
  }

  text {
    color: var(--font-gray);
  }

  span {
    color: var(--font-color);
  }
}

.flex-row-center {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  line-height: 1;
}

.el-input {
  --el-border-color: rgba(0,0,0,0);
  --el-input-bg-color: #343647 !important;
  --el-input-text-color: var(--font-color);
}

.el-collapse {
  --el-fill-color-blank: #2D2E39;
  --el-text-color-primary: #fff;
  --el-border-color-lighter: transparent;
  --el-collapse-border-color: var(--el-border-color-lighter);
  --el-collapse-header-height: 48px;
  --el-collapse-header-bg-color: var(--el-fill-color-blank);
  --el-collapse-header-text-color: var(--el-text-color-primary);
  --el-collapse-header-font-size: 13px;
  --el-collapse-content-bg-color: var(--el-fill-color-blank);
  --el-collapse-content-font-size: 13px;
  --el-collapse-content-text-color: var(--el-text-color-primary);
  border-bottom: 1px solid var(--el-collapse-border-color);
  border-top: 1px solid var(--el-collapse-border-color);
}

.el-collapse-item__header {
  font-size: .12rem;
  padding-left: 20px;
}

.el-collapse-item__wrap {
  overflow: visible;
}

.el-select {
  --el-border-color: #2D303E !important;
  --el-color-primary: #0089E9;
}

.el-select__wrapper {
  color: #E1E1E1;
  background-color: rgba(37, 39, 47, 1);
}

.el-select__popper.el-popper .el-popper__arrow:before {
  border-color: #2D303E !important;
}

.el-popper__arrow:before {
  background-color: #2C2C33 !important;
}

.el-select__popper.el-popper {
  background: #2C2C33;
  border: 1px solid #39404D;
}

.el-select-dropdown__item {
  color: white;
}

.el-select-dropdown__item.is-hovering {
  background-color: #0089e9;
  color: white;
}

.el-button:focus-visible {
  outline: transparent
}
.el-button:hover {
  background-color: var(--el-button-hover-bg-color);
  border-color: var(--el-button-hover-border-color);
  color: var(--font-color);
  outline: none;
}
</style>
