<script setup lang="ts">
import {GPP_GetInteger, GPP_WriteInteger, SetVideoQualityParams} from "../stores/index"
import {computed, reactive} from "vue";
const re_modulus = 1;
const data = reactive({
    select166: 0,
    select53: 0,
    select54: 30,// fps
    videoBit: 10000,
})
const marks = reactive({
    3000: '3',
    10000: '10',
    20000: '20',
    30000: '30',
    40000: '40',
    50000: '50',
})
onMounted(async () => {
    data.select53 = await GPP_GetInteger(53)
    data.select54 = await GPP_GetInteger(54)
    data.select166 = await GPP_GetInteger(166)
    data.videoBit = await GPP_GetInteger(55)
})

const calcSize = computed(()=>{
    const VideoResolution = data.select53;
    const VideoFPS = data.select54;
    const VideoBit = data.videoBit / 1000;
    const VideoSize = VideoFPS * (VideoBit / 10);
    return (VideoBit) * 7 * re_modulus;
})
function setSelect54(value:number) {
    RestoreCustomize();
    GPP_WriteInteger(54, value);
    SetVideoQualityParams();
}
function RestoreCustomize() {
    data.select166 = 3;
    GPP_WriteInteger(166, 3);
}

function setSelect166 (value:number) {
    if (value === 0) {
        LowRecording();
        // GPP_SendStatics(9048);
    } else if (value === 1) {
        GeneralRecording();
        // GPP_SendStatics(9049);
    } else if (value === 2) {
        HighRecording();
        // GPP_SendStatics(9050);
    } else {
        // GPP_SendStatics(9051);
    }
    GPP_WriteInteger(166, value);
}
function LowRecording() {
    data.select54 = 30;
    GPP_WriteInteger(54, 30);
    data.videoBit = 3000;
    GPP_WriteInteger(55, 3000);
    SetVideoQualityParams();
}
function GeneralRecording() {
    data.select54 = 30;
    GPP_WriteInteger(54, 30);
    data.videoBit = 10000;
    GPP_WriteInteger(55, 10000);
    SetVideoQualityParams();
}

function HighRecording() {
    data.select54 = 60;
    GPP_WriteInteger(54, 60);
    data.videoBit = 15000;
    GPP_WriteInteger(55, 15000);
    SetVideoQualityParams();
}

function formatSliderTooltip (value: number) {
    return value / 1000 + 'Mbps'
}

function setVideoBit (value: number) {
    RestoreCustomize();
    GPP_WriteInteger(55, value);
    SetVideoQualityParams();
}
</script>

<template>
    <div class="scheme-box">
        <span class="scheme-box-title">
            <span class="title-name">{{$t('video.scheme')}}</span>
            <span class="gray">{{$t('video.text1')}} <span class="blue">{{data.videoBit/1000}}Mbps</span> <span class="white">÷ 8 × 60s ≈ {{calcSize}}MB</span></span>
        </span>

        <ul>
            <li>
                <span>{{$t('video.suggestScheme')}}</span>
                <div class="w-156" >
                    <el-select popper-class="select-family" v-model="data.select166" @change="setSelect166">
                        <el-option :value="0" :label="$t('video.low')">{{$t('video.low')}}</el-option>
                        <el-option :value="1" :label="$t('video.mid')">{{$t('video.mid')}}</el-option>
                        <el-option :value="2" :label="$t('video.high')">{{$t('video.high')}}</el-option>
                        <el-option :value="3" :label="$t('InGameMonitor.custom')">{{$t('InGameMonitor.custom')}}</el-option>
                    </el-select>
                </div>
            </li>
            <li>
                <span>{{$t('video.videoResolution')}}</span>
                <div class="w-156">
                    <el-select popper-class="select-family" v-model="data.select53">
                        <el-option :value="0" :label="$t('video.1080p')">{{$t('video.1080p')}}</el-option>
                    </el-select>
                </div>
            </li>
            <li>
                <span>{{$t('video.RecordingFPS')}}</span>
                <div class="w-156">
                    <el-select popper-class="select-family" v-model="data.select54" @change="setSelect54">
                        <el-option :value="30" label="30">30</el-option>
                        <el-option :value="60" label="60">60</el-option>
                    </el-select>
                </div>
            </li>
            <li>
                <span>{{$t('video.bitRate')}}</span>
                <div class="w-280" style="margin-right: 10px;">
                    <el-slider
                        tooltip-class="cus"
                        @change="setVideoBit"
                        :format-tooltip="formatSliderTooltip"
                        :min="3000" :step="1000" :marks="marks" :max="50000"
                        v-model="data.videoBit"
                    />
                </div>
            </li>
        </ul>
    </div>
</template>

<style scoped lang="scss">
.scheme-box {
    position: absolute;
    left: 486px;
    top: 40px;
    width: 570px;
    height: 280px;
    border-radius: 4px;
    background: #2B2C37;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
    padding: 10px;

    &-title {
        color: #ffffff;
        display: flex;
        gap: 5px;

        .title-name {
            display: inline-block;
            width: 63px;
            color: #ffffff;
        }
        .white {
            color: #ffffff;
        }
        .gray {
            color: #999999;
        }
        .blue {
            color: #409EFF;
        }
    }

    ul {
        display: flex;
        flex-flow: column nowrap;
        flex-direction: column;
        height: 206px;
        justify-content: space-between;
        margin-top: 10px;
        li {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #777777;
            .w-156 {
                width: 156px;
            }
            .w-280 {
                width: 280px;
            }
        }
    }
}
</style>
<style>
.el-popper.is-dark {
    background-color: #3E4050;
    border-color: #3E4050;
    color: #ffffff;
}
.el-popper__arrow::before {
    background-color: var(--el-bg-color-overlay) !important;
    border-color: var(--el-border-color-light) !important;
}
.cus .el-popper__arrow::before {
    background-color: #3E4050 !important;
    border-color: #3E4050 !important;
}


</style>
