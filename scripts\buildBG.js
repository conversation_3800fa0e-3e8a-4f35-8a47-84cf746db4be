import fs from "fs";
import path from "path";
import { execSync } from "child_process";
import { fileURLToPath } from 'url';
const __filename = fileURLToPath(import.meta.url);
// 获取当前文件所在的目录
const __dirname = path.dirname(__filename);

// 定义目标文件夹路径
const folderPath = path.resolve(__dirname, '../webapp/windows/background');

// 确保输出目录存在，如果不存在则创建它
const outputFolder = path.join(folderPath, 'js');
if (!fs.existsSync(outputFolder)) {
    fs.mkdirSync(outputFolder, { recursive: true });
}

// 读取文件夹并找到所有的ts文件
fs.readdir(folderPath, (err, files) => {
    console.log(files, '123123')
    if (err) {
        console.error('Error reading the directory', err);
        return;
    }

    const tsFiles = files.filter(file => file.endsWith('.ts'));

    tsFiles.forEach(file => {
        const jsFileName = file.replace('.ts', '.js');
        const inputFilePath = path.join(folderPath, file);
        const outputFilePath = path.join(outputFolder, jsFileName);

        console.log(`Compiling ${inputFilePath} to ${outputFilePath}...`);

        try {
            // 使用npx tsc来确保使用正确的TypeScript版本
            // execSync(`npx tsc ${inputFilePath} --outFile ${outputFilePath}`, { stdio: 'inherit' });
            execSync(`tsc ${inputFilePath} --outFile ${outputFilePath}`, { stdio: 'inherit' });
            console.log(`Compilation successful: ${outputFilePath}`);
        } catch (error) {
            console.error(`Error compiling ${file}:`, error);
        }
    });
});
