
import { ref, onMounted, onBeforeUnmount } from 'vue'
import {ProcessSensorUnit,SensorAddAverageData} from "@/uitls/sensor";
// import { initialSensors } from './DesktopSkinData/initialSensors1'
import { Sensor } from '../../Game_DMComponent/sharedTypes';
import {gamepp} from 'gamepp'
import img_people from '../../../assets/img/monitoring/img3/fg_img_people_01.gif';
import img_people2 from '../../../assets/img/monitoring/img3/fg_img_people_02.gif';

export default function useSensorData() {
    const bg_sensor_data = ref<any>(null)
    const sensorValues = ref<Record<number, any>>({})
    const sensorRawValues = ref<Record<number, number>>({});
    const ProgressValues = ref<Record<number, any>>({})
    const interval = ref<number>()
    const sensors = ref<Sensor[]>([])
    const seriesData = ref<Record<number, number[]>>({}); 

    const initializeSensors = async (id: number) => {
      console.log(id, 'id')
      let initialData: Sensor[] = [];
      // 重置所有图表数据
      // sensors.value.forEach(sensor => {
      //   if (sensor.nums === 10) {
      //     seriesData.value = [];
      //   }
      // });
      try {
        const initialSensorsModule = await import(`./DesktopSkinData/initialSensors${id}.ts`);
        initialData = initialSensorsModule.initialSensors;
        console.log(initialData, 'initialData')
      } catch (error) {}
      const storageKey = `sensorSettings_${id}`;
      if (localStorage.getItem(storageKey) === null || JSON.parse(localStorage.getItem(storageKey)).length === 0) {
        console.log('1') 
        sensors.value = initialData.map((sensor: any) => ({
          ...sensor,
          style: {
            ...sensor.style,
            shadow: sensor.style.shadow ? {
              enabled: false,
              ...sensor.style.shadow
            } : null
          }
        }))
        localStorage.setItem(storageKey, JSON.stringify(sensors.value))
      } else {
        try {
          const saved = localStorage.getItem(storageKey)
          // console.log(saved,'saved')
          if (saved) {
            console.log('2') 
            sensors.value = JSON.parse(saved).map((item: Sensor) => ({
              ...item,
              mediaSrc: item.mediaSrc,
              processedSvg: item.processedSvg,
              style: {
                ...item.style,
                left: Number(item.style.left) || 0,
                top: Number(item.style.top) || 0,
              }
            })) 
          }
          } catch (e) {
            sensors.value = initialData
          }
        // sensors.value = JSON.parse(localStorage.getItem('sensorSettings') || '[]');
      
      }
    };
  
    const getModuleStyle = (sensor: Sensor) => {
      // const s = sensor.style.shadow ?? {}
      return {
        zIndex: sensor.style.zIndex,
        position: 'absolute',
        width: sensor.style.width ? `${sensor.style.width}px` : 'auto',
        height: sensor.style.height ? `${sensor.style.height}px` : 'auto',
        fontFamily: sensor.style.fontFamily,
        fontWeight: sensor.style.fontWeight,
        fontStyle: sensor.style.fontStyle,
        fontSize: `${sensor.style.fontSize}px`,
        color: sensor.style.color,
      }
    }
  
    const getWrapperStyle = (sensor: Sensor, index: number) => ({
      position: 'absolute',
      left: `${sensor.style?.left}px`,
      top: `${sensor.style?.top}px`,
      zIndex: sensor.style.zIndex,
    })
  
    const getTextShadow = (sensor: Sensor) => {
      const s = sensor.style.shadow
      return s?.enabled ? `${s.x}px ${s.y}px ${s.blur}px ${s.color}` : ''
    }

    const updateSensorValues = () => {
      if(sensors.value === null || sensors.value.length === 0)return
      sensors.value.forEach((sensor: any) => {
          const result = calculateValue(sensor);
          if (result === undefined || result === null) return;
          const isProgressType = sensor.type2 === 'progress' && typeof result === 'object' && result !== null;
          const isUsageParameter = sensor.parameters === 'd_usage' || sensor.parameters === 'mem_usage' || sensor.type2 === 'graphic';
          if (isProgressType || isUsageParameter) {
              sensorValues.value[sensor.id] = result.calculatedValue;
              sensorRawValues.value[sensor.id] = result.rawValue;
          } else {
              sensorValues.value[sensor.id] = result;
          }
          
      });
  };
    // const toPercent = (value: number, maxValue: number) => {
    //   return Math.round((value / maxValue) * 100);
    // };

    // 记录每个 sensor.id 的最大值
    const maxValueHistoryMap = new Map<number, {maxValue: number, sensorName: string}>();

    function getDefaultMaxValue(unit: string, value: number, sensor: any): number {
      switch (unit) {
        case '℃':
        case '°C':
        case 'W':
        case '%':
          return Math.max(100, value);
        case 'RPM':
          const belongType = sensor.belong || sensor.sensor;
          if (belongType.includes('GPU')) return Math.max(3000, value);
          if (belongType.includes('CPU')) return Math.max(2000, value);
          return Math.max(2000, value);
        case 'MHz':
          return value > 3000 ? value + 50 : 3000;
        case 'V':
          return value > 3 ? value  : 1.5;
        default:
          return Math.max(100, value);
          // return null;
      }
    }

    const calculateValue = (sensor: { id: number; nums: number;class:string, page: number; type: string; type2: string; remark: string; sensor: string; belong:string; unit: string; unitshow: boolean; mediaSrc: string; processedSvg: string; parameters: string; style: { [x: string]: any; zIndex: number; fontSize: number; color: string; top: number; left: number; width: number; height: number; fontFamily: string; fontWeight: string; fontStyle: string; strokeColor: string; pathFills: string[]; shadow: { [x: string]: any; enabled?: boolean | undefined; x?: number | undefined; y?: number | undefined; blur?: number | undefined; color?: string | undefined; }; }; timeType: string; timeFormat: string; timeRule: string; group: boolean; showDetails: boolean; hideDetails: boolean; settop: boolean; showdelete: boolean; enter: boolean; }) => {
      const historyRecord = maxValueHistoryMap.get(sensor.id);
      // 传感器名称变化，重置这个的最大值
      if (historyRecord && historyRecord.sensorName !== sensor.sensor) {
        maxValueHistoryMap.delete(sensor.id);
      }

      if(bg_sensor_data.value == null || bg_sensor_data.value == undefined || bg_sensor_data.value == '') return
      if (sensor.enter === true  && ['sensor', 'progress', 'graphic', 'img','chart'].includes(sensor.type2)) {
          if (sensor.sensor.includes('CPU') && bg_sensor_data.value.cpu) {
            // 'temp'/'usage'/'clock'/'power'
            const param = sensor.parameters; 
            if (['sensor', 'img', 'chart'].includes(sensor.type2)) {
              // return `${bg_sensor_data.value.cpu[param] || 0}`;
              const value = `${bg_sensor_data.value.cpu[param] || 0}`
              if (!seriesData.value[sensor.id]) {
                seriesData.value[sensor.id] = [];
              }
              if(sensor.class ==='imgShape1' && param === 'temp'){
                sensor.mediaSrc = Number(value) >= 80 ? img_people2 : img_people;
              }
              seriesData.value[sensor.id].push(Number(value));
              if (seriesData.value[sensor.id].length > 10) {
                seriesData.value[sensor.id].shift(); // 如果长度超过 10，移除第一个元素
              }
              return value;
            }else if(sensor.type2 === 'progress' || sensor.type2 === 'graphic'){
              const unit = sensor.unit;
              const value = bg_sensor_data.value.cpu[param] || 0;
              const currentMaxValue = getDefaultMaxValue(unit, value,sensor);
              // 更新历史最大值：如果 currentMaxValue 比之前的大就更新，否则保留原值
              let record = maxValueHistoryMap.get(sensor.id);
              if (!record) {
                record = {maxValue: currentMaxValue, sensorName:sensor.sensor}; 
                maxValueHistoryMap.set(sensor.id, record);
              } else {
                record.maxValue = Math.max(record.maxValue, currentMaxValue);
                record.sensorName = sensor.sensor;
              }
              const maxValue = record.maxValue;
              // console.log('传感器名称变化', sensor.sensor,historyRecord);
              return {
                  calculatedValue: Math.round((value / maxValue) * 100),
                  rawValue: value
              };
              // return `${toPercent(value, maxValue)}`;
            }
           
          }else if (sensor.sensor.includes('GPU') && bg_sensor_data.value.gpu) {
            const param = sensor.parameters;
            if (['sensor', 'img', 'chart'].includes(sensor.type2)) {
              // return `${bg_sensor_data.value.gpu[param] || 0}`;
               const value = `${bg_sensor_data.value.gpu[param] || 0}`
              if (!seriesData.value[sensor.id]) {
                seriesData.value[sensor.id] = [];
              }
              seriesData.value[sensor.id].push(Number(value));
              if (seriesData.value[sensor.id].length > 10) {
                seriesData.value[sensor.id].shift();
              }
              return value;
            }
            else if(sensor.type2 === 'progress' || sensor.type2 === 'graphic'){
              const unit = sensor.unit;
              const value = bg_sensor_data.value.gpu[param] || 0;

              const currentMaxValue = getDefaultMaxValue(unit, value,sensor);
              let record = maxValueHistoryMap.get(sensor.id);
              if (!record) {
                record = {maxValue: currentMaxValue, sensorName:sensor.sensor};
                maxValueHistoryMap.set(sensor.id, record);
              } else {
                record.maxValue = Math.max(record.maxValue, currentMaxValue);
                record.sensorName = sensor.sensor;
              }
              const maxValue = record.maxValue;
              return {
                  calculatedValue: Math.round((value / maxValue) * 100),
                  rawValue: value
              };
              // return Math.round((value / maxValue) * 100);
            }

          }else if (sensor.sensor.includes('network')&& bg_sensor_data.value.network) {
            const param = sensor.parameters;
            return `${bg_sensor_data.value.network[param] || 0}`;
          }else if (sensor.sensor.includes('DRAM')&& bg_sensor_data.value.memory) {
            let MemoryTotal = Math.round(bg_sensor_data.value.memory.usage_mb / 1024);
            let Memorysize = parseFloat(Number(bg_sensor_data.value.memory.size / 1024).toFixed(0));
            if(sensor.parameters === 'usage_mb'){
              return `${MemoryTotal || 0}`;
            }else if(sensor.parameters === 'size'){
              return `${Memorysize || 0}`;
            }else if(sensor.parameters === 'd_usage'){
              let MemoryUse = parseFloat(bg_sensor_data.value.memory.usage);
              // return `${MemoryUse}`;
              return {
                calculatedValue: `${MemoryUse}`,
                rawValue: `${MemoryUse}`
              };
              // return `${toPercent(MemoryTotal, Memorysize)}`;
            }
          }else if (sensor.sensor.includes('VRAM') && bg_sensor_data.value.gpu) {
            let MemoryUse = parseFloat((((bg_sensor_data.value.gpu.mem_size * bg_sensor_data.value.gpu.mem_usage) / 100) / 1024).toFixed(1));
            let MemoryTotal = parseFloat(((bg_sensor_data.value.gpu.mem_size) / 1024).toFixed(1));
            if(sensor.parameters === 'mem_usage_mb'){
              return `${MemoryUse || 0}`;
            }else if(sensor.parameters === 'mem_size'){
              return `${MemoryTotal || 0}`;
            }else if(sensor.parameters === 'mem_usage'){
              let mem_usage = parseFloat(bg_sensor_data.value.gpu.mem_usage);
              // return `${mem_usage}`;
              return {
                calculatedValue: `${mem_usage}`,
                rawValue: `${mem_usage}`
              };
              // return `${toPercent(MemoryUse, MemoryTotal)}`;
            }
          }
          // return sensor.sensor; 
          
      }else if (sensor.enter === false  && ['sensor', 'progress', 'graphic','img','chart'].includes(sensor.type2)){
          let HWInfoJSONState = gamepp.hardware.getJSONState.sync();
          if (HWInfoJSONState != 1) {
            return;
          }
          var SensorInfoStr = null
          try {
            SensorInfoStr = gamepp.hardware.getSensorInfo.sync()
          } catch {
          }
          const SensorInfo = JSON.parse(SensorInfoStr)
          // console.log(SensorInfo)
          const booleanArr = ['Critical Temperature', 'Thermal Throttling', 'Power Limit Exceeded', 'IA: ', 'GT: ', 'RING:', 'Drive Failure', 'Drive Warning', 'Chassis Intrusion', 'Performance Limit']
          const SensorList = []
          const SensorInfoKeys = Object.keys(SensorInfo)
          let SensorInfoTidyUp = SensorAddAverageData(SensorInfo, SensorInfoKeys)
          let sensor_collected:any = window.localStorage.getItem('collected_sensor_list')
          if (sensor_collected) {
            sensor_collected = JSON.parse(sensor_collected)
          }else{
            sensor_collected = []
          }
          let matchedSensorInfo = null;
          const regex = /^(.*?)(:|\s|$)/ // 匹配第一个冒号或空格之前的内容
          for (let i = 0; i < SensorInfoKeys.length; i++) {
            const SensorInfoKey = SensorInfoKeys[i]
            // let Datas = SensorInfo[SensorInfoKey]
            let Datas = SensorInfoTidyUp[SensorInfoKey]
            const SensorObj:any = {}
            let SensorListText = ''
            SensorObj.name = SensorInfoKey
            const match = SensorInfoKey.match(regex)
            SensorObj.type = match ? match[1].trim() : ''
            SensorObj.Sensoritem = []
            if (!Datas) {
              Datas = [{
                Null: '',
                name: ''
              }]
            }
            for (let j = 0; j < Datas.length; j++) {
              const Key = Object.keys(Datas[j])[0]
              const Data = Datas[j][Key]
              const SensoritemObj:any = {}
              const ProcessKey = ProcessSensorUnit(Key, Data.type)
              if (!SensorListText.includes(ProcessKey.UnitText)) {
                SensorListText += ProcessKey.UnitText
              }
              if (booleanArr.find(item => Key.includes(item))) {
                SensoritemObj.value = Number(Data.value) ? 'Yes' : 'No';
              } else {
                const toFixedValue = Number.isInteger(ProcessKey.ToFixed) ? ProcessKey.ToFixed : 2;
                if (Data.value) {
                  SensoritemObj.value = Number(Data.value).toFixed(toFixedValue);
                } else {
                  SensoritemObj.value = 'Null';
                }
              }
              SensoritemObj.name = Key
              SensoritemObj.unit = ProcessKey.DataUnit
              SensoritemObj.choosen = false
              SensoritemObj.collect = false
              let sensor_collected_list:any =''
              if (sensor_collected_list.length > 0) {
                const findIndex = sensor_collected_list.findIndex((item: { mainName: string; name: string; }) => {
                  return item.mainName === SensorInfoKey && item.name === Key
                })
                if (findIndex !== -1) SensoritemObj.collect = true
              }
              SensorObj.Sensoritem.push(SensoritemObj)
            }
            SensorObj.UnitText = SensorListText
            SensorList.push(SensorObj)
            for (let j = 0; j < SensorObj.Sensoritem.length; j++) {
              const SensorItem = SensorObj.Sensoritem[j];
              if (SensorItem.name === sensor.sensor) {
                  matchedSensorInfo = {
                      value: SensorItem.value,
                      unit: SensorItem.unit,
                  };
                  sensor.unit = SensorItem.unit;
                  break;
              }
            }
            if (matchedSensorInfo) {
                break;
            }
            
          }
          if (matchedSensorInfo) {
            if (['sensor', 'img', 'chart'].includes(sensor.type2)) {
              if (!seriesData.value[sensor.id]) {
                seriesData.value[sensor.id] = [];
              }
              seriesData.value[sensor.id].push(matchedSensorInfo.value);
              if (seriesData.value[sensor.id].length > 10) {
                seriesData.value[sensor.id].shift();
              }
              if (matchedSensorInfo.unit.includes('V')) {
                const formattedValue = parseFloat(matchedSensorInfo.value).toFixed(2);
                return formattedValue
              }else{
                // console.log(matchedSensorInfo.value, matchedSensorInfo.unit ,'V');
                const formattedValue = Math.round(matchedSensorInfo.value)
                return formattedValue
              }
              // return matchedSensorInfo.value

            }else if (sensor.type2 === 'progress' || sensor.type2 === 'graphic') {
              const value = parseFloat(matchedSensorInfo.value);
              const unit = sensor.unit.replace(/\s+/g, '');
              // console.log(matchedSensorInfo.value,'progress',unit);
              // let currentMaxValue: number
              const currentMaxValue = getDefaultMaxValue(unit, value,sensor);
              let record = maxValueHistoryMap.get(sensor.id);
              if (!record) {
                record = {maxValue: currentMaxValue, sensorName:sensor.sensor};
                maxValueHistoryMap.set(sensor.id, record);
              } else {
                record.maxValue = Math.max(record.maxValue, currentMaxValue);
                record.sensorName = sensor.sensor;
              }
              const maxValue = record.maxValue;
              if (matchedSensorInfo.unit.includes('V')) {
                const formattedValue = value.toFixed(2);
                return {
                    calculatedValue: Math.round((value / maxValue) * 100),
                    rawValue: formattedValue
                };
              }else{
                console.log('修改后传感器重置最大值', sensor.sensor,historyRecord);
                // console.log((value / maxValue) * 100,'maxValue');
                // return Math.round((value / maxValue) * 100);
                const formattedValue = Math.round(value)
                return {
                  calculatedValue: Math.round((value / maxValue) * 100),
                  rawValue: formattedValue
                };
             }
            }
          }
        }
        
        else if (sensor.type2 === 'text') {
          return sensor.remark; 
        }
        return ''; 
    }
  
    onMounted(() => {
      bg_sensor_data.value = JSON.parse(localStorage.getItem('bg_sensor_data') || 'null')
      // updateSensorValues()
      let DataRefresh = gamepp.setting.getInteger.sync(450)
      let dataTime = Number(DataRefresh);
      console.log(dataTime)
      // let dataTime = JSON.stringify(DataRefresh);
      interval.value = window.setInterval(() => {
        bg_sensor_data.value = JSON.parse(localStorage.getItem('bg_sensor_data') || 'null')
        updateSensorValues()
      }, dataTime)
    })
  
    onBeforeUnmount(() => {
      if (interval.value) clearInterval(interval.value)
    })
  
    // const filteredSensors = computed(() => (page: number) => 
    //   sensors.value.filter(s => s.page === page)
    // )
  
    return {
      sensors,
      sensorValues,
      sensorRawValues,
      ProgressValues,
      initializeSensors,
      getModuleStyle,
      getWrapperStyle,
      getTextShadow,
      seriesData,
    //   filteredSensors
    }
}


