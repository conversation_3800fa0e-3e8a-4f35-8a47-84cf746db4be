import {defineStore} from 'pinia'
// import systemList from "@/modules/Game_CoreAssign/systemProcessList.json";
import md5 from "md5";
import Axios from 'axios';
// @ts-ignore
import CryptoJS from 'crypto-js';

export type LogicalCore = {
    CPUSetId: number
    LogicalCore: number
}

export interface Core {
    Core: number
    EfficiencyClass: number
    SchedulingClass: number
    LogicalCores: Array<LogicalCore>
    SMT: number
    Die: number
}

export interface CPUComplexInfo {
    CacheL3Size: number
    Complex: number
    Cores: Array<Core>
    ComplexId: number
    Group: number
}

export interface Process {
    name: string
    path: any
    icon: string
    pid: any
    cpu_usage: any
    gpu_usage: number
    mem_usage: number
    mem_usage_mb: number
    cpu_pp: number
    gpu_pp: number
    mem_pp: number
}

export interface ProcessWithGroup extends Process{
    group: string
}
export interface DisplayProcessGroup {
    id: string
    name: string
    cpuSet: Array<number>
}
// @ts-ignore
const gamepp = window.gamepp as any;

const defaultArr = [
    // 聊天工具：
    "QQ.exe",
    "WeChat.exe",
    "DingTalk.exe",
    // 浏览器：
    "360se.exe",
    "360chrome.exe",
    "qqbrowser.exe",
    "2345explorer.exe",
    "chrome.exe",
    "msedge.exe",
    "Baidu.exe",
    // 语音：
    "Discord.exe",
    "KOOK.exe",
    "oopz.exe",
    "YY.exe",
    "iSpeak.exe",
    "qtalk.exe",
    // 音乐：
    "YOUKU.exe",
    "qqmusic.exe",
    "cloudmusic.exe",
    // 监控：
    "HwMonitor64.exe",
    // 壁纸：
    "wallpaper64.exe",
    "wallpaper32.exe",
    "baiduwallpaper.exe",
    // 视频：
    "obs64.exe",
    "qqlive.exe",
    "iqiyi.exe",
    "bilibilipc.exe",
    "直播伴侣.exe",
    //直播：
    "huyagamelive.exe",
    "dy_PCClient.exe",
    "OBSDanmu.exe",
    "DYToolEx.exe",
    // 平台：
    "Battle.net.exe",
    "steam.exe",
    "epic.exe",
    "wegame.exe",
    //下载器：
    "thunder.exe",
    "Xunlei11.exe",
    "BaiduNetdisk.exe",
    "bitcomet.exe",
    //其他：
    "SunloginClient.exe",
    "ToDesk.exe",
    "wpsoffice.exe",
    "office.exe",
    "wpp.exe",
]

export const processCoreAssignStore = defineStore('processCoreAssign', {
    state: () => ({
        display: { // 页面显示用的
            cpuName: '',
            cpuLength: '',
            memory_size_mb: 0,
            local_process: [] as Array<ProcessWithGroup>,
            process_groups: [] as Array<DisplayProcessGroup>,
            activeGroup: '',
            showAddProcessDialog: false,
            showChooseCPUSetDialog: false,// 选cpuSet的对话框
        },
        process_list: [] as Array<ProcessWithGroup>,
        process_list_history: [] as Array<ProcessWithGroup>,
        timer: null as any,
        cpuComplexInfo: <Array<CPUComplexInfo>>[],
        systemCpuSet: [],
        enableCpuSet: false,
        curGroupCpuSet:[] as Array<number>,
        mouseEnterGroupId: '',//鼠标拖动进入的组id
    }),
    actions: {
        async init() {
            console.log(this)
            const localData = window.localStorage.getItem('pcaqwerasd')
            if (localData !== 'a') {
                const groupId = this.addProcessGroup('psc.notGameProcess')
                defaultArr.forEach((item)=>{
                    this.display.local_process.push({
                        name: item,
                        path: item,
                        icon: '',
                        pid: -1,
                        cpu_usage: 0,
                        gpu_usage: 0,
                        mem_usage: 0,
                        mem_usage_mb: 0,
                        cpu_pp: 0,
                        gpu_pp: 0,
                        mem_pp: 0,
                        group: groupId,
                    })
                })
                window.localStorage.setItem('pcaqwerasd', 'a')
                window.localStorage.setItem('local_process', JSON.stringify(this.display.local_process))
            }
            const isInit = await gamepp.isInitAllProcessDetail.promise()
            const duration = 1000
            if (!isInit) {
                await gamepp.initAllProcessDetail.promise(duration,duration); // 初始化进程信息模块，param1 循环查询进程时间，param2 循环查询进程详细信息时间
            }

            if (window.localStorage.getItem('enableCpuSet') === 'true') {
                this.enableCpuSet = true
            }
            this.timer = setInterval(this.getProcessList,duration)
        },
        getProcessList() {
            if ((window as any).dragging || (window as any).dragging2) return
            handleGetProcessList(this.process_list_history,this.display.memory_size_mb).then((list)=>{
                this.process_list = list
                this.process_list_history = list

                // 更新用户添加的进程的各种数据
                this.display.local_process.forEach((item)=>{
                    const lname = item.name
                    const findV = list.find(v=>v.name === lname)
                    if (findV) {
                        item.pid = findV.pid
                        item.path = findV.path
                        item.icon = findV.icon
                        item.cpu_usage = findV.cpu_usage
                        item.gpu_usage = findV.gpu_usage
                        item.mem_usage = findV.mem_usage
                        item.mem_usage_mb = findV.mem_usage_mb
                        item.cpu_pp = findV.cpu_pp
                        item.gpu_pp = findV.gpu_pp
                        item.mem_pp = findV.mem_pp
                    }
                })
            },(e)=>{
                console.log(e)
            })
        },
        async getCpuInfo(n = 0) {
            try {
                if (n == 60) {
                    return
                }
                // CPUComplexInfo数组每一个代表一个CCD Cores数组下的每项的LogicalCore数组如果长度是2代表开了超线程
                this.cpuComplexInfo = await gamepp.getCPUComplexInfo.promise();
                console.log(this.cpuComplexInfo)
                // 通过原始数据拿CPU名字
                let HwInfoJsonStr = await gamepp.hardware.getBaseJsonInfo.promise();
                if (HwInfoJsonStr) {
                    let HwInfoJson = JSON.parse(HwInfoJsonStr);
                    this.display.cpuName = HwInfoJson.CPU.SubNode[0].ProcessorName;
                    // 获取cpu名
                    if (HwInfoJson.CPU.SubNode.length > 1) {
                        this.display.cpuLength = ' x ' + HwInfoJson.CPU.SubNode.length;
                    }
                    // 获取内存大小
                    try {
                        this.display.memory_size_mb = Number(HwInfoJson?.MEMORY.Property["TotalMemorySize[MB]"])
                    }catch (e) {
                        this.display.memory_size_mb = 0
                    }
                } else {
                    setTimeout(() => {
                        this.getCpuInfo(n+1)
                    }, 500)
                }
            }catch(e){
                console.log(e);
            }
        },
        addProcessGroup(name?:string) {
            const id = Math.random().toString(36).substr(2, 9)
            this.display.process_groups.push({
                id: id,
                name: name ? name : 'psc.unNamedProcess',
                cpuSet: [],
            })
            window.localStorage.setItem('process_groups', JSON.stringify(this.display.process_groups))
            return id
        },
        saveProcessGroup() {
            window.localStorage.setItem('process_groups', JSON.stringify(this.display.process_groups))
        },
        addLocalProcess(process:ProcessWithGroup,groupId?:string) {
            if (groupId) {
                process.group = groupId;
            }else{
                process.group = this.display.activeGroup;
            }
            // 检查local_process中之前有没有添加过
            const findIndex = this.display.local_process.findIndex(item=>item.path === process.path)
            if (findIndex !== -1) {
                this.display.local_process.splice(findIndex,1)
            }
            this.display.local_process.push(process)
            window.localStorage.setItem('local_process', JSON.stringify(this.display.local_process))
            this.disableCpuSet()
        },
        removeProcessGroup (groupId:string) {
            for (let i = 0; i < this.display.local_process.length; i++) {
                if  (this.display.local_process[i].group === groupId) {
                    this.display.local_process[i].group = ''
                }
            }
            this.display.process_groups = this.display.process_groups.filter(item=>item.id !== groupId)
            this.saveProcessGroup()
            window.localStorage.setItem('local_process', JSON.stringify(this.display.local_process))
            this.display.activeGroup = ''
            this.disableCpuSet()
        },
        removeProcessFromGroup (process:ProcessWithGroup) {
            const i = this.display.local_process.findIndex(v=>v.path === process.path)
            if (i !== -1) {
                this.display.local_process.splice(i,1);
                window.localStorage.setItem('local_process', JSON.stringify(this.display.local_process))
            }
            this.disableCpuSet()
        },
        changeProcessGroup (groupId:string,process:ProcessWithGroup) {
            const i = this.display.local_process.findIndex(v=>v.path === process.path)
            if (i !== -1) {
                this.display.local_process[i].group = groupId
                window.localStorage.setItem('local_process', JSON.stringify(this.display.local_process))
            }
            this.disableCpuSet()
        },
        async getGroupCpuSet() {
            try {this.systemCpuSet = await gamepp.getSystemCpuSet.promise();}catch (e) {
                this.systemCpuSet = [];
            }
            const f = this.display.process_groups.find((item)=>{
                return item.id == this.display.activeGroup
            })
            if (f) {
               this.curGroupCpuSet = f.cpuSet
            }
        },
        addCpuSet(arr: Array<number>) {
            arr.forEach((item: number) => {
                if (!this.curGroupCpuSet.includes(item)) {
                    this.curGroupCpuSet.push(item);
                }
            })
        },
        removeCpuSet(arr: Array<number>) {
            arr.forEach((item: number) => {
                const index = this.curGroupCpuSet.findIndex((i: number) => i === item);
                if (index !== -1) {
                    this.curGroupCpuSet.splice(index, 1);
                }
            })
        },
        isActiveCpuSet(arr: Array<any>) {
            return arr.every((item) => {
                return this.curGroupCpuSet.includes(item);
            })
        },
        // 启用优化
        async handleEnableCpuSet() {
            this.enableCpuSet = true

            const _set = new Set()
            const _arr:Array<any> = []
            const _obj:any = {}
            this.display.process_groups.forEach(({id,cpuSet})=>{
                _obj[id] = JSON.parse(JSON.stringify(cpuSet));
            })
            // 取出要设置的所有进程名
            this.display.local_process.forEach(({name,group})=>{
                if (group) {
                    _set.add(name)
                }
            })
            // 活动的进程
            this.process_list.forEach(({name,pid})=>{
                console.log(pid)
                if (_set.has(name)) {
                    const findItem = this.display.local_process.find((item)=>item.name === name)
                    if (findItem) {
                        _arr.push({
                            pid,cpuSet:_obj[findItem.group]
                        })
                    }
                    UpdateDataToServer({name:findItem.name,cpuSet:_obj[findItem.group]},this.display.cpuName)
                }
            })
            console.log('setProcessCpuSet');
            console.log(_obj)
            console.log(_arr)
            for (let i = 0; i < _arr.length; i++) {
                const v = _arr[i];
                console.log('params', v['pid'], v['cpuSet']);
                await gamepp.setProcessCpuSet.promise(v['pid'], v['cpuSet'])
            }
            window.localStorage.setItem('enableCpuSet', 'true')
        },
        // 取消优化
        async disableCpuSet() {
            this.enableCpuSet = false
            this.process_list.forEach(({pid})=>{
                if (pid) gamepp.setProcessCpuSet.promise(pid,[])
            })
            window.localStorage.removeItem('enableCpuSet')
        },
    },
    getters: {
        isIntel(state) {
            return state.display.cpuName.toLowerCase().includes('intel')
        },
        cpuCores(state) { // cpu核心数
            let count = 0
            state.cpuComplexInfo.forEach(item => {
                count += item.Cores.length
            })
            return count
        },
        cpuThreads(state) { // cpu线程数
            let count = 0
            state.cpuComplexInfo.forEach(item => {
                item.Cores.forEach(core => {
                    count += core.LogicalCores.length
                })
            })
            return count
        }
    }
})
const iconList = new Set() // 生成过的icon列表
async function handleGetProcessList(historyList:Array<any>,memory_size_mb:number) { // 获取进程列表，包含icon
    const AllProcessDetailList = await gamepp.getAllProcessDetail.promise()
    const _arr = []
    // 获取icon
    for (let index = 0; index < AllProcessDetailList.length; index++) {
        const ProcessDetail = AllProcessDetailList[index]

        // 获取进程名
        const processName = getProcessName(ProcessDetail.process_full_path);
        if (!processName || !ProcessDetail.hasOwnProperty('pid') || !ProcessDetail.hasOwnProperty('cpu_usage')) continue;

        // 进程名图标
        const iconName = md5(processName)
        const iconPath = await gamepp.getAppDataDir.promise() + '\\common\\icon\\' + iconName + '.png'

        const tempObj = {
            name: processName,
            path: ProcessDetail.process_full_path,
            icon: iconPath,
            pid: ProcessDetail.pid, // 进程pid
            cpu_usage: ProcessDetail.cpu_usage.toFixed(2)*1, // 进程cpu占用
            gpu_usage: 0, // 进程gpu占用
            mem_usage: 0, // 进程内存占用
            mem_usage_mb: 0, // 进程内存占用MB
            cpu_pp: 0,
            gpu_pp: 0,
            mem_pp: 0,
            group: '',
        }

        // 算gpu占用
        if (ProcessDetail.gpu_utilizations && Array.isArray(ProcessDetail.gpu_utilizations)) {
            let gpu_load = 0
            for (let i = 0; i < ProcessDetail.gpu_utilizations.length; i++) {
                gpu_load += (ProcessDetail.gpu_utilizations[i].gpu_load || 0)
            }
            tempObj.gpu_usage = Number(gpu_load.toFixed(2))
        }

        // 算内存占用
        if (ProcessDetail.mem_working_set_size) {
            tempObj.mem_usage_mb = Number(ProcessDetail.mem_working_set_size) / 1024 / 1024
            if (memory_size_mb !== 0) {
                tempObj.mem_usage = Math.round(tempObj.mem_usage_mb / memory_size_mb * 100)
            }
        }

        // 算pp值
        const findItem = historyList.find(item => item.name === processName)
        if (findItem) {
            tempObj.cpu_pp = calculatePP(findItem.cpu_pp, tempObj.cpu_usage)
            tempObj.gpu_pp = calculatePP(findItem.gpu_pp, tempObj.gpu_usage)
            tempObj.mem_pp = calculatePP(findItem.mem_pp, tempObj.mem_usage)
        }else{
            tempObj.cpu_pp = calculatePP(0, tempObj.cpu_usage)
            tempObj.gpu_pp = calculatePP(0, tempObj.gpu_usage)
            tempObj.mem_pp = calculatePP(0, tempObj.mem_usage)
        }

        if (!iconList.has(iconName)) { // 生成进程图标
            await gamepp.SaveProcessIconToFile.promise(ProcessDetail.process_full_path, iconName + '.png', 'image/png')
            iconList.add(iconName)
        }

        _arr.push(tempObj)
    }
    return _arr
}

// 计算PP值
function calculatePP(N: number, M: number) {
    return N * 0.9 + M * 0.1
}

// 获取进程名
function getProcessName(filePath: string) {
    if (!filePath || typeof filePath !== 'string') return ''
    const parts = filePath.split('\\');
    return parts[parts.length - 1];
}

var UpdateDataToServerTimeout: any = null

async function UpdateDataToServer(v: any, cpuName: string) {
    // 参数校验
    if (!cpuName) return
    if (!isObject(v)) return
    if (!('name' in v && 'cpuSet' in v)) return
    // 防抖 0.5s后执行
    UpdateDataToServerTimeout && clearTimeout(UpdateDataToServerTimeout)
    UpdateDataToServerTimeout = setTimeout(async () => {
        let updatedProcessList: any = window.localStorage.getItem('updatedProcessList') // 获取已经上传过的数据
        if (!updatedProcessList) {
            updatedProcessList = {}
        } else {
            updatedProcessList = JSON.parse(updatedProcessList)
        }
        let data = {
            cpuName: cpuName,
            cpuSet: v['cpuSet'],
            processName: v['name']
        }
        // 更新已经上传过的数据
        updatedProcessList[v['name']] = data
        window.localStorage.setItem('updatedProcessList', JSON.stringify(updatedProcessList))

        try {
            let upload_obj = {
                mid: await gamepp.getMID.promise(),
                version: await gamepp.getPlatformVersion.promise(),
                cpuName: cpuName,
                cpuSet: v['cpuSet'],
                processName: v['name']
            }
            console.log(upload_obj);
            let aseKey = "gamepp@!123";
            let message = JSON.stringify(upload_obj);
            let des_upload_obj = encryptByDES(message, aseKey);
            Axios({
                method: "get",
                url: 'https://t6.gamepp.com/log/set.html?info=' + des_upload_obj,
            }).then((res) => {
                console.log(res)
            });
        } catch (e) {
            console.log(e)
        }
    }, 500)
}

// 排序后判断两个数组是否相等
function arraysEqualRegardlessOfOrder(a: Array<number>, b: Array<number>) {
    if (a.length !== b.length) return false;

    const sortedA = [...a].sort((a, b) => a - b);
    const sortedB = [...b].sort((a, b) => a - b);

    for (let i = 0; i < sortedA.length; i++) {
        if (sortedA[i] !== sortedB[i]) return false;
    }

    return true;
}

// 判断变量是否为对象
function isObject(obj: any) {
    return obj !== null && typeof obj === 'object' && !Array.isArray(obj);
}

// 加密
function encryptByDES(message: string, key: string) {
    return CryptoJS.DES.encrypt(message, CryptoJS.enc.Utf8.parse(key), {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.ZeroPadding
    }).toString();
}
