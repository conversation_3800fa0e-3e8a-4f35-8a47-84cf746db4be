<!-- 游戏加加更新启动页 -->
<script setup lang="ts">

import { <PERSON><PERSON>, Close } from "@element-plus/icons-vue";
import {onMounted, Ref, ref} from "vue";
import {LoadModuleStepInterface} from "@/modules/Game_Update/types/Interface";
import { ClickOutside } from "element-plus";
import { gpu_brand,showNumberOfLogicalCpus,showNumberOfCPUCores ,AVGNum,MaxNum,MinNum,FormatSeconds,FormatTimeCn,FormatTime,FormatTimePlus, DiskCapacityConversion,FormartMonthToNumber,RemoveAllSpace,RegExGetNum} from '../../../uitls/GameppTools'

// @ts-ignore
const gamepp = window.gamepp as any;

let DataList:any = ref([])
let realData:any = ref([])
let DatabaseId:any
let AppDataDir:any
onMounted (async () => {
     await initDataBase()
})

const initDataBase = async () =>
{
      AppDataDir = await gamepp.getAppDataDir.promise();
      DatabaseId = await gamepp.database.open.promise(AppDataDir + '\\common\\Data\\GamePPStressNew.dll');
      DataList.value = await gamepp.database.query.promise(DatabaseId, "'" + 'list' + "'", "*");
      console.warn('%c DataBaseList::','color:green',DataList.value);
      let result = DataList.value.reverse().slice(0, 10);
      DataList.value = JSON.parse(JSON.stringify(result))
      console.warn('%c DataBaseList::','color:red',DataList.value);
      await handleListData()
      
}

const PM_GetHardwareBase = async (BaseJsonStr:any) =>
{ 
  let hardwareinfo:any = {}
  console.warn('GetHardwareBase Start');
   let data_arr = null;
   if (BaseJsonStr) {
      data_arr = JSON.parse(decodeURIComponent(BaseJsonStr));
   } else {
      data_arr = JSON.parse(await gamepp.hardware.getBaseJsonInfo.promise());
   }
  
   console.warn('硬件详情信息',data_arr);
   console.warn(typeof(data_arr));
   

   for(let key in data_arr)
   {
      let HWInfoV = data_arr[key]
      switch(key)
      {
         case 'COMPUTER':
           let SystemName = ''
           if (HWInfoV.OperatingSystem) 
           {
             SystemName = (HWInfoV.OperatingSystem
               .replace('Microsoft ', '')
               .replace('Build ', ''));
           }
           hardwareinfo['SystemName'] =  SystemName
           console.warn('SystemName',SystemName);
           
        break;
        case 'CPU':
          let CPUType = 'intel'
          if (HWInfoV['SubNode'] && HWInfoV['SubNode'][0]) 
          {
            const CPUSubNode = HWInfoV['SubNode'][0];
            // let OriginalProcessorFrequency = Number(CPUSubNode["OriginalProcessorFrequency[MHz]"]) - 50;
            if ((CPUSubNode['ProcessorName'] || '').includes('Intel')) {CPUType = 'intel'} else {CPUType = 'amd'}
            // console.warn('CPUType',CPUType);
            hardwareinfo['CPUType'] =  CPUType
            hardwareinfo['ProcessorName'] =  CPUSubNode['ProcessorName']
            hardwareinfo['CPUCores'] = showNumberOfCPUCores(CPUSubNode)
            hardwareinfo['LogicalCPUs'] = showNumberOfLogicalCpus(CPUSubNode)
            hardwareinfo['NumberofCPUCores'] = CPUSubNode.NumberofCPUCores
            hardwareinfo['NumberofLogicalCPUs'] = CPUSubNode.NumberofLogicalCPUs
            hardwareinfo['L3Cache'] = CPUSubNode.L3Cache
            hardwareinfo['CPUTechnology'] = CPUSubNode.CPUTechnology
            hardwareinfo['OriginalProcessorFrequency'] = CPUSubNode.OriginalProcessorFrequency
            hardwareinfo['CPUTurboMax'] = CPUSubNode.CPUTurboMax ? CPUSubNode.CPUTurboMax.toLowerCase().split('mhz')[0] + 'MHz' : ''
            hardwareinfo['InstructionTLB'] = CPUSubNode.InstructionTLB // 指令集
          }
        break;
        case 'MOBO':
          let MainboardName = '',SystemManufacturer,MotherboardChipset
          if (HWInfoV['Mainboard'] && HWInfoV['Mainboard']['MainboardName'])
          {
            MainboardName =  HWInfoV['Mainboard']['MainboardName'];
          }

          if (HWInfoV['Mainboard'] && HWInfoV['Mainboard']['MainboardManufacturer'])
          {
            if (HWInfoV['Mainboard']['MainboardManufacturer'] === 'Notebook')
            {
              SystemManufacturer = HWInfoV['System']['SystemManufacturer']
            }
            else
            {
              SystemManufacturer = HWInfoV['Mainboard']['MainboardManufacturer'].replace('Technology', '').replace('And', '').replace('Development', '').replace('Computer', '').replace('COMPUTER', '').replace('Co.,LTD', '').replace('INC.', '')
            }
          }
          else
          {
            SystemManufacturer = ''
          }
          if (HWInfoV['Property'] && HWInfoV['Property']['MotherboardChipset'] && typeof HWInfoV['Property']['MotherboardChipset'] === 'string')
          {
            MotherboardChipset = HWInfoV['Property']['MotherboardChipset'].replace(/\(.*?\)/g, '')
          }
          else
          {
            MotherboardChipset = ''
          }
          hardwareinfo['MainboardName'] =  MainboardName
          hardwareinfo['SystemManufacturer'] =  SystemManufacturer
          hardwareinfo['MotherboardChipset'] =  MotherboardChipset
          hardwareinfo['BIOSVersion'] =  HWInfoV.BIOS?.BIOSVersion
          if (!hardwareinfo['BIOSVersion']) hardwareinfo['BIOSVersion'] = ''
        break;
        case 'DRIVES':
            let gameDiskName =  hardwareinfo['gameDiskName']
            if (HWInfoV['SubNode'])
            {
              let drive_index:any = HWInfoV['SubNode'].findIndex((item:any) => item['DriveModel'] === gameDiskName);
              drive_index = drive_index !== -1 ? drive_index : 0;
              let DriveData = HWInfoV['SubNode'][drive_index]
              let drivesType = 'HDD'
              if ((DriveData.MediaRotationRate && (DriveData.MediaRotationRate).indexOf('SSD') !== -1) || (DriveData.DriveController)?.includes('NVMe') || (DriveData.Interface)?.includes('NVMe')) {drivesType = 'SSD';} else if (DriveData.MediaRotationRate && (DriveData.MediaRotationRate).includes('RPM')) {drivesType === 'HDD'} else {drivesType === 'HDD'}
              if (((DriveData.DriveModel)?.toLowerCase()).includes('ssd')) {drivesType = 'SSD'}
              hardwareinfo['drivesType'] = drivesType
              hardwareinfo['Drive_size'] = DiskCapacityConversion(DriveData['DriveCapacity[MB]'], 1024)
              hardwareinfo['Drive_size[MB]'] = DriveData['DriveCapacity[MB]']*1
              if (!gameDiskName) {
                hardwareinfo['disk_name'] = DriveData.DriveModel || ''
              }
              hardwareinfo['DriveController'] = DriveData.DriveController || ''
            }
            else
            {
              hardwareinfo['drivesType'] = ''
              hardwareinfo['Drive_size'] = ''
              hardwareinfo['Drive_size[MB]'] = 0
              hardwareinfo['disk_name'] = ''
              hardwareinfo['DriveController'] = ''
            }
        break;
        case 'GPU':
            let GPUIndex = FilterGPUIndexFromMemoryNumber(data_arr)

            function FilterGPUIndexFromMemoryNumber (JsonInfo:any) {
              let srcIndex = 0
              if (JsonInfo.GPU && JsonInfo.GPU.SubNode) {
                let Gpu_Count = JsonInfo.GPU.SubNode.length
                if (Gpu_Count === 1) { return(0)}
                for (let i = 0; i < JsonInfo.GPU.SubNode.length; i++) {
                  const currentNode = JsonInfo.GPU.SubNode[i];
                  const currentVideoMemoryNumber = currentNode.VideoMemoryNumber;
                  if (currentVideoMemoryNumber > (JsonInfo.GPU.SubNode[srcIndex].VideoMemoryNumber || 0)) {
                    srcIndex = i;
                  }
                }
              }else{
                return (0)
              }
              return(srcIndex)
            }
            // let GPUIndex = 0
            let GPUData = HWInfoV['SubNode'][GPUIndex]
            console.warn('显卡索引',GPUIndex);
            console.warn('显卡数据',GPUData);
            if (!GPUData) GPUData = {VideoCard:''}

            // hardwareinfo['GPU'] = GPUData['VideoChipset']
            hardwareinfo['GPU'] = String(GPUData['VideoCard'])
            hardwareinfo['GPU'] = hardwareinfo['GPU'].replace(/\(.*?\)/g, '').replace(/\[.*?\]/g, '').trim();
            hardwareinfo['GPU_DriverVersion'] = GPUData['DriverVersion']
            hardwareinfo['GPU_DriverDate'] = FormartMonthToNumber(GPUData['DriverDate'])
            hardwareinfo['GPU_Brand'] = gpu_brand(GPUData)
            hardwareinfo['GPU_NumberOfUnifiedShaders'] = GPUData['NumberOfUnifiedShaders']
            hardwareinfo['GPU_GraphicsMemoryBusWidth'] = GPUData['GraphicsMemoryBusWidth']
            hardwareinfo['GPU_ASICManufacturer'] = GPUData['ASICManufacturer']
            hardwareinfo['VideoMemoryNumber'] = GPUData['VideoMemoryNumber']

            if (GPUData['VideoMemory']) {
              const VideoMemory = String(GPUData['VideoMemory'])
              if (VideoMemory.includes('[') && VideoMemory.includes(']')) {
                try {
                  const MidStr = VideoMemory.match(/\[(.+?)\]/)[1];
                  hardwareinfo['VideoMemoryBrand'] = MidStr
                }catch (e) {

                }
              }
            }

            if (!hardwareinfo['VideoMemoryBrand']) hardwareinfo['VideoMemoryBrand'] = ''

            let videoCardName = GPUData.VideoCard;
            let VideoBrand = videoCardName.match(/\[(.+?)\]$/) ? RegExp.$1 : videoCardName.split(' ')[0] || '';
            hardwareinfo['VideoBrand'] = VideoBrand
            const gpu_memory_size_type = (data: any) => {
              const regex = /\[(.+?)\]/g
              const VideoMemory = (data.VideoMemory).split(' ')
              const VideoMemoryBrandArr = (data.VideoMemory.match(regex))
              let VideoMemoryBrand = ''
              if (VideoMemoryBrandArr) {
                VideoMemoryBrand = (VideoMemoryBrandArr[VideoMemoryBrandArr.length - 1]).replace(/\[|]/g, '')
              }
              let VideoType = ''
              if (VideoMemory[3]) {
                VideoType = VideoMemory[3]
              }
              let typeBrand = ''
              if (VideoType || VideoMemoryBrand) {
                typeBrand = ' (' + VideoType + ' ' + VideoMemoryBrand + ')'
              }
              let n = 1024
              if (data.VideoMemory.includes('MBytes')) {
                n = 1024
              } else if (data.VideoMemory.includes('KBytes')) {
                n = 1024*1024
              } else if (data.VideoMemory.includes('GBytes')) {
                n = 1
              }
              return Math.ceil(((VideoMemory[0] / n))) + 'GB'
            }
            if (GPUData.VideoMemory && GPUData.VideoMemory !== 'Unknown') {
                hardwareinfo['GPU_VideoMemor'] = gpu_memory_size_type(GPUData)
            }
                let gpu_name = GPUData['VideoChipset'];
                let GPUType = ''
            if (['Radeon', 'AMD', 'Vega','amd'].find(item => gpu_name.includes(item))) {GPUType = 'amd';} else if (['GeForce', 'NVIDIA'].find(item => gpu_name.includes(item))) {GPUType = 'nvidia'}
                 hardwareinfo['GPUType'] = GPUType

       break;
        case 'MEMORY':
            let MemoryHtml = '';
            hardwareinfo['memory_list_all'] = []
            if (HWInfoV['SubNode'] != null) {
              let arr:Array<string> = []
              let arr2:Array<number> = []
              let arr3 = []
              for (let i = 0; i < HWInfoV['SubNode'].length; i++) {
                const item = HWInfoV['SubNode'][i];
                let str = ''
                str += item.ModuleManufacturer;
                str += ' '
                str += ((item.MemorySpeed)?.match(/\((.+?)\)/g)[0])?.split('/')[0].replace('(', '') || ''
                // str += Math.floor(item.MemorySpeed?.split(' ')[0]) + ' MHz '
                str += item.ModuleSize?.split(' ')[0] + 'GB'
                hardwareinfo['memory_list_all'].push(str);
                if (arr.length === 0) {
                  arr.push(str)
                  arr2.push(1)
                  arr3.push({
                    'MemoryType': item['MemoryType']
                  })
                }else{
                  if (arr.includes(str)){
                    arr2[arr.indexOf(str)] = arr2[arr.indexOf(str)]+1
                  }else{
                    arr.push(str)
                    arr2.push(1)
                    arr3.push({
                      'MemoryType': item['MemoryType']
                    })
                  }
                }
              }
              hardwareinfo['memory_list1'] = arr; // 内存条名字
              hardwareinfo['memory_list2'] = arr2; // 内存条数量
              hardwareinfo['memory_list3'] = arr3; // 内存条类型
              hardwareinfo['memory'] = HWInfoV['SubNode'][0]['ModuleManufacturer'] + "  " + +(HWInfoV.Property['TotalMemorySize[MB]']) / 1024 + 'GB' + "  " + (Math.ceil(HWInfoV.Property['CurrentMemoryClock'].split('MHz')[0])) * 2 + 'MHz' + "  " + HWInfoV.Property['CurrentTiming(tCAS-tRCD-tRP-tRAS)']

                // MemoryHtml = '<div class="memory">' + HWInfoV['SubNode'][0]['ModuleManufacturer'] + "  " + +(HWInfoV.Property['TotalMemorySize[MB]']) / 1024 + 'GB' + "  " + (Math.ceil(HWInfoV.Property['CurrentMemoryClock'].split('MHz')[0])) * 2 + 'MHz' + "  " + HWInfoV.Property['CurrentTiming(tCAS-tRCD-tRP-tRAS)'] + '</div></li>';
            }
            let obj = HWInfoV['Property']
            if (!obj) obj = {}
            if (obj['TotalMemorySize[MB]']) {
              hardwareinfo['TotalMemorySize[MB]'] = obj['TotalMemorySize[MB]'];
              hardwareinfo['Memory_size']  = (obj['TotalMemorySize[MB]'] / 1024) + 'GB'
            } else {
              hardwareinfo['TotalMemorySize[MB]'] = 0
              hardwareinfo['Memory_size']  = ''
            }
            if (obj['MemoryChannelsActive']) {
              hardwareinfo['Memory_channels_active'] = obj['MemoryChannelsActive']
            } else {
              hardwareinfo['Memory_channels_active']  = ''
            }
            if (HWInfoV['SubNode'] && Array.isArray(HWInfoV['SubNode'])) {
              hardwareinfo['Memory_count'] = HWInfoV['SubNode'].length
            } else {
              hardwareinfo['Memory_count']  = 0
            }

            if (obj['CurrentMemoryClock']) {
              hardwareinfo['Memory_Clock'] = (Math.ceil(obj['CurrentMemoryClock'].split('MHz')[0])) * 2
            } else {
              hardwareinfo['Memory_Clock']  = ''
            }

            if (obj['CurrentTiming(tCAS-tRCD-tRP-tRAS)']) {
              hardwareinfo['Memory_CurrentTiming'] = obj['CurrentTiming(tCAS-tRCD-tRP-tRAS)']
            }else{
              hardwareinfo['Memory_CurrentTiming'] = ''
            }

        break;
        case 'MONITOR':
            let MONITORDataV = HWInfoV['SubNode'][0]
            let MonitorNameStr
            if (MONITORDataV.MonitorName !== 'Unknown') {
              hardwareinfo['MonitorName'] = MONITORDataV.MonitorName.replace(/\[.*?\]/g, '');
            } else {
              hardwareinfo['MonitorName'] = ''
            }
            if (MONITORDataV['MonitorName(Manuf)']) {
                let brand = RemoveAllSpace( hardwareinfo['MonitorName']);
                let model = RemoveAllSpace(MONITORDataV['MonitorName(Manuf)']);
                if (brand.toLowerCase() === model.toLowerCase()) {
                    MonitorNameStr = MONITORDataV['MonitorName'];
                } else {
                    MonitorNameStr =hardwareinfo['MonitorName']+ MONITORDataV['MonitorName(Manuf)'];
                }
            } else {
                MonitorNameStr = MONITORDataV['MonitorName'];
            }
            if (MonitorNameStr !== '') {
              hardwareinfo['MonitorNameStr'] = MonitorNameStr
              hardwareinfo['refresh_rate'] = MONITORDataV['RefreshFrequency'] + 'Hz'
              let WH = MONITORDataV.Resolutions.split('*')
              hardwareinfo['resolutiop'] = WH[0] + '*' + WH[1]

                if (MONITORDataV['Max.HorizontalSize'] && MONITORDataV['Max.VerticalSize']) {
                    let HorizontalSize = RegExGetNum(MONITORDataV['Max.HorizontalSize']);
                    let VerticalSize = RegExGetNum(MONITORDataV['Max.VerticalSize']);
                    let MonitorSize = parseFloat((Math.sqrt(Math.pow(HorizontalSize, 2) + Math.pow(VerticalSize, 2)) / 2.54).toFixed(1));
                    hardwareinfo['display_screen_size'] = MonitorSize + '英寸'
                }
            }
            break;
      }
   }
   console.warn('GetHardwareBase End');
   console.warn('hard1;:',);
   
  return hardwareinfo
}





function calculateAverage(arr:any) {
    if (!Array.isArray(arr) || arr.length === 0) {
        throw new Error('Input must be a non-empty array');
    }
    const sum = arr.reduce((sum, current) => sum + current, 0);
    return sum / arr.length;
}

const handleListData = async() =>
{
   for(const item of DataList.value)
   {
      let list_data = JSON.parse(decodeURIComponent(item.cpu_list_data));
      let hard = await PM_GetHardwareBase(item.hd_info)
      console.warn('hard::',hard);
      console.warn('list_data::',list_data);
      
      let obj = {
            starttime:item.starttime,
            endtime:item.endtime,
            bm_version:item.bm_version,
            model:list_data['model'],
            hard:{
                  cpu_name:hard['ProcessorName'],
                  gpu_name:hard['GPU'],
                  mem_name:hard['memory_list1'][0],
                  mem_channel:hard['memory_list2'][0],
                  mem_clock:hard['Memory_Clock'],
                  mem_time:hard['Memory_CurrentTiming'],
            },
            cpu:{
                  avg_clock:calculateAverage(list_data['cpu_clock']).toFixed(0),
                  max_temp:Math.max(...list_data['cpu_TEMP']),
                  max_tdp:Math.max(...list_data['cpu_TDP']),
                  limit:list_data['cpu_LIMIT'].length   
            },
            gpu:{
                  avg_clock:calculateAverage(list_data['gpu_clock']).toFixed(0),
                  max_temp:Math.max(...list_data['gpu_TEMP']),
                  max_tdp:Math.max(...list_data['gpu_TDP']),
                  limit:0       
            },
            mem:{
                  mem_error:list_data['mem_error']    
            }
      }
      realData.value.push(obj)
   }
   console.log('%crealData::','color:green',realData.value);
}

const emit = defineEmits(['child-click']);

const emitClickEvent = () => 
{
      // 触发自定义事件，并传递参数
      emit('child-click', '这是从子组件传递的消息');
};

const openDetailPage = async(timeStamp:number) =>
{
      let obj = 
      {
            StartTime:timeStamp,
            EndTime:0
      }   
      await gamepp.setting.setBool2.promise('window', 'pressureResult', false);
      gamepp.webapp.windows.show.sync('pressureResult', false);
      await IsReadyShowSendPage('window', "pressureResult", false);
      await gamepp.webapp.sendInternalAppEvent.promise('pressureResult', obj)
}

async function IsReadyShowSendPage(sectionName:any, keyName:any, value = false)
{
      return new Promise((resolve, reject) => {
      let nRet = false;
      let setInterval_getbool2 = setInterval(async () => {
            nRet = await gamepp.setting.getBool2.promise(sectionName, keyName, value);
            if (nRet) {
                  clearInterval(setInterval_getbool2);
                  resolve(1);
            }
      }, 100)
      })
}

const deletePage = async(item:any) =>
{
  realData.value = realData.value.filter((itemx:any) => {return itemx.starttime != item.starttime})
  await gamepp.database.delete.sync(DatabaseId, "list", "starttime = " + item.starttime + "");//列表数据
}
</script>

<template>
   <div class="HistoryBox">
         <div class="hisNav">
          <span class="span1"  @click="emitClickEvent" style="margin-left: 20px;color:#409EFF;">返回</span>
          <span style="margin-right: 20px;color:#999999;font-size: 12px;">仅保留最近10条记录</span>
         </div>
         <div class="hisBox scroll">
            <div class="hisItem" v-for="(item,index) in realData" v-show="realData.length>0">
                <div class="line">
                  <span class="des">开始时间</span><span class="left10">{{ FormatTimePlus(item.starttime/1000) }}</span>
                  <span class="des left10">测试时长</span><span class="left10">{{ FormatSeconds((item.endtime -item.starttime)/1000)}}</span>
                  <span class="des left10">测试版本</span><span class="left10">{{ item.bm_version}}</span>
                  <!-- <span class="des">w</span><span class="left10">{{ item.bm_version}}</span> -->
                   <div class="detial" @click="openDetailPage(item.starttime)">详情</div>
                   <div class="delete" @click="deletePage(item)">删除</div>
                </div>
                <div class="detailBox">
                  <div class="de_item">
                     <div class="info">
                        <span class="des">CPU温度测试</span>
                        <span v-show="item['model'][0].tested">{{ FormatSeconds(item['model'][0].seconds)}}</span>
                        <span v-show="!item['model'][0].tested" style="color: #F14343;">未参与测试</span>
                     </div>
                     <div class="info">
                        <span class="des">CPU稳定性测试</span>
                        <span v-show="item['model'][1].tested">{{  FormatSeconds(item['model'][1].seconds) }}</span>
                        <span v-show="!item['model'][1].tested" style="color: #F14343;">未参与测试</span>
                     </div>
                     <div class="info">
                        <span class="des">内存稳定性测试</span>
                        <span v-show="item['model'][2].tested">{{  FormatSeconds(item['model'][2].seconds) }}</span>
                        <span v-show="!item['model'][2].tested" style="color: #F14343;">未参与测试</span>
                     </div>
                     <div class="info">
                        <span class="des">CPU&GPU测试</span>
                        <span v-show="item['model'][3].tested">{{  FormatSeconds(item['model'][3].seconds) }}</span>
                        <span v-show="!item['model'][3].tested" style="color: #F14343;">未参与测试</span>
                     </div>
                  </div>
                  <!-- CPU -->
                  <div class="de_item">
                      <div class="flex left10" style="margin-top: 10px;">
                        <span class="iconfont icon-GPU"></span>
                        <span class="left10">{{ item['hard']['cpu_name'] }}</span>
                      </div>   
                      <div class="info">
                        <div class="flex"> 
                              <span class="des">平均频率：</span>
                              <span>{{ item['cpu']['avg_clock'] }}MHZ</span>
                        </div>
                        <div>
                              <span class="des">温度传感器事件：</span>
                              <span>{{ item['cpu']['limit'] }}次</span>
                        </div>
                      </div>
                      <div class="info">
                        <div class="flex"> 
                              <span class="des">最高温度：</span>
                              <span>{{ item['cpu']['max_temp'] }}°C</span>
                        </div>
                      </div>
                      <div class="info">
                        <div  class="flex"> 
                              <span class="des">最大TDP：</span>
                              <span>{{ item['cpu']['max_tdp'] }}W</span>
                        </div>
                      </div>
                  </div>
                  <!-- GPU -->
                  <div class="de_item">
                      <div  class="flex left10" style="margin-top: 10px;">
                        <span class="iconfont icon-CPU"></span>
                        <span class="left10">{{ item['hard']['gpu_name'] }}</span>
                      </div>   
                      <div class="info">
                        <div class="flex"> 
                              <span class="des">平均频率：</span>
                              <span>{{ item['gpu']['avg_clock'] }}W</span>
                        </div>
                      </div>
                      <div class="info">
                        <div class="flex"> 
                              <span class="des">最高温度：</span>
                              <span>{{ item['gpu']['max_temp'] }}°C</span>
                        </div>
                      </div>
                      <div class="info">
                        <div  class="flex"> 
                              <span class="des">最大TDP：</span>
                              <span>{{ item['gpu']['max_tdp'] }}W</span>
                        </div>
                      </div>
                  </div>
                  <!-- MEM -->
                  <div class="de_item">
                      <div  class="flex left10" style="margin-top: 10px;">
                        <span class="iconfont icon-Dram"></span>
                        <span class="left10">{{ item['hard']['mem_name'] }}</span>
                      </div>   
                      <div class="info">
                        <div class="flex"> 
                              <span class="des">通道：</span>
                              <span>{{ item['hard']['mem_channel'] }}</span>
                        </div>
                        <!-- <div>
                              <span class="des">最高温度：</span>
                              <span>50</span>
                        </div> -->
                      </div>
                      <div class="info">
                        <div class="flex"> 
                              <span class="des">频率：</span>
                              <span>{{ item['hard']['mem_clock'] }}Mhz</span>
                        </div>
                        <div  class="flex"> 
                              <span class="des">报错次数：</span>
                              <span>{{ item['mem']['mem_error'] }}</span>
                        </div>
                      </div>
                      <div class="info">
                        <div class="flex"> 
                              <span class="des">时序：</span>
                              <span>{{ item['hard']['mem_time'] }}</span>
                        </div>
                      </div>
                  </div>
                </div>
            </div>
            <div class="noneRecord" style="width: 100%;height: 100%;display: flex;align-items: center;justify-content: center;" v-show="realData.length == 0">
                 <span style="color: #666666;font-size: 50px;font-weight: bold">无测试记录</span>
            </div>
         </div>
      </div>
</template>
<style lang="scss">
.HistoryBox{
      width: 100%;
      height: 900px;
      font-size: 12px;
      .hisNav{
            margin-top:10px;
            width: 100%;
            display: flex;
            justify-content: space-between;
            .span1:hover{
             opacity: 0.8;
            }
      }
      .hisBox{
            width: 1280px;
            height: 800px;
            overflow: auto;
      }
      .hisItem{
            width:1240px;
            height: 185px;
            border-radius: 6px;
            background-color:#2B2C37;
            margin-top: 10px;
            margin-left: 20px;
            .line{
                  padding-top:10px;
                  margin-left: 20px;
                  position: relative;
                  display: flex;
            }
            .detial{
                  cursor: pointer;
                  top: 10px;
                  right: 100px;
                  position:absolute;
                  width:75px;
                  height: 30px;
                  border-radius: 4px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  background-color: transparent;
                  border: 1px solid #409EFF;
                  color: #409EFF;
            }
            .detial:hover{
                  opacity: 0.8;
            }
            .delete{
                  cursor: pointer;
                  top: 10px;
                  right: 5px;
                  position:absolute;
                  width:75px;
                  height: 30px;
                  border-radius: 4px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  background-color: transparent;
                  border: 1px solid #F14343;
                  color: #F14343;
            }
            .delete:hover{
                  opacity: 0.8;
            }
            .detailBox{
                  display: flex;
                  .de_item{
                       width: 280px;
                       height: 125px;
                       background-color: #262731;
                       border-radius: 6px; 
                       margin-left: 20px;
                       display: flex;
                       flex-direction: column;
                       margin-top: 20px;
                       .info{
                        width: 90%;
                        margin-left: 10px;
                        display: flex;
                        justify-content: space-between;
                        margin-top: 10px;
                       }
                  }
            }
      }
}
      .des{
            color: #999999;
        }
      .left20{
      margin-left: 20px;
      }
      .left10{
      margin-left:10px
      }
      .flex{
            display: flex;
      }
.scroll::-webkit-scrollbar{
      width: 5px;
      transition: 0.25s;
     }

      .scroll::-webkit-scrollbar-thumb {
        background: #71738C;
        border-radius: 3px;
      }

      .scroll::-webkit-scrollbar-thumb:hover {
        background-color: #71738C;
      }

      .scroll::-webkit-scrollbar-track {
        background-color: #2D2E39;
        width: 2px;
      }
</style>
