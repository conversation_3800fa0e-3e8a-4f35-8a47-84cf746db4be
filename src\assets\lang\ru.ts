const ru = {
  "update": {
    "Game": "Game",
    "PP": "PP",
    "upgradingInProgress": "Обновление в процессе",
    "theModuleIsBeingUpdated": "Обновление модуля",
    "dataIsBeingUpdated": "Обновление данных...",
    "checkingUpdate": "Проверка обновлений",
    "checkingUpgrade": "Проверка обновлений",
    "loadingProgramComponent": "Загрузка компонентов программы...",
    "loadingHotkeyModules": "Загрузка компонента горячих клавиш",
    "loadingGPPModules": "Загрузка компонентов GamePP",
    "loadingBlackWhiteList": "Загрузка черного и белого списков",
    "loadingGameSetting": "Загрузка параметров игровых настроек...",
    "loadingUserAbout": "Загрузка данных аутентификации пользователя",
    "loadingGameBenchmark": "Загрузка рейтинга игры",
    "loadingHardwareInfo": "Загрузка компонента информации о оборудовании",
    "loadingDBModules": "Загрузка модуля базы данных...",
    "loadingIGCModules": "Загрузка модуля IGC в процессе",
    "loadingFTPModules": "Загрузка модуля поддержки FTP в процессе",
    "loadingDialogModules": "Загрузка модуля диалогового окна",
    "loadingDataStatisticsModules": "Загрузка модуля статистики в процессе",
    "loadingSysModules": "Загрузка системных компонентов в процессе",
    "loadingGameOptimization": "Загрузка оптимизации игры",
    "loadingGameAcceleration": "Загрузка ускорения игры",
    "loadingScreenshot": "Загрузка снимка экрана записи",
    "loadingVideoComponent": "Загрузка компонента сжатия видео",
    "loadingFileFix": "Загрузка восстановления файла",
    "loadingGameAI": "Загрузка качества ИИ игры",
    "loadingNVAPIModules": "Загрузка модуля NVAPI",
    "loadingAMDADLModules": "Загрузка модуля AMDADL в процессе",
    "loadingModules": "Загрузка модуля"
  },
  "messages": {
    "append": "Добавить",
    "confirm": "Подтвердить",
    "cancel": "Отменить",
    "default": "По умолчанию",
    "quickSelect": "Быстрый выбор",
    "onoffingame": "Включить/Выключить внутриигровой мониторинг:",
    "changeKey": "Нажмите, чтобы изменить сочетание клавиш",
    "clear": "Очистить",
    "hotkeyOccupied": "Горячая клавиша уже используется, пожалуйста, задайте другую!",
    "minimize": "Свернуть",
    "exit": "Выход",
    "export": "Экспорт",
    "import": "Импорт",
    "screenshot": "Скриншот",
    "showHideWindow": "Показать/Скрыть окно",
    "ingameControlPanel": "Панель управления внутри игры",
    "openOrCloseGameInSettings": "Включить/выключить панель настройки в игре",
    "openOrCloseGameInSettings2": "Нажмите эту комбинацию клавиш, чтобы активировать",
    "openOrCloseGameInSettings3": "Включить/Выключить мониторинг в игре",
    "openOrCloseGameInSettings4": "Включить/выключить игровой фильтр",
    "startManualRecord": "Начать/Остановить ручную запись статистики",
    "performanceStatisticsMark": "Маркер статистики производительности",
    "EnableAIfilter": "Для активации AI-фильтра необходимо нажать эту комбинацию клавиш",
    "Start_stop": "Начать/Остановить ручную запись статистики",
    "pressureTest": "Тест на стресс",
    "moduleNotInstalled": "Функциональный модуль не установлен",
    "installingPressureTest": "Установка модуля тестирования на нагрузку...",
    "importFailed": "Ошибка импорта",
    "gamepp": "ГеймПП",
    "copyToClipboard": "Скопировано в буфер обмена"
  },
  "home": {
    "homeTitle": "Главная",
    "hardwareInfo": "Сведения о оборудовании",
    "functionIntroduction": "Функции",
    "fixedToNav": "Закрепить на панели навигации",
    "cancelFixedToNav": "Открепить от панели навигации",
    "hardwareInfoLoading": "Загрузка информации о оборудовании...",
    "performanceStatistics": "Статистика производительности",
    "updateNow": "Обновить сейчас",
    "recentRun": "Недавняя активность",
    "resolution": "Разрешение:",
    "duration": "Продолжительность:",
    "gameFilter": "Игровой фильтр",
    "gameFilterHasAccompany": "Фильтр игры теперь активен",
    "gameFilterHasAccompany2": "Пользователи играют в такие игры, как Cyberpunk, APEX и Hogwarts Legacy",
    "currentList": "Элементы мониторинга в текущем списке",
    "moreFunction": "Тестирование производительности, стресс-тесты, мониторинг рабочего стола и другие функции находятся в разработке.",
    "newVersion": "Доступно новое обновление !",
    "discoverUpdate": "Обновление найдено!",
    "downloading": "Загружается",
    "retry": "Повторить попытку",
    "erhaAI": "2ХаАй",
    "recordingmodule": "Эта функция зависит от модуля записи",
    "superPower": "Супер-режим",
    "autoRecord": "Автоматически записывайте моменты убийств в игре и легко сохраняйте лучшие моменты",
    "externalDevice": "Динамическая подсветка периферийных устройств",
    "linkage": "Активировать сцены убийств в играх и отображать их через подключенные периферийные устройства",
    "AI": "Тест производительности ИИ",
    "test": "Тестируйте модели ИИ с GPU и просмотрите оценку производительности ИИ",
    "supportedGames": "Поддерживаемые игры",
    "games": "Naraka: Bladepoint, Delta Force, FragPunk, League of Legends, PUBG, Valorant, The Finals, APEX Legends, COD20",
    "videoRecording": "Запись видео",
    "videoRecording2": "Видеозаписывающая функция на основе OBS, поддерживает регулировку битрейта видео и частоты кадров (FPS) для удовлетворения различных требований к качеству и плавности; также поддерживает «Instant Replay», нажмите сочетание клавиш для сохранения моментов в любое время!",
    "addOne": "Получить бесплатно",
    "gamePlatform": "Игровая платформа",
    "goShop": "Перейти на страницу магазина",
    "receiveDeadline": "Срок Заявки После Действия",
    "2Ai": "2 Смех AI",
    "questionDesc": "Описание проблемы",
    "inputYourQuestion": "Пожалуйста, введите здесь свои предложения или комментарии。",
    "uploadLimit": "Загрузите до 3 локальных изображений в формате JPG/PNG/BMP",
    "email": "Электронная почта",
    "contactWay": "Контактная информация",
    "qqNumber": "Номер QQ (опционально)",
    "submit": "Отправить"
  },
  "hardwareInfo": {
    "hardwareOverview": "Обзор оборудования",
    "copyAllHardwareInfo": "Копировать всю информацию о железе",
    "processor": "Процессор",
    "coreCount": "Ядра:",
    "threadCount": "Количество Потоков:",
    "currentFrequency": "Текущая частота:",
    "currentVoltage": "Текущее напряжение:",
    "copy": "Копировать",
    "releaseDate": "Дата выхода",
    "codeName": "Кодовое имя",
    "thermalDesignPower": "Тепловой Расчет Мощности",
    "maxTemperature": "Максимальная температура",
    "graphicsCard": "Графическая карта",
    "brand": "Бренд:",
    "streamProcessors": "Потоковый процессор:",
    "Videomemory": "Видеопамять:",
    "busSpeed": "Скорость Шины",
    "driverInfo": "Информация драйвера",
    "driverInstallDate": "Дата установки драйвера",
    "hardwareID": "Идентификатор оборудования",
    "motherboard": "Материнская плата",
    "chipGroup": "Чипсет:",
    "BIOSDate": "Дата BIOS",
    "BIOSVersion": "Версия BIOS",
    "PCIESlots": "PCIe слот",
    "PCIEVersion": "Поддерживаемая версия PCIe",
    "memory": "Память",
    "memoryBarCount": "Количество:",
    "totalSize": "Размер:",
    "channelCount": "Канал:",
    "Specificmodel": "Конкретная модель",
    "Pellet": "Производитель частиц",
    "memoryBarEquivalentFrequency": "Эффективная частота памяти:",
    "hardDisk": "Жесткий диск",
    "hardDiskCount": "Количество жестких дисков:",
    "actualCapacity": "Фактическая ёмкость",
    "type": "Тип",
    "powerOnTime": "Время включения",
    "powerOnCount": "Циклы подачи питания",
    "SSDRemainingLife": "Оставшийся срок службы SSD",
    "partitionInfo": "Информация о разделе",
    "hardDiskController": "Контроллер Жесткого Диска",
    "driverNumber": "Номер серии диска",
    "display": "Монитор",
    "refreshRate": "Частота обновления:",
    "screenSize": "Размер экрана:",
    "inches": "дюймы",
    "productionDate": "Дата производства",
    "supportRefreshRate": "Поддержка частоты обновления",
    "screenLongAndShort": "Размер экрана",
    "systemInfo": "Системная информация",
    "version": "Версия",
    "systemInstallDate": "Дата установки системы",
    "systemBootTime": "Время Последнего Запуска",
    "systemRunTime": "Время выполнения",
    "Poccupied": "Использование P",
    "Eoccupied": "E занято",
    "occupied": "Занят",
    "temperature": "Температура",
    "Pfrequency": "Частота P",
    "Efrequency": "Частота E",
    "thermalPower": "Тепловая Мощность",
    "frequency": "Частота",
    "current": "Текущий",
    "noData": "Нет данных",
    "loadHwinfo_SDK": "Не удалось загрузить Hwinfo_SDK.dll, невозможно считать данные с датчиков и оборудования",
    "loadHwinfo_SDK_reason": "Возможные причины этой проблемы:",
    "reason": "Причина",
    "BlockIntercept": "Заблокировано антивирусным программным обеспечением, например: 2345 Антивирусное ПО (Процесс активной защиты 2345, Процесс активной защиты McAfee)",
    "solution": "Решение:",
    "solution1": "После закрытия и удаления связанных процессов перезапустите GamePP",
    "solution2": "Отключите связанные устройства, затем перезапустите GamePP",
    "RestartGamePP": "Отключите контроллер, дождитесь ответа устройства, затем перезапустите GamePP",
    "HWINFOcannotrun": "Hwinfo не может работать должным образом",
    "downloadHWINFO": "Загрузить Hwinfo",
    "openHWINFO": "После запуска Hwinfo, можно ли нажать RUN для нормального открытия?",
    "hardwareDriverProblem": "Проблемы с драйверами оборудования",
    "checkHardwareManager": "Проверьте диспетчер оборудования, чтобы убедиться, что драйверы материнской платы и видеокарты установлены правильно",
    "systemProblem": "Системные проблемы, например: использование активационных инструментов (Baofeng, Xiaoma и т.д.) может привести к сбоям загрузки драйверов и невозможности автоматической установки пакетов обновления Windows 7",
    "reinstallSystem": "Повторно установите систему для активации, Скачайте и установите WIN7: Обновление *********",
    "Windows7": "Windows 7: Установите патч SHA-256, Windows 10: Активируйте с помощью цифрового сертификата, Windows 11 Предварительная версия: Отключите целостность памяти",
    "ViolenceActivator": "Если вы использовали недозволенные инструменты активации, такие как Xiaoma, пожалуйста, отремонтируйте или переустановите систему",
    "MultipleGraphicsCardDrivers": "На компьютере установлены драйверы видеокарт различных брендов, например одновременно установлены драйверы AMD и Nvidia",
    "UninstallUnused": "Перезагрузите компьютер после удаления ненужных драйверов видеокарты",
    "OfficialQgroup": "Ни одна из вышеперечисленных причин не подходит. Присоединитесь к официальной группе QQ: 908287288 (Группа 5) для решения проблемы.",
    "ExportHardwareData": "Экспорт аппаратных данных",
    "D3D": "Использование D3D",
    "Total": "Общее использование",
    "VRAM": "Использование видеопамяти",
    "VRAMFrequency": "Частота видеопамяти",
    "SensorData": "Данные датчика",
    "CannotGetSensorData": "Не удалось получить данные датчика",
    "LoadingHardwareInfo": "Загрузка аппаратной информации...",
    "ScanTime": "Последний скан:",
    "Rescan": "Пересканировать",
    "Screenshot": "Скриншот",
    "configCopyed": "Конфигурационная информация была скопирована в буфер обмена.",
    "LegalRisks": "Обнаружены потенциальные юридические риски",
    "brandLegalRisks": "Отображение бренда может содержать потенциальные юридические риски",
    "professionalVersion": "Профессиональная версия",
    "professionalWorkstationVersion": "Профессиональная версия для рабочей станции",
    "familyEdition": "Домашняя версия",
    "educationEdition": "Образовательная версия",
    "enterpriseEdition": "Бизнес-версия",
    "flagshipEdition": "Премиум-версия",
    "familyPremiumEdition": "Домашняя премиум-версия",
    "familyStandardEdition": "Стандартная версия для семьи",
    "primaryVersion": "Базовая версия",
    "bit": "бит",
    "tempWall": "Температурная стена",
    "error": "Ошибка",
    "screenshotSuccess": "Скриншот успешно сохранен",
    "atLeastOneData": "Сохраните хотя бы 1 запись данных",
    "atMostSixData": "Добавьте максимум 6 записей данных",
    "screenNotActivated": "Не активирован"
  },
  "psc": {
    "processCoreAssign": "Распределение процессов по ядрам",
    "CoreAssign": "Распределение ядер:",
    "groupName": "Имя группы:",
    "notGameProcess": "Негейм-процессы",
    "unNamedProcess": "Неименованная группа",
    "Group2": "Группа",
    "selectTheCore": "Выберите ядро",
    "controls": "Операция",
    "tips": "Уведомление",
    "search": "Поиск",
    "shiftOut": "Извлечь",
    "ppValue": "Значение PP",
    "ppDesc": "Значение PP отражает историческое потребление аппаратных ресурсов. Чем больше это значение, тем выше нагрузка на аппаратные ресурсы.",
    "littletips": "Совет: Нажмите и удерживайте процесс, чтобы перетащить его в левую группу",
    "warning1": "Выбор потоков ядра между группами может повлиять на производительность. Рекомендуется использовать ядра из одной группы.",
    "warning2": "Вы уверены, что хотите оставить это имя группы пустым?",
    "warning3": "После удаления распределение ядра станет недействительным. Вы уверены, что хотите удалить группу?",
    "allprocess": "Все процессы",
    "pleaseCheckProcess": "Пожалуйста, активируйте процесс",
    "dataSaveDesktop": "Данные были сохранены на рабочий стол.",
    "createAGroup": "Создать группу",
    "delGroup": "Удалить группу",
    "Group": "Группа",
    "editGroup": "Изменить группу",
    "groupinfo": "Информация группы",
    "moveOutGrouping": "Удалить из группы",
    "createANewGroup": "Создать новую группу",
    "unallocatedCore": "Неназначенное ядро",
    "inactiveProcess": "Неактивный процесс",
    "importGroupingScheme": "Импорт профиля группировки",
    "derivedPacketScheme": "Экспорт групповой конфигурации",
    "addNowProcess": "Добавить текущий запущенный процесс",
    "displaySystemProcess": "Показать системные процессы",
    "max64": "Максимум можно выбрать 64 потока",
    "processName": "Имя процесса",
    "chooseCurProcess": "Выбрать текущий процесс",
    "selectNoProcess": "Не выбрано ни одного процесса",
    "coreCount": "Ядра",
    "threadCount": "Потоки",
    "process": "Процесс",
    "plzInputProcessName": "Введите имя процесса, чтобы вручную добавить",
    "has_allocation": "Процессы со схемами распределения потоков",
    "not_made": "Вы не назначили ядра ни для одного процесса",
    "startUse": "Включить оптимизацию",
    "stopUse": "Отключить оптимизацию",
    "threadAllocation": "Распределение Потоков",
    "configProcess": "Конфигурация процесса",
    "selectThread": "Выберите поток",
    "hyperthreadingState": "Состояние Hyper-Threading",
    "open": "Включено",
    "notYetUnlocked": "Отключено",
    "nonhyperthreading": "Без Hyper-Threading",
    "intervalSelection": "Выбор интервала",
    "invertSelection": "Инвертировать выбор",
    "description": "Блокировка игровых процессов на указанных ядрах процессора, интеллектуальное изолирование фоновых программ. Эффективно повышает предел FPS и стабилизирует частоту кадров. Уменьшает внезапные лаги и резкие падения производительности, полностью раскрывая потенциал многоядерных процессоров для гарантирования стабильно высокой частоты кадров на протяжении всей игры.",
    "importSuccess": "Импорт успешно выполнен",
    "importFailed": "Ошибка импорта"
  },
  "InGameMonitor": {
    "onoffingame": "Включить/Выключить Внутриигровой Мониторинг:",
    "InGameMonitor": "Мониторинг в игре",
    "CustomMode": "Пользовательский Режим",
    "Developing": "В разработке...",
    "NewMonitor": "Добавить элемент мониторинга",
    "Data": "Параметры",
    "Des": "Заметка",
    "Function": "Функциональность",
    "Editor": "Редактировать",
    "Top": "Закрепить наверху",
    "Delete": "Удалить",
    "Use": "Использовать",
    "DragToSet": "После вызова панели вы можете перетаскивать для настройки",
    "MonitorItem": "Элемент мониторинга",
    "addMonitorItem": "Добавить элемент мониторинга",
    "hide": "Скрыть",
    "show": "Отображение",
    "generalstyle": "Общие настройки",
    "restoredefault": "Сбросить настройки по умолчанию",
    "arrangement": "Макет",
    "horizontal": "горизонтальный",
    "vertical": "Вертикальный",
    "monitorposition": "Позиция Мониторинга",
    "canquickselectposition": "Быстро выберите расположение на левой карте",
    "curposition": "Текущее Расположение:",
    "background": "Фон",
    "backgroundcolor": "Цвет Фона:",
    "font": "Шрифт",
    "fontStyle": "Стиль шрифта",
    "fontsize": "Размер шрифта:",
    "fontcolor": "Цвет Шрифта:",
    "style": "Стиль:",
    "style2": "Стиль",
    "performance": "Производительность",
    "refreshTime": "Время обновления:",
    "goGeneralSetting": "Перейти к общим настройкам",
    "selectMonitorItem": "Выберите элемент мониторинга",
    "selectedSensor": "Выбранный датчик:",
    "showTitle": "Отображать заголовок",
    "hideTitle": "Скрыть заголовок",
    "showStyle": "Режим отображения:",
    "remarkSize": "Размер примечания:",
    "remarkColor": "Цвет Примечания:",
    "parameterSize": "Размер параметра:",
    "parameterColor": "Цвет параметров:",
    "lineChart": "Линейный график",
    "lineColor": "Цвет ломаной линии:",
    "lineThickness": "Толщина линии:",
    "areaHeight": "Высота Региона:",
    "sort": "Сортировать",
    "displacement": "Смещение:",
    "up": "Переместить вверх",
    "down": "Переместить вниз",
    "bold": "Жирный",
    "stroke": "Контур",
    "text": "Текст",
    "textLine": "Текст + Линейный График",
    "custom": "Пользовательский",
    "upperLeft": "Верхний левый",
    "upper": "Средний верхний",
    "upperRight": "Верхний правый",
    "Left": "Левый центр",
    "middle": "Центр",
    "Right": "Правый центр",
    "lowerLeft": "Внизу слева",
    "lower": "Средний Низкий",
    "lowerRight": "Внизу справа",
    "notSupport": "Периферийное устройство не поддерживает отображение или скрытие щелчком мыши",
    "notSupportRate": "Норма прибыли не поддерживает переключение при клике",
    "notFindSensor": "Датчик не найден. Нажмите, чтобы изменить.",
    "monitoring": "Мониторинг",
    "condition": "Условие",
    "bigger": "Больше",
    "smaller": "Меньше чем",
    "biggerThan": "Превышает порог",
    "biggerThanthreshold": "Больше порогового процента",
    "smallerThan": "Меньше порога",
    "smallerThanthreshold": "Меньше порогового процента",
    "biggerPercent": "Процент снижения текущего значения",
    "smallerPercent": "Процент увеличения текущего значения",
    "replay": "Функция мгновенного воспроизведения",
    "screenshot": "Функция Скриншота",
    "text1": "Значение датчика при",
    "text2": ", а также в",
    "text3": "Время ожидания",
    "text4": "Если за указанное количество секунд не появится более высокое значение, немедленно активируется",
    "text5": "После каждого срабатывания повтора обновляйте пороговое значение до значения в момент активации, чтобы уменьшить частые срабатывания",
    "text6": "Отображать текущее пороговое значение для запуска реплея",
    "text7": "Отображение значений датчика",
    "text8": "Превышение начального порога",
    "text9": "Ниже начального порога",
    "text10": "Начальное количество порога",
    "initThreshold": "Начальный порог",
    "curThreshold": "Текущий порог:",
    "curThreshold2": "Текущий порог",
    "resetCurThreshold": "Сбросить текущий порог",
    "action": "Активировать функцию",
    "times": "раз",
    "percentage": "Процент",
    "uninstallobs": "Модуль записи не загружен",
    "install": "Скачать",
    "performanceAndAudioMode": "Режим совместимости производительности и аудио",
    "isSaving": "Сохранение в процессе",
    "video_replay": "Мгновенное воспроизведение",
    "saved": "Сохранено",
    "loadQualitysScheme": "Загрузить графический профиль",
    "notSet": "Не настроен",
    "mirrorEnable": "Фильтр включен",
    "canBeTurnedOff": "Вернуться",
    "mirrorClosed": "Игровой фильтр отключен",
    "closed": "Закрыто",
    "openMirror": "Включить фильтр",
    "wonderfulScenes": "Выдающиеся моменты",
    "VulkanModeHaveProblem": "Режим Vulkan имеет проблемы совместимости",
    "suggestDxMode": "Рекомендуется переключиться в режим Dx",
    "functionNotSupported": "Функция не поддерживается",
    "NotSupported": "Не поддерживается",
    "gppManualRecording": "GamePP, ручная запись",
    "perfRecordsHaveBeenSaved": "Данные о производительности сохранены",
    "redoClickF8": "Для продолжения записи нажмите клавишу F8 еще раз.",
    "startIngameMonitor": "Включение функции мониторинга внутри игры",
    "inGameMarkSuccess": "Маркировка в игре выполнена успешно",
    "recordingFailed": "Запись не удалась",
    "recordingHasNotDownload": "Функция записи видео не загружена",
    "hotkeyDetected": "Обнаружено конфликт горячих клавиш функций",
    "plzEditIt": "Пожалуйста, внесите изменения в программу, а затем используйте её",
    "onePercentLowFrame": "1% Низкий кадр",
    "pointOnePercentLowFrame": "0.1% Низкая частота кадров",
    "frameGenerationTime": "Время генерации кадра",
    "curTime": "Текущее время",
    "runTime": "Продолжительность выполнения",
    "cpuTemp": "Температура CPU",
    "cpuUsage": "Использование ЦП",
    "cpuFreq": "Частота CPU",
    "cpuPower": "Тепловая мощность ЦП",
    "gpuTemp": "Температура GPU",
    "gpuUsage": "Загрузка GPU",
    "gpuPower": "Тепловая мощность GPU",
    "gpuFreq": "Частота GPU",
    "memUsage": "Использование памяти"
  },
  "LoginArea": {
    "login": "Войти",
    "loginOut": "Выйти из аккаунта",
    "vipExpire": "Истёкший",
    "remaining": "Оставшийся",
    "day": "Небо",
    "openVip": "Активировать членство",
    "vipPrivileges": "Привилегии участника",
    "rechargeRenewal": "Пополнить и продлить",
    "Exclusivefilter": "Фильтр",
    "configCloudSync": "Настройка облачной синхронизации",
    "comingSoon": "Скоро будет доступно"
  },
  "GameMirror": {
    "filterStatus": "Статус фильтра",
    "filterPlan": "Предустановленный фильтр",
    "filterShortcut": "Сочетания клавиш для фильтра",
    "openCloseFilter": "Включить/выключить фильтр:",
    "effectDemo": "Демонстрация Эффектов",
    "demoConfig": "Конфигурация Демонстрации",
    "AiFilter": "Эффекты ИИ-фильтра зависят от эффектов в игре",
    "AiFilterFAQ": "Распространенные проблемы с AI-фильтрами",
    "gamePPAiFilter": "AI-фильтр GamePP",
    "gamePPAiFilterVip": "VIP-эксклюзивный фильтр ИИ GamePP, динамически корректирует параметры фильтра в зависимости от игровых сценариев для оптимизации визуальных эффектов и улучшения игрового опыта.",
    "AiMingliangTips": "AI-яркость: Рекомендуется использовать при слишком темном игровом экране.",
    "AiBrightTips": "AI Яркость: Рекомендуется использовать при слишком тусклом игровом экране.",
    "AiDarkTips": "AI Затемнение: Рекомендуется использовать при излишне насыщенных игровых графиках.",
    "AiBalanceTips": "Равновесие ИИ: подходит для большинства игровых сценариев",
    "AiTips": "Советы: AI-фильтр необходимо использовать, нажав комбинацию клавиш в игре",
    "AiFilterUse": "Пожалуйста, используйте в игре",
    "AiFilterAdjust": "Настройка фильтра AI с помощью горячих клавиш",
    "Bright": "Яркий",
    "Soft": "мягкий",
    "Highlight": "Выделить",
    "Film": "Фильм",
    "Benq": "Бенкью",
    "AntiGlare": "Антибликовый",
    "HighSaturation": "Высокая насыщенность",
    "Brightness": "Яркий",
    "Day": "День",
    "Night": "Ночь",
    "Nature": "Естественный",
    "smooth": "Детализированный",
    "elegant": "Сдержанная",
    "warm": "Теплый оттенок",
    "clear": "Ясный",
    "sharp": "Резкость",
    "vivid": "Динамичный",
    "beauty": "Vivid",
    "highDefinition": "Высокое разрешение",
    "AiMingliang": "AI Яркий",
    "AiBright": "ИИ Яркий",
    "AiDark": "AI Приглушенный",
    "AiBalance": "Баланс ИИ",
    "BrightTips": "Яркий фильтр подходит для休闲ных, экшн или приключенческих игр, усиливая насыщенность цветов для более живого и привлекательного игрового изображения.",
    "liangTips": "Фильтры рекомендуется использовать когда画面 игры слишком темные.",
    "anTips": "Фильтр рекомендуется использовать, когда экран игры слишком темный.",
    "jianyiTips": "Фильтр рекомендуется использовать, когда игровая графика слишком яркая.",
    "shiTips": "Фильтр подходит для большинства игровых сцен.",
    "shi2Tips": "Фильтр подходит для игр в жанре casual, action или adventure, усиливает насыщенность цветов, делая игровую графику более яркой и привлекательной.",
    "ruiTips": "Плавные цвета фильтра, мягкие световые эффекты, подходящие для отображения фантастических, уютных или ностальгических сцен",
    "qingTips": "Яркие тона, высокий контраст, четкие детали, подходит для отображения живых и хорошо освещенных сцен.",
    "xianTips": "Высокие параметры контрастности и яркости обеспечивают четкую передачу деталей в темных сценах без искажений и комфортный просмотр в ярких сценах.",
    "dianTips": "Умеренно повысьте яркость экрана и насыщенность цветов для достижения кинематографического качества изображения",
    "benTips": "Уменьшает эффект белого света, предотвращая ослепление в сценах игры с чисто белым цветом",
    "fangTips": "Оптимизировано для игр с открытым миром и приключенческих игр, улучшает яркость и контрастность для более четкого изображения",
    "jiaoTips": "Подходит для ролевых и симуляционных игр, сбалансированные тона, улучшенная визуальная достоверность",
    "jieTips": "Оптимизировано для игр с насыщенным сюжетом и тонкими эмоциями, усиление деталей и мягкости для более качественной графики",
    "jingTips": "Оптимизировано для экшн- и спортивных игр, повышает четкость и контрастность изображения",
    "xiuTips": "Оптимизировано для терапевтических и casual-игр, усиливает теплые тона и мягкость, создавая более уютную атмосферу",
    "qihuanTips": "Подходит для сцен с богатыми фэнтезийными элементами и яркими цветами, усиливает насыщенность цветов для создания сильного визуального эффекта",
    "shengTips": "Усиление цветов и деталей для выделения живости и реалистичности сцены,",
    "sheTips": "Оптимизировано для FPS, головоломок и приключенческих игр, усиливает детали и контраст, повышая реализм игрового мира.",
    "she2Tips": "Подходит для шутеров, гонок или файтингов, выделяет детали высокого разрешения и динамическую прорисовку для усиления ощущения напряженности и визуального восприятия игры",
    "an2Tips": "Улучшает четкость сцен в темных условиях, подходит для темных или ночных сценариев.",
    "wenTips": "Подходит для художественных, приключенческих или расслабляющих игр, создает мягкие цветовые оттенки и эффекты освещения и теней, добавляя элегантности и тепла сцене。",
    "jing2Tips": "Подходит для конкурентных игр, игр с музыкальным ритмом или ночных городских сценариев, выделяя яркие цвета и световые эффекты,",
    "jing3Tips": "Подходит для игр в жанрах экшн, соревнований или фэнтези, усиливает цветовой контраст, делая изображение более насыщенным и живым.",
    "onlyVipCanUse": "Этот фильтр доступен только для VIP-пользователей"
  },
  "GameRebound": {
    "noGame": "Нет записей игр",
    "noGameRecord": "Нет записей об игровой активности. Начните новую игру прямо сейчас!",
    "gameDuration": "Продолжительность игры сегодня:",
    "gameElectricity": "Ежедневное потребление энергии",
    "degree": "Градус",
    "gameCo2": "CO₂ выбросы за день:",
    "gram": "Ключ",
    "manualRecord": "Ручная запись",
    "recordDuration": "Продолжительность записи:",
    "details": "Сведения",
    "average": "Среднее значение",
    "minimum": "Минимум",
    "maximum": "Максимум",
    "occupancyRate": "Использование",
    "voltage": "Напряжение",
    "powerConsumption": "Потребляемая мощность",
    "start": "Запуск:",
    "end": "Завершить",
    "Gametime": "Продолжительность игры:",
    "Compactdata": "Оптимизация данных",
    "FullData": "Полные данные",
    "PerformanceAnalysis": "Анализ производительности",
    "PerformanceAnalysis2": "Отчет о событии",
    "HardwareStatus": "Состояние оборудования",
    "totalPower": "Общий расход энергии",
    "TotalEmissions": "Общее излучение",
    "PSS": "Примечание: данные графика ниже представляют средние значения",
    "FrameGenerationTime": "Время генерации кадров",
    "GameResolution": "Разрешение игры",
    "FrameGenerationTimeTips": "Это значение аномально высокое и не было включено в статистику",
    "FrameGenerationTimeTips2": "Эта точка данных аномально низкая и, следовательно, не включена в статистику",
    "noData": "Нет",
    "ProcessorOccupancy": "Загрузка ЦП",
    "ProcessorFrequency": "Частота процессора",
    "ProcessorTemperature": "Температура процессора",
    "ProcessorHeatPower": "Тепловая мощность процессора",
    "GraphicsCardOccupancy": "Использование D3D на видеокарте",
    "GraphicsCardOccupancyTotal": "Общее использование видеокарты",
    "GraphicsCardFrequency": "Частота GPU",
    "GraphicsCardTemperature": "Температура видеокарты",
    "GraphicsCardCoreTemperature": "Температура горячей точки графического процессора",
    "GraphicsCardHeatPower": "Тепловая мощность GPU",
    "GraphicsCardMemoryTemperature": "Температура видеопамяти",
    "MemoryOccupancy": "Использование памяти",
    "MemoryTemperature": "Температура памяти",
    "MemoryPageFaults": "Прерывание пейджинга памяти",
    "Duration": "Продолжительность",
    "Time": "Время",
    "StartStatistics": "Начать статистику",
    "Mark": "Метка",
    "EndStatistics": "Завершить статистику",
    "LineChart": "Линейный График",
    "AddPointInGame_m1": "Нажмите в игре",
    "AddPointInGame_m2": "Можно добавить маркировочную точку",
    "LeftMouse": "Левый клик для переключения отображения/скрытия, правый клик для изменения цвета",
    "DeleteThisLine": "Удалить эту полилинию",
    "AddCurve": "Добавить кривую",
    "AllCurvesAreHidden": "Все кривые графики скрыты",
    "ThereAreSamplingData": "Общие данные выборки:",
    "Items": "Запись",
    "StatisticsData": "Статистика",
    "electricity": "Электропотребление",
    "carbonEmission": "Углеродные Выбросы",
    "carbonEmissionTips": "Выбросы диоксида углерода (кг) = Потребление электроэнергии (кВт·ч) × 0.785",
    "D3D": "Использование D3D:",
    "TOTAL": "Общее использование:",
    "Process": "Процесс:",
    "L3Cache": "Кэш L3:",
    "OriginalFrequency": "Исходная частота:",
    "MaximumBoostFrequency": "Максимальный Turbo Boost:",
    "DriverVersion": "Версия драйвера:",
    "GraphicsCardMemoryBrand": "Бренд видеопамяти:",
    "Bitwidth": "Разрядность шины",
    "System": "Система:",
    "Screen": "Экран",
    "Interface": "Интерфейс:",
    "Channel": "Канал:",
    "Timing": "Последовательность:",
    "Capacity": "Емкость:",
    "Generation": "Алгебра",
    "AddPoint_m1": "Нажмите в игре",
    "AddPoint_m2": "Добавить контрольную точку",
    "Hidden": "Скрыто",
    "Totalsampling": "Общие Данные Выборки:",
    "edition": "Версия драйвера:",
    "MainHardDisk": "Основной Жесткий Диск",
    "SetAsStartTime": "Установить как время начала",
    "SetAsEndTime": "Установить как время завершения",
    "WindowWillBe": "Окно статистики производительности будет находиться в",
    "After": "После закрытия",
    "NoLongerPopUpThisGame": "Эта игра больше не будет отображаться",
    "HideTemperatureReason": "Скрыть причину температуры",
    "HideTemperatureReason2": "Скрыть отчет о событии",
    "HideOtherReason": "Скрыть Другие Причины",
    "CPUanalysis": "Анализ производительности ЦП",
    "TemperatureCause": "Причина температуры",
    "tempSensorEvent": "Событие датчика температуры",
    "NoTemperatureLimitation": "Не обнаружено термического ограничения производительности CPU. Ваша система охлаждения полностью соответствует требованиям этого игрового приложения.",
    "NoTemperatureLimitation2": "Событий с датчиками температуры нет, ваша система охлаждения идеально справится с требованиями этой игры.",
    "performanceis": "В выбранном",
    "Inside": "Внутренний,",
    "TheStatisticsTimeOf": "Статистический период соответствует соответствующим условиям срабатывания. Причина с самой высокой частотой срабатывания —",
    "limited": "Доля общего времени, вызванная ограничениями производительности из-за температуры",
    "SpecificReasons": "Конкретные причины и их доля в температурных причинах:",
    "OptimizationSuggestion": "Рекомендации по оптимизации:",
    "CPUtemperature": "Температура процессора перегревается. Пожалуйста, проверьте/улучшите условия охлаждения процессора.",
    "CPUoverheat": "Перегрев процессора из-за питания материнской платы. Проверьте настройки материнской платы или улучшите условия охлаждения.",
    "OtherReasons": "Другие причины",
    "NoPowerSupplyLimitation": "Производительность CPU не ограничена из-за энергоснабжения/потребления. Ваши настройки энергопотребления BIOS полностью соответствуют требованиям этой игры.",
    "PowerSupplyLimitation": "Из-за ограничений питания/энергопотребления",
    "SpecificReasonsInOtherReasons": "Конкретная причина и ее доля среди других причин:",
    "PleaseCheckTheMainboard": "Проверьте состояние питания материнской платы или настройте параметры энергопотребления BIOS для устранения ограничений производительности процессора, вызванных другими факторами",
    "CPUcoretemperature": "Температура ядра достигло Tj,Max и ограничено",
    "CPUCriticalTemperature": "Температура процессора достигла критического уровня",
    "CPUCircuitTemperature": "Упаковка CPU/кольцевая шина ограничена достижением Tj,Max",
    "CPUCircuitCriticalTemperature": "Температура CPU Package/Ring Bus достигла критического уровня",
    "CPUtemperatureoverheating": "Обнаружено перегревание процессора, которое вызовет автоматическое снижение частоты для понижения температуры и предотвращения аппаратных сбоев",
    "CPUoverheatingtriggered": "При перегреве активируется механизм охлаждения, процессор отрегулирует напряжение и частоту для снижения энергопотребления и температуры",
    "CPUPowerSupplyOverheating": "Ограничение ЦП из-за серьезного перегрева питания материнской платы",
    "CPUPowerSupplyLimitation": "CPU ограничен из-за перегрева питания материнской платы",
    "CPUMaximumPowerLimitation": "Ядро ограничено максимальным энергопотреблением",
    "CPUCircuitPowerLimitation": "Корпус процессора/кольцевая шина достигла предела мощности",
    "CPUElectricalDesignLimitation": "Активировать ограничения электрического дизайна (включая стену тока ICCmax, стену пиковой мощности PL4, ограничение напряжения SVID и др.)",
    "CPULongTermPowerLimitation": "Долгосрочное энергопотребление ЦП достигло предела",
    "CPULongTermPowerinstantaneous": "Мгновенное энергопотребление ЦП достигло предела",
    "CPUPowerLimitation": "Механизм снижения частоты Turbo CPU, обычно ограничивается BIOS или специализированным ПО",
    "CPUPowerWallLimitation": "Ограничение мощности процессора",
    "CPUcurrentwalllimit": "Ограничение токовой стены процессора",
    "AiAgent": "Агент GamePP (AI Agent)",
    "AgentDesc": "Вернуться на главную страницу",
    "fnBeta": "Эта функция находится на этапе тестирования по приглашению. Ваш аккаунт GamePP пока не получил доступ к тестированию.",
    "getAIReport": "Получить отчет ИИ",
    "waitingAi": "Ожидание завершения генерации отчета",
    "no15mins": "Продолжительность игры менее 15 минут, отчет ИИ недоступен",
    "timeout": "Запрос к серверу истек",
    "agentId": "ID агента:",
    "reDo": "Пересоздать отчет",
    "text2": "Специальный агент GamePP: Онлайн-отчет анализа ИИ, следующее содержимое сгенерировано ИИ и предназначено только для ознакомления.",
    "amdAiagentTitle": "GamePP Агент: Анализ AMD Ryzen AI, нижеследующее содержание сгенерировано ИИ и предназначено только для ознакомления.",
    "noCurData": "Нет текущих данных",
    "dataScreening": "Фильтрация данных",
    "dataScreeningDescription": "Эта функция предназначена для исключения статистики данных из неэффективных периодов игры, таких как загрузка карты или простоя на стартовом экране. 0 означает, что исключение не производится.",
    "excessivelyHighParameter": "Чрезмерные параметры",
    "tooLowParameter": "Слишком низкие параметры",
    "theMaximumValueIs": "Максимум равен",
    "theMinimumValueIs": "Минимальное значение",
    "exclude": "Исключить",
    "dataStatisticsAtThatTime": "Статистика данных на данный момент",
    "itHasBeenGenerated": "Генерация завершена,",
    "clickToView": "Нажмите, чтобы просмотреть",
    "onlineAnalysis": "Онлайн-анализ",
    "amdRyzenAILocalAnalysis": "AMD Ryzen AI Локальный анализ",
    "useTerms": "Условия использования:",
    "term1": "1. Процессор Ryzen AI Max или процессор серии Ryzen Al 300",
    "term2": "2.Версия драйвера AMD NPU",
    "term3": "3. Включить встроенную графику",
    "conformsTo": "Соответствие",
    "notInLineWith": "Не соответствует",
    "theVersionIsTooLow": "Слишком старая версия",
    "canNotUseAmdNpu": "Ваша конфигурация не соответствует требованиям. Анализ сущности AMD NPU невозможен。",
    "unusable": "Недоступно",
    "downloadTheFile": "Скачать файл",
    "downloadSource": "Источник загрузки:",
    "fileSize": "Размер файла: примерно 8,34 ГБ",
    "cancelDownload": "Отменить загрузку",
    "filePath": "Путь к файлу",
    "generateAReport": "Сформировать отчет",
    "fileMissing": "Файл отсутствует. Требуется повторная загрузка.",
    "downloading": "Загрузка...",
    "theModelConfigurationLoadingFailed": "Не удалось загрузить конфигурацию модели",
    "theModelDirectoryDoesNotExist": "Директория модели не существует",
    "thereIsAMistakeInReasoning": "Ошибка вывода",
    "theInputExceedsTheModelLimit": "Ввод превышает лимит модели",
    "selectModelNotSupport": "Выбранный модель загрузки не поддерживается",
    "delDirFail": "Не удалось удалить существующую директорию модели",
    "failedCreateModelDir": "Не удалось создать директорию модели",
    "modelNotBeenFullyDownload": "Модель не загружена полностью",
    "agentIsThinking": "Агент Jiajia думает",
    "reasoningModelFile": "Файл модели для вывода",
    "modelReasoningTool": "Инструмент для вывода модели"
  },
  "SelectSensor": {
    "DefaultSensor": "Датчик по умолчанию",
    "Change": "Изменить",
    "FanSpeed": "Скорость вентилятора",
    "MainGraphicsCard": "Основная видеокарта",
    "SetAsMainGraphicsCard": "Установить как основной графический процессор",
    "GPUTemperature": "Температура GPU",
    "GPUHeatPower": "Тепловая мощность GPU",
    "GPUTemperatureD3D": "Использование D3D GPU",
    "GPUTemperatureTOTAL": "Общая нагрузка на GPU",
    "GPUTemperatureCore": "Температура горячей точки GPU-ядра",
    "MotherboardTemperature": "Температура материнской платы",
    "MyAttention": "Мои избранное",
    "All": "Все",
    "Unit": "Единица:",
    "NoAttention": "Неотслеживаемые сенсоры",
    "AttentionSensor": "Отслеживаемые датчики (бета)",
    "GoToAttention": "Перейти к Фокусу",
    "CancelAttention": "Отписаться",
    "noThisSensor": "Нет датчика",
    "deviceAbout": "Связанные с периферийными устройствами",
    "deviceBattery": "Уровень заряда периферийных устройств",
    "testFunction": "Тестовая функция",
    "mouseEventRate": "Частота опроса",
    "relatedWithinTheGame": "Связанные с игрой",
    "winAbout": "Система",
    "trackDevicesBattery": "Отслеживать уровень заряда периферийных устройств",
    "ingameRealtimeMouseRate": "Текущая частота опроса мыши в реальном времени во время игры",
    "notfoundDevice": "Не найдено поддерживаемое устройство",
    "deviceBatteryNeedMythcool": "Список устройств с поддержкой отображения заряда: (требуется использование Myth.Cool)",
    "vkm1mouse": "Мышь Valkyrie M1",
    "vkm2mouse": "Valkyrie M2 Мышь",
    "vk99keyboard": "Валькирия 99 Клавиатура с Магнитным Валом",
    "logitechProWireless": "Logitech Pro Wireless",
    "logitechProXSUPERLIGHT": "Logitech PRO X SUPERLIGHT",
    "logitechProXSUPERLIGHT2": "Logitech PRO",
    "logitechPro2LIGHTSPEED": "Логитек PRO 2 LIGHTSPEED",
    "steelSeriesArctisGameBuds": "SteelSeries Arctis GameBuds",
    "steelSeriesArctis7PPlus": "Стелсериес Arctis 7P Plus",
    "razerV3": "Razer Valkyrie V3 Профессиональная версия",
    "razerV2": "Razer Viper V2 Профессиональная версия",
    "wireless": "Беспроводной",
    "logitechNeedGhub": "Если Logitech не может получить модель устройства, необходимо загрузить GHUB",
    "chargingInProgress": "Зарядка",
    "inHibernation": "В режиме спячки"
  },
  "video": {
    "videoRecord": "Запись видео",
    "recordVideo": "Записанное видео",
    "scheme": "Профиль",
    "suggestScheme": "Рекомендуемый план",
    "text1": "При этой конфигурации объем 1-минутного видео составляет примерно",
    "text2": "Эта функция использует дополнительные системные ресурсы",
    "low": "Низкий",
    "mid": "Главная",
    "high": "Высокий",
    "1080p": "Нативный",
    "RecordingFPS": "Запись FPS",
    "bitRate": "Видео битрейт",
    "videoResolution": "Разрешение видео",
    "startStopRecord": "Начать/Остановить запись экрана",
    "instantReplay": "Мгновенная Перезапись",
    "instantReplayTime": "Мгновенная длительность воспроизведения",
    "showIngame": "Запустить контрольную панель во время игры",
    "CaptureMode": "Метод захвата",
    "gameWindow": "Игровое окно",
    "desktopWindow": "Окно рабочего стола",
    "fileSavePath": "Путь хранения файлов",
    "selectVideoSavePath": "Выбрать путь сохранения записи",
    "diskFreeSpace": "Свободное место на жестком диске:",
    "edit": "Изменить",
    "open": "Открыть",
    "displayMouse": "Показать курсор мыши",
    "recordMicrophone": "Запись микрофона",
    "gameGraphics": "Оригинальное отображение игры"
  },
  "Setting": {
    "common": "Общий",
    "personal": "Персонализация",
    "messageNotification": "Уведомления",
    "sensorReading": "Показания датчика",
    "OLEDscreen": "Защита от ожога экрана OLED",
    "performanceStatistics": "Статистика производительности",
    "shortcut": "Комбинация клавиш",
    "ingameSetting": "Сохранить настройки игры",
    "other": "Другой",
    "otherSettings": "Другие Настройки",
    "GeneralSetting": "Общие Настройки",
    "softwareVersion": "Версия программного обеспечения",
    "checkForUpdates": "Проверить обновления",
    "updateNow": "Обновить сейчас",
    "currentVersion": "Текущая версия",
    "latestVersion": "Последняя версия",
    "isLatestVersion": "Текущая версия уже является последней.",
    "functionModuleUpdate": "Обновление функционального модуля",
    "alwaysUpdateModules": "Сохранить все установленные функциональные модули в последней версии",
    "lang": "Язык",
    "bootstrap": "Автозапуск",
    "powerOn_m1": "Запуск",
    "powerOn_m2": "Автозапуск через секунд",
    "defaultDelay": "По умолчанию: 40 секунд",
    "followSystemScale": "Следовать масштабированию системы",
    "privacySettings": "Настройки конфиденциальности",
    "JoinGamePPPlan": "Присоединитесь к программе улучшения пользовательского опыта GamePP",
    "personalizedSetting": "Персонализация",
    "restoreDefault": "Восстановить параметры по умолчанию",
    "color": "Цвет",
    "picture": "Изображение",
    "video": "Видео",
    "browse": "Просмотр",
    "clear": "Очистить",
    "mp4VideoOrPNGImagesCanBeUploaded": "Загрузка видео MP4 или изображений PNG",
    "transparency": "Прозрачность",
    "backgroundColor": "Цвет фона",
    "textFont": "Основной текстовый шрифт",
    "message": "Сообщение",
    "enableInGameNotifications": "Включить уведомления в игре",
    "messagePosition": "Позиция отображения в игре",
    "leftTop": "Верхний левый угол",
    "leftCenter": "Левый центр",
    "leftBottom": "Нижний левый угол",
    "rightTop": "Правый верхний угол",
    "rightCenter": "Правая середина",
    "rightBottom": "Правый нижний угол",
    "noticeContent": "Содержание уведомления",
    "gameInjection": "Игровая Инъекция",
    "ingameShow": "Показать в игре",
    "inGameMonitoring": "Мониторинг в игре",
    "gameFilter": "Игровой фильтр",
    "start": "Начать",
    "endMarkStatistics": "Завершить статистику меток",
    "readHwinfoFail": "HWINFO Ошибка считывания информации о оборудовании",
    "dataSaveDesktop": "Данные сохранены в буфер обмена и файл на рабочем столе",
    "TheSensorCacheCleared": "Кэшированные данные датчика очищены",
    "defaultSensor": "Сенсор по умолчанию",
    "setSensor": "Выбрать Датчик",
    "refreshTime": "Время обновления данных",
    "recommend": "По умолчанию",
    "sensorMsg": "Чем короче временной интервал, тем выше потребление ресурсов. Выбирайте внимательно.",
    "exportData": "Экспорт данных",
    "exportHwData": "Экспорт данных аппаратной информации",
    "sensorError": "Аномальные показания датчика",
    "clearCache": "Очистить кэш",
    "littleTips": "Совет: Отчет о производительности будет сгенерирован через 2 минуты после запуска игры",
    "disableAutoShow": "Отключить автоматическое всплывающее окно статистики производительности",
    "AutoClosePopUpWindow_m1": "Окно статистики производительности автоматически закроется через заданное время:",
    "AutoClosePopUpWindow_m2": "секунд",
    "abnormalShutdownReport": "Отчет об аварийном отключении",
    "showWeaAndAddress": "Отображать погоду и информацию о местоположении",
    "autoScreenShots": "Автоматически делать скриншот экрана игры при маркировке",
    "keepRecent": "Количество недавних записей для хранения:",
    "noLimit": "Без ограничений",
    "enableInGameSettingsSaving": "Включить сохранение настроек в игре",
    "debugMode": "Режим отладки",
    "enableDisableDebugMode": "Включить/выключить режим отладки",
    "audioCompatibilityMode": "Режим совместимости аудио",
    "quickClose": "Быстрое закрытие",
    "closeTheGameQuickly": "Быстро закрыть игровой процесс",
    "cancel": "Отмена",
    "confirm": "Подтвердить",
    "MoveInterval_m1": "Мониторинг рабочего стола и внутри игры немного сдвинется:",
    "MoveInterval_m2": "минуты",
    "text3": "После выхода из игры окно отчета о производительности не будет отображаться, будут сохранены только исторические записи",
    "text5": "После неожиданного выключения будет автоматически сгенерирован отчет. Включение этой функции потребует дополнительных системных ресурсов.",
    "text6": "Использование функции горячих клавиш может конфликтовать с другими игровыми горячими клавишами, пожалуйста, настраивайте с осторожностью,",
    "text7": "Установите сочетание клавиш как \"Нет\", используйте клавишу Backspace",
    "text8": "Сохранять состояние фильтров, мониторинга в игре и других функций во время выполнения игры на основе названия процесса",
    "text9": "Включение будет постоянно записывать журналы выполнения; отключение очистит файлы журналов (Рекомендуется отключить)",
    "text10": "После включения датчики материнской платы будут недоступны для устранения проблем с аудио, вызванных GamePP",
    "text11": "Дважды нажмите Alt+F4, чтобы быстро выйти из текущей игры",
    "text12": "Вы хотите продолжить? Этот режим требует перезапуска GamePP.",
    "openMainUI": "Показать приложение",
    "setting": "Настройки",
    "feedback": "Обратная связь по проблемам",
    "help": "Помощь",
    "sensorReadingSetting": "Настройки показаний датчиков",
    "searchlanguage": "Язык поиска"
  },
  "GamePlusOne": {
    "year": "Год",
    "month": "месяц",
    "day": "День",
    "success": "Успех",
    "fail": "Неудача",
    "will": "Текущий",
    "missedGame": "Пропущенные игры",
    "text1": "Сумма, прибл. ￥",
    "text2": "Общее количество полученных игр",
    "text3": "Версия",
    "gamevalue": "Стоимость Игры",
    "gamevalue1": "Получить",
    "total": "Всего заявлено",
    "text4": "игр, совокупное сэкономленное время",
    "text6": "Продукт, Значение",
    "Platformaccountmanagement": "Управление учетными записями платформы",
    "Missed1": "(Не получено)",
    "Received2": "(Успешно получено)",
    "Receivedsoon2": "Сейчас доступно",
    "Receivedsoon": "Доступно сейчас",
    "Missed": "Пропущенное Получение",
    "Received": "Успешно получено",
    "Getaccount": "Получить учетную запись",
    "Worth": "Значение",
    "Auto": "Автоматически",
    "Manual": "Вручную",
    "Pleasechoose": "Выберите игру",
    "Receive": "Получить сейчас",
    "Selected": "Выбрано",
    "text5": "Игр",
    "Automatic": "Автоматическое получение...",
    "Collecting": "Получение...",
    "ReceiveTimes": "Количество получений за этот месяц",
    "Thefirst": "№",
    "Week": "неделя",
    "weekstotal": "Всего 53 недели",
    "Return": "Главная",
    "Solutionto": "Сбой привязки аккаунта. Решение проблемы",
    "accounts": "Количество привязанных аккаунтов",
    "Addaccount": "Добавить учетную запись",
    "Clearcache": "Очистить кэш",
    "Bindtime": "Время привязки",
    "Status": "Состояние",
    "Normal": "Нормальный",
    "Invalid": "Недействительный",
    "text7": "игр, общая сумма сэкономленного",
    "Yuan": "Юань",
    "untie": "Отвязать",
    "disable": "Отключить",
    "enable": "Включить",
    "gamePlatform": "Игровая платформа",
    "goStorePage": "Перейти на страницу магазина",
    "receiveEnd": "После окончания срока",
    "loginPlatformAccount": "Входящий аккаунт платформы",
    "waitReceive": "Ожидает получения",
    "receiveSuccess": "Успешно получено",
    "accountInvalid": "Аккаунт истёк",
    "alreadyOwn": "Уже Приобретено",
    "networkError": "Сетевая Аномалия",
    "noGame": "Нет Основной Игры",
    "manualReceiveInterrupt": "Прерывание Ручного Получения",
    "receiving": "В процессе получения",
    "agree": "Я согласен присоединиться к бесплатной программе получения GamePP.",
    "again": "Собрать снова"
  },
  "shutdownTimer": {
    "timedShutdown": "Завершение работы по расписанию",
    "currentTime": "Текущее время:",
    "setCountdown": "Настроить обратный отсчет",
    "shutdownInSeconds": "Выключение через X секунд",
    "shutdownIn": "После выключения",
    "goingToBe": "будет",
    "executionPlan": "План выполнения",
    "startTheClock": "Запустить таймер",
    "stopTheClock": "Отменить план",
    "isShuttingDown": "Выполняется запланированное выключение:",
    "noplan": "Нет текущего плана выключения",
    "hour": "Час",
    "min": "Минута",
    "sec": "Секунда",
    "ms": "Миллисекунда",
    "year": "Год",
    "month": "Месяц",
    "day": "День",
    "hours": "Час"
  },
  "screenshotpage": {
    "screenshot": "Скриншот",
    "screenshotFormat": "Специально разработано для захвата игровых экранов, поддерживает сохранение в формате JPG/PNG/BMP, позволяет быстро делать снимки экрана игр и обеспечивает высококачественный вывод без потерь",
    "Turnon": "Включить автоматический скриншот, каждые",
    "seconds": "секунда",
    "takeScreenshot": "Выполнить автоматический скриншот",
    "screenshotSettings": "Этот параметр не действует при выборе в игре",
    "saveGameFilterAndMonitoring": "Сохранять эффекты 'Игровой фильтр' и 'Мониторинг в игре' на скриншоте",
    "disableScreenshotSound": "Выключить звуковое уведомление о снимках экрана",
    "imageFormat": "Формат изображения",
    "recommended": "Рекомендовать",
    "viewingdetails": "Сохраняет детали качества изображения, умеренный размер, подходит для просмотра деталей",
    "saveSpace": "Сжимаемое качество изображения, минимальный объем, экономия места",
    "ultraQuality": "Сверхвысокое разрешение без сжатия, очень большой размер файла. Рекомендуется игрокам, стремящимся к сохранению максимального качества изображения.",
    "fileSavePath": "Путь сохранения файла",
    "hardDiskSpace": "Свободное место на жёстком диске:",
    "minutes": "Минута"
  },
  "DesktopMonitoring": {
    "desktopMonitoring": "Мониторинг рабочего стола",
    "SomeSensors": "Мы рекомендуем некоторые датчики для мониторинга. Их можно удалить или добавить",
    "AddComponent": "Добавить новый компонент",
    "Type": "Тип",
    "Remarks": "Примечание",
    "AssociatedSensor": "Связать сенсор",
    "Operation": "Операция",
    "Return": "Назад",
    "TimeSelection": "Выбор времени:",
    "Format": "Формат:",
    "Rule": "Правило：",
    "Coordinate": "Координаты:",
    "CustomTextContent": "Пользовательский текст:",
    "SystemTime": "Системное время",
    "China": "Китай",
    "Britain": "Великобритания",
    "America": "США",
    "Russia": "Россия",
    "France": "Франция",
    "DateAndTime": "Дата и время",
    "Time": "Время",
    "Date": "Дата",
    "Week": "День недели",
    "DateAndTimeAndWeek": "Дата+Время+День недели",
    "TimeAndWeek": "Время+День недели",
    "Hour12": "12-часовой формат",
    "Hour24": "24-часовой формат",
    "SelectSensor": "Выберите сенсор:",
    "AssociatedSensor1": "Связать датчик:",
    "SensorUnit": "Единица измерения сенсора：",
    "Second": "Секунда:",
    "Corner": "Скругленные углы:",
    "BackgroundColor": "Цвет фона:",
    "ProgressColor": "Цвет прогресса:",
    "Font": "Шрифт：",
    "SelectFont": "Выберите шрифт",
    "FontSize": "Размер шрифта:",
    "Color": "Цвет：",
    "Style": "Стиль:",
    "Bold": "Полужирный",
    "Italic": "Курсив",
    "Shadow": "Тень",
    "ShadowPosition": "Позиция тени：",
    "ShadowEffect": "Эффекты теней：",
    "Blur": "Размытие",
    "ShadowColor": "Цвет тени：",
    "SelectFromLocalFiles": "Выбрать из локальных файлов:",
    "UploadImageVideo": "Загрузить изображение/видео",
    "UploadSVGFile": "Загрузить SVG-файл",
    "Width": "Ширина:",
    "Height": "Высокий: ",
    "Effect": "Эффект:",
    "Rotation": "Вращение:",
    "WhenTheSensorValue": "Значение сенсора больше чем",
    "conditions": "Когда условия не выполнены (нет вращения при значении сенсора 0)",
    "Clockwise": "По часовой стрелке",
    "Counterclockwise": "Против часовой стрелки",
    "QuickRotation": "Быстрое вращение",
    "SlowRotation": "Медленное вращение",
    "StopRotation": "Остановить вращение",
    "StrokeColor": "Цвет контура：",
    "Path": "Путь",
    "Color1": "Цвет",
    "ChangeColor": "Изменить цвет",
    "When": "Когда",
    "SensorValue": "Значение датчика больше или равно",
    "SensorValue1": "Значение сенсора меньше или равно",
    "SensorValue2": "Значение сенсора равно",
    "MonitoringSettings": "Настройки мониторинга",
    "RestoreDefault": "Восстановить значения по умолчанию",
    "Monitor": "Монитор",
    "AreaSize": "Размер области",
    "Background": "Фон",
    "ImageVideo": "Изображения/Видео",
    "PureColor": "Чистый цвет",
    "Select": "Выбрать",
    "ImageVideoDisplayMode": "Режим отображения изображений/видео",
    "Transparency": "Прозрачность",
    "DisplayPosition": "Показать позицию",
    "Stretch": "Растяжение",
    "Fill": "Заполнение",
    "Adapt": "Адаптировать",
    "SelectThePosition": "Нажмите на ячейку, чтобы быстро выбрать положение",
    "CurrentPosition": "Текущее местоположение:",
    "DragLock": "Закрепление при перетаскивании",
    "LockMonitoringPosition": "Позиция монитора заблокирована (После блокировки монитор нельзя перемещать)",
    "Unlockinterior": "Включить перемещение внутренних элементов",
    "Font1": "Шрифт",
    "GameSettings": "Настройки игры",
    "CloseDesktopMonitor": "Автоматически отключить мониторинг рабочего стола при запуске игры.",
    "OLED": "Защита OLED от ожога экрана",
    "Display": "Показать",
    "PleaseEnterContent": "Введите содержание",
    "NextStep": "Далее",
    "Add": "Добавить",
    "StylesForYou": "Мы рекомендуем несколько стилей мониторинга. Вы можете выбрать и применить их. В будущем будут добавлены новые стили.",
    "EditPlan": "Редактировать профиль",
    "MonitoringStylePlan": "Схема стиля мониторинга",
    "AddDesktopMonitoring": "Добавить мониторинг рабочего стола",
    "TextLabel": "Текстовая метка",
    "ImageVideo1": "Изображения, Видео",
    "SensorGraphics": "Графика датчика",
    "SensorData": "Данные сенсора",
    "CustomText": "Пользовательский текст",
    "DateTime": "Дата и время",
    "Image": "Изображение",
    "Video": "Видео",
    "SVG": "SVG",
    "ProgressBar": "Полоса прогресса",
    "Graphics": "Графика",
    "UploadImage": "Загрузить изображение",
    "UploadVideo": "Загрузить видео",
    "RealTimeMonitoring": "Реальное отслеживание температуры и загрузки CPU/GPU, свободное перетаскивание и настройка макета, персонализация стиля – управляйте производительностью и эстетикой рабочего стола",
    "Chart": "Диаграмма",
    "Zigzagcolor": "Цвет линии (Начало)",
    "Zigzagcolor1": "Цвет линии (Конечная точка)",
    "Zigzagcolor2": "Цвет области линейного графика (Начало)",
    "Zigzagcolor3": "Цвет области линейного графика (конец)",
    "CustomMonitoring": "Пользовательский мониторинг"
  }
}
//messageEnd 
 export default ru 