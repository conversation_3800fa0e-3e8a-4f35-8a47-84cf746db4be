<script setup lang="ts">
import { ref, computed, onMounted , onUnmounted,inject} from 'vue'
import { useRouter } from "vue-router";
import { defineProps } from 'vue';
import { moduleInfo } from '../../modules/Game_Home/stores/index';
import { gameppBaseSetting,hardware} from '../../modules/Game_Home/stores';
import { gamepp } from 'gamepp'
import LoginStatus  from '../LoginStatus/loginstatus.vue';
import { number } from 'echarts/core';
import vipImg from '../assets/img/vip.png';
import vipFalseImg from '../assets/img/vipfalse.png';
import { el, pa } from 'element-plus/es/locale';
import defaultAvatar  from '../assets/img/userhead.png';
import emitter from '../../uitls/emitter'
import { ElMessage } from "element-plus";
import { useZoomStore } from '../../modules/Game_Home/stores/zoomStore';
import {useI18n} from "vue-i18n";
defineOptions({
  // 命名当前组件
  name: "sideBar"
})
const user = moduleInfo()
const router = useRouter()
const gamepp2 = gameppBaseSetting()
const zoomStore = useZoomStore();
const isLoginVisible = ref(false); // 控制登录状态组件的显示
const isLoggedIn = ref(false); // 用户是否登录状态显示
const isVip = ref(false);
const timer:any = ref(0);
const showIframe = ref(false);
const iframeSrc = ref('about:blank');
const drawer = ref(false)
const direction = ref('ltr')
const UserName = ref('')
const email = ref('')
const imgavatar = ref('')
const iframeRef = ref<HTMLIFrameElement | null>(null);
// const setIntervalUserRefresh = ref<NodeJS.Timer | null>(null);
const loading = ref(true)
const {t} = useI18n()
// const currentLanguage = ref('CN');
const currentLanguage = inject('currentLanguage');
onMounted(async() =>
  {
    console.log('user', user.tabInfo)
    console.log('currentLanguage', currentLanguage)
    await init()
    await initPage()
    gamepp.webapp.onInternalAppEvent.addEventListener(async data =>
   {
      console.warn('dataValue',data)
      if(data['action'] == 'LoginSuccessful')
      {
        await init()
        drawer.value = false
        isLoggedIn.value = true;
        await gamepp.user.refreshUserToken.promise(true);
      }
      if(data['action'] == 'LogBackIn'){
        drawer.value = true
        isLoggedIn.value = true;
        LoginEntry()
      }
      if(data['action'] == 'ChangeName'){
        await init()
      }
      if(data['action'] == 'RegisterBindOpenid'){
        console.log(data)
        iframeRef.value.contentWindow.postMessage(data, '*');
      }
    });

     //监听更新用户信息
    gamepp.user.onUserInfoUpdate.addEventListener( async(value) => {
      await init()
    });
  })

  emitter.on('Setup-Login',()=>{
    console.log('登录')
    drawer.value = true
    LoginEntry()
  })

  emitter.on('Setup-Logout',(value:boolean)=>{
    getlogout(value)
  })

  // 在组件卸载时解绑事件
  onUnmounted(()=>{
    emitter.all.clear()
  })



const initPage = async () =>
{
  initChunk()
}

const init = async () =>
{
  const data_arr = await gamepp.user.loadBaseInfo.promise();
  if (data_arr['online'] === -1) {
    isLoggedIn.value = false;
  }else {
    isLoggedIn.value = true;
  }

  if(data_arr['uname'] !== ''){
    const decodedUname = decodeURIComponent(data_arr['uname']);
    UserName.value = decodedUname;
  }

  email.value = data_arr['email'];
  imgavatar.value = data_arr['avatar'];

  const result = await gamepp.user.isVIP.promise();
  isVip.value = result;
  console.log('是否vip',isVip.value)
}



const handleError = (event:any) => {
  event.target.src = defaultAvatar;
};

const MouseEnter = () => {
  isLoginVisible.value = true;
  if (timer.value) {
    clearTimeout(timer.value);
  }
};

const MouseLeave = () => {
  timer.value = setTimeout(() => {
    isLoginVisible.value = false;
  }, 500);
};

const LoginMouseEnter = () => {
  if (timer.value) {
    clearTimeout(timer.value);
  }
  isLoginVisible.value = true;
};

const LoginMouseLeave = () => {
  timer.value = setTimeout(() => {
    isLoginVisible.value = false;
  }, 500);
};

const getlogout = async(value:boolean) => {
  isLoginVisible.value = value;
  isLoginVisible.value = value;
  isLoggedIn.value = value;
  gamepp.user.logout.promise();
};


const LoginEntry = () => {
  showIframe.value = true;
  const randomNum = Math.random();
  iframeSrc.value = `https://client-v3.gamepp.com/account/login.html?timestamp=${randomNum}`;
  loading.value = false;
};

const closeDrawer = (closeFunction:Function) => {
  showIframe.value = false;
  closeFunction();
};

const iframeLoaded = () => {
  const randomNum = Math.random();
  if (iframeSrc.value === 'about:blank') {
    iframeSrc.value = `https://client-v3.gamepp.com/account/login.html?timestamp=${randomNum}`;
    loading.value = false;
  }
};

const beforeClose = (done:Function) => {
  // 重置 iframeSrc
  iframeSrc.value = 'about:blank';
   // 调用 done 函数关闭抽屉
  done();
};


const vipImgSrc = computed(() => {
  return isVip.value ? vipImg : vipFalseImg;
});

//路由跳转
const routeTo = (index:any) =>
{
  if(index ==  9|| index == 10)
  {
    if(user.tabInfo[index].download.installed)
    {

        gamepp.webapp.windows.show.sync(user.tabInfo[index].windowName,'desktop')
    }
    else
    {
      ElMessage({
               message: t('messages.moduleNotInstalled'),
               type: 'warning',
               grouping:true
      })
    }
    return
  }

  if(index ==  9|| index == 10)return
  const moduleMap:any =
  {
         3: { name: 'GameRebound', textIndex: 0 },
         4: { name: 'GameMirror', textIndex: 1 },
         5: { name: 'GameCoreAssign', textIndex: 2 },
         6: { name: 'GameFreeGame', textIndex: 3 },
         7: { name: 'GameObs', textIndex: 4 },
         8: { name: 'AiLab', textIndex: 5 },
        11: { name: 'DesktopMonitor', textIndex: 6 },
  };
  console.warn('index',index);
  console.warn('gamepp ',gamepp2.gameppHeight);
  if(!user.tabInfo[index].module)
  {
    user.tabInfo.forEach((v:any,i:any)=>{v.choosen = false})
    user.tabInfo[index].choosen = true
    chunkAnimate(index)
  }
  else
  {
    if(user.tabInfo[index].download.installed)
    {
      if(gamepp.package.islibversionmatch.sync(moduleMap[index].name))
      {
        gamepp.webapp.windows.show.sync(user.tabInfo[index].windowName,'desktop')
      }
      else
      {
        ElMessage({
                message: t('Setting.functionModuleUpdate'),
                type: 'warning',
                grouping:true
              })
      }
    }
    else
    {
      ElMessage({
               message: t('messages.moduleNotInstalled'),
               type: 'warning',
               grouping:true
            })
    }
  }
  localStorage.setItem('moduleInfosetting',JSON.stringify(user.tabInfo))
}

const GPP_OpenURL = (url:string) =>
{
  try {
    gamepp.shell.openExternal.sync(url);
  } catch (error) {
    window.open(url)
  }
}

const initChunk = () =>
{
  const InleftTab = user.tabInfo.filter((v)=>{
    return v.Inleft == true
  })

  const index = InleftTab.findIndex((v)=>{
    return v.choosen == true
  })

  user.activeTab = index
}

const chunkAnimate = (innum:number) =>
{
  const InleftTab = user.tabInfo.filter((v)=>{
    return v.Inleft == true
  })

  const index = InleftTab.findIndex((v)=>{
    return v.choosen == true
  })

  router.push({
    path: user.tabInfo[innum].route
  })


  user.activeTab = index
}
</script>

<template>
  <div class="left" :style="{height:`${gamepp2.gameppHeight}+'px'`}">
    <div class="Fordrag" style="width: 100%;height: 50px;-webkit-app-region:drag;"></div>
    <div class="banner" style="display: flex;cursor: pointer;" @click="GPP_OpenURL('https://gamepp.com')">
      <div style="display: flex;align-items: center;">
        <img src="../assets//img/gamepp2.png" alt="" class="gpp-img">
          <div class="gpp-text">
              <span>{{$t('update.Game')}}</span>
              <span>{{$t('update.PP')}}</span>
          </div>
      </div>
      <div style="margin-top: 5px;letter-spacing: 0.2em;">
        <p style="color: #FFFFFF;cursor: pointer;">GamePP.com</p>
      </div>
      <!-- <p class="version" style="margin-top: 39px;color:#555555;font-size: 12px;">V 5.3.2212.1229</p> -->
    </div>
    <div class="menu_box scroll" :style="{height:  `${(gamepp2.gameppHeight  - (200*zoomStore.zoomLevel) )/ zoomStore.zoomLevel}px`}">
      <div class="menu">
      <div class="avtivechuck" :style="{ top: (user.activeTab*80) + 'px' }"></div>
      <div class="item" :class="[item.choosen?'isActive':'']"  v-for="(item,index) in user.tabInfo" @click="routeTo(index)" v-show="item.Inleft">
        <span v-show="item.icon != ''"  :class="item.icon" style="margin-left: 10px;width: 24px;height: 24px;font-size: 20px;"></span>
        <!-- <img v-show="item.icon == ''"  src="../../modules/Game_Home/assets/icon_erha.png" alt="" style="width: 24px;height: 24px;margin-left: 10px;"> -->
        <img v-show="!item.icon" :src="item.img" alt="" style="width: 24px;height: 24px;margin-left: 10px;">
        <p style="width: 80px;text-align: left;margin-left: 10px;">
          <!-- {{ currentLanguage === 'CN' ? item.name : item.enname }} -->
            {{ $t(item.name) }}
        </p>
      </div>
    </div>
    </div>

    <div class="status_box">
      <el-drawer v-model="drawer"  class="adddrawer" :append-to-body="true" :direction="direction" title=""   :with-header="true" :size="360" :before-close="beforeClose">
        <template #header="{ close, }">
          <el-button type="danger" @click="closeDrawer(close)" class="buttonstyle">
            <!-- <el-icon class="el-icon--left"><CircleCloseFilled /></el-icon> -->
          </el-button>
        </template>
        <el-table v-loading="loading"></el-table>
        <iframe class="iframebar" v-show="showIframe" ref="iframeRef" :src="iframeSrc" frameborder="0" width="100%" height="100%" scrolling="auto"  @load="iframeLoaded"></iframe>
      </el-drawer>

      <!-- 未登录 -->
      <div class="status_itme" @click="LoginEntry()"  v-show="!isLoggedIn">
        <el-button type="primary"  @click="drawer = true">
          <!-- <span class="iconfont"></span> -->
          <div class="block_bar">
            <img src="../assets/img/userhead.png" alt="">
            <p>{{$t('LoginArea.login')}}</p>
          </div>
        </el-button>
      </div>

      <!-- 已登录 -->
      <div class="status_itme"  v-show="isLoggedIn" @click="emitter.emit('Active-UserName',true)"  @mouseenter="MouseEnter" @mouseleave="MouseLeave">
        <div class="avatar">
          <img :src="imgavatar" @error="handleError" alt="">
        </div>
        <div class="user">
          <p>{{ UserName }}</p>
          <img :src="vipImgSrc">
        </div>
      </div>
    </div>

    <Teleport to="body">
      <LoginStatus  v-show="isLoginVisible"  @mouseenter="LoginMouseEnter" @mouseleave="LoginMouseLeave" :isVip="isVip" :sendLogout="getlogout" :UserName="UserName" :email="email" />
    </Teleport>
  </div>
</template>
<style lang="scss" scoped>
.gpp-img {
    width: 24px; height: 24px; margin-right: 5px;
}
.gpp-text span:first-child {
    color: #3981D0;
    font-size: 18px;
    font-weight: bold;
}

.gpp-text span:nth-child(2) {
    color: #F6B619;
    font-size: 18px;
    font-weight: bold;
}
.left{
  position: relative;
  width: 160px;
  // height: 100%;
  // background-color:#1C1C22;
  background:rgba(28, 28, 34, 0.8);
  font-size: 12px;
  z-index: 5;
  // flex-shrink: 0;
  .banner{
    width: 100%;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;

  }
  span{
    display: inline-flex;
    align-items: center;
    font-size: 16px;
    // font-weight: bold;
  }
  .menu_box{
    overflow: auto;
    height: 610px;
    overflow-x: hidden;
  }
  .menu{
    margin-top:20px;
    // height: 600px;
    // overflow: auto;
    width:160px;
    display: flex;
    // flex-direction: column;
    flex-wrap: wrap;
    align-items: center;
    position: relative;
    justify-content: center;
    .item{
      color: #999999;
      width: 140px;
      height: 60px;
      font-size: 12px;
      background-color: transparent;
      margin-bottom: 20px;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 2;
      cursor: pointer;
      p{
        word-wrap: break-word;
      }
    }


    .item:hover{
      color: #FFFFFF;
      // background:linear-gradient(-90deg, #42434D 0%, #383943 100%);
    }
    .isActive{
      color: #FFFFFF;
      // background:linear-gradient(-90deg, #42434D 0%, #383943 100%);
    }
    .avtivechuck{
      position: absolute;
      width: 140px;
      height: 60px;
      z-index: 1;
      background:linear-gradient(-90deg, #42434D 0%, #383943 100%);
      border-radius: 4px;
      transition: 0.2s linear;
    }
  }


  .status_box{
    position: absolute;
    bottom: 20px;
    width: 100%;
    .status_itme{
      width: 140px;
      height: 70px;
      background: #393943;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      margin: 0 auto;
      cursor: pointer;
      .block{
        height: 50px;
        line-height: normal;
      }
      .user{
        p{
          color: #FFFFFF;
          font-size: 12px;
          width:56px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-bottom: 5px;
        }
      }
      .avatar{

        width: 50px;
        height: 50px;
        border-radius: 4px;
        background-color: #393943;
        img{
          width: 100%;
          height: 100%;
          border-radius: 6px;
        }
      }
    }
  }
  // .overload{
  //     position: absolute;
  //     left: 20px;
  //     width: 200px;
  //     height: 50px;
  //     border: 1px solid pink;
  // }
}
.block_bar{
  display: flex;
  align-items: center;
  // justify-content: space-evenly;
  width: 100%;
  p{
    margin-left: 10px;
  }
  img{
    margin-left: 10px;
  }
}
.iframebar{
  position: absolute;
  right: 0;
  top: 0;
  z-index: 99;
}

.scroll::-webkit-scrollbar {
    width: 5px;
    transition: 0.25s;
}

.scroll::-webkit-scrollbar-thumb {
    background: #71738C;
    border-radius: 3px;
}

.scroll::-webkit-scrollbar-thumb:hover {
    background-color: #71738C;
}
.scroll::-webkit-scrollbar-track {
    background-color: #2D2E39;
    width: 2px;
}

/* Your styles here */
</style>
<style>
.icon-icon_timed:before {
    content: "\e689";
}
.status_itme .el-button--primary{
    background: transparent;
    border: none;
}
.el-drawer__body{
    padding: 0;
    overflow: hidden;
}
.el-overlay{
    cursor: pointer;
}
.el-drawer{
  background: #22232e;
}
.buttonstyle{
    position: absolute;
    z-index: 999;
    top: 49px;
    background: transparent;
    border: 0;
    width: 100px;
    height: 30px;
}
.buttonstyle:active {
    background-color: transparent;
    border-color: transparent;
    color: transparent;
    outline: none;
}
.buttonstyle:hover {
    background-color: transparent;
    border-color: transparent;
    color: transparent;
    outline: none;
}
.status_itme .el-button:focus-visible {
    outline: 0px solid var(--el-button-outline-color);
    outline-offset: 0px;
}
.adddrawer .el-drawer__header{
  color: transparent!important;
}
.el-overlay .el-table--fit{
  width: 100%;
  height: 101%;
  background: transparent;
}
.el-overlay .el-loading-mask{
  background: transparent!important;
  z-index: 99;
}
.el-overlay .el-table__empty-text{display: none;}
.status_itme .el-button{padding: 0;}
.status_itme .el-button span{width: 140px;}
</style>


