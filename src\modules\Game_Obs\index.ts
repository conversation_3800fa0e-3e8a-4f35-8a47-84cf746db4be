import { createApp } from 'vue'
import { createPinia } from 'pinia'

import 'element-plus/theme-chalk/dark/css-vars.css'
import './simple_reset.css'
import './Game_Obs_RTL.scss'
import '../../assets/iconfont/iconfont.css'
import '../../assets/iconfont/iconfont.js'
import App from './index.vue'
import i18n from '../../assets/lang'

const app = createApp(App)

app.use(createPinia())
app.use(i18n)
app.mount('#app')
