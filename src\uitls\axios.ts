import axios, { AxiosInstance, AxiosResponse,InternalAxiosRequestConfig } from 'axios';

const axiosInstance: AxiosInstance = axios.create({
    timeout: 5000,
});

// 添加请求拦截器
axiosInstance.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
        // 在发送请求之前做些什么
        return config;
    },
    (error: any) => {
        // 处理请求错误
        return Promise.reject(error);
    },
);

// 添加响应拦截器
axiosInstance.interceptors.response.use(
    (response: AxiosResponse) => {
        // 对响应数据做点什么
        return response;
    },
    (error: any) => {
        // 处理响应错误
        return Promise.reject(error);
    },
);

// 返回值类型
export interface ApiResult {
    code: number;
    message: string;
    data: any;
}

export async function get(url: string, params?: any): Promise<ApiResult> {
    const response = await axiosInstance.get<ApiResult>(url, { params });
    return response.data;
}

export async function post(url: string, data?: any): Promise<ApiResult> {
    const response = await axiosInstance.post<ApiResult>(url, data);
    return response.data;
}

export async function put(url: string, data?: any): Promise<ApiResult> {
    const response = await axiosInstance.put<ApiResult>(url, data);
    return response.data;
}

export async function del(url: string, params?: any): Promise<ApiResult> {
    const response = await axiosInstance.delete<ApiResult>(url, { params });
    return response.data;
}

