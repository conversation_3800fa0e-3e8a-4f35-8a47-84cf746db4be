import {defineStore} from 'pinia'
import {totalSeconds} from "@/modules/Game_ReboundDetail/components/someScripts";
import axios from "axios";

const ai_agent_bc = new BroadcastChannel('ai_agent')
let peCoreArr:any[] = [0]
export const useReboundDetailStore = defineStore('reboundDetail', {
  state: () => ({
    startTime: {h: 0, m: 0, s: 0},
    endTime: {h: 0, m: 0, s: 0},
    endTimeOriginal: {h: 0, m: 0, s: 0},
    gameTime: 0, // 游戏时长
    powerData: {} as any,
    hardwaerinfo: {} as any,
    recentGameInfo: {} as any,
    curDisplayDataCount: 0,
    CPA: { // CPU性能分析的数据 CPU Performance Analysis
      show_data: {name: 'fps', value: 'FPS',value2:""},
      // 有多少条cpu性能受限的数据
      limit_data_temp: new Set(),
      limit_data_other: new Set(),
    },
    fpsLimitDialogShow: false,
    max_limit: 0, // 排除 FPS ≥ 的值 ，为0表示不限制
    min_limit: 0, // 排除 FPS ≤ 的值 ，为0表示不限制
    isLab: false, // 是不是手动记录
    dev:{
      PCType:"",
      hwinfo:{} as any,
      processTableInfo:[] as any[],
      cpuComplexInfo: null,
    },
    ai_agent:{
      resultContent:"",
      ai_agent_id:"",
      isWaiting: false,
      try_count: 0,
      errMsg:"",
      baseinfo_id:-1,
      baseinfo_timestamp: 0,
      statusNot200: false,
      disableCount: 0,

      // amd ryzen ai
      downloaded: false,
      downloadDialogShow: false,
      downloadDialogShowOnlyPath: false,
      downloading: false,
      downloadingExe: false,
      downloadExeProgress:{
        "percent": 0,
      },
      netError: false,
      downloadLLMProgress: {
        "total_num": 0,
        "current_download_num": 0,
        "percent": 0,
        "error":"",
      },
      RunLLMError:"",
      isGenerating: false,
    },
    isManual:false,
  }),
  actions: {
    closeFpsLimitDialog(){
      this.fpsLimitDialogShow = false;
    },
    openFpsLimitDialog () {
      this.fpsLimitDialogShow = true;
    },
    setMaxLimit(num:number){
      if (num < 0) {
        num = 0
      }
      this.max_limit = num
    },
    setMinLimit(num:number){
      if (num < 0) {
        num = 0
      }
      this.min_limit = num
    },
    handleInputTime (val: string, type: 'h'|'m'|'s', type2: string) {
      let v: number = Number(val)
      if (isNumber(v)) {
        if (type2 === 'start') {
          if (type === 'h') {
            if (v > this.endTime.h) {
              this.startTime.h = Math.max(this.endTime.h, 0)
              const tts = totalSeconds(this.startTime)
              const etts = totalSeconds(this.endTime)
              if (tts >= etts) {
                this.startTime.m = 0
                this.startTime.s = 0
              }
            } else if (v == this.endTime.h) {
              const tts = totalSeconds(this.startTime)
              const etts = totalSeconds(this.endTime)
              if (tts >= etts) {
                this.startTime.m = 0
                this.startTime.s = 0
              }
            } else {
              if (v < 0) {
                this.startTime.h = 0
              }
            }
          } else if (type === 'm') {
            if (v > this.endTime.m) {
              if (v > 59) {
                this.startTime.m = 59
              }
              if (this.startTime.h >= this.endTime.h) {
                if (this.endTime.s != 0) {
                  this.startTime.s = 0
                  this.startTime.m = this.endTime.m
                } else {
                  this.startTime.m = this.endTime.m - 1
                }
              }
            } else {
              if (v > 59) {
                this.startTime.m = 59
              } else if (v < 0) {
                this.startTime.m = 0
              }
            }
          } else if (type === 's') {
            const tts = totalSeconds(this.startTime)
            const etts = totalSeconds(this.endTime)
            if (tts >= etts) {
              if (v > 59) {
                this.startTime.s = 59
              }
              this.startTime.s = 0
            } else {
              if (v > 59) {
                this.startTime.s = 59
              } else if (v < 0) {
                this.startTime.s = 0
              }
            }
          }
        } else {
          const etts = totalSeconds(this.endTime)
          const etts2 = totalSeconds(this.endTimeOriginal)
          const tts = totalSeconds(this.startTime)
          if (etts >= etts2) {
            this.endTime.h = this.endTimeOriginal.h
            this.endTime.m = this.endTimeOriginal.m
            this.endTime.s = this.endTimeOriginal.s
          } else {
            if (tts >= etts) {
              this.endTime.h = this.endTimeOriginal.h
              this.endTime.m = this.endTimeOriginal.m
              this.endTime.s = this.endTimeOriginal.s
            } else {
              if (type === 'h') {
                if (v < 0) {
                  this.endTime.h = this.endTimeOriginal.h
                }
              }
              if (type === 'm') {
                if (v < 0) {
                  this.endTime.m = this.endTimeOriginal.m
                }
                if (v > 59) {
                  this.endTime.m = 59
                }
              }
              if (type === 's') {
                if (v < 0) {
                  this.endTime.s = this.endTimeOriginal.s
                }
                if (v > 59) {
                  this.endTime.s = 59
                }
              }
            }
          }
        }
      } else {
        if (type2 === 'start') {
          this.startTime[type] = 0
        } else {
          this.endTime[type] = this.endTimeOriginal[type]
        }
      }
    },
    handlePointCommand(e: any, type: string) {
      if (type === 'start') {
        const startSeconds = totalSeconds(e.time)
        const endSeconds = totalSeconds(this.endTime)
        if (startSeconds < endSeconds) {
          this.startTime.h = e.time.h
          this.startTime.m = e.time.m
          this.startTime.s = e.time.s
          return true
        } else {
          // @ts-ignore
          ElMessage.warning("开始时间需要大于结束时间")
          return false
        }
      } else {
        const endSeconds = totalSeconds(e.time)
        const eos = totalSeconds(this.endTimeOriginal)
        const startSeconds = totalSeconds(this.startTime)
        if (endSeconds > startSeconds) {
          this.endTime.h = e.time.h
          this.endTime.m = e.time.m
          this.endTime.s = e.time.s
          return true
        } else {
          // @ts-ignore
          ElMessage.warning("结束时间需要大于开始时间")
          return false
        }
      }
    },
    handleGetContent(ai_agent_id:string,trycount = 0) {
      const url = 'https://ai-agent.gamepp.com/v1/aiagent/get_content';
      const userInfo = gamepp.user.loadBaseInfo.sync()
      const data = {
        "uid":userInfo.uid,
        "language":"CN",
        "ai_agent_id": ai_agent_id,
        baseinfo_id: this.ai_agent.baseinfo_id,
        baseinfo_timestamp: this.ai_agent.baseinfo_timestamp,
      }
      const encodeData = gamepp.user.getEncodeInfo.sync(JSON.stringify(data))
      console.log('encodeData',encodeData)
      if (this.ai_agent.try_count < 10) {
        this.ai_agent.try_count++
        ai_agent_bc.postMessage({action: 'try_count++',content:this.ai_agent.try_count})
        axios.post(url,encodeData).then((res)=>{
          console.log(res)
          if (res.status !== 200) { // 状态码返回不是200就不再请求
            if (trycount === 0) { // 第一次状态码不是200，过一秒再请求一次，防止是网络问题导致的
              setTimeout(()=>{
                this.handleGetContent(ai_agent_id,trycount+1);
              },1000)
            }else{
              this.ai_agent.statusNot200 = true;
              this.ai_agent.isWaiting = false;
              ai_agent_bc.postMessage({action: 'statusNot200',content:1000});
            }
          }else{
            this.ai_agent.statusNot200 = false;
            if (res.data.code == 200) {
              this.ai_agent.isWaiting = false
              this.ai_agent.resultContent = res.data.data.content
              ai_agent_bc.postMessage({action: 'get_content_success',content:res.data.data.content,ai_agent_id})
            } else if (res.data.code > 1000) {
              // 返回的code大于1000就不再请求了
              this.ai_agent.isWaiting = false
              this.ai_agent.resultContent = ""
              this.ai_agent.errMsg = (res.data.msg ?? '')
              this.ai_agent.try_count = 1000
              ai_agent_bc.postMessage({action: 'code>1000',content:1000})
            }
          }
        }).catch((err)=>{
          this.ai_agent.isWaiting = false
          console.log(err)
        })
      }
    },
    handleAddContent () {
      if (this.ai_agent.isWaiting) {
        ElMessage("正在等待报告生成完毕，请稍后再试")
        return
      }
      // @ts-ignore
      if (window.addcontent) clearTimeout(window.addcontent) // 防抖
      // @ts-ignore
      window.addcontent = setTimeout(()=>{
        this.ai_agent.errMsg = '';
        this.ai_agent.isWaiting = true
        this.ai_agent.resultContent = ""
        this.ai_agent.try_count = 0;
        this.ai_agent.ai_agent_id = ""
        const url = 'https://ai-agent.gamepp.com/v1/aiagent/add_content';
        const userInfo = gamepp.user.loadBaseInfo.sync()
        const data = {
          "uid":userInfo.uid,
          "language":"CN",
          content: "",
          baseinfo_id: this.ai_agent.baseinfo_id,
          baseinfo_timestamp: this.ai_agent.baseinfo_timestamp,
        }
        this.getSendStr().then((str)=>{
          if (str !== '') {
            data.content = str
            const encodeData = gamepp.user.getEncodeInfo.sync(JSON.stringify(data))
            console.log('encodeData',encodeData)
            axios.post(url,encodeData).then((res)=>{
              console.log(res)
              if (res.data.code == 200) {
                ai_agent_bc.postMessage({ai_agent_id: res.data.data.ai_agent_id, action: 'ai_agent_id'})
              }else{
                this.ai_agent.isWaiting = false
                ai_agent_bc.postMessage({action: 'add_content_err',msg:"请求错误，错误码："+res.data.code + (res.data.msg ?? '')})
              }
            }).catch((err)=>{
              console.log(err)
            })
          }else{

          }
        })
      },150)
    },
    async getSendStr():Promise<string> {
      // let top20PageFaultDelta = [0,0]; // [min,max]
      let pageFaultDeltaAvg = 0
      // // 算前20的内存分页中断数据
      if (this.powerData.pageFaultDelta.detail.length !== 0 && this.powerData.pageFaultDelta.avg !== 0) {
        const pageFaultDelta = deepClone(this.powerData.pageFaultDelta.detail)
        const sliceLen = Math.floor(pageFaultDelta.length * 0.025)
        pageFaultDeltaAvg = calcAvg(pageFaultDelta.sort((a:any,b:any) => b - a).slice(0,sliceLen))
        console.log(pageFaultDelta,this.powerData.pageFaultDelta.avg,pageFaultDeltaAvg);
        //   top20PageFaultDelta[0] = pageFaultDelta[0]
        //   top20PageFaultDelta[1] = pageFaultDelta[0]
        //
        //   for (let i = 1; i < 20; i++) {
        //     top20PageFaultDelta[0] = Math.min(pageFaultDelta[i], top20PageFaultDelta[0])
        //     top20PageFaultDelta[1] = Math.max(pageFaultDelta[i], top20PageFaultDelta[1])
        //   }
      }
      let str = '以下是游戏加加性能统计中的数据：\n';
      str += '一. 电脑配置\n'
      if (this.dev.PCType === 'Notebook') {
        str+="电脑类型: 笔记本电脑\n"
      }else{
        str+="电脑类型: 台式电脑\n"
      }
      if (this.dev.hwinfo.CPU && this.dev.hwinfo.CPU.SubNode && this.dev.hwinfo.CPU.SubNode[0]) {
        let cpuName = ''
        if (this.dev.hwinfo.CPU.SubNode[0].ProcessorName) {
          cpuName = this.dev.hwinfo.CPU.SubNode[0].ProcessorName
          str+="CPU型号: " + cpuName + "\n"
        }else{
          str+="CPU型号: \n"
        }
        if (cpuName.toLowerCase().includes('amd')) {
          if (this.dev.hwinfo.CPU.SubNode[0].NumberofCPUCores) {
              const NumberofCPUCores = this.dev.hwinfo.CPU.SubNode[0].NumberofCPUCores
              str+="CPU核心数量: " + NumberofCPUCores + "\n"
          }else{
              str+="CPU核心数量: \n"
          }
          if (this.dev.hwinfo.CPU.SubNode[0].NumberofLogicalCPUs) {
              const threadCount = this.dev.hwinfo.CPU.SubNode[0].NumberofLogicalCPUs
              str+="CPU线程数量: " + threadCount + "\n"
          }else{
              str+="CPU线程数量: \n"
          }

          // amd 判断CCD/CCX是不是多个
          if (cpuName.toLowerCase().includes('9950x')) {
            str += 'CPU存在多个CCD。\n'
          } else {
            let CpuComplexInfo:any[] = []
            CpuComplexInfo = gamepp.getCPUComplexInfo.sync();
            if (this.dev.cpuComplexInfo) {
              CpuComplexInfo = this.dev.cpuComplexInfo
            }
            const [isMutiCCD,isMutiCCX] = getAMDIsMutiCCDCCX(CpuComplexInfo)
            if (isMutiCCD) {
              str += 'CPU存在多个CCD'
            } else {
              str += 'CPU只有单个CCD'
            }
            if (isMutiCCX) {
              str += '，存在多个CCX。\n'
            } else {
              str += '。\n'
            }
          }
        }else{
          let tempArr: Array<number> = [];
          let CpuComplexInfo:any[] = []
          CpuComplexInfo = gamepp.getCPUComplexInfo.sync();
          if (this.dev.cpuComplexInfo) {
            CpuComplexInfo = this.dev.cpuComplexInfo
          }
          CpuComplexInfo.forEach((item:any) => {
              item.Cores.forEach((core:any) => {
                  tempArr.push(core.EfficiencyClass);
              })
          })
          tempArr = Array.from(new Set(tempArr)) // 去重
          tempArr.sort((a, b) => b - a)
          peCoreArr = tempArr
          let largeCores: Array<any> = [0,0];// 大核
          let smallCores: Array<any> = [0,0];// 小核
          let littleCores: Array<any> = [0,0];// 小小核
          for (let i = 0; i < CpuComplexInfo.length; i++) {
             let result = this.LargeAndSmallCore(CpuComplexInfo[i])
             largeCores[0] += result.largeCores[0]
             largeCores[1] += result.largeCores[1]
             smallCores[0] += result.smallCores[0]
             smallCores[1] += result.smallCores[1]
             littleCores[0] += result.littleCores[0]
             littleCores[1] += result.littleCores[1]
          }
          if (largeCores[0] !== 0) {
              str+=`性能核心数：${largeCores[0]}，性能核心线程数：${largeCores[1]}\n`
          }
          if (smallCores[0] !== 0) {
              str+=`能效核心数：${smallCores[0]}，能效核心线程数：${smallCores[1]}\n`
          }
          if (littleCores[0] !== 0) {
              str+=`节能核心数：${littleCores[0]}，节能核心线程数：${littleCores[1]}\n`
          }
        }
        if (this.dev.hwinfo.CPU.SubNode[0]['CPUMax.JunctionTemperature(Tj,max)']) {
          const cpuTempMax = this.dev.hwinfo.CPU.SubNode[0]['CPUMax.JunctionTemperature(Tj,max)']
          str+="CPU最高温度限制: " + cpuTempMax + "\n"
        }else{
          str+="CPU最高温度限制: \n"
        }
      }

      let GPUIndex = FilterGPUIndexFromMemoryNumber(this.dev.hwinfo)

      function FilterGPUIndexFromMemoryNumber (JsonInfo:any) {
        let srcIndex = 0
        if (JsonInfo.GPU && JsonInfo.GPU.SubNode) {
          let Gpu_Count = JsonInfo.GPU.SubNode.length
          if (Gpu_Count === 1) { return(0)}
          for (let i = 0; i < JsonInfo.GPU.SubNode.length; i++) {
            const currentNode = JsonInfo.GPU.SubNode[i];
            const currentVideoMemoryNumber = currentNode.VideoMemoryNumber;
            if (currentVideoMemoryNumber > (JsonInfo.GPU.SubNode[srcIndex].VideoMemoryNumber || 0)) {
              srcIndex = i;
            }
          }
        }else{
          return (0)
        }
        return(srcIndex)
      }
      let SensorInfoStr = null
      try {
        SensorInfoStr = gamepp.hardware.getSensorInfo.sync()
      } catch {
      }
      const SensorInfo = JSON.parse(SensorInfoStr);
      if (this.dev.hwinfo.GPU && this.dev.hwinfo.GPU.SubNode && this.dev.hwinfo.GPU.SubNode[GPUIndex]) {
        if (this.dev.hwinfo.GPU.SubNode[GPUIndex].VideoCard) {
          const gpuName = this.dev.hwinfo.GPU.SubNode[GPUIndex].VideoCard.replace(/\(.*?\)/g, '').replace(/\[.*?\]/g, '').trim()
          str+="GPU型号: " + gpuName + "\n"
        }else{
          str+="GPU型号: \n"
        }
        if (this.dev.hwinfo.GPU.SubNode[GPUIndex].VideoMemoryNumber) {
          const gpuMemorySize = gpu_memory_size_type(this.dev.hwinfo.GPU.SubNode[GPUIndex])
          str+="GPU显存容量: " + gpuMemorySize + "\n"
        }else{
          str+="GPU显存容量: \n"
        }
        // GPU Thermal Limit
        if (this.dev.hwinfo.GPU.SubNode[GPUIndex].VideoCard && this.dev.hwinfo.GPU.SubNode[GPUIndex].VideoCard.toLowerCase().includes('nvidia')) {
          const v = findValueByKeyAndType(SensorInfo, 'GPU', 'GPU Thermal Limit', 'temperature')
          if (v[0] !== '') {
            str+="GPU最高温度限制: " + v[1] + '℃' + "\n"
          }
        }
      }

      if (this.dev.hwinfo.MEMORY && this.dev.hwinfo.MEMORY.Property) {
        const CurrentMemoryClock = (Math.ceil(this.dev.hwinfo.MEMORY.Property['CurrentMemoryClock'].split('MHz')[0]))
        str+="内存总容量: " + this.dev.hwinfo.MEMORY.Property.TotalMemorySize + "\n"
        // str+="内存频率: " + getDDRVersion(this.dev.hwinfo.MEMORY.SubNode[0].MemoryType) + ' '+ CurrentMemoryClock * 2 + 'MHz\n';

        // 处理内存是否超频
        let HWInfoXmlStr = window.localStorage.getItem('HWInfoXml')
        if (HWInfoXmlStr) {
          const HWInfoXml:any = JSON.parse(HWInfoXmlStr)
          if (HWInfoXml.COMPUTER.SubNodes.MEMORY.SubNode) {
            let tCKAVGmin = 0
            // 多根内存条
            if (Array.isArray(HWInfoXml.COMPUTER.SubNodes.MEMORY.SubNode)) {
              for (const memory of HWInfoXml.COMPUTER.SubNodes.MEMORY.SubNode) {
                for (const property of memory.Property) {
                  if (property.Entry === "Minimum SDRAM Cycle Time (tCKAVGmin)") {
                    const match = property.Description.match(/\((\d+)\s*MHz\)/);
                    if (match) {
                      if (!tCKAVGmin) tCKAVGmin = parseInt(match[1], 10)
                      else tCKAVGmin = Math.min(tCKAVGmin, parseInt(match[1], 10))
                      break
                    }
                  }
                }
              }
            }else{
              // 单根内存条
              for (const property of HWInfoXml.COMPUTER.SubNodes.MEMORY.SubNode.Property) {
                if (property.Entry === "Minimum SDRAM Cycle Time (tCKAVGmin)") {
                  const match = property.Description.match(/\((\d+)\s*MHz\)/);
                  if (match) {
                    tCKAVGmin = parseInt(match[1], 10)
                    break
                  }
                }
              }
            }

            if (Math.abs(CurrentMemoryClock - tCKAVGmin) < 50) {
              str += '内存当前不处于超频状态'
            }else{
              str += '内存当前处于超频状态'
            }
          }
        }
      }

      if (this.hardwaerinfo.game_resolutions) {
        str += '\n' + '游戏分辨率：' + this.hardwaerinfo.game_resolutions
      }
      let refresh_rate:null|number = null;
      if (this.hardwaerinfo.resolutiop && this.hardwaerinfo.refresh_rate) {
        refresh_rate = Number(this.hardwaerinfo.refresh_rate.replace('Hz',''))
        str += '\n' + '主显示器 分辨率：' + this.hardwaerinfo.resolutiop + ' 刷新率：' + this.hardwaerinfo.refresh_rate.replace('Hz','')
      }

      str += '\n\n'
      str += '二. 一局游戏此电脑的传感器数据，按照5分钟的时间间隔分段：\n\n'

      const gameTime = this.recentGameInfo.endtime - this.recentGameInfo.starttime // 游戏时长 秒
      const totalPoint = this.powerData.fps.detail.length // 总共记录的数据数量
      const pointTime = gameTime / totalPoint // 每个数据点的时间
      // 计算5分钟对应的数据点数量
      const segmentDuration = 300; // 5分钟=300秒
      const pointsPerSegment = Math.ceil(segmentDuration / pointTime);
      // 分段处理数据
      let c = 0;
      for (let i = 0; i < totalPoint; i += pointsPerSegment) {
        if ((i+pointsPerSegment) == totalPoint) {
          // 最后一段
          const time = secondsToMinutes(gameTime)
          str += `${c+1}. ${c*5}至${time}\n`;
        }else if ((i+pointsPerSegment) > totalPoint) {
          break;
        }else{
          str+= `${c+1}. ${c*5}至${(c+1)*5}分钟\n`;
        }
        c++;
        const fps = calcAvg(this.powerData.fps.detail.slice(i, i + pointsPerSegment));
        str+=`平均帧数：${fps}\n`
        str+=`平均帧数：${fps}\n`
        if (refresh_rate) {
          let ma = ''
          if (refresh_rate < fps) {
            ma = '大于'
          }else if (refresh_rate === fps) {
            ma = '等于'
          }else {
            ma = '小于'
          }
          str += `平均帧数${ma}显示器的刷新率\n`
        }
        // const fpslow1 = calcMin(this.powerData.fps1.detail.slice(i, i + pointsPerSegment));
        // str+=`最低1%的帧数: ${fpslow1}\n`
        str+= 'CPU的'
        // 获取当前段的数据
        if (this.powerData.cpuload.detail.length !== 0) {
          const cpuload = calcAvg(this.powerData.cpuload.detail.slice(i, i + pointsPerSegment));
          str+= `占用${cpuload}%，`
        }
        if (this.powerData.cpupower.detail.length !== 0) {
          const cpupower = calcAvg(this.powerData.cpupower.detail.slice(i, i + pointsPerSegment));
          str+= `功耗${cpupower}W，`
        }
        if (this.powerData.cputemperature.detail.length !== 0) {
          const cputemperature = calcAvg(this.powerData.cputemperature.detail.slice(i, i + pointsPerSegment));
          str+= `温度${cputemperature}℃。`
        }
        str+='\n'
        if (this.powerData.cpufan.detail.length !== 0) {
          const cpufan = calcAvg(this.powerData.cpufan.detail.slice(i, i + pointsPerSegment));
          str+= `CPU的散热器风扇转数${cpufan}RPM。\n`
        }
        str += 'GPU的'
        if (this.powerData.gpuload1.detail.length !== 0) {
          const gpuload1 = calcAvg(this.powerData.gpuload1.detail.slice(i, i + pointsPerSegment));
          str+= `占用${gpuload1}%`
        }
        if (this.powerData.gpupower.detail.length !== 0) {
          const gpupower = calcAvg(this.powerData.gpupower.detail.slice(i, i + pointsPerSegment));
          str+= `功耗${gpupower}W，`
        }
        if (this.powerData.gputemperature.detail.length !== 0) {
          const gputemperature = calcAvg(this.powerData.gputemperature.detail.slice(i, i + pointsPerSegment));
          str+= `温度${gputemperature}℃，`
        }
        if (this.powerData.gpumemoryload.detail.length !== 0) {
          const gpumemoryload = calcAvg(this.powerData.gpumemoryload.detail.slice(i, i + pointsPerSegment));
          str+= `显存占用${gpumemoryload}%。`
        }
        str+='\n'
        if (this.powerData.gpufan.detail.length !== 0) {
          const gpufan = calcAvg(this.powerData.gpufan.detail.slice(i, i + pointsPerSegment));
          str+= `GPU的散热器风扇转数${gpufan}RPM。\n`
        }
        str += '内存的'
        if (this.powerData.memory.detail.length !== 0) {
          const memory = calcAvg(this.powerData.memory.detail.slice(i, i + pointsPerSegment));
          str+= `占用${memory}%，`
        }
        if (this.powerData.memorytemperature.detail.length !== 0 && this.powerData.memorytemperature.avg !== 0) {
          const memorytemperature = calcAvg(this.powerData.memorytemperature.detail.slice(i, i + pointsPerSegment));
          str+= `温度${memorytemperature}℃。`
        }else{
          str+= '无温度传感器。'
        }
        if (this.powerData.pageFaultDelta.detail.length !== 0 && this.powerData.pageFaultDelta.avg !== 0 && pageFaultDeltaAvg !== 0) {
          // const arr_pageFaultDelta = deepClone(this.powerData.pageFaultDelta.detail.slice(i, i + pointsPerSegment)).sort((a:any,b:any) => b - a)
          // console.warn(arr_pageFaultDelta,calcAvg(arr_pageFaultDelta))
          // const len = this.powerData.pageFaultDelta.detail.slice(i, i + pointsPerSegment).filter((item:number) => item >= top20PageFaultDelta[0] && item <= top20PageFaultDelta[1]).length
          const len = this.powerData.pageFaultDelta.detail.slice(i, i + pointsPerSegment).filter((item:number) => item >= pageFaultDeltaAvg).length
          if (len > 0) {
            str+= `\n此时间段内出现${len}次大量的内存分页中断数据。`
          }
        }

        str+='\n'

        if (this.powerData.cpuclock && this.powerData.cpuclock.performance) {
          const arr = JSON.parse(JSON.stringify(this.powerData.cpuclock.performance))
          const performances = arr.slice(i, i + pointsPerSegment)
          const obj:any = {}
          for (let j = 0; j < performances.length; j++) {
            const item = performances[j]
            if (item !== '') {
              const _arr = JSON.parse(item)
              for (const o of _arr) {
                for (const key of Object.keys(o)) {
                  if (obj[key]) {
                    obj[key]++
                  }else{
                    obj[key] = 1
                  }
                }
              }
            }
          }
          for (const objKey in obj) {
            const reason = objKey.toLowerCase()
            performance_limit_list.forEach(({tags,desc_index})=>{
              let _bool = true;
              tags.forEach((v: string) => {
                if (!reason.includes(v)) {
                  _bool = false
                }
              })
              if (_bool) {
                str+=`触发${obj[objKey]}次${objKey}，此事件表示"${desc_list[desc_index]}"。\n`
              }
            })
          }
        }

        str += '\n\n'
      }

      return str;
    },
    getSendStrAmdRyzenAI () {
      let str = '以下是游戏加加性能统计中的数据：\n';
      str += '一. 电脑配置\n'
      if (this.dev.PCType === 'Notebook') {
        str+="电脑类型: 笔记本电脑\n"
      }else{
        str+="电脑类型: 台式电脑\n"
      }
      if (this.dev.hwinfo.CPU && this.dev.hwinfo.CPU.SubNode && this.dev.hwinfo.CPU.SubNode[0]) {
        let cpuName = ''
        if (this.dev.hwinfo.CPU.SubNode[0].ProcessorName) {
          cpuName = this.dev.hwinfo.CPU.SubNode[0].ProcessorName
          str+="CPU型号: " + cpuName + "\n"
        }else{
          str+="CPU型号: \n"
        }
        if (this.dev.hwinfo.CPU.SubNode[0].NumberofCPUCores) {
          const NumberofCPUCores = this.dev.hwinfo.CPU.SubNode[0].NumberofCPUCores
          str+="CPU核心数量: " + NumberofCPUCores + "\n"
        }else{
          str+="CPU核心数量: \n"
        }
        if (this.dev.hwinfo.CPU.SubNode[0].NumberofLogicalCPUs) {
          const threadCount = this.dev.hwinfo.CPU.SubNode[0].NumberofLogicalCPUs
          str+="CPU线程数量: " + threadCount + "\n"
        }else{
          str+="CPU线程数量: \n"
        }
        if (this.dev.hwinfo.CPU.SubNode[0]['CPUMax.JunctionTemperature(Tj,max)']) {
          const cpuTempMax = this.dev.hwinfo.CPU.SubNode[0]['CPUMax.JunctionTemperature(Tj,max)']
          str+="CPU最高温度限制: " + cpuTempMax + "\n"
        }else{
          str+="CPU最高温度限制: \n"
        }
      }
      let GPUIndex = FilterGPUIndexFromMemoryNumber(this.dev.hwinfo)

      function FilterGPUIndexFromMemoryNumber (JsonInfo:any) {
        let srcIndex = 0
        if (JsonInfo.GPU && JsonInfo.GPU.SubNode) {
          let Gpu_Count = JsonInfo.GPU.SubNode.length
          if (Gpu_Count === 1) { return(0)}
          for (let i = 0; i < JsonInfo.GPU.SubNode.length; i++) {
            const currentNode = JsonInfo.GPU.SubNode[i];
            const currentVideoMemoryNumber = currentNode.VideoMemoryNumber;
            if (currentVideoMemoryNumber > (JsonInfo.GPU.SubNode[srcIndex].VideoMemoryNumber || 0)) {
              srcIndex = i;
            }
          }
        }else{
          return (0)
        }
        return(srcIndex)
      }
      let SensorInfoStr = null
      try {
        SensorInfoStr = gamepp.hardware.getSensorInfo.sync()
      } catch {
      }
      const SensorInfo = JSON.parse(SensorInfoStr);
      if (this.dev.hwinfo.GPU && this.dev.hwinfo.GPU.SubNode && this.dev.hwinfo.GPU.SubNode[GPUIndex]) {
        if (this.dev.hwinfo.GPU.SubNode[GPUIndex].VideoCard) {
          const gpuName = this.dev.hwinfo.GPU.SubNode[GPUIndex].VideoCard.replace(/\(.*?\)/g, '').replace(/\[.*?\]/g, '').trim()
          str+="GPU型号: " + gpuName + "\n"
        }else{
          str+="GPU型号: \n"
        }
        if (this.dev.hwinfo.GPU.SubNode[GPUIndex].VideoMemoryNumber) {
          const gpuMemorySize = gpu_memory_size_type(this.dev.hwinfo.GPU.SubNode[GPUIndex])
          str+="GPU显存容量: " + gpuMemorySize + "\n"
        }else{
          str+="GPU显存容量: \n"
        }
        // GPU Thermal Limit
        if (this.dev.hwinfo.GPU.SubNode[GPUIndex].VideoCard && this.dev.hwinfo.GPU.SubNode[GPUIndex].VideoCard.toLowerCase().includes('nvidia')) {
          const v = findValueByKeyAndType(SensorInfo, 'GPU', 'GPU Thermal Limit', 'temperature')
          if (v[0] !== '') {
            str+="GPU最高温度限制: " + v[1] + '℃' + "\n"
          }
        }
      }
      if (this.dev.hwinfo.MEMORY && this.dev.hwinfo.MEMORY.Property) {
        str+="内存总容量: " + this.dev.hwinfo.MEMORY.Property.TotalMemorySize + "\n"
      }
      str += '\n二. 游戏全时段数据\n'
      const avgFps = this.powerData.fps.avg;
      const cpuAvgUsage = this.powerData.cpuload.avg;
      const cpuAvgTemp = this.powerData.cputemperature.avg;
      const gpuAvgUsage = this.powerData.gpuload1.avg;
      const gpuAvgTemp = this.powerData.gputemperature.avg;
      const gpuAvgMem = this.powerData.gpumemoryload.avg;
      const memAvgUsage = this.powerData.memory.avg;
      str += `游戏平均帧数${avgFps}，`
      if (avgFps >= 60) {
        str += '大于等于60。\n'
      }else{
        str += '小于60。\n'
      }
      str += `CPU平均占用${cpuAvgUsage}%，温度${cpuAvgTemp}℃。\n`
      str += `内存平均占用${memAvgUsage}%。\n`
      str += `GPU平均占用${gpuAvgUsage}%，温度${gpuAvgTemp}℃，显存占用${gpuAvgMem}%。\n`
      if (this.powerData.cpuclock && this.powerData.cpuclock.performance) {
        let arr:any[] = [];
        const performances = JSON.parse(JSON.stringify(this.powerData.cpuclock.performance))
        const obj:any = {}
        for (let j = 0; j < performances.length; j++) {
          const item = performances[j]
          if (item !== '') {
            const _arr = JSON.parse(item)
            for (const o of _arr) {
              for (const key of Object.keys(o)) {
                if (obj[key]) {
                  obj[key]++
                }else{
                  obj[key] = 1
                }
              }
            }
          }
        }
        for (const objKey in obj) {
          const reason = objKey.toLowerCase()
          for (let i = 0; i < performance_limit_list.length; i++) {
            const {tags,desc_index} = performance_limit_list[i]
            let _bool = true;
            tags.forEach((v: string) => {
              if (!reason.includes(v)) {
                _bool = false
              }
            })
            if (_bool) {
              arr.push({
                count: obj[objKey],
                k: objKey,
                v: desc_list[desc_index]
              })
            }
          }
          performance_limit_list.forEach()
        }
        arr = arr.sort((a,b)=> {
          return b.count - a.count
        })
        for (let i = 0; i < 3; i++) {
          if (arr[i]) {
            str += `出现 ${arr[i].count}次 "${arr[i].k}"事件，此事件表示"${arr[i].v}"。\n`
          }
        }
      }
      console.log(str)
      return str;
    },
    LargeAndSmallCore (ComplexInfo:any){
      let largeCores: Array<any> = [0,0];// 大核
      let smallCores: Array<any> = [0,0];// 小核
      let littleCores: Array<any> = [0,0];// 小小核
      ComplexInfo.Cores.forEach((core:any, innerIndex:any) => {
          if (core.EfficiencyClass === peCoreArr[0]) {
              largeCores[0]++;
              largeCores[1] += core.LogicalCores.length;
          } else if (peCoreArr.length >=2 && core.EfficiencyClass === peCoreArr[1]) {
            smallCores[0]++;
            smallCores[1] += core.LogicalCores.length;
          } else if (peCoreArr.length >=3 && core.EfficiencyClass === peCoreArr[2]) {
            littleCores[0]++;
            littleCores[1] += core.LogicalCores.length;
          }
      })
      return {
          largeCores,smallCores,littleCores
      }
    },
    downloadAmdRyzenAIModel (modelIndex:number) {
      this.ai_agent.downloadDialogShow = false;
      this.downloadGppaiagent_ryzenai().then(res=>{
        if (res) {
          this.ai_agent.downloading = true;
          this.ai_agent.netError = false;
          gamepp.aiagent.runAIAgentClient.sync(0);
          this.sendAIAgentMsg({"type":"DownloadLLM", "source_type": modelIndex});
        }else{
          this.ai_agent.netError = true;
        }
      })
    },
    async downloadGppaiagent_ryzenai () {
      const packageName = 'gppaiagent_ryzenai'
      const moduleExists = await gamepp.package.isexists.promise(packageName);
      if (moduleExists) {
        return true;
      }else{
        const _timer = setInterval(()=>{
          this.ai_agent.downloadExeProgress.percent += 1;
          if (this.ai_agent.downloadExeProgress.percent >= 99) {
            this.ai_agent.downloadExeProgress.percent = 99;
            clearInterval(_timer)
          }
        },16)
        this.ai_agent.downloadingExe = true;
        const checkUpdate = await gamepp.package.checkupdate.promise(packageName);
        console.warn(checkUpdate);
        if (checkUpdate.result) {
          const result = await gamepp.package.startdownload.promise(packageName, checkUpdate.url, checkUpdate.md5, checkUpdate.version);
          this.ai_agent.downloadingExe = false;
          clearInterval(_timer)
          console.log(result)
          if (result) { //下载完成
            return true;
          } else {
            return false;
          }
        } else {
          this.ai_agent.downloadingExe = false;
          clearInterval(_timer)
          return false;
        }
      }
    },
    async sendAIAgentMsg (obj:any) {
      await gamepp.aiagent.sendMessage.promise(JSON.stringify(obj));
    },
  },
})
export function isNumber(val: any) {
  return !isNaN(val) && !isNaN(parseFloat(val))
}

export const dropdown_menu = [
  {name: 'fps', value: 'FPS'},
  {name: 'frametime', value: 'GameRebound.FrameGenerationTime'},
  {name: 'fps1', value: 'FPS 1% low'},
  {name: 'fps01', value: 'FPS 0.1% Low'},
  {name: 'cpuload', value: 'GameRebound.ProcessorOccupancy'},
  {name: 'cpuloadP', value: 'GameRebound.ProcessorOccupancy',value2:"P"},
  {name: 'cpuloadE', value: 'GameRebound.ProcessorOccupancy',value2:"E"},
  {name: 'cpuclock', value: 'GameRebound.ProcessorFrequency'},
  {name: 'cpuclockP', value: 'GameRebound.ProcessorFrequency',value2:"P"},
  {name: 'cpuclockE', value: 'GameRebound.ProcessorFrequency',value2:"E"},
  {name: 'cputemperature', value: 'GameRebound.ProcessorTemperature'},
  {name: 'cpupower', value: 'GameRebound.ProcessorHeatPower'},
  {name: 'gpuload', value: 'GameRebound.GraphicsCardOccupancy'},
  {name: 'gpuload1', value: 'GameRebound.GraphicsCardOccupancyTotal'},
  {name: 'gpuclock', value: 'GameRebound.GraphicsCardFrequency'},
  {name: 'gputemperature', value: 'GameRebound.GraphicsCardTemperature'},
  {name: 'gpuhotspottemp', value: 'GameRebound.GraphicsCardCoreTemperature'},
  {name: 'gpupower', value: 'GameRebound.GraphicsCardHeatPower'},
  {name: 'gpumemoryload', value: 'hardwareInfo.VRAM'},
  {name: 'gpumemorytemp', value: 'GameRebound.GraphicsCardMemoryTemperature'},
  {name: 'gpumemoryclock', value: 'hardwareInfo.VRAMFrequency'},
  {name: 'memory', value: 'GameRebound.MemoryOccupancy'},
  {name: 'memorytemperature', value: 'GameRebound.MemoryTemperature'},
  {name: 'pageFaultDelta', value: 'GameRebound.MemoryPageFaults'},
]


function gpu_memory_size_type(data: any) {
  const VideoMemory = (data.VideoMemory).split(' ')
  let n = 1024
  if (data.VideoMemory.includes('MBytes')) {
    n = 1024
  } else if (data.VideoMemory.includes('KBytes')) {
    n = 1024*1024
  } else if (data.VideoMemory.includes('GBytes')) {
    n = 1
  }
  return Math.ceil(((VideoMemory[0] / n))) + 'GB'
}
const desc_list = [
  '当前CPU核心温度达到Tj,Max最大频率受到限制',
  'CPU达到临界温度，CPU频率可能会低于基础频率',
  'CPU封装/环形总线达到Tj,Max温度，总线最大频率受到限制',
  'CPU封装/环形总线达到临界温度，总线最大频率受到限制',
  'CPU达到临界温度，触发后CPU会降低运行频率以降低温度，防止硬件故障',
  'CPU温度达到Tj,Max，触发后CPU会降低运行频率以降低温度，防止硬件故障',
  'CPU最大频率因主板供电过热而受到限制',
  'CPU最大频率因主板供电达到当前温度下最大电流而受到限制',
  'CPU温度达到Tj,Max，触发后CPU会降低运行频率以降低温度，防止硬件故障',
  'CPU最大频率因主板供电达到当前温度下最大电流而受到限制',
  'CPU达到Tj,Max温度，触发后CPU会降低运行频率以降低温度，防止硬件故障',
  'CPU达到临界温度，触发后CPU会降低运行频率以降低温度，防止硬件故障',
  'CPU最大频率因主板供电过热而受到限制',
  '当前CPU核心达到最大功耗限制最大频率受到限制',
  'CPU封装/环形总线达到功耗限制，总线最大频率受到限制',
  'CPU当前最大频率受到电气设计限制（含最大电流限制、瞬时功耗限制、最大请求电压限制等)',
  'CPU当前最大频率受到长时功耗限制',
  'CPU当前最大频率受到短时功耗限制',
  'CPU睿频频率限制',
  'CPU当前最大频率受到长时功耗限制',
  'CPU当前最大频率受到最大电流限制',
]
const performance_limit_list = ([
  // {"name":"Core Thermal Throttling","value":0,"tags":["core","thermal","throttling"],desc_index:0},
  {"name":"Core Critical Temperature","value":0,"tags":["core","critical","temperature"],desc_index:1},
  {"name":"Package/Ring Thermal Throttling","value":0,"tags":["package/ring","thermal","throttling"],desc_index:2},
  {"name":"Package/Ring Critical Temperature","value":0,"tags":["package/ring","critical","temperature"],desc_index:3},
  {"name":"IA：PROCHOT","value":0,"tags":["ia","prochot"],desc_index:4},
  {"name":"IA：Thermal Event","value":0,"tags":["ia","thermal","event"],desc_index:5},
  {"name":"IA: VR Thermal Alert","value":0,"tags":["ia","vr","thermal","alert"],desc_index:6},
  {"name":"IA: VR TDC","value":0,"tags":["ia","vr","tdc"],desc_index:7},
  {"name":"Thermal Limit","value":0,"tags":["thermal","limit"],desc_index:8},
  {"name":"CPU TDC Limit","value":0,"tags":["cpu","tdc","limit"],desc_index:9},
  {"name":"Thermal Throttling HTC","value":0,"tags":["thermal","throttling","htc"],desc_index:10},
  {"name":"PROCHOT CPU","value":0,"tags":["prochot","cpu"],desc_index:11},
  {"name":"PROCHOT EXT","value":0,"tags":["prochot","ext"],desc_index:12},
  //{"name":"Core Power Limit Exceeded","value":0,"tags":["core","power","limit","exceeded"],desc_index:13},
  {"name":"Package/Ring Power Limit Exceeded","value":0,"tags":["package/ring","power","limit","exceeded"],desc_index:14},
  {"name":"IA: Electrical Design Point/Other (ICCmax,PL4,SVID,DDR RAPL)","value":0,"name2":"IA: Electrical Design Point/Other","tags":["ia","electrical","design","point/other"],desc_index:15},
  {"name":"IA: Package-Level RAPL/PBM PL1","value":0,"tags":["ia","package-level","rapl/pbm","pl1"],desc_index:16},
  {"name":"IA: Package-Level RAPL/PBM PL2,PL3","value":0,"tags":["ia","package-level","rapl/pbm","pl2,pl3"],desc_index:17},
  {"name":"IA: Turbo Attenuation (MCT)","value":0,"tags":["ia","turbo","attenuation","(mct)"],desc_index:18},
  {"name":"CPU PPT Limit","value":0,"tags":["cpu","ppt","limit"],desc_index:19},
  {"name":"CPU EDC Limit","value":0,"tags":["cpu","edc","limit"],desc_index:20}
])

function getDDRVersion(memoryString:string) {
  // 正则表达式匹配 DDR 后面跟着数字（1-9）
  const regex = /DDR(\d+)/i;
  const match = memoryString.match(regex);

  // 如果匹配成功，返回数字部分，否则返回 null
  return match ? 'DDR'+parseInt(match[1], 10) : '';
}

function secondsToMinutes(seconds:number) {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}分钟${secs < 10 ? '0' : ''}${secs}秒`;
}

function calcMin(arr: number[]) {
  let sum = arr[0]
  for (let i = 0; i < arr.length; i++) {
    sum = Math.min(sum, arr[i])
  }
  return sum
}

function calcAvg(arr: number[]) {
  let sum = 0
  for (let i = 0; i < arr.length; i++) {
    sum += arr[i]
  }
  return Math.floor(sum / arr.length)
}

function findValueByKeyAndType (data:Object, key:string, keySubstring:any = false, type:any) {
  let ToFixed = 0
  if (type.includes('voltage')) {
    ToFixed = 3
  }
  let key1:any = false, key2:any = false;
  if (key === 'Mainboard') {
    const sift = JSON.parse(JSON.stringify(data)) //深拷贝
    const keywords = ['CPU', 'GPU', 'Memory', 'S.M.A.R.T', 'System:', 'Windows Hardware', 'Drive', 'Network', 'DIMM']
    const filteredData = {}
    for (const key1 in sift) {
      let shouldKeep = true
      for (const keyword of keywords) {
        if (data[key1] === null || key1.toLowerCase().includes(keyword.toLowerCase())) {
          shouldKeep = false
          break
        }
      }
      if (shouldKeep) {
        filteredData[key1] = sift[key1]
      }
    }
    data = filteredData
    key = Object.keys(filteredData)[0]
    key1 = Object.keys(filteredData)[1]
    key2 = Object.keys(filteredData)[2]
  } else if (key === 'Network') {
    let totalDLRate = 0, totalUPRate = 0;
    for (const key1 in data) {
      if (key1.startsWith("Network")) {
        const networkData = data[key1] || [];
        networkData.forEach(item => {
          if (item["Current DL rate"]) {
            totalDLRate += parseFloat(item["Current DL rate"].value);
          }
          if (item["Current UP rate"]) {
            totalUPRate += parseFloat(item["Current UP rate"].value);
          }
        });
      }
    }
    return [Number(totalDLRate.toFixed(0)), Number(totalUPRate.toFixed(0))];
  }
  //除开主板与NetWork都在此处进行查找
  for (const [parentKey, arr] of Object.entries(data)) {
    // console.warn('ArrArrArr',arr);
    if (data[parentKey] && (parentKey.includes(key) || (key1 && parentKey.includes(key1))|| (key2 && parentKey.includes(key2)))) {
      for (const obj of arr) {
        const [itemKey, itemValue] = Object.entries(obj)[0] as [any,any]
        // const [itemValue,itemKey] = Object.entries(obj)[0]
        if (keySubstring) {
          if (keySubstring.includes('|')) {
            const substrings = keySubstring.split('|');
            for (const substring of substrings) {
              if (itemKey.includes(substring) && type === (itemValue.type)) {
                return [itemKey, Number(parseFloat(itemValue.value).toFixed(ToFixed))];
              }
            }
          } else {
            if (itemKey.includes(keySubstring) && type.includes(itemValue.type)) {
              return [itemKey, Number(parseFloat((itemValue.value)).toFixed(ToFixed))]
            }
          }
        } else {
          if (type.includes(itemValue.type)) {
            return [itemKey, Number(parseFloat((itemValue.value)).toFixed(ToFixed))]
          }
        }
      }
    }
  }
  return ['', '']
}
function deepClone(params:any) {
  return JSON.parse(JSON.stringify(params))
}

function getAMDIsMutiCCDCCX(CpuComplexInfo) {
  const obj = {}
  let maxDieNum = 0;
  let isMutiCCX = false;
  let isMutiCCD = false;
  for (let i = 0; i < CpuComplexInfo.length; i++) {
    const {ComplexId,Cores} = CpuComplexInfo[i];
    Cores.forEach(({Die})=>{
      console.log(Die)
      if (!obj.hasOwnProperty('die'+Die)) obj['die'+Die] = new Set();
      obj['die'+Die].add(ComplexId)
      maxDieNum = Math.max(Die,maxDieNum)
    })
  }
  Object.values(obj).forEach((oSet: Set)=>{
    if (oSet.size >= 2) isMutiCCX = true
  })

  if (maxDieNum + 1 >= 2) isMutiCCD = true
  return [isMutiCCD,isMutiCCX]
}
