<script setup lang="ts">
import {onMounted, ref, watch, computed} from "vue";
import {gameppBaseSetting, hardware} from '../stores'
import {useWaterFall} from '@/modules/Game_Home/hooks/waterFall'
import HardWareAll from "@/modules/Game_Home/components/hardware/HardWareAll.vue";
import HardWareCPU from "@/modules/Game_Home/components/hardware/HardWareCPU.vue";
import HardWareGPU from "@/modules/Game_Home/components/hardware/HardWareGPU.vue";
import HardWareMemory from "@/modules/Game_Home/components/hardware/HardWareMemory.vue";
import HardWareDisk from "@/modules/Game_Home/components/hardware/HardWareDisk.vue";
import HardWareError from "@/modules/Game_Home/components/hardware/HardWareError.vue";
import HardWareSensorError from "@/modules/Game_Home/components/hardware/HardWareSensorError.vue";
import { useZoomStore } from '../stores/zoomStore';
import {GPP_SendStatics} from "@/uitls/sendstatics"
import {useI18n} from "vue-i18n";
// @ts-ignore
const gamepp = window.gamepp as any
const $gamepp = gameppBaseSetting()
const hardwareStore = hardware()
const zoomStore = useZoomStore();
console.warn(hardwareStore)
let loading = ref(false)
let loading_count = 0
const {t} = useI18n()

let waterArr = ref([
  {height: 730, top: 0, left: 0},
  {height: 425, top: 0, left: 0},
  {height: 750, top: 0, left: 0},
  {height: 340, top: 0, left: 0},
  {height: 488, top: 0, left: 0},
])
const {menuInfo, waterFall} = useWaterFall(waterArr.value)

let noHwData = ref(false) // 没有硬件信息 这个值为true时显示错误页面，为false时正常显示

onMounted(()=>{
  loadingFn()
  checkNoHwData()
})
const changeMenuInfo = (index: number, height: number) => {
  const tempObj = menuInfo.value[index]
  tempObj.height = height
  menuInfo.value[index] = tempObj
}

const checkNoHwData = () => { // 检查之前是不是loading过了
  let sessionData = window.sessionStorage.getItem('noHwData')
  if (sessionData) {
    sessionData = JSON.parse(sessionData);
    if (hardwareStore.HwInfo.CPU) {
      noHwData.value = false
      loading.value = false
    }else{
      loading.value = false
      noHwData.value = true
    }
  }else{
    loading.value = true
  }
}

const loadingFn = () => {
  loading_count++
  if (loading_count > 60) { // 60秒后还没拿到传感器和硬件信息
    window.sessionStorage.setItem('noHwData',JSON.stringify(true))
    loading.value = false
    noHwData.value = true
    return
  }
  if (hardwareStore.HwInfo.CPU) {
    window.sessionStorage.setItem('noHwData',JSON.stringify(false))
    noHwData.value = false
    loading.value = false
  }else{
    setTimeout(()=>{loadingFn()},1000)
  }
}

//截图(选择保存路径)
function GPP_ScreenShot(FileName:string) {
  GPP_SendStatics(100403)
  gamepp.dialog.showSaveDialog.async((value:any) => showSaveDialogCallback(value[0]), {
    title: "GamePP", defaultPath: FileName, filters: [{name: 'Jpeg Picture', extensions: ['*.jpg']}]
  });
}
let bounds = {width: 1450,height: 820}
//截图回调
function showSaveDialogCallback(Path:any) {
  console.log(Path);
  if (Path['canceled']) {
    let getTitle = gamepp.webapp.windows.getTitle.sync()
    if (getTitle.includes('截图窗口')) {
      gamepp.webapp.windows.close.sync('rebound_details_v3_screenshot')
    }
    // AlertLayuiMsg(eval(Languagevalue)['CancelScreenshot']);
  } else {
    var regex = /^[a-zA-Z]:(([a-zA-Z]*)||([a-zA-Z]*\\))*/;
    var array = regex.exec(Path['filePath']);
    if (array != null) {
      bounds = gamepp.webapp.windows.getBounds.sync()
      const el = document.querySelector('.hardware-content');
      if (el) {
        let contentHeight = el.scrollHeight
        if (contentHeight < 700) {
          contentHeight = 1700
        }else{
          contentHeight += 100
        }
        gamepp.webapp.windows.resize.sync('desktop',1450,contentHeight)
      }else{
        gamepp.webapp.windows.resize.sync('desktop',1450,1700)
      }
      gamepp.webapp.windows.capturePage.async((value:any) => CapturePageCallback(value,Path['filePath']), '', Path['filePath'], 'jpg');
    } else {
      ElMessage.error(t('hardwareInfo.error'));
    }
  }
}

//保存截图回调
function CapturePageCallback(value:any, filePath:any) {
  gamepp.webapp.windows.resize.sync('desktop',bounds.width,bounds.height)
  if (value) {
    try {
      let image = gamepp.nativeImage.createFromPath.sync(filePath);
      gamepp.clipboard.writeImage.sync(image);
    }catch (e) {
      console.log('gamepp.nativeImage.createFromPath',e)
    }
    ElMessage.success(t('hardwareInfo.screenshotSuccess'));
    if (filePath.includes('GamePP-性能分析')) {
      gamepp.webapp.windows.close.sync('rebound_details_v3_screenshot')
    }
  }
}

async function HDRescan() {
  GPP_SendStatics(100402)
  await gamepp.hardware.reloadMonitorProcess.promise();
  window.localStorage.removeItem('HwIsScanned')
  window.localStorage.removeItem('LocalHwInfoJsonStr')
  //通知bg重新获取解析xml
  let Obj:any = {};
  Obj['action'] = 'reloadXMLInfo';
  await gamepp.webapp.sendInternalAppEvent.promise('background', Obj);
  loading.value = true
  setTimeout(()=>{
    hardwareStore.getBaseHardwareInfo()
    loading.value = false
  },6000)
}
function GPP_WindowOpen(name:string) {
  try{
    if (name === 'hardware_setupsensor') {
      GPP_SendStatics(100404)
    }
    gamepp.webapp.windows.show.promise(name)
  }catch (e) {
    console.warn(e)
  }
}
/**
 * 外部浏览器打开URL
 * @param url
 */
function GPP_OpenURL(url:string) {
  if (url === 'https://www.hwinfo.com/') {
    GPP_SendStatics(100401)
  }
  try {
    gamepp.shell.openExternal(url);
  } catch (error) {
    window.open(url)
  }
}
const hardwareStyle = computed(() => {
  return {
    height:  `${($gamepp.gameppHeight - (50*zoomStore.zoomLevel)) / zoomStore.zoomLevel}px`
  }
})
const hardwareMessageStyle = computed(() => {
  return {
    width: `${($gamepp.gameppWidth - (180*zoomStore.zoomLevel)) / zoomStore.zoomLevel}px`
  }
})

const hardwareContentStyle = computed(() => {
  let h = $gamepp.gameppHeight
  if (window.innerHeight) {
    h = window.innerHeight - 20
  }
  return {
    height: `${(h - (90*zoomStore.zoomLevel)) / zoomStore.zoomLevel}px`,
  }
})
</script>
<script lang="ts">
export default {
  name: 'HardWare'
}
</script>
<template>
  <div id="hardware-outside" class="hardware-outside" v-loading="loading"  :element-loading-text="$t('hardwareInfo.LoadingHardwareInfo')">
    <div class="hardware-message" :style="hardwareMessageStyle" v-show="!loading">
      <h1>{{ $t('home.hardwareInfo') }}</h1>

      <img class="icon-hwinfo" src="../assets/logo-hwinfo.png" alt="" @click="GPP_OpenURL('https://www.hwinfo.com/')">

<!--      <span class="hwinfo" @click="GPP_OpenURL('https://www.hwinfo.com/')">HWINFO</span>-->

      <span>{{ $t('hardwareInfo.ScanTime').replace(':','') }}: </span>

      <span class="scan-time">{{ hardwareStore.scanTime }}</span>

      <el-tooltip effect="dark" placement="bottom" :content="$t('hardwareInfo.Rescan')" popper-class="custom_tooltip">
        <el-button style="width: 30px;height: 30px;" color="#2D2E39" @click="HDRescan">
          <span class="iconfont icon-ic_Scan iconfont2"></span>
        </el-button>
      </el-tooltip>
      <el-tooltip effect="dark" placement="bottom" :content="$t('hardwareInfo.Screenshot')" popper-class="custom_tooltip">
        <el-button style="width: 30px;height: 30px;" color="#2D2E39" @click="GPP_ScreenShot(`GamePP-${$t('home.hardwareInfo')}.jpg`)">
          <span class="iconfont icon-ic_Screenshot iconfont2"></span>
        </el-button>
      </el-tooltip>
      <el-tooltip effect="dark" placement="bottom" :content="$t('Setting.setSensor')" popper-class="custom_tooltip">
        <el-button style="width: 30px;height: 30px;" color="#2D2E39" @click="GPP_WindowOpen('hardware_setupsensor')" class="iconfont2Wrap">
          <span class="iconfont icon-ic_manage iconfont2" style="margin-left: 3px;"></span>
        </el-button>
      </el-tooltip>
    </div>

    <div class="hardware-content scroll"
         v-show="!loading"
         :style="hardwareContentStyle">
      <div v-if="!noHwData" :style="{height:menuInfo[0].height+'px',top:menuInfo[0].top+'px',left:menuInfo[0].left+'px'}">
        <hard-ware-all :changeMenuInfo="changeMenuInfo"></hard-ware-all>
      </div>
      <div v-if="!noHwData" :style="{height:menuInfo[1].height+'px',top:menuInfo[1].top+'px',left:menuInfo[1].left+'px'}">
        <hard-ware-c-p-u :changeMenuInfo="changeMenuInfo"></hard-ware-c-p-u>
      </div>
      <div v-if="!noHwData" :style="{height:menuInfo[2].height+'px',top:menuInfo[2].top+'px',left:menuInfo[2].left+'px'}">
        <hard-ware-g-p-u :changeMenuInfo="changeMenuInfo"></hard-ware-g-p-u>
      </div>
      <div v-if="!noHwData" :style="{height:menuInfo[3].height+'px',top:menuInfo[3].top+'px',left:menuInfo[3].left+'px'}">
        <hard-ware-memory :changeMenuInfo="changeMenuInfo"></hard-ware-memory>
      </div>
      <div v-if="!noHwData" :style="{height:menuInfo[4].height+'px',top:menuInfo[4].top+'px',left:menuInfo[4].left+'px'}">
        <hard-ware-disk :changeMenuInfo="changeMenuInfo"></hard-ware-disk>
      </div>
      <div v-if="noHwData" :style="{height:menuInfo[0].height+'px',top:menuInfo[0].top+'px',left:menuInfo[0].left+'px'}">
        <hard-ware-error :changeMenuInfo="changeMenuInfo"></hard-ware-error>
      </div>
      <div v-if="noHwData" :style="{height:menuInfo[1].height+'px',top:menuInfo[1].top+'px',left:menuInfo[1].left+'px'}">
        <hard-ware-sensor-error :changeMenuInfo="changeMenuInfo"></hard-ware-sensor-error>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
#screenshotCanvas {
  position: absolute;
  top: -9999px;
  left: -9999px;
  z-index: -1;
}
.iconfont2 {
  font-size: 14px;
  color: #666;
}
.hardware-outside {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 10px;
  position: relative;

  .hardware-message {
    height: 40px;
    border-radius: 4px;
    // margin-top: 20px;
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #ffffff;

    //:deep(.el-button:hover) {
    //  background-color: #3e4050 !important;
    //}

    h1 {
      font-size: 16px;
      color: var(--hardware-color-primary);
      margin-left: 15px;
    }

    .icon-hwinfo {
      //width: 15px;
      height: 18px;
      margin-left: auto;
      cursor: pointer;
      margin-right: 30px;
    }

    .hwinfo {
      margin-right: 26px;
      margin-left: 8px;
      cursor: pointer;
    }

    .scan-time {
      margin-left: 10px;
      margin-right: 10px;
    }
  }

  .hardware-content {
    position: relative;
    width: 100%;
    overflow-y: auto;

    & > div {
      width: 600px;
      position: absolute;
      transition: left 0.4s linear, top 0.4s linear;
    }
  }
}
</style>
<style lang="scss">
:root {
  --hardware-color-primary: #3579d5;
}
.custom_tooltip{
    background: #343647!important;
    border: 1px solid #777777!important;
}
.custom_tooltip  .el-popper__arrow:before{
  background: #343647!important;
  border: 1px solid #777777!important;
}

.hardware-outside .hardware-message .el-button:hover {
  background-color: #3579d5;
  border: transparent;
}
.el-button {
  &:hover {
    .iconfont2{
      color: #ffffff;
    }
  }
}
.hardware-outside {
  .el-loading-mask {
    background-color: rgba(28, 28, 34, 0);
    position: fixed;
    left: 160px;
    top: 35px;
  }
  .el-loading-spinner .path {
    stroke: var(--hardware-color-primary);
  }
}

.hardware-outside {
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  .ml-auto {
    margin-left: auto;
  }

  .flex-items-center {
    display: flex;
    align-items: center;
  }

  .el-collapse-item.as {
    --el-collapse-header-height: 70px;
  }

  .el-collapse {
    --el-collapse-border-color: transparent !important;
    --el-collapse-header-bg-color: transparent !important;
    --el-collapse-content-bg-color: transparent !important;
    --el-collapse-header-text-color: #fff;
    --el-collapse-content-text-color: #fff;
    --el-collapse-header-font-size: 12px;
    --el-collapse-header-height: 24px;

    .el-collapse-item__header svg {
      color: #777777;
    }
  }

  .el-collapse-item__content {
    padding-bottom: 5px;
  }
}

.hardware-content {
  &.scroll::-webkit-scrollbar {
    width: 5px;
    transition: 0.25s;
  }

  &.scroll::-webkit-scrollbar-thumb {
    background: #71738C;
    border-radius: 3px;
  }

  &.scroll::-webkit-scrollbar-thumb:hover {
    background-color: #71738C;
  }

  &.scroll::-webkit-scrollbar-track {
    background-color: #2D2E39;
    width: 2px;
  }

  .scroll::-webkit-scrollbar {
    width: 5px;
    transition: 0.25s;
  }

  .scroll::-webkit-scrollbar-thumb {
    background: #71738C;
    border-radius: 3px;
  }

  .scroll::-webkit-scrollbar-thumb:hover {
    background-color: #71738C;
  }

  .scroll::-webkit-scrollbar-track {
    background-color: #2D2E39;
    width: 2px;
  }
  .el-icon.el-collapse-item__arrow{
    transform: rotate(90deg)
  }
  .el-icon.el-collapse-item__arrow.is-active {
    transform: rotate(270deg)
  }
}
</style>
