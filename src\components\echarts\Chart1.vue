<!-- eCharts折线图 -->
<template>
    <div class="outer" style="width: 270px; height:210px;background-color:rgba(34, 35, 46, 1)">
      <div id="main" style="width: 250px; height: 150px"></div>
    </div>
</template>
  <script setup lang="ts">
  import { ref, onMounted,onUnmounted} from 'vue';
  import echarts from "../../uitls/echarts";
  import { EchartsBase }  from '../../modules/Game_Home/hooks/INTERFACE'

  const props = defineProps<EchartsBase>();
  const { xAxisNum , yAxisValue ,Linedirection = 0 , LineColorStart = 'rgba(53, 121, 213, 0.2)',LineColorEnd = 'rgba(53, 121, 213, 1)',yAxisMax = 'dataMax' } = props; //默认值以及解构赋值

  function createTimestampArray(interval = 2) {  //生成x轴刻度线
    if (interval < 1 || interval > 59) {
      console.error("Interval must be between 1 and 59 seconds.");
      return [];
    }
    
    const timestamps = [];
    for (let minute = 0; minute < 3; minute++) {
      for (let second = 0; second < 60; second += interval) {
        const remainingSeconds = 60 - second;
        if (remainingSeconds < interval) break; // 避免超过5分钟界限
        const formattedSecond = second < 10 ? `0${second}` : second;
        const formattedMinute = minute < 10 ? `0${minute}` : minute;
        const timestamp = `${formattedMinute}:${formattedSecond}`;
        timestamps.push(timestamp);
      }
    }
    return timestamps;
  }
  
  // 使用示例：
  console.log(createTimestampArray(2)); // 5秒间隔
    onMounted(() => {
        setEchartsOption()
    });
    // let outData = 'Rick:'
    function setEchartsOption(){ //配置Echarts
        var chartDom = document.getElementById("main")!;
        var myChart = echarts.init(chartDom);
        var option = {
            formatter:function(data:any){
              let AllDataHtml //
              console.warn('formatter:',data);
              AllDataHtml = 'FPS : '+ data[0]['value'] + '<br/>';
              // AllDataHtml =`<span class="rickxixi"> FPS :  ${data[0]['value']}</span>  '<br/>';`
              return AllDataHtml
            },
            animation:false,
            xAxis: {
                type: 'category',
                data: createTimestampArray(2).reverse(),//X轴个数
                show: false,
                boundaryGap: false,
                axisTick: { show: false },
                axisLabel: {
                  show: false,
                },
            },
            tooltip: {
                // formatter: '{b0}: {c0}<br />{b1}: {c1}',
                type:'line',
                trigger: 'axis',
                axisPointer:{
                    label:{
                        show:false
                    }
                }
                // trigger: 'none',
                //   axisPointer: {
                //     type: 'cross'
                //   }
            },
            yAxis: {
                type: 'value',
                name:'5分钟前', 
                nameLocation:'start',
                max:yAxisMax,
                axisTick: { show: false },
                axisLine: { show: false },
                // axisLabel: {  //y轴单位
                //     formatter: '{value} W'
                //   },
                // show: false,
                splitLine: {
                    interval:4,
                    lineStyle: {
                    color:'rgba(69, 70, 85, 1)',
                    type: [5, 3],
                    },
                },
            },
          grid: { left: 45, right: 0, top: 20, bottom:25},
          series: [
            {
                data: yAxisValue,
                type: 'line',
                lineStyle: {
                    color: new echarts.graphic.LinearGradient(0, Linedirection, 1-Linedirection, 0, [
                    { offset: 0, color: LineColorStart }, // 起始颜色，红色，透明度0.8
                    { offset: 1, color: LineColorEnd } // 结束颜色，蓝色，透明度0.8
                    ])
                },
                symbol: 'none',
            }
          ]
      }
      myChart.setOption(option);
    }
   
  </script>
  
  <style lang="scss" scoped>

    .outer{
      margin:20px 0 0 20px;
      border-radius: 4px;
      display: flex;
      align-items: center
    }

</style>

  ../INTERFACE/INTERFACE