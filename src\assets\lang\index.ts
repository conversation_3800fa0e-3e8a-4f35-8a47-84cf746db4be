import {createI18n} from "vue-i18n";
import cn from "./cn";
import en from "./en";
import ar from "./ar";
import cs from "./cs";
import da from "./da";
import de from "./de";
import el from "./el";
import es from "./es";
import fi from "./fi";
import fr from "./fr";
import hu from "./hu";
import id from "./id";
import it from "./it";
import ja from "./ja";
import kr from "./kr";
import ms from "./ms";
import nl from "./nl";
import no from "./no";
import pl from "./pl";
import pt from "./pt";
import ru from "./ru";
import sv from "./sv";
import th from "./th";
import tl from "./tl";
import tr from "./tr";
import uk from "./uk";
import vi from "./vi";
import zh from "./zh";


const i18n = createI18n({
  legacy: false, // 使用Composition API，这里必须设置为false
  locale: "CN",
  globalInjection: true, // 全局注册$t方法
  messages: {
    "EN": en, //中文
    "CN": cn, //英语
    "AR": ar, //阿拉伯语
    "CS": cs, //捷克语
    "DA": da, //丹麦语
    "DE": de, //德语
    "EL": el, //希腊语
    "ES": es, //西班牙语
    "FI": fi, //芬兰语
    "FR": fr, //法语
    "HU": hu, //匈牙利语
    "ID": id, //印度尼西亚语
    "IT": it, //意大利语
    "JA": ja, //日本语
    "KR": kr, //韩语
    "MS": ms, //马来西亚语
    "NL": nl, //荷兰语
    "NO": no, //挪威语
    "PL": pl, //波兰语
    "PT": pt, //葡萄牙语
    "RU": ru, //俄语
    "SV": sv, //瑞典语
    "TH": th, //泰语
    "TL": tl, //菲律宾语
    "TR": tr, //土耳其语
    "UK": uk, //乌克兰语
    "VI": vi, //越南语
    "ZH": zh, //繁体中文

  },
});

function createListener() {
  // @ts-ignore
  gamepp.setting.onConfigChanged.addEventListener((type: any, id: number, value: any) => {
      if (id === 242) {
        i18n.global.locale.value = value;
      }
  })

  try{
    // @ts-ignore
    i18n.global.locale.value = gamepp.getLanguage.sync().toUpperCase() || 'CN';
  }catch (e) {
    i18n.global.locale.value = 'CN'
  }
}

createListener()

export default i18n;
