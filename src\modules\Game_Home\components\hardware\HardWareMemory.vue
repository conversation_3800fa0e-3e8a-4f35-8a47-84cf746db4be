<script setup lang="ts">
import {hardware} from "@/modules/Game_Home/stores";
import {defineProps, ref, reactive, onMounted, onBeforeUnmount, watch, computed} from "vue";
import ChartHW from "@/components/echarts/ChartHW.vue";
import {findValueByKeyAndType, SensorAddAverageData} from "@/uitls/sensor";

const $store = hardware()
const props = defineProps({
  changeMenuInfo: {
    type: Function,
    required: true
  }
})
let loading = ref(true)
let loading_count = 0
const activeCollapse_memory = ref(["1"])
const memory_temp_arr = reactive<Array<number | string>>([]) // 内存温度
const memory_usage_arr = reactive<Array<number | string>>([]) // 内存占用
const limit = 60

onMounted(() => {
  setTimeout(() => {emitChangeFn()}, 100)
  loadingFn();
  listenHardwareChangeMsg()
})
const memorySizeAndLoad = ref('')

watch(() => $store.bg_sensor_data, () => {
  if ($store.originData.MEMORY) {
    const load = ($store.bg_sensor_data.memory.size! / 1024 / 100 * $store.bg_sensor_data.memory.usage!).toFixed(2)
    memorySizeAndLoad.value = `${load}GB/${$store.bg_sensor_data.memory.size! / 1024}GB`
  }
  try {
    recordPECoreCallBack()
  }catch(e){

  }
}, {immediate: true, deep: true})
function listenHardwareChangeMsg() {
  const hw = new BroadcastChannel('hw')
  hw.onmessage = (e:any)=>{
    if (e.data.action && e.data.action == 'change') {
      emitChangeFn();
    }
  }
}
const showMemoryTemp = computed(()=>{
  return $store.bg_sensor_data && $store.bg_sensor_data.memory && $store.bg_sensor_data.memory.temp
})

async function recordPECoreCallBack() {
  if ($store.bg_sensor_data.memory) {
    const memory_temp: any = $store.bg_sensor_data.memory.temp
    const memory_usage: any = $store.bg_sensor_data.memory.usage

    memory_temp_arr.push(memory_temp)
    memory_usage_arr.push(memory_usage)

    if (memory_temp_arr.length > limit) memory_temp_arr.shift()
    if (memory_usage_arr.length > limit) memory_usage_arr.shift()
  }
}
const emitChangeFn = () => {
  if (activeCollapse_memory.value.length === 0) {
    props.changeMenuInfo(3, 72)
  }else {
    props.changeMenuInfo(3,307)
  }
}
const loadingFn = () => {
  loading_count++
  if (loading_count > 10) {
    loading.value = false
    return
  }
  if ($store.HwInfo.MEMORY) {
    emitChangeFn()
    loading.value = false
  }else{
    setTimeout(()=>{loadingFn()},1000)
  }
}

</script>

<template>
  <div class="HardwareMemory">
    <el-collapse v-model="activeCollapse_memory" @change="emitChangeFn">
      <el-collapse-item :title="$t('hardwareInfo.memory')" name="1" class="as">
        <template #title>
          <div class="HardwareMemoryTitle flex-items-center">
            <span class="iconfont icon-Dram" style="color: #3579D5;font-size: 24px;margin-right: 5px;"></span>
            <span style="color: #ffffff">{{ $t('hardwareInfo.memory') }}</span>
            <span style="margin-left: 10px;"
                  v-if="$store.originData.MEMORY">{{ $t('hardwareInfo.memoryBarCount') }}{{ $store.originData.MEMORY.SubNode.length }}</span>

            <span class="ml-auto"></span>
          </div>
        </template>
        <div class="box">
          <ChartHW :dataHtml="'hardwareInfo.occupied'" :unit="memorySizeAndLoad" unit2="%" :showyAxisValue="false" :yAxisValue="memory_usage_arr"
                   :yAxisMax="100"></ChartHW>
          <ChartHW v-if="showMemoryTemp" :dataHtml="'hardwareInfo.temperature'" unit="℃" :showyAxisValue="true" :yAxisValue="memory_temp_arr"  :yAxisMax="120"></ChartHW>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<style scoped lang="scss">
.HardwareMemory {
  width: 100%;
  background: rgba(45 ,46 ,57, 0.8);
  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  padding: 0 25px 0 20px;

  .HardwareMemoryTitle {
    width: 100%;
    padding-right: 25px;
  }

  .name {
    margin-bottom: 10px;
  }

  .box {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
    margin-bottom: 20px;
  }
}
</style>
