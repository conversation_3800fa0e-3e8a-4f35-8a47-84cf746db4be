const ja = {
  "update": {
    "Game": "Game",
    "PP": "PP",
    "upgradingInProgress": "更新中",
    "theModuleIsBeingUpdated": "モジュールの更新中",
    "dataIsBeingUpdated": "データを更新しています",
    "checkingUpdate": "更新を確認しています",
    "checkingUpgrade": "更新確認中",
    "loadingProgramComponent": "プログラムコンポーネントを読み込み中です",
    "loadingHotkeyModules": "熱鍵コンポーネントの読み込み中",
    "loadingGPPModules": "ゲームPPコンポーネントを読み込み中です",
    "loadingBlackWhiteList": "ブラックリスト/ホワイトリストが読み込まれています",
    "loadingGameSetting": "ゲーム設定パラメータを読み込み中です",
    "loadingUserAbout": "ユーザー認証関連の読み込み中",
    "loadingGameBenchmark": "ゲームベンチマークを読み込んでいます",
    "loadingHardwareInfo": "ハードウェア情報コンポーネントを読み込み中",
    "loadingDBModules": "データベースモジュールを読み込み中です",
    "loadingIGCModules": "IGCモジュールをロードしています",
    "loadingFTPModules": "FTPサポートモジュールを読み込み中です",
    "loadingDialogModules": "ダイアログボックスモジュールを読み込み中です",
    "loadingDataStatisticsModules": "統計モジュールの読み込み中",
    "loadingSysModules": "システムコンポーネントを読み込み中です",
    "loadingGameOptimization": "ゲーム最適化を読み込み中",
    "loadingGameAcceleration": "ゲーム加速を読み込み中",
    "loadingScreenshot": "録画スクリーンショットを読み込み中",
    "loadingVideoComponent": "動画圧縮コンポーネントを読み込み中です",
    "loadingFileFix": "ファイル修復を読み込み中",
    "loadingGameAI": "ゲームのAI品質を読み込み中です",
    "loadingNVAPIModules": "NVAPIモジュールをロードしています",
    "loadingAMDADLModules": "AMDADLモジュールの読み込み中",
    "loadingModules": "モジュールを読み込み中です"
  },
  "messages": {
    "append": "追加",
    "confirm": "確定",
    "cancel": "キャンセル",
    "default": "デフォルト",
    "quickSelect": "クイックセレクション",
    "onoffingame": "ゲーム内モニタリングを有効/無効:",
    "changeKey": "クリックしてキーボードショートカットを変更",
    "clear": "すべてクリア",
    "hotkeyOccupied": "ホットキーが既に使用されています。再設定してください！",
    "minimize": "最小化",
    "exit": "終了",
    "export": "エクスポート",
    "import": "インポート",
    "screenshot": "スクリーンショット",
    "showHideWindow": "表示/非表示ウィンドウ",
    "ingameControlPanel": "ゲーム内コントロールパネル",
    "openOrCloseGameInSettings": "ゲーム内設定パネルのオン/オフ",
    "openOrCloseGameInSettings2": "このショートカットキーを押して有効にする",
    "openOrCloseGameInSettings3": "ゲーム内のモニタリングをオン/オフ",
    "openOrCloseGameInSettings4": "ゲームフィルターの有効化/無効化",
    "startManualRecord": "手動統計記録の開始/停止",
    "performanceStatisticsMark": "パフォーマンス統計マーカー",
    "EnableAIfilter": "AIフィルターを有効にするにはこのショートカットキーを押す必要があります",
    "Start_stop": "手動統計記録の開始/停止",
    "pressureTest": "ストレステスト",
    "moduleNotInstalled": "機能モジュールがインストールされていません",
    "installingPressureTest": "圧力テストモジュールをインストールしています...",
    "importFailed": "インポートに失敗しました",
    "gamepp": "ゲームPP",
    "copyToClipboard": "クリップボードにコピーしました"
  },
  "home": {
    "homeTitle": "ホーム",
    "hardwareInfo": "ハードウェア情報",
    "functionIntroduction": "機能",
    "fixedToNav": "ナビゲーションバーに固定",
    "cancelFixedToNav": "ナビゲーションバーからの固定を解除",
    "hardwareInfoLoading": "ハードウェア情報の読み込み中...",
    "performanceStatistics": "パフォーマンス統計",
    "updateNow": "今すぐ更新",
    "recentRun": "最近の動作",
    "resolution": "解像度：",
    "duration": "期間:",
    "gameFilter": "ゲームフィルター",
    "gameFilterHasAccompany": "ゲームフィルターが伴っています",
    "gameFilterHasAccompany2": "ユーザーは「Cyberpunk」「APEX」「ホグワーツ・レガシー」などのゲームをプレイしています",
    "currentList": "現在のリストの監視項目",
    "moreFunction": "ベンチマーク、ストレステスト、デスクトップ監視などの追加機能は開発中です。",
    "newVersion": "新しいバージョンが利用可能です。",
    "discoverUpdate": "更新が見つかりました!",
    "downloading": "ダウンロード中",
    "retry": "再試行",
    "erhaAI": "2ハAI",
    "recordingmodule": "この機能は録画モジュールに依存しています",
    "superPower": "ウルトラモード",
    "autoRecord": "ゲーム中のキル瞬間を自動記録し、ハイライト瞬間を簡単に保存",
    "externalDevice": "周辺機器ダイナミックライティング",
    "linkage": "ゲーム内でキルシーンをトリガーし、接続された周辺機器で表示します",
    "AI": "AIパフォーマンステスト",
    "test": "GPUを使用してAIモデルをテストし、GPUのAIパフォーマンススコアを表示します",
    "supportedGames": "対応済みゲーム",
    "games": "Naraka: Bladepoint, Delta Force, FragPunk, League of Legends, PUBG, Valorant, The Finals, APEX Legends, COD20",
    "videoRecording": "動画録画",
    "videoRecording2": "OBSベースの動画録画機能、動画のビットレートおよびフレームレート（FPS）の調整をサポートし、異なる画質と滑らかさの要件に対応します。また「インスタントリプレイ」をサポートしており、ショートカットキーを押すことでいつでもハイライトを保存できます！",
    "addOne": "無料で入手",
    "gamePlatform": "ゲームプラットフォーム",
    "goShop": "ストアページに移動",
    "receiveDeadline": "操作後の受取期限",
    "2Ai": "2ハAI",
    "questionDesc": "問題の説明",
    "inputYourQuestion": "ここにご提案またはご意見を入力してください。",
    "uploadLimit": "最大3枚のローカル画像をJPG/PNG/BMP形式でアップロードしてください",
    "email": "メールアドレス",
    "contactWay": "お問い合わせ先",
    "qqNumber": "QQ番号（任意）",
    "submit": "提出"
  },
  "hardwareInfo": {
    "hardwareOverview": "ハードウェア概要",
    "copyAllHardwareInfo": "すべてのハードウェア情報をコピー",
    "processor": "プロセッサー",
    "coreCount": "コア数：",
    "threadCount": "スレッド数:",
    "currentFrequency": "現在の周波数：",
    "currentVoltage": "現在の電圧:",
    "copy": "コピー",
    "releaseDate": "リリース日",
    "codeName": "コードネーム",
    "thermalDesignPower": "熱設計消費電力",
    "maxTemperature": "最大温度",
    "graphicsCard": "グラフィックカード",
    "brand": "ブランド：",
    "streamProcessors": "ストリームプロセッサー:",
    "Videomemory": "ビデオメモリ:",
    "busSpeed": "バス速度",
    "driverInfo": "ドライバ情報",
    "driverInstallDate": "ドライバーインストール日",
    "hardwareID": "ハードウェアID",
    "motherboard": "マザーボード",
    "chipGroup": "チップセット：",
    "BIOSDate": "BIOS日付",
    "BIOSVersion": "BIOSバージョン",
    "PCIESlots": "PCI Express スロット",
    "PCIEVersion": "対応PCIeバージョン",
    "memory": "メモリ",
    "memoryBarCount": "数量：",
    "totalSize": "サイズ:",
    "channelCount": "チャネル:",
    "Specificmodel": "具体的モデル",
    "Pellet": "粒子メーカー",
    "memoryBarEquivalentFrequency": "メモリモジュールの有効周波数：",
    "hardDisk": "ハードドライブ",
    "hardDiskCount": "ハードディスク数：",
    "actualCapacity": "実際の容量",
    "type": "タイプ",
    "powerOnTime": "通電時間",
    "powerOnCount": "通電回数",
    "SSDRemainingLife": "SSD残り寿命",
    "partitionInfo": "パーティション情報",
    "hardDiskController": "ハードディスクコントローラー",
    "driverNumber": "ドライブ番号",
    "display": "モニター",
    "refreshRate": "リフレッシュレート:",
    "screenSize": "画面サイズ：",
    "inches": "インチ",
    "productionDate": "製造日",
    "supportRefreshRate": "リフレッシュレートをサポート",
    "screenLongAndShort": "画面サイズ",
    "systemInfo": "システム情報",
    "version": "バージョン",
    "systemInstallDate": "システムインストール日付",
    "systemBootTime": "今回の起動時間",
    "systemRunTime": "実行時間",
    "Poccupied": "P使用量",
    "Eoccupied": "Eは使用中",
    "occupied": "占有中",
    "temperature": "温度",
    "Pfrequency": "P周波数",
    "Efrequency": "E周波数",
    "thermalPower": "熱功耗",
    "frequency": "周波数",
    "current": "現在",
    "noData": "データなし",
    "loadHwinfo_SDK": "Hwinfo_SDK.dllの読み込みに失敗しました。ハードウェア/センサーのデータを読み込めません",
    "loadHwinfo_SDK_reason": "この問題の可能性のある原因:",
    "reason": "原因",
    "BlockIntercept": "アンチウイルスソフトウェアによってブロックされました。例: 2345 アンチウイルスソフトウェア（2345 アクティブ防御プロセス、McAfee アクティブ防御プロセス）",
    "solution": "解決策:",
    "solution1": "関連プロセスを閉じてアンインストールした後、GamePPを再起動してください",
    "solution2": "関連するデバイスを取り外した後、GamePPを再起動してください",
    "RestartGamePP": "コントローラーを取り外し、デバイス応答を待ち、GamePPを再起動してください",
    "HWINFOcannotrun": "Hwinfoが正常に動作しません",
    "downloadHWINFO": "Hwinfoをダウンロード",
    "openHWINFO": "Hwinfoを起動後、RUNをクリックして正常に開けますか？",
    "hardwareDriverProblem": "ハードウェアドライバの問題",
    "checkHardwareManager": "ハードウェアマネージャーを確認し、マザーボードとビデオカードのドライバーが正しくインストールされていることを保証してください",
    "systemProblem": "システム問題。例えば：BaofengやXiaomaなどのアクティベーションツール使用時にドライバがロードされず、Windows 7のシステムパッチが自動インストールできない場合があります",
    "reinstallSystem": "システムを再インストールしてアクティベート,WI7をダウンロードしてインストール：********* アップデート",
    "Windows7": "Windows 7: SHA-256 パッチをインストールしてください。Windows 10: デジタル証明書でアクティベートしてください。Windows 11 プレビュー版：メモリ整合性を無効化してください",
    "ViolenceActivator": "Xiaomaなどの不正なアクティベーションツールを使用した場合、システムの修復または再インストールを行ってください",
    "MultipleGraphicsCardDrivers": "コンピューターに異なるブランドのビデオカードドライバーがインストールされています。例として、AMDとNvidiaのドライバーが同時にインストールされています。",
    "UninstallUnused": "不要なグラフィックカードドライバーをアンインストールした後、コンピューターを再起動してください",
    "OfficialQgroup": "上記の理由に該当しません。公式QQグループ：908287288（第5グループ）に参加して問題を解決してください。",
    "ExportHardwareData": "ハードウェアデータのエクスポート",
    "D3D": "D3D使用",
    "Total": "総使用量",
    "VRAM": "VRAM使用量",
    "VRAMFrequency": "VRAM周波数",
    "SensorData": "センサーデータ",
    "CannotGetSensorData": "センサーデータを取得できません",
    "LoadingHardwareInfo": "ハードウェア情報の読み込み中...",
    "ScanTime": "最終スキャン：",
    "Rescan": "再スキャン",
    "Screenshot": "スクリーンショット",
    "configCopyed": "設定情報がクリップボードにコピーされました",
    "LegalRisks": "潜在的な法的リスクが存在する",
    "brandLegalRisks": "ブランド表示に潜在的な法的リスクが存在する",
    "professionalVersion": "プロフェッショナル版",
    "professionalWorkstationVersion": "プロフェッショナルワークステーション版",
    "familyEdition": "ホームエディション",
    "educationEdition": "教育版",
    "enterpriseEdition": "エンタープライズエディション",
    "flagshipEdition": "フラグシップエディション",
    "familyPremiumEdition": "ファミリープレミアム",
    "familyStandardEdition": "家庭標準版",
    "primaryVersion": "基本版",
    "bit": "ビット",
    "tempWall": "温度ウォール",
    "error": "エラー",
    "screenshotSuccess": "スクリーンショットの保存に成功しました",
    "atLeastOneData": "少なくとも1つのデータを保持してください",
    "atMostSixData": "最大6つのデータを追加できます",
    "screenNotActivated": "未アクティブ化"
  },
  "psc": {
    "processCoreAssign": "プロセスのコア割り当て",
    "CoreAssign": "コア割り当て:",
    "groupName": "グループ名:",
    "notGameProcess": "非ゲームプロセス",
    "unNamedProcess": "未命名グループ",
    "Group2": "グループ",
    "selectTheCore": "コアを選択",
    "controls": "操作",
    "tips": "プロンプト",
    "search": "検索",
    "shiftOut": "取り出す",
    "ppValue": "PP値",
    "ppDesc": "PP値は過去のハードウェアリソース消費状況を反映しています。この値が大きいほどハードウェアリソースを多く消費します。",
    "littletips": "ヒント: プロセスを長押しして左側のグループにドラッグできます",
    "warning1": "別のグループのコアスレッドを選択するとパフォーマンスに影響する可能性があります。同じグループのコアを使用することを推奨します。",
    "warning2": "このグループ名を空にしますか？",
    "warning3": "削除後、コアの割り当て効果は無効になります。グループを削除してもよろしいですか？",
    "allprocess": "すべてのプロセス",
    "pleaseCheckProcess": "プロセスをチェックしてください",
    "dataSaveDesktop": "データがデスクトップに保存されました。",
    "createAGroup": "グループを作成",
    "delGroup": "グループを削除",
    "Group": "グループ",
    "editGroup": "グループを編集",
    "groupinfo": "グループ情報",
    "moveOutGrouping": "グループから削除",
    "createANewGroup": "新しいグループを作成する",
    "unallocatedCore": "未割り当てのコア",
    "inactiveProcess": "非アクティブなプロセス",
    "importGroupingScheme": "グループ化プロファイルのインポート",
    "derivedPacketScheme": "グループ構成のエクスポート",
    "addNowProcess": "現在実行中のプロセスを追加",
    "displaySystemProcess": "システムプロセス表示",
    "max64": "最大64スレッドまで選択可",
    "processName": "プロセス名",
    "chooseCurProcess": "現在のプロセスを選択",
    "selectNoProcess": "プロセスが選択されていません",
    "coreCount": "コア数",
    "threadCount": "スレッド数",
    "process": "プロセス",
    "plzInputProcessName": "プロセス名を入力して手動で追加してください",
    "has_allocation": "スレッド割り当てスキーマが存在するプロセス",
    "not_made": "プロセスにコアを割り当てていません",
    "startUse": "最適化を有効にする",
    "stopUse": "最適化を無効にする",
    "threadAllocation": "スレッド割り当て",
    "configProcess": "プロセス構成",
    "selectThread": "スレッド選択",
    "hyperthreadingState": "ハイパースレッディング状態",
    "open": "オン",
    "notYetUnlocked": "未開",
    "nonhyperthreading": "ハイパースレッディングなし",
    "intervalSelection": "間隔選択",
    "invertSelection": "選択を反転",
    "description": "ゲームプロセスを特定のCPUコアに固定して動作させ、バックグラウンドプログラムの干渉をスマートに分離。FPS上限を効果的に向上させ、ゲーム中のフレームレートを安定化！ 突発的なラグやフレームレートの急降を抑制し、マルチコアプロセッサーの性能を完全に解放し、ゲーム全体を通じて高フレームレートを維持します。",
    "importSuccess": "インポート成功",
    "importFailed": "インポート失敗"
  },
  "InGameMonitor": {
    "onoffingame": "ゲーム内監視のオン/オフ:",
    "InGameMonitor": "ゲーム内監視",
    "CustomMode": "カスタムモード",
    "Developing": "開発中...",
    "NewMonitor": "監視項目を追加",
    "Data": "パラメーター",
    "Des": "備考",
    "Function": "機能",
    "Editor": "編集",
    "Top": "最上部に固定",
    "Delete": "削除",
    "Use": "使用",
    "DragToSet": "パネルを呼び出した後、ドラッグして設定できます",
    "MonitorItem": "監視項目",
    "addMonitorItem": "モニタリング項目の追加",
    "hide": "隠す",
    "show": "表示",
    "generalstyle": "全般設定",
    "restoredefault": "デフォルト設定に復元",
    "arrangement": "配置方法",
    "horizontal": "横向",
    "vertical": "縦方向",
    "monitorposition": "監視位置",
    "canquickselectposition": "左図から位置を素早く選択可能",
    "curposition": "現在の位置：",
    "background": "背景",
    "backgroundcolor": "背景色:",
    "font": "フォント",
    "fontStyle": "フォントスタイル",
    "fontsize": "フォントサイズ：",
    "fontcolor": "フォントの色：",
    "style": "スタイル:",
    "style2": "スタイル",
    "performance": "パフォーマンス",
    "refreshTime": "更新時間：",
    "goGeneralSetting": "移動する 一般設定へ",
    "selectMonitorItem": "監視項目の選択",
    "selectedSensor": "選択されたセンサー：",
    "showTitle": "タイトルを表示",
    "hideTitle": "タイトルを非表示",
    "showStyle": "表示モード:",
    "remarkSize": "備考のサイズ:",
    "remarkColor": "備考色:",
    "parameterSize": "パラメーターサイズ：",
    "parameterColor": "パラメーターカラー：",
    "lineChart": "折れ線グラフ",
    "lineColor": "折れ線の色：",
    "lineThickness": "折れ線の太さ:",
    "areaHeight": "領域の高さ：",
    "sort": "並べ替え",
    "displacement": "ビデイ:",
    "up": "上に移動する",
    "down": "下へ移動",
    "bold": "太字",
    "stroke": "輪郭",
    "text": "テキスト",
    "textLine": "テキスト+折れ線グラフ",
    "custom": "カスタム",
    "upperLeft": "左上",
    "upper": "中上",
    "upperRight": "右上",
    "Left": "左中央",
    "middle": "中央",
    "Right": "右中",
    "lowerLeft": "左下",
    "lower": "中下",
    "lowerRight": "右下",
    "notSupport": "周辺機器はクリックして表示または非表示にする機能をサポートしていません",
    "notSupportRate": "リターンレートはクリックでの表示切替不可",
    "notFindSensor": "センサーが見つかりません。変更するにはクリックしてください。",
    "monitoring": "モニタリング",
    "condition": "条件",
    "bigger": "より大きい",
    "smaller": "未満",
    "biggerThan": "閾値を超える",
    "biggerThanthreshold": "閾値を超えるパーセント",
    "smallerThan": "しきい値未満",
    "smallerThanthreshold": "しきい値未満のパーセンテージ",
    "biggerPercent": "現在値の減少率（パーセンテージ）",
    "smallerPercent": "現在値の増加率（%）",
    "replay": "インスタントリプレイ機能",
    "screenshot": "スクリーンショット機能",
    "text1": "センサー数値が",
    "text2": "、および",
    "text3": "待ち時間",
    "text4": "指定された秒数内に高い数値が表示されない場合、即座にトリガーします",
    "text5": "リプレイを毎回トリガー後、頻繁なトリガーを抑えるためにトリガー時の数値に閾値を更新",
    "text6": "リプレイをトリガーする現在のしきい値を表示",
    "text7": "センサー数値を表示",
    "text8": "初期しきい値を超える",
    "text9": "初期しきい値未満",
    "text10": "初期しきい値回数",
    "initThreshold": "初期しきい値",
    "curThreshold": "現在のしきい値：",
    "curThreshold2": "現在のしきい値",
    "resetCurThreshold": "現在のしきい値をリセット",
    "action": "機能を有効化",
    "times": "回",
    "percentage": "パーセンテージ",
    "uninstallobs": "録画モジュールがダウンロードされていません",
    "install": "ダウンロード",
    "performanceAndAudioMode": "パフォーマンスとオーディオの互換モード",
    "isSaving": "保存中",
    "video_replay": "即時リプレイ",
    "saved": "保存済み",
    "loadQualitysScheme": "グラフィックプリセットを読み込む",
    "notSet": "未設定",
    "mirrorEnable": "フィルターが有効です",
    "canBeTurnedOff": "戻る",
    "mirrorClosed": "ゲームフィルターが無効です",
    "closed": "閉じられています",
    "openMirror": "フィルターを開始",
    "wonderfulScenes": "ハイライト",
    "VulkanModeHaveProblem": "Vulkanモードには互換性の問題があります",
    "suggestDxMode": "Dxモードへの切り替えをお勧めします",
    "functionNotSupported": "この機能はサポートされていません",
    "NotSupported": "非対応",
    "gppManualRecording": "ゲームPP、手動記録",
    "perfRecordsHaveBeenSaved": "パフォーマンス記録が保存されました",
    "redoClickF8": "記録を続行するには再度F8キーを押してください",
    "startIngameMonitor": "ゲーム内監視機能を有効化しています",
    "inGameMarkSuccess": "ゲーム内でマークが成功しました",
    "recordingFailed": "録画失敗",
    "recordingHasNotDownload": "録画機能がダウンロードされていません",
    "hotkeyDetected": "機能ホットキーの衝突が検出されました",
    "plzEditIt": "ソフト内で変更の上、ご使用ください",
    "onePercentLowFrame": "1% ローフレーム",
    "pointOnePercentLowFrame": "0.1% ローフレーム",
    "frameGenerationTime": "フレーム生成時間",
    "curTime": "現在時刻",
    "runTime": "実行時間",
    "cpuTemp": "CPU温度",
    "cpuUsage": "CPU占有率",
    "cpuFreq": "CPU周波数",
    "cpuPower": "CPU熱消費",
    "gpuTemp": "GPU温度",
    "gpuUsage": "GPU使用率",
    "gpuPower": "GPU熱設計電力",
    "gpuFreq": "GPU周波数",
    "memUsage": "メモリ使用量"
  },
  "LoginArea": {
    "login": "ログイン",
    "loginOut": "ログアウト",
    "vipExpire": "期限切れ",
    "remaining": "残り",
    "day": "空",
    "openVip": "会員をアクティブ化",
    "vipPrivileges": "会員特典",
    "rechargeRenewal": "チャージと更新",
    "Exclusivefilter": "フィルター",
    "configCloudSync": "クラウド同期の設定",
    "comingSoon": "近日公開予定"
  },
  "GameMirror": {
    "filterStatus": "フィルターステータス",
    "filterPlan": "フィルタープリセット",
    "filterShortcut": "フィルターショートカット",
    "openCloseFilter": "フィルターのオン/オフ:",
    "effectDemo": "エフェクトデモ",
    "demoConfig": "デモ設定",
    "AiFilter": "AIフィルターの効果はゲーム内効果に従います",
    "AiFilterFAQ": "AIフィルターのよくある問題",
    "gamePPAiFilter": "GamePP AIフィルター",
    "gamePPAiFilterVip": "GamePP VIP専用AIフィルター、ゲームシーンに応じてリアルタイムでフィルターパラメーターを調整し、ゲーム画面の効果を最適化してゲーム体験を向上させます。",
    "AiMingliangTips": "AI明るさ：ゲーム画面が非常に暗い場合に使用することをお勧めします。",
    "AiBrightTips": "AI鮮やか：ゲーム画面が暗すぎる場合に使用することをお勧めします。",
    "AiDarkTips": "AIディミング：ゲーム画面が過度に鮮やかになる際に使用することをお勧めします。",
    "AiBalanceTips": "AIバランス: ほとんどのゲームシーンに適しています",
    "AiTips": "ヒント：AIフィルターはゲーム内でショートカットキーを押して使用します",
    "AiFilterUse": "ゲーム内でご使用ください",
    "AiFilterAdjust": "AIフィルターのショートカットキー調整",
    "Bright": "鮮やか",
    "Soft": "柔らかい",
    "Highlight": "ハイライト",
    "Film": "ムービー",
    "Benq": "ベンQ",
    "AntiGlare": "アンチグレア",
    "HighSaturation": "高彩度",
    "Brightness": "ビビッド",
    "Day": "昼",
    "Night": "夜",
    "Nature": "自然（しぜん）",
    "smooth": "微細",
    "elegant": "淡雅",
    "warm": "ウォームトーン",
    "clear": "クリア",
    "sharp": "シャープネス",
    "vivid": "動的",
    "beauty": "ビビッド",
    "highDefinition": "高解像度",
    "AiMingliang": "AI ブライト",
    "AiBright": "AI ビビッド",
    "AiDark": "AI 減光",
    "AiBalance": "AIバランス",
    "BrightTips": "鮮やかなフィルターはカジュアル、アクション、アドベンチャー系ゲームに適しており、色の彩度を高めることでゲーム画面をより生き生きと魅力的にします。",
    "liangTips": "ゲーム画面が暗すぎる場合はフィルターの使用を推奨します。",
    "anTips": "ゲーム画面が暗すぎる場合はフィルターの使用が推奨されます。",
    "jianyiTips": "フィルターはゲームのビジュアルが非常に鮮やかであるときに使用することをお勧めします。",
    "shiTips": "フィルターはほとんどのゲームシーンに適しています。",
    "shi2Tips": "フィルターはカジュアル、アクション、アドベンチャー系ゲームに適しています。色の鮮やかさを高めることで、ゲーム画面をより生き生きとした魅力的なものにします。",
    "ruiTips": "フィルターの色合いが繊細で、光と影が穏やかで、幻想的・温かみのある・ノスタルジックなシーンに最適です",
    "qingTips": "明るいトーン、高コントラスト、シャープなディテール、明るく活気に満ちたシーンの表示に最適",
    "xianTips": "高いコントラストと輝度設定により、暗いシーンのディテールを忠実に再現し、明るいシーンでも目がく dazzle しません。",
    "dianTips": "画面の明るさと色を適度に向上させ、映画のような画質を最大限に実現してください",
    "benTips": "白い光の効果を軽減し、純白のゲーム画面がまぶしくないようにします",
    "fangTips": "オープンワールド・アドベンチャーゲーム向け、明るさとコントラストを向上させクリアな映像を実現",
    "jiaoTips": "RPGおよびシミュレーションゲーム向け、トーンバランス調整、視覚的リアリズム向上",
    "jieTips": "ストーリーが豊かで感情表現が細かいゲームに最適、ディテールとソフトネスを強化し、より洗練されたビジュアルを実現",
    "jingTips": "アクション・競技系ゲーム向け、クリア度とコントラストを向上させ画面をよりシャープに",
    "xiuTips": "癒やし系・カジュアルゲーム向け、暖色系と柔らかさを強調し、より温かみのある画面を実現",
    "qihuanTips": "ファンタジー要素と豊かな色彩が特徴のシーン向け。色の彩度を高め、視覚的なインパクトを強調します。",
    "shengTips": "色彩とディテールを強化し、シーンの臨場感とリアリズムを強調します。",
    "sheTips": "FPS、パズル、アドベンチャー向けに設計され、詳細とコントラストを強化し、ゲームワールドのリアリズムを向上させます。",
    "she2Tips": "シューティング、レース、または格闘ゲーム向け。高解像度のディテールとダイナミックなパフォーマンスを強調し、ゲーム体験の緊張感と視覚効果を強化",
    "an2Tips": "暗所でのシーンをより明瞭に表示し、暗いまたは夜間のシナリオに適しています。",
    "wenTips": "アート、冒険、またはカジュアルゲームに適しており、柔らかな色調とライティング効果を演出し、シーンのエレガントさと温かさを追加します。",
    "jing2Tips": "対戦型、音楽リズム、または夜間都市シーンに適したゲーム。明るい色と照明効果を強調しています。",
    "jing3Tips": "競技・アクション・ファンタジー系ゲーム向け、色のコントラストを強化し、画像をさらに豊かで活力的になります。",
    "onlyVipCanUse": "このフィルターはVIPユーザーのみに開かれています"
  },
  "GameRebound": {
    "noGame": "ゲーム記録はありません",
    "noGameRecord": "まだゲーム記録はありません。さあ、プレイを始めましょう！",
    "gameDuration": "本日のゲーム時間：",
    "gameElectricity": "日次電力使用量",
    "degree": "度",
    "gameCo2": "当日CO₂排出量：",
    "gram": "キー",
    "manualRecord": "手動記録",
    "recordDuration": "録画時間：",
    "details": "詳細",
    "average": "平均値",
    "minimum": "最小値",
    "maximum": "最大値",
    "occupancyRate": "使用率",
    "voltage": "電圧",
    "powerConsumption": "消費電力",
    "start": "開始:",
    "end": "終了",
    "Gametime": "ゲーム時間:",
    "Compactdata": "データの最適化",
    "FullData": "完全データ",
    "PerformanceAnalysis": "パフォーマンス分析",
    "PerformanceAnalysis2": "イベントレポート",
    "HardwareStatus": "ハードウェア状態",
    "totalPower": "総消費電力",
    "TotalEmissions": "総排出量",
    "PSS": "備考：以下のグラフデータは平均値を表します",
    "FrameGenerationTime": "フレーム生成時間",
    "GameResolution": "ゲーム解像度",
    "FrameGenerationTimeTips": "このデータポイントは異常に高いため、統計に含まれていません",
    "FrameGenerationTimeTips2": "このデータポイントは異常に低いため、統計に含まれていません",
    "noData": "なし",
    "ProcessorOccupancy": "CPU使用率",
    "ProcessorFrequency": "プロセッサ周波数",
    "ProcessorTemperature": "プロセッサ温度",
    "ProcessorHeatPower": "プロセッサーの熱設計電力",
    "GraphicsCardOccupancy": "グラフィックカードのD3D使用率",
    "GraphicsCardOccupancyTotal": "GPU使用量合計",
    "GraphicsCardFrequency": "GPU周波数",
    "GraphicsCardTemperature": "GPU温度",
    "GraphicsCardCoreTemperature": "GPUコアのホットスポット温度",
    "GraphicsCardHeatPower": "GPU熱出力",
    "GraphicsCardMemoryTemperature": "GPUメモリ温度",
    "MemoryOccupancy": "メモリ使用量",
    "MemoryTemperature": "メモリ温度",
    "MemoryPageFaults": "メモリーページング割り込み",
    "Duration": "期間",
    "Time": "時間",
    "StartStatistics": "統計を開始",
    "Mark": "タグ",
    "EndStatistics": "統計を終了する",
    "LineChart": "折れ線グラフ",
    "AddPointInGame_m1": "ゲーム内で押してください",
    "AddPointInGame_m2": "マーキングポイントを追加可能",
    "LeftMouse": "左クリックで表示/非表示を切り替え、右クリックで色を変更",
    "DeleteThisLine": "ポリラインを削除",
    "AddCurve": "曲線を追加",
    "AllCurvesAreHidden": "すべてのカーブグラフが非表示になっています",
    "ThereAreSamplingData": "総サンプルデータ：",
    "Items": "項目",
    "StatisticsData": "統計データ",
    "electricity": "電力使用",
    "carbonEmission": "炭素排出",
    "carbonEmissionTips": "二酸化炭素排出量（kg） = 電力消費量（kWh） × 0.785",
    "D3D": "D3D使用量:",
    "TOTAL": "総使用量：",
    "Process": "プロセス：",
    "L3Cache": "L3キャッシュ：",
    "OriginalFrequency": "元の周波数：",
    "MaximumBoostFrequency": "最大ターボブースト:",
    "DriverVersion": "ドライバーバージョン：",
    "GraphicsCardMemoryBrand": "ビデオメモリーブランド：",
    "Bitwidth": "バス幅",
    "System": "システム：",
    "Screen": "画面",
    "Interface": "インターフェース:",
    "Channel": "チャネル:",
    "Timing": "シーケンス:",
    "Capacity": "容量：",
    "Generation": "代数",
    "AddPoint_m1": "ゲーム内で押してください",
    "AddPoint_m2": "マーキングポイント追加",
    "Hidden": "非表示",
    "Totalsampling": "総サンプリングデータ：",
    "edition": "ドライバーバージョン：",
    "MainHardDisk": "プライマリーハードディスク",
    "SetAsStartTime": "開始時間として設定",
    "SetAsEndTime": "終了時間として設定",
    "WindowWillBe": "パフォーマンス統計ウィンドウは次の位置にあります",
    "After": "後で閉じる",
    "NoLongerPopUpThisGame": "このゲームはもう表示されません",
    "HideTemperatureReason": "温度理由を非表示",
    "HideTemperatureReason2": "イベントレポートの非表示",
    "HideOtherReason": "他の理由を非表示",
    "CPUanalysis": "CPUパフォーマンス分析",
    "TemperatureCause": "温度原因",
    "tempSensorEvent": "温度センサーイベント",
    "NoTemperatureLimitation": "温度によるCPU性能制限はありません。ご利用の冷却システムはこのゲームの要件を完全に満たしています。",
    "NoTemperatureLimitation2": "温度センサーイベントはありません。あなたの冷却システムはこのゲームの要件を完璧に処理できます。",
    "performanceis": "選択された中の",
    "Inside": "内部,",
    "TheStatisticsTimeOf": "統計期間は対応するトリガー条件を満たしています。トリガー頻度が最も高い原因は",
    "limited": "温度による性能制限総時間比率",
    "SpecificReasons": "具体的な原因および温度関連の原因における割合：",
    "OptimizationSuggestion": "最適化提案:",
    "CPUtemperature": "CPU温度が過熱しています。CPUの冷却環境を確認/改善してください。",
    "CPUoverheat": "マザーボード電源供給によるCPU過熱が発生しています。マザーボード関連設定の確認または冷却環境の改善を行ってください。",
    "OtherReasons": "その他の理由",
    "NoPowerSupplyLimitation": "電源供給/消費電力によるCPU性能の制限はありません。BIOSの電力消費設定がこのゲームの要件に完全に対応可能です。",
    "PowerSupplyLimitation": "電源/消費電力の制限によりパフォーマンスが制約された総時間の",
    "SpecificReasonsInOtherReasons": "具体的な原因および他の原因における割合：",
    "PleaseCheckTheMainboard": "マザーボードの電源供給状況を確認するか、BIOSの電力設定を調整して他の要因によるCPU性能制限を解決してください",
    "CPUcoretemperature": "コア温度がTj,Maxに達し制限されました",
    "CPUCriticalTemperature": "CPU温度が臨界値に達しました",
    "CPUCircuitTemperature": "CPUパッケージ/リングバスが Tj,Max に達成して制限されています",
    "CPUCircuitCriticalTemperature": "CPUパッケージ/リングバスが臨界温度に達しました",
    "CPUtemperatureoverheating": "CPU温度過熱が検出されました。自動的に周波数を低下させ温度を制御し、ハードウェア障害を防止します",
    "CPUoverheatingtriggered": "過熱により放熱メカニズムが作動し、CPUは電圧と周波数を調整して消費電力と温度を低下させます",
    "CPUPowerSupplyOverheating": "CPUはマザーボードの電源供給が深刻な過熱により制限されています",
    "CPUPowerSupplyLimitation": "CPUはマザーボードの電源過熱により制限されています",
    "CPUMaximumPowerLimitation": "コアは最大消費電力制限に達しています",
    "CPUCircuitPowerLimitation": "CPUパッケージ/リングバスが電力制限に達しました",
    "CPUElectricalDesignLimitation": "電気設計制限をトリガー（ICCmax電流制限、PL4ピーク消費電力制限、SVID電圧制限など）",
    "CPULongTermPowerLimitation": "CPUの長時間消費電力が制限に達しました",
    "CPULongTermPowerinstantaneous": "CPUの瞬時消費電力が制限に達しました",
    "CPUPowerLimitation": "CPUターボ周波数低下メカニズム - 通常はBIOSまたは特定ソフトウェアによって制限されます",
    "CPUPowerWallLimitation": "CPU電力制限",
    "CPUcurrentwalllimit": "CPU電流制限",
    "AiAgent": "GamePPエージェント（AI Agent）",
    "AgentDesc": "ホームに戻る",
    "fnBeta": "この機能は現在招待テストフェーズにあります。あなたのGamePPアカウントはまだテストアクセス権限を取得していません。",
    "getAIReport": "AIレポートを取得",
    "waitingAi": "レポート生成完了を待っています",
    "no15mins": "ゲーム時間が15分未満のため、有効なAIレポートを取得できません",
    "timeout": "サーバー要求のタイムアウト",
    "agentId": "エージェントID：",
    "reDo": "再生成レポート",
    "text2": "ゲームPPエージェント：オンラインAI分析レポート、以下はAIによって生成された内容であり、参考のためにのみ掲載しています。",
    "amdAiagentTitle": "ゲームPPエージェント：AMD Ryzen AI分析レポート、以下はAIによって生成された内容であり、参考情報としてご利用ください。",
    "noCurData": "現在のデータはありません",
    "dataScreening": "データフィルタリング",
    "dataScreeningDescription": "この機能は、地図の読み込みやロビーでの放置などの無効なゲーム時間帯の統計データを除外することを目的としています。0 は除外を行わないことを示します。",
    "excessivelyHighParameter": "過剰なパラメータ",
    "tooLowParameter": "パラメータが低すぎる",
    "theMaximumValueIs": "最大値は",
    "theMinimumValueIs": "最小値は",
    "exclude": "除外",
    "dataStatisticsAtThatTime": "時のデータ統計",
    "itHasBeenGenerated": "生成が完了しました。",
    "clickToView": "クリックして表示",
    "onlineAnalysis": "オンライン分析",
    "amdRyzenAILocalAnalysis": "AMD Ryzen AI ローカル分析",
    "useTerms": "使用条件：",
    "term1": "1. ライゼン AI Max プロセッサ または ライゼン Al 300シリーズ プロセッサ",
    "term2": "2.AMD NPU ドライバーバージョン",
    "term3": "3. 統合型グラフィックスを有効にする",
    "conformsTo": "一致",
    "notInLineWith": "非対応",
    "theVersionIsTooLow": "バージョンが古すぎます",
    "canNotUseAmdNpu": "お使いの設定は使用条件を満たしていません。AMD NPUエンティティ分析は利用できません。",
    "unusable": "使用不可",
    "downloadTheFile": "ファイルをダウンロード",
    "downloadSource": "ダウンロード元：",
    "fileSize": "ファイルサイズ：約8.34GB",
    "cancelDownload": "ダウンロードをキャンセル",
    "filePath": "ファイルの場所",
    "generateAReport": "レポートを生成",
    "fileMissing": "ファイルが欠損しています。再ダウンロードが必要です。",
    "downloading": "ダウンロード中...",
    "theModelConfigurationLoadingFailed": "モデル構成の読み込みに失敗しました",
    "theModelDirectoryDoesNotExist": "モデル ディレクトリが存在しません",
    "thereIsAMistakeInReasoning": "推論エラー",
    "theInputExceedsTheModelLimit": "入力がモデル制限を超えています",
    "selectModelNotSupport": "選択したダウンロードモデルはサポートされていません",
    "delDirFail": "既存のモデルディレクトリの削除に失敗しました",
    "failedCreateModelDir": "モデルディレクトリの作成に失敗しました",
    "modelNotBeenFullyDownload": "モデルがすべてダウンロードされていません",
    "agentIsThinking": "ジアジアエージェントは考え中です",
    "reasoningModelFile": "推論モデルファイル",
    "modelReasoningTool": "モデル推論ツール"
  },
  "SelectSensor": {
    "DefaultSensor": "デフォルトセンサー",
    "Change": "変更",
    "FanSpeed": "ファン速度",
    "MainGraphicsCard": "メインGPU",
    "SetAsMainGraphicsCard": "メインGPUに設定する",
    "GPUTemperature": "GPU温度",
    "GPUHeatPower": "GPUの熱消費",
    "GPUTemperatureD3D": "GPU D3D使用率",
    "GPUTemperatureTOTAL": "GPU総負荷",
    "GPUTemperatureCore": "GPUコアホットスポット温度",
    "MotherboardTemperature": "マザーボード温度",
    "MyAttention": "お気に入り",
    "All": "すべて",
    "Unit": "単位：",
    "NoAttention": "未追跡のセンサー",
    "AttentionSensor": "フォローセンサー (Beta)",
    "GoToAttention": "フォーカスに移動",
    "CancelAttention": "フォロー解除",
    "noThisSensor": "センサーなし",
    "deviceAbout": "周辺機器関連",
    "deviceBattery": "外部デバイスバッテリー残量",
    "testFunction": "テスト機能",
    "mouseEventRate": "ポーリングレート",
    "relatedWithinTheGame": "ゲーム関連",
    "winAbout": "システム",
    "trackDevicesBattery": "ペリフェラルのバッテリー残量を追跡",
    "ingameRealtimeMouseRate": "ゲーム実行中のマウスポーリングレート",
    "notfoundDevice": "サポート対応デバイスが見つかりません",
    "deviceBatteryNeedMythcool": "バッテリー表示をサポートするデバイス一覧：（Myth.Coolとの併用が必要です）",
    "vkm1mouse": "ヴァルキリー M1 マウス",
    "vkm2mouse": "ヴァルキリー M2 マウス",
    "vk99keyboard": "Valkyrie 99磁軸キーボード",
    "logitechProWireless": "ロジクス PRO WIRELESS",
    "logitechProXSUPERLIGHT": "ロジクス PRO X SUPERLIGHT",
    "logitechProXSUPERLIGHT2": "ロジクール PRO",
    "logitechPro2LIGHTSPEED": "ロジクール PRO 2 LIGHTSPEED",
    "steelSeriesArctisGameBuds": "セーレArctis GameBuds",
    "steelSeriesArctis7PPlus": "スティールシリーズ Arctis 7P Plus",
    "razerV3": "レーザー バルキリー V3 プロフェッショナル",
    "razerV2": "Razer Viper V2 プロフェッショナルエディション",
    "wireless": "無線",
    "logitechNeedGhub": "ロジテックがデバイスモデルを取得できない場合は、GHUBをダウンロードしてください",
    "chargingInProgress": "充電中",
    "inHibernation": "スリープ中"
  },
  "video": {
    "videoRecord": "ビデオ録画",
    "recordVideo": "録画済み動画",
    "scheme": "プロファイル",
    "suggestScheme": "推奨プラン",
    "text1": "この設定では1分間の動画サイズは約",
    "text2": "この機能は追加のシステム リソースを使用します",
    "low": "低",
    "mid": "ホーム",
    "high": "高",
    "1080p": "ネイティブ",
    "RecordingFPS": "録画FPS",
    "bitRate": "動画ビットレート",
    "videoResolution": "ビデオ解像度",
    "startStopRecord": "録画開始/停止",
    "instantReplay": "インスタントリプレイ",
    "instantReplayTime": "インスタント再生期間",
    "showIngame": "ゲーム内コントロールパネルを起動",
    "CaptureMode": "キャプチャ方法",
    "gameWindow": "ゲームウィンドウ",
    "desktopWindow": "デスクトップウィンドウ",
    "fileSavePath": "ファイル保存パス",
    "selectVideoSavePath": "録画保存パスの選択",
    "diskFreeSpace": "ハードディスクの空き容量：",
    "edit": "変更",
    "open": "開く",
    "displayMouse": "マウスカーソルを表示する",
    "recordMicrophone": "マイク録音",
    "gameGraphics": "ゲームのオリジナル画面"
  },
  "Setting": {
    "common": "全般",
    "personal": "パーソナライゼーション",
    "messageNotification": "メッセージ通知",
    "sensorReading": "センサー読み取り値",
    "OLEDscreen": "OLED焼け防止",
    "performanceStatistics": "パフォーマンス統計",
    "shortcut": "ショートカット",
    "ingameSetting": "ゲーム設定を保存",
    "other": "その他",
    "otherSettings": "その他の設定",
    "GeneralSetting": "全般設定",
    "softwareVersion": "ソフトウェアバージョン",
    "checkForUpdates": "更新を確認する",
    "updateNow": "今すぐ更新",
    "currentVersion": "現在のバージョン",
    "latestVersion": "最新バージョン",
    "isLatestVersion": "現在のバージョンは既に最新です。",
    "functionModuleUpdate": "機能モジュール更新",
    "alwaysUpdateModules": "すべてのインストール済み機能モジュールを最新バージョンに保つ",
    "lang": "言語",
    "bootstrap": "自動起動",
    "powerOn_m1": "起動",
    "powerOn_m2": "秒後に自動起動",
    "defaultDelay": "デフォルトは40秒",
    "followSystemScale": "システム拡大に従う",
    "privacySettings": "プライバシー設定",
    "JoinGamePPPlan": "GamePPユーザー体験改善プログラムに参加する",
    "personalizedSetting": "パーソナライズ",
    "restoreDefault": "デフォルト設定を復元する",
    "color": "色",
    "picture": "画像",
    "video": "ビデオ",
    "browse": "閲覧",
    "clear": "クリア",
    "mp4VideoOrPNGImagesCanBeUploaded": "MP4動画またはPNG画像をアップロード",
    "transparency": "透明度",
    "backgroundColor": "背景色",
    "textFont": "本文フォント",
    "message": "メッセージ",
    "enableInGameNotifications": "ゲーム内通知を有効にする",
    "messagePosition": "ゲーム内表示位置",
    "leftTop": "左上隅",
    "leftCenter": "左中央",
    "leftBottom": "左下隅",
    "rightTop": "右上角",
    "rightCenter": "右中央",
    "rightBottom": "右下隅",
    "noticeContent": "通知内容",
    "gameInjection": "ゲームインジェクション",
    "ingameShow": "ゲーム内表示",
    "inGameMonitoring": "ゲーム内モニタリング",
    "gameFilter": "ゲームフィルター",
    "start": "開始",
    "endMarkStatistics": "終了マーカー統計",
    "readHwinfoFail": "HWINFO ハードウェア情報の読み取りに失敗しました",
    "dataSaveDesktop": "データがクリップボードとデスクトップファイルに保存されました",
    "TheSensorCacheCleared": "センサーキャッシュデータを消去済み",
    "defaultSensor": "デフォルトセンサー",
    "setSensor": "センサーを選択",
    "refreshTime": "データ更新時間",
    "recommend": "デフォルト",
    "sensorMsg": "時間間隔が短いほどパフォーマンス消費が大きくなります。慎重に選択してください。",
    "exportData": "データエクスポート",
    "exportHwData": "ハードウェア情報データのエクスポート",
    "sensorError": "センサー読み取り異常",
    "clearCache": "キャッシュをクリアする",
    "littleTips": "ヒント: パフォーマンスレポートはゲーム起動後2分以内に生成されます",
    "disableAutoShow": "パフォーマンス統計ウィンドウの自動ポップアップを無効にする",
    "AutoClosePopUpWindow_m1": "パフォーマンス統計ウィンドウは指定された時間後に自動的に閉じます：",
    "AutoClosePopUpWindow_m2": "秒",
    "abnormalShutdownReport": "異常シャットダウン報告",
    "showWeaAndAddress": "天気と位置情報を表示",
    "autoScreenShots": "マーク時にゲーム画面を自動的にスクリーンショット",
    "keepRecent": "保持する最近の記録の数：",
    "noLimit": "無制限",
    "enableInGameSettingsSaving": "ゲーム内設定の保存を有効にする",
    "debugMode": "デバッグモード",
    "enableDisableDebugMode": "デバッグモードの起動/停止",
    "audioCompatibilityMode": "オーディオ互換モード",
    "quickClose": "クイッククローズ",
    "closeTheGameQuickly": "ゲームプロセスを迅速に終了",
    "cancel": "キャンセル",
    "confirm": "確認",
    "MoveInterval_m1": "デスクトップおよびゲーム内モニタリングは若干移動します：",
    "MoveInterval_m2": "分",
    "text3": "ゲーム終了後、パフォーマンスレポートウィンドウは表示されず、履歴記録のみが保持されます",
    "text5": "異常シャットダウン後に自動的にレポートが生成されます。この機能を有効にすると追加のシステムリソースを消費します。",
    "text6": "ショートカット機能の使用は他のゲームのショートカットと競合する可能性があります。注意して設定してください。",
    "text7": "キーボードショートカットを「なし」に設定する場合は、キーボードのBackspaceキーを使用してください",
    "text8": "プロセス名に基づいてゲーム実行時のフィルター、ゲーム内モニタリングなどの機能状態を保持します",
    "text9": "有効化すると実行ログが継続記録されます。無効化するとログファイルがクリアされます（無効化推奨）",
    "text10": "有効にするとマザーボードセンサーを取得できなくなり、GamePPによって引き起こされるオーディオの問題を解決します",
    "text11": "Alt+F4を2回連続して使用すると、現在のゲームからすぐに終了できます",
    "text12": "このモードを有効にするにはGamePPを再起動する必要があります。続けますか？",
    "openMainUI": "アプリを表示",
    "setting": "設定",
    "feedback": "問題フィードバック",
    "help": "ヘルプ",
    "sensorReadingSetting": "センサー読み取り設定",
    "searchlanguage": "検索言語"
  },
  "GamePlusOne": {
    "year": "年",
    "month": "月",
    "day": "日",
    "success": "成功",
    "fail": "失敗",
    "will": "現在",
    "missedGame": "見逃したゲーム",
    "text1": "金額、約￥",
    "text2": "累計受取ゲーム数",
    "text3": "バージョン",
    "gamevalue": "ゲームの価値",
    "gamevalue1": "受け取る",
    "total": "総請求済み",
    "text4": "ゲーム数、累計節約時間",
    "text6": "製品、価値",
    "Platformaccountmanagement": "プラットフォームアカウント管理",
    "Missed1": "(受取未了)",
    "Received2": "(受け取り成功)",
    "Receivedsoon2": "現在受け取れます",
    "Receivedsoon": "現在入手可能",
    "Missed": "受け取りを逃した",
    "Received": "受取成功",
    "Getaccount": "アカウントを取得",
    "Worth": "値",
    "Auto": "自動",
    "Manual": "手動",
    "Pleasechoose": "ゲームを選択してください",
    "Receive": "今すぐ取得",
    "Selected": "選択済み",
    "text5": "ゲーム",
    "Automatic": "自動受取中...",
    "Collecting": "受け取り中...",
    "ReceiveTimes": "今月の取得回数",
    "Thefirst": "第",
    "Week": "週",
    "weekstotal": "合計53週",
    "Return": "ホーム",
    "Solutionto": "アカウントのバインドに失敗しました - 解決方法",
    "accounts": "バインドされたアカウント数",
    "Addaccount": "アカウントを追加",
    "Clearcache": "キャッシュをクリア",
    "Bindtime": "バインド時間",
    "Status": "状態",
    "Normal": "正常",
    "Invalid": "無効",
    "text7": "ゲーム数々、累計節約額",
    "Yuan": "元",
    "untie": "解除",
    "disable": "無効にする",
    "enable": "有効にする",
    "gamePlatform": "ゲームプラットフォーム",
    "goStorePage": "ストアページに移動",
    "receiveEnd": "期限後収集",
    "loginPlatformAccount": "ログイン中のプラットフォームアカウント",
    "waitReceive": "受け取りを待っています",
    "receiveSuccess": "取得成功",
    "accountInvalid": "アカウント期限切れ",
    "alreadyOwn": "所有済み",
    "networkError": "ネットワーク異常",
    "noGame": "ゲーム本体なし",
    "manualReceiveInterrupt": "手動取得中断",
    "receiving": "受領中",
    "agree": "私は「シカイチ受け取り計画」に参加することに同意します。",
    "again": "再领取"
  },
  "shutdownTimer": {
    "timedShutdown": "スケジュールシャットダウン",
    "currentTime": "現在時刻：",
    "setCountdown": "カウントダウンを設定する",
    "shutdownInSeconds": "X秒後にシャットダウン",
    "shutdownIn": "シャットダウン後",
    "goingToBe": "が行われます",
    "executionPlan": "実行計画",
    "startTheClock": "タイマーを開始",
    "stopTheClock": "計画をキャンセルする",
    "isShuttingDown": "スケジュールされたシャットダウン計画を実行中:",
    "noplan": "シャットダウン計画はありません",
    "hour": "時間",
    "min": "分",
    "sec": "秒",
    "ms": "ミリ秒",
    "year": "年",
    "month": "月",
    "day": "日",
    "hours": "時間"
  },
  "screenshotpage": {
    "screenshot": "スクリーンショット",
    "screenshotFormat": "ゲーム画面のキャプチャに特化し設計され、JPG/PNG/BMP形式での保存をサポートし、ゲーム画面を迅速にキャプチャでき、高解像度で損なわれない出力が可能です",
    "Turnon": "「自動スクリーンショット」を有効にする、毎",
    "seconds": "秒",
    "takeScreenshot": "スクリーンショットを自動で実行する",
    "screenshotSettings": "この設定はゲーム内でチェックしても無効です",
    "saveGameFilterAndMonitoring": "スクリーンショットに「ゲームフィルター」と「ゲーム内モニタリング」の効果を保存する",
    "disableScreenshotSound": "スクリーンショットの音声通知をオフにする",
    "imageFormat": "画像フォーマット",
    "recommended": "おすすめ",
    "viewingdetails": "画質のディテールを保持し、サイズが適度で、ディテールの表示に適しています",
    "saveSpace": "画質圧縮可能、最小サイズ、スペース節約",
    "ultraQuality": "超解像度・非圧縮の高品質画像。非常に大きなファイルサイズ。画質重視プレイヤー向けのゲーム保存に適しています。",
    "fileSavePath": "ファイル保存パス",
    "hardDiskSpace": "ハードディスク空き容量：",
    "minutes": "分"
  },
  "DesktopMonitoring": {
    "desktopMonitoring": "デスクトップ監視",
    "SomeSensors": "いくつかのセンサーを監視用に推奨しました。削除または追加できます",
    "AddComponent": "コンポーネントを追加",
    "Type": "タイプ",
    "Remarks": "メモ",
    "AssociatedSensor": "センサーを関連付ける",
    "Operation": "操作",
    "Return": "戻る",
    "TimeSelection": "時間の選択：",
    "Format": "フォーマット：",
    "Rule": "ルール：",
    "Coordinate": "座標：",
    "CustomTextContent": "カスタムテキスト内容：",
    "SystemTime": "システム時間",
    "China": "中国",
    "Britain": "イギリス",
    "America": "アメリカ合衆国",
    "Russia": "ロシア",
    "France": "フランス",
    "DateAndTime": "日付と時刻",
    "Time": "時間",
    "Date": "日付",
    "Week": "曜日",
    "DateAndTimeAndWeek": "日付＋時刻＋曜日",
    "TimeAndWeek": "時間＋曜日",
    "Hour12": "12時間表示",
    "Hour24": "24時間制",
    "SelectSensor": "センサーを選択：",
    "AssociatedSensor1": "センサーを関連付け：",
    "SensorUnit": "センサー単位：",
    "Second": "秒：",
    "Corner": "角丸：",
    "BackgroundColor": "背景色：",
    "ProgressColor": "進捗色：",
    "Font": "フォント：",
    "SelectFont": "フォントを選択",
    "FontSize": "フォントサイズ：",
    "Color": "色：",
    "Style": "スタイル：",
    "Bold": "太字",
    "Italic": "斜体",
    "Shadow": "影",
    "ShadowPosition": "シャドウポジション：",
    "ShadowEffect": "シャドウエフェクト：",
    "Blur": "ぼかし",
    "ShadowColor": "シャドウの色：",
    "SelectFromLocalFiles": "ローカルファイルから選択：",
    "UploadImageVideo": "画像/動画をアップロード",
    "UploadSVGFile": "SVGファイルをアップロード",
    "Width": "幅：",
    "Height": "高：",
    "Effect": "効果：",
    "Rotation": "回転：",
    "WhenTheSensorValue": "センサ値が以上",
    "conditions": "条件が満たされない場合（センサー値が0のときは回転しません）",
    "Clockwise": "時計回り",
    "Counterclockwise": "反時計回り",
    "QuickRotation": "高速回転",
    "SlowRotation": "スローローテーション",
    "StopRotation": "回転を停止",
    "StrokeColor": "輪郭色：",
    "Path": "パス",
    "Color1": "色",
    "ChangeColor": "色を変更する",
    "When": "当",
    "SensorValue": "センサー数値が以上",
    "SensorValue1": "センサー値が以下の場合",
    "SensorValue2": "センサ値が等しい",
    "MonitoringSettings": "監視設定",
    "RestoreDefault": "デフォルトに戻す",
    "Monitor": "ディスプレイ",
    "AreaSize": "領域サイズ",
    "Background": "背景",
    "ImageVideo": "画像/動画",
    "PureColor": "単色",
    "Select": "選択",
    "ImageVideoDisplayMode": "画像/動画の表示方法",
    "Transparency": "透過率",
    "DisplayPosition": "表示位置",
    "Stretch": "ストレッチ",
    "Fill": "フィル",
    "Adapt": "適応",
    "SelectThePosition": "グリッドセルをクリックして位置を素早く選択できます",
    "CurrentPosition": "現在地：",
    "DragLock": "ドラッグロック",
    "LockMonitoringPosition": "モニターポジションをロック（ロック後はモニターをドラッグできません）",
    "Unlockinterior": "内部要素ドラッグを有効にする",
    "Font1": "フォント",
    "GameSettings": "ゲーム設定",
    "CloseDesktopMonitor": "ゲームが実行中の場合、デスクトップ監視を自動的に無効化します。",
    "OLED": "OLED焼込み防止",
    "Display": "表示",
    "PleaseEnterContent": "内容を入力してください",
    "NextStep": "次",
    "Add": "追加",
    "StylesForYou": "いくつかの監視スタイルを推奨いたします。選択後に適用可能です。今後、さらに多くのスタイルが追加されます。",
    "EditPlan": "プロファイル編集",
    "MonitoringStylePlan": "監視スタイル設定",
    "AddDesktopMonitoring": "デスクトップ監視を追加",
    "TextLabel": "テキストラベル",
    "ImageVideo1": "画像、動画",
    "SensorGraphics": "センサーグラフィック",
    "SensorData": "センサーデータ",
    "CustomText": "カスタムテキスト",
    "DateTime": "日時",
    "Image": "画像",
    "Video": "動画",
    "SVG": "SVG",
    "ProgressBar": "プログレスバー",
    "Graphics": "グラフィック",
    "UploadImage": "画像をアップロード",
    "UploadVideo": "動画をアップロード",
    "RealTimeMonitoring": "CPUおよびGPUの温度や使用率などのデータをリアルタイムで監視し、ドラッグ＆ドロップによるレイアウト自由設定、個別スタイルのカスタマイズで、性能とデスクトップの美しさを統合的に管理します",
    "Chart": "チャート",
    "Zigzagcolor": "折れ線の色（開始点）",
    "Zigzagcolor1": "折れ線の色（終点）",
    "Zigzagcolor2": "折れ線グラフ領域色（開始）",
    "Zigzagcolor3": "折れ線チャート領域色（終点）",
    "CustomMonitoring": "カスタム監視"
  }
}
//messageEnd 
 export default ja 