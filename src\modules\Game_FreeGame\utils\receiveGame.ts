import axios from "axios";
import { gamepp } from "gamepp";

export async function getGameList(): Promise<any> {
  try {
    const response = await axios({
      method: 'get',
      url: 'https://free-game.gamepp.com/game/getFreeGameList',
      timeout: 5000,
    });

    const EncodeData = response.data.data;
    if (EncodeData !== '') {
      const DecodeData = JSON.parse(gamepp.user.getDecodeInfo.sync(EncodeData));
      console.warn('DecodeData：:',DecodeData);
      return DecodeData;
    } else {
      return [{
        cover:'',
        game_name:'',
        platform:'',
        get_url:'',
        original_price:'',
        expired_date:''
     }];
    }
  } catch (error) {
    console.error('Error fetching game list:', error);
    return [{
      cover:'',
      game_name:'',
      platform:'',
      get_url:'',
      original_price:'',
      expired_date:''
   }];
  }
}


export async function getAllFreeGameList(): Promise<any> {
  try {
    const response:any = await axios({
      method: 'get',
      url: 'http://free-game.gamepp.com/game/getAllFreeGameList?'+Date.now(),
      timeout: 5000,
    });

    // const EncodeData = response;
    if ( response !== '') {
      console.warn('response',response);
      const DecodeData = response.data.data;
      console.warn('DecodeData',DecodeData);
      
      return DecodeData;
    } else {
      return [];
    }
  } catch (error) {
    console.error('Error fetching game list:', error);
    return [];
  }
}

export async function ReceiveGameList(url:string):Promise<any>
{
  axios.get(url).then(response => 
  {
     
  }
  ).catch(error =>
  {

  }
  )
}

function getJSONData(jsonObject: any, name: string, defaultvalue: any)
{
    if (jsonObject && Object.prototype.hasOwnProperty.call(jsonObject, name))
    {
        return jsonObject[name];
    }
    return defaultvalue;

}

function getProperty<Type,Key extends keyof Type>(obj:Type,key:Key)
{
  return obj[key]
}

let x = { a: 1, b: 2, c: 3, d: 4 };
// Type 被推断为 ｛a:number,b:number,c:number,d:number｝
// keyof Type 为 "a"|"b"|"c"|"d"
// 在许多情况下 显示指定泛型参数是不必要的！
getProperty(x, "a");
        
interface FreeGame<T>
{
   data:T;
   status:boolean;
   startText:string
}

export async function sendReceiveEmail<T,U>(url:string,data:T):Promise<FreeGame<U>>
{
  try 
  {
    const response: FreeGame<U> = await axios.post(url, data);
    return {
        data: response.data,
        status: response.status,
        startText: 'rick',
    };
  } 
  catch (error:any) 
  {
    throw new Error(`Request failed: ${error.message}`);
  }
}

// interface MyData {
//   name: string;
//   age: number;
//   email: string;
// }

// const url = 'https://jsonplaceholder.typicode.com/posts';
// const data: MyData = 
// {
//     name: 'John Doe',
//     age: 30,
//     email: '<EMAIL>',
// };

// const response = await sendReceiveEmail<MyData, any>(url, data);


// class ListNode {
//   private val:any;
//   private next:any
  
//   constructor(val:any, next = null) {
//     this.val = val;  // 当前节点的值
//     this.next = next; // 指向下一个节点的指针，默认为 null
//   }
// }

// const node1 = new ListNode(1); // 节点1，值为1，没有下一个节点

// 1 -> 2 -> 3 -> 4 -> null，

// let nodeList = {
//   val: 1,
//   next: {
//       val: 2,
//       next:{
//           val:3,
//           next:{
//               val:4,
//               next: null
//           }
//       }
//   } 
// }

// function reverse(node)
// {
//   let deepCopy = JSON.parse(JSON.stringify(node))
//   let prev = null
//   let curr = deepCopy //{val:1,next:{}}
//   // console.warn('node::',node);
  
//   while(curr != null)
//   {
//     const next = curr.next  //{val:2,next:{}}  {val:3,next:{val:4,next:null}}
//     curr.next = prev //{val:1,next:null} {val:2,next:{val:1,next:null}}
//     prev = curr //{val:1,next:null}
//     console.warn('prev::',prev);
//     console.warn('curr::',curr);
//     curr = next//{val:2,next:{}}
//   }
//   return prev
// }


// export async function getGameList () {
//   console.warn('启动新的axios');
//   return new Promise((resolve, reject) => {
//     Axios({
//       method: 'get',
//       url: 'https://free-game.gamepp.com/game/getFreeGameList',
//       dataType: 'json',
//       timeout: 5000
//     } as any).then((res) => {
//       const EncodeData = res.data.data
//       if (EncodeData !== '') {
//         const DecodeData = JSON.parse(gamepp.user.getDecodeInfo.sync(EncodeData))
//         resolve(DecodeData)
//       } else {
//         resolve([])
//       }
//     }).catch(() => {
//       resolve([])
//     })
//   })
// }

class TreeNode {
  constructor(val,left = null , right = null)
  {
    this.val = val;
    this.left = left;
    this.right = right
  }
}
  //     1
  //    / \
  //   2   3
  //  / \
  // 4   5 

let obj = {val:1,left:{val:2,left:{val:4,left:null,right:null}},right:{val:3,left:null,right:null}}

function preorderTraversal(root) 
{
  const result = [];

  function traverse(node)  //{val:4,left:null,right:null}
  {
    if (!node) return; // 根节点为空
    traverse(node.left);   // null return 
    result.push(node.val); // push 根节点
    traverse(node.right);  // null return 
  }

  traverse(root); // 从根节点开始遍历
  return result;
}

function mergeSortedArrays(arr1:any[], arr2:any[]) 
{
  let i = 0; // 指向arr1的指针
  let j = 0; // 指向arr2的指针
  let result:any[] = []; // 用于存储合并后的结果

  // 遍历两个数组，直到其中一个数组遍历完
  while (i < arr1.length && j < arr2.length) 
  {
      if (arr1[i] < arr2[j]) {
          result.push(arr1[i]); // 将arr1[i]添加到结果中
          i++; // 移动arr1的指针
      } else {
          result.push(arr2[j]); // 将arr2[j]添加到结果中
          j++; // 移动arr2的指针
      }
  }

  // 如果arr1还有剩余元素，直接添加到结果中
  while (i < arr1.length) 
  {
      result.push(arr1[i]);
      i++;
  }

  // 如果arr2还有剩余元素，直接添加到结果中
  // while (j < arr2.length) {
  //     result.push(arr2[j]);
  //     j++;
  // }
  if (j < arr2.length) 
  {
    result.push(...arr2.slice(j));//slice(1)//原数组[1,2,3,4,5] => [2,3,4,5]   slice(1,3)=>[2,3] 提取索引1-2的元素 不包含索引3
  }

  return result; // 返回合并后的结果
}

var lengthOfLongestSubstring = function(s:string) //abccdeabcde
{
    const charIndexMap = new Map(); 
    let left = 0; // 左指针
    let maxLength = 0; 

    for (let right = 0; right < s.length; right++) 
    {
        const char = s[right];

        if (charIndexMap.has(char)) 
        {
            left = Math.max(left, charIndexMap.get(char) + 1);
        }
        
        charIndexMap.set(char, right);
        maxLength = Math.max(maxLength, right - left + 1);
    }

    return maxLength;
};

//aabccdeabcde
//a left 0 right 0 charIndexMap = ['a',0] Max = 1 此时窗口[0,0] [a]
//a left 1 right 1 charIndexMap = ['a',1] Max = 1 此时窗口[0,1] [ab]
//b left 1 right 2 charIndexMap = ['a':1,'b':2] Max = 2 此时窗口[0,2] [abc]
//b left 1 right 2 charIndexMap = ['a':1,'b':2] Max = 2 此时窗口[3,3] [c]
//b left 1 right 2 charIndexMap = ['a':1,'b':2] Max = 2 此时窗口[3,4] [cd]
//b left 1 right 2 charIndexMap = ['a':1,'b':2] Max = 2 此时窗口[3,5] [cde]
//b left 1 right 2 charIndexMap = ['a':1,'b':2] Max = 2 此时窗口[3,6] [cdea]


// console.log(isValid("()"));       // true
// console.log(isValid("()[]{}"));   // true
// console.log(isValid("(]"));       // false
// console.log(isValid("([)]"));     // false
// console.log(isValid("{[]}"));     // true
// console.log(isValid(""));         // true
// console.log(isValid("("));        // false
// console.log(isValid("}"));        // false
function isValid(s) {
  if (s.length % 2 !== 0) return false; // 奇数长度直接无效

  const stack = [];
  const bracketMap:any = {
    ')': '(',
    '}': '{',
    ']': '['
  };
  for (const char of s) {
    if (bracketMap[char]) { // 当前字符是右括号
      const top = stack.pop(); // 弹出栈顶元素
      if (top !== bracketMap[char]) {
        return false; // 栈顶不匹配或栈为空
      }
    } else { // 当前字符是左括号
      stack.push(char);
    }
  }
  return stack.length === 0; // 栈空则有效
}

function bubbleSort(arr:[]) {
  let n = arr.length;
  for (let i = 0; i < n - 1; i++) 
  {
      let swapped = false;
      for (let j = 0; j < n - 1 - i; j++) 
      {
          if (arr[j] > arr[j + 1]) {
              let temp = arr[j];
              arr[j] = arr[j + 1];
              arr[j + 1] = temp;
              swapped = true;
          }
      }
      if (!swapped) 
      {
          break;
      }
  }
  return arr;
}

function getLongestString(stringT:string)
{
  let left = 0
  let saveMap = new Map()
  let maxLength = 0
  for(let right = 0;right<stringT.length;right++)
  {
     const char = stringT[right]
     if(saveMap.has(char))
     {
      left = right
     }

     saveMap.set(char,right)

     maxLength = Math.max(maxLength,right-left+1)
  }
}


export async function FPSResults(): Promise<any> {
  let score
  var benchmark_str2 = window.localStorage.getItem('benchmark_data_v2');
  if (!benchmark_str2) 
  {
      score = 0;
  }
  else
  {
      var benchmark_data2 = JSON.parse(benchmark_str2);
      var score_data = benchmark_data2.serverscore.all_game_performance_score;
      score = score_data;
      console.log(score)
  }
  const jsonObj:any = {}
  jsonObj.uid = gamepp.user.getUserID.sync()
  console.log('userId',jsonObj);
  const encodeData = gamepp.user.getEncodeInfo.sync(JSON.stringify(jsonObj))
  try {
    const response = await axios({
      method: 'post',
      url: "https://rank.gamepp.com/v1/api/getForecastFPSList2",
      timeout: 5000,
      data: {"cpu_name": cpu_name,"gpu_name":gpu_name,"resolutions":resolutions,'score': score,},
      dataType: "json",
    });
     const EncodeData = response.data.data;
    if (EncodeData !== '') {
      const DecodeData = JSON.parse(gamepp.user.getDecodeInfo.sync(EncodeData));
      // console.warn('DecodeData::',DecodeData);
      return DecodeData;
    } else {
      return [];
    }
  } catch (error) {
    console.error('Error fetching game list:', error);
    return [];
  }
}



// 测试代码
let arr1:any[] = [1, 3, 5];
let arr2:any[] = [2, 4, 6];
console.log(mergeSortedArrays(arr1, arr2)); // 输出: [1, 2, 3, 4, 5, 6]

export async function getHistoryList(): Promise<any> {
    const jsonObj:any = {}
    jsonObj.uid = gamepp.user.getUserID.sync()
    console.log('userId',jsonObj);
    const encodeData = gamepp.user.getEncodeInfo.sync(JSON.stringify(jsonObj))
    try {
      const response = await axios({
        method: 'post',
        url: 'https://free-game.gamepp.com/game/getReceiveGameInfo',
        timeout: 5000,
        data: encodeData,
        dataType: "json",
      });
       const EncodeData = response.data.data;
      if (EncodeData !== '') {
        const DecodeData = JSON.parse(gamepp.user.getDecodeInfo.sync(EncodeData));
        // console.warn('DecodeData::',DecodeData);
        return DecodeData;
      } else {
        return [];
      }
    } catch (error) {
      console.error('Error fetching game list:', error);
      return [];
    }
  }

  export async function postReceiveGameInfo (gid, platformName, code, errMessage, tryAgain) {
    const uid = gamepp.user.getUserID.sync()
    const jsonObj = {
      is_cloud: 0,
      receiver_uid: -1,
      uid: uid,
      gid: gid,
      platform_name: platformName,
      status: code,
      errMessage: errMessage
    }
    console.warn('uploadData:',jsonObj);
    const encodeData = gamepp.user.getEncodeInfo.sync(JSON.stringify(jsonObj))
    const response = await axios({
        method: 'post',
        url: 'https://free-game.gamepp.com/game/addReceiveGameInfo',
        timeout: 5000,
        data: encodeData,
      });
  
    console.warn('responseData',response);
    
  }


  export function getTime () 
  {
    const date = new Date()
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2,0)
    const day = (date.getDate()).toString().padStart(2,0)
    const hour = date.getHours().toString().padStart(2,0)
    const min = date.getMinutes().toString().padStart(2,0)
    const sec = date.getSeconds().toString().padStart(2,0)

    return `${year}-${month}-${day} ${hour}:${min}:${sec}`
  }