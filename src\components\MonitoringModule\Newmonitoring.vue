<template>
    <div ref="containerRef"
       @mouseenter="handleMouseEnter"
       @mouseleave="handleMouseLeave"
      class="dream"  :style="{ 
      width: `${dreamSize.width}px`,
      height: `${dreamSize.height}px`,
      }"
    >
      <!-- 文本 -->
      <div v-if="nums === 1" v-show="show" class="textlist" :style="moduleStyles">
        <p :style="styletextShadow">{{ text }}</p>
      </div>
  
      <!-- 传感器 -->
      <div v-if="nums === 2" v-show="show" class="sensor" :style="moduleStyles">
        <p :style="styletextShadow">{{ value }}</p>
        <span v-if="unitshow" :style="styletextShadow">{{ unit }}</span>
      </div>
  
      <!-- 图片视频 -->
      <div v-if="[3, 4].includes(nums)" v-show="show" class="media-container" :class="customClass" >
        <img v-if="nums === 3" :style="[moduleStyles,RotationStyle]" :src="mediaSrc" alt="图片" draggable="false" />
        <video v-if="nums === 4" :style="moduleStyles" :src="mediaSrc" muted loop autoplay></video>
      </div>
  
      <!-- SVG -->
      <div v-if="nums === 5" v-show="show" class="svg-container" >
        <div :style="[svgStyle, moduleStyles]" v-html="processedSvg"></div>
      </div>
  
      <!-- 时间 -->
      <div v-if="nums === 6" v-show="show" class="timecontainer" :style="moduleStyles">
        <p :style="styletextShadow">{{ formattedTime }}</p>
      </div>
      <!-- 线条动画 -->
      <div v-if="nums === 7" v-show="show" :class="['line-box', customClass]" :style="moduleStyles">
        <div class="line" :style="linestyle"></div>
      </div>
      <!-- 进度条 -->
      <div v-if="nums === 8" v-show="show" :class="['ProgressBar', customClass]" :style="moduleStyles">
        <el-progress   :percentage="value"  :style="progressStyles" />
      </div>
      <!-- 图形1 -->
      <div v-if="nums === 9" v-show="show" :class="['backgr_box', customClass]" :style="moduleStyles">
         <div class="backgr" :style="backgrStyle"></div>
      </div>

      <!-- 折线图 -->
      <div v-if="nums === 10" v-show="show" :class="['backgr_box', customClass]" :style="moduleStyles"> 
          <div :id="`chart-${props.id}`" :key="chartKey"  class="echarts-container" :style="{ width: '100%', height: '100%' }" ></div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
  import echarts from '@/uitls/echarts';
  import { useI18n } from 'vue-i18n';
  const { t } = useI18n();
  // 定义 props
    interface Sensor {
        style?: {
            width?: number;
            height?: number;
            color?: string;
            seconds?: number;
            backgroundColor?: string;
            borderRadius?: number;
            ProgressColor?: string;
            ProgressColor2?: string;
            Judgmentitem?: number;
            Interval?: number;
            gradientColors1?: string;
            gradientColors2?: string;
            areaColors1?: string;
            areaColors2?: string;
            animation?: {
              names?: number;
              names2?: number;
              duration?: number;
              duration2?: number;
              [key: string]: any;
            }
        };
        timeType?: number; 
        timeFormat?: number;
        timeRule?: number;
        [key: string]: any;
    }
    const props = defineProps<{
        nums: number;
        id: number;
        value?: string;
        processvalue?: number;
        text?: string;
        boxShadow?: string; 
        textShadow?: string; 
        unit?: string; 
        mediaSrc?: string; 
        custStyle?: Object;
        unitshow?: boolean;
        show?: boolean;
        isHovered?: boolean;
        isSelected?: boolean;
        sensor: Sensor;
        initwidth?: number;
        initheight?: number;
        processedSvg?: string;
        fill?: string;
        stroke?: string; 
        customClass?: string,
        switchcolorsshow?: boolean;
        transformShow?: boolean;
        parameters?:any[];
    }>();


  const emit = defineEmits(['component-enter', 'component-leave']);
  const handleMouseEnter = () => {
    emit('component-enter', props.sensor);
  };

  const handleMouseLeave = () => {
    emit('component-leave');
  };

  const moduleStyles = computed(() => ({
    ...props.custStyle,
        // width: props.sensor.style?.width ? `${props.sensor.style.width}px` : 'auto',
        // height: props.sensor.style?.height ? `${props.sensor.style.height}px` : 'auto',
    border: props.isHovered? '1px dashed #7389B2': props.isSelected
      ? '1px solid #409EFF': 'none',
    cursor: props.isSelected ? 'move' : 'auto',
    backgroundColor: props.isSelected ? 'rgba(64, 158, 255, 0.3)' : '',
    // boxSizing: 'border-box',
    transition: 'all 0.3s ease'
  }))

  const svgStyle = computed(() => ({
    
  }))
  
  const styletextShadow = computed(() => ({
    textShadow: props.textShadow
  }))

  const linestyle = computed(() => ({
    backgroundColor: props.sensor.style?.color,
    '--line-color': props.sensor.style?.color,
    width: props.sensor.style?.width ? `${props.sensor.style.width}px` : 'auto',
    height: `${Math.floor((props.sensor.style?.height || 0 ) / 10)}px`,
    // boxShadow: props.boxShadow
    animation: `line-animation ${props.sensor.style?.seconds || 2.2}s linear infinite`
  }))


  function shouldUseColor() {
    //processvalue本身传感器值
    const processvalue = props.processvalue ?? 0;
    const interval = props.sensor.style?.Interval ?? 0;
    if (!props.switchcolorsshow) return false;
    // console.log('processvalue', props.sensor.style?.Judgmentitem)
    switch (props.sensor.style?.Judgmentitem) {
      case 0:return processvalue >= interval;
      case 1: return processvalue <= interval;
      case 2: return processvalue === interval;
      default: return false;
    }
  }
 
  const progressStyles = computed(() => {
    const useProgressColor = shouldUseColor();
    return{
      width: props.sensor.style?.width ? `${props.sensor.style.width}px` : 'auto',
      height: props.sensor.style?.height ? `${props.sensor.style.height}px` : 'auto',
      '--progress-background-color': props.sensor.style?.backgroundColor,
      '--progress-border-radius': `${props.sensor.style?.borderRadius}px`,
      '--progress-color': useProgressColor ? props.sensor.style?.ProgressColor2: props.sensor.style?.ProgressColor,
      '--progress-width': props.sensor.style?.width ? `${props.sensor.style.width}px` : 'auto',
      '--progress-height': props.sensor.style?.height ? `${props.sensor.style.height}px` : 'auto',
    }
  })

  const backgrStyle = computed(() => {
    const useProgressColor = shouldUseColor();
    return {
      backgroundColor: useProgressColor 
        ? props.sensor.style?.ProgressColor2 
        : props.sensor.style?.backgroundColor,
    };
  });


  const RotationStyle = computed(() => {
    const value = Number(props.value);
    const interval = props.sensor.style?.Interval ?? 0;

    const usePrimary = value > interval;
    if (props.sensor.sensor === '/' || value === 0)return
    const durationProp = usePrimary ? 'duration' : 'duration2';
    const namesProp = usePrimary ? 'names' : 'names2';

    let seconds;
    // console.log('props.sensor.style?.animation?.[durationProp]',props.sensor.style?.animation?.[durationProp]);
    if (props.sensor.style?.animation?.[durationProp] === 0) {
      seconds = 1;
    } else if (props.sensor.style?.animation?.[durationProp] === 1) {
      seconds = 3;
    } else if (props.sensor.style?.animation?.[durationProp] === 2) {
      seconds = 0;
    }
    let animationName;
    if (props.sensor.style?.animation?.[namesProp] === 0) {
      animationName = 'Clockwise';
    } else if (props.sensor.style?.animation?.[namesProp] === 1) {
      animationName = 'Counterclockwise';
    }
      return {
        animation: props.transformShow ? `${animationName} ${seconds}s linear infinite` : '',
    };
  })

  // const backgrStyle = computed(() => ({
  //   backgroundColor: props.sensor.style?.backgroundColor,
  // }))
  
  // 时间相关逻辑
  const formattedTime = ref('')
  let intervalId: number | null = null

const updateTime = () => {
  const now = new Date()
  let formattedTimeValue = ''
  const { timeType, timeFormat, timeRule } = props.sensor
  // console.log('timeType', timeType, timeFormat, timeRule)
  // 星期（英文和中文）
  const weekEn = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
  const weekZh = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  const dayIndex = now.getDay() // 0(Sun) ~ 6(Sat)
  const dayNameEn = weekEn[dayIndex]
  const dayNameZh = weekZh[dayIndex]

  // 公共选项
  const commonOptions: Intl.DateTimeFormatOptions = {
    hour12: timeRule === 0
  }

  // 根据 timeType 设置 locale / timeZone
  let locale = 'zh-CN'
  let timeZone = undefined as string | undefined

  if (timeType === 4) {//英国
    locale = 'en-GB'
    timeZone = 'Europe/London'
  } else if (timeType === 2) {//美国
    locale = 'en-US'
    timeZone = 'America/New_York'
  } else if (timeType === 3) {//俄罗斯
    locale = 'ru-RU'
    timeZone = 'Europe/Moscow'
  } else if (timeType === 5) {//法国
    locale = 'fr-FR'
    timeZone = 'Europe/Paris'
  } else if (timeType === 1) {//中国
    locale = 'zh-CN'
    timeZone = 'Asia/Shanghai'
  } else {
    // 系统时间
    locale = 'zh-CN'
    timeZone = undefined
  }

  // 封装 toLocaleDateString / toLocaleTimeString 方法
  const formatDate = (options?: Intl.DateTimeFormatOptions) =>
    now.toLocaleDateString(locale, { ...{ year: 'numeric', month: '2-digit', day: '2-digit' }, ...options })

  const formatTime = (options?: Intl.DateTimeFormatOptions) =>
    now.toLocaleTimeString(locale, { ...{ hour: '2-digit', minute: '2-digit', second: '2-digit' }, ...options })

  // 组合格式化
  const fullOptions = { ...commonOptions, timeZone }

  switch (timeFormat) {
    case 0:
      formattedTimeValue = `${formatDate({ timeZone })} ${formatTime(fullOptions)}`
      break

    case 1:
      formattedTimeValue = formatTime(fullOptions)
      break

    case 2:
      formattedTimeValue = formatDate({ timeZone })
      break

    case 3:
      if(timeZone!== undefined && timeZone !== 'Asia/Shanghai'){
        formattedTimeValue = dayNameEn
      }else{
        formattedTimeValue = dayNameZh
      }
      break

    case 4:
      if(timeZone!== undefined && timeZone !== 'Asia/Shanghai'){
        formattedTimeValue = `${formatDate({ timeZone })} ${formatTime(fullOptions)} ${dayNameEn}`
      }else{
        formattedTimeValue = `${formatDate({ timeZone })} ${formatTime(fullOptions)} ${dayNameZh}`
      }
      break

    case 5:
      if(timeZone!== undefined && timeZone !== 'Asia/Shanghai'){
        formattedTimeValue = `${formatTime(fullOptions)} ${dayNameEn}`
      }else{
        formattedTimeValue = `${formatTime(fullOptions)} ${dayNameZh}`
      }
      break

    default:
      formattedTimeValue = formatTime(fullOptions)
  }

  formattedTime.value = formattedTimeValue
}

  const containerRef = ref<HTMLElement | null>(null)
  const dreamSize = ref({ width: 0, height: 0 })
  const updateDreamSize = () => {
    if (containerRef.value) {
      const child = containerRef.value.firstElementChild as HTMLElement
      if (child) {
        dreamSize.value = {
          width: child.offsetWidth,
          height: child.offsetHeight
        }
        if (props.nums === 10) {
          nextTick(resizeChart);
        }
      }
    }
    
  }
  
  const chartKey = ref(0);
  const myChart = ref<echarts.ECharts | null>(null);
  const initChart = (retryCount = 0) => {
    if (props.nums !== 10) {
      return;
    }
    nextTick(() => {
      const chartContainer = document.getElementById(`chart-${props.id}`);
      if (!chartContainer) {
        return;
      }
      if (chartContainer.offsetWidth === 0 || chartContainer.offsetHeight === 0) {
        console.warn('图表容器尺寸为0，延迟初始化');
        setTimeout(initChart, 100);
        return;
      }
      if (chartContainer.offsetWidth === 0 || chartContainer.offsetHeight === 0) {
        if (retryCount < 10) { 
          console.warn(`图表容器尺寸为0，重试中... (${retryCount + 1}/10)`);
          setTimeout(() => initChart(retryCount + 1), 100);
          return;
        } else {
          console.error('图表容器尺寸为0，初始化失败');
          return;
        }
      }
      
      // 确保没有旧实例
      // if (myChart.value) {
      //   clearChartData();
      // }
      try {
          myChart.value = echarts.init(chartContainer);
          updateChart();
        } catch (e) {
          console.error('初始化图表失败:', e);
        }
      // if (chartContainer) {
      //   myChart.value = echarts.init(chartContainer);
      //   updateChart();
      // }
    });
  };

  let validData;
  const getChartOption = () => {
    const gradientColors1 = props.sensor.style?.gradientColors1 || 'rgba(255, 255, 255, 1)';
    const gradientColors2 = props.sensor.style?.gradientColors2 || 'rgba(255, 255, 255, 1)';
    const areaColors1 = props.sensor.style?.areaColors1 || 'rgba(255, 255, 255, 1)';
    const areaColors2 = props.sensor.style?.areaColors2 || 'rgba(255, 255, 255, 1)';
    // console.log('图表颜色配置:', {gradientColors1,gradientColors2,areaColors1,areaColors2});
    // console.log('图表数据:', props.parameters);
    
    // validData = (props.parameters || []).map(item => Number(item) || 0).filter(item => !isNaN(item));
    // if (validData.length === 0) {
    //     validData.push(0, 0, 0, 0, 0, 0, 0, 0, 0, 0); // 默认值
    // }
    const dataArray = Array.isArray(props.parameters) ? props.parameters : [];
    validData = dataArray.map(item => {
      const num = Number(item);
      return isNaN(num) ? 0 : num;
    });

    // 至少有一个数据点
    // if (validData.length === 0) {
    //   validData = [0]; 
    // }
    // // 确保数据长度恰好为10
    // if (validData.length < 10) {
    //   validData = [...validData, ...Array(10 - validData.length).fill(0)];
    // } else if (validData.length > 10) {
    //   validData = validData.slice(0, 10);
    // }

    const xAxisData = Array.from({ length: 10 }, () => '');
    // const xAxisData = Array(10).fill('');
    return {
      animation: false,
      xAxis: {
        type: 'category',
        boundaryGap: false,
        axisTick: { show: false },
        symbol: 'none',
        show: false,
        data:xAxisData,
      },
      yAxis: {
        type: 'value',
        axisTick: { show: false },
        axisLine: { show: false }, 
        show: false,
      },
      grid: { left: 0, right: 0, top: 0, bottom: 0 },
      series: [
        {
          data: validData,
          type: 'line',
          symbol: 'none',
          lineStyle: {
            width: 2,
            color: gradientColors1 && gradientColors2 
              ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: gradientColors1 },
                  { offset: 1, color: gradientColors2 },
                ])
              : 'rgba(255, 255, 255, 1)',
          },
          areaStyle: {
            color: areaColors1 && areaColors2 
              ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: areaColors1 },
                  { offset: 1, color: areaColors2 },
                ])
              : 'rgba(255, 255, 255, 1)',
            globalCoord: false,
          },
        },
      ],
    };
};

const updateChart = () => {
  if (!myChart.value || props.nums !== 10) return;
  try {
    if (!props.parameters || !Array.isArray(props.parameters) || props.parameters.length === 0) {
      return;
    }
    // console.log('更新图表数据111:', props.parameters);
    if (myChart.value && !myChart.value.isDisposed()) {
      const option = getChartOption();
      myChart.value.setOption(option);
    }
  } catch (e) {
    initChart();
  }
};
const resizeChart  = () => {
  if (myChart.value) {
    myChart.value.resize();
    // updateChart();
  }
};
 // 清空图表实例
  const clearChartData = () => {
    if (myChart.value) {
      try {
        myChart.value.clear();
        myChart.value.dispose(); 
      } catch (e) {}
      myChart.value = null;
    }
    validData = []
  };
  onMounted(() => {
    updateTime()
    intervalId = window.setInterval(updateTime, 1000)
    updateDreamSize()
     const resizeObserver = new ResizeObserver(updateDreamSize)
    if (containerRef.value) {
      resizeObserver.observe(containerRef.value)
    }
    nextTick(()=>{
        if(props.nums===10){
          initChart();
        }
    });

  })
  
  onBeforeUnmount(() => {
    if (intervalId !== null) {
      window.clearInterval(intervalId)
    }
    clearChartData();
  })


  watch(() => props.parameters, (newVal) => {
    if(!myChart.value || props.nums!==10) return;
      //确保新数据是数组且不为空
      if(Array.isArray(newVal) && newVal.length>0){
          nextTick(updateChart);
      }
      try {
        const option = getChartOption();
        if (myChart.value) {
          myChart.value.setOption(option);
        } else {
          initChart();
        }
      } catch (e) {
        console.error('更新图表失败:', e);
        initChart();
      }
  }, { deep: true });

  watch(
    () => [
      props.sensor.style?.width, 
      props.sensor.style?.height
    ],
    () => {
      // 折线图宽高变化时重绘
      if (props.nums === 10) {
        nextTick(resizeChart);
      }
    },
    { deep: true }
  );
  watch(() => [
      props.sensor.style?.gradientColors1,
      props.sensor.style?.gradientColors2,
      props.sensor.style?.areaColors1,
      props.sensor.style?.areaColors2
    ],
    (newValues, oldValues) => {
    const hasChanged = newValues.some((val, i) => val !== oldValues?.[i]);
    if (props.nums === 10 && hasChanged && newValues !== oldValues &&  props.id.toString() === props.sensor.id) {
      console.log('颜色发生变化，重绘折线图');
        updateChart();
      }
    },
    { deep: true }
  );
  watch(() => props.custStyle, updateDreamSize, { deep: true })
  watch(() => props.nums, updateDreamSize)
  watch(
    () => props.sensor,
    () => {
      updateTime()
    },
    { deep: true }
  )

  watch( () => props.sensor.sensor,(newVal, oldVal) => {
    if (props.nums === 10 && newVal !== oldVal &&  props.id.toString() === props.sensor.id) {
      clearChartData();
      // 重新初始化图表
      console.log(`[图表 ${props.id}] 传感器变化: ${oldVal} -> ${newVal}`);
      nextTick(() => {
        initChart();
      });
    }
  },
  { deep: true }
  );


  window.addEventListener('storage', (event) => {
      if(event.key === 'activeMonitorId'){
        const activeMonitorId = Number(event.newValue);
        if (activeMonitorId === 3 || props.nums === 10) {
          // console.log('监听到storage变化1111111111111', event.key, event.newValue,props.nums);
          chartKey.value++;
          clearChartData();
          setTimeout(() => {
            if (props.nums === 10) {
              initChart();
            }
          }, 100);
        }
      }
      const activeId = Number(localStorage.getItem('activeMonitorId'));
      if (activeId  === 3 || props.nums === 10) {
        // const sensorSettingsData = localStorage.getItem(`sensorSettings_${activeId}`);
        if(event.key === `sensorSettings_${activeId}`){
        //  console.log('监听到storage变化3', event.key, event.newValue,props.nums);
          chartKey.value++;
            clearChartData();
            setTimeout(() => {
              if (props.nums === 10) {
                initChart();
              }
            }, 100);
        }
      }
  });
  </script>
  
  <style lang="scss">
  @font-face{
      font-family: 'QuartzRegular';
      src:url('../../assets//font/QuartzRegular.ttf');
  }
  @font-face{
      font-family: 'Hybriddd';
      src:url('../../assets/font/xiaokou.ttf');
  }
  @font-face{
      font-family: 'AeeAndCui';
      src:url('../../assets/font/aeeandcuiRegular.ttf');
  }
  .sensor {
    display: flex;
    align-items: center;
    span {
      margin-left: 2px;
    }
  }
  .media-container {
    width: 100%;
    height: 100%;
  }
  .textlist {
    p {
      width: max-content;
    }
  }
  .dream {
    // width: 0;
    // height: 0;
    // display: inline-flex;
  }
  .svg-container svg {
    width: 100%;
    height: 100%;
  }
  .timecontainer {
    display: flex;
    p {
      width: max-content;
    }
  }
  .breath img{
    -webkit-animation: breath 2s infinite ease-in-out alternate;
    animation: breath 2s infinite ease-in-out alternate;
  }
  .breath2 img{
    -webkit-animation: breath 3s infinite ease-in-out alternate;
    animation: breath 3s infinite ease-in-out alternate;
  }
  .breath3 img{
    -webkit-animation: breath 2.5s infinite ease-in-out alternate;
    animation: breath 2.5s infinite ease-in-out alternate;
  }
  @keyframes breath {
    from { opacity: 0.4; }
    50%  { opacity:   1; }
    to   { opacity: 0.4; }
}
  .rotate180 img{
    transform: rotate(180deg);
  }
  .line-box{
    overflow: hidden;
    position: absolute;
    .line{
      position: absolute;
      top: 50%;
      left: 0;
      transform: translateY(-50%);
      animation: line-animation 2s linear infinite;
      width: 100%;
    }
  }
  .line-box .line::after {
      content: '';
      display: block;
      width: 6px;
      height: 6px;
      // background: #FBAC14;
      background: var(--line-color);
      border-radius: 50%;
      position: absolute;
      right: -2px;
      top: -2px;
  }
  @keyframes line-animation {
    0% {
        left: 0;
    }
    50% {
        left: 100%;
    }
    51% {
        left: -100%;
    }
    100% {
        left: 0;
    }
}
.backgr{
  -webkit-clip-path: polygon(3% 0%, 97% 0%,100% 60%,100% 100%,0% 100%,0% 60%);
  width: 100%;
  height: 100%;
}
.el-progress__text{display: none;}
.ProgressBar .el-progress-bar__outer{
  background: var(--progress-background-color);
  border-radius: var(--progress-border-radius);
  height: var(--progress-height)!important;
  width: var(--progress-width);
}
.ProgressBar .el-progress-bar__inner{
  background:var(--progress-color);
  border-radius: var(--progress-border-radius);
}
.ProgressShape1{
  -webkit-clip-path: polygon(4% 0%, 96% 0%,100% 20%,100% 80%,96% 100%,4% 100%,0% 80%,0% 20%);
}
@keyframes Clockwise {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
@keyframes Counterclockwise {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}

.animation img{
  animation: rotation 4s linear infinite;
}
@keyframes rotation {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
  .echarts-container canvas{
    width: 100%!important;
    height: 100%!important;
  }
  .echarts-container div{
    width: 100%!important;
    height: 100%!important;
  }
  </style>