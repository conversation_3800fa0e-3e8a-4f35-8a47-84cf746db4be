<!-- eCharts折线图 -->
<template>
  <div class="outer" style="width: 270px; height:210px;background-color:rgba(34, 35, 46, 0.8)">
    <div ref="chart" style="width: 250px; height: 150px"></div>
    <div class="dq">{{ $t('hardwareInfo.current') }}</div>
    <div class="leftTopText">{{$t(dataHtml)}}</div>
    <div class="rightTopSensorData">
      <span v-if="showyAxisValue">{{yAxisValue[yAxisValue.length-1]}}</span>
      <span>{{props.unit}}</span>
    </div>
    <div class="loader-wrap" v-if="yAxisValue && yAxisValue.length < 1">
      <svg viewBox="25 25 50 50">
        <circle r="20" cy="50" cx="50"></circle>
      </svg>
    </div>
    <div class="no-data-wrap" v-if="yAxisValue[yAxisValue.length-1] === '无'">
      {{ $t('hardwareInfo.noData') }}
    </div>
  </div>
</template>
<script setup lang="ts">
import {ref, watch, onMounted, onBeforeUnmount, nextTick} from 'vue';
import * as echarts from 'echarts';
import {useI18n} from "vue-i18n";

const {t} = useI18n();

type Props = {
  xAxisNum?:any[];//x轴的个数
  yAxisValue:number[];//y轴的值
  Linedirection?:number;//线条渐变方向
  LineColorStart?:string;//线条起始颜色 rgba 可以改变透明度
  LineColorEnd?:string;//线条终止颜色
  yAxisMax?:any;//y轴刻度线最大值
  xAxisMin?:number; //记录分钟数
  xAxisSecond?:number //间隔秒数
  dataHtml?:string;//数据展示
  unit:string;//单位
  showyAxisValue?: any;//是否显示右上角值
  unit2?: string|null
  tempWall?: number|null
  // width: number;
  // height: number;
};
const chart = ref()
const props = defineProps<Props>();
let init = false
const { xAxisNum , yAxisValue ,Linedirection = 0 , LineColorStart = 'rgba(53, 121, 213, 0.2)',
  LineColorEnd = 'rgba(53, 121, 213, 1)',  yAxisMax = 'dataMax',dataHtml = '',unit,unit2 = null,showyAxisValue = true} = props;//设置默认值
const PointNum = 60
let xAxisData:Array<string> = new Array(PointNum).fill('')
let yAxisData:Array<any> = new Array(PointNum).fill('无')

function createTimestampArray(interval = 2) {  //生成x轴刻度线
  if (interval < 1 || interval > 59) {
    console.error("Interval must be between 1 and 59 seconds.");
    return [];
  }

  const timestamps = [];
  for (let minute = 0; minute < 3; minute++) {
    for (let second = 0; second < 60; second += interval) {
      const remainingSeconds = 60 - second;
      if (remainingSeconds < interval) break; // 避免超过5分钟界限
      const formattedSecond = second < 10 ? `0${second}` : second;
      const formattedMinute = minute < 10 ? `0${minute}` : minute;
      const timestamp = `${formattedMinute}:${formattedSecond}`;
      timestamps.push(timestamp);
    }
  }
  return timestamps;
}

watch(props.yAxisValue,()=>{
  setEchartsOption()
})
let animateShadowOpacity:any = null;
let shadowBlur = 8;
let shadowOpacity = 0.8;
let symbolSize = 6;
let symbolOpacity = 1;
let addOrReduce = '+';
let animationFrameId:number;
onMounted(() => {
  try {
    setEchartsOption()
    setPointAni()
  }catch (e) {

  }
});
onBeforeUnmount(()=>{
  cancelAnimationFrame(animationFrameId);
})
function setPointAni () {
  let updateCount = 0; // 计数器
  animateShadowOpacity = () => {
    updateCount++;

    if (updateCount % 8 === 0) { // 每10次调用才更新一次状态
      updateCount = 0
      // if (addOrReduce == '+') shadowOpacity += 0.1;
      // if (addOrReduce == '-') shadowOpacity -= 0.1;
      //
      // if (shadowOpacity >= 1) addOrReduce = '-';
      // if (shadowOpacity <= 0.2) addOrReduce = '+';
      if (addOrReduce == '-') {
        symbolSize -= 0.1;
        symbolOpacity -= 0.06;
      }
      if (addOrReduce == '+') {
        symbolSize += 0.1;
        symbolOpacity += 0.06;
      }

      if (symbolSize >= 6) addOrReduce = '-';
      if (symbolSize <= 5) addOrReduce = '+';

      setEchartsOption(false);
    }

    // @ts-ignore
    animationFrameId = requestAnimationFrame(animateShadowOpacity);
  };

  // 开始动画
  animateShadowOpacity();
}

// let outData = 'Rick:'
async function setEchartsOption(flag=true){ //配置Echarts
  let myChart
  if (init) {
    myChart = echarts.getInstanceByDom(chart.value)
  }else{
    await nextTick()
    myChart = echarts.init(chart.value)
    init = true
  }

  if (yAxisValue && yAxisValue.length > 0 && flag) {
    yAxisData.push(yAxisValue[yAxisValue.length-1])
    if (yAxisData.length > PointNum) {
      yAxisData.shift()
    }
  }
  let displayArr = JSON.parse(JSON.stringify(yAxisData))
  if (displayArr && displayArr.length > 0) {
    const v = displayArr[displayArr.length-1]
    displayArr[displayArr.length-1] = {
      value: v,
      symbol: 'circle',
    }
  }else{
    displayArr = []
  }

  let option:any = {
    animation:false,
    xAxis: {
      type: 'category',
      data: xAxisData,//X轴个数
      show: false,
      boundaryGap: true,
      axisLabel:{
        interval: 0
      }
    },
    tooltip: {
      show: false,
    },
    yAxis: {
      type: 'value',
      name:'',
      nameLocation:'start',
      max:yAxisMax,
      axisTick: { show: false },
      axisLine: { show: false },
      axisLabel: {  //y轴单位
        formatter: '{value}'+(unit2? unit2:unit)
      },
      // show: false,
      splitLine: {
        interval:4,
        lineStyle: {
          color:'rgba(69, 70, 85, 1)',
          type: [5, 3],
        },
      },
    },
    grid: { left: 45, right: 3, top: 20, bottom:25},
    series: [
      {
        data: displayArr,
        type: 'line',
        lineStyle: {
          color: new echarts.graphic.LinearGradient(0, Linedirection, 1-Linedirection, 0, [
            { offset: 0, color: LineColorStart }, // 起始颜色，红色，透明度0.8
            { offset: 1, color: LineColorEnd } // 结束颜色，蓝色，透明度0.8
          ])
        },
        symbol: 'none',
        symbolSize: symbolSize, // 圆点的大小
        itemStyle: {
          color: `rgba(53,121,213,1)`,
          shadowBlur: shadowBlur, // 发光效果的模糊半径
          opacity: symbolOpacity,
          shadowColor: `rgba(39,131,255,1)`, // 发光效果的颜色
          shadowOffsetX: 0,
          shadowOffsetY: 0,
        },
      }
    ]
  }
  if ("tempWall" in props && props.tempWall) {
    option.series[0].markLine = {
      symbol: ['none', 'none'],
      label: {show: true,},
      data: [
          {
            type: 'value',
            yAxis: props.tempWall,
            lineStyle: {
              color: "#ff5b5b", // 设置颜色
              width: 1, // 设置线宽
              type: 'dashed', // 设置虚线
              cap: 'round',
            },
            label: {
              show: true,
              position: 'end',
              formatter: '{b}: {c}'
            },
            tooltip: {
              show: true,
              formatter: t('hardwareInfo.tempWall')+': {c}℃'
            }
          }
      ]
    }
  }

  if (myChart) {
    myChart.setOption(option)
  }
}

</script>
<style>
.absa {
  border: 3px solid red;
}
</style>
<style lang="scss" scoped>
 .rickxixi{
  width: 200px;
  height: 30px;
  background-color: #35D5B1 !important
}
.no-data-wrap {
 position: absolute;
 top: 0;
 bottom: 0;
 left: 0;
 right: 0;
 background-color: rgba(34, 35, 46, 1);
 display: flex;
 justify-content: center;
 align-items: center;
  color:#fff;
}
.loader-wrap {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(34, 35, 46, 1);
  display: flex;
  justify-content: center;
  align-items: center;

  svg {
    width: 3.25em;
    transform-origin: center;
    animation: rotate4 2s linear infinite;
  }

  circle {
    fill: none;
    stroke: hsl(214, 97%, 59%);
    stroke-width: 2;
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
    stroke-linecap: round;
    animation: dash4 1.5s ease-in-out infinite;
  }

  @keyframes rotate4 {
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes dash4 {
    0% {
      stroke-dasharray: 1, 200;
      stroke-dashoffset: 0;
    }

    50% {
      stroke-dasharray: 90, 200;
      stroke-dashoffset: -35px;
    }

    100% {
      stroke-dashoffset: -125px;
    }
  }

}

.outer{
  display: flex;
  align-items: center;
  position: relative;
  width: 270px;
  height: 210px;
  background: rgba(34, 35, 46, 0.8);
  box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.3);
  border-radius: 4px;
}

.dq {
  position: absolute;
  right: 19px;
  bottom: 25px;
  font-size: 12px;
  color: #777777;
}

.leftTopText {
  position: absolute;
  left: 12px;
  top: 14px;
  font-size: 12px;
  color: #ffffff;
}

.rightTopSensorData {
  position: absolute;
  right: 21px;
  top: 14px;
  font-size: 12px;
  color: #35D5B1;
}
</style>

