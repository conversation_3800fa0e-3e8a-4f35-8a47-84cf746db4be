<script setup lang="ts">
import {reactive, ref, onMounted} from "vue";
import {GPP_WriteInteger,GPP_GetInteger} from "../stores/index";

const sliderVal = ref(15);
const switch45 = ref(false)
const marks = reactive({
    15: "15s",
    60: "1min",
    120: "2min",
    180: "3min",
    240: "4min",
    300: "5min",
})
onMounted(async () => {
    sliderVal.value = await GPP_GetInteger(56)
    switch45.value = (await GPP_GetInteger(406)) === 1;
    gamepp.setting.onConfigChanged.addEventListener(async (type, id, value) => {
        if (id === 406 && value) {
            switch45.value = true;
        }
    });
})
function formatSliderTooltip(value:number) {
    return value + 's'
}

function setInteger56(value:number) {
    GPP_WriteInteger(56,value)
}
</script>

<template>
    <div class="video-replay-wrap">
        <div class="switch-line">
            <span>{{$t('video.instantReplay')}}</span>
            <el-switch
                v-model="switch45"
                @change="GPP_WriteInteger(406,switch45)"
                active-color="#508DE2"
            />
            <span class="gray">{{$t('video.text2')}}</span>
        </div>
        <div class="video-replay-box">
            <span class="gray">{{$t('video.instantReplayTime')}}</span>

            <div class="w-280">
                <el-slider
                    tooltip-class="cus"
                    @change="setInteger56"
                    :format-tooltip="formatSliderTooltip"
                    :min="15" :step="1" :marks="marks" :max="300"
                    v-model="sliderVal"
                />
            </div>
        </div>
    </div>
</template>

<style scoped lang="scss">
.video-replay-wrap {
    position: absolute;
    left: 486px;
    top: 310px;
    width: 570px;
    height: 120px;
    font-size: 12px;

    .switch-line {
        display: flex;
        gap: 10px;
        align-items: center;
        height: 60px;
        padding-left: 12px;
    }

    .gray {
        color: #777777;
    }

    .video-replay-box {
        width: 570px;
        height: 85px;
        background: #2B2C37;
        border-radius: 4px;
        box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px;

        .w-280 {
            width: 280px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
    }
}
</style>
