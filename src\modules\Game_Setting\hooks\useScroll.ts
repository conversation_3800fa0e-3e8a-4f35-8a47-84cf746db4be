import {inject, nextTick, onMounted} from "vue";

// 监听滚动事件，当滚动到某个元素时，改变导航栏样式
export const useScroll = (el:string)=>{
  let NavList:any = inject('NavList');
  let element: HTMLElement | null = null;
  function handleScrollx() {
    if (!window.ToAnchorPoint) {
      const t = element!.getBoundingClientRect().top
      if (t <= 50 && t > -50){
        NavList.value.forEach((item:any)=>{
          item.active = false
          if (item.target == '#'+el) {
            item.active = true
          }
        })
      }
    }
  }
  onMounted(()=>{
    nextTick(()=>{
      element = document.getElementById(el)
      window.addEventListener('scroll',handleScrollx,true)
    })
  })
}
