// function createTimestampArray() {
//     const timestamps = [];
//     for (let minute = 0; minute <= 5; minute++) {
//       for (let second = 0; second < 60; second += 5) {
//         const formattedSecond = second < 10 ? `0${second}` : second;
//         const formattedMinute = minute < 10 ? `0${minute}` : minute;
//         const timestamp = `${formattedMinute}:${formattedSecond}`;
//         timestamps.push(timestamp);
//       }
//     }
//     return timestamps.filter((_, index) => index % 12 !== 0); // 移除每分钟的第0秒（即重复的分钟标记）
//   }
  
//   console.log(createTimestampArray());
function createTimestampArray(interval = 2) {  //生成x轴刻度线
    if (interval < 1 || interval > 59) {
      console.error("Interval must be between 1 and 59 seconds.");
      return [];
    }
  
    const timestamps = [];
    for (let minute = 0; minute <= 5; minute++) {
      for (let second = 0; second < 60; second += interval) {
        const remainingSeconds = 60 - second;
        if (remainingSeconds < interval) break; // 避免超过5分钟界限
        const formattedSecond = second < 10 ? `0${second}` : second;
        const formattedMinute = minute < 10 ? `0${minute}` : minute;
        const timestamp = `${formattedMinute}:${formattedSecond}`;
        timestamps.push(timestamp);
      }
    }
    return timestamps;
  }
  
  // 使用示例：
  console.log(createTimestampArray(2)); // 5秒间隔
//   console.log(createTimestampArray(3)); // 3秒间隔
  