<template>
    <div class="desktop-monitor-setting">
        <window-header :window-name="windowName" :maximizeIcon="false" :minimize-icon="false">
            <span class="windowName" style="margin-right: 10px;">{{ $t('DesktopMonitoring.desktopMonitoring') }}</span>
            <span>V{{ version }}</span>
        </window-header>

        <div class="mainContent">
            <div class="mainSwitch" v-show="activeRoute === 'chooseMonitor'">
                <span class="title">{{ $t('DesktopMonitoring.desktopMonitoring') }}</span>
                <el-switch size="small" v-model="mainSwitch" @change="changeMainSwitch"/>
            </div>
            <div class="goBack" v-show="activeRoute === 'MonitorSet'">
                <el-button type="text" @click="goBack">
                    <el-icon><ArrowLeftBold/></el-icon>
                    <span>{{ $t('GamePlusOne.Return') }}</span>
                </el-button>

                <div class="import-export">
                    <div class="import-button">
                    <button @click="exportDesktopMonitor" class="Sensor-buttonMove Sensor-buttonMove2 hover">
                        <el-icon style="color: #3579d5;font-size: 17px;"><Upload /></el-icon>
                        <span class="guide">{{ $t('messages.export') }}</span>
                    </button>
                    </div>
                    <div class="import-button">
                    <label for="fileInput" class="custom-file-input Sensor-buttonMove Sensor-buttonMove2 hover">
                        <el-icon style="color: #3579d5;font-size: 17px;"><Download /></el-icon>
                        <span class="guide">{{ $t('messages.import') }}</span>
                    </label>
                    <input id="fileInput" type="file" accept=".json"  @change="importDesktopMonitor" style="display: none;"/>
                    </div>
                </div>
                
            </div>
            <ChooseMonitor @edit="activeRoute = 'MonitorSet'" v-if="activeRoute === 'chooseMonitor'"/>
            <MonitorSet  :zoomValue = zoomValue v-if="activeRoute === 'MonitorSet'"/>
        </div>
    </div>
</template>

<script setup lang="ts">
import WindowHeader from "@/components/mainCom/windowHeader.vue";
import ChooseMonitor from "./components/ChooseMonitor.vue";
import MonitorSet from "./components/MonitorSet.vue";
import {ArrowLeftBold,Download,Upload} from "@element-plus/icons-vue";
import { ElMessage } from 'element-plus';
import {GPP_GetInteger, GPP_WriteInteger} from "./utils/public";
import {nextTick, onBeforeMount, ref, watch} from "vue";
import useSensorData from './shared/useSensorData'
import idb from '@/uitls/indexedDB'
import { Sensor } from '../Game_DMComponent/sharedTypes';
import { useLanguage } from '@/uitls/useLanguage';
useLanguage()
// @ts-ignore
const gamepp: any = window.gamepp;
const windowName = 'desktop_monitor_setting'
const mainSwitch = ref(false)
const version = ref('')
const zoomValue = ref(1)
const activeRoute = ref("chooseMonitor")
const goBack = () => {
    activeRoute.value = 'chooseMonitor'
    if (gamepp.webapp.windows.isValid.sync('desktop_monitor')) {
        gamepp.webapp.sendInternalAppEvent.promise('desktop_monitor', 'selectedIdfalse');
    }
}
async function changeMainSwitch() {
    await GPP_WriteInteger(173, mainSwitch.value ? 1 : 0)
    let jsonState = gamepp.hardware.getJSONState.sync();
    if (jsonState === 0) {
        ElMessage.error('请等待硬件信息加载完成~~');
        return
    }
    if (mainSwitch.value) {
        if (gamepp.webapp.windows.isVisible.sync('desktop_monitor') === false) {
            await gamepp.webapp.windows.show.promise('desktop_monitor');
        }
    }else{
        if (gamepp.webapp.windows.isVisible.sync('desktop_monitor') === true) {
            await gamepp.webapp.windows.close.promise('desktop_monitor');
        }
    }
}

async function getVersion() {
    try {
        const versionObj: any = await gamepp.package.getversion.promise("DesktopMonitor")
        if (Object.prototype.toString.call(versionObj) === '[object Object]' && 'version' in versionObj) {
            version.value = versionObj.version;
        } else {
            version.value = gamepp.getPlatformVersion.sync()
        }

    } catch (e) {
        console.log(e)
    }
}

const activeId = ref(1)
const sensorsData = ref<Sensor[]>([]);
// 导出数据
const exportDesktopMonitor = async() => {
    const savedId = localStorage.getItem('activeMonitorId');
    console.log(savedId,'activeMonitorId')
    if (savedId) {
        activeId.value = parseInt(savedId);
    }
    const storedData = localStorage.getItem(`sensorSettings_${activeId.value}`);
    sensorsData.value = storedData ? JSON.parse(storedData) : [];
    let window_settingData = await idb.getItem(`custom_monitor_window_setting_${activeId.value}`)
    const images = {};
    for (const sensor of sensorsData.value) {
        if ((sensor.type2 === 'img' || sensor.type2 === 'video') && sensor.mediaSrc) {
            const isBase64 = sensor.mediaSrc.startsWith('data:');
            if (!isBase64) {
                try {
                    const response = await fetch(sensor.mediaSrc);
                    const blob = await response.blob();
                    const base64 = await new Promise((resolve) => {
                        const reader = new FileReader();
                        reader.onloadend = () => resolve(reader.result);
                        reader.readAsDataURL(blob);
                    });
                    images[sensor.id] = base64;
                } catch (error) {
                    console.error('转换图片失败:（项目里的不用转化，谁导入都能显示）', error);
                }
            }
        }
    }
    // 只处理用户上传的布局背景图片
    if (window_settingData?.background?.img || window_settingData?.background?.img_video_name) {
        try {
            const response = await fetch(window_settingData.background.img);
            const blob = await response.blob();
            const base64 = await new Promise((resolve) => {
                const reader = new FileReader();
                reader.onloadend = () => resolve(reader.result);
                reader.readAsDataURL(blob);
            });
            images['layout_background'] = base64;
        } catch (error) {
            console.error('转换布局背景图片失败:', error);
        }
    }
    //合并数据
    const exportData = {
        // version: '2.0',
        timestamp: new Date().toISOString(),
        activeMonitorId: activeId.value,
        data: sensorsData.value,
        layout: window_settingData,
        images: images 
    };
    const dataStr = JSON.stringify(exportData, null, 2);
    const blob = new Blob([dataStr], { type: 'application/json' });
    const href = URL.createObjectURL(blob);
    // 创建一个隐藏的 <a> 元素来触发下载
    const link = document.createElement('a');
    link.href = href;
    link.download = `sensorSettings.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(href);
};

// 导入数据
const importDesktopMonitor = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (!input.files || input.files.length === 0) return;

  const file = input.files[0];
  const reader = new FileReader();

  reader.onload = async (e) => {
    try {
      const content = e.target?.result as string;
      if (!content) {
        ElMessage.error('文件内容为空');
        return;
      }
      const data = JSON.parse(content);
      const activeId = localStorage.getItem('activeMonitorId') || '1';
      if (data.data) {
         // 处理图片数据 渲染images里面base64的图片对应替换
        if (data.images) {
          for (const sensor of data.data) {
            if ((sensor.type2 === 'img' || sensor.type2 === 'video')  && data.images[sensor.id]) {
              sensor.mediaSrc = data.images[sensor.id];
            }
          }
        }
        localStorage.setItem(`sensorSettings_${parseInt(activeId)}`, JSON.stringify(data.data));
        activeRoute.value = '';
        nextTick(() => {
          activeRoute.value = 'MonitorSet';
        });
      }
       // 保存布局设置
      if (data.layout) {
        if (data.images?.layout_background) {
            let layoutCopy = typeof data.layout === "string"  ? JSON.parse(data.layout) : JSON.parse(JSON.stringify(data.layout));
        
            const hasImg = layoutCopy.background.img !== undefined && layoutCopy.background.img !== '';
            const hasImgVideo = layoutCopy.background.img_video_name !== undefined && layoutCopy.background.img_video_name !== '';
            
            if (hasImg && hasImgVideo) {
                layoutCopy.background.img = data.images.layout_background;
                layoutCopy.background.img_video_name = data.images.layout_background;
            } else if (hasImg) {
                layoutCopy.background.img = data.images.layout_background;
            } else if (hasImgVideo) {
                layoutCopy.background.img_video_name = data.images.layout_background;
            } 
            idb.setItem(`custom_monitor_window_setting_${parseInt(activeId)}`, layoutCopy);  
        } else {
            // 没有布局背景图片数据，直接保存原始布局
            idb.setItem(`custom_monitor_window_setting_${parseInt(activeId)}`, data.layout);
        } 
      }

      if(data.activeMonitorId){
        localStorage.setItem('activeMonitorId2', data.activeMonitorId);
      }

      ElMessage.success('导入成功');
      
      // 重新初始化传感器数据
      const store = useSensorData();
      store.initializeSensors(parseInt(activeId));
      
    } catch (error) {
        ElMessage.error('导入失败: 文件格式不正确');
        console.error('导入失败:', error);
    } finally {
      input.value = '';
    }
  };

  reader.onerror = () => {
    ElMessage.error('读取文件失败');
    input.value = '';
  };

  reader.readAsText(file);
};

onBeforeMount(async () => {
    getVersion()
    try {
        const zoomWithSystem = gamepp.setting.getInteger.sync(313) // 是否点了跟随系统缩放开关
        if (zoomWithSystem === 1) {
            zoomValue.value = await gamepp.display.getScaleFromWindowInMonitor.promise();
            gamepp.webapp.windows.resize.sync(windowName, Math.floor(1292 * zoomValue.value), Math.floor(712 * zoomValue.value))
        }
        gamepp.display.onDisplayMetricsChanged.addEventListener(async (scaleFactor: number) => {
            const zoomWithSystem = gamepp.setting.getInteger.sync(313)
            if (zoomWithSystem === 1) {
                zoomValue.value = scaleFactor
                gamepp.webapp.windows.resize.sync(windowName, Math.floor(1292 * zoomValue.value), Math.floor(712 * zoomValue.value))
            }
        })
    } catch {

    }
    mainSwitch.value = await GPP_GetInteger(173) === 1;

})
watch(zoomValue, (newValue) => {
    (document.body.style as any).zoom = newValue + ''
})
</script>

<style scoped lang="scss">
.desktop-monitor-setting {
    width: 1280px;
    height: 700px;
    position: relative;
    border-radius: 4px;
    margin-left: 6px;
    margin-top: 6px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, .6);
}

.mainContent {
    background: #22232e;
    height: 660px;
    width: 1280px;
    padding: 10px;
    font-size: 12px;
    color: #ffffff;

    .mainSwitch {
        display: flex;
        height: 24px;
        color: #ffffff;
        line-height: 24px;

        .title {
            margin-right: 10px;
        }
    }
    .goBack{
        display: flex;
        justify-content: space-between; 
        align-items: center;
    }
    .import-export{
        display: flex;
        align-items: center;
        gap: 10px;
        .Sensor-buttonMove {
            cursor: pointer;
        }
    }
    .import-button {
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .Sensor-buttonMove2{
        background: transparent !important;
    }
    .Sensor-buttonMove{
        border: none;
        padding: 0 16px 0 10px;
        height: 30px;
        background: #343647;
        color: #fff;
        border-radius: 2px;
        display: flex;
        align-items: center;
        .guide {
            margin-left: 5px;
            color: #3579d5;
        }
    }
}
</style>
