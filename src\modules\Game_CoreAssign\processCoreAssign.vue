<script setup lang="ts">
import {processCoreAssignStore} from "./stores";
import {ElLoading} from "element-plus";
import CustomHeader from "./components/customHeader.vue";
import {onMounted, ref, watch} from "vue";
import LeftLayout from "./components/left-layout.vue";
import RightLayout from "./components/right-layout.vue";
import AddProcessDialog from "./components/addProcessDialog.vue";
import ChooseCPUSetDialog from "./components/chooseCPUSetDialog.vue";
import { useLanguage } from '@/uitls/useLanguage';
useLanguage()
import {useI18n} from "vue-i18n";
import i18n from "@/assets/lang";

const I18n = useI18n();
const $store = processCoreAssignStore();
// @ts-ignore
const gamepp = window.gamepp;
const zoomValue = ref<number>(1)
const version = ref('')

// window.addEventListener('beforeunload', function (event) {
//     console.log('执行uninitAllProcessDetail')
//     gamepp.uninitAllProcessDetail.promise()
// })

onMounted(() => {
    init();
    initZoom();
    initLang();
    $store.init()
    getVersion();
    checkLocal();
});

function checkLocal() {
    const local_process = window.localStorage.getItem('local_process')
    if (local_process) {
        $store.display.local_process = JSON.parse(local_process)
    }
}

function initLang() {
    setLang()
    // 监听语言切换
    gamepp.setting.onConfigChanged.addEventListener((type:any, id:number, value:any) =>
        {
            if (id === 242) {
                setLang()
            }
        }
    )
}

function setLang() {
    try{
        i18n.global.locale.value = gamepp.getLanguage.sync().toUpperCase() || 'CN';
    }catch (e) {
        i18n.global.locale.value = 'CN'
    }
}

const init = () => {
    try {
        gamepp.webapp.windows.setMinimumSize.promise('processCoreAssign', 1280, 720)
    } catch (e) {

    }
    $store.getCpuInfo()
    if ($store.display.cpuName === "") {
        const loading = ElLoading.service({ // 加载中
            lock: true,
            text: "Loading",
            background: "rgba(0, 0, 0, 0.7)",
        });
        let t1 = setTimeout(() => {
            loading.close();
        }, 20000);
        let t2 = setInterval(() => { // 检查是否获取到cpu信息
            if ($store.display.cpuName !== "") {
                setTimeout(() => {
                    loading.close();
                }, 1000);
                clearTimeout(t1);
                clearInterval(t2);
            }
        }, 500);
    } else {
        const loading = ElLoading.service({ // 加载中
            lock: true,
            text: "Loading",
            background: "rgba(0, 0, 0, 0.7)",
        });
        setTimeout(() => {
            loading.close();
        }, 2000);
    }
};

async function initZoom() {
    try {
        const zoomWithSystem = gamepp.setting.getInteger.sync(313)
        if (zoomWithSystem === 1) {
            // 设置body zoom
            zoomValue.value = await gamepp.display.getScaleFromWindowInMonitor.promise();
            gamepp.webapp.windows.setMinimumSize.sync('processCoreAssign', Math.floor(1280 * zoomValue.value), Math.floor(720 * zoomValue.value))
            gamepp.webapp.windows.resize.sync('processCoreAssign', 1280, 720)
        }
    } catch (e) {
        zoomValue.value = 1
    }

    try {
        gamepp.display.onDisplayMetricsChanged.addEventListener(async (scaleFactor: number) => {
            const zoomWithSystem = gamepp.setting.getInteger.sync(313)
            if (zoomWithSystem === 1) {
                console.log('display', scaleFactor)
                zoomValue.value = scaleFactor
                try {
                    gamepp.webapp.windows.setMinimumSize.sync('processCoreAssign', Math.floor(1280 * zoomValue.value), Math.floor(720 * zoomValue.value))
                } catch (e) {
                    console.log('gamepp.webapp.windows.resize.sync(gamepp_config)', e)
                }
            }
            gamepp.webapp.windows.resize.sync('processCoreAssign', 1280, 720)
        })
    } catch (e) {
        console.log(e)
    }
}

watch(zoomValue, (newValue) => {
    document.body.style.zoom = newValue+''
    document.documentElement.style.setProperty('--zoomV--', `${newValue}`);
})

async function getVersion() {
    try {
        const versionObj: any = await gamepp.package.getversion.promise("GameCoreAssign")
        if (Object.prototype.toString.call(versionObj) === '[object Object]' && 'version' in versionObj) {
            version.value = versionObj.version;
        } else {
            version.value = gamepp.getPlatformVersion.sync()
        }

    } catch (e) {
        console.log(e)
    }
}
</script>
<template>
    <div class="container">
        <custom-header>
            <div class="page-title">
                <span>{{$t('psc.processCoreAssign')}}</span>
                <span>V {{version}}</span>
            </div>
        </custom-header>
        <div class="content">
            <left-layout />
            <right-layout />
            <add-process-dialog />
            <choose-c-p-u-set-dialog v-if="$store.display.showChooseCPUSetDialog"/>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.container {
  width: var(--page-ww);
  height: var(--page-hh);
  background: #21222d;
  border-radius: 4px;
  position: relative;

  .content {
    width: var(--page-ww);
    height: calc(var(--page-hh) - 40px);
    padding: 10px 19px;
    overflow: hidden;
    position: relative;
    background: #21222d;
    border-radius: 4px;
    box-shadow: 0 1px var(--shadow-size) rgba(0, 0, 0, 0.6);

    display: flex;
    flex-direction: row;
    gap: 10px;
  }
}

.page-title {
  font-size: 12px;
  line-height: 40px;

  span:last-child {
    margin-left: 10px;
  }
}
</style>
<style lang="scss">
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:root {
  --zoomV--: 1;
  --page-w: 100vw;
  --page-ww: calc(var(--page-w) / var(--zoomV--));
  --page-h: 100vh;
  --page-hh: calc(var(--page-h) / var(--zoomV--));
  --shadow-size: 3px;
}

.el-input {
  --el-input-border-color: transparent;
  --el-input-focus-border-color: transparent;
  --el-input-hover-border-color: transparent;
}

.el-input__wrapper {
  background-color: #22232e;
  border: 0;
}

.el-input-group__append {
  background-color: #343647;
  color: #3579d5;
}

.ml-auto {
  margin-left: auto;
}

.flex-items-center {
  display: flex;
  align-items: center;
}

.scroll-y {
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 5px;
  }

  &::-webkit-scrollbar-track {
    background-color: #21222a;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #71738c;
    border-radius: 2px;
  }
}

.scroll-y2 {
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 5px;
  }

  &::-webkit-scrollbar-track {
    background-color: #2d2e39;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #71738c;
    border-radius: 2px;
  }
}

.quick-choose {
  .el-button + .el-button {
    margin-left: 0;
  }
}
</style>
