export interface ListItem {
    title: string;
    data: Record<string, any>;
}

export interface MonitorStyle {
    name: string;
    id: number;
    checked: boolean;
}

export interface FontData {
    postscriptName: string;
    fullName: string;
    family: string;
    style: string;
}

export interface SensorData {
    name: string
    des: string | number
    show: boolean
    hovering: boolean
    group: boolean
    active: boolean
    color: string
    methodWay: number
    fontSize: string
    fontStyle: number
    fontBold: boolean
    outIndex: number
    innerIndex: number
    origin: boolean
    bg? : string
    top: boolean
    lock: boolean
    lineColor: string
    lineHeight: number|null
    lineSize: number
    showOnlyHide: boolean
    title: string,
    unit: string,
    key?:string|number|undefined,
    textShadow: boolean,
    desshow: boolean,
    TitlefontStyle: number,
    titlecolor: string,
    desc?:string,
    notFound: boolean,
    monitor?:{
        switch: boolean,
        condition:string, // 条件
        threshold:number, // 大于/小于 xx% 阈值
        initial_threshold:number, // 初始阈值
        cur_threshold:number, // 当前阈值
        is_update_threshold: boolean, // 触发后是否更新阈值
        wait_time: number, // 等待时间 s
        do_what: "replay"|"screenshot", // 触发功能
        is_show_threshold:boolean, // 是否显示当前用于回放的阈值
        is_init_threshold_big1000:boolean, // 是否显示超过初始阈值1000的次数
    }
    rangeColor?:Array
}

export interface Fps {
    fps: number
    fps01low: number
    fps1low: number
    fpsavg: number
    frameTime: number
    frameTimeList: Array<number>
    processId: number
    runtime: number
    mouseEvent: number
    mouseEventRate: number
}

export interface Overall {
    showWay: number
    position: number
    Alpha: number
    bgColor: string
    fontFamily: string
    fontSize: number
    fontColor: string
    fontStyle: boolean
    site: string
    textShadow: boolean
}
