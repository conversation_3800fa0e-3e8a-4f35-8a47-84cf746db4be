<script setup lang="ts">
import {hardware} from "@/modules/Game_Home/stores";
import {defineProps,ref,reactive,onMounted,onBeforeUnmount,computed,watch} from "vue";
import ChartHW from "@/components/echarts/ChartHW.vue";
import {findValueByKeyAndType, SensorAddAverageData} from "@/uitls/sensor";

const $store = hardware()
const props = defineProps({
  changeMenuInfo: {
    type: Function,
    required: true
  }
})
let loading = ref(true)
let loading_count = 0
const activeCollapse_disk = ref(["1"])
interface diskTemp {
  [propName:string]:any
}
let disk_temp = reactive<diskTemp>({})

onMounted(() => {
  setTimeout(() => {emitChangeFn()}, 100)
  loadingFn();
  listenHardwareChangeMsg()
})

function listenHardwareChangeMsg() {
  const hw = new BroadcastChannel('hw')
  hw.onmessage = (e:any)=>{
    if (e.data.action && e.data.action == 'change') {
      emitChangeFn();
    }
  }
}
const all_disk_size = computed(() => {
  let v = 0;
  if ($store.originData.DRIVES){
    $store.originData.DRIVES.SubNode.forEach((item:any)=>{
      v += parseInt(String(item['DriveCapacity[MB]'] / 1024))
    })
  }
  return v
})
watch(() => $store.bg_sensor_data, () => {
  try {
    recordPECoreCallBack()
  }catch(e){
    console.log(e);
  }
},{deep: true})

async function recordPECoreCallBack() {
  let SensorInfoOriginal: any = await $store.getSensorInfo()
  if (Object.keys(SensorInfoOriginal).length > 1) {
    const SensorInfoKeys = Object.keys(SensorInfoOriginal)
    SensorInfoKeys.forEach((k)=>{
      if (k.includes('S.M.A.R.T.:') && !disk_temp.hasOwnProperty(k)) {
        Object.assign(disk_temp, {[k]: []})
      }
    })
    let SensorInfo = SensorAddAverageData(SensorInfoOriginal, SensorInfoKeys)
    Object.keys(disk_temp).map((k,i)=>{
      const drive_temp = $store.bg_sensor_data.drive.temp_list_all[i]
      if (drive_temp) {
        disk_temp[k].push(drive_temp)
      }else{
        disk_temp[k].push(null)
      }
      const limit = 60
      if (disk_temp[k].length > limit) disk_temp[k].shift()
    })
  }
}
const emitChangeFn = () => {
  if (activeCollapse_disk.value.length === 0) {
    props.changeMenuInfo(4, 72)
  }else {
    if ($store.originData.DRIVES && $store.originData.DRIVES.SubNode.length > 0) {
      let h = 72
      h += Math.ceil($store.originData.DRIVES.SubNode.length /2) * 284
      props.changeMenuInfo(4,h)
    }
  }
}
const loadingFn = () => {
  loading_count++
  if (loading_count > 10) {
    loading.value = false
    return
  }
  if ($store.HwInfo.DRIVES) {
    emitChangeFn()
    loading.value = false
  }else{
    setTimeout(()=>{loadingFn()},1000)
  }
}
</script>

<template>
<div class="HardwareDisk">
  <el-collapse v-model="activeCollapse_disk" @change="emitChangeFn">
    <el-collapse-item :title="$t('hardwareInfo.hardDisk')" name="1" class="as">
      <template #title>
        <div class="HardwareDiskTitle flex-items-center">
          <span class="iconfont icon-Harddisk" style="color: #3579D5;font-size: 24px;margin-right: 5px;"></span>
          <span style="color: #ffffff">{{ $t('hardwareInfo.hardDisk') }}</span>
          <span style="margin-left: 10px;"
                v-if="$store.originData.MEMORY">{{ $t('hardwareInfo.hardDiskCount') }}{{ $store.originData.DRIVES.SubNode.length }}</span>
          <span style="margin-left: 10px;"
                v-if="$store.originData.MEMORY">{{ $t('hardwareInfo.totalSize') }}{{ all_disk_size }}GB</span>

          <span class="ml-auto"></span>
        </div>
      </template>
      <div class="box">
        <template v-if="$store.originData.DRIVES">
          <div
            v-for="(item,index) in $store.originData.DRIVES.SubNode"
            :key="'diskdw'+index">
            <div class="disk" v-if="Object.keys(disk_temp).findIndex(k=>k.includes(item.DriveModel)) !== -1">
              <p>{{item.DriveModel}}</p>
              <ChartHW
                  :data-html="'hardwareInfo.temperature'"
                  unit="℃"
                  :tempWall="65"
                  :y-axis-max="120"
                  :y-axis-value="disk_temp[Object.keys(disk_temp).find((k:string)=>k.includes(item.DriveModel)) as string]"
              ></ChartHW>
            </div>
          </div>
        </template>

      </div>
    </el-collapse-item>
  </el-collapse>
</div>
</template>

<style scoped lang="scss">
.HardwareDisk {
  width: 100%;
  background: rgba(45 ,46 ,57, 0.8);
  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  padding: 0 25px 0 20px;

  .HardwareDiskTitle {
    width: 100%;
    padding-right: 25px;
  }

  .name {
    margin-bottom: 10px;
  }

  .box {
    display: flex;
    flex-flow: row wrap;
    gap: 15px;

    .disk {
      margin-bottom: 20px;
      p {
        margin-bottom: 5px;
      }
    }
  }
}
</style>
