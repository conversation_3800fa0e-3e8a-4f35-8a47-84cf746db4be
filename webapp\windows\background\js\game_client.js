"strict";
// import { gamepp } from "gamepp"
// let sparkMessageListener
let CurrentGameInfo = null, InjectGamePID = 0, is_switch = false;//当前注入游戏数据
let GPP5DatabaseId = null, StartTimestampTable = null, ProcessName = null, ProcessNameImg = null, AUTO_Screenshot_SetInterval = null;
let IN_GAME_MONITORPositionX = -1, IN_GAME_MONITORPositionY = -1;
let IN_GAME_Barrage_ToolPositionX = -1, IN_GAME_Barrage_ToolPositionXY = -1;
let GameProcessInfo = []
let REFRESH_TIME = 0
let InGameProcessInfoSaverInstance = []

//硬件监控序号
let gpu_index = parseInt(window.localStorage.getItem('gpu_index'), 10) || 0;
let drive_index = 0;

//尖峰时刻配置
let HighLight_Game_List = {
    "VALORANT-Win64-Shipping.exe": true, // 瓦洛兰特 无畏契约
    "NarakaBladepoint.exe" : true, // 永劫无间
    "Discovery.exe" : true, // The Finals
    "cod.exe" : true, // COD
    "r5apex.exe" : true, // Apex英雄
    "TslGame.exe" : true, // PUBG
    "CalabiYau.exe" : true, // 卡拉彼丘
    "League of Legends.exe": true, // 英雄联盟
    "b1-Win64-Shipping.exe": true, // 黑神话悟空
    "eldenring.exe": true, // 艾尔登法环
    "Remnant2-Win64-Shipping.exe": true, // 遗迹2
    "WorldOfWarships64.exe": true, // 战舰世界
    "HaloInfinite.exe": true, // 光环无限
    "Hearthstone.exe": true, // 炉石传说
    "Marvel-Win64-Shipping.exe": true, // 漫威争锋
    "DeltaForceClient-Win64-Shipping.exe": true, // 三角洲行动
    "RainbowSix.exe": true, // 彩虹6号
    "FragPunk.exe": true, // 界外狂潮
    "Overwatch.exe": true, // 守望先锋
    "WorldOfWarplanes.exe": true, // 战机世界
    "forhonor.exe": true, // 荣耀战魂
    "Cuphead.exe": true, // 茶杯头
    "sekiro.exe": true, // 只狼
    "DarkSoulsRemastered.exe": true, // 黑魂1
    "DarkSoulsII.exe": true, // 黑魂2
    "DarkSoulsIII.exe": true, // 黑魂3
    "Blasphemous.exe": true, // 渎神1
    "Blasphemous 2.exe": true, // 渎神2
    "aces.exe": true, // 战争雷霆
    "LOP-Win64-Shipping.exe": true, // 匹诺曹
    "nioh.exe": true, // 仁王1
    "nioh2.exe": true, // 仁王2
    "LOTF2-Win64-Shipping.exe": true, // 堕落之主
    "WorldOfTanks.exe": true, // 坦克世界
    "Steelrising.exe": true, // 钢之崛起
    "nightreign.exe": true, // 艾尔登法环：黑夜君临
    "PlagueProject-Win64-Shipping.exe": true, // 记忆边境
    "CodeVein-Win64-Shipping.exe": true, // 嗜血代码
    "BBQ-Win64-Shipping.exe": true, // 第一狂战士：卡赞
    "hyxd.exe": true, // 荒野行动
}

//需要提示无法注入进程列表
//-allow_third_party_software
let Not_Inject_Game_List = ["EscapeFromTarkov.exe", "cs2.exe", "BF2042.exe"]

//切换游戏需要重启窗口列表
let switchwindows = [
    "ingame_main",
    "ingame_monitor",
    "ingame_tipspage",
    "ingame_quit_game_tips",
    "laboratory_tips_ingame",
]

// 游戏消息弹窗列表
let GameMessageTipMap = new Map();

//本次运行激活的进程(黑名单不再激活窗口);
let ActivatedProcessBlack = [], ActivatedProcessInfo = null;

class GameClient {
    sparkMessageListener = null
    _hotkey_RecordHotkey = 0;
    _hotkey_RecordHotkeyCount = 0;
    RecordStartStr = '';
    RecordStopStr = '';
    RecordFrameRateData = null;
    setInterval0 = null;
    MajorFrameRatePageTime = 0;//性能统计页面计数
    RefreshTime = 1000;
    RecordIntervals = 10;//两次记录按键间隔时间
    BG_GPP_PCAwaken_monitor = false;
    BG_GPP_PCAwaken_monitor_type = null;
    GAME_ProcessName = null;
    GPPHwInfoData = '';
    GlobelProcessAccesssData = null
    starInfo = {}

    /*
     进程激活事件
     */
    async AppEvent_OnProcessActived (result) {
        if (ActivatedProcessBlack.includes(result['baseName'])) {return false}//黑名单进程重复激活窗口
        console.log('%cA New Process Active: ', 'color: orange;', result);
        ActivatedProcessInfo = result;
        let isBlackList = await gamepp.database.isValidInBlackList.promise(result.pid,result.baseName,result.execPath);
        let isWhiteList = await gamepp.database.isValidInWhiteList.promise(result.pid, result.baseName, result.execPath);
        //进程名上一级目录
        let DirPathArr = result['execPath'].split("\\");
        let DirUpperPath = DirPathArr[DirPathArr.length - 2];
        if (result.baseName === 'Hades.exe' && DirUpperPath === 'x64Vk') {
            console.log('禁止Hades(VK)注入');
            return false
        }

        if (isBlackList === false || isWhiteList) {
            // 白名单or未收录进程
            let ProcessAccesssData = await gamepp.database.getProcessAccesssData.promise(result.baseName);
            console.warn('进程信息::',ProcessAccesssData);
            this.GlobelProcessAccesssData = ProcessAccesssData
            console.log('ProcessAccesssData', ProcessAccesssData)
            let WorkSetSizeInfo = await gamepp.database.getWorkSetSizeInfo.promise(result.baseName)
            let isActive = await gamepp.game.activeIngameModuleInTargetProcess.promise(result.pid, ProcessAccesssData, WorkSetSizeInfo.wssize, WorkSetSizeInfo.wstimeout);
            console.log('触发注入::isActive',isActive)
        } else {
            //黑名单进程
            console.log(`%cBlacklisted Process Active: ${result.baseName}`, 'color: red');
            ActivatedProcessBlack.push(result['baseName']);
        }

        if (Not_Inject_Game_List.includes(result.baseName)) {
            // 客户端提示
            console.log('---------------客户端提示---------------')
            let is_show = window.localStorage.getItem('noTips_' + result.baseName);//noTips_cs2.exe
            if (result.baseName === 'cs2.exe' && result.commandLine && result.commandLine.includes('-allow_third_party_software')) {
                is_show = 1
            }
            if (Number(is_show) !== 1) {
                window.localStorage.setItem('Not_Inject',result.baseName)
            }
        }
    }

    /*
     监听游戏窗口大小改变
     */
    async AppEvent_OnGameWindowChanged (WindowInfo) {
        console.log(WindowInfo);
        if (!CurrentGameInfo) return false
        CurrentGameInfo['viewportWidth'] = WindowInfo['windowWidth'];
        CurrentGameInfo['viewportHeight'] = WindowInfo['windowHeight'];
        video_client.isInFullScreen = WindowInfo.isInFullScreen;
        if (video_client.is_PREPARE) {
            //游戏窗口更改修改OBS参数
            // video_client.SendObsMessageU_VIDEO();
            console.log('分辨率修改重启OBS录像模块')
            await video_client.setupMediaStartupParameters(true);

            //设置OBS参数
            setTimeout(async () => {
                await video_client.SendObsMessageP_PREPARE();//OBS录制
                setTimeout(() => {
                    video_client.SendObsMessageP_MUTE();//设置麦克风参数
                }, 200);
                setTimeout(() => {
                    video_client.SendObsMessageP_OVERLAY();
                }, 200);
                setTimeout(() => {
                    video_client.SendObsMessageP_CURSOR();
                }, 200);
                if (video_client.has_open_StartReplay === false && gamepp.isDesktopMode.sync() === false) {
                    let is_open_streaming = gamepp.setting.getInteger.sync(COMMANDID.CM_VC_STREAMING_MODE_SWITCH);
                    if (is_open_streaming === 1) {
                        // //开启OBS回溯录像
                        setTimeout(() => {
                            video_client.SendObsMessageP_StartReplay();
                            video_client.has_open_StartReplay = true;
                        }, 1000);
                    }
                }
            }, 3000)
        }
        // 窗口大小改变通知INTELSPARK reset
        let Is_VIDEO_HIGHLIGHT = await gamepp.setting.getInteger.promise(COMMANDID.CM_VIDEO_HIGHLIGHT_SWITCH);
        if (Is_VIDEO_HIGHLIGHT) {
            video_client.SendIntelSparkleMessage_RESET()
        }
    }

    setTimeout0 = null;
    setTimeout1 = null;

    /*
     监听游戏窗口位置改变 监控吸附
     */
    AppEvent_OnWindowPositionChanged (value) {
        if (value['name'] === WindowName.IN_GAME_MONITOR) {
            window.localStorage.removeItem('ingame_monitor_location');
            // console.log(value);
            clearInterval(this.setTimeout0);
            IN_GAME_MONITORPositionX = value['x'];
            IN_GAME_MONITORPositionY = value['y'];
            this.setTimeout0 = setTimeout(async function () {
                console.log(IN_GAME_MONITORPositionX, IN_GAME_MONITORPositionY);
                //是否开启吸附功能
                let is_adsorption = gamepp.setting.getInteger.sync(491)
                if (is_adsorption === 1) {
                    let gameWidth = gamepp.game.getWidth.sync();
                    let XPosition = gameWidth * IN_GAME_MONITORPositionX;
                    let YPosition = 0, isSet = false;
                    if (value['y'] < 0.01) {
                        YPosition = 0;
                        IN_GAME_MONITORPositionY = 0;
                        isSet = true;
                    } else if (value['y'] > 0.975) {
                        let gameHeight = gamepp.game.getHeight.sync();
                        let getBounds = gamepp.webapp.windows.getBounds.sync(WindowName.IN_GAME_MONITOR);
                        YPosition = Number(gameHeight - getBounds['height']);
                        IN_GAME_MONITORPositionY = 0.983
                        isSet = true;
                    } else {
                        IN_GAME_MONITORPositionX = value['x'];
                        IN_GAME_MONITORPositionY = value['y'];
                    }
                    console.log(XPosition, YPosition)
                    if (XPosition < 0) {XPosition = 0, IN_GAME_MONITORPositionX = 0}
                    if (isSet) {await gamepp.webapp.windows.setPosition.promise(WindowName.IN_GAME_MONITOR, XPosition, YPosition);}
                    await gamepp.setting.setFloat.promise(COMMANDID.CM_HWM_HOR_POS_X, IN_GAME_MONITORPositionX)
                    await gamepp.setting.setFloat.promise(COMMANDID.CM_HWM_HOR_POS_Y, IN_GAME_MONITORPositionY)
                } else {
                    await gamepp.setting.setFloat.promise(COMMANDID.CM_HWM_HOR_POS_X, IN_GAME_MONITORPositionX)
                    await gamepp.setting.setFloat.promise(COMMANDID.CM_HWM_HOR_POS_Y, IN_GAME_MONITORPositionY)
                }
            }, 1000);
        } else if (value['name'] === WindowName.Barrage_Tool) {
            clearInterval(this.setTimeout1);
            IN_GAME_Barrage_ToolPositionX = value['x'];
            IN_GAME_Barrage_ToolPositionXY = value['y'];
            this.setTimeout1 = setTimeout(async function () {
                // console.log(IN_GAME_Barrage_ToolPositionX, IN_GAME_Barrage_ToolPositionXY);
                // await gamepp.setting.setString.promise(COMMANDID.CM_BARRGE_WINDOW_POS_X, String(IN_GAME_Barrage_ToolPositionX));
                // await gamepp.setting.setString.promise(COMMANDID.CM_BARRGE_WINDOW_POS_Y, String(IN_GAME_Barrage_ToolPositionXY));
            }, 1000);
        }
    }

    /**
     *游戏连接事件
     */
    async AppEvent_OnNewConnectionArrived (result) {
        //newPid
        await gamepp.utils.uploadGameInfo.promise(result.newPid)
        console.log('%cNew Connection From Game Arrived: ', 'color: green;', result);
        await gamepp.game.ingame.UpdateProcessAccess.promise(result.newPid, this.GlobelProcessAccesssData);
        console.log('UpdateProcessAccess:',result.newPid, this.GlobelProcessAccesssData)
        const BG = new Background();
        let file = result.execPath.split('\\');
        let ProcessName = (file[file.length - 1]);
        this.GAME_ProcessName = ProcessName;
        // let isDisabled = await this.Is_ManuallyBanProcess(ProcessName, false);
        // if (!isDisabled) return false

        //判断是否存在保存的游戏加加配置
        this.IsSaveCurrentGamePPConfig(ProcessName)
        if (ProcessName === 'GPP_PCAwaken.exe') {
            if (gamepp.webapp.windows.isValid.sync("ingame_monitor")) {await gamepp.webapp.windows.close.promise("ingame_monitor");}
            if (this.BG_GPP_PCAwaken_monitor === false) {
                console.log('关闭GPU测试显示游戏内监控');
                await gamepp.game.connectClient.promise(result.newPid);
                return false
            }
        } else {
            if (this.BG_GPP_PCAwaken_monitor && gamepp.webapp.windows.isValid.sync(WindowName.IN_GAME_GPU_BENCHMARK_MONITOR)) {
                await gamepp.webapp.windows.close.promise(WindowName.IN_GAME_GPU_BENCHMARK_MONITOR);
            }
        }
        if (InjectGamePID !== 0 && result.newPid !== InjectGamePID) {
            //另一游戏进程加载 切换游戏
            console.log('另一游戏进程加载');
            if (gamepp.webapp.windows.isVisible.sync("ingame_quit_game_tips")) {
                app.CloseGameProcessTipsCount = 0;
                gamepp.webapp.windows.close.sync("ingame_quit_game_tips");
            }
            this.SwitchGameProgress(result);
        }
        CurrentGameInfo = result;

        //设置游戏激活状态
        await gamepp.hotkey.setGameWindowActiveState.promise(true);

        //设置游戏内显示
        await gamepp.game.ingame.enableOverlay.promise();

        /**
         * 保存进程Icon
         */
        ProcessNameImg = ProcessName.replace('.exe', '.png');
        await gamepp.SaveProcessIconToFile.promise(result.execPath, ProcessNameImg, "image/png");

        await gamepp.game.connectClient.promise(result.newPid);
        InjectGamePID = result.newPid;

        if (ProcessName === 'GPP_PCAwaken.exe') {
            if (this.BG_GPP_PCAwaken_monitor_type === 'benchmark' && this.BG_GPP_PCAwaken_monitor) {
                console.log('GPU测试显示游戏内监控');
                await gamepp.webapp.windows.show.promise(WindowName.IN_GAME_GPU_BENCHMARK_MONITOR);
                return false
            } else if (this.BG_GPP_PCAwaken_monitor_type === 'stress') {
                //未测评显卡,游戏外显示窗口
                let stress_data_str = window.localStorage.getItem('stress_test_data');
                if (stress_data_str !== null && stress_data_str !== 'null' && stress_data_str !== undefined && stress_data_str !== '{}') {
                    let stress_data = JSON.parse(stress_data_str);
                    if (this.BG_GPP_PCAwaken_monitor === 1) {
                        await gamepp.webapp.windows.show.promise('ingame_stress_new');
                        await this.IsReadyShowSendPage('window', 'ingame_stress_new', false);
                        await gamepp.webapp.sendInternalAppEvent.promise('ingame_stress_new', stress_data);
                        await gamepp.webapp.windows.show.promise('ingame_stress_new');

                        //显示BENCHMARK_MONITOR
                        setTimeout(async () => {
                            console.log('显示:' + WindowName.IN_GAME_GPU_BENCHMARK_MONITOR)
                            await gamepp.webapp.windows.show.promise(WindowName.IN_GAME_GPU_BENCHMARK_MONITOR);
                            await this.IsReadyShowSendPage('window', WindowName.IN_GAME_GPU_BENCHMARK_MONITOR, false);
                            await gamepp.webapp.windows.show.promise(WindowName.IN_GAME_GPU_BENCHMARK_MONITOR);
                        }, 2000)
                        return false;
                    }
                }
            }
        }

        //当前进程是否在白名单
        let InWhite_list = await gamepp.database.isValidInWhiteList.promise(result.newPid, ProcessName, result.execPath);

        //初始化游戏内监控
        const isShowIngameHardwareMonitor = await gamepp.setting.getInteger.promise(COMMANDID.CM_ENABLE_HW_MONITING);
        if (isShowIngameHardwareMonitor === 1) {
            let is_limit = false
            if (ProcessName) {
                is_limit = await gamepp.database.queryProcessLimitAccesss.promise(ProcessName, ProcessAcess.GPP_MONITORY_ACCESS);
            }
            if (InWhite_list && is_limit) {
                console.log('功能限制:游戏内监控');
                setTimeout(() => {
                    BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 19, '', 3);
                }, 8000);
            }
            else
            {
                if (!is_switch) {
                    BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 30, '正在启用游戏内监控功能', 1);
                }
                setTimeout(async () => {
                    console.log('正在启用游戏内监控功能')
                    //切换游戏 监控需要重新开启一次
                    if (gamepp.webapp.windows.isVisible.sync(WindowName.IN_GAME_MONITOR)) {
                        await gamepp.webapp.windows.close.promise(WindowName.IN_GAME_MONITOR);
                    }
                    await gamepp.webapp.windows.show.promise(WindowName.IN_GAME_MONITOR);
                }, 1000);
            }
        }

        try {
            if (InWhite_list) {
                let is_limit = false
                if (ProcessName) {
                    is_limit = await gamepp.database.queryProcessLimitAccesss.promise(ProcessName, ProcessAcess.GPP_MONITORY_ACCESS);
                }
                let ingame_message_noshow = window.localStorage.getItem('ingame_message_noshow')
                if (ingame_message_noshow) {
                    ingame_message_noshow = JSON.parse(ingame_message_noshow)
                    ingame_message_noshow = ingame_message_noshow.map(item=>{
                        if (!item.includes('.exe')) {
                            item = item+'.exe'
                        }
                        return item
                    })
                }else{
                    ingame_message_noshow = []
                }
                if (!is_limit && GameMessageTipMap.has(ProcessName) && !ingame_message_noshow.includes(ProcessName)) {
                    const obj = GameMessageTipMap.get(ProcessName)
                    const ingame_message_noshow = window.localStorage.getItem('ingame_message_noshow')
                    let ingame_message_noshow_arr = []
                    if (ingame_message_noshow) {
                        ingame_message_noshow_arr = JSON.parse(ingame_message_noshow)
                    }
                    if (obj.hasOwnProperty('inject') && obj.inject === 1 && !ingame_message_noshow_arr.includes(ProcessName)) {
                        obj.isInGame = true
                        window.localStorage.setItem('GameMessageInGameShow',JSON.stringify(obj))
                        await gamepp.webapp.windows.show.promise('ingame_message')
                        if (obj.hasOwnProperty('time')) {
                            setTimeout(() => {
                                try {
                                    window.localStorage.removeItem('GameMessageInGameShow')
                                    if (gamepp.webapp.windows.isValid.sync('ingame_message')) {
                                        gamepp.webapp.windows.close.promise('ingame_message')
                                    }
                                }catch (e) {

                                }
                            }, obj.time)
                        }
                    }
                    if (obj.hasOwnProperty('exit') && obj.exit === 1) { // 需要退弹显示这个窗口
                        obj.isInGame = false
                        window.localStorage.setItem('GameMessageExitShow',JSON.stringify(obj))
                    }
                }
            }
        }catch (e) {
            console.log(e)
        }

        //判断游戏时是否关闭桌面监控
        const DesktopInGame = await gamepp.setting.getInteger.promise(COMMANDID.CM_DESK_OFF_WHEN_PLAYING);
        if (DesktopInGame === 1)
        {
            gamepp.webapp.windows.close.sync(WindowName.Desktop_Monitor);
            if (!is_switch) {
                // BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 30, '正在关闭桌面监控功能', 1);
            }
        }

        let mirrorArr = [
             "gpp_bright.ini",
             "gpp_gentle.ini",
             "gpp_highlight.ini",
             "gpp_movie.ini",
             "gpp_mingji.ini",
             "gpp_snow.ini",
             "gpp_wutian.ini",
             "gpp_karakin.ini",
             "gpp_tarkovcustoms.ini",
             "gpp_tarkovfactory.ini",
             "gpp_apex.ini",
             "gpp_valorant.ini",
             "gpp_cod16.ini",
             "gpp_rsix.ini",
             "gpp_gw2.ini",
             "gpp_naraka.ini",
             "gpp_naraka_sp.ini",
             "gpp_mythofempire.ini",
             "gpp_genshinimpact.ini",
             "gpp_ai_bright.ini",//19
             "gpp_ai_colorful.ini",
             "ggpp_ai_dim.ini",
             "gpp_ai_balance.ini",
          ]
        //初始化游戏滤镜
        const GameQualityInGame = await gamepp.setting.getInteger.promise(COMMANDID.CM_ENABLE_EFFECT);
        const _hotkey_OpenOrCloseInGameQuality = await gamepp.setting.getString.promise(COMMANDID.CM_HOTKEY_PRESENT_EFFECT_SWITCH_INFO);
        console.warn('滤镜开关热键:',_hotkey_OpenOrCloseInGameQuality,'滤镜开关:',GameQualityInGame);

        if (GameQualityInGame === 1)
        {
            let Mirror_exist  = await gamepp.package.isexists.promise('GameMirror')
            if(Mirror_exist)
            {
                let is_limit = false
                if (ProcessName)
                {
                    is_limit = await gamepp.database.queryProcessLimitAccesss.promise(ProcessName, ProcessAcess.GPP_RESHADE_ACCESS);
                }
                if (InWhite_list && is_limit)
                {
                    console.log('功能限制:游戏滤镜');
                    setTimeout(() => {
                        BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 20, '', 3);
                    }, 8000);
                }
                else
                {
                    const QualityText = await gamepp.setting.getString.promise(COMMANDID.CM_RESHADE_TYPE);
                    let originIndex = 100603
                    let mirrorIndex =  mirrorArr.findIndex(v=>
                    {
                        return v == QualityText
                    })

                    if(mirrorIndex !== -1)
                    {
                        console.warn('已上传,',Number(originIndex+mirrorIndex),'滤镜方案：',QualityText,typeof(QualityText));
                        await gamepp.utils.sendstatics.promise(Number(originIndex+mirrorIndex));
                    }
                    await gamepp.game.ingame.setShaderPreset.promise(QualityText);
                    await gamepp.game.ingame.enableShader.promise();
                }
            }
        }
        else
        {
            gamepp.game.ingame.disableShader.sync();
            setTimeout(() => {
                if (!is_switch) {
                    BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 10, _hotkey_OpenOrCloseInGameQuality, 3);
                }
            }, 4000)
        }

        // 性能统计数据存储
        let Rebound_exist  = await gamepp.package.isexists.promise('GameRebound')
        let isOpenRebound  = await gamepp.setting.getInteger.promise(COMMANDID.CM_PERFORMANCE_OPEN_SWITCH);
        if (isOpenRebound === 1 && Rebound_exist)
        {
            if (!is_switch) {
                BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 30, '正在初始化性能统计功能', 1);
            }
            this.Init_Database(ProcessName, ProcessNameImg, result.execPath,result.newPid);
        }

        //自动截图
        const AUTO_Screenshot_SWITCH = await gamepp.setting.getInteger.promise(COMMANDID.CM_SCREENHOT_AUTO_SWITCH);
        if (!is_switch)
        {
            BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 30, '正在检查自动截图功能', 1);
        }
        if (AUTO_Screenshot_SWITCH === 1)
        {
            let AUTO_INTERVAL_TIME = await gamepp.setting.getInteger.promise(COMMANDID.CM_SCREENHOT_AUTO_INTERVAL_TIME);

            AUTO_Screenshot_SetInterval = setInterval(() => {
                video_client.Hotkey_OnTriggerTakeScreenshot(true);
            }, 1000 * AUTO_INTERVAL_TIME)

        }
        //锁定英文输入法
        const LockEnglishInput = await gamepp.setting.getInteger.promise(COMMANDID.CM_SWITCH_INPUT_METHOD);
        if (LockEnglishInput === 1)
        {
            if (!is_switch) {
                BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 30, '正在锁定英文输入法', 1);
            }
            let status = await gamepp.hotkey.setEnglishInputLock.promise(result.windowHandle, true);
        }
        //智能帧数控制

        let gameBooster = window.localStorage.getItem('game_booster_begin_time');
        let FPSLockValue = await gamepp.opti.getValue.promise(75);
        const lockFpsSwitch = await gamepp.opti.getValue.promise(73);
        if (lockFpsSwitch === 1 && FPSLockValue !== 0 && gameBooster !== null)
        {
            if (!is_switch) {
                BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 30, '正在智能调节游戏帧数', 1);
            }

            await gamepp.game.ingame.lockFPS.promise(FPSLockValue);
        }

        // 开启OBS录像模块
        console.log('开启OBS录像模块')
        await video_client.setupMediaStartupParameters(false);

        //设置OBS参数
        window.setOBSTimer = setTimeout(async () => {
            let Video_On_Off = await gamepp.setting.getInteger.promise(COMMANDID.CM_VC_VIDEO_SWITCH);
            let video_Mode = await gamepp.setting.getInteger.promise(COMMANDID.CM_VR_VIDEO_RELIVE_SWITCH);
            if (Video_On_Off === 1) {
                if (video_Mode === 3) {
                    if (!is_switch) {
                        BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 30, '正在应用OBS录像设置', 1);
                    }

                    await video_client.SendObsMessageP_PREPARE();//OBS录制

                    setTimeout(() => {
                        video_client.SendObsMessageP_MUTE();//设置麦克风参数
                    }, 200);

                    setTimeout(() => {
                        video_client.SendObsMessageP_OVERLAY();
                    }, 200);

                    setTimeout(() => {
                        video_client.SendObsMessageP_CURSOR();
                    }, 200);

                    if (video_client.has_open_StartReplay === false && gamepp.isDesktopMode.sync() === false) {
                        let is_open_streaming = gamepp.setting.getInteger.sync(COMMANDID.CM_VC_STREAMING_MODE_SWITCH);
                        if (is_open_streaming === 1) {
                            // //开启OBS回溯录像
                            setTimeout(() => {
                                video_client.SendObsMessageP_StartReplay();
                                video_client.has_open_StartReplay = true;
                            }, 1000);
                        }
                    }

                }
            }
        }, 3000)

      //开启按键监控
        let keysPressed = {};
        let mousePressed = {};
        let simultaneousKeyPressCount = 0; // 记录同时按下键的数量
         this.sparkMessageListener =async (message) => {
            const { keycode, type } = message;
            const timestamp = Date.now(); // 获取当前时间戳

            if (type === 'keydown') {
                // 如果已经有三个键同时被按下，则忽略新的keydown事件
                if (simultaneousKeyPressCount >= 4) {
                    return;
                }
                if (!keysPressed[keycode]) {
                    // 键之前未被按下，记录keydown并标记为已按下
                    simultaneousKeyPressCount++; // 增加同时按键计数
                    console.log({ ...message, timestamp, simultaneousKeyPressCount }, '按键按下'); // 记录带有时间戳和同时按键计数的keydown
                    keysEventsLog.push({
                        ...message,
                        timestamp,
                        simultaneousKeyPressCount
                    }); // 只有首次按下时才添加到数组
                    keysPressed[keycode] = { pressed: true, timestamp };
                }
                // 注意：如果键已经被按下，我们这里什么都不做，即忽略重复的keydown事件
            } else if (type === 'keyup') {
                if (keysPressed[keycode]) {
                    // 键之前已经被按下，计算间隔并重置状态
                    console.log({ message, timestamp }, '按键释放'); // 记录带有时间戳的keyup
                    const interval = timestamp - keysPressed[keycode].timestamp; // 计算间隔时间
                    keysEventsLog.push({ ...message, timestamp, interval }); // 添加keyup事件到数组
                    // console.log(`按键 ${keycode} 的按下抬起间隔时间: ${interval} 毫秒`);
                    delete keysPressed[keycode]; // 移除keysPressed中的记录
                    simultaneousKeyPressCount--; // 减少同时按键计数
                }
            } else if (type === 'mousedown') {
                // 处理鼠标事件
                // 直接将鼠标事件记录到mouseEventsLog数组
                mousePressed[keycode] = timestamp; // 记录按下时的时间戳
                // console.log('鼠标按下', message, timestamp)
                mouseEventsLog.push({
                    ...message,
                    timestamp
                });
                // if (!gamepp.webapp.windows.isValid.sync('ingame_broadsword')) {
                //     gamepp.webapp.windows.show('ingame_broadsword')
                // }
                if (gamepp.webapp.windows.isValid.sync('ingame_broadsword')) {
                    window.localStorage.setItem('ingame_broadsword', 'mousedown')
                }
            } else if (type === 'mouseup') {
                // 处理鼠标释放事件
                if (mousePressed[keycode]) {
                    const interval = timestamp - mousePressed[keycode]; // 计算按下到释放的间隔时间
                    mouseEventsLog.push({
                        ...message,
                        timestamp,
                        interval // 将间隔时间添加到日志对象
                    });
                    delete mousePressed[keycode]; // 清除记录的按下时间戳
                }
                if (gamepp.webapp.windows.isValid.sync('ingame_broadsword')) {
                    window.localStorage.setItem('ingame_broadsword', 'mouseup')
                }
                // console.log('鼠标抬起', message, timestamp)
            }
        };

        //开启击杀高能录像
        let Is_VIDEO_HIGHLIGHT = false
        let switchGamepad_HighLight = JSON.parse(window.localStorage.getItem('ai_HighLight'));
        if (switchGamepad_HighLight) {
            switchGamepad_HighLight = switchGamepad_HighLight.switchGamepad
            Is_VIDEO_HIGHLIGHT = switchGamepad_HighLight.some(item => item.value === true);
        }

        if (Is_VIDEO_HIGHLIGHT) {
            console.log('开启' + ProcessName + '尖峰时刻')
            if (!is_switch) {
                BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 30, '正在启用超能时刻', 1);
                localStorage.setItem('startHighLight', Date.now().toString())
            }
            console.log('启动按键监听')
            await video_client.SendIntelSparkleMessage_START(HighLight_Game_List[ProcessName], this.sparkMessageListener);
        }

        //预加载游戏内设置面板
        if (!is_switch) {
            BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 30, '正在初始化控制面板功能', 1);
        }
        setTimeout(() => {
            //控制面板快捷键提示
            if (!is_switch) {
                BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 1, gamepp.setting.getString.sync(COMMANDID.CM_HOTKEY_SHOW_MAINUI_INFO), 3);
            }

        }, 3000)

        setTimeout(async () => {
            let isValid = gamepp.webapp.windows.isValid.sync(WindowName.IN_GAME_MAIN);
            if (isValid === false) {
                await gamepp.webapp.windows.show.promise(WindowName.IN_GAME_MAIN, true);
            }
        }, 4000);

        //上传黑白名单数据
        if (InWhite_list === false) {
            this.upload_blackAndwhiteProcess(ActivatedProcessInfo);
        }

        //热键冲突提示
        this.HotkeyConflictsTips(10000);

        //保存注入进程到localStorage  进程注入管理
        let injectionStr = window.localStorage.getItem('process_manage');
        let injection = null;
        if (injectionStr) {injection = JSON.parse(injectionStr);}

        if (injection === null) {
            let data = {};
            data['status'] = 1;
            data['update_time'] = StartTimestampTable;
            data['lastrun_time'] = StartTimestampTable;
            data['lastrun_date'] = this.Get_Visualization_Date();
            data['name'] = ProcessName.replace('.exe','');
            let Key = {};
            Key[ProcessName] = data;
            let mergeObj = JSON.stringify({ ...injection, ...Key });
            window.localStorage.setItem('process_manage', mergeObj)
        } else {
            if (!injection[ProcessName]) {
                let data = {};
                data['status'] = 1;
                data['update_time'] = StartTimestampTable;
                data['lastrun_time'] = StartTimestampTable;
                data['lastrun_date'] = this.Get_Visualization_Date();
                data['name'] = ProcessName.replace('.exe','');
                let Key = {};
                Key[ProcessName] = data;
                let mergeObj = JSON.stringify({ ...injection, ...Key });
                window.localStorage.setItem('process_manage', mergeObj)
            } else {
                if (injection[ProcessName]) {
                    injection[ProcessName]['lastrun_time'] = StartTimestampTable;
                    injection[ProcessName]['lastrun_date'] = this.Get_Visualization_Date();
                    injection[ProcessName]['name'] = ProcessName.replace('.exe','');
                }
                let mergeObj = JSON.stringify(injection);
                window.localStorage.setItem('process_manage', mergeObj);
            }
        }

        const open_ingame_broadsword = async function(){
            let ai_master
            try{
                ai_master = JSON.parse(window.localStorage.getItem('ai_master'));
            }catch (e) {
                ai_master = null;
            }
            console.warn(ai_master)
            console.warn(HighLight_Game_List[ProcessName])
            let GppConfigAIMaster;
            try{GppConfigAIMaster = gamepp.setting.getInteger.sync(522)}catch(e){
                GppConfigAIMaster=0
            }
            let enableAIMaster = !!(GppConfigAIMaster && GppConfigAIMaster == 1);
            console.log('enableAIMaster',enableAIMaster)
            if (ai_master && ProcessName === 'NarakaBladepoint.exe' && ai_master.KD_switch && ai_master.switchGamepad.some(item=>item.value) && enableAIMaster) {

                try {
                    await gamepp.webapp.windows.show.promise('ingame_broadsword');
                    setTimeout(async ()=>{
                        if (!gamepp.webapp.windows.isValid.sync('ingame_broadsword')) {
                            await gamepp.webapp.windows.show.promise('ingame_broadsword');
                        }
                    },1000);
                    console.log('SHOW:', 'ingame_broadsword')
                } catch (e) {
                    console.error('SHOW error:', 'ingame_broadsword')
                }
                try {
                    await gamepp.webapp.windows.show.promise('ingame_kdTips');
                    console.log('SHOW:', 'ingame_kdTips')
                } catch (e) {
                    console.error('SHOW error:', 'ingame_kdTips')
                }

            } else {

                if (gamepp.webapp.windows.isValid.sync('ingame_broadsword')) {
                    try {
                        await gamepp.webapp.windows.close.promise('ingame_broadsword');
                    } catch (e){}
                }
                if (gamepp.webapp.windows.isValid.sync('ingame_kdTips')) {
                    try {
                        await gamepp.webapp.windows.close.promise('ingame_kdTips');
                    } catch (e) {

                    }
                }

            }
        }

        const open_ingame_highlightsavetip = async function() {
            let ai_HighLight
            try{
                ai_HighLight = JSON.parse(window.localStorage.getItem('ai_HighLight'));
            }catch (e) {
                ai_HighLight = null;
            }
            const gamelist = {
                "VALORANT-Win64-Shipping.exe": 'valorant',
                "NarakaBladepoint.exe" : 'naraka',
                "Discovery.exe" : 'thefinals',
                "cod.exe" : "COD20", // COD
                "r5apex.exe" : "APEX", // Apex英雄
                "TslGame.exe" : "PUBG", // PUBG
                "CalabiYau.exe" : "CalabiYau", // 卡拉彼丘
                "League of Legends.exe": "lol",
                "b1-Win64-Shipping.exe": "bwk",
                "eldenring.exe": 'EDR', // 艾尔登法环
                "Remnant2-Win64-Shipping.exe": 'RMNT2', // 遗迹2
                "WorldOfWarships64.exe": 'WOWS', // 战舰世界
                "HaloInfinite.exe": 'HALO', // 光环无限
                "Hearthstone.exe": 'HS', // 炉石传说
                "Marvel-Win64-Shipping.exe": 'MR', // 漫威争锋
                "DeltaForceClient-Win64-Shipping.exe": 'DF', // 三角洲行动
                "RainbowSix.exe": 'SIX', // 彩虹6号
                "FragPunk.exe": 'FP', // 界外狂潮
                "Overwatch.exe": 'ow', // 守望先锋
                "WorldOfWarplanes.exe": 'wowp', // 战机世界
                "forhonor.exe": 'forhonor', // 荣耀战魂
                "Cuphead.exe": 'Cuphead', // 茶杯头
                "sekiro.exe": 'sekiro', // 只狼
                "DarkSoulsRemastered.exe": 'darksoul1', // 黑魂1
                "DarkSoulsII.exe": 'darksoul2', // 黑魂2
                "DarkSoulsIII.exe": 'darksoul3', // 黑魂3
                "Blasphemous.exe": 'blasphemous', // 渎神1
                "Blasphemous 2.exe": 'blasphemous2', // 渎神2
                "aces.exe": 'warthunder', // 战争雷霆
                "LOP-Win64-Shipping.exe": 'pinocchio', // 匹诺曹
                "nioh.exe": 'nioh', // 仁王1
                "nioh2.exe": 'nioh2', // 仁王2
                "LOTF2-Win64-Shipping.exe": 'LOTF2', // 堕落之主
                "WorldOfTanks.exe": 'WorldOfTanks', // 坦克世界
                "Steelrising.exe": 'Steelrising', // 钢之崛起
                "nightreign.exe": 'nightreign', // 艾尔登法环：黑夜君临
                "PlagueProject-Win64-Shipping.exe": 'PlagueProject', // 记忆边境
                "CodeVein-Win64-Shipping.exe": 'CodeVein', // 嗜血代码
                "BBQ-Win64-Shipping.exe": 'BBQ', // 第一狂战士：卡赞
                "hyxd.exe": 'hyxd', // 荒野行动
            }
            console.log(ProcessName,HighLight_Game_List[ProcessName],ai_HighLight)
            if (ai_HighLight && HighLight_Game_List[ProcessName] &&
              ai_HighLight.switchGamepad.find(item=> item.value && item.enname.toLowerCase() === gamelist[ProcessName].toLowerCase())) {
                console.log('sa65d456ad465a4d56asdadadad')
                await gamepp.webapp.windows.show.promise('ingame_highlightsavetips');
            }else{
                if (gamepp.webapp.windows.isValid.sync('ingame_highlightsavetips')){
                    await gamepp.webapp.windows.close.promise('ingame_highlightsavetips');
                }
            }
        }

        // 开开关时执行
        function open_ingame__listen(msg) {
            if (msg === 'open_ingame_broadsword') open_ingame_broadsword();
            if (msg === 'open_ingame_highlightsavetip') open_ingame_highlightsavetip();
        }

// let obj = structuredClone()

        setTimeout(()=>
        {
            open_ingame_broadsword();
            open_ingame_highlightsavetip();
        },1000)

        if (window.open_ingame__listenId) gamepp.webapp.onInternalAppEvent.removeEventListener(window.open_ingame__listenId)
        window.open_ingame__listenId = gamepp.webapp.onInternalAppEvent.addEventListener(open_ingame__listen)
    }

    Get_Visualization_Date ()
    {
        const date = new Date()
        const year = date.getFullYear().toString()
        const month = (date.getMonth() + 1).toString().padStart(2, '0')
        const day = date.getDate().toString().padStart(2, '0')
        const hour = date.getHours().toString().padStart(2, '0')
        const minutes = date.getMinutes().toString().padStart(2, '0')
        const nowDate = `${year}-${month}-${day} ${hour}:${minutes}`
        return nowDate
    }

    async HotkeyConflictsTips (time) {
        let data = window.localStorage.getItem('conflict_hotkey');
        if (data !== null && data !== '[]' && data !== "") {
            let conflictKey = JSON.parse(data);
            let ObjArr = Object.keys(conflictKey);
            if (ObjArr.length !== 0) {
                setTimeout(async () => {
                    const BG = new Background();
                    if (!is_switch) {
                        await BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 33, '', 5);
                    }
                }, time)
            }
        }
    }

    async Init_Database (processpath, icon,execPath,newPid) {
        let AppDataDir = await gamepp.getAppDataDir.promise();
        GPP5DatabaseId = await gamepp.database.open.promise(AppDataDir + '\\common\\Data\\GamePP5.dll');
        gameclient.AddDataToGamePP_BaseInfo(processpath,icon,execPath,newPid);
    }
    //检查激活游戏进程
    checkActiveGame (newPid,StartTimestampTable)
    {
        if(GameProcessInfo.length>0)
        {
            let exist =  GameProcessInfo.some((v)=>
                {
                    return  v.pid == newPid
                })

                if(!exist)
                {
                    let obj =
                    {
                        time:StartTimestampTable,
                        pid:newPid
                    }
                    GameProcessInfo.push(obj)
                }
        }
        else
        {
            let obj =
            {
                time:StartTimestampTable,
                pid:newPid
            }
            GameProcessInfo.push(obj)
        }
            console.log('当前激活进程：',GameProcessInfo);
    }

    async AddDataToGamePP_BaseInfo (processpath, icon,execPath,newPid) {
        let alreadyHave = false
        if (InGameProcessInfoSaverInstance.findIndex(item=>item.gamePid === newPid) !== -1) {
            return
        }
        console.log(processpath, icon,execPath)
        StartTimestampTable = Date.parse(new Date()) / 1000; //记录表名

        this.checkActiveGame(newPid,StartTimestampTable)

        const igpisinstance = new InGameProcessInfoSaver();
        InGameProcessInfoSaverInstance.push(igpisinstance)
        igpisinstance.gamePid = newPid; // 记录当前游戏进程pid
        igpisinstance.tableName = StartTimestampTable+'_process'; // 记录表名

        let driveLetter = ''
        try {
            let driveLetterMatch = execPath.match(/^[a-zA-Z]/);
            driveLetter = driveLetterMatch ? driveLetterMatch[0].toUpperCase() : '';
        } catch {}

        //找到驱动盘 硬盘名称
        let DiskName = await this.findDiskNameForLetter(driveLetter)
        console.log(DiskName)
        let DiskNameInfo = ''
        if (DiskName)
        {
            delete DiskName['partitionSize']
            delete DiskName['diskNum']
            DiskNameInfo = encodeURIComponent(JSON.stringify(DiskName))
            console.log('当前游戏运行在' + DiskName.partitionName + '盘, ' + DiskName.diskName)
            window.localStorage.setItem('game_run_disk',JSON.stringify(DiskName))
            //修改 disk_index为游戏所在盘
            let SensorInfoStr = await gamepp.hardware.getSensorInfo.promise();
            if (SensorInfoStr)
            {
                let SensorInfo = JSON.parse(SensorInfoStr);
                let SensorInfoKeys = Object.keys(SensorInfo);
                const driveArr = SensorInfoKeys.filter(item => item.includes("Drive:"));
                console.log(driveArr)
                const lowerCaseDiskName = DiskName.diskName.toLowerCase();
                let driveIndex = 0;
                for (let i = 0; i < driveArr.length; i++) {
                    const lowerCaseDriveKey = driveArr[i].toLowerCase();
                    if (lowerCaseDriveKey.includes(lowerCaseDiskName)) {
                        driveIndex = i;
                        break;
                    }
                }
                if (driveIndex) {
                    drive_index = driveIndex
                }
                console.log(driveIndex)
            }
        }

        if (!alreadyHave) await gamepp.database.insert.promise(GPP5DatabaseId, "GamePP_BaseInfo", ["starttime", "processpath", "icon", "exec_path"], [[StartTimestampTable, processpath, icon, DiskNameInfo]]);
        localStorage.setItem('pointmark',JSON.stringify(0))
        //创建详情数据表
        let DetailedStatus = await gamepp.database.exists.promise(GPP5DatabaseId, StartTimestampTable);
        if (!DetailedStatus) {
            await gamepp.database.create.promise(GPP5DatabaseId, "'" + StartTimestampTable + "'", '("id"INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,"cpufan"INTEGER,"gpufan"INTEGER,"recordTime"INTEGER,"pageFaultDelta"INTEGER,"clip_path"INTEGER,"clip_id"INTEGER,"ddr5_voltage"INTEGER,"ddr5_temp"INTEGER,"pointmark"INTEGER,"fps"INTEGER,"fps1"INTEGER,"fps01"INTEGER,"memory"INTEGER,"memorytemperature"INTEGER,"cpuload"TEXT,"cpu_load_core"TEXT,"cpuloadP"INTEGER,"cpuloadE"INTEGER,"cputemperature"INTEGER,"cpu_temperature_core"TEXT,"cpuclock"INTEGER,"cpuclockP"INTEGER,"cpuclockE"INTEGER,"cpu_clock_core"TEXT,"cpu_clock_effective_core"TEXT,"cpuclockAVG"TEXT,"cpupower"INTEGER,"cpuvoltage"INTEGER,"gpuload"TEXT,"gpuload1"TEXT,"gputemperature"TEXT,"gpuhotspottemp"INTEGER,"gpuclock"TEXT,"gpupower"TEXT,"gpumemoryload"INTEGER,"gpumemorytemp"INTEGER,"gpumemoryclock"INTEGER,"gpuvoltage"INTEGER,"disk_temp"INTEGER,"disk_load"INTEGER,"frametime"INTEGER,"performance"TEXT,"performance_gpu"TEXT,"amd_cpu_thermal"INTEGER,"amd_gpu_thermalhotspot"INTEGER,"amd_gpu_thermalmemory"INTEGER)');
        }
        //创建进程详情数据表
        let DetailedStatus_process = await gamepp.database.exists.promise(GPP5DatabaseId, StartTimestampTable+'_process');
        if (!DetailedStatus_process && !alreadyHave) {
            await gamepp.database.create.promise(GPP5DatabaseId, "'" + StartTimestampTable+'_process' + "'", '("id"INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,"cpupp"TEXT,"gpupp"TEXT,"mempp"TEXT)');
        }

        //添加数据到详情表
        // const PERFORMANCE_RECORD_TIME = await gamepp.setting.getInteger.promise(COMMANDID.CM_PERFORMANCE_RECORD_TIME);
        // gamepp.game.ingame.onMajorFrameRateUpdated.addEventListener(GameInfo => gameclient.GPP_UpdateGPP5Data(GameInfo,PERFORMANCE_RECORD_TIME));//每秒回调一次

        // let RefreshTime = gamepp.setting.getInteger(450);
        this.RefreshTime = await gamepp.setting.getInteger.promise(450);
        // if (!isNaN(this.RefreshTime) || this.RefreshTime === '' || this.RefreshTime === -1) {this.RefreshTime = 1000}

        let FreReducePresent = 99;

        let HWInfoJSONState = await gamepp.hardware.getJSONState.promise();
        let saveEffectiveCore = false
        if (HWInfoJSONState === 1) {
            let HwInfoJsonStr = await gamepp.hardware.getBaseJsonInfo.promise();
            this.GPPHwInfoData = JSON.parse(HwInfoJsonStr);

            let CPUType = this.GPPHwInfoData.CPU.SubNode[0].ProcessorName
            if (CPUType.includes('AMD'))
            {
                saveEffectiveCore = true
            }
        }

        let lastExecutionTime = 0;
        igpisinstance.startGetAllProcessDetail();
    gamepp.game.ingame.onMajorFrameRateUpdated.addEventListener(GameInfo =>  //接收消息
    {
        if((GameInfo.recordTime - REFRESH_TIME)/1000 >= this.RefreshTime || this.RefreshTime == 100)
        {
            // console.warn('记录时间::',REFRESH_TIME,'GameInfo_recordTime::',GameInfo.recordTime,'数据刷新时间::',this.RefreshTime,'时间间隔::',(GameInfo.recordTime - REFRESH_TIME)/1000+'ms');
            gameclient.GPP_DetailHwInfo(GameInfo, Number(FreReducePresent), saveEffectiveCore);
            REFRESH_TIME = GameInfo.recordTime
        }
    })
    ;//每100ms回调一次
    }

    async findDiskNameForLetter (driveLetter)
    {
        let diskData = await gamepp.getDiskInfo.promise();
        if (diskData) {
            const uppercaseDriveLetter = driveLetter.toUpperCase();
            const matchingDisk = diskData.find(disk => disk.partitionName.startsWith(uppercaseDriveLetter));
            return matchingDisk ? matchingDisk : diskData[0];
        }
        return null;
    }

    async registerClipMsg()
    {
        gamepp.media.onScreenshotTaken.addEventListener(async (MESSAGE) =>
        {
            let PID = MESSAGE.processId
            console.warn('收到截图返回消息:::',MESSAGE);
            let CURINDEX = GameProcessInfo.findIndex((ITEM)=>
            {
                return ITEM.pid == PID
            })
            if(CURINDEX == -1) return
            let curTimeTable = GameProcessInfo[CURINDEX].time
            if(MESSAGE.recordTime)
            {
                await gamepp.database.update.promise(GPP5DatabaseId, "'" + curTimeTable + "'", ['clip_path="' + value.filePath + '"'], 'clip_id = ' + value.recordTime + '');
            }
        });
    }

    getIndexByPID(newPid)
    {
        //获取PID 创建表 检查PID是否有对应的表名
        let curIndex = 0
        curIndex = GameProcessInfo.findIndex((item)=>
        {
            return item.pid == newPid
        })
        return curIndex
    }
    /**
     * 添加详细数据处理
     */
    async GPP_DetailHwInfo (GameInfo, FreReducePresent = 99,saveEffectiveCore)
    {
        let FrameTime = Date.parse(new Date()) / 1000
        //获取当前进程的INDEX
        let PROCESS_INDEX = this.getIndexByPID(GameInfo.processId)
        if(PROCESS_INDEX == -1) return
        let curTimeTable = GameProcessInfo[PROCESS_INDEX].time
        // 自增
        this.MajorFrameRatePageTime += 100;
        /*
         获取间隔时间存储数据
         */
            // console.warn('添加详细数据处理');
            this.MajorFrameRatePageTime = 0
            this.RecordFrameRateData = GameInfo
            //FPS上限限制
            // if (GameInfo.fps <= 10 || GameInfo.fps >= 1000) return false;
            let SensorDataStr = window.localStorage.getItem('bg_sensor_data');
            let SensorData = SensorDataStr ? JSON.parse(SensorDataStr) : null;
            if (SensorData) {
                let cpu_temperature_core = SensorData['cpu']['core_info']['Temp'].join("|");
                let cpu_clock_effective_core = '';
                if (saveEffectiveCore) {
                    let EffectiveClock = SensorData['cpu']['core_info']['effective_clock']
                    for (let key in EffectiveClock) {
                        if (EffectiveClock.hasOwnProperty(key)) {
                            let T1 = EffectiveClock[key].hasOwnProperty('T1') ? EffectiveClock[key].T1 : 0;
                            let average = (EffectiveClock[key].T0 + T1) / (EffectiveClock[key].hasOwnProperty('T1') ? 2 : 1);
                            cpu_clock_effective_core += Math.round(average) + '|'
                        }
                    }
                    cpu_clock_effective_core = cpu_clock_effective_core.slice(0, -1);
                }
                let HWInfo =
                {
                    // 内存
                    "memory"                  : SensorData['memory']['usage'],
                    "memorytemperature"       : SensorData['memory']['temp'],
                    // CPU
                    "cpufan"                  : SensorData['cpu']['fan'],
                    "cpuload"                 : SensorData['cpu']['usage'] + '|',
                    "cpu_load_core"           : SensorData['cpu']['core_info']['Load'].join("|"),
                    "cpuloadP"                : SensorData['cpu']['usageP'],
                    "cpuloadE"                : SensorData['cpu']['usageE'],
                    "cputemperature"          : SensorData['cpu']['temp'],
                    "cpu_temperature_core"    : cpu_temperature_core,
                    "cpuclock"                : SensorData['cpu']['clock'] + '|',
                    "cpuclockP"               : SensorData['cpu']['clockP'],
                    "cpuclockE"               : SensorData['cpu']['clockE'],
                    "cpu_clock_core"          : SensorData['cpu']['core_info']['Clock'].join("|"),
                    "cpu_clock_effective_core": cpu_clock_effective_core,
                    "cpuclockAVG"             : SensorData['cpu']['clock'],
                    "cpupower"                : SensorData['cpu']['power'],
                    "cpuvoltage"              : SensorData['cpu']['voltage'],
                    "amd_cpu_thermal"         : SensorData['cpu']['amd_thermal'],
                    // GPU
                    "gpufan"                  : SensorData['gpu']['fan'],
                    "gpuload"                 : (SensorData['gpu']['d3d_usage']) + '|',
                    "gpuload1"                : (SensorData['gpu']['total_usage']) + '|',
                    "gputemperature"          : (SensorData['gpu']['temp']) + '|',
                    "gpuhotspottemp"          : (SensorData['gpu']['hot_spot_temp']),
                    "gpuclock"                : (SensorData['gpu']['clock']) + '|',
                    "gpupower"                : (SensorData['gpu']['power']) + '|',
                    "gpumemoryload"           : (SensorData['gpu']['mem_usage']) + '|',
                    "gpumemorytemp"           : (SensorData['gpu']['mem_temp']),
                    "gpumemoryclock"          : (SensorData['gpu']['mem_clock']),
                    "gpuvoltage"              : SensorData['gpu']['voltage'] + '|',
                    // 硬盘
                    "disk_temp"               : SensorData['drive']['temp'],
                    "disk_load"               : SensorData['drive']['usage'],

                    "amd_gpu_thermalhotspot"  : SensorData['gpu']['thermal_hotspot'],
                    "amd_gpu_thermalmemory"   : SensorData['gpu']['thermal_memory']
                }
                // PERFORMANCE LIMIT REASONS
                let performance = [];
                let PlrArr = SensorData['cpu']['limit'];
                PlrArr.forEach((itemDetail) => {
                    let infoKey = Object.keys(itemDetail)[0], infoValue = itemDetail[infoKey];
                    if (infoValue === 1 || infoValue >= FreReducePresent) {
                        let performanceObj = {};
                        performanceObj[infoKey] = infoValue;
                        performance.push(performanceObj);
                    }
                })
                let performanceStr = performance.length !== 0 ? JSON.stringify(performance) : '';

                let pointmark = JSON.parse(localStorage.getItem('pointmark'))

                let AutoClip =  localStorage.getItem('ingamePointAutoScreenshot')

                if (!AutoClip)
                {
                    AutoClip = true
                }
                else
                {
                    AutoClip = JSON.parse(AutoClip)
                }

                if(pointmark === 1) //如果进行标记
                {
                    if(AutoClip)
                    {
                        await gamepp.media.takeGameScreenshot.promise('jpg', 0 , 'manually', 'game-screen',0,GameInfo.recordTime);
                        console.warn('完成游戏内性能统计截图');
                        localStorage.setItem('pointmark',JSON.stringify(0))
                    }
                }
                localStorage.setItem('pointmark',JSON.stringify(0))
                let str_1 = SensorData['memory']['ddr5_temp']
                let str_2 = SensorData['memory']['ddr5_voltage']
                let ddr5_Info =
                {
                    temp:str_1['#1']+'|'+str_1['#2']+'|'+str_1['#3']+'|'+str_1['#4'],
                    voltage:str_2['#1']+'|'+str_2['#2']+'|'+str_2['#3']+'|'+str_2['#4'],
                }

                // let src = await
                // console.warn(ddr5_Info);
                // 文件夹名称 = 表名
                // 1725851197//_id=11
                //显卡降频数据保存
                let pl_thermal = +(SensorData['gpu']['pl_thermal'] === 1);
                let Data_Field = ["cpufan","gpufan","recordTime","pageFaultDelta","clip_path","clip_id","ddr5_voltage","ddr5_temp","pointmark","fps", "fps1", "fps01", "frameTime", "memory", "memorytemperature", "cpuload", "cpu_load_core", "cpuloadP", "cpuloadE", "cputemperature", "cpu_temperature_core", "cpuclock", "cpuclockP", "cpuclockE", "cpu_clock_core", "cpu_clock_effective_core", "cpuclockAVG", "cpupower", "cpuvoltage", "gpuload", "gpuload1", "gputemperature", "gpuhotspottemp", "gpuclock", "gpupower", "gpumemoryload", "gpumemorytemp", "gpumemoryclock", "gpuvoltage", "disk_temp", "disk_load", "performance", "performance_gpu", "amd_cpu_thermal", "amd_gpu_thermalhotspot", "amd_gpu_thermalmemory"];
                let Data_Content = [[HWInfo['cpufan'],HWInfo['gpufan'],FrameTime,GameInfo.pageFaultDelta,'',GameInfo.recordTime,ddr5_Info.voltage,ddr5_Info.temp,pointmark,GameInfo.fps, GameInfo.fps1low, GameInfo.fps01low, parseFloat((GameInfo.frameTime).toFixed(1)), HWInfo['memory'], HWInfo['memorytemperature'], HWInfo['cpuload'], HWInfo['cpu_load_core'], HWInfo['cpuloadP'], HWInfo['cpuloadE'], HWInfo['cputemperature'], HWInfo['cpu_temperature_core'], HWInfo['cpuclock'], HWInfo['cpuclockP'], HWInfo['cpuclockE'], HWInfo['cpu_clock_core'], HWInfo['cpu_clock_effective_core'], HWInfo['cpuclockAVG'], HWInfo['cpupower'], HWInfo['cpuvoltage'], HWInfo['gpuload'], HWInfo['gpuload1'], HWInfo['gputemperature'], HWInfo['gpuhotspottemp'], HWInfo['gpuclock'], HWInfo['gpupower'], HWInfo['gpumemoryload'], HWInfo['gpumemorytemp'], HWInfo['gpumemoryclock'], HWInfo['gpuvoltage'], HWInfo['disk_temp'], HWInfo['disk_load'], performanceStr, pl_thermal, HWInfo['amd_cpu_thermal'], HWInfo['amd_gpu_thermalhotspot'], HWInfo['amd_gpu_thermalmemory']]]
                // await gamepp.database.insert.promise(GPP5DatabaseId, "'" + StartTimestampTable + "'", Data_Field, Data_Content);
                await gamepp.database.insert.promise(GPP5DatabaseId, "'" + curTimeTable + "'", Data_Field, Data_Content);
                try
                {
                    let starSensor =  JSON.parse(localStorage.getItem('star_sensor_data'))
                    if(starSensor == null && starSensor.length == 0)
                    {
                        console.warn('无关注传感器');
                    }
                    else
                    {
                        starSensor.forEach(item=>{
                            if (this.starInfo.hasOwnProperty(item.name))
                        {
                            this.starInfo[item.name].data.push(item.value)
                            }else{
                            this.starInfo[item.name] = {
                                name:item.name,
                                data:[item.value],
                                unit:item.unit
                            }
                            }
                        })
                    }
                }
                catch
                {
                }

            }
        // }
    }

    /**
     * 游戏结束后通知 GameOver
     */
    async AppEvent_OnConnectionDropped (result)
     {
        if (window.setOBSTimer) clearTimeout(window.setOBSTimer) // 关闭定时器
        const newPid = result.newPid
        let curIndex = GameProcessInfo.findIndex((item)=>
        {
            return item.pid == newPid
        })

        if (curIndex !== -1) {
            let curTimeTable = GameProcessInfo[curIndex].time
            GameProcessInfo.splice(curIndex,1)
           console.warn('游戏结束当前表名:',curTimeTable);
           console.warn('游戏结束',this.starInfo);
           console.log('%c Game Over Message: ', 'color: #ad1897;', result);
           let file = result.execPath.split('\\');
           let ProcessName = (file[file.length - 1]);
           //退弹
           let EndTime = Date.parse(new Date()) / 1000;
           let isOpenRebound = await gamepp.setting.getInteger.promise(296);
           const TenMinute = 10 * 60;
           //EndTime - StartTimestampTable >= TenMinute
           //2分钟 弹出,10分钟上传
           if (isOpenRebound === 1 && EndTime - curTimeTable >= 120)
            {
               let Rebound_exist  = await gamepp.package.isexists.promise('GameRebound')
               if(!Rebound_exist)return
               console.warn('产生退弹报告：',(EndTime - curTimeTable) + '秒');
               let hd_info_obj = JSON.parse(await gamepp.hardware.getBaseJsonInfo.promise())
                hd_info_obj['cpuComplexInfo'] = await gamepp.getCPUComplexInfo.promise();
               let HD_Info = encodeURIComponent(JSON.stringify(hd_info_obj));
               // console.warn('产生HD_Info：',HD_Info);
               let refreshTime = gamepp.setting.getInteger.sync(450);
               //更新游戏分辨率数据
               let resolutions_Width = CurrentGameInfo['viewportWidth'], resolutions_Height = CurrentGameInfo['viewportHeight'];
               let resolutions = resolutions_Width + '*' + resolutions_Height;
               let GameTime = EndTime - curTimeTable;
               let Data_Field_GameInfo = ['endtime="' + EndTime + '"', 'gametime="' + GameTime + '"', 'hd_info="' + HD_Info + '"', 'refresh_time="' + refreshTime + '"', 'resolutions="' + resolutions + '"', 'dx_version="' + result.dxVersion + '"'];
               let Data_Condition_GameInfo = 'starttime = "' + curTimeTable + '"';
               await gamepp.database.update.promise(GPP5DatabaseId, "GamePP_BaseInfo", Data_Field_GameInfo, Data_Condition_GameInfo);
               console.warn('退弹：上传数据库：', GPP5DatabaseId);
               /***收藏传感器*/
               let star_Info = encodeURIComponent(JSON.stringify(this.starInfo));
               /***天气数据*/
               let WeatherInfo
               let Wearsult = false
               try{
                   WeatherInfo = await gameclient.GetTianqiApiInfo();
                   console.warn('WeatherInfo',WeatherInfo);
               }
               catch{

               }
               try{
                   if (WeatherInfo)
                   {
                       await gamepp.database.update.promise(GPP5DatabaseId, "GamePP_BaseInfo", ['city="' + WeatherInfo['city'] + '"', 'province="' + WeatherInfo['province'] + '"', 'wea="' + WeatherInfo['wea'] + '"', 'wea_img="' + WeatherInfo['wea_img'] + '"', 'tem="' + WeatherInfo['tem'] + '"'], 'starttime = "' + curTimeTable + '"');
                       Wearsult = true
                   }
               }
               catch
               {
                   console.warn('未产生天气报告');
               }

               try
               {
                 await gamepp.database.update.promise(GPP5DatabaseId, "GamePP_BaseInfo", ['starsensorInfo="' + star_Info + '"','city="' + WeatherInfo['city'] + '"', 'province="' + WeatherInfo['province'] + '"', 'wea="' + WeatherInfo['wea'] + '"', 'wea_img="' + WeatherInfo['wea_img'] + '"', 'tem="' + WeatherInfo['tem'] + '"'], 'starttime = "' + curTimeTable + '"');
               }
               catch
               {

               }

               console.warn('退弹：产生天气分析报告：',WeatherInfo);
               let show_rebound_process = true;
               // if (gamepp.setting.getInteger.sync(297) === 1) {show_rebound_process = false;}

               // if (gamepp.setting.getInteger.sync(297) === 0 || gamepp.setting.getInteger.sync(297) === 1)
               if (show_rebound_process)
               {
                   //判断是否取消显示
                   var ShowReboundProcessStr = localStorage.getItem('ShowReboundProcess');
                   if (ShowReboundProcessStr !== null) {
                       var ProcessArr = JSON.parse(ShowReboundProcessStr);
                       for (var i = 0; i < ProcessArr.length; i++) {
                           if (ProcessArr[i]['name'] === ProcessName)
                               {
                                   if (ProcessArr[i]['value'] === 1) {
                                       console.log('当前进程不显示性能分析');
                                       show_rebound_process = false;
                                       break;
                                   }
                               }
                       }
                   }
                   console.log('退弹：性能统计详细表',GPP5DatabaseId, curTimeTable);
                   let exists_table = await gamepp.database.exists.promise(GPP5DatabaseId, curTimeTable);
                   if (exists_table === false) {
                       console.log('性能统计详细表不存在');
                       return false;
                   }
                   // console.warn('退弹：性能统计详细');
                   let rebound_details = 'rebound_details_v2'

                   let is_show = await gamepp.webapp.windows.isVisible.sync(rebound_details);
                   if (is_show) {
                       await gamepp.webapp.windows.close.sync(rebound_details);
                   }
                   console.warn('退弹：开始整理数据：');
                   // let showGameBuffTool = window.localStorage.getItem('showGameBuffTool');
                   let SendObject = {};
                   SendObject['table'] = Number(curTimeTable);
                   SendObject['vip'] = gamepp.user.isVIP.sync();
                   SendObject['not_show'] = Number(0);
                   SendObject['is_open_windows'] = show_rebound_process;
                   SendObject['is_upload'] = EndTime - curTimeTable >= TenMinute;
                   console.log('%c 退弹：生成退弹页面: ', 'color: #ad1897;', SendObject);
                   await gamepp.setting.setBool2.promise('window', rebound_details, false);

                   let Rebound_exist  = await gamepp.package.isexists.promise('GameRebound')
                   if(Rebound_exist)
                   {
                       let result = gamepp.setting.getInteger.sync(297)
                       window.localStorage.setItem('rebound_details_v2_open_by', 'bg');
                       if(result == 1)
                       {
                           window.localStorage.setItem('rebound_details_v2_open_by', 'bg');
                           if (show_rebound_process) gamepp.webapp.windows.show.sync('rebound_details_v2',true)
                           // setTimeout(() => {
                           //     gamepp.webapp.windows.close.sync('rebound_details_v2')
                           // }, 10000);
                       }
                       else
                       {
                           if (show_rebound_process) gamepp.webapp.windows.show.sync('rebound_details_v2',false)
                       }
                   }

                   let Bsend = 0
                   while(Bsend === 0)
                   {
                       if(gamepp.webapp.windows.isValid.sync(rebound_details))
                       {
                           await gamepp.webapp.sendInternalAppEvent.promise(rebound_details, SendObject);
                           Bsend = 1
                           break;
                       }
                       await this.Sleep(1000)
                   }
                   //刷新主程序退弹历史列表
                   if (gamepp.webapp.windows.isVisible.sync('game_rebound'))
                   {
                       setTimeout(async () => {await gamepp.webapp.sendInternalAppEvent.promise('game_rebound', { "action": "RefreshReboundList" });}, 3000);
                   }
               }
           }
           else
           {
               console.log('2分钟 弹出报告,10分钟上传数据')
               console.log('游戏连接时间不足!', (EndTime - curTimeTable) + '秒')
           }
        }


        //弹幕工具


        //手动试验室数据记录中  重新开启提示窗口
        // let is_openLaboratory = await gamepp.webapp.windows.isVisible.promise('laboratory_tips_ingame');
        let is_openLaboratory = JSON.parse(window.localStorage.getItem('laboratoryStatus'));
        if (is_openLaboratory === '1') {
            await gamepp.webapp.windows.close.promise('laboratory_tips_ingame');
            await gamepp.webapp.windows.show.promise('laboratory_tips');
        }

        // await gamepp.webapp.windows.close.promise(WindowName.Barrage_Tool_Ingame);

        //不同游戏保存不同游戏加加配置
        this.SaveCurrentGamePPConfig(result);

        //OBS_UNINIT

        this.UnInitGameInfo();

        //Vulkan 游戏提示

         for (let i = 0; i < InGameProcessInfoSaverInstance.length; i++) {
             const item = InGameProcessInfoSaverInstance[i];
             if (item.gamePid === newPid) {
                 item.stopGetAllProcessDetail()
                 InGameProcessInfoSaverInstance.splice(i, 1)
                 break
             }
         }
    }

    async Sleep(nTimeOut)
    {
        await new Promise((resolve) =>
        {
            setTimeout(resolve, nTimeOut);
        });
    }

    async SaveCurrentGamePPConfig (result) {
        let isOpen = gamepp.setting.getInteger.sync(475);
        if (!isOpen) return;
        // a. 游戏内监控（开关状态、样式、监控内容、字体大小、游戏内坐标）
        // b. 游戏滤镜（开关状态、滤镜方案）
        // c. 显示设置（暗部场景平衡开关状态、百分比值；数字振动开关状态、百分比值）
        // d. 导演模式（开关状态、选项设置）

        let file = result.execPath.split('\\');
        let ProcessName = (file[file.length - 1]);

        let ingame_monitor_id = [{ "type": "int", "id": 3 }, { "type": "int", "id": 177 }, { "type": "int", "id": 18 }, { "type": "int", "id": 19 }, { "type": "int", "id": 20 }, { "type": "int", "id": 21 }, { "type": "int", "id": 22 }, { "type": "int", "id": 32 }, { "type": "int", "id": 36 }, { "type": "int", "id": 37 }, { "type": "int", "id": 38 }, { "type": "int", "id": 39 }, { "type": "int", "id": 33 }, { "type": "int", "id": 34 }, { "type": "int", "id": 23 }, { "type": "int", "id": 24 }, { "type": "int", "id": 25 }, { "type": "int", "id": 26 }, { "type": "int", "id": 30 }, { "type": "int", "id": 31 }, { "type": "int", "id": 72 }, { "type": "int", "id": 155 }, { "type": "int", "id": 152 }, { "type": "int", "id": 201 }, { "type": "int", "id": 200 }, { "type": "int", "id": 202 }, { "type": "int", "id": 162 }, { "type": "int", "id": 282 }, { "type": "int", "id": 407 }, { "type": "int", "id": 439 }, { "type": "int", "id": 440 }, { "type": "int", "id": 441 }, { "type": "int", "id": 442 }, { "type": "int", "id": 443 }, { "type": "int", "id": 444 }, { "type": "int", "id": 445 }, { "type": "int", "id": 293 }, { "type": "int", "id": 243 }, { "type": "int", "id": 476 }, { "type": "int", "id": 478 }, { "type": "float", "id": 130 }, { "type": "float", "id": 131 }];
        let game_visual_id = [{ "type": "int", "id": 2 }, { "type": "str", "id": 73 }];
        let display_setting_id = [{ "type": "int", "id": 206 }, { "type": "int", "id": 207 }, { "type": "int", "id": 196 }, { "type": "int", "id": 198 }];
        let director_mode_id = [{ "type": "int", "id": 334 }, { "type": "int", "id": 335 }, { "type": "int", "id": 336 }, { "type": "int", "id": 337 }, { "type": "int", "id": 338 }, { "type": "int", "id": 339 }, { "type": "int", "id": 340 }, { "type": "int", "id": 341 }]

        let gpp_configStr = window.localStorage.getItem('gpp_config_for_game');
        let gpp_config = null;
        if (gpp_configStr) {gpp_config = JSON.parse(gpp_configStr);}

        console.log('old', gpp_config)

        let gpp_config_obj = {};
        gpp_config_obj['ingame_monitor'] = GPP_Config_Data_Sort(ingame_monitor_id);
        gpp_config_obj['game_visual'] = GPP_Config_Data_Sort(game_visual_id);
        gpp_config_obj['display_setting'] = GPP_Config_Data_Sort(display_setting_id);
        gpp_config_obj['director_mode'] = GPP_Config_Data_Sort(director_mode_id);

        let Key = {};
        Key[ProcessName] = gpp_config_obj;

        if (gpp_config) {
            let mergeObj = JSON.stringify({ ...gpp_config, ...Key });
            console.log('new', { ...gpp_config, ...Key })
            window.localStorage.setItem('gpp_config_for_game', mergeObj)
        } else {
            console.log('new', Key)
            let gpp_config = JSON.stringify(Key);
            window.localStorage.setItem('gpp_config_for_game', gpp_config)
        }

        console.log('保存当前游戏 游戏加加配置成功!')

        //配置数据分类
        function GPP_Config_Data_Sort (data)
        {
            let data_list = [];
            for (let i = 0; i < data.length; i++) {
                let id = data[i]['id'];
                let object = {};
                object['id'] = id;
                object['type'] = data[i]['type'];
                if (data[i]['type'] === 'int') {
                    object['ivalue'] = gamepp.setting.getInteger.sync(id);
                }
                if (data[i]['type'] === 'str') {
                    object['svalue'] = gamepp.setting.getString.sync(id);
                }
                if (data[i]['type'] === 'float') {
                    object['svalue'] = gamepp.setting.getFloat.sync(id);
                }
                data_list.push(object);
            }
            return data_list
        }
    }

    /**
     * 游戏连接时判断是否存在保存的游戏加加配置
     * @param ProcessName
     * @constructor
     */
    IsSaveCurrentGamePPConfig (ProcessName) {
        // let isOpen = gamepp.setting.getInteger.sync(475);
        // if (!isOpen) return;
        // console.log(ProcessName)
        // let gpp_configStr = window.localStorage.getItem('gpp_config_for_game');
        // if (gpp_configStr !== null && gpp_configStr !== 'null') {
        //     let gpp_config = null;
        //     if (gpp_configStr) {gpp_config = JSON.parse(gpp_configStr);}
        //     console.log(gpp_config)
        //     if (gpp_config && gpp_config[ProcessName]) {
        //         let config_data = gpp_config[ProcessName];
        //         let funList = Object.keys(config_data);
        //         for (let i = 0; i < funList.length; i++) {
        //             let funKey = funList[i]
        //             for (let j = 0; j < config_data[funKey].length; j++) {
        //                 let data = config_data[funKey][j]
        //                 let id = data['id'];
        //                 if (data['type'] === 'int') {gamepp.setting.setInteger.sync(id, Number(data['ivalue']));}
        //                 if (data['type'] === 'str') {gamepp.setting.setString.sync(id, data['svalue']);}
        //                 if (data['type'] === 'float') {gamepp.setting.setFloat.sync(id, Number(data['svalue']));}
        //             }
        //         }
        //         console.log('加载当前游戏 游戏加加配置成功!')
        //     }
        // }
    }

    async UnInitGameInfo()
    {
            //设置游戏激活状态
            await gamepp.hotkey.setGameWindowActiveState.promise(false);
            this._hotkey_RecordHotkeyCount = 0;
            this.RecordStartStr = '';
            this.RecordStopStr = '';
            this.RecordFrameRateData = null;
            this.setInterval0 = null;
            this.RecordIntervals = 10;//两次记录按键间隔时间

            this.BG_GPP_PCAwaken_monitor = false;
            this.BG_GPP_PCAwaken_monitor_type = null;
            this.GAME_ProcessName = null;

            this.MajorFrameRatePageTime = 0;

            is_switch = false;
            InjectGamePID = 0;
            ActivatedProcessInfo = null;
            const background = new Background();
            background.LoadingTime = 0;

            for (let i = 0; i < switchwindows.length; i++)
            {
                gamepp.webapp.windows.close.sync(switchwindows[i]);
            }

            await this.IsReadyShowSendPage('window', WindowName.IN_GAME_TipsPage, false);
            await this.IsReadyShowSendPage('window', WindowName.IN_GAME_Quit_Game_Tips, false);
            await this.IsReadyShowSendPage('window', WindowName.IN_GAME_GPU_BENCHMARK_MONITOR, false);
    }

    //百分比
    toPercent (num, total)
    {
            if (Number(num == 0 && Number(total) == 0||!total)) {
                return 0;
            } else {
                return (Math.round(num / total * 1000) / 10.0);
            }
    }

    /*
     循环调取接口获取天气信息
     */
     async GetTianqiApiInfo () {
        let isOpen = await gamepp.setting.getInteger.promise(COMMANDID.CM_GPPSETTING_WEATHER_LOCATION)
        if (!isOpen) return false
        let result = null;
        //获取地址市省区
        //https://restapi.amap.com/v3/ip?key=0113a13c88697dcea6a445584d535837
        //http://whois.pconline.com.cn/ipJson.jsp?json=true
        //http://ip-api.com/json?lang=zh-CN
        let location = null;
        const tokenList = [
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJBY2NvdW50SWQiOiJiMmFlNjIzNzQzZmZmODQ4M2MxYTkwMTI5NjhiOGI5YiJ9.2mRjsuAo3zBeYPiVIk_fcGHcBBtZtFMZErNMwrwgRjY',
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJBY2NvdW50SWQiOiJmOGZhODA5MjA5ZThkMTQyNDdlNGZhNmI5NGE3ZmRhZSJ9._IH6bUshlRVjn1xCm5Orx2H33tjg-Cd5OP_on_Dk3kE',
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJBY2NvdW50SWQiOiJkOTQyY2RhMTVkOWMzZjQ3MzhiZTA5MDI2YTM1MTU4NSJ9.jCGrZzseH_EkWWFX8xippvMfmgcaXhbH6L2W7yJrKUU',
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJBY2NvdW50SWQiOiJlZWQ4ZmQ1ODBmYTRmNjkyIn0.d7qF_mjdXMC0R5M6f04Lnh6x61kaU4lqHT0Axt9xUOY'
        ]
        const token = tokenList[Math.floor(Math.random() * tokenList.length)]
        // const response = await gamepp.http.httpget.promise('https://www.douyacun.com/api/openapi/geo/location?token=' + token + '', '');
        const response = await gamepp.http.httpget.promise('https://api.live.bilibili.com/xlive/web-room/v1/index/getIpInfo');
        if (response) {
            const response_obj = JSON.parse(response);
            if (response_obj['code'] === 0) {
                location = response_obj['data'];
            }

            if (location) {
                if (location['province'] && location['city']) {
                    const response = await gamepp.http.httpget.promise('https://wis.qq.com/weather/common?source=pc&weather_type=observe&province=' + encodeURI(location["province"]) + '&city=' + encodeURI(location["city"]), '');
                    if (response) {
                        const response_obj = JSON.parse(response);
                        if (response_obj['status'] === 200) {
                            console.log(response_obj)
                            let response_obj_new = {};
                            response_obj_new['type'] = 'tencent';
                            response_obj_new['city'] = location['city'];
                            response_obj_new['province'] = location['province'];
                            response_obj_new['cityEn'] = null;
                            response_obj_new['wea'] = response_obj['data']['observe']['weather'];
                            response_obj_new['wea_img'] = response_obj['data']['observe']['weather_code'];
                            response_obj_new['tem'] = response_obj['data']['observe']['degree'];
                            result = response_obj_new
                        }
                    }
                }
            }
        }

        return result;
    }

    registerGamerListener () {
        //新游戏激活
        gamepp.game.onProcessActived.addEventListener((value) => this.AppEvent_OnProcessActived(value))
        //新游戏注入连接成功
        gamepp.game.onNewConnection.addEventListener((value) => this.AppEvent_OnNewConnectionArrived(value));
        //游戏结束
        gamepp.game.onConnectionDropped.addEventListener((value) => this.AppEvent_OnConnectionDropped(value));
        //
        gamepp.game.ingame.onGameWindowStateChanged.addEventListener((value) => this.AppEvent_OnGameWindowChanged(value));
        //监听游戏窗口位置改变 监控吸附
        gamepp.game.ingame.onWindowPositionChanged.addEventListener((value) => this.AppEvent_OnWindowPositionChanged(value));
    }

    /**
     *切换游戏进程
     */
    async SwitchGameProgress (result) {
        console.log("Switch Game Progress", result);
        /**
         * 游戏数据
         */
        let EndTime = Date.parse(new Date()) / 1000;

        /**
         * 10分钟有效数据
         */
        let isOpenRebound = await gamepp.setting.getInteger.promise(COMMANDID.CM_PERFORMANCE_OPEN_SWITCH);
        // const TenMinute = 10 * 60;
        // EndTime - StartTimestampTable >= TenMinute
        //1分钟 弹出,10分钟上传
        if (isOpenRebound === 1 && EndTime - StartTimestampTable >= 60) {
            let BaseJsonInfo = await gamepp.hardware.getBaseJsonInfo.promise();
            let BaseJsonInfoJson = JSON.parse(BaseJsonInfo);
            delete BaseJsonInfoJson["SOUND"];
            delete BaseJsonInfoJson["NETWORK"];
            let HD_Info = encodeURIComponent(JSON.stringify(BaseJsonInfoJson));
            gamepp.database.update.async(value => {}, GPP5DatabaseId, "GamePP_BaseInfo", ['endtime="' + EndTime + '"', 'hd_info="' + HD_Info + '"', 'starttime = "' + StartTimestampTable + '"']);
            /**
             *天气数据
             */

            let WeatherInfo
            try{
                WeatherInfo = await gameclient.GetTianqiApiInfo();
                console.warn('WeatherInfo',WeatherInfo);
            }
            catch{
                let response_obj_new = {};
                response_obj_new['type'] = 'tencent';
                response_obj_new['city'] = '';
                response_obj_new['province'] = '';
                response_obj_new['cityEn'] = null;
                response_obj_new['wea'] = '';
                response_obj_new['wea_img'] = '';
                response_obj_new['tem'] = '';
                WeatherInfo = response_obj_new
            }
            try
            {
                if (WeatherInfo) {
                    await gamepp.database.update.promise(GPP5DatabaseId, "GamePP_BaseInfo", ['city="' + WeatherInfo['city'] + '"', 'province="' + WeatherInfo['province'] + '"', 'wea="' + WeatherInfo['wea'] + '"', 'wea_img="' + WeatherInfo['wea_img'] + '"', 'tem="' + WeatherInfo['tem'] + '"'], 'starttime = "' + StartTimestampTable + '"');
                }
            }
            catch{
                let response_obj_new = {};
                response_obj_new['type'] = 'tencent';
                response_obj_new['city'] = '';
                response_obj_new['province'] = '';
                response_obj_new['cityEn'] = null;
                response_obj_new['wea'] = '';
                response_obj_new['wea_img'] = '';
                response_obj_new['tem'] = '';
                WeatherInfo = response_obj_new
            }

        }

        //判断窗口是否需要重启
        for (let i = 0; i < switchwindows.length; i++) {
            let isValid = gamepp.webapp.windows.isValid.sync(switchwindows[i])
            console.log('isValid',switchwindows[i], isValid)
            if (isValid) {
                let isVisible = gamepp.webapp.windows.isVisible.sync(switchwindows[i])
                gamepp.webapp.windows.close.sync(switchwindows[i]);
                console.log('isVisible',switchwindows[i],isVisible)
                if (isVisible) {
                    gamepp.webapp.windows.show.sync(switchwindows[i]);
                }
            }
        }

        await gamepp.game.disconnectCurrentClient.promise();
        is_switch = true;
    }

    async upload_blackAndwhiteProcess (ConnectedClient) {
        let Dir = await gamepp.getAppDataDir.promise() + '\\Apps\\8fb708e5972662062da26a2bb4753a23\\' + await gamepp.getPlatformVersion.promise() + '\\win32\\data\\GamePP0.dat';
        let databaseid = await gamepp.database.open.promise(Dir);
        const process_info = await gamepp.database.query.promise(databaseid, "WhiteBlack", "*", 'process="' + ConnectedClient['baseName'] + '"');
        if (process_info.length !== 0) {
            //ActivatedProcessBlack.push(ConnectedClient['baseName']);
            // 如果是黑名单，不应该走到此处。
            // select sql = select * from WhiteBlack where process like "%P01.exe%"
            // 我自己自己编译的DX11测试程序走到这里，但是用Navicat没有查询结果，需要检查。
            console.log('黑白名单已存在当前进程');
            return
        }
        let dir = '';
        if ((ConnectedClient.execPath).indexOf('steamapps') !== -1) {dir = 'steamapps';}
        let mid = await gamepp.getMID.promise();
        let version = await gamepp.getPlatformVersion.promise();
        let process_version = "1.0";
        let processname = ConnectedClient.baseName;
        let companyname = ConnectedClient.companyName;
        let filedescription = ConnectedClient.fileDescription;
        let producttitle = await gamepp.getWindowTextA.promise(ConnectedClient.windowHandle);
        let hash = await gamepp.getFileMd5?.promise(ConnectedClient.execPath);
        let hash_value = hash === undefined ? '' : hash
        let getClassNameA = await gamepp.getClassNameA.promise(ConnectedClient.windowHandle);
        let exePath = await gamepp.getProcessFilePathByProcessId.promise(ConnectedClient.pid);
        // let moduleList = await gamepp.getModuleListInfo.promise(ConnectedClient.pid);
        let moduleList = "";
        $.ajax({
                   type: "GET",
                   url : "http://process2_log.gamepp.com/process/1?" +
                         "&mid=" + mid +
                         "&version=" + version +
                         "&process_version=" + process_version +
                         "&processname=" + processname +
                         "&dir=" + dir +
                         "&companyname=" + companyname +
                         "&filedescription=" + filedescription +
                         "&producttitle=" + producttitle +
                         "&hash=" + hash_value +

                         "&className=" + getClassNameA +
                         "&exePath=" + exePath +
                         "&moduleList=" + moduleList
                   ,
                   dataType: "json",
               });
        console.log('上传黑白名单进程:' + ConnectedClient['baseName']);
    }

    /**
     * Des加密
     */
    encryptByDES (message, key) {
        return CryptoJS.DES.encrypt(message, CryptoJS.enc.Utf8.parse(key), {
            mode   : CryptoJS.mode.ECB,
            padding: CryptoJS.pad.ZeroPadding
        }).toString();
    }

    registerNotifyReceiver () {
        gamepp.webapp.onMonitoredGameLaunched.addEventListener((value) => this.AppOnMonitoredGameLaunched(value))
    }

    async AppOnMonitoredGameLaunched (gameId) {
        console.log(`${gameId} Started`)
        // b990c0ce5e4f57420eeb29774ac305db   tft:c0d763096f8a4d9ee3dbd8580bf36233
        if (gameId === 'c0d763096f8a4d9ee3dbd8580bf36233') {

        }
    }

    async Run () {
        console.warn("游戏注入监听启动...");

        await this.GaIsReady();

        gamepp.webapp.onInternalAppEvent.addEventListener(value => {
            if(value === 'stopSpark') {
                video_client.SendIntelSparkleMessage_STOP(this.sparkMessageListener);
            } else if (value === 'startSpark') {
                video_client.SendIntelSparkleMessage_START(HighLight_Game_List[this.GAME_ProcessName], this.sparkMessageListener)
            }
        })

        console.warn('运行游戏注入监听器...');

        this.registerGamerListener();

        console.warn('已开始游戏进程注入监听...');

        this.registerNotifyReceiver()

        // await this.registerInGameHotkey();

        this.GetGameMessageTipList()
    }

    GetGameMessageTipList() {
        try {
            const timestamp = Date.now()
            gamepp.http.httpget.promise('https://config.gamepp.com/notice/process_exit.json?timestamp='+timestamp, '')
              .then((res)=>{
                  const JsonData = JSON.parse(res)
                  if (res && Array.isArray(JsonData)) {
                      JsonData.forEach((item)=>{
                          if (!item.process.includes('.exe')) item.process += '.exe'
                          GameMessageTipMap.set(item.process,item)
                      })
                  }
              })
        } catch (e) {
            console.log(e)
        }
    }

    //用于创建窗口后需要发送数据到新窗口 判断窗口是否初始化完成
    async IsReadyShowSendPage (sectionName, keyName, value = false) {
        let nRet = false;
        while (1) {
            nRet = await gamepp.setting.getBool2.promise(sectionName, keyName, value);
            if (nRet) {break;}
            await this.Sleep(100);
        }
    }

    async Sleep (nTimeOut) {
        return new Promise((resolve) => {setTimeout(resolve, nTimeOut)});
    }


    async GaIsReady () {
        return new Promise((resolve, reject) => {
            let nRet = 0;
            let isready_time = setInterval(async () => {
                try {nRet = await gamepp.apiinfo.isready.promise();} catch (error) {console.log(error);}
                if (nRet === 1) {
                    clearInterval(isready_time)
                    resolve(1)
                }
            }, 100)

        })
    }

}

const gameclient = new GameClient();

gameclient.registerClipMsg()
// export { gameclient }
class InGameProcessInfoSaver {
    WhiteList = ["GamePPLite.exe","MythCool.exe","HwMonitor64.exe","GameBuff.exe","GamePP.exe"]
    ProcessDetails=[]
    getAllProcessDetailTimer= null
    saveInterval= 60
    // saveInterval=10
    getProcessDetailCount= 0
    gamePid=-1
    tableName= ""
    theTopFew=30 // 存数据库时取前几的数据
    memory_size_mb= 0
    async getAllProcessDetail () {
        this.getProcessDetailCount++;
        const AllProcessDetailList = await gamepp.getAllProcessDetail.promise();
        // console.log('-------------',AllProcessDetailList)
        const _arr = []
        for (let index = 0; index < AllProcessDetailList.length; index++) {
            const ProcessDetail = AllProcessDetailList[index]
            // 获取进程名
            const processName = this.getProcessName(ProcessDetail.process_full_path);
            if (!processName || !ProcessDetail.hasOwnProperty('pid') || !ProcessDetail.hasOwnProperty('cpu_usage')) continue;
            if (ProcessDetail.pid === this.gamePid) continue; // 游戏进程不记录
            // 白名单的也不记录
            if (this.WhiteList.includes(processName)) continue;

            const tempObj = {
                name: processName,
                pid: ProcessDetail.pid, // 进程pid
                cpu_pp: 0,
                gpu_pp: 0,
                mem_pp: 0,
            }
            let cpu_usage = ProcessDetail.cpu_usage.toFixed(2)*1
            let gpu_usage = 0
            let mem_usage = 0
            let mem_usage_mb = 0
            // 算gpu占用
            if (ProcessDetail.gpu_utilizations && Array.isArray(ProcessDetail.gpu_utilizations)) {
                let gpu_load = 0
                for (let i = 0; i < ProcessDetail.gpu_utilizations.length; i++) {
                    gpu_load += (ProcessDetail.gpu_utilizations[i].gpu_load || 0)
                }
                gpu_usage = Number(gpu_load.toFixed(2))
            }

            // 算内存占用
            if (ProcessDetail.mem_working_set_size) {
                mem_usage_mb = Number(ProcessDetail.mem_working_set_size) / 1024 / 1024
                if (this.memory_size_mb !== 0) {
                    mem_usage = mem_usage_mb / this.memory_size_mb * 100
                }
            }

            // 算pp值
            const findItem = this.ProcessDetails.find(item => item.name === processName)
            if (findItem) {
                tempObj.cpu_pp = this.calculatePP(findItem.cpu_pp, cpu_usage)
                tempObj.gpu_pp = this.calculatePP(findItem.gpu_pp, gpu_usage)
                tempObj.mem_pp = this.calculatePP(findItem.mem_pp, mem_usage)
            }else{
                tempObj.cpu_pp = this.calculatePP(0, cpu_usage)
                tempObj.gpu_pp = this.calculatePP(0, gpu_usage)
                tempObj.mem_pp = this.calculatePP(0, mem_usage)
            }
            _arr.push(tempObj)
        }
        this.ProcessDetails = _arr
        if (this.getProcessDetailCount % this.saveInterval === 0) {
            let CPUPPList = this.ProcessDetails.toSorted((a, b) => b.cpu_pp - a.cpu_pp);
            let GPUPPList = this.ProcessDetails.toSorted((a, b) => b.gpu_pp - a.gpu_pp);
            let MEMPPList = this.ProcessDetails.toSorted((a, b) => b.mem_pp - a.mem_pp);

            CPUPPList = JSON.stringify(CPUPPList.slice(0,this.theTopFew))
            GPUPPList = JSON.stringify(GPUPPList.slice(0,this.theTopFew))
            MEMPPList = JSON.stringify(MEMPPList.slice(0,this.theTopFew))
            // console.log(MEMPPList)
            let Data_Field = ["cpupp","gpupp","mempp"]
            let Data_Content = [[CPUPPList,GPUPPList,MEMPPList]]
            await gamepp.database.insert.promise(GPP5DatabaseId, "'" + this.tableName + "'", Data_Field, Data_Content);
            this.ProcessDetails = []
        }
    }
    async startGetAllProcessDetail() {
        const isInit = await gamepp.isInitAllProcessDetail.promise()
        const duration = 1000
        if (!isInit) {
            await gamepp.initAllProcessDetail.promise(duration,duration); // 初始化进程信息模块，param1 循环查询进程时间，param2 循环查询进程详细信息时间
        }
        this.getMemorySizeMb()
        if (this.getAllProcessDetailTimer) {
            clearInterval(this.getAllProcessDetailTimer)
        }
        this.getAllProcessDetailTimer = setInterval(()=>{
            this.getAllProcessDetail()
        },duration)
    }
    async stopGetAllProcessDetail() {
        if (this.getAllProcessDetailTimer) {
            clearInterval(this.getAllProcessDetailTimer)
        }
    }
    // 拿内存大小
    async getMemorySizeMb (trycount=0) {
        if (trycount >= 60) return
        let HWInfoJSONState = await gamepp.hardware.getJSONState.promise();
        if (HWInfoJSONState === 1) {
            let HwInfoJsonStr = await gamepp.hardware.getBaseJsonInfo.promise();
            if (HwInfoJsonStr) {
                let HwInfoJson = JSON.parse(HwInfoJsonStr);
                // 获取内存大小
                try {
                    this.memory_size_mb = Number(HwInfoJson?.MEMORY.Property["TotalMemorySize[MB]"])
                }catch (e) {
                    this.memory_size_mb = 0
                }
            }
        }else{
            setTimeout(()=>{
                this.getMemorySizeMb(trycount++)
            },500)
        }
    }
    // 获取进程名
    getProcessName(filePath) {
        if (!filePath || typeof filePath !== 'string') return ''
        const parts = filePath.split('\\');
        return parts[parts.length - 1];
    }
    // 计算PP值
    calculatePP(N, M) {
        return N * 0.9 + M * 0.1
    }
}
