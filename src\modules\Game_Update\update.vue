<!-- 游戏加加更新启动页 -->
<script setup lang="ts">
import {onMounted, ref} from "vue";
import {LoadModuleStepInterface} from "@/modules/Game_Update/types/Interface";
// @ts-ignore
const gamepp = window.gamepp as any;
onMounted (() => {
   Init()
})

let Loading = ref<boolean>(true)
const Progress = ref<number>(0)
let Progress_show = ref<boolean>(false)
const Loading_text = ref<string>('update.checkingUpdate')
const Download_speed = ref<number>(0)
const updateText = ref<string>('')
let nStartTime = new Date().getTime(), IsLoadFinished = false, nMaxDelay = 2500, IsContinueProgress = true;
let LoadModuleStep:LoadModuleStepInterface = { LMS0: "update.loadingProgramComponent", LMS1: "update.loadingHotkeyModules", LMS2: "update.loadingGPPModules", LMS3: "update.loadingBlackWhiteList", LMS4: "update.loadingGameSetting", LMS5: "update.loadingUserAbout", LMS6: "update.loadingGameBenchmark", LMS7: "update.loadingHardwareInfo", LMS8: "update.loadingDBModules", LMS9: "update.loadingIGCModules", LMS10: "update.loadingIGCModules", LMS11: "update.loadingFTPModules", LMS12: "update.loadingDialogModules", LMS13: "update.loadingDataStatisticsModules", LMS14: "update.loadingSysModules", LMS15: "update.loadingGameOptimization", LMS16: "update.loadingGameAcceleration", LMS17: "update.loadingScreenshot", LMS18: "update.loadingVideoComponent", LMS19: "update.loadingFileFix", LMS20: "update.loadingGameAI", LMS21: "update.loadingNVAPIModules", LMS22: "update.loadingAMDADLModules" }

async function IsReady () {
  return new Promise((resolve, reject) => {
    let nRet = 0;
    let isready_time = setInterval(async () => {
      try {nRet = await gamepp.apiinfo.isready.promise();} catch (error) {console.log(error);}
      if (nRet === 1) {
        clearInterval(isready_time)
        resolve(1)
      }
    }, 500)

  })
}

async function InitUpdate () {
  console.log("Start running the main services....");

  // await gamepp.initUpdateModules.promise();


  gamepp.update.onDownloadStart.addEventListener(e =>
  {
    console.log(e);
  });



  gamepp.update.onDownloadSpeed.addEventListener((name,res) => {
    console.log('下载进度', name, res)
    // setdownloadspeed(res.speed);
    // setdownloadsize(res.length + " / " + res.totallength);
    // setprogress(res.percent);
    Download_speed.value = res.speed
    Progress.value = res.percent
  });

  gamepp.update.onDownloadComplete.addEventListener(e => {
    console.log(e);
    //settitle("下载完成");
  });

  if (gamepp.isSkipUpdate.sync())
  {
    await PrepareStart();
  }
  else
  {
    // Loading.value = false
    // Progress_show.value = true
    await CheckUpdate();
  }
}


async function PrepareStart ()
{
  console.log("RunSyncData");
  await gamepp.runSyncData.promise();
  try
  {
    // await gamepp.startMainServices.promise();
  }
  catch(err)
  {
    console.log('startMainServices',err);
  }

  if (gamepp.webapp.windows.isVisible.sync('update'))
  {
    gamepp.webapp.windows.show.sync('desktop');
    gamepp.webapp.windows.focus.sync('desktop');
    gamepp.webapp.windows.close.sync('update');
  }
  else
  {
    gamepp.webapp.windows.show.sync('desktop', true);
    gamepp.webapp.windows.close.sync('update');
  }
}

async function Init () {
  await IsReady();

  /*
   加载模块通知
   */
  gamepp.onServiceLoading.addEventListener(value => {
    console.log(LoadModuleStep['LMS' + value]);
    Loading_text.value = LoadModuleStep['LMS' + value]
  });

  if (!gamepp.webapp.windows.isVisible.sync()) {
    console.log("Install app event listener..");
    await InitUpdate();

    gamepp.webapp.onInternalAppEvent.addEventListener(e => {
      console.log(e);
    });
  } else {
    gamepp.webapp.onInternalAppEvent.addEventListener(e => {
      console.log(e);
    });
    // Run main services.
    await InitUpdate();
  }

}

function delayShow ()
{
  const nCurrenTime = new Date().getTime();
  const nSections = (nCurrenTime - nStartTime);
  if (!IsContinueProgress)
  {
    return;
  }
  if (nSections > nMaxDelay && IsLoadFinished)
  {
    Progress.value = 100
    if (gamepp.webapp.windows.isVisible.sync('update')) {
      gamepp.webapp.windows.show.sync('desktop');
      gamepp.webapp.windows.focus.sync('desktop');
      gamepp.webapp.windows.close.sync('update');
    } else {
      gamepp.webapp.windows.show.sync('desktop', true);
      gamepp.webapp.windows.close.sync('update');
    }
  }
  else
  {
    Progress.value = Math.min(nSections * 100 / nMaxDelay, 99)
    setTimeout(function () {
      delayShow();
    }, 100);
  }
}

async function CheckUpdate ()
{
        console.log("CheckUpdate 1()");

        updateText.value = "update.checkingUpgrade"
        delayShow();

        //alert("CheckUpdate 1");

        let isUpdateManual = await gamepp.isUpdateManual.promise()
        let Update = 1

        if (isUpdateManual) {Update = 2}
        const nUpdateStatus = await gamepp.update.checkUpdate.promise(Update);

        let update = 0, upgrade = 0;
        if (nUpdateStatus === 1)
        {
            Loading.value = false
            Progress_show.value = true
            console.log("runUpdate");
            IsContinueProgress = false;
            updateText.value = "update.upgradingInProgress"
            await gamepp.update.runUpdate.promise();
            upgrade = 1;
        }
        else if (nUpdateStatus === 2)
        {
            Loading.value = false
            Progress_show.value = true
            console.log("runUpdatePackage");
            IsContinueProgress = false;
            updateText.value = "update.theModuleIsBeingUpdated"
            await gamepp.update.runUpdatePackage.promise();
            update = 1;
        }
        else
        {
            Loading.value = false
            // Progress_show.value = true
            console.log("RunSyncData");
            Loading_text.value = "update.dataIsBeingUpdated"
            await gamepp.runSyncData.promise();
            // Loading.value = true
            // Progress_show.value = false
            Loading_text.value = "update.loadingModules"
            console.log("gamepp.startMainServices 1");
            const nCurrenTime = new Date().getTime();
            const nSections = (nCurrenTime - nStartTime);
            if (nSections < (nMaxDelay - 300)) {
              nStartTime = nStartTime - ((nMaxDelay - 300) - nSections);
            }
            IsLoadFinished = true;
            update = 0;
            upgrade = 0;
        }

        let Obj:any = {};
        Obj['update'] = update;
        Obj['upgrade'] = upgrade;
        let update_storage_new = JSON.stringify(Obj);
        console.log(update_storage_new);
        window.localStorage.setItem('update_storage', update_storage_new);

}


</script>

<template>
  <div class="update-container">
    <div class="top-bar">
    </div>
    <div class="update-center">
      <div class="gpp-title">
        <img src="./assets/img/gamepp.png" alt="gpp-title">
        <div class="gpp-text">
          <span>{{$t('update.Game')}}</span>
          <span>{{$t('update.PP')}}</span>
        </div>
      </div>
      <div class="gpp-com">
        <span>GamePP.com</span>
      </div>
      <!-- <div class="gpp-progress" > -->
        <!-- <div class="gpp-progress" v-loading="{visible:Loading&&!Progress_show,text:Loading_text}"> -->
          <div class="gpp-progress" v-loading="Loading&&!Progress_show" :element-loading-text="$t(Loading_text)">
        <Transition>
        <div class="download_speed" v-show="Progress_show">
        <p class="speed">{{Download_speed}}</p>
        <!-- <p class="speed-type">MB/S</p> -->
        </div>
        </Transition>
        <Transition>
        <div class="progress-theme">
          <el-progress  :percentage="Progress" v-show="Progress_show" :show-text="false" />
          <div class="progress-text" v-show="Progress_show">
            <p class="bottom-text">{{$t(updateText)}}</p>
            <div class="Unit">
              <p class="bottom-number" style="margin-right: 2px;">{{Progress}}</p>
              <p class="bottom-text">%</p>
            </div>
          </div>
        </div>
        </Transition>
      </div>
    </div>

  </div>
</template>
<style lang="scss">
body[dir=rtl] {
    .update-center .gpp-title .gpp-text {
        padding-left: 0;
        padding-right: 10px;
    }
}
.v-enter-active,
.v-leave-active {
  transition: opacity 0.5s ease;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
}
html {
  --el-color-primary: #3579d5 !important;
  --el-loading-spinner-size: 27px !important;

}
.el-progress-bar__outer {
  background: rgba(43, 44, 55, 1);
}
.el-progress-bar__inner {
  background: rgba(53, 121, 213, 1);
}
.el-loading-spinner .el-loading-text {
  color: rgba(119, 119, 119, 1);
  margin-top: 37px;
}
.el-loading-mask {
  background-color: transparent;
}

.update-container {
  padding: 5px;
  width: 540px;
  height: 320px;
  background: linear-gradient(30deg, #1D1D24 0%, #22232E 100%);
  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
  border-radius: 4px;

  .top-bar {
    .top-icon {
      display: flex;
      justify-content: flex-end;

      i {
        padding-left: 10px;
      }

      cursor: pointer;
    }
  }

  .gpp-progress {
    width: 80%;
    height: 50px;
    margin-top: 35px;
    .download_speed {
      display: flex;
      justify-content: center;
      .speed {
        font-size: 12px;
        color: rgba(255, 255, 255, 1);
      }
      .speed-type {
        font-size: 12px;
        color: rgba(119, 119, 119, 1);
      }
    }
    .progress-theme {
      width: 80%;
      margin: 0 auto;
      .progress-text {
        display: flex;
        justify-content: center;
        .bottom-text {
          font-size: 12px;
          color: rgba(119, 119, 119, 1);
          padding: 3px;
        }
        .Unit {
          padding: 3px;
          display: flex;
          align-items: center;
          .bottom-number {
            font-size: 12px;
            color: rgba(255, 255, 255, 1);
          }
          .bottom-text {
            font-size: 12px;
            color: rgba(119, 119, 119, 1);
            padding: 0;
          }
        }
      }
    }
  }
}

.update-center {
  height: 100%;
  padding-top: 64px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .gpp-title {
    display: flex;
    align-items: center;
    img {
      width: 20px;
      height: 20px;
    }
    .gpp-text {
      padding-left: 10px;
    }
    .gpp-text span:first-child {
      color: #3981D0;
      font-size: 18px;
      font-weight: bold;
    }

    .gpp-text span:nth-child(2) {
      color: #F6B619;
      font-size: 18px;
      font-weight: bold;
    }
  }

  .gpp-com {
    font-size: 13px;
    color: #FFFFFF;
    padding-top: 7px;

    span {
      letter-spacing: 2px;
    }
  }
}
</style>
