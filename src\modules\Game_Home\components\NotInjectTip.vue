<script setup lang="ts">
import {onMounted, ref} from "vue";
import {useI18n} from "vue-i18n";

const no_more_show = ref(false);
const is_show = ref(false);
const Process = ref("");
const {t} = useI18n()

function CopyText () {
  try {
    gamepp.clipboard.writeText('-allow_third_party_software');
    ElMessage.success(t('messages.copyToClipboard'));
  } catch {

  }
}

function checkLocal() {
  const local_data = window.localStorage.getItem('Not_Inject');
  console.log(local_data)
  if (local_data) {
    Process.value = local_data;
    is_show.value = true;
  }else{
    is_show.value = false;
    Process.value = ""
  }
}

function changeNoMoreShow() {
  if (no_more_show.value) {
    window.localStorage.setItem('noTips_'+Process.value, '1')
  }else{
    window.localStorage.removeItem('noTips_'+Process.value)
  }
}

function closeWindow () {
  is_show.value = false
  window.localStorage.removeItem('Not_Inject')
}

onMounted(()=>{
  is_show.value = false;
  checkLocal()
  window.addEventListener('storage',(e)=>{
    if (e.key === 'Not_Inject') {
      checkLocal()
    }
  })
});
</script>

<template>
  <div class="not-inject-tip" v-if="is_show">
    <div class="not-inject-tip-container">
      <header class="not-inject-tip-header">
        <img src="../../../assets/img/Public/logo_gpp.png" alt="">
        <span style="margin-right: auto;">异常提示</span>
        <el-checkbox v-model="no_more_show" @change="changeNoMoreShow" label="不再提示"></el-checkbox>
        <div class="icon-Close-wrap" @click="closeWindow">
          <span class="iconfont icon-Close"></span>
        </div>
      </header>
      <div class="not-inject-tip-content">
        <p class="accident">出现了意料之外的问题</p>
        <article v-if="Process !== 'cs2.exe'">
          当前游戏无法使用游戏内显示、滤镜等功能，是因为游戏厂商对第三方软件做出了限制，禁止第三方软件注入。
          目前游戏加加已经主动和游戏厂商进行了联系，具体功能恢复时间请关注游戏加加公众号以及QQ群通知。
          感谢您对游戏加加的支持!
        </article>
        <article v-else>
          CS2官匹暂不支持游戏内显示，需在启动项中增加
          <span class="argument" @click="CopyText">-allow_third_party_software</span>才可支持。支持例如5E、完美等第三方对战平台直接使用。
          <br>
          <a style="color:#FFA800;"
             href="https://www.bilibili.com/video/BV1WN4y1k7t2/?spm_id_from=333.999.0.0&vd_source=760562e7bfdd39c5c9bdaf026df22c30"
             target="_blank"
          >>>B站讲解视频<<</a>
        </article>

        <ul>
          <li>
            <span class="title">QQ群:</span>
            <span><a class="QQ" href="https://qm.qq.com/q/DIAsdcMwZq"
                     target="_blank">908287288</a>(五群)</span>
          </li>
          <li>
            <span class="title">官方邮箱:</span>
            <span><EMAIL></span>
          </li>
        </ul>

        <el-button type="primary" @click="closeWindow">我知道了</el-button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
* {
  box-sizing: border-box;
}

.not-inject-tip {
  position: absolute;
  top: 0;
  left: 0;
  right: -12px;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 30000;
  display: flex;
  justify-content: center;
  align-items: center;

  &-container {
    width: 420px;
    height: 440px;
    background: #22232E;
    box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
  }

  &-header {
    width: 420px;
    height: 30px;
    background: #343647;
    box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    display: flex;
    align-items: center;
    flex-flow: row nowrap;
    color: #ffffff;
    overflow: hidden;

    img {
      width: 18px;
      height: 18px;
      margin: 0 10px;
    }

    :deep(.el-checkbox) {
      --el-color-primary: #3579d5;
      color: #777777;
      margin-right: 10px;
    }

    .icon-Close-wrap {
      width: 40px;
      height: 26px;
      text-align: center;
      line-height: 26px;
      cursor: pointer;
      margin-right: 10px;

      .icon-Close {
        font-size: 14px;
        color: #3579d5;
      }

      &:hover {
        background-color: #2d2e39;

        .icon-Close {
          color: #ffffff;
        }
      }
    }

  }

  &-content {
    width: 420px;
    height: 410px;
    padding: 44px 40px 40px 40px;
    display: flex;
    flex-flow: column nowrap;

    .accident {
      font-weight: 400;
      font-size: 14px;
      color: #FFA800;
      line-height: 20px;
      margin-bottom: 28px;
      text-align: center;
    }

    article {
      font-weight: 400;
      font-size: 14px;
      color: #FFFFFF;
      line-height: 30px;
      margin-bottom: 37px;
      text-indent: 2em;

      .argument {
        color: #3579d5;
        cursor: pointer;
      }
    }

    ul {
      margin-bottom: 43px;
    }
    ul li {
      font-weight: 400;
      font-size: 14px;
      color: #FFFFFF;
      line-height: 20px;

      span.title {
        display: inline-block;
        width: 81px;
      }

      a.QQ {
        color: #3579D5;
        cursor: pointer;
      }
    }
  }
}
</style>
