// @ts-nocheck
export const  findValueByKeyAndType = (data:Object, key:string, keySubstring:any = false, type) => {
  let ToFixed = 0
  if (type.includes('voltage')) {
      ToFixed = 3
  }
  let key1:any = false, key2:any = false;
  if (key === 'Mainboard') {
      const sift = JSON.parse(JSON.stringify(data)) //深拷贝
      const keywords = ['CPU', 'GPU', 'Memory', 'S.M.A.R.T', 'System:', 'Windows Hardware', 'Drive', 'Network', 'DIMM']
      const filteredData = {}
      for (const key1 in sift) {
          let shouldKeep = true
          for (const keyword of keywords) {
              if (data[key1] === null || key1.toLowerCase().includes(keyword.toLowerCase())) {
                  shouldKeep = false
                  break
              }
          }
          if (shouldKeep) {
              filteredData[key1] = sift[key1]
          }
      }
      data = filteredData
      if (type === 'temperature' && Object.keys(data).some(key => key.includes('Maxsun'))) {keySubstring = 'Temp5'}// 铭瑄主板温度异常,厂商要求取 Temp5
      if (type === 'temperature' && Object.keys(data).some(key => key.includes('BIOSTAR Group B850MT-E PRO'))) {keySubstring = 'Temperature 3'}// 映泰主板温度异常,厂商要求这个主板取 Temp3
      if (type === 'temperature' && Object.keys(data).some(key => key.includes('BIOSTAR Group B850MT2-E DJ'))) {keySubstring = 'Temperature 2'}// 映泰主板温度异常,厂商要求这个主板取 Temp2
      if (window.motherboard_appoint && Array.isArray(window.motherboard_appoint)) {
          for (let i = 0; i < window.motherboard_appoint.length; i++) {
              const board_name = window.motherboard_appoint[i]['name'] ?? ''
              const sensor_key = window.motherboard_appoint[i]['key'] ?? ''
              if (board_name && type === 'temperature' && Object.keys(data).some(key => key.includes(board_name))) {keySubstring = sensor_key}
          }
      }
      key = Object.keys(filteredData)[0]
      key1 = Object.keys(filteredData)[1]
      key2 = Object.keys(filteredData)[2]
  } else if (key === 'Network') {
      let totalDLRate = 0, totalUPRate = 0;
      for (const key1 in data) {
          if (key1.startsWith("Network")) {
              const networkData = data[key1] || [];
              networkData.forEach(item => {
                  if (item["Current DL rate"]) {
                      totalDLRate += parseFloat(item["Current DL rate"].value);
                  }
                  if (item["Current UP rate"]) {
                      totalUPRate += parseFloat(item["Current UP rate"].value);
                  }
              });
          }
      }
      return [Number(totalDLRate.toFixed(0)), Number(totalUPRate.toFixed(0))];
  }
  //除开主板与NetWork都在此处进行查找
  for (const [parentKey, arr] of Object.entries(data)) {
      // console.warn('ArrArrArr',arr);
      if (data[parentKey] && (parentKey.includes(key) || (key1 && parentKey.includes(key1))|| (key2 && parentKey.includes(key2)))) {
          for (const obj of arr) {
              const [itemKey, itemValue] = Object.entries(obj)[0]
              if (keySubstring) {
                  if (keySubstring.includes('|')) {
                      const substrings = keySubstring.split('|');
                      for (const substring of substrings) {
                          if (itemKey.includes(substring) && type === (itemValue.type)) {
                              return [itemKey, Number(parseFloat(itemValue.value).toFixed(ToFixed))];
                          }
                      }
                  } else {
                      if (itemKey.includes(keySubstring) && type.includes(itemValue.type)) {
                          return [itemKey, Number(parseFloat((itemValue.value)).toFixed(ToFixed))]
                      }
                  }
              } else {
                  if (type.includes(itemValue.type)) {
                      return [itemKey, Number(parseFloat((itemValue.value)).toFixed(ToFixed))]
                  }
              }
          }
      }
  }
  return ['', '']
}
export function SensorAddAverageData (SensorInfo:any, SensorInfoKeys:any) {
  let vidSum = 0, vidCount = 0, vidSumP = 0, vidCountP = 0, vidSumE = 0, vidCountE = 0;
  let vidMax = 0, vidMaxP = 0, vidMaxE = 0;

  let clockSum = 0, clockCount = 0, clockSumP = 0, clockCountP = 0, clockSumE = 0, clockCountE = 0;

  let tempSum = 0, tempCount = 0, tempSumP = 0, tempCountP = 0, tempSumE = 0, tempCountE = 0;

  let usageSum = 0, usageCount = 0, usageSumP = 0, usageCountP = 0, usageSumE = 0, usageCountE = 0;

  const vidRegex = /^Core[ \f\r\t\n][0-9]+[ \f\r\t\n]VID$/i;
  const vidRegexP = /^P-Core[ \f\r\t\n][0-9]+[ \f\r\t\n]VID$/i;
  const vidRegexE = /^E-Core[ \f\r\t\n][0-9]+[ \f\r\t\n]VID$/i;

  const clockRegex = /^Core[ \f\r\t\n][0-9]+[ \f\r\t\n]Clock[.\n]*$/i;
  const clockRegexP = /^P-Core[ \f\r\t\n][0-9]+[ \f\r\t\n]Clock[.\n]*$/i;
  const clockRegexE = /^E-Core[ \f\r\t\n][0-9]+[ \f\r\t\n]Clock[.\n]*$/i;

  const tempRegex = /^Core[ \f\r\t\n][0-9]{1,2}$/i;
  const tempRegexP = /^P-Core[ \f\r\t\n][0-9]{1,2}$/i;
  const tempRegexE = /^E-Core[ \f\r\t\n][0-9]{1,2}$/i;

  const usageRegex = /^CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]T[0-9][ \f\r\t\n]USAGE$/i;
  const usagePRegex = /^P-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]T[0-9][ \f\r\t\n]USAGE$/i;
  const usageERegex = /^E-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]T[0-9][ \f\r\t\n]USAGE$/i;

  SensorInfoKeys.forEach(key => {
    if (SensorInfo[key]) {
      SensorInfo[key].forEach(item => {
        let itemKey = String(Object.keys(item)[0]);
        const value = parseFloat(item[itemKey].value);
        const type = item[itemKey]['type'];
        itemKey = itemKey.toLowerCase();
        if (type === "voltage" && itemKey.includes('core') && key.includes('CPU')) {
          vidSum += value;
          vidMax = Math.max(vidMax, value)
          vidCount++;
          if (itemKey.includes('p-core')) {
            vidSumP += value;
            vidMaxP = Math.max(vidMaxP, value)
            vidCountP++;
          } else if (itemKey.includes('e-core')) {
            vidSumE += value;
            vidMaxE = Math.max(vidMaxE, value)
            vidCountE++;
          }
        } else if (type === "clock" && itemKey.includes('core') && itemKey.includes('clock') && key.includes('CPU') && !itemKey.includes('effective')) {
          clockSum += value;
          clockCount++;
          if (itemKey.includes('p-core')) {
            clockSumP += value;
            clockCountP++;
          } else if (itemKey.includes('e-core')) {
            clockSumE += value;
            clockCountE++;
          }
        } else if (tempRegex.test(itemKey) || tempRegexP.test(itemKey) || tempRegexE.test(itemKey)) {
          tempSum += value;
          tempCount++;
          if (itemKey.includes('p-core')) {
            tempSumP += value;
            tempCountP++;
          } else if (itemKey.includes('e-core')) {
            tempSumE += value;
            tempCountE++;
          }
        } else if (type === "usage" && itemKey.includes('core') && itemKey.includes('usage') && key.includes('CPU')) {
          usageSum += value;
          usageCount++;
          if (itemKey.includes('p-core')) {
            usageSumP += value;
            usageCountP++;
          } else if (itemKey.includes('e-core')) {
            usageSumE += value;
            usageCountE++;
          }
        }
      });
    }
  });

  const averageVidObj = { "Core Max VID": { "type": "voltage", "value": vidCount !== 0 ? vidMax : 0 } };
  const averageVidPObj = { "P Core Max VID": { "type": "voltage", "value": vidCountP !== 0 ? vidMaxP : 0 } };
  const averageVidEObj = { "E Core Max VID": { "type": "voltage", "value": vidCountE !== 0 ? vidMaxE : 0 } };

  const averageClockObj = { "Core Clocks": { "type": "clock", "value": clockCount !== 0 ? clockSum / clockCount : 0 } };
  const averageClockPObj = { "P Core Clocks": { "type": "clock", "value": clockCountP !== 0 ? clockSumP / clockCountP : 0 } };
  const averageClockEObj = { "E Core Clocks": { "type": "clock", "value": clockCountE !== 0 ? clockSumE / clockCountE : 0 } };

  // const averageTempObj = { "Core Temps": { "type": "temperature", "value": tempCount !== 0 ? tempSum / tempCount : 0 } };
  // const averageTempPObj = { "P Core Temps": { "type": "temperature", "value": tempCountP !== 0 ? tempSumP / tempCountP : 0 } };
  // const averageTempEObj = { "E Core Temps": { "type": "temperature", "value": tempCountE !== 0 ? tempSumE / tempCountE : 0 } };

  const averageUsageObj = { "Core Usages": { "type": "usage", "value": usageCount !== 0 ? usageSum / usageCount : 0 } };

  const averageUsagePObj = { "P Core Usages": { "type": "usage", "value": usageCountP !== 0 ? usageSumP / usageCountP : 0 } };
  const averageUsageEObj = { "E Core Usages": { "type": "usage", "value": usageCountE !== 0 ? usageSumE / usageCountE : 0 } };

  let CoreVidFound = false, CoreClockFound = false, CoreTempFound = false, CoreUsageFound = false;
  let pCoreVidFound = false, pCoreClockFound = false, pCoreTempFound = false, pCoreUsageFound = false;
  let eCoreVidFound = false, eCoreClockFound = false, eCoreTempFound = false, eCoreUsageFound = false;

  SensorInfoKeys.forEach(sensorKey => {
    let resultData = [];
    if (SensorInfo[sensorKey]) {
      SensorInfo[sensorKey].forEach(sensorItem => {
        const sensorItemKey = Object.keys(sensorItem)[0];
        const sensorItemKeyUpper = sensorItemKey.toUpperCase()
        const type = sensorItem[sensorItemKey]['type'];
        if (sensorKey.includes('CPU'))
        {
          if (vidCount !== 0 && !CoreVidFound) {
            resultData.push(averageVidObj);
            CoreVidFound = true
          }
          if (vidCountP !== 0 && !pCoreVidFound) {
            resultData.push(averageVidPObj);
            pCoreVidFound = true
          }
          if (vidCountE !== 0 && !eCoreVidFound) {
            resultData.push(averageVidEObj);
            eCoreVidFound = true
          }
          if (clockCount !== 0 && !CoreClockFound) {
            resultData.push(averageClockObj);
            CoreClockFound = true
          }
          if (clockCountP !== 0 && !pCoreClockFound) {
            resultData.push(averageClockPObj);
            pCoreClockFound = true
          }
          if (clockCountE !== 0 && !eCoreClockFound) {
            resultData.push(averageClockEObj);
            eCoreClockFound = true
          }
          if (usageCount !== 0 && !CoreUsageFound) {
            resultData.push(averageUsageObj);
            CoreUsageFound = true
          }
          if (usageCountP !== 0 && !pCoreUsageFound) {
            resultData.push(averageUsagePObj);
            pCoreUsageFound = true
          }
          if (usageCountE !== 0 && !eCoreUsageFound) {
            resultData.push(averageUsageEObj);
            eCoreUsageFound = true
          }
        }
        resultData.push(sensorItem);
      });
      SensorInfo[sensorKey] = resultData;
    }

  });
  return SensorInfo;
}
export const toPercent = (num:any, total:any) => {
  if (Number(num == 0 && Number(total) == 0||!total)) {
    return 0;
  } else {
    return (Math.round(num / total * 1000) / 10.0);
  }
}

export function ProcessSensorUnit (Key, DataType):any {
  const condition_MB = ['Memory Dynamic', 'Read Total', 'Write Total', 'Memory Allocated', 'Memory Dedicated', 'Total DL', 'Total UP', 'Memory Committed', 'Memory Available', 'Memory Used','GPU Memory Usage']
  const condition_GB = ['Total Host Writes', 'Total Host Reads']
  const condition_T = ['Tcas', 'Trcd', 'Trp', 'Tras', 'Trc', 'Trfc', 'Command Rate']
  const condition_KBs = ['Current DL rate', 'Current UP rate']
  let DataUnit = '';
  let ToFixed = 0;
  let UnitText = ''
  if (DataType === 'temperature') {
    DataUnit = ' ℃'
    UnitText = ' 温度'
  } else if (DataType === 'voltage') {
    DataUnit = ' V'
    ToFixed = 3
    UnitText = ' 电压'
  } else if (DataType === 'fan') {
    DataUnit = ' RPM'
    UnitText = ' 转速'
  } else if (DataType === 'power') {
    DataUnit = ' W'
    ToFixed = 3
    UnitText = ' 功耗'
  } else if (DataType === 'clock') {
    DataUnit = ' MHz'
    UnitText = ' 频率'
  } else if (DataType === 'usage') {
    DataUnit = ' %'
    ToFixed = 1
    UnitText = ' 占用'
  } else if (DataType === 'other') {
    if (Key.includes('Ratio')) {
      DataUnit = ' x'
      ToFixed = 1
    } else if (condition_MB.find(item => Key.includes(item))) {
      DataUnit = ' MB'
    } else if (['Read Rate', 'Write Rate'].includes(Key)) {
      DataUnit = ' MB/s'
    } else if (Key.match(new RegExp('^GPU Fan[0-9]$')) || Key === 'GPU Fan PWM') {
      DataUnit = ' %'
    } else if (Key.includes('[% of TDP]') || Key.includes('Memory Load') || Key.includes('Page File Usage')) {
      DataUnit = ' %'
      ToFixed = 1
      UnitText = ' 占用'
    } else if (Key === 'PCIe Link Speed') {
      DataUnit = ' GT/s'
      ToFixed = 1
    } else if (condition_T.find(item => Key.includes(item))) {
      DataUnit = ' T'
    } else if (condition_KBs.find(item => Key.includes(item))) {
      DataUnit = ' KB/s'
      ToFixed = 3
    } else if (condition_GB.find(item => Key.includes(item))) {
      DataUnit = ' GB'
    }
  }
  const TypeData = {}
  TypeData.DataUnit = DataUnit
  TypeData.ToFixed = ToFixed
  TypeData.UnitText = UnitText
  return TypeData
}
