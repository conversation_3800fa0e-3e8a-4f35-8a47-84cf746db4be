<template>
    <section  class="login_status" :style="zoomStyle">
      <div class="user_head">
        <div class="user_icon">
          <p>{{ UserName }}</p>
          <img class="hoverfilter" @click="emitter.emit('Active-UserName',true)"  src="../assets/img/redit.png" alt="">
        </div>
        <div class="Logout hoverfilter" @click="handleLogout">{{ $t('LoginArea.loginOut') }}</div>

      </div>
      <p class="eml">{{ email }}</p>
      <div class="vipnot">
        <!-- <img src="../assets/img/vipfalse.png" alt=""> -->
        <img :src="vipImgSrc">
        <div class="viptime" v-if="isVip">
          <p>
            <strong>{{ expireDate }}</strong><span>{{ $t('LoginArea.vipExpire') }}</span>
          </p>
         <p>
          <span>{{ $t('LoginArea.remaining') }}</span><strong>{{remainingDays}}</strong><span>{{ $t('LoginArea.day') }}</span>
         </p>
        </div>

        <button class="member hoverfilter" v-if="!isVip" @click="handleClick('https://www.gamepp.com/vip/')">{{ $t('LoginArea.openVip') }}</button>
      </div>
      <div class="privilege" :style="isVip ? 'margin-top: 15px;' : 'margin-top: 8px;'">
        <div class="priv_bar">
          <h2>{{ $t('LoginArea.vipPrivileges') }}</h2>
          <p class="hoverfilter" v-if="isVip" @click="handleClick('https://www.gamepp.com/vip/')">{{ $t('LoginArea.rechargeRenewal') }}</p>
        </div>
        <ul class="privilegeUl">
            <li v-for="(item, index) in privilegeitems" :key="index">
              <img :src="item.imgSrc" v-if="index !== 2 || item.imgSrc">
              <p :class="{ 'vip_style': !isVip }">{{ $t(item.text) }}</p>
            </li>
        </ul>
      </div>
    </section>
  </template>

  <script setup lang="ts">
  import {ref,computed,onMounted ,watch} from 'vue';
  import vipImg from '../assets/img/vip.png';
  import vipFalseImg from '../assets/img/vipfalse.png';
  import privilegeImg from '../assets/img/vip_filter.png';
  import privilegefalseImg from '../assets/img/vip_filterfalse.png';
  import privilegeImg2 from '../assets/img/vip_cloud.png';
  import privilegefalseImg2 from '../assets/img/vip_cloudfalse.png';
  import { gamepp } from 'gamepp'
  import emitter from '../../uitls/emitter'
  import { useZoomStore } from '../../modules/Game_Home/stores/zoomStore';

  const zoomStore = useZoomStore();
  const expireDate = ref('');
  const remainingDays = ref(0);
  // const userData = ref({});
  // const setIntervalUserRefresh = ref<NodeJS.Timer | null>(null);

  const props = defineProps(['isVip','sendLogout','UserName','email','ActiveUserName']);
  // const emit =  defineEmits(['Active-UserName'])

  const vipImgSrc = computed(() => {
    return props.isVip ? vipImg : vipFalseImg;
  });

  const privilegeImgSrc = computed(() => {
    return props.isVip ? privilegeImg : privilegefalseImg;
  })

  const privilegeImgSrc2 = computed(() => {
    return props.isVip ? privilegeImg2 : privilegefalseImg2;
  })

  const privilegeitems = ref([
    {
      imgSrc: privilegeImgSrc,
      text: 'LoginArea.Exclusivefilter'
    },
    {
      imgSrc: privilegeImgSrc2,
      text: 'LoginArea.configCloudSync'
    },
    {
      text: 'LoginArea.comingSoon'
    }
  ]);


  async function GPP_LoadUserBaseInfo() {
    const data_arr = await gamepp.user.loadBaseInfo.promise();
      if (data_arr['date'] !== '') {
        // userData.value = data_arr;
        expireDate.value = data_arr['date'];
      }
  }

  const getRemainingDays = async() => {
    const data_arr = await gamepp.user.loadBaseInfo.promise();
    const date = new Date();
    const Y = date.getFullYear() + '-';
    const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
    const D = date.getDate() + ' ';
    const startDate = Y + M + D;
    const expiration = DateDifference(startDate, data_arr['date'].toString());
    if (expiration['days'] !== undefined) {
      remainingDays.value = expiration['days'];
    } else {
      // console.error('日期计算错误，可能是无效的日期值或格式不正确');
    }
  };

  // 计算两个日期之间的差异
  function DateDifference(startDate: string, endDate: string) {
    const s_timestamp = Date.parse(startDate);
    const e_timestamp = Date.parse(endDate);
    if (isNaN(s_timestamp) || isNaN(e_timestamp)) {
      // console.error('日期解析失败', startDate, endDate);
      return { "days": undefined };
    }
    const date_differ = e_timestamp - s_timestamp;
    const days = Math.floor(date_differ / (24 * 3600 * 1000));
    return { "days": days };
  }

  const userOpenPay = (url: string) => {
    const timestamp = new Date().getTime();
    url = `${url}?t=${timestamp}`;
    gamepp.webapp.windows.openExternalUrlInWAP.async((value: any) => console.log(value), url, 820, 590, '会员充值', true);
  };

  function handleClick(url: string | string[]) {
    console.log('url:', url);
    if (url.indexOf('vip') !== -1) {
      // const url = 'https://pay.gamepp.com/goods/list.html';
      userOpenPay('https://pay.gamepp.com/goods/list3.html');
      return;
    }
  }
  const handleLogout = async () => {
    await  gamepp.webapp.sendInternalAppEvent.promise('desktop','closeLogin');
    // 调用props中定义的sendLogout函数
    props.sendLogout(false);
  };


  onMounted(async () => {
    GPP_LoadUserBaseInfo();
    // await gamepp.user.refreshUserToken.promise(true);
    getRemainingDays()
    //监听更新用户信息
    gamepp.user.onUserInfoUpdate.addEventListener((value) => {
      GPP_LoadUserBaseInfo();
      getRemainingDays()
    });
  });

  watch(() => zoomStore.zoomLevel,(newZoomLevel) => {
      console.log('newZoomLevel:', newZoomLevel);
    }
  );

  const zoomStyle = computed(() => ({
    zoom: `${zoomStore.zoomLevel}`,
  }));

  </script>

<style lang="scss" scoped>
  .login_status {
    width: 420px;
    height: 260px;
    background: #2D2E39;
    box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.3);
    border-radius: 4px;
    position: absolute;
    bottom: 10px;
    left: 158px;
    z-index: 99;
    padding: 20px;
    box-sizing: border-box;
    .user_head{
      display: flex;
      align-items: center;
      justify-content: space-between;
      .user_icon{
        display: flex;
        align-items: center;
        p{
          color: #ffffff;
          font-size: 14px;
          margin-right: 8px;
          // white-space: nowrap;
          // overflow: hidden;
          // text-overflow: ellipsis;
          // width: 142px;
          min-width: 142px;
          max-width: 250px;
        }
        img{
          cursor: pointer;
        }
      }
      .Logout{
        color: #BF4040;
        font-size: 12px;
        cursor: pointer;
      }
    }
    .eml{
      color: #777777;
      font-size: 12px;
      padding: 10px 0;
    }
    .vipnot{
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .viptime{
      display: flex;
      align-items: center;
      gap: 30px;
      strong{
        font-size: 14px;
        font-weight: bold;
        color: #ffffff;
        padding: 0 5px;
      }
      span{
        font-size: 12px;
        color: #777777;
      }
    }
    .member{
      width: 199px;
      height: 41px;
      background: #3579D5;
      border-radius: 4px;
      color: #ffffff;
      font-size: 14px;
      font-weight: bold;
      cursor: pointer;
      border: 0;
    }
    .privilege{
      margin-top: 15px;
      .priv_bar{
        display: flex;
        align-items: center;
        justify-content: space-between;
        h2{
          color: #777777;
          font-size: 12px;
          font-weight: 400;
        }
        p{
          color: #3579D5;
          font-size: 12px;
          cursor: pointer;
        }
      }
      .privilegeUl{
        display: flex;
        align-items: center;
        gap: 10px;
        margin-top: 10px;
        li{
          width: 120px;
          height: 90px;
          background: #323444;
          border-radius: 4px;
          box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.3);
          font-size: 12px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          img{
            width: 24px;
            height: 22px;
          }
          p{
            background: linear-gradient(-90deg, #D89F77 0%, #D89F77 30.4443359375%, #EBCFB4 63.28125%, #F5E1CD 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            display: inline-flex;
            padding: 5px 5px 0 5px;
          }
        }
      }
    }
  }
  .hoverfilter:hover {
    filter: brightness(1.2);
    -webkit-filter: brightness(1.2);
     -webkit-tap-highlight-color: transparent;
    }
  .vip_style {
    background:none!important;
    -webkit-background-clip: initial!important;
    -webkit-text-fill-color: #999999!important;
  }
  </style>
