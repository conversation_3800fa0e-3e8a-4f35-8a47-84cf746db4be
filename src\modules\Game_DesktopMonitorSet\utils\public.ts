import {gamepp} from "gamepp";

export async function GPP_WriteInteger (id:number, value:number) {
    let old_value = await GPP_GetInteger(Number(id))
    if (Number(old_value) !== Number(value)) {
        console.log('setInteger,' + '' + Number(id) + ',' + Number(value));
        try {
            await gamepp.setting.setInteger.promise(Number(id), Number(value));
        } catch (error) {
        }
    }
}

export async function GPP_GetInteger (id:number) {
    let value = 0;
    try {value = await gamepp.setting.getInteger.promise(id);} catch {}
    return value;
}
