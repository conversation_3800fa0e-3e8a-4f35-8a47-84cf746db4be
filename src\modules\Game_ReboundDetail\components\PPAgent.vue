<script setup lang="ts">
import {computed, onMounted, ref, watch} from "vue";
import {useReboundDetailStore} from "@/modules/Game_ReboundDetail/stores/reboundDetail";
import {marked} from './marked.esm.js'
import PPAgentOnline from "@/modules/Game_ReboundDetail/components/PPAgentOnline.vue";
import PPAgentAmdAI from "@/modules/Game_ReboundDetail/components/PPAgentAmdAI.vue";
marked.setOptions({
  breaks: true,      // 换行转换为 <br>
  gfm: true,         // 启用 GitHub Flavored Markdown
  smartypants: true, // 使用智能引号等
  xhtml: true        // 生成符合 XHTML 的标签
});
// @ts-ignore
const gamepp:any = window.gamepp;

const canUse = ref(false) // 是否可以使用
const haveReport = ref(0) // 是否有报告
const no15Mins = ref(false); // 是不是不足15分钟的数据
const analysisMode = ref(0); // 分析模式 0 在线分析; 1 AMD Ryzen AI 本地分析;
const $store = useReboundDetailStore()
const ai_agent_bc = new BroadcastChannel('ai_agent')
onMounted(() => {
    checkCanUse()
    if ($store.ai_agent.resultContent) {
        haveReport.value = 1
    }
    gamepp.aiagent.onAIAgentMessage.addEventListener((e:any)=>{
        console.log(e);
        const data = JSON.parse(e)
        if (data?.type === 'DownloadLLMProgress') {
            Object.assign($store.ai_agent.downloadLLMProgress,data);
        } else if (data?.type === 'DownloadLLM') {
            if (data.code === 0) {
                $store.ai_agent.downloading = false;
                $store.ai_agent.downloadLLMProgress.error = "";
                window.localStorage.setItem('downloadLLM','1')
            } else if (data.code < 0) {
                $store.ai_agent.downloading = false;
                if (data.code === -1) {
                    $store.ai_agent.downloadLLMProgress.error = "GameRebound.theModelConfigurationLoadingFailed";
                }else if (data.code === -2) {
                    $store.ai_agent.downloadLLMProgress.error = "GameRebound.selectModelNotSupport";
                }else if (data.code === -3) {
                    $store.ai_agent.downloadLLMProgress.error = "GameRebound.delDirFail";
                }else if (data.code === -4) {
                    $store.ai_agent.downloadLLMProgress.error = "GameRebound.failedCreateModelDir";
                }else if (data.code === -6) {
                    $store.ai_agent.downloadLLMProgress.error = "GameRebound.modelNotBeenFullyDownload";
                }
            }
        } else if (data?.type === 'CheckLLM') {
            if (data.code === 0) {
                $store.ai_agent.downloaded = true;
            }else if (data.code < 0) {
                $store.ai_agent.downloaded = false;
            }
        } else if (data?.type === 'CancelDownloadLLM') {
            $store.ai_agent.downloading = false;
            $store.ai_agent.downloadLLMProgress.error = "";
        } else if (data?.type === 'RunLLM') {
            if (data.code === 0) {
                if (data.generate) { // 生成的整个内容
                    const agentid = 'AMDRyzenAI' + Math.random().toString(36).substr(2, 9)
                    $store.ai_agent.resultContent = data.generate;
                    ai_agent_bc.postMessage({action: 'get_content_success_amdryzenai',content:data.generate,ai_agent_id:agentid})
                    $store.ai_agent.isGenerating = false;
                }
            } else if (data.code === -1) {
                $store.ai_agent.RunLLMError = 'GameRebound.theModelConfigurationLoadingFailed'
                $store.ai_agent.isGenerating = false;
            } else if (data.code === -3) {
                $store.ai_agent.RunLLMError = 'GameRebound.theModelDirectoryDoesNotExist'
                $store.ai_agent.isGenerating = false;
            } else if (data.code === -4) {
                $store.ai_agent.RunLLMError = 'GameRebound.thereIsAMistakeInReasoning'
                $store.ai_agent.isGenerating = false;
            } else if (data.code === -5) {
                $store.ai_agent.RunLLMError = 'GameRebound.theInputExceedsTheModelLimit'
                $store.ai_agent.isGenerating = false;
            }
        } else if (data?.type === 'gen_status') {
            if (data.status === 1) { // 开始推理
                $store.ai_agent.isGenerating = true;
            } else if (data.status === 0) { // 结束推理
                $store.ai_agent.isGenerating = false;
            }
        } else if (data?.type === 'gen_data') {
            const gen_data = data.data;
            $store.ai_agent.resultContent += gen_data
        }
    })
})

async function checkCanUse(trycount=0)
{
    // 不足15分钟的数据
    if ($store.recentGameInfo.endtime - $store.recentGameInfo.starttime < 900)
    {
        no15Mins.value = true
    }
    const userBaseInfo = await gamepp.user.loadBaseInfo.promise()
    console.log('loadBaseInfo')
    console.log(JSON.stringify(userBaseInfo))
    if (userBaseInfo.ai_agent == 1) {
        canUse.value = true
    }else if (trycount <= 2){
        setTimeout(()=>{
            checkCanUse(trycount+1)
        },500)
    }
}

watch(()=>$store.ai_agent.resultContent,(newVal)=>{
    if (newVal) {
        console.log(newVal)
        haveReport.value = 1
    }
})

function reAddContent() {
    haveReport.value = 0
    const agentIdBc = new BroadcastChannel('ai_agent')
    agentIdBc.postMessage({action:"reAddContent"})
}

const markdownContent = computed(()=>{
    console.log(marked)
    return marked($store.ai_agent.resultContent)
})
</script>

<template>
    <div class="pp-agent-container scroll">
        <div class="wait-box" v-if="haveReport === 0">
            <div class="toggle" >
                <!--在线分析-->
                <div class="toggle-btn" @click="analysisMode = 0" :class="{'active':analysisMode === 0}">{{$t('GameRebound.onlineAnalysis')}}</div>
                <!--AMD Ryzen AI 本地分析-->
                <div class="toggle-btn" @click="analysisMode = 1" :class="{'active':analysisMode === 1}">{{$t('GameRebound.amdRyzenAILocalAnalysis')}}</div>
            </div>
            <PPAgentOnline v-if="analysisMode === 0" :canUse="canUse" :no15Mins="no15Mins"/>
            <PPAgentAmdAI  v-if="analysisMode === 1" :no15Mins="no15Mins"/>
        </div>
        <div class="report-box" v-if="haveReport === 1">
            <div class="report-title">
                <p style="user-select: text;" v-if="!$store.ai_agent.ai_agent_id.includes('AMDRyzenAI')">{{ $t('GameRebound.text2') }}</p>
                <p style="user-select: text;" v-if="$store.ai_agent.ai_agent_id.includes('AMDRyzenAI')">{{ $t('GameRebound.amdAiagentTitle') }}</p>
                <el-button style="padding: 0 20px" type="primary" color="#3578d3" size="small" :disabled="no15Mins || $store.ai_agent.statusNot200" @click="reAddContent">{{ $t('GameRebound.reDo') }}</el-button>
            </div>
            <p style="user-select: text;font-size: 16px;color: #c3c3c3;" v-if="!$store.ai_agent.ai_agent_id.includes('AMDRyzenAI')">{{ $t('GameRebound.agentId') }}{{$store.ai_agent.ai_agent_id}}</p>
            <!--<p>根据您提供的游戏加加性能统计数据，以下是综合分析和优化建议：</p>-->
            <p class="result-content" v-html="markdownContent">
            </p>
        </div>
    </div>
</template>

<style scoped lang="scss">
.pp-agent-container {
    height: calc(calc(100vh - 95px) / var(--zoomV--));
    width: 1280px;
    background-color: #22232e;
    overflow-y: auto;
    overflow-x: hidden;
    :deep(.el-button) {
        --el-button-disabled-bg-color: rgb(124, 124, 124) !important;
        --el-button-disabled-border-color: rgb(124, 124, 124) !important;
    }

    .wait-box {
        display: flex;
        align-items: center;
        height: 100%;
        flex-direction: column;

        .toggle {
            display: flex;
            flex-flow: row nowrap;
            gap: 44px;
            margin-top: 196px;
            margin-bottom: 20px;

            .toggle-btn {
                min-width: 240px;
                height: 50px;
                line-height: 50px;
                border-radius: 4px;
                box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.3);
                color: #fff;
                background: #2B2C37;
                text-align: center;
                font-size: 14px;
                cursor: pointer;

                &:hover,&.active {
                    background: #336AB5;
                }
            }
        }
    }

    .report-box {
        padding: 10px 20px;

        .report-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            line-height: 2.0;
            color: #c3c3c3;
            font-size: 15px;
        }
    }

    .result-content {
        margin-top: 10px;
        width: 1260px;
        word-break: break-all;
        line-height: 2.0;
        color: #c3c3c3;
        font-size: 15px;
        user-select: text;
    }
}
</style>
<style lang="scss">
.report-box .result-content hr{
    border: none;
    height: 1px;
    background-color: #333;
}
.report-box .result-content ul {
    list-style: disc;
}
.report-box .result-content ul li{
    list-style: disc;
    margin-left: 1.5em;
}
.report-box .result-content ol {
    list-style: decimal;
}
.report-box .result-content ol li{
    list-style: decimal;
    margin-left: 1.5em;
}
.report-box .result-content dl {
    list-style: circle;
}
.report-box .result-content dl li{
    list-style: circle;
    margin-left: 1.5em;
}
.report-box .result-content{
    table {
        border-right: 1px solid #777777;
        border-bottom: 1px solid #777777;
        text-align: center;
    }

    table th {
        padding: 0 15px;
        border-left: 1px solid #777777;
        border-top: 1px solid #777777;
        background-color: #333333;
    }

    table td {
        padding: 0 15px;
        border-left: 1px solid #777777;
        border-top: 1px solid #777777;
        text-align: left;
    }
}
</style>
