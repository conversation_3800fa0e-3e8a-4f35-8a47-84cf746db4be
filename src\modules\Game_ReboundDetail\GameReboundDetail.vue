<script setup lang="ts">
import {ref, reactive, watch, computed, onBeforeMount, onUnmounted, onMounted, watchEffect, provide} from 'vue';
import { gamepp } from 'gamepp'
import { AVGNum,MaxNum,MinNum,FormatSeconds,FormatTimeCn,FormatTime,FormatTimePlus, DiskCapacityConversion,FormartMonthToNumber,RemoveAllSpace,RegExGetNum} from '../../uitls/GameppTools'
//引入天气照片
import 冰雹 from './assets/icon/ic_report_bingbao.png'
import 雨 from './assets/icon/ic_report_yu.png'
import 阴 from './assets/icon/ic_report_yin.png'
import 沙尘 from './assets/icon/ic_report_shachen.png'
import 晴天 from './assets/icon/ic_report_qing.png'
import 雾霾 from './assets/icon/ic_report_wumai.png'
import 云 from './assets/icon/ic_report_yun.png'
import 雪 from './assets/icon/ic_report_xue.png'
import 雾 from './assets/icon/ic_report_wu.png'
import 雷 from './assets/icon/ic_report_lei.png'
// import {gameppBaseSetting} from "@/modules/Game_Home/stores";
import ReboundDetailHeader from "@/modules/Game_ReboundDetail/components/ReboundDetailHeader.vue";
import SimpleData from "@/modules/Game_ReboundDetail/components/SimpleData.vue";
import FullData from "@/modules/Game_ReboundDetail/components/FullData.vue";
import CpuPerformanceAnalysis from "@/modules/Game_ReboundDetail/components/CpuPerformanceAnalysis.vue";
import FPSLimitDialog from '@/modules/Game_ReboundDetail/components/FPSLimitDialog/FPSLimitDialog.vue'
import {useReboundDetailStore} from "@/modules/Game_ReboundDetail/stores/reboundDetail";
import GameMessageTip from "@/components/mainCom/GameMessageTip.vue";
import PPAgent from "@/modules/Game_ReboundDetail/components/PPAgent.vue";
import idb from "@/uitls/indexedDB";
import { useLanguage } from '@/uitls/useLanguage';
import {useI18n} from "vue-i18n";
import PPAgentDownloadDialog from "@/modules/Game_ReboundDetail/components/PPAgentDownloadDialog.vue";
useLanguage()

const i18n = useI18n()
let listData:any = ref([])
// const settingStore = gameppBaseSetting()
let hardwaerinfo:any = ref({

})
let version = ref('')
const $store = useReboundDetailStore()
let isMaunl = ref(false) //是否手动
let showMessageDialog = ref(false)
const tab3Tooltip = ref(false)

let recentGameInfo:any = ref({
    gameName:'???',
    resolution:'???',
    gametime:'???',
    iconsrc:'',
    starttime:0,
    endtime:0,
    cputemp:0,
    gputemp:0,
    fps:{
        avg:'???',
        min:'???',
        max:'???',
        fps01:'???'
    },
    cputemperature:{
        avg:'???',
        min:'???',
        max:'???',
        cputemperature:'???'
    },
    gputemperature:{
        avg:'???',
        min:'???',
        max:'???',
        gputemperature:'???'
    },
})

const powerData = ref<any>({
  errs: [],
  errs_reason: []
})

const activeTab = ref(0)

let zoomValue = ref<number>(1)
provide('zoomV',zoomValue)

let isShowWea = ref(false)
onBeforeMount(()=>{
  try {
    isShowWea.value = (gamepp.setting.getInteger.sync(521) === 1)
    gamepp.webapp.windows.setMinimumSize.sync('rebound_details_v2', 1292, 762)
  }catch(e){
    console.log(e);
  }
  initZoom()
})
let DatabaseTableRef = ref('')
let DatabaseId = ''
let DatabaseTable = ''

const currentLanguage =ref('CN')
const agentIdBc = new BroadcastChannel('ai_agent')
onMounted(async() =>
{
  getVersion()
  loadLanguage()
  window.addEventListener('storage', handleStorageChange);
  //该页面只要打开 必然会进入此监听器
  gamepp.webapp.onInternalAppEvent.addEventListener(async data =>
   {
        console.warn(data)
        if (data.hasOwnProperty('is_open_windows') && !data.is_open_windows) {
          gamepp.webapp.windows.close.sync('rebound_details_v2')
        }
        if (data.vip || data.not_show === 1) {
            await gamepp.webapp.windows.resize.promise('rebound_details_v2', 1292, 762);
        }
        DatabaseTableRef.value = data.table;
        DatabaseTable = data.table;
        let is_upload = data.is_upload;//上传退弹数据
        let is_open_windows = data.is_open_windows;
        let AppDataDir = await gamepp.getAppDataDir.promise();
        DatabaseId = await gamepp.database.open.promise(AppDataDir + '\\common\\Data\\GamePP5.dll');
       console.log("DatabaseId",DatabaseId)
       const isProcessTableExist = await gamepp.database.exists.promise(DatabaseId,+DatabaseTable+'_process')
       if (isProcessTableExist) {
           console.log(await gamepp.database.query.promise(DatabaseId, DatabaseTable+'_process', "*"))
           $store.dev.processTableInfo = await gamepp.database.query.promise(DatabaseId, "'"+DatabaseTable+'_process'+"'", "*");
           console.log('processTableInfo',$store.dev.processTableInfo)
       }
        listData.value = await gamepp.database.query.promise(DatabaseId, "GamePP_BaseInfo", "*", 'starttime="' + DatabaseTable + '"');
       //新增ai_agent_id
       let queryField_ai_agent_id = await gamepp.database.queryField.promise(DatabaseId, 'GamePP_BaseInfo', 'aiagentid');
       if (!queryField_ai_agent_id) {
           await gamepp.database.alter.promise(DatabaseId, 'GamePP_BaseInfo', 'aiagentid TEXT');
       }
       let queryField_agentcontent = await gamepp.database.queryField.promise(DatabaseId, 'GamePP_BaseInfo', 'agentcontent');
       if (!queryField_agentcontent) {
           await gamepp.database.alter.promise(DatabaseId, 'GamePP_BaseInfo', 'agentcontent TEXT');
       }
       let queryField_getcontentcount = await gamepp.database.queryField.promise(DatabaseId, 'GamePP_BaseInfo', 'getcontentcount');
       if (!queryField_getcontentcount) {
           await gamepp.database.alter.promise(DatabaseId, 'GamePP_BaseInfo', 'getcontentcount INTEGER');
       }
        let list = listData.value[0];
        console.warn('接收到的消息',list);
        let userAiAgent = 0
        try {
          userAiAgent = gamepp.user.getAIAgent.sync();
        }catch {
          userAiAgent = 0
        }
        if (list.aiagentid) {
            console.log(list.aiagentid)
            $store.ai_agent.ai_agent_id = list.aiagentid
            const agentcontent = await idb.getItem(list.aiagentid)
            console.log(agentcontent)
            if (agentcontent) {
                $store.ai_agent.resultContent = agentcontent
            }else if (!list.getcontentcount || list.getcontentcount < 10) {
                $store.ai_agent.isWaiting = true;
                getAgentContent(list.aiagentid,true)
            }
        }
        if (list.getcontentcount) {
            $store.ai_agent.try_count = list.getcontentcount
        }
        $store.ai_agent.baseinfo_id = list.id
        $store.ai_agent.baseinfo_timestamp = Number(DatabaseTable);
        try{
          const gn = await gamepp.utils.getGameNameByPath.promise(list.processpath);
          console.log(gn)
          if (gn) {
            recentGameInfo.value.gameName = gn
          }else{
            recentGameInfo.value.gameName = list.processpath.split('\\').pop()
          }
        }catch(e){
          recentGameInfo.value.gameName = list.processpath.split('\\').pop()
        }
        recentGameInfo.value.starttime = list.starttime
        recentGameInfo.value.resolution = list.resolutions
        recentGameInfo.value.gametime = FormatSeconds(list.gametime)
        recentGameInfo.value.endtime = list.starttime + list.gametime
        if (list.hd_list_data) {
          Object.assign(powerData.value,JSON.parse(decodeURIComponent(list.hd_list_data)))
          console.log(powerData.value)
        }
        await PM_GetOverview(list);//获取概况信息
        await PM_GetHardwareBase(list['hd_info']);//获取硬件信息
        console.warn('硬件信息：整理后',hardwaerinfo.value);
        $store.hardwaerinfo = hardwaerinfo.value
        $store.recentGameInfo = recentGameInfo.value
        let DetailedDataHtmlReturn = await ListHtmlProcess(listData.value[0],DatabaseId);
        localStorage.setItem('isMaualOpendetail',JSON.stringify(true))//设置手动发送
    });
    await gamepp.setting.setBool2.promise('window', 'rebound_details_v2', true);
    agentIdBc.onmessage = (ev) => {
        if (ev.data.action === 'ai_agent_id') {
            gamepp.database.update.promise(DatabaseId,"GamePP_BaseInfo",['aiagentid="' + ev.data.ai_agent_id + '"','getcontentcount="' + 0 + '"'], 'starttime = "' + DatabaseTable + '"')
            getAgentContent(ev.data.ai_agent_id)
        }else if (ev.data.action === 'add_content_err') {
            ElMessage.error(ev.data.msg)
            $store.ai_agent.disableCount = 30
            const interval = setInterval(()=>{
              $store.ai_agent.disableCount--;
              if ($store.ai_agent.disableCount <= 0) {
                clearInterval(interval)
              }
            },1000)
        }else if (ev.data.action === 'get_content_success') {
            tab3Tooltip.value = true
            if (getAgentContentTimer) clearInterval(getAgentContentTimer)
            idb.setItem(ev.data.ai_agent_id,ev.data.content)
            gamepp.database.update.promise(DatabaseId,"GamePP_BaseInfo",['agentcontent="' + ev.data.content + '"'], 'starttime = "' + DatabaseTable + '"')
        }else if (ev.data.action === 'get_content_success_amdryzenai') {
            idb.setItem(ev.data.ai_agent_id,ev.data.content)
            gamepp.database.update.promise(DatabaseId,"GamePP_BaseInfo",['agentcontent="' + ev.data.content + '"'], 'starttime = "' + DatabaseTable + '"')
        }else if (ev.data.action === 'reAddContent') {
            gamepp.database.update.promise(DatabaseId,"GamePP_BaseInfo",['aiagentid="' + '' + '"','agentcontent="' + '' + '"'], 'starttime = "' + DatabaseTable + '"')
        }else if (ev.data.action === 'code>1000') {
            $store.ai_agent.isWaiting = false;
            // 返回的code大于1000就不再请求了
            if (getAgentContentTimer) clearInterval(getAgentContentTimer)
            gamepp.database.update.promise(DatabaseId,"GamePP_BaseInfo",['getcontentcount="' + 1000 + '"'], 'starttime = "' + DatabaseTable + '"')
        }else if (ev.data.action === 'try_count++') {
            if (ev.data.content >= 10) { // 请求10次停止
                $store.ai_agent.isWaiting = false;
                if (getAgentContentTimer) clearInterval(getAgentContentTimer)
            }
            gamepp.database.update.promise(DatabaseId,"GamePP_BaseInfo",['getcontentcount="' + ev.data.content + '"'], 'starttime = "' + DatabaseTable + '"')
        }else if (ev.data.action === 'statusNot200') {
            $store.ai_agent.isWaiting = false;
            if (getAgentContentTimer) clearInterval(getAgentContentTimer)
        }
    }
})
const loadLanguage = () => {
    const savedLanguage = localStorage.getItem('language');
  if (savedLanguage) {
    currentLanguage.value = savedLanguage;
  }
};

const handleStorageChange = (event: any) => {
  if (event.key === 'language') {
    currentLanguage.value = event.newValue;
  }
};

onUnmounted(() => {
  window.removeEventListener('storage', handleStorageChange);
});

watch(zoomValue,(newValue)=>{
  document.body.style.zoom = newValue
    document.documentElement.style.setProperty('--zoomV--', `${newValue}`);
})

let getAgentContentTimer:any = null
function getAgentContent(id:string,immediate=false) {
    if (getAgentContentTimer) clearInterval(getAgentContentTimer)
    const dur = 1000 * 30 * 1;
    if (immediate) $store.handleGetContent(id)
    getAgentContentTimer = setInterval(()=>{
        $store.handleGetContent(id)
    },dur)
}

async function getVersion() {
  try {
    const versionObj:any = await gamepp.package.getversion.promise("GameRebound")
    if (Object.prototype.toString.call(versionObj) === '[object Object]' && 'version' in versionObj) {
      version.value = versionObj.version;
    }else{
      version.value = gamepp.getPlatformVersion.sync()
    }

  } catch (e) {
    console.log(e)
  }
}
async function initZoom() {
  try {
    const zoomWithSystem = gamepp.setting.getInteger.sync(313)
    if (zoomWithSystem === 1) {
      // 设置body zoom
      // document.body.style.zoom = zoomValue.value
      zoomValue.value = await gamepp.display.getScaleFromWindowInMonitor.promise();
      gamepp.webapp.windows.setMinimumSize.sync('rebound_details_v2', Math.floor(1292 * zoomValue.value),Math.floor(762 * zoomValue.value))
      gamepp.webapp.windows.resize.sync('rebound_details_v2',1292,762)
    }
  }catch (e) {
    zoomValue.value = 1
  }

  try {
    gamepp.display.onDisplayMetricsChanged.addEventListener( async (scaleFactor:number)=>{
      const zoomWithSystem = gamepp.setting.getInteger.sync(313)
      if (zoomWithSystem === 1) {
        console.log('display',scaleFactor)
        // zoom.value = display.scaleFactor
        zoomValue.value = scaleFactor
        // document.body.style.zoom = scaleFactor
        try{
          gamepp.webapp.windows.setMinimumSize.sync('rebound_details_v2', Math.floor(1292 * zoomValue.value),Math.floor(762 * zoomValue.value))
        }catch (e) {
          console.log('gamepp.webapp.windows.resize.sync(gamepp_config)',e)
        }
      }
      gamepp.webapp.windows.resize.sync('rebound_details_v2',1292,762)
    })
  }catch (e) {
    console.log(e)
  }
}
async function PM_GetOverview (data_arr:any) {
    let AppDataDir = await gamepp.getAppDataDir.promise();
    console.warn('GetOverview Start');
    for (let k in data_arr) {
        if (data_arr.hasOwnProperty(k)) {
            let value = data_arr[k]
            switch (k) {
                case 'processpath':
                    if(value)
                    {
                        let ProcesIcon = AppDataDir + '\\common\\Icon\\' + (value.split('\\').pop().replace('.exe','.png'));
                        hardwaerinfo.value['ProcesIcon'] = ProcesIcon
                        recentGameInfo.value.iconsrc = ProcesIcon

                        let Process = value.replace('.exe', '');
                        hardwaerinfo.value['ProcessName'] = Process
                    }
                    else
                    {
                        console.warn('手动记录无processIcon');
                        isMaunl.value = true
                    }
                    break;
                case 'starttime':
                    hardwaerinfo.value['starttime'] = (FormatTime(value))

                    break;
                case 'gametime':
                    hardwaerinfo.value['gametime'] = data_arr.endtime - data_arr.starttime
                    break;
                case 'endtime':
                     hardwaerinfo.value['endtime'] = FormatTime(value)

                    break;
                case 'city':
                    if (value !== 'null' && value !== '' && value !== null)
                    {
                      hardwaerinfo.value['city'] = value
                      hardwaerinfo.value['province'] = data_arr.province
                    }
                    else
                    {
                    }
                    break;
                case 'wea':
                    if (value !== 'null' && value !== '' && value !== null && value !== 'undefined')
                    {
                      hardwaerinfo.value['weather'] = value

                    }
                    break;
                case 'wea_img':
                    if (data_arr.wea && value) {
                        let wea_img = '';
                        const weaIcons:any = { "晴": 晴天, "阴": 阴, "雨": 雨, "云":  云, "雪": 雪, "冰": 冰雹, "雾": 雾, "雷": 雷, "霾": 雾霾 };
                        for (let weaType in weaIcons) {
                            if (data_arr.wea.includes(weaType)) {
                                wea_img = weaIcons[weaType];
                                hardwaerinfo.value['weatherIconSrc'] = weaIcons[weaType]
                                break;
                            }
                        }
                    }
                    break;
                case 'tem':
                    if (value && value !== 'null' && value !== 'undefined')
                    {
                      hardwaerinfo.value['tem'] = value
                    }
                    break;
                case 'resolutions':
                    if (value){
                        hardwaerinfo.value['game_resolutions'] = value
                    }
                    break;
                case 'exec_path':
                    if (value) {
                        if(value.includes('diskName')){
                          {
                              let diskInfo:any = JSON.parse(decodeURIComponent(value))
                              console.log('diskInfo:::',diskInfo)

                              hardwaerinfo.value['DiskName'] = diskInfo.diskName + ' (' + (diskInfo.partitionName).replace(':', '') + '盘)'
                              hardwaerinfo.value['gameDiskName'] = diskInfo.diskName
                              hardwaerinfo.value['DiskRemainSize'] = gamepp.readDiskSpace.sync(diskInfo.partitionName)
                          }
                      }
                    }
                  break;
          }
        }
    }
    console.warn('GetOverview End');
}

const handleDetailedData = async (DetailedData:any,DatabaseId:any,b:any,ponitmark:boolean) =>
{
    $store.isLab = true;
    var data_Det_arr:any = {};
        data_Det_arr['cpufan'] = {};
        data_Det_arr['gpufan'] = {};
        data_Det_arr['cpuload'] = {};
        data_Det_arr['cpuloadP'] = {};
        data_Det_arr['cpuloadE'] = {};
        data_Det_arr['cpuclock'] = {};
        data_Det_arr['cpuclockP'] = {};
        data_Det_arr['cpuclockE'] = {};
        data_Det_arr['cpupower'] = {};
        data_Det_arr['cputemperature'] = {};
        data_Det_arr['cpuvoltage'] = {};
        data_Det_arr['fps'] = {};
        data_Det_arr['fps1'] = {};
        data_Det_arr['fps01'] = {};
        data_Det_arr['frametime'] = {};
        data_Det_arr['gpuload'] = [];
        data_Det_arr['gpuload1'] = [];
        data_Det_arr['gpumemoryload'] = [];
        data_Det_arr['gpumemorytemp'] = {};
        data_Det_arr['gpumemoryclock'] = {};
        data_Det_arr['gpupower'] = [];
        data_Det_arr['gputemperature'] = [];
        data_Det_arr['gpuhotspottemp'] = {};
        data_Det_arr['gpuclock'] = [];
        data_Det_arr['gpuvoltage'] = [];
        data_Det_arr['memory'] = {};
        data_Det_arr['memorytemperature'] = {};
        data_Det_arr['ddr5temperature'] = [];
        data_Det_arr['ddr5voltage'] = [];

        data_Det_arr['diskload'] = {};
        data_Det_arr['disktemp'] = {};

        data_Det_arr['performance'] = {};
        data_Det_arr['amd_cpu_thermal'] = [];
        data_Det_arr['performance_gpu'] = [];
        data_Det_arr['amd_gpu_thermalhotspot'] = [];
        data_Det_arr['amd_gpu_thermalmemory'] = [];

        data_Det_arr['cpu_clock_core'] = [];
        data_Det_arr['cpu_clock_effective_core'] = [];

        data_Det_arr['disk_temp'] = {};
        data_Det_arr['disk_load'] = {};
        data_Det_arr['pageFaultDelta'] = {};

        let cpufanDetail = [],gpufanDetail = [];
        let cpuloadDetail = [],cpuloadPDetail = [],cpuloadEDetail = [], cpuclockDetail = [],cpuclockPDetail = [],cpuclockEDetail = [] ,cpupowerDetail = [], cputemperatureDetail = [], cpuvoltageDetail = [],
        gpuloadDetail0 = [], gpuloadDetail1 = [],gpuload1Detail0 = [], gpumemoryloadDetail0 = [], gpumemoryloadDetail1 = [], gpumemorytempDetail = [], gpumemoryclockDetail = [], gpupowerDetail0 = [], gpupowerDetail1 = [], gputemperatureDetail0 = [], gputemperatureDetail1 = [], gpuhotspottempDetail = [], gpuclockDetail0 = [], gpuclockDetail1 = [],
        gpuvoltageDetail0 = [], gpuvoltageDetail1 = [],
        fpsDetail = [],fps1Detail = [],fps01Detail = [], frametimeDetail = [], memoryDetail = [], memorytemperatureDetail = [], diskloadDetail = [], disktempDetail = [], performanceDetail = [], amd_cpu_thermalDetail = [], performance_gpu_THERMALDetail = [],amd_gpu_thermalhotspotDetail=[],amd_gpu_thermalmemoryDetail=[],
        cpu_clock_coreDetail     = [], cpu_clock_effective_coreDetail = [],disk_tempDetail=[],disk_loadDetail=[],pageFaultDeltaDetail=[],ddr5VoltageDetail = [],ddr5TempDetail = [];
        let del_interval = 1, DetailedDataLen = DetailedData.length;
        let cpuTemperatureCore = [], cpuLoadCore = [], cpuClockCore = [];
    for (let i = 0; i < DetailedData.length; i++) {
        if (DetailedData[i]['cpuload'].indexOf('NaN') === -1) {
            if (Number.isInteger(i / del_interval)) {
                if (DetailedData[i]['cpufan'] !== null && DetailedData[i]['cpufan'] !== undefined) {
                    cpufanDetail.push(Number(DetailedData[i]['cpufan']))
                }
                if (DetailedData[i]['gpufan'] !== null && DetailedData[i]['gpufan'] !== undefined) {
                    gpufanDetail.push(Number(DetailedData[i]['gpufan']))
                }

                cpuloadDetail.push(Number(DetailedData[i]['cpuload'].split('|')[0]));
                if (DetailedData[i]['cpuloadP'] || DetailedData[i]?.cpuloadP === 0) {cpuloadPDetail.push(DetailedData[i]['cpuloadP']);}
                if (DetailedData[i]['cpuloadE'] || DetailedData[i]?.cpuloadE === 0) {cpuloadEDetail.push(DetailedData[i]['cpuloadE']);}

                if (DetailedData[i]['cpuclockAVG'] || DetailedData[i]?.cpuclockAVG === 0) {
                    cpuclockDetail.push(Number(DetailedData[i]['cpuclockAVG']));
                } else {
                    cpuclockDetail.push(Number(DetailedData[i]['cpuclock'].split('|')[0]));
                }

                if (DetailedData[i]['cpuclockP']) {cpuclockPDetail.push(DetailedData[i]['cpuclockP']);}
                if (DetailedData[i]['cpuclockE']) {cpuclockEDetail.push(DetailedData[i]['cpuclockE']);}


                cpupowerDetail.push(Number(DetailedData[i]['cpupower']));

                if (typeof (DetailedData[i]['cputemperature']) != 'number') {cputemperatureDetail.push(Number(DetailedData[i]['cputemperature'].split('|')[0]));} else {cputemperatureDetail.push((DetailedData[i]['cputemperature']));}

                cpuvoltageDetail.push(Number(DetailedData[i]['cpuvoltage']))

                var gpuloadArr = DetailedData[i]['gpuload'].split('|');

                gpuloadDetail0.push(Number(DetailedData[i]['gpuload'].split('|')[0]));
                gpuloadDetail1.push(Number(DetailedData[i]['gpuload'].split('|')[1]));

                if (DetailedData[i]['gpuload1']!==undefined){
                    gpuload1Detail0.push(Number(DetailedData[i]['gpuload1'].split('|')[0]));
                }


                gpumemoryloadDetail0.push(Number(DetailedData[i]['gpumemoryload'].split('|')[0]));
                gpumemoryloadDetail1.push(Number(DetailedData[i]['gpumemoryload'].split('|')[1]));


                if (DetailedData[i]['gpumemorytemp']) {gpumemorytempDetail.push(Number(DetailedData[i]['gpumemorytemp']))}
                if (DetailedData[i]['gpumemoryclock']) {gpumemoryclockDetail.push(Number(DetailedData[i]['gpumemoryclock']))}


                gpupowerDetail0.push(Number(DetailedData[i]['gpupower'].split('|')[0]));
                gpupowerDetail1.push(Number(DetailedData[i]['gpupower'].split('|')[1]));

                gputemperatureDetail0.push(Number(DetailedData[i]['gputemperature'].split('|')[0]))
                gputemperatureDetail1.push(Number(DetailedData[i]['gputemperature'].split('|')[1]))


                gpuhotspottempDetail.push(Number(DetailedData[i]['gpuhotspottemp']))

                gpuclockDetail0.push(Number(DetailedData[i]['gpuclock'].split('|')[0]))
                gpuclockDetail1.push(Number(DetailedData[i]['gpuclock'].split('|')[1]))

                if (DetailedData[i]['gpuvoltage']) {
                    gpuvoltageDetail0.push(Number(DetailedData[i]['gpuvoltage'].split('|')[0]));
                    gpuvoltageDetail1.push(Number(DetailedData[i]['gpuvoltage'].split('|')[1]));
                }

                if (DetailedData[i]['fps']!== undefined) {fpsDetail.push(Number(DetailedData[i]['fps']))}

                if (DetailedData[i]['fps1']!== undefined) {fps1Detail.push(Number(DetailedData[i]['fps1']))}
                if (DetailedData[i]['fps01']!== undefined) {fps01Detail.push(Number(DetailedData[i]['fps01']))}

                if(DetailedData[i]['frametime'] !== undefined)
                {
                    if (DetailedData[i]['frametime']) {frametimeDetail.push(Number(DetailedData[i]['frametime']))}
                }

                memoryDetail.push(Number(DetailedData[i]['memory']));
                memorytemperatureDetail.push(Number(DetailedData[i]['memorytemperature']));
                if (DetailedData[i]['ddr5_temp'] && typeof DetailedData[i]['ddr5_temp']  === 'string') {
                  const _arr = DetailedData[i]['ddr5_temp'].split('|')
                  if (i === 0) {
                    let n = 0
                    for (let j = 0; j < _arr.length; j++) {
                      if (_arr[j] != 'null' && hardwaerinfo.value['memory_list_all'][n]) {
                        hardwaerinfo.value['memory_list_all'][n] += `[#${j}]`
                        n++
                      }
                    }
                  }
                  ddr5TempDetail.push(_arr.filter((item:string) => item !== 'null'))
                }
                if (DetailedData[i]['ddr5_voltage'] && typeof DetailedData[i]['ddr5_temp']  === 'string') {
                  const _arr = DetailedData[i]['ddr5_voltage'].split('|')
                  ddr5VoltageDetail.push(_arr.filter((item:string) => item !== 'null'))
                }


                diskloadDetail.push(Number(DetailedData[i]['disk_load']));
                disktempDetail.push(Number(DetailedData[i]['disk_temp']));


                if (DetailedData[i]['performance'] !== undefined) {
                    performanceDetail.push(DetailedData[i]['performance']);
                }else{
                    performanceDetail.push('');
                }
                if (DetailedData[i]['amd_cpu_thermal'] !== undefined) {
                    amd_cpu_thermalDetail.push(DetailedData[i]['amd_cpu_thermal']);
                }

                performance_gpu_THERMALDetail.push(Number(DetailedData[i]['performance_gpu']));

                amd_gpu_thermalhotspotDetail.push(DetailedData[i]['amd_gpu_thermalhotspot']);
                amd_gpu_thermalmemoryDetail.push(DetailedData[i]['amd_gpu_thermalmemory']);

                cpuTemperatureCore.push(DetailedData[i]['cpu_temperature_core'].split('|').map(Number));

                if (DetailedData[i]['cpu_load_core'])
                {
                    cpuLoadCore.push(DetailedData[i]['cpu_load_core'].split('|').map(Number));
                }
                if (DetailedData[i]['cpu_clock_core'])
                {
                    cpuClockCore.push(DetailedData[i]['cpu_clock_core'].split('|').map(Number));
                }

                if (DetailedData[i]['cpu_clock_core'])
                {
                    cpu_clock_coreDetail.push(DetailedData[i]['cpu_clock_core'])
                }

                if (DetailedData[i]['cpu_clock_effective_core'])
                {
                    cpu_clock_effective_coreDetail.push(DetailedData[i]['cpu_clock_effective_core'])
                }


                if (DetailedData[i]['disk_temp']!== undefined) {disk_tempDetail.push(Number(DetailedData[i]['disk_temp']))}
                if (DetailedData[i]['disk_load']!== undefined) {disk_loadDetail.push(Number(DetailedData[i]['disk_load']))}
                if (DetailedData[i]['pageFaultDelta'] !== null && DetailedData[i]['pageFaultDelta'] !== undefined) {
                  pageFaultDeltaDetail.push(Number(DetailedData[i]['pageFaultDelta']))
                }
            }
        }
    }

    let fps01_avg = AVGNum(fps1Detail)

//Fps低于1%low值
let cputempDetailLow = [], cpuloadDetailLow = [], cpuclockDetailLow = []
let cpuClockCore1 = [], cpuLoadCore1 = [];


let gputempDetailLow = [], gpuloadd3dDetailLow = [],gpuloadtotalDetailLow = [], gpuclockDetailLow = [],memoryloadDetailLow = [];

let isLowFPS01 = false

if (DetailedData[0]['cpu_clock_core'])
{
    for (let i = 0; i < DetailedData.length; i++) {
        if (DetailedData[i]['fps'] < fps01_avg) {
            isLowFPS01 = true
            cputempDetailLow.push((DetailedData[i]['cputemperature']));
            cpuloadDetailLow.push(Number(DetailedData[i]['cpuload'].split('|')[0]));
            cpuclockDetailLow.push(Number(DetailedData[i]['cpuclockAVG']));
            if (DetailedData[i]['cpu_clock_core']) {
                cpuClockCore1.push(DetailedData[i]['cpu_clock_core'].split('|').map(Number));
            }

            cpuLoadCore1.push(DetailedData[i]['cpu_load_core'].split('|').map(Number));

            gputempDetailLow.push(Number(DetailedData[i]['gputemperature'].split('|')[0]))
            gpuloadd3dDetailLow.push(Number(DetailedData[i]['gpuload'].split('|')[0]))
            gpuloadtotalDetailLow.push(Number(DetailedData[i]['gpuload1'].split('|')[0]))
            gpuclockDetailLow.push(Number(DetailedData[i]['gpuclock'].split('|')[0]))
            memoryloadDetailLow.push(Number(DetailedData[i]['memory']));
        }
    }
}

for (let i = 0; i < Object.keys(data_Det_arr).length; i++) {
        let key=Object.keys(data_Det_arr)[i]
        if (['cpufan','gpufan','cpuload', 'cpuloadP', 'cpuloadE', 'cpuclock', 'cpuclockP', 'cpuclockE', 'cpupower', 'cputemperature', 'cpuvoltage', 'fps','fps1','fps01', 'frametime', 'memory', 'memorytemperature','diskload','disktemp', 'gpuhotspottemp', 'gpumemorytemp', 'gpumemoryclock','disk_temp','disk_load','pageFaultDelta'].includes(key)) {
          DetailProcess(data_Det_arr, Object.keys(data_Det_arr)[i], eval(Object.keys(data_Det_arr)[i] + 'Detail'));
        }
    }

         function DetailProcess(data_Det_arr:any, ObjectKeys:any, Detail:any) {
            data_Det_arr[ObjectKeys]['detail'] = Detail;
            data_Det_arr[ObjectKeys]['avg'] = AVGNum(Detail)
            data_Det_arr[ObjectKeys]['max'] = MaxNum(Detail)
            data_Det_arr[ObjectKeys]['min'] = MinNum(Detail)
            if (ObjectKeys.includes('cpuvoltage')) {
              data_Det_arr[ObjectKeys]['avg'] = AVGNumForVoltage(Detail)
            }
         }
          function AVGNumForVoltage(Detail:any) {
            let sum = 0
            for (let value of Detail) {
              sum += value;
            }
            return (sum / Detail.length).toFixed(3);
          }


        data_Det_arr['cpuclock']['performance'] = performanceDetail;
        data_Det_arr['amd_cpu_thermal'] = amd_cpu_thermalDetail;


        data_Det_arr['performance_gpu'] = performance_gpu_THERMALDetail;
        data_Det_arr['amd_gpu_thermalhotspot'] = amd_gpu_thermalhotspotDetail;
        data_Det_arr['amd_gpu_thermalmemory'] = amd_gpu_thermalmemoryDetail;

        data_Det_arr['ddr5temperature'] = ddr5TempDetail
        data_Det_arr['ddr5voltage'] = ddr5VoltageDetail

        for (let j = 0; j < (gpuloadArr.length - 1); j++) {
            let data_Det_arr1:any = {};
            data_Det_arr1['detail'] = eval('gpuloadDetail' + j);
            data_Det_arr1['avg'] = AVGNum(eval('gpuloadDetail' + j));
            data_Det_arr1['max'] = MaxNum(eval('gpuloadDetail' + j));
            data_Det_arr1['min'] = MinNum(eval('gpuloadDetail' + j));
            data_Det_arr['gpuload'].push(data_Det_arr1);

            let data_Det_arr7:any = {};
            data_Det_arr7['detail'] = eval('gpuload1Detail' + j);
            data_Det_arr7['avg'] = AVGNum(eval('gpuload1Detail' + j));
            data_Det_arr7['max'] = MaxNum(eval('gpuload1Detail' + j));
            data_Det_arr7['min'] = MinNum(eval('gpuload1Detail' + j));
            data_Det_arr['gpuload1'].push(data_Det_arr7);

            console.warn('gpuload1Detail0',gpuload1Detail0);

            let data_Det_arr2:any = {};
            data_Det_arr2['detail'] = eval('gputemperatureDetail' + j);
            data_Det_arr2['avg'] = AVGNum(eval('gputemperatureDetail' + j));
            data_Det_arr2['max'] = MaxNum(eval('gputemperatureDetail' + j));
            data_Det_arr2['min'] = MinNum(eval('gputemperatureDetail' + j));
            data_Det_arr['gputemperature'].push(data_Det_arr2);

            let data_Det_arr3:any = {};
            data_Det_arr3['detail'] = eval('gpumemoryloadDetail' + j);
            data_Det_arr3['avg'] = AVGNum(eval('gpumemoryloadDetail' + j));
            data_Det_arr3['max'] = MaxNum(eval('gpumemoryloadDetail' + j));
            data_Det_arr3['min'] = MinNum(eval('gpumemoryloadDetail' + j));
            data_Det_arr['gpumemoryload'].push(data_Det_arr3);

            let data_Det_arr4:any = {};
            data_Det_arr4['detail'] = eval('gpupowerDetail' + j);
            data_Det_arr4['avg'] = AVGNum(eval('gpupowerDetail' + j));
            data_Det_arr4['max'] = MaxNum(eval('gpupowerDetail' + j));
            data_Det_arr4['min'] = MinNum(eval('gpupowerDetail' + j));
            data_Det_arr['gpupower'].push(data_Det_arr4);

            let data_Det_arr5:any = {};
            data_Det_arr5['detail'] = eval('gpuclockDetail' + j);
            data_Det_arr5['avg'] = AVGNum(eval('gpuclockDetail' + j));
            data_Det_arr5['max'] = MaxNum(eval('gpuclockDetail' + j));
            data_Det_arr5['min'] = MinNum(eval('gpuclockDetail' + j));
            data_Det_arr['gpuclock'].push(data_Det_arr5);

            let data_Det_arr6:any = {};
            data_Det_arr6['detail'] = eval('gpuvoltageDetail' + j);
            data_Det_arr6['avg'] = AVGNumForVoltage(eval('gpuvoltageDetail' + j));
            data_Det_arr6['max'] = MaxNum(eval('gpuvoltageDetail' + j));
            data_Det_arr6['min'] = MinNum(eval('gpuvoltageDetail' + j));
            data_Det_arr['gpuvoltage'].push(data_Det_arr6);
  }

  let dataData = PerformanceProcessing(data_Det_arr);

  console.warn('评测详情信息：处理后',dataData);

  powerData.value['full'] = dataData
  powerData.value['duration'] = []
  let len = 0
  len = dataData.fps.detail.length
  const gameTime = hardwaerinfo.value['gametime']
  for (let i = 0; i < len; i++) {
    powerData.value['duration'].push(gameTime / len * i)
  }
  powerData.value['full']['duration'] = powerData.value['duration']
  $store.powerData = powerData.value
  Object.keys(dataData).forEach(key => {
    if (Array.isArray(dataData[key]) && dataData[key][0]) {
      powerData.value[key] = dataData[key][0]
    }else{
      powerData.value[key] = dataData[key]
    }
  })

  recentGameInfo.value.fps = {
    avg:dataData['fps']['avg'],
    min:dataData['fps']['min'],
    max:dataData['fps']['max'],
    fps01:dataData['fps1']['avg']
  }
  try {
    recentGameInfo.value.cputemperature = {
      avg:dataData['cputemperature']['avg'],
      min:dataData['cputemperature']['min'],
      max:dataData['cputemperature']['max'],
      cputemperature:dataData['cputemperature']['avg']
    }
    recentGameInfo.value.gputemperature = {
      avg:dataData['gputemperature'][0]['avg'],
      min:dataData['gputemperature'][0]['min'],
      max:dataData['gputemperature'][0]['max'],
      gputemperature:dataData['gputemperature'][0]['avg']
    }
  }catch(e){

  }

  console.warn('recentGameInfo.value',recentGameInfo.value);
  if(!isMaunl.value)
  {
    let local = localStorage.getItem('recentGame') //更新最新游戏近况
        if(local == null)
        {
            localStorage.setItem('recentGame',JSON.stringify(recentGameInfo.value))
        }
        else
        {
            let Info  = JSON.parse(local)
            if(recentGameInfo.value.starttime > Info.starttime)
            {
                localStorage.setItem('recentGame',JSON.stringify(recentGameInfo.value))
            }
        }
  }

        let EndTimeData = DetailedData[DetailedData.length - 1];
        let StandGPULocaIndex = 0
        if (data_Det_arr.gpuload !== null) {
            if (data_Det_arr.gpuload.length === 2) {
                if (data_Det_arr.gpuload[0]['avg'] < data_Det_arr.gpuload[1]['avg']) {
                    StandGPULocaIndex = 1;
                } else {
                    StandGPULocaIndex = 0;
                }
            } else {
                StandGPULocaIndex = 0;
            }
        }

        function PowerProcess(data:any, cpuload:any, gpuload:any, GameTime:any, GPUIndex:number) {
               let  GPUEstimatePower:any = 0
               let GameTimeH:any = 0
               let  CPUEstimatePower:any = 0
               var cpower = (data.cpupower.avg * 2);
               var gpower = data.gpupower[GPUIndex].avg;
               if (cpuload <= 25) {
                  CPUEstimatePower = cpower * 1.7;
               } else if (cpuload > 25 && cpuload <= 50) {
                  CPUEstimatePower = cpower * 1.5;
               } else if (cpuload > 50 && cpuload <= 75) {
                  CPUEstimatePower = cpower * 1.3;
               } else if (cpuload > 75 && cpuload <= 100) {
                  CPUEstimatePower = cpower * 1.1;
               }
               if (gpuload <= 25) {
                  GPUEstimatePower = gpower * 1.25;
               } else if (gpuload > 25 && gpuload <= 50) {
                  GPUEstimatePower = gpower * 1.2;
               } else if (gpuload > 50 && gpuload <= 75) {
                  GPUEstimatePower = gpower * 1.15;
               } else if (gpuload > 75 && gpuload <= 100) {
                  GPUEstimatePower = gpower * 1.05;
               }
               GameTimeH = (((GameTime) / 3600));
               var TotalPower:any = CPUEstimatePower + GPUEstimatePower;
               var TotalPowerKWh:any = ((TotalPower / 1000) * GameTimeH) === 0 ? 0.01 : (TotalPower / 1000) * GameTimeH.toFixed(4);
               var TotalCarbon:any = parseFloat(((TotalPowerKWh * 0.785) * 1000).toFixed(4));
               var DataArr:any = [];
               DataArr['power'] = TotalPowerKWh;
               DataArr['carbon'] = TotalCarbon;
               return DataArr;
        }

        let PowerCarbon
        if (data_Det_arr.cpuload !== null && data_Det_arr.gpuload !== null && data_Det_arr.cpuload.avg) {
            // console.warn('b',b);
            PowerCarbon = PowerProcess(data_Det_arr, data_Det_arr.cpuload.avg, data_Det_arr.gpuload[StandGPULocaIndex].avg, (b.endtime - b.starttime), StandGPULocaIndex);
        } else {
            PowerCarbon = { "power": "0", "carbon": "0" };
        }
        try {
            // eval 赋值
            let hd_list_data:any = {};
            let hd_list_data_fps = {},hd_list_data_fps1 = {},hd_list_data_fps01 = {}, hd_list_data_memory = {},hd_list_data_memorytemperature={}, hd_list_data_diskload = {},hd_list_data_disktemp={};
            let hd_list_data_cpuload = {}, hd_list_data_cputemperature = {}, hd_list_data_cpuclock = {}, hd_list_data_cpupower = {}, hd_list_data_cpuvoltage = {};
            let hd_list_data_gpuload = {},hd_list_data_gpuload1 = {}, hd_list_data_gpumemoryload = {}, hd_list_data_gputemperature = {}, hd_list_data_gpuclock = {}, hd_list_data_gpupower = {}, hd_list_data_gpuvoltage = {};
            let hd_list_data_disk_temp = {},hd_list_data_disk_load = {};

            let KeyArr = ['fps', 'fps1', 'fps01', 'memory', 'memorytemperature', 'cpuclock', 'cpuload', 'cpupower', 'cputemperature', 'cpuvoltage', 'gpuclock', 'gpuload', 'gpuload1', 'gpumemoryload', 'gpupower', 'gputemperature', 'gpuvoltage', 'disk_temp', 'disk_load'];

            for (let i = 0; i < KeyArr.length; i++) {
                let key = KeyArr[i];
                let hd_list_data_ = 'hd_list_data_';
                if (key.includes('gpu')) {
                    eval("hd_list_data_" + [key])['max'] = data_Det_arr[key][StandGPULocaIndex].max;
                    eval("hd_list_data_" + [key])['min'] = data_Det_arr[key][StandGPULocaIndex].min;
                    eval("hd_list_data_" + [key])['avg'] = data_Det_arr[key][StandGPULocaIndex].avg;
                } else {
                    eval("hd_list_data_" + [key])['max'] = data_Det_arr[key].max;
                    eval("hd_list_data_" + [key])['min'] = data_Det_arr[key].min;
                    eval("hd_list_data_" + [key])['avg'] = data_Det_arr[key].avg;
                }
            }

            for (let i = 0; i < KeyArr.length; i++) {
                let key = KeyArr[i];
                hd_list_data[key] = eval('hd_list_data_' + key);
            }
            // console.warn('hd_list_data',hd_list_data);
            if(ponitmark){return} //如果是手动标记数据 不上传数据库

            hd_list_data['power'] = Number(parseFloat(PowerCarbon['power']));
            hd_list_data['co2'] = Number(parseFloat(PowerCarbon['carbon']));
            let _Info = encodeURIComponent(JSON.stringify(hd_list_data));
            try
            {
                await gamepp.database.update.promise(DatabaseId, "GamePP_BaseInfo", ['hd_list_data="' + _Info + '"'], 'id = "' + b.id + '"');
                console.warn('成功上传数据库:',_Info);

            }
            catch
            {
                console.warn('错误！！上传数据库:');
            }
            }
            catch(err)
            {
                console.log(err)
            }
}

const handleDetailedDataLab = async (DetailedData:any,DatabaseId:any,b:any,ponitmark:boolean) =>
{
    $store.isLab = true;
    var data_Det_arr:any = {};
    data_Det_arr['cpuload'] = {};
    data_Det_arr['cpuclock'] = {};
    data_Det_arr['cpupower'] = {};
    data_Det_arr['cputemperature'] = {};
    data_Det_arr['cpuvoltage'] = {};
    data_Det_arr['fps'] = {};
    data_Det_arr['fps1'] = {};
    data_Det_arr['fps01'] = {};
    data_Det_arr['frametime'] = {};
    data_Det_arr['gpuload'] = [];
    data_Det_arr['gpuload1'] = [];
    data_Det_arr['gpumemoryload'] = [];
    data_Det_arr['gpupower'] = [];
    data_Det_arr['gputemperature'] = [];
    data_Det_arr['gpuclock'] = [];
    data_Det_arr['gpuvoltage'] = [];
    data_Det_arr['memory'] = {};
    data_Det_arr['memorytemperature'] = {};
    data_Det_arr['performance'] = {};
    data_Det_arr['amd_cpu_thermal'] = [];

  let cpuloadDetail = [], cpuclockDetail = [], cpupowerDetail = [],gpuload1Detail0 = [], cputemperatureDetail = [], cpuvoltageDetail = [], gpuloadDetail0 = [], gpuloadDetail1 = [], gpumemoryloadDetail0 = [], gpumemoryloadDetail1 = [], gpupowerDetail0 = [], gpupowerDetail1 = [], gputemperatureDetail0 = [], gputemperatureDetail1 = [], gpuclockDetail0 = [], gpuclockDetail1 = [], gpuvoltageDetail0 = [], gpuvoltageDetail1 = [], fpsDetail = [],fps1Detail = [],fps01Detail = [], frametimeDetail = [], memoryDetail = [],memorytemperatureDetail = [], performanceDetail = [], amd_cpu_thermalDetail = [];
  let del_interval = 1, DetailedDataLen = DetailedData.length;

for (let i = 0; i < DetailedData.length; i++) {
    if (DetailedData[i]['cpuload'].indexOf('NaN') === -1) {
      if (Number.isInteger(i / del_interval)) {

        cpuloadDetail.push(Number(DetailedData[i]['cpuload'].split('|')[0]));

        if (DetailedData[i]['cpuclockAVG']) {
          cpuclockDetail.push(Number(DetailedData[i]['cpuclockAVG']));
        } else {
          cpuclockDetail.push(Number(DetailedData[i]['cpuclock'].split('|')[0]));
        }

        cpupowerDetail.push(Number(DetailedData[i]['cpupower']));
        if (typeof (DetailedData[i]['cputemperature']) != 'number') {cputemperatureDetail.push(Number(DetailedData[i]['cputemperature'].split('|')[0]));} else {cputemperatureDetail.push((DetailedData[i]['cputemperature']));}
        cpuvoltageDetail.push(Number(DetailedData[i]['cpuvoltage']))

        var gpuloadArr = DetailedData[i]['gpuload'].split('|');

        gpuloadDetail0.push(Number(DetailedData[i]['gpuload'].split('|')[0]));
        gpuloadDetail1.push(Number(DetailedData[i]['gpuload'].split('|')[1]));

        if (DetailedData[i]['gpuload1'] !== undefined){
            gpuload1Detail0.push(Number(DetailedData[i]['gpuload1'].split('|')[0]));
        }


        gpumemoryloadDetail0.push(Number(DetailedData[i]['gpumemoryload'].split('|')[0]));
        gpumemoryloadDetail1.push(Number(DetailedData[i]['gpumemoryload'].split('|')[1]));

        gpupowerDetail0.push(Number(DetailedData[i]['gpupower'].split('|')[0]));
        gpupowerDetail1.push(Number(DetailedData[i]['gpupower'].split('|')[1]));

        gputemperatureDetail0.push(Number(DetailedData[i]['gputemperature'].split('|')[0]))
        gputemperatureDetail1.push(Number(DetailedData[i]['gputemperature'].split('|')[1]))

        gpuclockDetail0.push(Number(DetailedData[i]['gpuclock'].split('|')[0]))
        gpuclockDetail1.push(Number(DetailedData[i]['gpuclock'].split('|')[1]))

        if (DetailedData[i]['gpuvoltage']) {
          gpuvoltageDetail0.push(Number(DetailedData[i]['gpuvoltage'].split('|')[0]));
          gpuvoltageDetail1.push(Number(DetailedData[i]['gpuvoltage'].split('|')[1]));
        }

        if (DetailedData[i]['fps']) {fpsDetail.push(Number(DetailedData[i]['fps']))}
        if (DetailedData[i]['fps1']) {fps1Detail.push(Number(DetailedData[i]['fps1']))}
        if (DetailedData[i]['fps01']) {fps01Detail.push(Number(DetailedData[i]['fps01']))}

        if (DetailedData[i]['frametime']) {frametimeDetail.push(Number(DetailedData[i]['frametime']))}
        memoryDetail.push(Number(DetailedData[i]['memory']));
        memorytemperatureDetail.push(Number(DetailedData[i]['memorytemperature']));

        if (DetailedData[i]['performance'] !== undefined) {
          performanceDetail.push(DetailedData[i]['performance']);
        }
        if (DetailedData[i]['amd_cpu_thermal']){
          amd_cpu_thermalDetail.push(DetailedData[i]['amd_cpu_thermal']);
        }
      }
    }
  }

  for (let i = 0; i < Object.keys(data_Det_arr).length; i++) {
    if (['cpuload', 'cpuclock', 'cpupower', 'cputemperature', 'cpuvoltage', 'fps','fps1','fps01', 'frametime', 'memory','memorytemperature'].includes(Object.keys(data_Det_arr)[i])) {
      DetailProcess(data_Det_arr, Object.keys(data_Det_arr)[i], eval(Object.keys(data_Det_arr)[i] + 'Detail'));
    }
  }

  data_Det_arr['cpuclock']['performance'] = performanceDetail;
  data_Det_arr['amd_cpu_thermal'] = amd_cpu_thermalDetail;






    function DetailProcess(data_Det_arr:any, ObjectKeys:any, Detail:any) {
    data_Det_arr[ObjectKeys]['detail'] = Detail;
    data_Det_arr[ObjectKeys]['avg'] = AVGNum(Detail)
    data_Det_arr[ObjectKeys]['max'] = MaxNum(Detail)
    data_Det_arr[ObjectKeys]['min'] = MinNum(Detail)
    }


    for (let j = 0; j < (gpuloadArr.length - 1); j++) {
    let data_Det_arr1:any = {};
    data_Det_arr1['detail'] = eval('gpuloadDetail' + j);
    data_Det_arr1['avg'] = AVGNum(eval('gpuloadDetail' + j));
    data_Det_arr1['max'] = MaxNum(eval('gpuloadDetail' + j));
    data_Det_arr1['min'] = MinNum(eval('gpuloadDetail' + j));
    data_Det_arr['gpuload'].push(data_Det_arr1);

    let data_Det_arr7:any = {};
    data_Det_arr7['detail'] = eval('gpuload1Detail' + j);
    data_Det_arr7['avg'] = AVGNum(eval('gpuload1Detail' + j));
    data_Det_arr7['max'] = MaxNum(eval('gpuload1Detail' + j));
    data_Det_arr7['min'] = MinNum(eval('gpuload1Detail' + j));
    data_Det_arr['gpuload1'].push(data_Det_arr7);

    console.warn('gpuload1Detail0',gpuload1Detail0);

    let data_Det_arr2:any = {};
    data_Det_arr2['detail'] = eval('gputemperatureDetail' + j);
    data_Det_arr2['avg'] = AVGNum(eval('gputemperatureDetail' + j));
    data_Det_arr2['max'] = MaxNum(eval('gputemperatureDetail' + j));
    data_Det_arr2['min'] = MinNum(eval('gputemperatureDetail' + j));
    data_Det_arr['gputemperature'].push(data_Det_arr2);

    let data_Det_arr3:any = {};
    data_Det_arr3['detail'] = eval('gpumemoryloadDetail' + j);
    data_Det_arr3['avg'] = AVGNum(eval('gpumemoryloadDetail' + j));
    data_Det_arr3['max'] = MaxNum(eval('gpumemoryloadDetail' + j));
    data_Det_arr3['min'] = MinNum(eval('gpumemoryloadDetail' + j));
    data_Det_arr['gpumemoryload'].push(data_Det_arr3);

    let data_Det_arr4:any = {};
    data_Det_arr4['detail'] = eval('gpupowerDetail' + j);
    data_Det_arr4['avg'] = AVGNum(eval('gpupowerDetail' + j));
    data_Det_arr4['max'] = MaxNum(eval('gpupowerDetail' + j));
    data_Det_arr4['min'] = MinNum(eval('gpupowerDetail' + j));
    data_Det_arr['gpupower'].push(data_Det_arr4);

    let data_Det_arr5:any = {};
    data_Det_arr5['detail'] = eval('gpuclockDetail' + j);
    data_Det_arr5['avg'] = AVGNum(eval('gpuclockDetail' + j));
    data_Det_arr5['max'] = MaxNum(eval('gpuclockDetail' + j));
    data_Det_arr5['min'] = MinNum(eval('gpuclockDetail' + j));
    data_Det_arr['gpuclock'].push(data_Det_arr5);

    let data_Det_arr6:any = {};
    data_Det_arr6['detail'] = eval('gpuvoltageDetail' + j);
    data_Det_arr6['avg'] = AVGNum(eval('gpuvoltageDetail' + j));
    data_Det_arr6['max'] = MaxNum(eval('gpuvoltageDetail' + j));
    data_Det_arr6['min'] = MinNum(eval('gpuvoltageDetail' + j));
    data_Det_arr['gpuvoltage'].push(data_Det_arr6);
  }


  let dataData = PerformanceProcessing(data_Det_arr);

  console.warn('评测详情信息：处理后',dataData);
  powerData.value['full'] = dataData
  powerData.value['duration'] = []
  let len = 0
  len = dataData.gpuload[0].detail.length
  const gameTime = hardwaerinfo.value['gametime']
  for (let i = 0; i < len; i++) {
    powerData.value['duration'].push(gameTime / len * i)
  }
  powerData.value['full']['duration'] = powerData.value['duration']
  $store.powerData = powerData.value
  Object.keys(dataData).forEach(key => {
    if (Array.isArray(dataData[key]) && dataData[key][0]) {
      powerData.value[key] = dataData[key][0]
    }else{
      powerData.value[key] = dataData[key]
    }
  })
      //   function create_variable (num:number) {
      //       var name = "DetailedData_" + num    //生成函数名
      //       var Hd_Info = "HdInfo_" + num    //生成函数名
      //       window[name] = PerformanceProcessing(data_Det_arr);
      //       window[Hd_Info] = b['hd_info'];
      //   }



        function PowerProcess(data:any, cpuload:any, gpuload:any, GameTime:any, GPUIndex:number) {
              //  console.warn('GameTime',GameTime);

               let  GPUEstimatePower:any = 0
               let GameTimeH:any = 0
               let  CPUEstimatePower:any = 0
               var cpower = (data.cpupower.avg * 2);
               var gpower = data.gpupower[GPUIndex].avg;
               if (cpuload <= 25) {
                  CPUEstimatePower = cpower * 1.7;
               } else if (cpuload > 25 && cpuload <= 50) {
                  CPUEstimatePower = cpower * 1.5;
               } else if (cpuload > 50 && cpuload <= 75) {
                  CPUEstimatePower = cpower * 1.3;
               } else if (cpuload > 75 && cpuload <= 100) {
                  CPUEstimatePower = cpower * 1.1;
               }
               if (gpuload <= 25) {
                  GPUEstimatePower = gpower * 1.25;
               } else if (gpuload > 25 && gpuload <= 50) {
                  GPUEstimatePower = gpower * 1.2;
               } else if (gpuload > 50 && gpuload <= 75) {
                  GPUEstimatePower = gpower * 1.15;
               } else if (gpuload > 75 && gpuload <= 100) {
                  GPUEstimatePower = gpower * 1.05;
               }
               GameTimeH = (((GameTime) / 3600));
               var TotalPower:any = CPUEstimatePower + GPUEstimatePower;
               // var TotalPowerKWh:any = ((TotalPower / 1000) * GameTimeH) === 0 ? 0.01 : parseFloat((TotalPower / 1000) * GameTimeH).toFixed(4);
               var TotalPowerKWh:any = ((TotalPower / 1000) * GameTimeH) === 0 ? 0.01 : (TotalPower / 1000) * GameTimeH.toFixed(4);
               var TotalCarbon:any = parseFloat(((TotalPowerKWh * 0.785) * 1000).toFixed(4));
              //  console.warn('GameTimeH',GameTimeH);
              //  console.warn('TotalPower',TotalPower);
              //  console.warn('TotalPowerKWh',TotalPowerKWh);
              //  console.warn('TotalCarbon',TotalCarbon);

               var DataArr:any = [];
               DataArr['power'] = TotalPowerKWh;
               DataArr['carbon'] = TotalCarbon;
               return DataArr;
        }


// let main_gpu = await GetMainGPUIndex(HdJsonInfoObj);

        let PowerCarbon
        if (data_Det_arr.cpuload !== null && data_Det_arr.gpuload !== null && data_Det_arr.cpuload.avg) {
            // console.warn('b',b);
            PowerCarbon = PowerProcess(data_Det_arr, data_Det_arr.cpuload.avg, data_Det_arr.gpuload[0].avg, (b.endtime - b.starttime), 0);
        } else {
            PowerCarbon = { "power": "0", "carbon": "0" };
        }
        console.warn('p1');
        try {
            // eval 赋值
            // eval 赋值
            let hd_list_data:any = {}; //手动记录
            let hd_list_data_fps = {}, hd_list_data_memory = {},hd_list_data_memorytemperature={};
            let hd_list_data_cpuload = {}, hd_list_data_cputemperature = {}, hd_list_data_cpuclock = {}, hd_list_data_cpupower = {}, hd_list_data_cpuvoltage = {};
            let hd_list_data_gpuload = {}, hd_list_data_gpumemoryload = {}, hd_list_data_gputemperature = {}, hd_list_data_gpuclock = {}, hd_list_data_gpupower = {}, hd_list_data_gpuvoltage = {};
            let hd_list_data_gpuload1 = {}
            let KeyArr = ['memory','memorytemperature', 'cpuclock', 'cpuload', 'cpupower', 'cputemperature', 'cpuvoltage', 'gpuclock', 'gpuload','gpuload1', 'gpumemoryload', 'gpupower', 'gputemperature', 'gpuvoltage'];
            for (let i = 0; i < KeyArr.length; i++) {
                let key = KeyArr[i];
                let hd_list_data_ = 'hd_list_data_';
                if (key.includes('gpu')) {
                eval("hd_list_data_" + [key])['max'] = data_Det_arr[key][0].max;
                eval("hd_list_data_" + [key])['min'] = data_Det_arr[key][0].min;
                eval("hd_list_data_" + [key])['avg'] = data_Det_arr[key][0].avg;
                } else {
                eval("hd_list_data_" + [key])['max'] = data_Det_arr[key].max;
                eval("hd_list_data_" + [key])['min'] = data_Det_arr[key].min;
                eval("hd_list_data_" + [key])['avg'] = data_Det_arr[key].avg;
                }
            }
            console.warn('p2');
            for (let i = 0; i < KeyArr.length; i++) {
                let key = KeyArr[i];
                hd_list_data[key] = eval('hd_list_data_' + key);
            }
            // console.warn('hd_list_data',hd_list_data);
            hd_list_data['power'] = Number(parseFloat(PowerCarbon['power']));
            hd_list_data['co2'] = Number(parseFloat(PowerCarbon['carbon']));
            console.warn('p3',hd_list_data);
            let _Info = encodeURIComponent(JSON.stringify(hd_list_data));
            try
            {
                await gamepp.database.update.promise(DatabaseId, "GamePP_BaseInfo", ['hd_list_data="' + _Info + '"'], 'id = "' + b.id + '"');
                console.warn('hd_list_data成功上传数据库:',_Info);
            }
            catch
            {
                console.warn('错误！！上传数据库:');
            }
            }
            catch(err)
            {
                console.log(err)
            }
}
function showNumberOfCPUCores (CPUSubNode: any) {
  let CPUCoresPEHtml = '';
  // 大小核处理
  if (CPUSubNode.NumberofCPUCoresPerformance && CPUSubNode.NumberofCPUCoresEfficient) {
    if (CPUSubNode.NumberofCPUCoresPerformance !== 0 || CPUSubNode.NumberofCPUCoresEfficient !== 0) {
      if (CPUSubNode.NumberofCPUCoresLowPowerEfficient) {
        CPUCoresPEHtml = ' (' + CPUSubNode.NumberofCPUCoresPerformance + 'P+' + CPUSubNode.NumberofCPUCoresEfficient + 'E+' + CPUSubNode.NumberofCPUCoresLowPowerEfficient+')'
      } else {
        CPUCoresPEHtml = ' (' + CPUSubNode.NumberofCPUCoresPerformance + 'P+' + CPUSubNode.NumberofCPUCoresEfficient + 'E)'
      }
    }
  }
  // AMD AI 300系列
  if (CPUSubNode.NumberofCPUCoresClassic || CPUSubNode.NumberofCPUCoresCompact) {
    if (CPUSubNode.NumberofCPUCoresClassic !== 0 || CPUSubNode.NumberofCPUCoresCompact !== 0) {
      CPUCoresPEHtml += ' ('
      if (CPUSubNode.NumberofCPUCoresClassic && CPUSubNode.NumberofCPUCoresClassic !== 0) {
        CPUCoresPEHtml += CPUSubNode.NumberofCPUCoresClassic
      }
      if (CPUSubNode.NumberofCPUCoresCompact && CPUSubNode.NumberofCPUCoresCompact !== 0) {
        if (CPUSubNode.NumberofCPUCoresClassic && CPUSubNode.NumberofCPUCoresClassic !== 0) {
          CPUCoresPEHtml += '+'
        }
        CPUCoresPEHtml += CPUSubNode.NumberofCPUCoresCompact+'C'
      }
      CPUCoresPEHtml += ')'
    }
  }
  return CPUCoresPEHtml;
}
function showNumberOfLogicalCpus(CPUSubNode: any) {
  let CPULogicalPEHtml = '';
  if (CPUSubNode.NumberofLogicalCPUsPerformance && CPUSubNode.NumberofLogicalCPUsEfficient) {
    if (CPUSubNode.NumberofLogicalCPUsPerformance !== 0 || CPUSubNode.NumberofLogicalCPUsEfficient !== 0) {
      CPULogicalPEHtml = ' (' + CPUSubNode.NumberofLogicalCPUsPerformance + 'P+' + CPUSubNode.NumberofLogicalCPUsEfficient + 'E)'
    }
  }
  if (CPUSubNode.NumberofLogicalCPUsClassic || CPUSubNode.NumberofLogicalCPUsCompact) {
    if (CPUSubNode.NumberofLogicalCPUsClassic !== 0 || CPUSubNode.NumberofLogicalCPUsCompact !== 0) {
      CPULogicalPEHtml = ' ('
      let haveNumberofLogicalCPUsClassic = (CPUSubNode.NumberofLogicalCPUsClassic && CPUSubNode.NumberofLogicalCPUsClassic !== 0)
      if (haveNumberofLogicalCPUsClassic) {
        CPULogicalPEHtml += CPUSubNode.NumberofLogicalCPUsClassic
      }
      if (CPUSubNode.NumberofLogicalCPUsCompact && CPUSubNode.NumberofLogicalCPUsCompact !== 0) {
        if (haveNumberofLogicalCPUsClassic) {
          CPULogicalPEHtml += '+'
        }
        CPULogicalPEHtml += CPUSubNode.NumberofLogicalCPUsCompact+'C'
      }
      CPULogicalPEHtml += ')'
    }
  }

  return CPULogicalPEHtml
}
const gpu_brand = (data: any) => {
  const CPUSubvendor = (data.VideoCard).split(' ')
  const regex = /\[(.+?)\]/g
  const VideoBrandArr = (data.VideoCard.match(regex))
  let VideoBrand = ''
  if (VideoBrandArr) {
    VideoBrand = (VideoBrandArr[VideoBrandArr.length - 1]).replace(/\[|]/g, '')
  }
  if (VideoBrand === '') {
    VideoBrand = CPUSubvendor[0]
  }
    const VideoBrand1Lower =VideoBrand.toLowerCase()
    if (VideoBrand1Lower.includes('清华同方') || VideoBrand1Lower.includes('tongfang') ||  VideoBrand1Lower.includes('thtf')) {
        return '品牌显示存在潜在法律风险'
    }
  return VideoBrand
}
const PM_GetHardwareBase = async (BaseJsonStr:any) =>
{
  console.warn('GetHardwareBase Start');
   let data_arr = null;
   if (BaseJsonStr) {
      data_arr = JSON.parse(decodeURIComponent(BaseJsonStr));
   } else {
      data_arr = JSON.parse(await gamepp.hardware.getBaseJsonInfo.promise());
   }
   try {
       $store.dev.hwinfo = data_arr;
       $store.dev.PCType = data_arr.MOBO.Mainboard.CaseType;
   }catch (e) {
       $store.dev.PCType = ''
       console.log(e)
   }
   console.warn('硬件详情信息',data_arr);

   for(let key in data_arr)
   {
      let HWInfoV = data_arr[key]
      // console.warn('DataArrINfo',key,data_arr[key]);
      switch(key)
      {
         case 'COMPUTER':
           let SystemName = ''
           if (HWInfoV.OperatingSystem) {
             SystemName = (HWInfoV.OperatingSystem
               .replace('Microsoft ', '')
               .replace('Build ', ''));
           }
           hardwaerinfo.value['SystemName'] =  SystemName
        break;
        case 'CPU':
          let CPUType = 'intel'
          if (HWInfoV['SubNode'] && HWInfoV['SubNode'][0]) {
            const CPUSubNode = HWInfoV['SubNode'][0];
            // let OriginalProcessorFrequency = Number(CPUSubNode["OriginalProcessorFrequency[MHz]"]) - 50;
            if ((CPUSubNode['ProcessorName'] || '').includes('Intel')) {CPUType = 'intel'} else {CPUType = 'amd'}
            // console.warn('CPUType',CPUType);
            hardwaerinfo.value['CPUType'] =  CPUType
            hardwaerinfo.value['ProcessorName'] =  CPUSubNode['ProcessorName']
            hardwaerinfo.value['CPUCores'] = showNumberOfCPUCores(CPUSubNode)
            hardwaerinfo.value['LogicalCPUs'] = showNumberOfLogicalCpus(CPUSubNode)
            hardwaerinfo.value['NumberofCPUCores'] = CPUSubNode.NumberofCPUCores
            hardwaerinfo.value['NumberofLogicalCPUs'] = CPUSubNode.NumberofLogicalCPUs
            hardwaerinfo.value['L3Cache'] = CPUSubNode.L3Cache
            hardwaerinfo.value['CPUTechnology'] = CPUSubNode.CPUTechnology
            hardwaerinfo.value['OriginalProcessorFrequency'] = CPUSubNode.OriginalProcessorFrequency
            hardwaerinfo.value['CPUTurboMax'] = CPUSubNode.CPUTurboMax ? CPUSubNode.CPUTurboMax.toLowerCase().split('mhz')[0] + 'MHz' : ''
            hardwaerinfo.value['InstructionTLB'] = CPUSubNode.InstructionTLB // 指令集
          }
        break;
        case 'MOBO':
          let MainboardName = '',SystemManufacturer,MotherboardChipset
          if (HWInfoV['Mainboard'] && HWInfoV['Mainboard']['MainboardName'])
          {
            MainboardName =  HWInfoV['Mainboard']['MainboardName'];
          }

          if (HWInfoV['Mainboard'] && HWInfoV['Mainboard']['MainboardManufacturer'])
          {
            if (HWInfoV['Mainboard']['MainboardManufacturer'] === 'Notebook')
            {
              SystemManufacturer = HWInfoV['System']['SystemManufacturer']
            }
            else
            {
              SystemManufacturer = HWInfoV['Mainboard']['MainboardManufacturer'].replace('Technology', '').replace('And', '').replace('Development', '').replace('Computer', '').replace('COMPUTER', '').replace('Co.,LTD', '').replace('INC.', '')
            }
          }
          else
          {
            SystemManufacturer = ''
          }
          if (HWInfoV['Property'] && HWInfoV['Property']['MotherboardChipset'] && typeof HWInfoV['Property']['MotherboardChipset'] === 'string')
          {
            MotherboardChipset = HWInfoV['Property']['MotherboardChipset'].replace(/\(.*?\)/g, '')
          }
          else
          {
            MotherboardChipset = ''
          }
          hardwaerinfo.value['MainboardName'] =  MainboardName
          hardwaerinfo.value['SystemManufacturer'] =  SystemManufacturer
          hardwaerinfo.value['MotherboardChipset'] =  MotherboardChipset
          hardwaerinfo.value['BIOSVersion'] =  HWInfoV.BIOS?.BIOSVersion
          if (!hardwaerinfo.value['BIOSVersion']) hardwaerinfo.value['BIOSVersion'] = ''
        break;
        case 'DRIVES':
            let gameDiskName =  hardwaerinfo.value['gameDiskName']
            if (HWInfoV['SubNode'])
            {
              let drive_index:any = HWInfoV['SubNode'].findIndex((item:any) => item['DriveModel'] === gameDiskName);
              drive_index = drive_index !== -1 ? drive_index : 0;
              let DriveData = HWInfoV['SubNode'][drive_index]
              let drivesType = 'HDD'
              if ((DriveData.MediaRotationRate && (DriveData.MediaRotationRate).indexOf('SSD') !== -1) || (DriveData.DriveController)?.includes('NVMe') || (DriveData.Interface)?.includes('NVMe')) {drivesType = 'SSD';} else if (DriveData.MediaRotationRate && (DriveData.MediaRotationRate).includes('RPM')) {drivesType === 'HDD'} else {drivesType === 'HDD'}
              if (((DriveData.DriveModel)?.toLowerCase()).includes('ssd')) {drivesType = 'SSD'}
              hardwaerinfo.value['drivesType'] = drivesType
              hardwaerinfo.value['Drive_size'] = DiskCapacityConversion(DriveData['DriveCapacity[MB]'], 1024)
              hardwaerinfo.value['Drive_size[MB]'] = DriveData['DriveCapacity[MB]']*1
              if (!gameDiskName) {
                hardwaerinfo.value['disk_name'] = DriveData.DriveModel || ''
              }
              hardwaerinfo.value['DriveController'] = DriveData.DriveController || ''
            }
            else
            {
              hardwaerinfo.value['drivesType'] = ''
              hardwaerinfo.value['Drive_size'] = ''
              hardwaerinfo.value['Drive_size[MB]'] = 0
              hardwaerinfo.value['disk_name'] = ''
              hardwaerinfo.value['DriveController'] = ''
            }
        break;
        case 'GPU':
            let GPUIndex = FilterGPUIndexFromMemoryNumber(data_arr)

            function FilterGPUIndexFromMemoryNumber (JsonInfo:any) {
              let srcIndex = 0
              if (JsonInfo.GPU && JsonInfo.GPU.SubNode) {
                let Gpu_Count = JsonInfo.GPU.SubNode.length
                if (Gpu_Count === 1) { return(0)}
                for (let i = 0; i < JsonInfo.GPU.SubNode.length; i++) {
                  const currentNode = JsonInfo.GPU.SubNode[i];
                  const currentVideoMemoryNumber = currentNode.VideoMemoryNumber;
                  if (currentVideoMemoryNumber > (JsonInfo.GPU.SubNode[srcIndex].VideoMemoryNumber || 0)) {
                    srcIndex = i;
                  }
                }
              }else{
                return (0)
              }
              return(srcIndex)
            }
            // let GPUIndex = 0
            let GPUData = HWInfoV['SubNode'][GPUIndex]
            console.warn('显卡索引',GPUIndex);
            console.warn('显卡数据',GPUData);
            if (!GPUData) GPUData = {VideoCard:''}

            // hardwaerinfo.value['GPU'] = GPUData['VideoChipset']
            hardwaerinfo.value['GPU'] = String(GPUData['VideoCard'])
            hardwaerinfo.value['GPU'] = hardwaerinfo.value['GPU'].replace(/\(.*?\)/g, '').replace(/\[.*?\]/g, '').trim();
            hardwaerinfo.value['GPU_DriverVersion'] = GPUData['DriverVersion']
            hardwaerinfo.value['GPU_DriverDate'] = FormartMonthToNumber(GPUData['DriverDate'])
            hardwaerinfo.value['GPU_Brand'] = gpu_brand(GPUData)
            hardwaerinfo.value['GPU_NumberOfUnifiedShaders'] = GPUData['NumberOfUnifiedShaders']
            hardwaerinfo.value['GPU_GraphicsMemoryBusWidth'] = GPUData['GraphicsMemoryBusWidth']
            hardwaerinfo.value['GPU_ASICManufacturer'] = GPUData['ASICManufacturer']
            hardwaerinfo.value['VideoMemoryNumber'] = GPUData['VideoMemoryNumber']

            if (GPUData['VideoMemory']) {
              const VideoMemory = String(GPUData['VideoMemory'])
              if (VideoMemory.includes('[') && VideoMemory.includes(']')) {
                try {
                  const MidStr = VideoMemory.match(/\[(.+?)\]/)[1];
                  hardwaerinfo.value['VideoMemoryBrand'] = MidStr
                }catch (e) {

                }
              }
            }

            if (!hardwaerinfo.value['VideoMemoryBrand']) hardwaerinfo.value['VideoMemoryBrand'] = ''

            let videoCardName = GPUData.VideoCard;
            let VideoBrand = videoCardName.match(/\[(.+?)\]$/) ? RegExp.$1 : videoCardName.split(' ')[0] || '';
            hardwaerinfo.value['VideoBrand'] = VideoBrand
            const gpu_memory_size_type = (data: any) => {
              const regex = /\[(.+?)\]/g
              const VideoMemory = (data.VideoMemory).split(' ')
              const VideoMemoryBrandArr = (data.VideoMemory.match(regex))
              let VideoMemoryBrand = ''
              if (VideoMemoryBrandArr) {
                VideoMemoryBrand = (VideoMemoryBrandArr[VideoMemoryBrandArr.length - 1]).replace(/\[|]/g, '')
              }
              let VideoType = ''
              if (VideoMemory[3]) {
                VideoType = VideoMemory[3]
              }
              let typeBrand = ''
              if (VideoType || VideoMemoryBrand) {
                typeBrand = ' (' + VideoType + ' ' + VideoMemoryBrand + ')'
              }
              let n = 1024
              if (data.VideoMemory.includes('MBytes')) {
                n = 1024
              } else if (data.VideoMemory.includes('KBytes')) {
                n = 1024*1024
              } else if (data.VideoMemory.includes('GBytes')) {
                n = 1
              }
              return Math.ceil(((VideoMemory[0] / n))) + 'GB'
            }
            if (GPUData.VideoMemory && GPUData.VideoMemory !== 'Unknown') {
                hardwaerinfo.value['GPU_VideoMemor'] = gpu_memory_size_type(GPUData)
            }
                let gpu_name = GPUData['VideoChipset'];
                let GPUType = ''
            if (['Radeon', 'AMD', 'Vega','amd'].find(item => gpu_name.includes(item))) {GPUType = 'amd';} else if (['GeForce', 'NVIDIA'].find(item => gpu_name.includes(item))) {GPUType = 'nvidia'}
                 hardwaerinfo.value['GPUType'] = GPUType

       break;
        case 'MEMORY':
            let MemoryHtml = '';
            hardwaerinfo.value['memory_list_all'] = []
            if (HWInfoV['SubNode'] != null) {
              let arr:Array<string> = []
              let arr2:Array<number> = []
              let arr3 = []
              for (let i = 0; i < HWInfoV['SubNode'].length; i++) {
                const item = HWInfoV['SubNode'][i];
                let str = ''
                str += item.ModuleManufacturer;
                str += ' '
                str += ((item.MemorySpeed)?.match(/\((.+?)\)/g)[0])?.split('/')[0].replace('(', '') || ''
                // str += Math.floor(item.MemorySpeed?.split(' ')[0]) + ' MHz '
                str += item.ModuleSize?.split(' ')[0] + 'GB'
                hardwaerinfo.value['memory_list_all'].push(str);
                if (arr.length === 0) {
                  arr.push(str)
                  arr2.push(1)
                  arr3.push({
                    'MemoryType': item['MemoryType']
                  })
                }else{
                  if (arr.includes(str)){
                    arr2[arr.indexOf(str)] = arr2[arr.indexOf(str)]+1
                  }else{
                    arr.push(str)
                    arr2.push(1)
                    arr3.push({
                      'MemoryType': item['MemoryType']
                    })
                  }
                }
              }
              hardwaerinfo.value['memory_list1'] = arr; // 内存条名字
              hardwaerinfo.value['memory_list2'] = arr2; // 内存条数量
              hardwaerinfo.value['memory_list3'] = arr3; // 内存条类型
              hardwaerinfo.value['memory'] = HWInfoV['SubNode'][0]['ModuleManufacturer'] + "  " + +(HWInfoV.Property['TotalMemorySize[MB]']) / 1024 + 'GB' + "  " + (Math.ceil(HWInfoV.Property['CurrentMemoryClock'].split('MHz')[0])) * 2 + 'MHz' + "  " + HWInfoV.Property['CurrentTiming(tCAS-tRCD-tRP-tRAS)']

                // MemoryHtml = '<div class="memory">' + HWInfoV['SubNode'][0]['ModuleManufacturer'] + "  " + +(HWInfoV.Property['TotalMemorySize[MB]']) / 1024 + 'GB' + "  " + (Math.ceil(HWInfoV.Property['CurrentMemoryClock'].split('MHz')[0])) * 2 + 'MHz' + "  " + HWInfoV.Property['CurrentTiming(tCAS-tRCD-tRP-tRAS)'] + '</div></li>';
            }
            let obj = HWInfoV['Property']
            if (!obj) obj = {}
            if (obj['TotalMemorySize[MB]']) {
              hardwaerinfo.value['TotalMemorySize[MB]'] = obj['TotalMemorySize[MB]'];
              hardwaerinfo.value['Memory_size']  = (obj['TotalMemorySize[MB]'] / 1024) + 'GB'
            } else {
              hardwaerinfo.value['TotalMemorySize[MB]'] = 0
              hardwaerinfo.value['Memory_size']  = ''
            }
            if (obj['MemoryChannelsActive']) {
              hardwaerinfo.value['Memory_channels_active'] = obj['MemoryChannelsActive']
            } else {
              hardwaerinfo.value['Memory_channels_active']  = ''
            }
            if (HWInfoV['SubNode'] && Array.isArray(HWInfoV['SubNode'])) {
              hardwaerinfo.value['Memory_count'] = HWInfoV['SubNode'].length
            } else {
              hardwaerinfo.value['Memory_count']  = 0
            }

            if (obj['CurrentMemoryClock']) {
              hardwaerinfo.value['Memory_Clock'] = (Math.ceil(obj['CurrentMemoryClock'].split('MHz')[0])) * 2
            } else {
              hardwaerinfo.value['Memory_Clock']  = ''
            }

            if (obj['CurrentTiming(tCAS-tRCD-tRP-tRAS)']) {
              hardwaerinfo.value['Memory_CurrentTiming'] = obj['CurrentTiming(tCAS-tRCD-tRP-tRAS)']
            }else{
              hardwaerinfo.value['Memory_CurrentTiming'] = ''
            }

        break;
        case 'MONITOR':
            let MONITORDataV = HWInfoV['SubNode'][0]
            let MonitorNameStr
            if (MONITORDataV.MonitorName !== 'Unknown') {
              hardwaerinfo.value['MonitorName'] = MONITORDataV.MonitorName.replace(/\[.*?\]/g, '');
            } else {
              hardwaerinfo.value['MonitorName'] = ''
            }
            if (MONITORDataV['MonitorName(Manuf)']) {
                let brand = RemoveAllSpace( hardwaerinfo.value['MonitorName']);
                let model = RemoveAllSpace(MONITORDataV['MonitorName(Manuf)']);
                if (brand.toLowerCase() === model.toLowerCase()) {
                    MonitorNameStr = MONITORDataV['MonitorName'];
                } else {
                    MonitorNameStr =hardwaerinfo.value['MonitorName']+ MONITORDataV['MonitorName(Manuf)'];
                }
            } else {
                MonitorNameStr = MONITORDataV['MonitorName'];
            }
            if (MonitorNameStr !== '') {
              hardwaerinfo.value['MonitorNameStr'] = MonitorNameStr
              hardwaerinfo.value['refresh_rate'] = MONITORDataV['RefreshFrequency'] + 'Hz'
              let WH = MONITORDataV.Resolutions.split('*')
              hardwaerinfo.value['resolutiop'] = WH[0] + '*' + WH[1]

                if (MONITORDataV['Max.HorizontalSize'] && MONITORDataV['Max.VerticalSize']) {
                    let HorizontalSize = RegExGetNum(MONITORDataV['Max.HorizontalSize']);
                    let VerticalSize = RegExGetNum(MONITORDataV['Max.VerticalSize']);
                    let MonitorSize = parseFloat((Math.sqrt(Math.pow(HorizontalSize, 2) + Math.pow(VerticalSize, 2)) / 2.54).toFixed(1));
                    hardwaerinfo.value['display_screen_size'] = MonitorSize + '英寸'
                }
            }
            break;
          case 'cpuComplexInfo':
              $store.dev.cpuComplexInfo = HWInfoV
              break;
      }
   }
   console.warn('GetHardwareBase End');

}

function PerformanceProcessing (PerformanceInfoObj:any) {
    // let objStr = JSON.stringify(PerformanceInfoObj);
    // let tempObj = JSON.parse(objStr);
    // console.log(tempObj);
    return DetailedDataProcessing(PerformanceInfoObj, 0.45, 1.10);
}
function getPercentileValues(arr:Array<number>) {
    // 首先检查输入是否为非空数组
    if (!Array.isArray(arr) || arr.length === 0) {
        return [0,0]
    }

    // 对数组进行升序排序
    const sortedArr = [...arr].sort((a, b) => a - b);

    // 计算数组中元素的数量
    const length = sortedArr.length;

    // 计算前5%和后5%位置的索引
    // 注意：对于小数部分，我们应该向上取整（对于前5%）和向下取整（对于后5%）
    const fivePercent = length * 0.05;
    const startIdx = Math.ceil(fivePercent) - 1; // 因为索引从0开始，所以减1
    const endIdx = length - Math.floor(fivePercent);

    // 获取前5%处和后5%处的数字
    // 如果数组长度不足以提供明确的5%，则返回边界值
    const startValue = startIdx >= 0 ? sortedArr[startIdx] : 0;
    const endValue = endIdx < length ? sortedArr[endIdx] : 0;

    return [startValue, endValue];
}
// controlRateMin 越小，最小FPS限制越小
// controlRateMax 越大，最大FPS限制越大
function DetailedDataProcessing(DetailedDataObj:any, controlRateMin:any, controlRateMax:any) {
    function MathArraySum(arr:any) {
        return eval(arr.join("+"));
    }

    function MathArrayAvgInt(arr:any) {
        let sum = eval(arr.join("+"));
        let avgValue = ~~(sum / arr.length * 100) / 100;
        return parseInt(parseFloat(String(avgValue)).toFixed(0));
    }
    function MathArrayAvgInt2(arr:any) {
        let sum = 0;
        let count = 0
        const errs = powerData.value.errs
        if (errs) {
          const errsSet = new Set(errs);
          for (let i = 0; i < arr.length; i++) {
            if (!errsSet.has(i)) {
              sum += arr[i]
              count++
            }
          }
        }

        if (count === 0) return 0;
        let avgValue = Math.floor(sum / count * 100) / 100;
        return parseInt(parseFloat(String(avgValue)).toFixed(0));
    }

    function MathArrayMax2(arr:any) {
        if (arr.length === 0) return 0;
        let maxNum = 0
        const errs = powerData.value.errs
        if (errs) {
          const errsSet = new Set(errs);
          for (let i = 0; i < arr.length; i++) {
            if (!errsSet.has(i)) {
              maxNum = Math.max(maxNum, arr[i])
            }
          }
        }
        return maxNum
    }
    function MathArrayMax(arr:any) {
      if (arr.length === 0) return 0;
      return Math.max.apply(null, arr);
    }

    function MathArrayMin2(arr:any) {
      if (arr.length === 0) return 0;
      let minNum = arr[0]
      const errs = powerData.value.errs
      if (errs) {
        const errsSet = new Set(errs);
        for (let i = 0; i < arr.length; i++) {
          if (!errsSet.has(i)) {
            minNum = Math.min(minNum, arr[i])
          }
        }
      }
      return minNum
    }
    function MathArrayMin(arr:any) {
        if (arr.length === 0) return 0;
        return Math.min.apply(null, arr);
    }

    function PreProcessing(arr:any) {
        function sortFPS(a:any, b:any) {
            // 升序
            return a - b;
        }
        let arrStr = JSON.stringify(arr);
        let arrObj = JSON.parse(arrStr);

        arrObj.sort(sortFPS);

        // 中FPS区域平均
        let midSta = parseInt(parseFloat(String(arrObj.length * 0.325)).toFixed(0));
        let midEnd = parseInt(parseFloat(String(arrObj.length * 0.775)).toFixed(0));
        let midArr = arrObj.slice(midSta, midEnd);
        let midAvg = MathArrayAvgInt(midArr);

        // 低FPS区域平均
        let minLeftIndexRate = 0.005;
        let minRightIndexRate = 0.105;
        let minAvg = midAvg * 0.35;
        while(1) {
            let minSta = parseInt(parseFloat(String(arrObj.length * minLeftIndexRate)).toFixed(0));
            let minEnd = parseInt(parseFloat(String(arrObj.length * minRightIndexRate)).toFixed(0));
            let minArr = arrObj.slice(minSta, minEnd);
            minAvg = MathArrayAvgInt(minArr);

            // 如果 (低FPS区域平均/中FPS区域平均) < 0.35
            if ((minAvg / midAvg) < 0.35) {
                minLeftIndexRate += 0.005;
                minRightIndexRate += 0.005;
            } else {
                break;
            }
        }

        // 高FPS区域平均
        let maxLeftIndexRate = 0.895;
        let maxRightIndexRate = 0.995;
        let maxAvg = midAvg * 1.35;
        while(1) {
            let maxSta = parseInt(parseFloat(String(arrObj.length * maxLeftIndexRate)).toFixed(0));
            let maxEnd = parseInt(parseFloat(String(arrObj.length * maxRightIndexRate)).toFixed(0));
            let maxArr = arrObj.slice(maxSta, maxEnd);
            maxAvg = MathArrayAvgInt(maxArr);

            // 如果 (高FPS区域平均/中FPS区域平均) > 1.35
            if ((maxAvg / midAvg) > 1.35) {
                maxLeftIndexRate -= 0.005;
                maxRightIndexRate -= 0.005;
            } else {
                break;
            }
        }

        let infoObj = Object();
        infoObj.minAvg = minAvg;
        infoObj.midAvg = midAvg;
        infoObj.maxAvg = maxAvg;
        return infoObj;
    }

    // FPS Obj
    let fpsInfoObj = DetailedDataObj["fps"];
    let fpsInfoDetailArr = JSON.parse(JSON.stringify(fpsInfoObj["detail"]));

    // let PreInfo = getPercentileValues(fpsInfoDetailArr);
    // console.log('前后5%处的值',PreInfo);

    // Cal Limit
    let FPS_LIMIT_MIN = 0;
    let FPS_LIMIT_MAX = 0;

    // $store.setMinLimit(FPS_LIMIT_MIN)
    // $store.setMaxLimit(FPS_LIMIT_MAX)

    // Process Obj by FPS_LIMIT_MIN
    let cpuclock = DetailedDataObj["cpuclock"];
    let cpuload = DetailedDataObj["cpuload"];
    let cpupower = DetailedDataObj["cpupower"];
    let cputemperature = DetailedDataObj["cputemperature"];
    let cpuvoltage = DetailedDataObj["cpuvoltage"];
    let fps = DetailedDataObj["fps"];
    let fps1 = DetailedDataObj["fps1"];
    let fps01 = DetailedDataObj["fps01"];
    let frametime = DetailedDataObj["frametime"];

    let gpuclock = DetailedDataObj["gpuclock"];
    let gpuload = DetailedDataObj["gpuload"];
    let gpumemoryload = DetailedDataObj["gpumemoryload"];
    let gpupower = DetailedDataObj["gpupower"];
    let gputemperature = DetailedDataObj["gputemperature"];
    let gpuhotspottemp = DetailedDataObj["gpuhotspottemp"];
    let gpumemoryclock = DetailedDataObj["gpumemoryclock"];

    let memory = DetailedDataObj["memory"];
    let memorytemperature = DetailedDataObj["memorytemperature"];

    let disk_temp = DetailedDataObj["disk_temp"];
    let disk_load = DetailedDataObj["disk_load"];


    var isClip = false;
    let fps_process = JSON.parse(JSON.stringify(fpsInfoObj["detail"]));
    for (let idx = fps_process.length - 1; idx >= 0; idx--) {
        let fps_current = fps_process[idx];
        let delete_flag = false;
        if (fps_current <= FPS_LIMIT_MIN && FPS_LIMIT_MIN !== 0) {
            delete_flag = true;
        } else if (fps_current >= FPS_LIMIT_MAX && FPS_LIMIT_MAX !== 0) {
            delete_flag = true;
        }

        if (delete_flag) {
            isClip = true;
            powerData.value.errs ? powerData.value.errs.push(idx): powerData.value.errs = [idx];
            if (fps_current < FPS_LIMIT_MIN) {
              powerData.value.errs_reason ? powerData.value.errs_reason.push('min'): powerData.value.errs_reason = ['min'];
            } else {
              powerData.value.errs_reason ? powerData.value.errs_reason.push('max'): powerData.value.errs_reason = ['max'];
            }

            // // cpuclock
            // let cpuclockDetail = cpuclock["detail"];
            // cpuclockDetail.splice(idx, 1);
            //
            // //cpuclock_performance
            // let cpuclockPLR = cpuclock["performance"]
            // cpuclockPLR.splice(idx, 1);
            //
            // // cpuload
            // let cpuloadDetail = cpuload["detail"];
            // cpuloadDetail.splice(idx, 1);
            //
            // // cpupower
            // let cpupowerDetail = cpupower["detail"];
            // cpupowerDetail.splice(idx, 1);
            //
            // // cputemperature
            // let cputemperatureDetail = cputemperature["detail"];
            // cputemperatureDetail.splice(idx, 1);
            //
            // // cpuvoltage
            // let cpuvoltageDetail = cpuvoltage["detail"];
            // cpuvoltageDetail.splice(idx, 1);
            //
            // // fps
            // let fpsDetail = fps["detail"];
            // fpsDetail.splice(idx, 1);
            //
            // // fps1
            // let fps1Detail = fps1["detail"];
            // fps1Detail.splice(idx, 1);
            //
            // // fps01
            // let fps01Detail = fps01["detail"];
            // fps01Detail.splice(idx, 1);
            //
            // // frametime
            // let frametimeDetail = frametime["detail"];
            // frametimeDetail.splice(idx, 1);
            //
            // // gpuclock
            // gpuclock.forEach(function (info:any) {
            //     let infoDetail = info["detail"];
            //     infoDetail.splice(idx, 1);
            // });
            //
            // // gpuload
            // gpuload.forEach(function (info:any) {
            //     let infoDetail = info["detail"];
            //     infoDetail.splice(idx, 1);
            // });
            //
            // // gpumemoryload
            // gpumemoryload.forEach(function (info:any) {
            //     let infoDetail = info["detail"];
            //     infoDetail.splice(idx, 1);
            // });
            //
            // // gpupower
            // gpupower.forEach(function (info:any) {
            //     let infoDetail = info["detail"];
            //     infoDetail.splice(idx, 1);
            // });
            //
            // // gputemperature
            // gputemperature.forEach(function (info:any) {
            //     let infoDetail = info["detail"];
            //     infoDetail.splice(idx, 1);
            // });
            //
            // // memory
            // let memoryDetail = memory["detail"];
            // memoryDetail.splice(idx, 1);
            //
            // let memorytemperatureDetail = memorytemperature["detail"];
            // memorytemperatureDetail.splice(idx, 1)
            //
            // // disk
            // if (disk_temp) {
            //     let disk_tempDetail = disk_temp["detail"];
            //     disk_tempDetail.splice(idx, 1)
            // }
            //
            // if (disk_load) {
            //     let disk_loadDetail = disk_load["detail"];
            //     disk_loadDetail.splice(idx, 1)
            // }
        }
    }

    // Cal new avg | min | max
    cpuclock["avg"] = MathArrayAvgInt2(cpuclock["detail"]);
    cpuclock["min"] = MathArrayMin2(cpuclock["detail"]);
    cpuclock["max"] = MathArrayMax2(cpuclock["detail"]);

    cpuload["avg"] = MathArrayAvgInt2(cpuload["detail"]);
    cpuload["min"] = MathArrayMin2(cpuload["detail"]);
    cpuload["max"] = MathArrayMax2(cpuload["detail"]);

    cpupower["avg"] = MathArrayAvgInt2(cpupower["detail"]);
    cpupower["min"] = MathArrayMin2(cpupower["detail"]);
    cpupower["max"] = MathArrayMax2(cpupower["detail"]);

    cputemperature["avg"] = MathArrayAvgInt2(cputemperature["detail"]);
    cputemperature["min"] = MathArrayMin2(cputemperature["detail"]);
    cputemperature["max"] = MathArrayMax2(cputemperature["detail"]);
    recentGameInfo.value.cputemp = cputemperature["avg"]

    fps["avg"] = MathArrayAvgInt(fps["detail"]);
    fps["min"] = MathArrayMin(fps["detail"]);
    fps["max"] = MathArrayMax(fps["detail"]);


    fps1["avg"] = MathArrayAvgInt2(fps1["detail"]);
    fps1["min"] = MathArrayMin2(fps1["detail"]);
    fps1["max"] = MathArrayMax2(fps1["detail"]);

    fps01["avg"] = MathArrayAvgInt2(fps01["detail"]);
    fps01["min"] = MathArrayMin2(fps01["detail"]);
    fps01["max"] = MathArrayMax2(fps01["detail"]);



    frametime["avg"] = MathArrayAvgInt2(frametime["detail"]);
    frametime["min"] = MathArrayMin2(frametime["detail"]);
    frametime["max"] = MathArrayMax2(frametime["detail"]);

    gpuclock.forEach(function (info:any) {
        info["avg"] = MathArrayAvgInt2(info["detail"]);
        info["min"] = MathArrayMin2(info["detail"]);
        info["max"] = MathArrayMax2(info["detail"]);
    });

    gpuload.forEach(function (info:any) {
        info["avg"] = MathArrayAvgInt2(info["detail"]);
        info["min"] = MathArrayMin2(info["detail"]);
        info["max"] = MathArrayMax2(info["detail"]);
    });

    gpumemoryload.forEach(function (info:any) {
        info["avg"] = MathArrayAvgInt2(info["detail"]);
        info["min"] = MathArrayMin2(info["detail"]);
        info["max"] = MathArrayMax2(info["detail"]);
    });

    gpupower.forEach(function (info:any) {
        info["avg"] = MathArrayAvgInt2(info["detail"]);
        info["min"] = MathArrayMin2(info["detail"]);
        info["max"] = MathArrayMax2(info["detail"]);
    });

    gputemperature.forEach(function (info:any) {
        info["avg"] = MathArrayAvgInt2(info["detail"]);
        recentGameInfo.value.gputemp = info["avg"]
        info["min"] = MathArrayMin2(info["detail"]);
        info["max"] = MathArrayMax2(info["detail"]);
    });


    memory["avg"] = MathArrayAvgInt2(memory["detail"]);
    memory["min"] = MathArrayMin2(memory["detail"]);
    memory["max"] = MathArrayMax2(memory["detail"]);

    DetailedDataObj["isClip"] = isClip;
    return DetailedDataObj;
}


function getDataOption(name:any, k:any)
{ //获取列表详情信息
    let jsonData:any = {};
    jsonData['id'] = k.id;
    jsonData['date'] = name;
    jsonData['timestamp'] = Number(k.starttime);
    jsonData['timeend'] = Number(k.endtime);
    jsonData['gametime'] = Number(k.gametime);
    jsonData['icon'] = k.icon;
    jsonData['processname'] = (k['processpath']);
    jsonData['province'] = k.province;
    jsonData['city'] = k.city;
    jsonData['wea'] = k.wea;
    jsonData['wea_img'] = k.wea_img;
    jsonData['tem'] = k.tem;
    jsonData['recordstart'] = k.recordstart;
    jsonData['hd_info'] = k.hd_info;
    jsonData['refreshtime'] = k.refresh_time;
    jsonData['hd_list_data'] = k.hd_list_data;
    return jsonData;
}

async function ListHtmlProcess (b:any,DatabaseId:any)

{
    console.log('b',b);
    let AllDate = '', DateArr = [], DateArr1 = [], RecordTimeAll = 0, RecordPowerAll = 0, RecordcarbonAll = 0, getAppDataDir = null
    let DetailedDataHtml = '';
    const DetailedData_id = await gamepp.database.query.promise(DatabaseId, "'" + b['starttime'] + "'", "COUNT(id)");
    if (DetailedData_id.length === 0) {
        await gamepp.database.delete.promise(DatabaseId, "GamePP_BaseInfo", "starttime = " + b['starttime'] + "");//列表数据
        return false
    }
    console.warn('DetailedData_id',DetailedData_id);

    let list_length = DetailedData_id[0]['COUNT(id)'];
    let list_length_spacing = 1;
    // if (list_length >= 5000) {list_length_spacing = Math.ceil(list_length / 5000);}
    let DetailedData =  await gamepp.database.query.promise(DatabaseId, "'" + b['starttime'] + "'", "*", "id-(id/" + list_length_spacing + ")*" + list_length_spacing + "=0");
    if (DetailedData.length === 0) return false;
    if(DetailedData[0].hasOwnProperty('fps'))
    {
        handleDetailedData(DetailedData,DatabaseId,b,false)
        console.warn('性能统计详情数据：',DetailedData);

    }
    else
    {
        console.warn('实验室详情数据：',DetailedData);
        $store.isManual = true;
        handleDetailedDataLab(DetailedData,DatabaseId,b,false)
    }
    // 使用filter方法找到所有值为1的元素，然后使用map方法获取它们的索引
    let indicesOfOne:any = []
    let points:any = []
    let starttime = b['starttime'] //开始时间

    DetailedData.filter((item:any,index:number) => indicesOfOne)
    DetailedData.forEach((item:any,index:number)=>
    {
        if(item.hasOwnProperty('pointmark') && item.pointmark == 1)
        {
            points.push(item.clip_path)
            indicesOfOne.push(index)
        }else{
          points.push(null)
        }
    })
    powerData.value['points'] = points
    // indicesOfOne.push(DetailedData.length-1)//标记终点
    //发送消息更新性能统计页面
    try
    {
      let bool = true
      bool = JSON.parse(localStorage.getItem('isMaualOpendetail')!)
      console.warn('bool',bool);
      if(!bool){return}else{
        let Obj:any = {};
        Obj['action'] = 'updateGameRebound';
        if (gamepp.webapp.windows.isValid.sync('game_rebound'))
        {
          await gamepp.webapp.sendInternalAppEvent.promise('game_rebound', Obj)
        }
      }

    }
    catch
    {
      console.warn('message send Error:game_rebound');
    }

    try
    {
      let Obj2:any = {};
      Obj2['action'] = 'changeRebound';
      if (gamepp.webapp.windows.isValid.sync('desktop'))
      {
        await gamepp.webapp.sendInternalAppEvent.promise('desktop', Obj2)
      }

    }
    catch
    {
      console.warn('message send Error:desktop');
    }

    if(indicesOfOne.length == 0){console.warn('未进行打点标记：');return}
    console.log('标记点的秒数',indicesOfOne); // 输出所有值为1的索引
    console.warn('开始时间');

    indicesOfOne.forEach((item:number,index:number)=>
    {
      console.log(item)
      console.warn(`第${index+1}个标记点：`,FormatTimePlus(starttime+item));
    })

}



function closeWindow() {
  gamepp.webapp.windows.close.promise('rebound_details_v2')
}

function minimizeWindow() {
  gamepp.webapp.windows.minimize.promise('rebound_details_v2')
}

function formatTimestampToTime(timestamp:number) {
  // 创建Date对象
  let date = new Date(timestamp*1000);

  // 获取年、月、日
  let years:number|string = date.getFullYear();
  let months:number|string = date.getMonth() + 1;
  let days:number|string = date.getDate();

  // 补零操作
  years = ('0' + years).slice(-4);
  months = ('0' + months).slice(-2);
  days = ('0' + days).slice(-2);

  // 获取小时、分钟和秒
  let hours:number|string = date.getHours();
  let minutes:number|string = date.getMinutes();
  let seconds:number|string = date.getSeconds();

  // 补零操作
  hours = ('0' + hours).slice(-2);
  minutes = ('0' + minutes).slice(-2);
  seconds = ('0' + seconds).slice(-2);

  // 返回格式化的时间字符串
  return `${years}-${months}-${days} ${hours}:${minutes}:${seconds}`;
}

function handleChangeTab(tabIndex:number) {
  activeTab.value = tabIndex;
  if (tabIndex === 3) {
      tab3Tooltip.value = false
  }
}
</script>
<template>
  <div class="GameReboundDetail">
    <ReboundDetailHeader :process-name="recentGameInfo.gameName" :database-table="DatabaseTableRef" :activeTab="activeTab" :zoomValue="zoomValue"/>
    <div ref="watermarkContainer" class="watermarkContainer">
      <div v-for="i in 100" class="water-content">
        <em><span class="G">G</span><span class="ame">ame</span><span class="pp">PP</span></em>
        <span class="version">V{{version}}</span>
      </div>
    </div>
    <div class="GameReboundDetail-content">
        <div class="tab-bar">
          <img :src="recentGameInfo.iconsrc" alt="" class="game-img">
          <div class="game-info">
            <p class="game-name">{{recentGameInfo.gameName}}</p>
            <div class="wea">
              <span>{{ $t('GameRebound.start') }}</span><span class="white-font">{{formatTimestampToTime(recentGameInfo.starttime)}}</span>
              <span style="margin-left: 17px;">{{ $t('GameRebound.end') }}</span><span class="white-font">{{formatTimestampToTime(recentGameInfo.endtime)}}</span>
              <span style="margin-left: 17px;">{{ $t('GameRebound.Gametime') }}</span><span class="white-font">{{recentGameInfo.gametime}}</span>
              <img v-if="hardwaerinfo.weatherIconSrc && currentLanguage === 'CN'" :src="hardwaerinfo.weatherIconSrc" style="cursor: pointer;" @click="isShowWea=!isShowWea" alt="" class="weather-icon">
              <span v-if="hardwaerinfo.tem && currentLanguage === 'CN'" class="white-font" style="cursor: pointer;">
                <span v-show="isShowWea" @click="isShowWea=false">{{hardwaerinfo.tem}}℃</span>
                <span v-show="!isShowWea" @click="isShowWea=true">***</span>
              </span>
              <span v-if="hardwaerinfo.weather && currentLanguage === 'CN'" class="white-font" style="cursor: pointer;">
                <span v-show="isShowWea" @click="isShowWea=false">{{hardwaerinfo.weather}}</span>
                <span v-show="!isShowWea" @click="isShowWea=true">**</span>
              </span>
              <span v-if="hardwaerinfo.province && currentLanguage === 'CN'" class="iconfont icon-locate" style="cursor: pointer;" @click="isShowWea=!isShowWea"></span>
              <span v-if="hardwaerinfo.province && currentLanguage === 'CN'" class="white-font" style="cursor: pointer;">
                <span v-show="isShowWea" @click="isShowWea=false">{{hardwaerinfo.province}}</span>
                <span v-show="!isShowWea" @click="isShowWea=true">***</span>
              </span>
              <span v-if="hardwaerinfo.city && currentLanguage === 'CN'" class="white-font" style="cursor: pointer;">
                <span v-show="isShowWea" @click="isShowWea=false">{{hardwaerinfo.city}}</span>
                <span v-show="!isShowWea" @click="isShowWea=true">***</span>
              </span>
            </div>
          </div>

          <div class="tabs" :class="{'tabs-cn': currentLanguage === 'CN', 'tabs-ru': currentLanguage === 'RU'}">
            <div class="tab-item" :title="$t('GameRebound.Compactdata')" :class="{'is-active': activeTab === 0}" @click="handleChangeTab(0)">{{ $t('GameRebound.Compactdata') }}</div>
            <div class="tab-item" :title="$t('GameRebound.FullData')" :class="{'is-active': activeTab === 1}" @click="handleChangeTab(1)">{{ $t('GameRebound.FullData') }}</div>
            <div class="tab-item" :title="$t('GameRebound.PerformanceAnalysis2')"  :class="{'is-active': activeTab === 2}" @click="handleChangeTab(2)">{{ $t('GameRebound.PerformanceAnalysis2') }}</div>
            <el-tooltip effect="light" :visible="tab3Tooltip" v-show="!$store.isManual">
              <template #content>
                  <span>{{$t('GameRebound.itHasBeenGenerated')}}</span>
                  <span style="color: #3579d5;cursor: pointer;" @click="handleChangeTab(3);">{{$t('GameRebound.clickToView')}}</span>
              </template>
              <div class="tab-item" :title="$t('GameRebound.AiAgent')" :class="{'is-active': activeTab === 3}" @click="handleChangeTab(3)">{{ $t('GameRebound.AiAgent') }}</div>
            </el-tooltip>
          </div>
        </div>
        <SimpleData v-if="activeTab === 0" :powerData="powerData" :recentGameInfo="recentGameInfo" :hardwaerinfo="hardwaerinfo" />
        <FullData v-if="activeTab === 1" :powerData="powerData" :recentGameInfo="recentGameInfo" :hardwaerinfo="hardwaerinfo" />
        <CpuPerformanceAnalysis v-if="activeTab === 2" :powerData="powerData" :recentGameInfo="recentGameInfo" :hardwaerinfo="hardwaerinfo"/>
        <PPAgent v-if="activeTab === 3"/>
    </div>

    <FPSLimitDialog v-if="$store.fpsLimitDialogShow"/>

    <PPAgentDownloadDialog v-show="$store.ai_agent.downloadDialogShow" />

      <div class="GameMessageTipDialog" v-show="showMessageDialog">
          <div class="GameMessageTipDialog-content">
              <GameMessageTip @close="showMessageDialog=false" @show="showMessageDialog=true"/>
          </div>
      </div>
  </div>
</template>

<style lang="scss" scoped>
@font-face{
  font-family: 'SUPREME';
  src: url('./assets/SUPREME.TTF');
}
.GameReboundDetail{
  width: var(--rebound-ww);
  height: var(--rebound-hh);
  background-color: #22232E;
  position: relative;
  box-shadow: 0 1px var(--shadow-size) rgba(0, 0, 0, 0.6);
  margin: var(--shadow-size);
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  flex-flow: column nowrap;
  .watermarkContainer {
    width: var(--rebound-ww);
    height: var(--rebound-hh);
    position: absolute;
    top: 30px;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    flex-flow: row wrap;
    z-index: 0;
    align-content: space-between;
    justify-content: space-between;
    opacity: 0.1;
    pointer-events: none;

    .water-content {
      width: 300px;
      height: 300px;
      display: flex;
      flex-flow: column nowrap;
      justify-content: center;
      align-content: center;
      transform: rotate(330deg);
      text-align: center;
      em {
        font-family: 'SUPREME';
        font-size: 14px;
        color: #ffffff;
        letter-space: 1px;
        .ame {
          font-size: 10px;
        }
        .pp {
          color: #f9ab14;
        }
      }

      .version {
        font-size: 12px;
      }
    }
  }
  .GameReboundDetail-content {
    margin: 0 auto;
    width: 1280px;
    height: calc(100vh - 42px);
    position: relative;
    display: flex;
    flex-flow: column nowrap;
    //background-color: #22232E;
    .tab-bar {
      border-bottom: 1px solid #292A33;
      display: flex;
      align-items: center;
      flex-flow: row nowrap;
      height: 39px;
      padding: 7px 0 7px 20px;

      .game-info {
        margin-left: 8px;
        display: flex;
        flex-flow: column nowrap;

        .game-name {
          color: #BBBBBB;
          margin-bottom: 2px;
        }

        .wea {
          display: flex;
          flex-flow: row nowrap;
          color: #777777;
          gap: 5px;
          span{

          }
          .iconfont {
            color: #3579D5;
          }

          .white-font {
            color: #FFFFFF;
          }

          .weather-icon {
            width: 16px;
            height: 16px;
            margin-left: 10px;
          }
        }
      }

      .tabs {
        margin-left: auto;
        display: flex;
        flex-flow: row nowrap;
        color: #727281;
        height: 37px;

        .tab-item {
          padding: 0 27px;
          line-height: 37px;
          cursor: pointer;
          max-width: 160px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .is-active {
          border-bottom: 2px solid #1193F8;
          color: #1193F8;
        }
      }
    }
    .tabs-cn .tab-item {
      max-width: 130px!important;
    }
    .tabs-ru .tab-item {
      max-width: 120px !important;
    }
    :deep(.charts-dur) {
        height: 30px;
        padding-right: 20px;
        display: flex;
        flex-flow: row nowrap;
        align-items: center;
        justify-content: space-between;

        .start-time, .end-time {
          width: 190px;
          height: 30px;
          background: #22232E;
          border-radius: 4px;
          border: 1px solid #2D2E39;
          display: flex;
          flex-flow: row nowrap;
          align-items: center;
          justify-content: space-between;
          padding: 0 5px;
          gap: 3px;
          cursor: pointer;

          :hover {
            border-color: #3579d5;
          }
        }

        .dur-process {
          width: 800px;
          height: 10px;
          background: #2D2E39;
          border-radius: 5px;
          position: relative;
          overflow: hidden;

          .line {
            position: absolute;
            top: 0;
            left: 0;
            height: 10px;
            width: 800px;
            transition: all .3s;
            background-image: linear-gradient(
                            to right,
                            rgba(53, 121, 213, 0.6) 0%,
                            #2e3649 50%,
                            rgba(53, 121, 213, 0.6) 100%
            );
          }

          .line-item {
            width: 1px;
            height: 10px;
            position: absolute;
            background-color: #777777;
            transition: all .2s;

            &:hover, &.is-active {
              width: 3px;
              height: 10px;
              margin-left: -1.5px;
              background: #0089E9;
              box-shadow: 0 0 2px 2px #0089e961;
            }
          }
        }
      }

  }
  .GameMessageTipDialog {
    position: absolute;
    top: 30px;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.4);
    .GameMessageTipDialog-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}
</style>
<style>
:root {
  --zoomV--: 1;
  --rebound-w: calc(100vw - 12px);
  --rebound-ww: calc(var(--rebound-w) / var(--zoomV--));
  --rebound-h: calc(100vh - 12px);
  --rebound-hh: calc(var(--rebound-h) / var(--zoomV--));
  --shadow-size: 6px;
}
.GameReboundDetail *{
    box-sizing: border-box;
}
.data-screening {
    color: #3579d5;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    gap: 9px;
    .iconfont {
        font-size: 14px;
        transform: rotate(90deg);
    }
}
html.dark{color-scheme:dark;--el-color-primary:#409eff;--el-color-primary-light-3:#3375b9;--el-color-primary-light-5:#2a598a;--el-color-primary-light-7:#213d5b;--el-color-primary-light-8:#1d3043;--el-color-primary-light-9:#18222c;--el-color-primary-dark-2:#66b1ff;--el-color-success:#67c23a;--el-color-success-light-3:#4e8e2f;--el-color-success-light-5:#3e6b27;--el-color-success-light-7:#2d481f;--el-color-success-light-8:#25371c;--el-color-success-light-9:#1c2518;--el-color-success-dark-2:#85ce61;--el-color-warning:#e6a23c;--el-color-warning-light-3:#a77730;--el-color-warning-light-5:#7d5b28;--el-color-warning-light-7:#533f20;--el-color-warning-light-8:#3e301c;--el-color-warning-light-9:#292218;--el-color-warning-dark-2:#ebb563;--el-color-danger:#f56c6c;--el-color-danger-light-3:#b25252;--el-color-danger-light-5:#854040;--el-color-danger-light-7:#582e2e;--el-color-danger-light-8:#412626;--el-color-danger-light-9:#2b1d1d;--el-color-danger-dark-2:#f78989;--el-color-error:#f56c6c;--el-color-error-light-3:#b25252;--el-color-error-light-5:#854040;--el-color-error-light-7:#582e2e;--el-color-error-light-8:#412626;--el-color-error-light-9:#2b1d1d;--el-color-error-dark-2:#f78989;--el-color-info:#909399;--el-color-info-light-3:#6b6d71;--el-color-info-light-5:#525457;--el-color-info-light-7:#393a3c;--el-color-info-light-8:#2d2d2f;--el-color-info-light-9:#202121;--el-color-info-dark-2:#a6a9ad;--el-box-shadow:0px 12px 32px 4px rgba(0,0,0,0.36),0px 8px 20px rgba(0,0,0,0.72);--el-box-shadow-light:0px 0px 12px rgba(0,0,0,0.72);--el-box-shadow-lighter:0px 0px 6px rgba(0,0,0,0.72);--el-box-shadow-dark:0px 16px 48px 16px rgba(0,0,0,0.72),0px 12px 32px #000000,0px 8px 16px -8px #000000;--el-bg-color-page:#0a0a0a;--el-bg-color:#141414;--el-bg-color-overlay:#1d1e1f;--el-text-color-primary:#E5EAF3;--el-text-color-regular:#CFD3DC;--el-text-color-secondary:#A3A6AD;--el-text-color-placeholder:#8D9095;--el-text-color-disabled:#6C6E72;--el-border-color-darker:#636466;--el-border-color-dark:#58585B;--el-border-color:#4C4D4F;--el-border-color-light:#414243;--el-border-color-lighter:#363637;--el-border-color-extra-light:#2B2B2C;--el-fill-color-darker:#424243;--el-fill-color-dark:#39393A;--el-fill-color:#303030;--el-fill-color-light:#262727;--el-fill-color-lighter:#1D1D1D;--el-fill-color-extra-light:#191919;--el-fill-color-blank:transparent;--el-mask-color:rgba(0,0,0,0.8);--el-mask-color-extra-light:rgba(0,0,0,0.3)}html.dark .el-button{--el-button-disabled-text-color:rgba(255,255,255,0.5)}html.dark .el-card{--el-card-bg-color:var(--el-bg-color-overlay)}html.dark .el-empty{--el-empty-fill-color-0:var(--el-color-black);--el-empty-fill-color-1:#4b4b52;--el-empty-fill-color-2:#36383d;--el-empty-fill-color-3:#1e1e20;--el-empty-fill-color-4:#262629;--el-empty-fill-color-5:#202124;--el-empty-fill-color-6:#212224;--el-empty-fill-color-7:#1b1c1f;--el-empty-fill-color-8:#1c1d1f;--el-empty-fill-color-9:#18181a}
</style>
