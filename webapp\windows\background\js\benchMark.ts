import axios from 'axios'

const getBenchMarkInfo = async() =>
{
    try 
    {
        let obj:any = {}
        obj.gpp_version = await gamepp.getPlatformVersion.promise();
        obj.gpp_username = decodeURIComponent(await gamepp.user.getUserName.promise());
        obj.gpp_userid = await gamepp.user.getUserID.promise();
        obj.gpp_mid = await gamepp.getMID.promise();
        obj.gpp_benchmark_ver = await gamepp.package.getversion.promise('benchmark');
        return obj
    } 
    catch 
    {

    }
}

const handleHWINFO = async() =>
{
    let HardWareInfo = await gamepp.hardware.getBaseJsonInfo.promise();
    let HwInfoArr = JSON.parse(HardWareInfo);
    let gpu_main_index = JSON.parse(localStorage.getItem('gpu_index') as any)
    
}

async function submitData(cpu_name, gpu_name, resolutions, score, key) {
    try {
      const response = await axios.post('https://rank.gamepp.com/v1/api/getForecastFPSList2', {
        cpu_name,
        gpu_name,
        resolutions,
        score,
      });

      const result = response.data;

      if (result.code === 200 && result.data && result.data.length >= 1) 
      {
        const FpsList = result.data;
        console.log('FpsList', FpsList);

        this.$nextTick(() => {
          if (key.length > 0) 
          {
          }
        });
      } else {
        console.error('API returned an error:', result.msg);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  },
