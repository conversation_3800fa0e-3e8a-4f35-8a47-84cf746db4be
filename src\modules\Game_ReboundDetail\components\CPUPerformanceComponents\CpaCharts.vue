<script setup lang="ts">
// CPU-performance-analysis Echarts
import {useReboundDetailStore, dropdown_menu} from "@/modules/Game_ReboundDetail/stores/reboundDetail";
import {storeToRefs} from "pinia";
import {computed, inject, onMounted, reactive, Ref, ref, watch} from "vue";
import * as echarts from "echarts";
import {
  MathArrayAvg2,
  MathArrayMax2,
  MathArrayMin2,
  totalSeconds,
  FormatTime,
  getHoursMinutesSeconds,
  displayHMSTime
} from "@/modules/Game_ReboundDetail/components/someScripts";
import RightClickMenu from "@/modules/Game_ReboundDetail/components/RightClickMenu.vue";
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

const $store = useReboundDetailStore()
const zoomValue = inject('zoomV') as Ref<number>
const {CPA,startTime,endTime,endTimeOriginal,gameTime} = storeToRefs($store)
const hide_temp = ref(false); // 隐藏温度原因区域
const hide_other = ref(false); // 隐藏其他原因区域
const curDisplayDataCount = ref(0);
const rightClickOptions = reactive({ // 一些开了右键菜单后需要的数据
  ArrSub: -1,
  dataIndex: -1,
  duration: 0,
  show: false,
  screenX: 0,
  screenY: 0,
  clientX: 0,
  clientY: 0,
})
const rcm = ref()
const dropdown_menu_toshow = computed(() => {
  return JSON.parse(JSON.stringify(dropdown_menu)).filter((item: any) => {
    if ((item.name !== CPA.value.show_data.name) && $store.powerData.full) {
      const checkName = item.name
      const PM_GetDetailsARR = $store.powerData.full;
      let DetailArr;
      if (PM_GetDetailsARR[checkName]) {
        if (checkName === 'memory' || checkName === 'fps' || checkName === 'cpupower' || checkName === 'frametime') {
          DetailArr = PM_GetDetailsARR[checkName]['detail'];
        } else if (checkName === 'gpuload' || checkName === 'gpuload1' || checkName === 'gputemperature' || checkName === 'gpumemoryload' || checkName === 'gpuclock' || checkName === 'gpupower') {
          if (PM_GetDetailsARR[checkName].length === 2) {
            if (PM_GetDetailsARR[checkName][0]['avg'] <= 0) {
              DetailArr = PM_GetDetailsARR[checkName][1]['detail'];
            } else {
              if (PM_GetDetailsARR.gpushadres !== undefined) {
                if (PM_GetDetailsARR.gpushadres[0] < PM_GetDetailsARR.gpushadres[1]) {
                  DetailArr = PM_GetDetailsARR[checkName][1]['detail'];
                } else {
                  DetailArr = PM_GetDetailsARR[checkName][0]['detail'];
                }
              } else {
                DetailArr = PM_GetDetailsARR[checkName][1]['detail'];
              }
            }
          } else {
            DetailArr = PM_GetDetailsARR[checkName][0]['detail'];
          }
        } else {
          DetailArr = PM_GetDetailsARR[checkName]['detail'];
        }
      } else {
        DetailArr = []
      }
      return DetailArr.length !== 0;
    } else {
      return false
    }
  })
})
const line_item = computed(() => {
  if ($store.powerData && $store.powerData.points) {
    let map: any = {}
    const totalLen = $store.powerData.points.length
    for (let i = 0; i < totalLen; i++) {
      const item = $store.powerData.points[i]
      if (item !== null) {
        // const index = Math.ceil(i / totalLen * props.powerData.fps.detail.length)
        const dur =  Math.floor(Number((gameTime.value / totalLen) * i))
        const hms = getHoursMinutesSeconds(dur)
        map[i] = {
          imgurl: item,
          time: {
            ...hms
          },
          left: `${i / totalLen * 800}px`,
        }
      }
    }
    return map
  }
  return {}
})
const chart3_temp_limit_area = computed(()=>{
  if (hide_temp.value) return []
  const _arr:Array<any> = Array.from($store.CPA.limit_data_temp)
  _arr.sort((a,b)=>a - b) // 升序排序
  // 单个点的宽度
  const single_width = ($store.curDisplayDataCount!==0?(1200 / $store.curDisplayDataCount):0);

  const resultArr = []

  let startIndex = 0
  if (startTime.value.h != 0 || startTime.value.m != 0 || startTime.value.s != 0) {
    startIndex = Math.ceil(totalSeconds(startTime.value) / gameTime.value * $store.powerData.cpuclock.performance.length)
  }
  for (let i = 0; i < _arr.length; i++) {
    let item = _arr[i]
    if (item < startIndex) {
      continue;
    }
    resultArr.push({
      width: single_width,
      left: single_width * (item - startIndex)
    })
    // 如果下一个数字紧挨着上一个数字，则递归合并
    if (i < _arr.length - 1) {
      let j = i + 1
      while (j < _arr.length) {
        const nextItem = _arr[j]
        if (nextItem === item + 1) {
          resultArr[resultArr.length - 1].width += single_width
          i++
        } else {
          break
        }
        item++
        j++
      }
    }
  }
  return resultArr
})
const chart3_other_limit_area = computed(()=>{
  if (hide_other.value) return []
  const _arr:Array<any> = Array.from($store.CPA.limit_data_other)
  _arr.sort((a,b)=>a - b) // 升序排序
  // 单个点的宽度
  const single_width = ($store.curDisplayDataCount!==0?(1200 / $store.curDisplayDataCount):0);

  const resultArr = []

  let startIndex = 0
  if (startTime.value.h != 0 || startTime.value.m != 0 || startTime.value.s != 0) {
    startIndex = Math.ceil(totalSeconds(startTime.value) / gameTime.value * $store.powerData.cpuclock.performance.length)
  }
  for (let i = 0; i < _arr.length; i++) {
    let item = _arr[i]
    if (item < startIndex) {
      continue;
    }
    resultArr.push({
      width: single_width,
      left: single_width * (item - startIndex)
    })
    // 如果下一个数字紧挨着上一个数字，则递归合并
    if (i < _arr.length - 1) {
      let j = i + 1
      while (j < _arr.length) {
        const nextItem = _arr[j]
        if (nextItem === item + 1) {
          resultArr[resultArr.length - 1].width += single_width
          i++
        } else {
          break
        }
        item++
        j++
      }
    }
  }
    console.log(resultArr)
  return resultArr
})
function FormatSeconds(value: any, second: any) {
    let theTime: number = parseInt(value);// 秒
    let theTime1: number = 0;// 分
    let theTime2: number = 0;// 小时
    if (theTime > 60) {
        theTime1 = parseInt(String(theTime / 60));
        theTime = parseInt(String(theTime % 60));
        if (theTime1 > 60) {
            theTime2 = parseInt(String(theTime1 / 60));
            theTime1 = parseInt(String(theTime1 % 60));
        }
    }
    let result = "";
    if (second) {
        result = "" + parseInt(String(theTime)) + t('shutdownTimer.sec');
    }
    if (theTime1 > 0) {
        result = "" + parseInt(String(theTime1)) + t('screenshotpage.minutes') + result;
    }
    if (theTime2 > 0) {
        result = "" + parseInt(String(theTime2)) + t('shutdownTimer.hours') + result;
    }
    return result;
}
let echartsInstance: any = null
let echartsShowIndexStart = 0;
let echartsShowIndexEnd = -1;
const handleCommand = (command: any) => {
  $store.CPA.show_data.name = command.name
  $store.CPA.show_data.value = command.value
    if (command.value2) {
        $store.CPA.show_data.value2 = command.value2
    }
  echartsSetOption()
}
function handleEchartsContextMenu(e:PointerEvent) {
  const {clientX, clientY, screenX, screenY} = e;
  const zoomLevel = zoomValue.value
  if (rcm.value.openMenu) {
    rightClickOptions.clientX = clientX/zoomLevel
    rightClickOptions.clientY = clientY/zoomLevel
    rightClickOptions.screenX = screenX/zoomLevel
    rightClickOptions.screenY = screenY/zoomLevel
    rightClickOptions.show = true
    rcm.value.openMenu(clientX, clientY, screenX, screenY)
  }
}
function handleRightClickMenuCommand(n:number) {
  const duration = Number(rightClickOptions.duration.toFixed(0))
  const hms = getHoursMinutesSeconds(duration)
  if (n === 1) { // 设置成开始时间
    startTime.value.h = hms.h
    startTime.value.m = hms.m
    startTime.value.s = hms.s
  } else { // 设置成结束时间
    endTime.value.h = hms.h
    endTime.value.m = hms.m
    endTime.value.s = hms.s
  }
}

function handleRightClickMenuClose() {
  rightClickOptions.show = false
}

const initEcharts = () => {
  echartsInstance = echarts.init(document.getElementById('charts3'))
  echartsSetOption()
}
const init = () => {
  const reboundDetailHideTempOrOther = window.localStorage.getItem('reboundDetailHideTempOrOther')
  if (reboundDetailHideTempOrOther) {
    const obj: any = JSON.parse(reboundDetailHideTempOrOther)
    hide_temp.value = obj.hide_temp
    hide_other.value = obj.hide_other
  }
  initEcharts();
  echartsSetOption();
  const bc = new BroadcastChannel('fps_limit')
  bc.onmessage = ()=>{
    setTimeout(()=>{
      echartsSetOption()
    },100)
  }
}

function echartsSetOption() {
  if ($store.recentGameInfo.starttime && $store.powerData.fps && $store.powerData.full && $store.powerData.full.fps) {
    let checkName = $store.CPA.show_data.name;
    let checkValue = $store.CPA.show_data.value;
    const PM_GetDetailsARR = $store.powerData.full;
    let DetailArr;
    if (PM_GetDetailsARR[checkName]) {
      if (checkName === 'memory' || checkName === 'fps' || checkName === 'cpupower' || checkName === 'frametime' || checkName === 'frametime') {
        DetailArr = PM_GetDetailsARR[checkName]['detail'];
      } else if (checkName === 'gpuload' || checkName === 'gpuload1' || checkName === 'gputemperature' || checkName === 'gpumemoryload' || checkName === 'gpuclock' || checkName === 'gpupower') {
        if (PM_GetDetailsARR[checkName].length === 2) {
          if (PM_GetDetailsARR[checkName][0]['avg'] <= 0) {
            DetailArr = PM_GetDetailsARR[checkName][1]['detail'];
          } else {
            if (PM_GetDetailsARR.gpushadres !== undefined) {
              if (PM_GetDetailsARR.gpushadres[0] < PM_GetDetailsARR.gpushadres[1]) {
                DetailArr = PM_GetDetailsARR[checkName][1]['detail'];
              } else {
                DetailArr = PM_GetDetailsARR[checkName][0]['detail'];
              }
            } else {
              DetailArr = PM_GetDetailsARR[checkName][1]['detail'];
            }
          }
        } else {
          DetailArr = PM_GetDetailsARR[checkName][0]['detail'];
        }
      } else {
        DetailArr = PM_GetDetailsARR[checkName]['detail'];
      }
    }
    if (DetailArr !== null && DetailArr !== undefined) {
      if (startTime.value.h != 0 || startTime.value.m != 0 || startTime.value.s != 0) {
        echartsShowIndexStart = Math.ceil(totalSeconds(startTime.value) / gameTime.value * DetailArr.length)
      }
      if (startTime.value.h == 0 && startTime.value.m == 0 && startTime.value.s == 0) {
        echartsShowIndexStart = 0
      }
      if (totalSeconds(endTime.value) < totalSeconds(endTimeOriginal.value)) {
        echartsShowIndexEnd = Math.ceil(totalSeconds(endTime.value) / gameTime.value * DetailArr.length)
      } else {
        echartsShowIndexEnd = -1
      }
      DELoadGraph(DetailArr, checkValue, checkName, PM_GetDetailsARR.fps1['detail'], PM_GetDetailsARR.fps01['detail']);
    } else {
      // @ts-ignore
      ElMessage(checkValue + '没有详细数据!');
    }
  } else {
    setTimeout(() => {
      echartsSetOption()
    }, 100)
  }
}
function computedFullDataMax(dataList:Array<number>) {
  let startIndex = 0
  let endIndex = 0
  if (startTime.value.h != 0 || startTime.value.m != 0 || startTime.value.s != 0) {
    startIndex = Math.ceil(totalSeconds(startTime.value) / gameTime.value * dataList.length)
  }
  if (startTime.value.h == 0 && startTime.value.m == 0 && startTime.value.s == 0) {
    startIndex = 0
  }
  if (totalSeconds(endTime.value) < totalSeconds(endTimeOriginal.value)) {
    endIndex = Math.ceil(totalSeconds(endTime.value) / gameTime.value * dataList.length)
  } else {
    endIndex = dataList.length
  }
  try {
    return MathArrayMax2(dataList.slice(startIndex, endIndex),startIndex,$store.powerData.errs)
  }catch (e) {
    return 0
  }
}
function computedFullDataMin(dataList:Array<number>) {
  let startIndex = 0
  let endIndex = 0
  if (startTime.value.h != 0 || startTime.value.m != 0 || startTime.value.s != 0) {
    startIndex = Math.ceil(totalSeconds(startTime.value) / gameTime.value * dataList.length)
  }
  if (startTime.value.h == 0 && startTime.value.m == 0 && startTime.value.s == 0) {
    startIndex = 0
  }
  if (totalSeconds(endTime.value) < totalSeconds(endTimeOriginal.value)) {
    endIndex = Math.ceil(totalSeconds(endTime.value) / gameTime.value * dataList.length)
  } else {
    endIndex = dataList.length
  }
  try {
    return MathArrayMin2(dataList.slice(startIndex, endIndex),startIndex,$store.powerData.errs)
  }catch (e) {
    return 0
  }
}
function computedFullDataAvg(dataList:Array<number>) {
  if (!Array.isArray(dataList)) return 0
  if (dataList.length == 0) return 0
  let startIndex = 0
  let endIndex = 0
  if (startTime.value.h != 0 || startTime.value.m != 0 || startTime.value.s != 0) {
    startIndex = Math.ceil(totalSeconds(startTime.value) / gameTime.value * dataList.length)
  }
  if (startTime.value.h == 0 && startTime.value.m == 0 && startTime.value.s == 0) {
    startIndex = 0
  }
  if (totalSeconds(endTime.value) < totalSeconds(endTimeOriginal.value)) {
    endIndex = Math.ceil(totalSeconds(endTime.value) / gameTime.value * dataList.length)
  } else {
    endIndex = dataList.length
  }
  try {
    return MathArrayAvg2(dataList.slice(startIndex, endIndex),startIndex,$store.powerData.errs)
  }catch (e) {
    return 0
  }
}
function isObject (obj:any) {
  return obj && typeof obj === 'object' && !Array.isArray(obj)
}
//加载折线图
function DELoadGraph(DetailArr: any, checkValue: string, CheckName: string, fps1 = [], fps01 = []) {
  if (DetailArr.length === 0) {
    // @ts-ignore
    ElMessage(t('GameRebound.noCurData'));
  }

  let maxN = computedFullDataMax(DetailArr).toFixed(0);
  let minN = computedFullDataMin(DetailArr).toFixed(0);
  let avgN = computedFullDataAvg(DetailArr).toFixed(0);

  let color1 = '#2FD274';
  let color2 = 'rgba(0, 137, 233, 0.4)';
  let DataUnit = ''
  if (CheckName === 'cpuload' || CheckName === 'gpuload' || CheckName === 'memory') {
    DataUnit = ' %';
  } else if (CheckName === 'cputemperature' || CheckName === 'gputemperature') {
    DataUnit = ' ℃';
  } else if (CheckName === 'cpupower' || CheckName === 'gpupower') {
    DataUnit = ' W';
  } else {
    DataUnit = '';
  }
  const errsSet = new Set($store.powerData.errs)
  let filterFn = (value: number, index: number)=>{
    if (errsSet.has(index)) {
      return false;
    }
    if (echartsShowIndexStart > 0 && index < echartsShowIndexStart) {
      return false
    }
    if (echartsShowIndexEnd != -1 && index > echartsShowIndexEnd) {
      return false
    }
    return true;
  }
  let DisplayArrData = DetailArr.filter(filterFn);
  let displayFormatterObj = JSON.parse(JSON.stringify($store.powerData.full))

  let _gpuarr = ['gpuload','gpuload1','gputemperature','gpumemoryload','gpuclock','gpupower','gpuvoltage']
  Object.keys(displayFormatterObj).forEach((k)=>{
    if (isObject(displayFormatterObj[k]) && Array.isArray(displayFormatterObj[k]['detail'])) {
      displayFormatterObj[k]['detail'] = displayFormatterObj[k]['detail'].filter(filterFn)
    }else if (Array.isArray(displayFormatterObj[k]) && k !== 'errs' && k !== 'errs_reason') {
      if (_gpuarr.includes(k)) {
        displayFormatterObj[k] = displayFormatterObj[k].map(item=>{
          item.detail = item.detail.filter(filterFn)
          return item;
        })
      }else{
        displayFormatterObj[k] = displayFormatterObj[k].filter(filterFn)
      }
    }
  });
  let OnePoint = '';
  for (let i = 0; i < (DisplayArrData.length) - 1; i++) {
    OnePoint += ',';
  }
  let FormartXAxisData = OnePoint.split(',');
  curDisplayDataCount.value = DisplayArrData.length
  $store.curDisplayDataCount = DisplayArrData.length
  let option:any = null;
  option = {
    title: {},
    tooltip: {
      trigger: 'axis',
      formatter: function (data: any) {
        if ((window as any).isScreenshot === true) return '';
        const PM_GetDetailsARR = displayFormatterObj;
        if (PM_GetDetailsARR.gpuload !== null && PM_GetDetailsARR.cpuload !== null) {
          let ArrSub = data[0].dataIndex;
          let TotalGameTiem: any = gameTime.value + '';
          let StartTime = $store.recentGameInfo.starttime;
          let TotalPoint = DetailArr.length;
          if (!rightClickOptions.show) {
            rightClickOptions.ArrSub = ArrSub
            rightClickOptions.dataIndex = data[0].dataIndex
            rightClickOptions.duration = PM_GetDetailsARR.duration[ArrSub];
          }

          let AllDataHtml = '<div class="AllDataHtml">'
          if ($store.CPA.show_data.name === 'fps') {
            AllDataHtml += '<span style="color: #FFFFFF">FPS : </span>' + (PM_GetDetailsARR.fps['detail'][ArrSub]?PM_GetDetailsARR.fps['detail'][ArrSub]:'无') + '<br/>';
          }else{
            AllDataHtml += '<span style="color: #777777">FPS : </span>' + (PM_GetDetailsARR.fps['detail'][ArrSub]?PM_GetDetailsARR.fps['detail'][ArrSub]:'无') + '<br/>';
          }

          if (PM_GetDetailsARR.frametime['detail'] !== null) {
            if ($store.CPA.show_data.name === 'frametime') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.FrameGenerationTime')} : </span>` + (PM_GetDetailsARR.frametime['detail'][ArrSub]?(PM_GetDetailsARR.frametime['detail'][ArrSub]+'ms'):t('GameRebound.noData')) + '<br/>';
            }else{
              AllDataHtml += `<span style="color: #777777">${t('GameRebound.FrameGenerationTime')} : </span>` + (PM_GetDetailsARR.frametime['detail'][ArrSub]?(PM_GetDetailsARR.frametime['detail'][ArrSub]+'ms'):t('GameRebound.noData')) + '<br/>';
            }
          }

          if (PM_GetDetailsARR.fps1['detail'] !== null) {
            if ($store.CPA.show_data.name === 'fps1') {
              AllDataHtml += '<span style="color: #FFFFFF">FPS 1% Low : </span>' + (PM_GetDetailsARR.fps1['detail'][ArrSub]?PM_GetDetailsARR.fps1['detail'][ArrSub]:'无') + '<br/>';
            }else{
              AllDataHtml += '<span style="color: #777777">FPS 1% Low : </span>' + (PM_GetDetailsARR.fps1['detail'][ArrSub]?PM_GetDetailsARR.fps1['detail'][ArrSub]:'无') + '<br/>';
            }
          }

          if (PM_GetDetailsARR.fps01['detail'] !== null) {
            if ($store.CPA.show_data.name === 'fps01') {
              AllDataHtml += '<span style="color: #FFFFFF">FPS 0.1% Low : </span>' + (PM_GetDetailsARR.fps01['detail'][ArrSub]?PM_GetDetailsARR.fps01['detail'][ArrSub]:'无')+ '<br/>';
            } else {
              AllDataHtml += '<span style="color: #777777">FPS 0.1% Low : </span>' + (PM_GetDetailsARR.fps01['detail'][ArrSub]?PM_GetDetailsARR.fps01['detail'][ArrSub]:'无')+ '<br/>';
            }
          }

          if ($store.CPA.show_data.name === 'cpuload') {
            AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.ProcessorOccupancy')}` + ' : </span>' + PM_GetDetailsARR.cpuload['detail'][ArrSub] + '%' + '<br/>';
          } else {
            AllDataHtml += `<span style="color: #777777">${t('GameRebound.ProcessorOccupancy')}` + ' : </span>' + PM_GetDetailsARR.cpuload['detail'][ArrSub] + '%' + '<br/>';
          }

          if (PM_GetDetailsARR.cpuloadP && PM_GetDetailsARR.cpuloadP.avg !== 0) {
            if ($store.CPA.show_data.name === 'cpuloadP') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.ProcessorOccupancy')}-P` + ' : </span>' + PM_GetDetailsARR.cpuloadP['detail'][ArrSub] + '%' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777777">${t('GameRebound.ProcessorOccupancy')}-P` + ' : </span>' + PM_GetDetailsARR.cpuloadP['detail'][ArrSub] + '%' + '<br/>';
            }
          }
          if (PM_GetDetailsARR.cpuloadE && PM_GetDetailsARR.cpuloadE.avg !== 0) {
            if ($store.CPA.show_data.name === 'cpuloadE') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.ProcessorOccupancy')}-E` + ' : </span>' + PM_GetDetailsARR.cpuloadE['detail'][ArrSub] + '%' + '<br/>';
            }else{
              AllDataHtml += `<span style="color: #777777">${t('GameRebound.ProcessorOccupancy')}-E` + ' : </span>' + PM_GetDetailsARR.cpuloadE['detail'][ArrSub] + '%' + '<br/>';
            }
          }

          if ($store.CPA.show_data.name === 'cpuclock') {
            AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.ProcessorFrequency')}` + ' : </span>' + PM_GetDetailsARR.cpuclock['detail'][ArrSub] + 'MHz' + '<br/>';
          } else {
            AllDataHtml += `<span style="color: #777777">${t('GameRebound.ProcessorFrequency')}` + ' : </span>' + PM_GetDetailsARR.cpuclock['detail'][ArrSub] + 'MHz' + '<br/>';
          }
          if (PM_GetDetailsARR.cpuclockP && PM_GetDetailsARR.cpuclockP.avg !== 0) {
            if ($store.CPA.show_data.name === 'cpuclockP') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.ProcessorFrequency')}-P` + ' : </span>' + PM_GetDetailsARR.cpuclockP['detail'][ArrSub] + 'MHz' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777777">${t('GameRebound.ProcessorFrequency')}-P` + ' : </span>' + PM_GetDetailsARR.cpuclockP['detail'][ArrSub] + 'MHz' + '<br/>';
            }
          }
          if (PM_GetDetailsARR.cpuclockE && PM_GetDetailsARR.cpuclockE.avg !== 0) {
            if ($store.CPA.show_data.name === 'cpuclockE') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.ProcessorFrequency')}-E` + ' : </span>' + PM_GetDetailsARR.cpuclockE['detail'][ArrSub] + 'MHz' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777777">${t('GameRebound.ProcessorFrequency')}-E` + ' : </span>' + PM_GetDetailsARR.cpuclockE['detail'][ArrSub] + 'MHz' + '<br/>';
            }
          }
          if ($store.CPA.show_data.name === 'cputemperature') {
            AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.ProcessorTemperature')}` + ' : </span>' + PM_GetDetailsARR.cputemperature['detail'][ArrSub] + '℃' + '<br/>';
          } else {
            AllDataHtml += `<span style="color: #777777">${t('GameRebound.ProcessorTemperature')}` + ' : </span>' + PM_GetDetailsARR.cputemperature['detail'][ArrSub] + '℃' + '<br/>';
          }
          if ($store.CPA.show_data.name === 'cpupower') {
            AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.ProcessorHeatPower')}` + ' : </span>' + PM_GetDetailsARR.cpupower['detail'][ArrSub] + 'W' + '<br/>';
          } else {
            AllDataHtml += `<span style="color: #777777">${t('GameRebound.ProcessorHeatPower')}` + ' : </span>' + PM_GetDetailsARR.cpupower['detail'][ArrSub] + 'W' + '<br/>';
          }

          let GpuArrlocation = 0;
          if (PM_GetDetailsARR.gpuload.length > 1) {
            if (PM_GetDetailsARR.gpushadres && (PM_GetDetailsARR.gpushadres).length === 2) {
              if (PM_GetDetailsARR.gpushadres[0] < PM_GetDetailsARR.gpushadres[1]) {
                GpuArrlocation = 1;
              }
            }
          }

          if (PM_GetDetailsARR.gpuload[GpuArrlocation]['detail'] !== null) {
            if ($store.CPA.show_data.name === 'gpuload') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.GraphicsCardOccupancy')}` + ' : </span>' + PM_GetDetailsARR.gpuload[GpuArrlocation]['detail'][ArrSub] + '%' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777777">${t('GameRebound.GraphicsCardOccupancy')}` + ' : </span>' + PM_GetDetailsARR.gpuload[GpuArrlocation]['detail'][ArrSub] + '%' + '<br/>';
            }
          }

          if (PM_GetDetailsARR.gpuload1 && PM_GetDetailsARR.gpuload1[GpuArrlocation]['detail'] !== null) {
            if ($store.CPA.show_data.name === 'gpuload1') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.GraphicsCardOccupancyTotal')}` + ' : </span>' + PM_GetDetailsARR.gpuload1[GpuArrlocation]['detail'][ArrSub] + '%' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777777">${t('GameRebound.GraphicsCardOccupancyTotal')}` + ' : </span>' + PM_GetDetailsARR.gpuload1[GpuArrlocation]['detail'][ArrSub] + '%' + '<br/>';
            }
          }

          if ($store.CPA.show_data.name === 'gpuclock') {
            AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.GraphicsCardFrequency')}` + ' : </span>' + PM_GetDetailsARR.gpuclock[GpuArrlocation]['detail'][ArrSub] + 'MHz' + '<br/>';
          } else {
            AllDataHtml += `<span style="color: #777777">${t('GameRebound.GraphicsCardFrequency')}` + ' : </span>' + PM_GetDetailsARR.gpuclock[GpuArrlocation]['detail'][ArrSub] + 'MHz' + '<br/>';
          }

          if (PM_GetDetailsARR.gputemperature && PM_GetDetailsARR.gputemperature[GpuArrlocation]['detail'] !== null) {
            if ($store.CPA.show_data.name === 'gputemperature') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.GraphicsCardTemperature')}` + ' : </span>' + PM_GetDetailsARR.gputemperature[GpuArrlocation]['detail'][ArrSub] + '℃' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777777">${t('GameRebound.GraphicsCardTemperature')}` + ' : </span>' + PM_GetDetailsARR.gputemperature[GpuArrlocation]['detail'][ArrSub] + '℃' + '<br/>';
            }
          }

          if (PM_GetDetailsARR.gpuhotspottemp && PM_GetDetailsARR.gpuhotspottemp['detail'] !== null) {
            if ($store.CPA.show_data.name === 'gpuhotspottemp') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.GraphicsCardCoreTemperature')}` + ' : </span>' + PM_GetDetailsARR.gpuhotspottemp['detail'][ArrSub] + '℃' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777777">${t('GameRebound.GraphicsCardCoreTemperature')}` + ' : </span>' + PM_GetDetailsARR.gpuhotspottemp['detail'][ArrSub] + '℃' + '<br/>';
            }
          }

          if ($store.CPA.show_data.name === 'gpupower') {
            AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.GraphicsCardHeatPower')}` + ' : </span>' + PM_GetDetailsARR.gpupower[GpuArrlocation]['detail'][ArrSub] + 'W' + '<br/>';
          } else {
            AllDataHtml += `<span style="color: #777777">${t('GameRebound.GraphicsCardHeatPower')}` + ' : </span>' + PM_GetDetailsARR.gpupower[GpuArrlocation]['detail'][ArrSub] + 'W' + '<br/>';
          }

          if (PM_GetDetailsARR.gpumemoryload && PM_GetDetailsARR.gpumemoryload[GpuArrlocation]['detail'] !== null) {
            if ($store.CPA.show_data.name === 'gpumemoryload') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('hardwareInfo.VRAM')}` + ' : </span>' + PM_GetDetailsARR.gpumemoryload[GpuArrlocation]['detail'][ArrSub] + '%' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777777">${t('hardwareInfo.VRAM')}` + ' : </span>' + PM_GetDetailsARR.gpumemoryload[GpuArrlocation]['detail'][ArrSub] + '%' + '<br/>';
            }
          }

          if (PM_GetDetailsARR.gpumemorytemp && PM_GetDetailsARR.gpumemorytemp['detail'] !== null && PM_GetDetailsARR.gpumemorytemp['detail'][ArrSub]) {
            if ($store.CPA.show_data.name === 'gpumemorytemp') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.GraphicsCardMemoryTemperature')}` + ' : </span>' + PM_GetDetailsARR.gpumemorytemp['detail'][ArrSub] + '℃' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777777">${t('GameRebound.GraphicsCardMemoryTemperature')}` + ' : </span>' + PM_GetDetailsARR.gpumemorytemp['detail'][ArrSub] + '℃' + '<br/>';
            }
          }

          if (PM_GetDetailsARR.gpumemoryclock && PM_GetDetailsARR.gpumemoryclock['detail'] !== null) {
            if ($store.CPA.show_data.name === 'gpumemoryclock') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('hardwareInfo.VRAMFrequency')}` + ' : </span>' + PM_GetDetailsARR.gpumemoryclock['detail'][ArrSub] + 'MHz' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777777">${t('hardwareInfo.VRAMFrequency')}` + ' : </span>' + PM_GetDetailsARR.gpumemoryclock['detail'][ArrSub] + 'MHz' + '<br/>';
            }
          }

          if ($store.CPA.show_data.name === 'memory') {
            AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.MemoryOccupancy')}` + ' : </span>' + PM_GetDetailsARR.memory['detail'][ArrSub] + '%' + '<br/>';
          } else {
            AllDataHtml += `<span style="color: #777777">${t('GameRebound.MemoryOccupancy')}` + ' : </span>' + PM_GetDetailsARR.memory['detail'][ArrSub] + '%' + '<br/>';
          }

          if (PM_GetDetailsARR.memorytemperature && PM_GetDetailsARR.memorytemperature['detail'] !== null) {
            if ($store.CPA.show_data.name === 'memorytemperature') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.MemoryTemperature')}` + ' : </span>' + PM_GetDetailsARR.memorytemperature['detail'][ArrSub] + '℃' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777777">${t('GameRebound.MemoryTemperature')}` + ' : </span>' + PM_GetDetailsARR.memorytemperature['detail'][ArrSub] + '℃' + '<br/>';
            }
          }
          if (PM_GetDetailsARR.pageFaultDelta && PM_GetDetailsARR.pageFaultDelta['detail'] !== null) {
            if ($store.CPA.show_data.name === 'pageFaultDelta') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.MemoryPageFaults')}` + ' : </span>' + PM_GetDetailsARR.pageFaultDelta['detail'][ArrSub]  + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777777">${t('GameRebound.MemoryPageFaults')}` + ' : </span>' + PM_GetDetailsARR.pageFaultDelta['detail'][ArrSub]  + '<br/>';
            }
          }

          let duration = PM_GetDetailsARR.duration[ArrSub]
          AllDataHtml += `<span style="color: #777777">${t('GameRebound.Duration')}` + ' : </span>' + FormatSeconds(duration, true) + '<br/>';
          AllDataHtml += `<span style="color: #777777">${t('GameRebound.Time')}` + ' : </span>' + FormatTime(Number((StartTime + duration))) + '<br/>' + '</div>';
          // 降频截图 打开既有
          // if(1>0){//判断加数组该点是否存在降频图片
          //     AllDataHtml += '降频截图：'+ '</br>'
          //     AllDataHtml +="<img style='width:140px;height:80px;margin-top:5px;margin-bottom:10px;cursor:pointer;' class='Frequencypic' onclick='Openthispic()'>";
          // }

          return AllDataHtml;
        } else {
          return null;
        }

      },
      confine: true,
      backgroundColor: 'rgb(44,44,51,0.8)',
      textStyle: {
        fontSize: '12',
        color: 'rgb(195,202,213)'
      },
      className: 'echarts-tooltip'
    },
    grid: {
      left: 0,
      right: 0,
      bottom: 0,
      top: 0,
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: FormartXAxisData,
        axisLine: {
          lineStyle: {
            type: 'solid',
            color: '#333645',//左边线的颜色
            width: '1'//坐标线的宽度
          }
        },
        axisTick: { //x轴刻度线
          show: false
        },
        axisLabel: {
          textStyle: {
            color: '#333645'//坐标值得具体的颜色
          }
        },
        splitArea: {
          show: false,
          areaStyle: {
            color: [
              'rgba(37,39,47,.5)',
              'rgba(100,100,100,.5)'
            ]
          }
        },
        splitLine: {
          show: true,
          //  改变轴线颜色
          lineStyle: {
            // 使用深浅的间隔色
            color: ['#333645']
          }
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        // min: minN - 1,
        // max: maxN + 1,
        dataMax: "",
        axisLabel: {
          // formatter: '{value} °C',
          color: "#21222a"  //刻度线标签颜色
        },
        axisTick: { //y轴刻度线
          show: false
        },
        splitLine: {
          show: true,
          //  改变轴线颜色
          lineStyle: {
            // 使用深浅的间隔色
            color: ['#333645']
          }
        }
      }
    ],

    series: [
      {
        name: checkValue,
        type: 'line',
        symbol: 'circle',  //取消折点圆圈
        symbolSize: '2',
        stack: '总量',

        data: DisplayArrData,
        cursor: 'default',
        itemStyle: {
          normal: {
//                        label : {show: true},
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                // 0% 处的颜色
                offset: 0, color: color1
              },
              {
                // 100% 处的颜色
                offset: 1, color: color2
              }
            ], false),
            borderColor: '#0089E9',
            opacity: 0,
            borderWidth: 1,
            lineStyle: {
              color: color1
            }

          }, lineStyle: {
            // 使用深浅的间隔色
            color: ['#333645']
          },
          emphasis: {
            // borderColor:'red'
            color: '#39404d',
            borderColor: '#0089E9',
            opacity: 1,
          }
        },
        markLine: {
          symbol: ['none', 'none'],
          label: {show: false},
          data: [

          ],
        }
      }
    ],
  };
  if (line_item.value && Object.keys(line_item.value).length > 0) { // point的竖线
    Object.keys(line_item.value).forEach(function (key: any) {
      if (Number(key) > echartsShowIndexStart && (Number(key) < echartsShowIndexEnd || echartsShowIndexEnd == -1)) {
        console.log(option)
        option.series[0].markLine.data.push({
          xAxis: Number(key) - echartsShowIndexStart,
          lineStyle: {
            type: 'solid',
            color: '#0089E9',
            lineWidth: 2
          },
          label: {
            show: false
          }
        })
      }
    })
  }
  if (option && typeof option === "object") {
    echartsInstance.setOption(option, true);
  }
}
const saveHideStatus = () => {
  window.localStorage.setItem('reboundDetailHideTempOrOther', JSON.stringify({
    hide_temp: hide_temp.value,
    hide_other: hide_other.value
  }))
  echartsSetOption()
}

onMounted(() => {
  init();
})

watch([startTime,endTime],()=>{
  echartsSetOption()
},{deep:true})

</script>

<template>
  <div class="chart-container">
      <div class="top-info">
          <p class="title">{{ $t('GameRebound.LineChart') }}</p>
          <div class="data-screening" style="margin-left: auto;" @click="$store.openFpsLimitDialog">
              <span class="iconfont icon-parameter"></span>
              <span>{{$t('GameRebound.dataScreening')}}</span>
          </div>
      </div>

    <div class="options">
      <el-dropdown @command="handleCommand" trigger="click" max-height="200">
        <div class="show-data-item">
          <span style="width: 85px;color: #2FD274;">{{ $t(CPA.show_data.value) + CPA.show_data.value2 }}</span>
          <span class="iconfont icon-hideshow"></span>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item
                v-for="(item2, index2) in dropdown_menu_toshow"
                :key="item2.name"
                :command="item2"
            ><span>{{ $t(item2.value) + (item2['value2']?item2['value2']:"") }}</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <div class="hide-options">
        <el-checkbox @change="saveHideStatus" v-model="hide_temp" :label="$t('GameRebound.HideTemperatureReason2')" />
      </div>
    </div>

    <div id="charts3" @contextmenu="handleEchartsContextMenu"></div>
    <div class="chart3-bottom-info">
      <div class="left-box">
        <span>{{displayHMSTime(startTime)}}</span>
        <text>{{ $t('GameRebound.ThereAreSamplingData') }}</text>
        <span>{{curDisplayDataCount}}{{ $t('GameRebound.Items') }}</span>
      </div>
      <div class="right-box">
        <span>{{displayHMSTime(endTime)}}</span>
      </div>
    </div>
    <div
        v-for="item in chart3_temp_limit_area"
        :key="item+'charts'"
        class="chart3-temp-limit-area"
        :style="{width:item.width+'px',transform: `translateX(${item.left}px)`}"
    >
    </div>
  </div>

  <teleport to="body">
    <div class="right-click-markline" v-show="rightClickOptions.show" :style="{left: rightClickOptions.clientX+'px'}"></div>
  </teleport>
  <RightClickMenu ref="rcm" @command="handleRightClickMenuCommand" @close="handleRightClickMenuClose"/>
</template>

<style scoped lang="scss">
.right-click-markline {
    width: 1px;
    height: 198px;
    background-color: #fff;
    position: absolute;
    top: 203px;
    left: 0;
}
.chart-container {
  width: 1200px;
  height: 255px;
  position: relative;

  .top-info {
    display: flex;
    height: 16px;
    margin-bottom: 14px;
  }

  p.title {
    font-weight: 400;
    font-size: 12px;
    color: #ffffff;
  }

  .options {
    display: flex;
    flex-flow: row nowrap;
    height: 20px;

    .hide-options {
      margin-left: auto;
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      color: #777777;

      :deep(.el-checkbox) {
        color: #777777;
      }

      :deep(.el-checkbox__inner) {
        border-radius: 4px;
        height: 16px;
        width: 16px;
      }

      :deep(.el-checkbox__inner:after) {
        left: 5px;
        top: 2px;
      }
    }
  }

  .show-data-item {
    width: 110px;
    height: 20px;
    background: #22232E;
    box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    border: 1px solid #777777;
    margin-right: 10px;
    cursor: pointer;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: space-between;
    padding: 0 5px;
    font-size: 12px;

    span {
      color: #ffffff;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }

  #charts3 {
    width: 1200px;
    height: 200px;
    background: #22232E;
    box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    border: 1px solid #777777;
    margin-top: 10px;

  zoom: calc(1 / var(--zoomV--));
  transform: scale(var(--zoomV--));
  transform-origin: 0 0;
    &:hover {
        z-index: 999;
    }
  }

  .chart3-bottom-info {
    width: 1200px;
    height: 20px;
    bottom: 5px;
    position: absolute;
    padding: 0 10px;
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
    color: #777777;
    z-index:200;
    span {
      color: #ffffff;
    }

    .left-box,.right-box {
      display: flex;
      gap: 5px;
      height: 20px;
      line-height: 20px;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 4px;
      padding: 0 10px;
      font-size: 12px;
    }
  }

  .chart3-temp-limit-area {
    position: absolute;
    height: 200px;
    bottom: -5px;
    background: linear-gradient(
            to bottom,
            rgba(191,64,64,0.4) 0%,       /* 上部红色 */
            rgba(191,64,64,0.2) 25%,    /* 开始逐渐变为透明 */
            rgba(191,64,64,0) 50%,      /* 完全透明 */
            rgba(191,64,64,0.2) 75%,    /* 开始逐渐从透明变为红色 */
            rgba(191,64,64,0.4) 100%      /* 下部红色 */
    );
    pointer-events: none;
    transform: translateX(0);
  }
  .chart3-other-limit-area {
    position: absolute;
    height: 200px;
    bottom: -5px;
    background: linear-gradient(
            to bottom,
            rgba(210,127,46,0.4) 0%,       /* 上部红色 */
            rgba(210,127,46,0.2) 25%,    /* 开始逐渐变为透明 */
            rgba(210,127,46,0) 50%,      /* 完全透明 */
            rgba(210,127,46,0.2) 75%,    /* 开始逐渐从透明变为红色 */
            rgba(210,127,46,0.4) 100%      /* 下部红色 */
    );
    pointer-events: none;
    transform: translateX(0);
      z-index: 99999;
  }
}
</style>
