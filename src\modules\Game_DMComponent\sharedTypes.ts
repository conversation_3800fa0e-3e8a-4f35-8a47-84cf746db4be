export interface Style {
    zIndex: number;
    fontSize?: number;
    color?: string;
    top: number;
    left: number;
    width?: number;
    height?: number;
    fontFamily?: string;
    fontStyle?: string;
    fontWeight?: string;
    strokeColor?: string;
    pathFills?: string[]
    seconds?: number;
    backgroundColor?: string;
    backgroundColor2?: string;
    borderRadius?: number;
    ProgressColor?: string;
    ProgressColor2?: string;
    Interval?: number;
    Judgmentitem?: number;
    gradientColors1?: string;
    gradientColors2?: string;
    areaColors1?: string;
    areaColors2?: string;
    shadow?: {
      enabled?: boolean;
      x?: number;
      y?: number;
      blur?: number;
      color?: string;
      [key: string]: any;
    };
    animation?: {
      // enabled?: boolean;
      names?: number;
      names2?: number;
      duration?: number;
      duration2?: number;
      [key: string]: any;
    }
    [key: string]: any;
  }

  export interface Sensor {
    id: number;
    nums: number;
    type: string;
    type2: string;
    remark: string;
    sensor: string;
    parameters?: string;
    belong?: string;
    page: number;
    group: boolean;
    showDetails: boolean;
    hideDetails: boolean;
    settop: boolean;
    showdelete: boolean;
    enter: boolean;
    unit?: string;
    unitshow?: boolean;
    mediaSrc?: string;
    processedSvg?: string;
    style: Style;
    timeType?: number;
    timeFormat?: number;
    timeRule?: number;
    class?: string;
    Switchcolorsshow?: boolean;
    transformShow?: boolean;
  }