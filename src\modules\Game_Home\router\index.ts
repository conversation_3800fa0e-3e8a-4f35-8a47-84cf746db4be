import { createRouter, createWebHistory } from 'vue-router'
import Tab from '../views/Main.vue'
import HardWare from '../views/HardWare.vue'
import GameRebound  from '@/modules/Game_Rebound/GameRebound.vue'
import InGameMonitor from "@/modules/Game_Home/views/InGameMonitor.vue";
import GameMirror from "../../../modules/Game_Mirror/GameMirror.vue";

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'home',
      component: Tab
    },

    { path: '/HardWare',
      component: HardWare,
    },

    { path: '/GameRebound',
      component: GameRebound
    },
    { path: '/InGameMonitor',
      component: InGameMonitor
    },
    {
      path: '/GameMirror',
      name: 'GameMirror',
      component: GameMirror
    },
  ]
})


export default router
