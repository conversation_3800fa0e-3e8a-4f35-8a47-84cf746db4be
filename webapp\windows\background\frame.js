async function handleClose() {
  const getTitleRes = gamepp.webapp.windows.getTitle.sync()
  await gamepp.webapp.sendInternalAppEvent.promise('desktop', 'frameClose');
  if (getTitleRes) {
    if (getTitleRes.indexOf('充值') !== -1) {
      var Obj = {};
      Obj['action'] = 'refreshUserToken';
      await gamepp.webapp.sendInternalAppEvent.promise('background', Obj);
    }
  }
  gamepp.webapp.windows.close.sync();    
}

function start() {
    let el = document.getElementById("frameTitle");
    let title = gamepp.webapp.windows.getTitle.sync();
    el.innerHTML = title;
}

start();
