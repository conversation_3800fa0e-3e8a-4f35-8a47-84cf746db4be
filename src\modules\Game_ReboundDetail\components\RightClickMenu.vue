<script setup lang="ts">
import {reactive, ref, computed, nextTick, defineEmits, onMounted} from 'vue'
const emit = defineEmits(['command','close'])
const menuRef = ref<HTMLUListElement>()
const menuShow = ref(false)// 菜单是否显示
const menuPosition = reactive({
  screenX: 0,
  screenY: 0,
  clientX: 0,
  clientY: 0,
})
const computedMenuPosition = computed(() => { // 菜单位置
  let x = menuPosition.clientX;
  let y = menuPosition.clientY;

  if (x + 150 > window.innerWidth) {
    x = x - 120
  }

  return {
    left: x + 'px',
    top: y + 'px',
  }
})

onMounted(()=>{
  window.addEventListener('resize',closeMenu)
})

function openMenu(clientX:number, clientY:number, screenX:number, screenY:number) {
  const zoomV = gamepp.display.getScaleFromWindowInMonitor.sync();
  menuShow.value = true
  menuPosition.screenX = screenX / zoomV
  menuPosition.screenY = screenY / zoomV
  menuPosition.clientX = clientX / zoomV
  menuPosition.clientY = clientY / zoomV

  nextTick(()=>{
    menuRef.value?.focus()
  })
}
function closeMenu() {
  if (menuShow.value) {
    emit('close')
    menuShow.value = false
  }
}

function handleClick(n:number) {
  emit('command', n)
  closeMenu()
}

defineExpose({
  openMenu,
  closeMenu
})
</script>

<template>
  <teleport to="body">
    <ul ref="menuRef" class="right_click_menu" v-show="menuShow" :style="computedMenuPosition" @blur="closeMenu" tabindex="-1">
      <li @click="handleClick(1)">{{ $t('GameRebound.SetAsStartTime') }}</li>
      <li @click="handleClick(2)">{{ $t('GameRebound.SetAsEndTime') }}</li>
    </ul>
  </teleport>
</template>

<style scoped lang="scss">
.right_click_menu {
  position: absolute;
  width: 105px;
  background: #2d2e39;
  margin-top: -30px;
  margin-left: 10px;
  z-index: 9999999;
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  border: 0;

  &:focus {
    outline: none;
  }

  li {
    color: #ffffff;
    padding: 5px 10px;
    cursor: pointer;
    &:hover {
      background: #4a4c56;
    }
  }
}
</style>
