const ro = {
  "update": {
    "Game": "Game",
    "PP": "PP",
    "upgradingInProgress": "Actualizarea este în curs",
    "theModuleIsBeingUpdated": "Actualizare modul",
    "dataIsBeingUpdated": "Se actualizează datele...",
    "checkingUpdate": "Se verifică actualizările",
    "checkingUpgrade": "Verificare actualizări",
    "loadingProgramComponent": "Încărcarea componentelor programului...",
    "loadingHotkeyModules": "Încărcarea componentei de taste rapide",
    "loadingGPPModules": "Se încarcă componentele GamePP",
    "loadingBlackWhiteList": "Încărcare listă neagră și albă",
    "loadingGameSetting": "Încărcarea parametrilor de configurare a jocului...",
    "loadingUserAbout": "Încărcare date de autentificare utilizator",
    "loadingGameBenchmark": "Încarcare scor joc",
    "loadingHardwareInfo": "Încărcarea componentei de informații hardware",
    "loadingDBModules": "Încărcare modul de bază de date...",
    "loadingIGCModules": "Încărcarea modulului IGC",
    "loadingFTPModules": "Încărcarea modulului de suport FTP este în curs",
    "loadingDialogModules": "Încărcarea modulului de casetă de dialog",
    "loadingDataStatisticsModules": "Încărcarea modulului de statistică este în desfășurare",
    "loadingSysModules": "Încărcarea componentelor de sistem este în curs",
    "loadingGameOptimization": "Optimizarea jocului se încarcă",
    "loadingGameAcceleration": "Încărcare accelerare joc",
    "loadingScreenshot": "Încărcare captură ecran înregistrare",
    "loadingVideoComponent": "Încărcare componentă de compresie video",
    "loadingFileFix": "Încărcare reparare fișier",
    "loadingGameAI": "Încărcarea calității AI-ului jocului",
    "loadingNVAPIModules": "Încărcarea modulului NVAPI",
    "loadingAMDADLModules": "Se încarcă modulul AMDADL",
    "loadingModules": "Se încarcă modulul"
  },
  "messages": {
    "append": "Adăuga",
    "confirm": "Confirmă",
    "cancel": "Anulează",
    "default": "Implicit",
    "quickSelect": "Alegere Rapidă",
    "onoffingame": "Activați/Dezactivați monitorizarea în joc:",
    "changeKey": "Faceți clic pentru a schimba combinația de taste",
    "clear": "Șterge",
    "hotkeyOccupied": "Combinația de taste este deja utilizată. Vă rugăm să configurați una nouă！",
    "minimize": "Minimizare",
    "exit": "Ieșire",
    "export": "Exportați",
    "import": "Import",
    "screenshot": "Captură de ecran",
    "showHideWindow": "Arată/Ascunde fereastra",
    "ingameControlPanel": "Panou de control în joc",
    "openOrCloseGameInSettings": "Comutați panoul de setări din joc",
    "openOrCloseGameInSettings2": "Apăsați această combinație de taste pentru a o activa",
    "openOrCloseGameInSettings3": "Activați/Dezactivați monitorizarea în joc",
    "openOrCloseGameInSettings4": "Activați/Dezactivați filtrul de joc",
    "startManualRecord": "Începe/Opriți înregistrarea manuală a statisticilor",
    "performanceStatisticsMark": "Marcatorul Statisticilor de Performanță",
    "EnableAIfilter": "Filtrul AI necesită apăsarea acestei taste rapide pentru activare",
    "Start_stop": "Porniți/Opriteți înregistrarea statistică manuală",
    "pressureTest": "Test de stres",
    "moduleNotInstalled": "Modul funcțional neinstalat",
    "installingPressureTest": "Instalarea modulului de test de solicitare...",
    "importFailed": "Importul a eșuat",
    "gamepp": "GamePP",
    "copyToClipboard": "Copiat în clipboard"
  },
  "home": {
    "homeTitle": "Acasă",
    "hardwareInfo": "Informații hardware",
    "functionIntroduction": "Funcționalități",
    "fixedToNav": "Atașează la bara de navigare",
    "cancelFixedToNav": "Dezabateți din bara de navigare",
    "hardwareInfoLoading": "Încărcare informații hardware...",
    "performanceStatistics": "Statistici de performanță",
    "updateNow": "Actualizează acum",
    "recentRun": "Activitate recentă",
    "resolution": "Rezoluție:",
    "duration": "Durată:",
    "gameFilter": "Filtru de jocuri",
    "gameFilterHasAccompany": "Filtrul de jocuri este activat",
    "gameFilterHasAccompany2": "Utilizatorii joacă jocuri precum Cyberpunk, APEX și Hogwarts Legacy.",
    "currentList": "Elemente monitorizate în lista actuală",
    "moreFunction": "Benchmark, test de stres, monitorizarea desktopului și mai multe funcții sunt în curs de dezvoltare.",
    "newVersion": "Este disponibilă o nouă versiune !",
    "discoverUpdate": "Actualizare găsită！",
    "downloading": "Descărcare",
    "retry": "Încercați din nou",
    "erhaAI": "2HaAI",
    "recordingmodule": "Această funcție depinde de modulul de înregistrare",
    "superPower": "Mod Ultra",
    "autoRecord": "Înregistrarea automată a momentelor de ucidere în joc și salvarea ușoară a clipurilor de vârf",
    "externalDevice": "Iluminare periferică dinamică",
    "linkage": "Activează scenele de kill din jocuri și le afișează prin intermediul dispozitivelor periferice conectate",
    "AI": "Test de performanță AI",
    "test": "Testați modelele AI cu GPU și vizualizați scorul de performanță AI al GPU-ului",
    "supportedGames": "Jocuri suportate",
    "games": "Naraka: Bladepoint, Delta Force, FragPunk, League of Legends, PUBG, Valorant, The Finals, APEX Legends, COD20",
    "videoRecording": "Înregistrare video",
    "videoRecording2": "Funcția de înregistrare video bazată pe OBS, permite ajustarea bit rate-ului și frecvenței cadrului (FPS) pentru a îndeplini diferite cerințe de calitate și fluiditate; de asemenea, suportă \"Reproducere instantanee\", apăsând tasta de scurtătura pentru a salva momentele importante oricând!",
    "addOne": "Obține gratuit",
    "gamePlatform": "Platformă de joc",
    "goShop": "Mergi la pagina magazinului",
    "receiveDeadline": "Termenul de solicitare după acțiune",
    "2Ai": "2 Râset AI",
    "questionDesc": "Descriere a problemei",
    "inputYourQuestion": "Vă rugăm să introduceți aici sugestiile sau comentariile dvs。",
    "uploadLimit": "Încărcați până la 3 imagini locale în format JPG/PNG/BMP",
    "email": "E-mail",
    "contactWay": "Informații de contact",
    "qqNumber": "Număr QQ (opțional)",
    "submit": "Trimite"
  },
  "hardwareInfo": {
    "hardwareOverview": "Prezentare generală hardware",
    "copyAllHardwareInfo": "Copiați toate informațiile despre hardware",
    "processor": "Procesor",
    "coreCount": "Nuclei :",
    "threadCount": "Număr de thread-uri:",
    "currentFrequency": "Frecvență curentă:",
    "currentVoltage": "Tensiunea actuală:",
    "copy": "Copiere",
    "releaseDate": "Data lansării",
    "codeName": "Nume cod",
    "thermalDesignPower": "Putere Termică de Proiectare",
    "maxTemperature": "Temperatura maximă",
    "graphicsCard": "Placă video",
    "brand": "Marcă:",
    "streamProcessors": "Procesor de stream:",
    "Videomemory": "Memorie video：",
    "busSpeed": "Viteză de bus",
    "driverInfo": "Informații despre driver",
    "driverInstallDate": "Data instalării driverului",
    "hardwareID": "ID hardware",
    "motherboard": "Placă de bază",
    "chipGroup": "Chipset:",
    "BIOSDate": "Data BIOS",
    "BIOSVersion": "Versiune BIOS",
    "PCIESlots": "slot PCIe",
    "PCIEVersion": "Versiune PCIe suportată",
    "memory": "Memorie",
    "memoryBarCount": "Cantitate:",
    "totalSize": "Dimensiune:",
    "channelCount": "Canal:",
    "Specificmodel": "Model specific",
    "Pellet": "Generator de particule",
    "memoryBarEquivalentFrequency": "Frecvența memoriei eficiente:",
    "hardDisk": "Disc fix",
    "hardDiskCount": "Număr de discuri rigide:",
    "actualCapacity": "Capacitate reală",
    "type": "Tip",
    "powerOnTime": "Timp de pornire",
    "powerOnCount": "Cicluri de energie",
    "SSDRemainingLife": "Durată de viață rămasă pentru SSD",
    "partitionInfo": "Informații despre partitie",
    "hardDiskController": "Controler de disc dur",
    "driverNumber": "Număr de disc",
    "display": "Afișare",
    "refreshRate": "Rata de actualizare:",
    "screenSize": "Dimensiunea ecranului:",
    "inches": "inch",
    "productionDate": "Data producției",
    "supportRefreshRate": "Suport pentru frecvența de actualizare",
    "screenLongAndShort": "Dimensiuni ecran",
    "systemInfo": "Informații de sistem",
    "version": "Versiune",
    "systemInstallDate": "Data instalării sistemului",
    "systemBootTime": "Timpul de pornire curent",
    "systemRunTime": "Timp de execuție",
    "Poccupied": "Utilizare P",
    "Eoccupied": "E este ocupat",
    "occupied": "Ocupat",
    "temperature": "Temperatură",
    "Pfrequency": "Frecvența procesorului",
    "Efrequency": "Frecvență E",
    "thermalPower": "Energie termică",
    "frequency": "Frecvență",
    "current": "Actual",
    "noData": "Niciun date",
    "loadHwinfo_SDK": "Nu s-a putut încărca Hwinfo_SDK.dll, nu se pot citi datele hardware/senzorului.",
    "loadHwinfo_SDK_reason": "Posibile cauze ale acestei probleme:",
    "reason": "Motiv",
    "BlockIntercept": "Blokat de către software-ul antivirus, de exemplu: 2345 Antivirus Software (Procesul 2345 Active Defense, Procesul Active Defense McAfee)",
    "solution": "Soluție:",
    "solution1": "După închiderea și deșteptarea proceselor asociate, reporniți GamePP",
    "solution2": "După deconectarea dispozitivelor asociate, reporniți GamePP",
    "RestartGamePP": "Deconectați controlorul, așteptați răspunsul dispozitivului și apoi reporniți GamePP",
    "HWINFOcannotrun": "Hwinfo nu se poate executa corect",
    "downloadHWINFO": "Descarcă Hwinfo",
    "openHWINFO": "După lansarea Hwinfo, apăsarea pe RUN va porni programul normal?",
    "hardwareDriverProblem": "Probleme cu driverele hardware",
    "checkHardwareManager": "Verificați managerul de hardware pentru a vă asigura că driverii plăcii de bază și ai plăcii grafice sunt instalați corect",
    "systemProblem": "Probleme de sistem, de exemplu: Utilizarea unor instrumente de activare precum Baofeng sau Xiaoma poate duce la eșecul încărcării controlerelor, iar pachetele de sistem Windows 7 nu pot fi instalate automat",
    "reinstallSystem": "Reinstalați sistemul pentru a-l activa. Descărcați și instalați Windows 7: corectia *********",
    "Windows7": "Windows 7: Instalare corecție SHA-256, Windows 10: Activați folosind un certificat digital, Windows 11 Versiune de prezentare: Dezactivați Memory Integrity",
    "ViolenceActivator": "Dacă ați folosit unelte de activare prin forță bruto, cum ar fi Xiaoma, vă rugăm să reparați sau să reinstalați sistemul",
    "MultipleGraphicsCardDrivers": "Pe calculator sunt instalați drivere pentru plăci grafice de diferite mărci, cum ar fi driverele AMD și Nvidia instalate simultan.",
    "UninstallUnused": "Reporniți calculatorul după dezinstalarea driverelor grafice inutile",
    "OfficialQgroup": "Nici unul dintre motivele de mai sus nu se aplică. Vă rugăm să vă alăturați grupului nostru oficial QQ: 908287288 (Grupul 5) pentru rezolvare.",
    "ExportHardwareData": "Exportați datele hardware",
    "D3D": "Utilizare D3D",
    "Total": "Utilizare totală",
    "VRAM": "Utilizare VRAM",
    "VRAMFrequency": "Frecvența VRAM",
    "SensorData": "Date de la senzor",
    "CannotGetSensorData": "Nu s-au putut obține datele senzorului",
    "LoadingHardwareInfo": "Încărcare informații hardware…",
    "ScanTime": "Ultima scanare:",
    "Rescan": "Rescanare",
    "Screenshot": "Captură de ecran",
    "configCopyed": "Informațiile de configurare au fost copiate în clipboard.",
    "LegalRisks": "Riscuri legale potențiale detectate",
    "brandLegalRisks": "Afișarea mărcii poate implica riscuri legale potențiale",
    "professionalVersion": "Ediție Professională",
    "professionalWorkstationVersion": "Ediție Workstation Profesională",
    "familyEdition": "Ediția Home",
    "educationEdition": "Ediție educațională",
    "enterpriseEdition": "Ediție Enterprise",
    "flagshipEdition": "Ediție Premium",
    "familyPremiumEdition": "Ediție Premium pentru familie",
    "familyStandardEdition": "Versiune standard pentru familie",
    "primaryVersion": "Versiune de bază",
    "bit": "bit",
    "tempWall": "Zid de temperatură",
    "error": "Eroare",
    "screenshotSuccess": "Capturarea ecranului a fost salvată cu succes",
    "atLeastOneData": "Măcar o intrare de date trebuie păstrată",
    "atMostSixData": "Adăugați maxim 6 intrări de date",
    "screenNotActivated": "Neactivat"
  },
  "psc": {
    "processCoreAssign": "Alocarea nucleului procesului",
    "CoreAssign": "Alocare de nucleu：",
    "groupName": "Numele grupului:",
    "notGameProcess": "Procese non-joc",
    "unNamedProcess": "Grup fără nume",
    "Group2": "Grupă",
    "selectTheCore": "Selectați nucleul",
    "controls": "Operațiune",
    "tips": "Prompt",
    "search": "Caută",
    "shiftOut": "Înapoi",
    "ppValue": "Valoare PP",
    "ppDesc": "Valoarea PP reflectă consumul istoric de resurse hardware. Valori mai mari indică o utilizare mai intensă a resurselor hardware.",
    "littletips": "Sfat: Țineți procesul și trageți-l în grupul din stânga。",
    "warning1": "Selectarea thread-urilor de procesor între grupuri poate afecta performanța. Se recomandă utilizarea nucleelor din aceeași grupă.",
    "warning2": "Sunteți sigur că doriți să lăsați numele grupului gol?",
    "warning3": "Efectul alocării nucleului va fi anulat după ștergere. Sunteți sigur că doriți să ștergeți acest grup?",
    "allprocess": "Toate procesele",
    "pleaseCheckProcess": "Selectați procesul, vă rugăm",
    "dataSaveDesktop": "Datele au fost salvate pe desktop.",
    "createAGroup": "Creează grup",
    "delGroup": "Șterge grupul",
    "Group": "Grup",
    "editGroup": "Editează grupul",
    "groupinfo": "Informații despre grup",
    "moveOutGrouping": "Șterge din grup",
    "createANewGroup": "Creează un grup nou",
    "unallocatedCore": "Nucleu nealocat",
    "inactiveProcess": "Proces inactiv",
    "importGroupingScheme": "Importați profil de grupare",
    "derivedPacketScheme": "Exportă Configurația Grupului",
    "addNowProcess": "Adaugă procesul în curs de execuție",
    "displaySystemProcess": "Arată procese de sistem",
    "max64": "Alegerea maximă este de 64 de threaduri",
    "processName": "Numele procesului",
    "chooseCurProcess": "Selectați procesul curent",
    "selectNoProcess": "Nu a fost selectat niciun proces",
    "coreCount": "Nuclee",
    "threadCount": "Threads",
    "process": "Proces",
    "plzInputProcessName": "Introduceți numele procesului pentru a-l adăuga manual",
    "has_allocation": "Procese cu Scheme de Alocare a Firelor",
    "not_made": "Nu ați alocat niciun proces niciunui nucleu",
    "startUse": "Activează optimizarea",
    "stopUse": "Dezactivați optimizarea",
    "threadAllocation": "Alocare fir",
    "configProcess": "Configurare proces",
    "selectThread": "Selectați un fir",
    "hyperthreadingState": "Stare Hyper-Threading",
    "open": "Activat",
    "notYetUnlocked": "Dezactivat",
    "nonhyperthreading": "Fără Hyper-Threading",
    "intervalSelection": "Selectarea intervalului",
    "invertSelection": "Inversați selecția",
    "description": "Blocați procesele de joc pe nucleele CPU desemnate pentru operare, izolând inteligențial interfe-rențele programelor din fundal. Crește eficient plafonul FPS și stabilizează FPS-ul în joc! Reduce întârzierile și scăderile bruste de frame rate, eliberând întreaga performanță a procesorilor multi-core pentru a garanta un frame rate ridicat și constant în timpul jocului!",
    "importSuccess": "Import reușit",
    "importFailed": "Importul a eșuat"
  },
  "InGameMonitor": {
    "onoffingame": "Activează/Dezactivează monitorizarea în joc:",
    "InGameMonitor": "Monitorizare în joc",
    "CustomMode": "Mod personalizat",
    "Developing": "În dezvoltare...",
    "NewMonitor": "Adaugă element de monitorizare",
    "Data": "Parametri",
    "Des": "Notă",
    "Function": "Funcție",
    "Editor": "Editare",
    "Top": "Fixați în partea de sus",
    "Delete": "Șterge",
    "Use": "Folosi",
    "DragToSet": "După ce ați activat panoul, puteți trage pentru a seta",
    "MonitorItem": "Element de monitorizare",
    "addMonitorItem": "Adăugați element de monitorizare",
    "hide": "Ascunde",
    "show": "Afișare",
    "generalstyle": "Setări generale",
    "restoredefault": "Restabilirea setărilor implicite",
    "arrangement": "Aspect",
    "horizontal": "Orizontal",
    "vertical": "Vertical",
    "monitorposition": "Locație de monitorizare",
    "canquickselectposition": "Selectați rapid o locație pe harta din stânga。",
    "curposition": "Locația curentă:",
    "background": "Fundal",
    "backgroundcolor": "Culoare fundal:",
    "font": "Font",
    "fontStyle": "Stil de font",
    "fontsize": "Dimensiunea fontului：",
    "fontcolor": "Culoare font:",
    "style": "Stil:",
    "style2": "Stil",
    "performance": "Performanță",
    "refreshTime": "Timp de actualizare:",
    "goGeneralSetting": "Mergi la setările generale",
    "selectMonitorItem": "Selectați elementul de monitorizare",
    "selectedSensor": "Sensor selectat:",
    "showTitle": "Afișare titlu",
    "hideTitle": "Ascunde titlul",
    "showStyle": "Mod de afișare:",
    "remarkSize": "Dimensiune notă:",
    "remarkColor": "Culoarea notiței:",
    "parameterSize": "Dimensiunea parametrului :",
    "parameterColor": "Culoare Parametru :",
    "lineChart": "Grafic de linii",
    "lineColor": "Culoare linie:",
    "lineThickness": "Grosimea liniei：",
    "areaHeight": "Înălțimea regiunii:",
    "sort": "Sortare",
    "displacement": "Deplasare:",
    "up": "Mutare în sus",
    "down": "Mutați în jos",
    "bold": "Grotesc",
    "stroke": "Contur",
    "text": "Text",
    "textLine": "Text + Grafic liniar",
    "custom": "Personalizat",
    "upperLeft": "Parte stângă sus",
    "upper": "Mijlociu-Superior",
    "upperRight": "Sus la dreapta",
    "Left": "Centru Stânga",
    "middle": "Centru",
    "Right": "Centru drept",
    "lowerLeft": "Stânga jos",
    "lower": "Mediu-Jos",
    "lowerRight": "Jos în dreapta",
    "notSupport": "Dispozitivele periferice nu suportă afișarea/ascunderea prin click pe mouse",
    "notSupportRate": "Rata de revenire nu poate fi comutată prin click",
    "notFindSensor": "Senzorul nu a fost găsit. Faceți clic pentru a modifica。",
    "monitoring": "Monitorizare",
    "condition": "Condiție",
    "bigger": "Mai mare decât",
    "smaller": "Mai puțin decât",
    "biggerThan": "Limita a fost depăşită",
    "biggerThanthreshold": "Mai mare decât procentul de prag",
    "smallerThan": "Sub prag",
    "smallerThanthreshold": "Mai puțin decât procentajul de prag",
    "biggerPercent": "Scăderea procentuală a valorii actuale",
    "smallerPercent": "Procentajul creșterii valorii actuale",
    "replay": "Funcția de redare instantanee",
    "screenshot": "Funcție de captură de ecran",
    "text1": "Când valoarea senzorului",
    "text2": " și în",
    "text3": "Timp de așteptare",
    "text4": "Dacă nu apare o valoare mai mare în secundele specificate, se activează imediat",
    "text5": "După fiecare activare a replay-ului, actualizați pragul cu valoarea din momentul activării pentru a reduce activările frecvente",
    "text6": "Afișează pragul curent folosit pentru a declanșa redarea",
    "text7": "Afișare valori senzor",
    "text8": "Limita inițială depășită",
    "text9": "Sub pragul inițial",
    "text10": "Număr de prag inițial",
    "initThreshold": "Limită inițială",
    "curThreshold": "Pragul actual:",
    "curThreshold2": "Limita actuală",
    "resetCurThreshold": "Resetează pragul curent",
    "action": "Activează funcție",
    "times": "ori",
    "percentage": "Procent",
    "uninstallobs": "Modulul de înregistrare nu a fost descărcat",
    "install": "Descărcare",
    "performanceAndAudioMode": "Mod de compatibilitate performanță și audio",
    "isSaving": "Salvare în curs",
    "video_replay": "Reproducere imediată",
    "saved": "Salvat",
    "loadQualitysScheme": "Încarcă setări grafice",
    "notSet": "Nu este configurat",
    "mirrorEnable": "Filtrul este activat",
    "canBeTurnedOff": "Înapoi",
    "mirrorClosed": "Filtrul de joc este dezactivat",
    "closed": "Închis",
    "openMirror": "Activează filtru",
    "wonderfulScenes": "Momente remarcabile",
    "VulkanModeHaveProblem": "Modul Vulkan are probleme de compatibilitate",
    "suggestDxMode": "Se recomandă să treci în modul Dx",
    "functionNotSupported": "Funcția nu este suportată",
    "NotSupported": "Nesuportat",
    "gppManualRecording": "GamePP, înregistrare manuală",
    "perfRecordsHaveBeenSaved": "Datele de performanță au fost salvate",
    "redoClickF8": "Pentru a continua înregistrarea, apăsați din nou F8.",
    "startIngameMonitor": "Activarea funcției de monitorizare în joc",
    "inGameMarkSuccess": "Etichetarea în joc a fost realizată cu succes",
    "recordingFailed": "Înregistrarea a eșuat",
    "recordingHasNotDownload": "Funcția de înregistrare nu a fost descărcată",
    "hotkeyDetected": "Detectat conflict între taste rapide de funcție",
    "plzEditIt": "Vă rugăm să faceți modificările în program și apoi să-l utilizați",
    "onePercentLowFrame": "1% Cadru scăzut",
    "pointOnePercentLowFrame": "0.1% FPS scăzut",
    "frameGenerationTime": "Timp de generare a cadrului",
    "curTime": "Ora curentă",
    "runTime": "Durata execuției",
    "cpuTemp": "Temperatura CPU",
    "cpuUsage": "Utilizare CPU",
    "cpuFreq": "Frecvența CPU",
    "cpuPower": "Putere termică CPU",
    "gpuTemp": "Temperatura GPU",
    "gpuUsage": "Utilizare GPU",
    "gpuPower": "Putere termică GPU",
    "gpuFreq": "Frecvența GPU",
    "memUsage": "Utilizare memorie"
  },
  "LoginArea": {
    "login": "Autentificare",
    "loginOut": "Deconectați",
    "vipExpire": "Expirat",
    "remaining": "Rămas",
    "day": "Cer",
    "openVip": "Activează Membresia",
    "vipPrivileges": "Avantajele membrului",
    "rechargeRenewal": "Reîncărcare & Renovare",
    "Exclusivefilter": "Filtru",
    "configCloudSync": "Configurați sincronizarea în cloud",
    "comingSoon": "Curând disponibil"
  },
  "GameMirror": {
    "filterStatus": "Stare filtru",
    "filterPlan": "Preset filtre",
    "filterShortcut": "Filtru de scurtături",
    "openCloseFilter": "Activează/Dezactivează filtrul:",
    "effectDemo": "Demonstrație efect",
    "demoConfig": "Configurare demo",
    "AiFilter": "Efectele filtrului AI sunt supuse efectelor in-game",
    "AiFilterFAQ": "Probleme comune cu filtrele AI",
    "gamePPAiFilter": "Filtru AI GamePP",
    "gamePPAiFilterVip": "Filtru AI exclusivist pentru VIP GamePP, care ajustează dinamic parametrii filtrului în funcție de scenariile de joc pentru a optimiza efectele vizuale și a îmbunătăți experiența de joc.",
    "AiMingliangTips": "Lumină AI: Recomandat pentru utilizare când ecranul jocului este prea întunecat.",
    "AiBrightTips": "AI Vibrant: Recomandat pentru utilizare când grafica jocului pare prea întunecată.",
    "AiDarkTips": "AI Dimming: Recomandat pentru utilizare atunci când elementele vizuale ale jocului sunt prea vii",
    "AiBalanceTips": "Echilibru AI: Potrivit pentru majoritatea scenariilor de joc.",
    "AiTips": "Sfat: Filtrul AI se activează apăsând tasta de comandă rapidă în joc.",
    "AiFilterUse": "Vă rugăm să utilizați în joc",
    "AiFilterAdjust": "Configurarea tastelor rapide pentru filtrul AI",
    "Bright": "Strălucitor",
    "Soft": "Software",
    "Highlight": "Evidențiere",
    "Film": "Videoclip",
    "Benq": "BenQ",
    "AntiGlare": "Antireflexie",
    "HighSaturation": "Saturație ridicată",
    "Brightness": "Vivid",
    "Day": "Zi",
    "Night": "Noapte",
    "Nature": "Natural",
    "smooth": "Fina",
    "elegant": "Sever",
    "warm": "Ton cald",
    "clear": "Șterge",
    "sharp": "Nitiditate",
    "vivid": "Dinamic",
    "beauty": "Vivid",
    "highDefinition": "HD",
    "AiMingliang": "Strălucire AI",
    "AiBright": "AI Vivid",
    "AiDark": "AI Întuneca",
    "AiBalance": "Echilibru AI",
    "BrightTips": "Filtrul Vivid este potrivit pentru jocuri casual, de acțiune sau de aventură, îmbunătățind saturația culorilor pentru a face vizualizările jocului mai dinamice și captivante。",
    "liangTips": "Se recomandă utilizarea filtrelor atunci când imaginea jocului este prea întunecată。",
    "anTips": "Se recomandă utilizarea acestui filtru când ecranul jocului este prea întunecat。",
    "jianyiTips": "Se recomandă utilizarea filtrului când vizualizările jocului sunt prea vii。",
    "shiTips": "Filtrul este potrivit pentru majoritatea scenariilor de joc。",
    "shi2Tips": "Filtrul este potrivit pentru jocuri casual, de acțiune sau de aventură și mărește saturația culorilor pentru a face vizualizările jocului mai vii și captivante。",
    "ruiTips": "Culorile suave ale filtrului și efectele de lumină moi sunt ideale pentru a reprezenta scene visătoare, calde sau nostalgie。",
    "qingTips": "Ton deschis, contrast ridicat, detalii clare. Potrivit pentru scene vii cu o iluminare adecvată.",
    "xianTips": "Setări de contrast și strălucire mai mari asigură detalii clare în scenele întunecate fără distorsiune și o vizualizare confortabilă în scenele luminoase.",
    "dianTips": "Ameliorează moderat strălucirea și culorile ecranului pentru a obține o calitate vizuală cinematografică cât mai aproape posibil",
    "benTips": "Reduce efectele luminii albe pentru a preveni oboseala ochilor în medii de joc complet albe",
    "fangTips": "Optimizat pentru jocuri open-world și de aventură, îmbunătățește strălucirea și contrastul pentru imagini mai clare.",
    "jiaoTips": "Potrivit pentru jocuri de rol și simulare, tonuri echilibrate, realism vizual îmbunătățit",
    "jieTips": "Optimizat pentru jocuri cu povești bogate și nuanțe emoționale, îmbunătățind detaliile și blândețea pentru a obține vizualizări mai rafinate。",
    "jingTips": "Optimizat pentru jocuri de acțiune și competitive, îmbunătățește claritatea și contrastul pentru vizualizări mai clare.",
    "xiuTips": "Optimizat pentru vindecare și jocuri casual, accentuează tonurile călduroase și blânde, creând o atmosferă mai prietenoasă.",
    "qihuanTips": "Potrivit pentru scene cu elemente fantasy bogate și culori intense, care îmbunătățește saturația culorilor pentru a crea un impact vizual puternic",
    "shengTips": "Intăriți culorile și detaliile pentru a sublinia vitalitatea și realismul scenei。",
    "sheTips": "Optimizat pentru FPS, jocuri de puzzle sau aventură, îmbunătățește detaliile și contrastul pentru a crește realismul lumii de joc.",
    "she2Tips": "Potrivit pentru jocuri de foc, curse sau lupte, evidențiază detalii în definiție mare și performanță dinamică pentru a intensifica intensitatea și efectele vizuale ale experienței de joc.",
    "an2Tips": "Îmbunătățește claritatea scenelor în mediile întunecate, ideal pentru scenarii întunecate sau de noapte。",
    "wenTips": "Potrivit pentru jocuri artistice, de aventură sau relaxante, creează nuanțe subtile de culoare și efecte de lumină și umbră pentru a adăuga eleganță și căldură scenei。",
    "jing2Tips": "Potrivit pentru jocuri competitive, de ritm muzical sau de scenariu oraș de noapte, subliniind culori vii și efecte de lumină,",
    "jing3Tips": "Optimizat pentru jocuri competitive, de acțiune sau fantasy, îmbunătățește contrastul culorilor pentru a face vizualizările mai vii și dinamice。",
    "onlyVipCanUse": "Acest filtru este disponibil doar pentru utilizatorii VIP"
  },
  "GameRebound": {
    "noGame": "Nu există înregistrări de joc",
    "noGameRecord": "Încă nu există înregistrări de joc! Începeți acum o sesiune.",
    "gameDuration": "Durata jocului astăzi:",
    "gameElectricity": "Consumul zilnic de energie electrică",
    "degree": "Grad",
    "gameCo2": "Emisii de CO₂ azi :",
    "gram": "Tastă",
    "manualRecord": "Înregistrare manuală",
    "recordDuration": "Durata înregistrării:",
    "details": "Detalii",
    "average": "Medie",
    "minimum": "Minim",
    "maximum": "Maxim",
    "occupancyRate": "Utilizare",
    "voltage": "Tensiune",
    "powerConsumption": "Consumul de energie",
    "start": "Start：",
    "end": "Sfârșit",
    "Gametime": "Durata jocului :",
    "Compactdata": "Optimizare date",
    "FullData": "Date complete",
    "PerformanceAnalysis": "Analiza performanței",
    "PerformanceAnalysis2": "Raport eveniment",
    "HardwareStatus": "Stare hardware",
    "totalPower": "Consum total de energie",
    "TotalEmissions": "Emisie totală",
    "PSS": "Notă: Datele de sub grafic reprezintă valori medii.",
    "FrameGenerationTime": "Timp de generare a frame-ului",
    "GameResolution": "Rezoluție joc",
    "FrameGenerationTimeTips": "Această valoare este excepțional de mare și a fost exclusă din statistică",
    "FrameGenerationTimeTips2": "Acest punct de date este anormal de scăzut și, prin urmare, nu este inclus în statistică",
    "noData": "Nu există date disponibile în acest moment",
    "ProcessorOccupancy": "Utilizare CPU",
    "ProcessorFrequency": "Frecvența procesorului",
    "ProcessorTemperature": "Temperatura procesorului",
    "ProcessorHeatPower": "Putere termică a procesorului",
    "GraphicsCardOccupancy": "Utilizare GPU D3D",
    "GraphicsCardOccupancyTotal": "Utilizare totală GPU",
    "GraphicsCardFrequency": "Frecvență GPU",
    "GraphicsCardTemperature": "Temperatura GPU",
    "GraphicsCardCoreTemperature": "Temperatura punctului fierbinte al nucleului GPU",
    "GraphicsCardHeatPower": "Putere termică GPU",
    "GraphicsCardMemoryTemperature": "Temperatura memoriei GPU",
    "MemoryOccupancy": "Utilizare memorie",
    "MemoryTemperature": "Temperatura memoriei",
    "MemoryPageFaults": "Interrupție de Paginare a Memoriei",
    "Duration": "Durată",
    "Time": "Timp",
    "StartStatistics": "Porniți statistica",
    "Mark": "Etichetă",
    "EndStatistics": "Încheie statisticile",
    "LineChart": "Grafic cu linii",
    "AddPointInGame_m1": "Apăsați în interiorul jocului",
    "AddPointInGame_m2": "Un punct de marcare poate fi adăugat",
    "LeftMouse": "Click stânga pentru a comuta între afișare/ascundere, click dreapta pentru a schimba culoarea",
    "DeleteThisLine": "Ștergeți această polilinie",
    "AddCurve": "Adăugați curbă",
    "AllCurvesAreHidden": "Toate curbele sunt ascunse",
    "ThereAreSamplingData": "Date de eșantion total:",
    "Items": "Intrare",
    "StatisticsData": "Statistici",
    "electricity": "Consumul de energie",
    "carbonEmission": "Emisii de carbon",
    "carbonEmissionTips": "Emisii de dioxid de carbon (kg) = Consumul de electricitate (kWh) × 0,785",
    "D3D": "Utilizare D3D:",
    "TOTAL": "Timp total de utilizare:",
    "Process": "Proces:",
    "L3Cache": "Cache L3:",
    "OriginalFrequency": "Frecvență originală:",
    "MaximumBoostFrequency": "Turbo Boost Maxim :",
    "DriverVersion": "Versiunea driverului :",
    "GraphicsCardMemoryBrand": "Marcă VRAM:",
    "Bitwidth": "Lățimea magistralei",
    "System": "Sistem:",
    "Screen": "Ecran",
    "Interface": "Interfață:",
    "Channel": "Canal:",
    "Timing": "Secvență: ",
    "Capacity": "Capacitate:",
    "Generation": "algebră",
    "AddPoint_m1": "Apăsați în joc",
    "AddPoint_m2": "Adaugă punct de marcator",
    "Hidden": "Ascuns",
    "Totalsampling": "Date de eșantionare totale :",
    "edition": "Versiunea dispozitivului:",
    "MainHardDisk": "Discuri dur principal",
    "SetAsStartTime": "Setați ca oră de începere",
    "SetAsEndTime": "Setați ca oră de încheiere",
    "WindowWillBe": "Fereastra de statistică a performanței va fi situată în",
    "After": "După închidere",
    "NoLongerPopUpThisGame": "Acest joc nu va mai afișa pop-up-uri",
    "HideTemperatureReason": "Ascunde motivul temperaturii",
    "HideTemperatureReason2": "Ascunde raportul evenimentelor",
    "HideOtherReason": "Ascunde alte motive",
    "CPUanalysis": "Analiza performanței CPU",
    "TemperatureCause": "Cauza temperaturii",
    "tempSensorEvent": "Evenimentul senzorului de temperatură",
    "NoTemperatureLimitation": "Nu s-a detectat o reducere a performanței CPU cauzată de temperatură. Sistemul tău de răcire este în totalitate capabil să îndeplinească cerințele acestui joc.",
    "NoTemperatureLimitation2": "Nu au fost detectate evenimente de la senzorul de temperatură, sistemul tău de răcire poate gestiona perfect cerințele acestui joc.",
    "performanceis": "Selectat",
    "Inside": "Internă,",
    "TheStatisticsTimeOf": "Perioada statistică îndeplinește condițiile relevante de declanșare. Cauza de declanșare cea mai frecventă este",
    "limited": "Procentul totalului de timp datorat limitărilor de performanță induse de temperatură",
    "SpecificReasons": "Cauze specifice și proporția lor în cauzele legate de temperatură:",
    "OptimizationSuggestion": "Sugestii de optimizare :",
    "CPUtemperature": "Temperatura CPU este prea ridicată. Vă rugăm să verificați/îmbunătățiți mediul de răcire al CPU。",
    "CPUoverheat": "Supraîncălzirea CPU este cauzată de alimentarea de la placa de bază. Verificați setările legate de placa de bază sau îmbunătățiți mediul de răcire。",
    "OtherReasons": "Alte motive",
    "NoPowerSupplyLimitation": "Performanța CPU nu este limitată de alimentare/consommare. Setările de consum de energie din BIOS sunt suficiente pentru cerințele acestui joc。",
    "PowerSupplyLimitation": "Din cauza limitărilor de alimentare electrică/consum de energie",
    "SpecificReasonsInOtherReasons": "Motiv specific și proporția sa printre alte motive:",
    "PleaseCheckTheMainboard": "Verificați starea de livrare a energiei de pe placa de bază sau ajustați setările de energie din BIOS pentru a rezolva limitările de performanță ale CPU cauzate de alți factori",
    "CPUcoretemperature": "Temperatura nucleului a atins Tj,Max și este limitată",
    "CPUCriticalTemperature": "Temperatura CPU critică",
    "CPUCircuitTemperature": "CPU Package/Ring Bus limitat la atingerea Tj,Max",
    "CPUCircuitCriticalTemperature": "Pachetul CPU / magistrala circulară a atins temperatura critică",
    "CPUtemperatureoverheating": "A fost detectată supraincalzirea CPU-ului, care va declanșa o reducere automată a frecvenței pentru a reduce temperaturile și a preveni defecțiunile hardware.",
    "CPUoverheatingtriggered": "CPU-ul va activa mecanismul de răcire și va ajusta tensiunea/frecvența pentru a reduce consumul de energie și temperatura.",
    "CPUPowerSupplyOverheating": "CPU-ul este limitat din cauza supraincalzirii severe a sursei de alimentare a plăcii de bază",
    "CPUPowerSupplyLimitation": "Performanța CPU-ului este limitată din cauza supracălzirii sursei de alimentare a plăcii de bază",
    "CPUMaximumPowerLimitation": "Nucleul este limitat de limita maximă de consum de energie",
    "CPUCircuitPowerLimitation": "Pachetul CPU/Bus Ring a atins limita de putere",
    "CPUElectricalDesignLimitation": "Activați restricțiile de design electric (inclusiv zidul de curent ICCmax, zidul de putere maximă PL4, limitarea tensiunii SVID etc.)",
    "CPULongTermPowerLimitation": "Limita de putere pe termen lung a CPU-ului a fost atinsă",
    "CPULongTermPowerinstantaneous": "Consumul instantaneu de energie al CPU a atins limita",
    "CPUPowerLimitation": "Mecanism de degradare a frecvenței Turbo CPU, în mod obișnuit restricționat de BIOS sau un software specific",
    "CPUPowerWallLimitation": "Limită de putere CPU",
    "CPUcurrentwalllimit": "Limita actuală de perete a CPU-ului",
    "AiAgent": "Agent GamePP (Agent AI)",
    "AgentDesc": "Întoarce-te la pagina principală",
    "fnBeta": "Această funcție se află în prezent în faza de testare invitată. Contul GamePP nu a primit încă accesul la testare。",
    "getAIReport": "Obține raport AI",
    "waitingAi": "Așteptați finalizarea generării raportului",
    "no15mins": "Durata jocului este mai mică de 15 minute, nu este disponibil un raport AI valid。",
    "timeout": "Timpul de așteptare a cererii serverului a expirat",
    "agentId": "ID agent:",
    "reDo": "Generați raportul din nou",
    "text2": "Agent GamePP: Raport de analiză AI online, următorul conținut a fost generat de AI și este doar pentru referință.",
    "amdAiagentTitle": "GamePP Agenție: Raportul de analiză AMD Ryzen AI, conținutul de mai jos a fost generat de AI și este doar pentru referință.",
    "noCurData": "Nu există date disponibile în prezent",
    "dataScreening": "Filtrarea datelor",
    "dataScreeningDescription": "Această funcție este concepută pentru a exclude statisticile de date din perioadele ineficiente de joc, cum ar fi încărcarea hărții sau timpul de așteptare în lobby. 0 indică faptul că nu se va face nicio excludere.",
    "excessivelyHighParameter": "Parametri prea mari",
    "tooLowParameter": "Parametri prea scăzi",
    "theMaximumValueIs": "Valoarea maximă este",
    "theMinimumValueIs": "Valoarea minimă este",
    "exclude": "Exclude",
    "dataStatisticsAtThatTime": "Statistici de date în acest moment",
    "itHasBeenGenerated": "Generare finalizată,",
    "clickToView": "Click pentru a vedea",
    "onlineAnalysis": "Analiză online",
    "amdRyzenAILocalAnalysis": "AMD Ryzen AI Analiză locală",
    "useTerms": "Condiții de utilizare:",
    "term1": "1. Procesor Ryzen AI Max sau Procesor Ryzen Al 300 Seriei",
    "term2": "2.Versiunea driverului AMD NPU",
    "term3": "3. Activați grafica integrată",
    "conformsTo": "Conform",
    "notInLineWith": "Incompatibil",
    "theVersionIsTooLow": "Versiunea este prea veche",
    "canNotUseAmdNpu": "Configurația ta nu îndeplinește cerințele. Analiza entităților AMD NPU nu poate fi utilizată。",
    "unusable": "Nu este disponibil",
    "downloadTheFile": "Descarcă fișier",
    "downloadSource": "Sursa de descărcare:",
    "fileSize": "Dimensiune fișier: aprox. 8,34 GB",
    "cancelDownload": "Anulează descărcarea",
    "filePath": "Locația fișierului",
    "generateAReport": "Generează raport",
    "fileMissing": "Fișierul lipsește. Este necesară re-instalarea.",
    "downloading": "Descărcare în curs...",
    "theModelConfigurationLoadingFailed": "Nu a fost posibilă încărcarea configurației modelului",
    "theModelDirectoryDoesNotExist": "Directorul modelului nu există",
    "thereIsAMistakeInReasoning": "Eroare de deducție",
    "theInputExceedsTheModelLimit": "Intrarea depășește limita modelului",
    "selectModelNotSupport": "Modelul de descărcare selectat nu este suportat",
    "delDirFail": "Eroare la ștergerea directorului de model existent",
    "failedCreateModelDir": "Crearea directorului model a eșuat",
    "modelNotBeenFullyDownload": "Modelul nu a fost descărcat complet",
    "agentIsThinking": "Agentul Jiajia gândește",
    "reasoningModelFile": "Fișier de model pentru inferență",
    "modelReasoningTool": "Instrument de inferență a modelului"
  },
  "SelectSensor": {
    "DefaultSensor": "Senzor implicit",
    "Change": "Schimbă",
    "FanSpeed": "Viteză ventilator",
    "MainGraphicsCard": "GPU principal",
    "SetAsMainGraphicsCard": "Setați ca GPU principal",
    "GPUTemperature": "Temperatura GPU",
    "GPUHeatPower": "Putere termică GPU",
    "GPUTemperatureD3D": "Utilizare GPU D3D",
    "GPUTemperatureTOTAL": "Utilizare totală GPU",
    "GPUTemperatureCore": "Temperatura punctului fierbinte al nucleului GPU",
    "MotherboardTemperature": "Temperatura plăcii de bază",
    "MyAttention": "Favoritele mele",
    "All": "Tot",
    "Unit": "Unitate:",
    "NoAttention": "Senzori neurmăriți",
    "AttentionSensor": "Sensoare monitorizate (Beta)",
    "GoToAttention": "Treceți în modul Focus",
    "CancelAttention": "Anulează abonamentul",
    "noThisSensor": "Fără senzor",
    "deviceAbout": "Periferice legate",
    "deviceBattery": "Baterie periferică",
    "testFunction": "Funcție de test",
    "mouseEventRate": "Frecvența de sondaj",
    "relatedWithinTheGame": "Legat de joc",
    "winAbout": "Sistem",
    "trackDevicesBattery": "Urmăriți nivelul bateriei dispozitivelor periferice",
    "ingameRealtimeMouseRate": "Frecvența de polling a mouse-ului utilizată în timp real în timpul jocurilor",
    "notfoundDevice": "Nu a fost găsit niciun dispozitiv compatibil",
    "deviceBatteryNeedMythcool": "Listă de dispozitive cu suport pentru afișarea bateriei: (necesită utilizarea Myth.Cool)",
    "vkm1mouse": "Mouse Valkyrie M1",
    "vkm2mouse": "Valkyrie M2 Mouse",
    "vk99keyboard": "Valkyrie 99 Tastatură cu Ax Magnetic",
    "logitechProWireless": "Logitech Pro Wireless",
    "logitechProXSUPERLIGHT": "Logitech PRO X SUPERLIGHT",
    "logitechProXSUPERLIGHT2": "Logitech PRO",
    "logitechPro2LIGHTSPEED": "Logitech PRO 2 LIGHTSPEED",
    "steelSeriesArctisGameBuds": "SteelSeries Arctis GameBuds",
    "steelSeriesArctis7PPlus": "SteelSeries Arctis 7P Plus",
    "razerV3": "Razer Valkyrie V3 Ediție Profesională",
    "razerV2": "Razer Viper V2 Ediție Profesională",
    "wireless": "Fără fir",
    "logitechNeedGhub": "Când Logitech nu poate obține modelul dispozitivului, este necesar să descărcați GHUB",
    "chargingInProgress": "Încărcare",
    "inHibernation": "În modul de suspensie"
  },
  "video": {
    "videoRecord": "Înregistrare video",
    "recordVideo": "Videoclip înregistrat",
    "scheme": "Profil",
    "suggestScheme": "Plan recomandat",
    "text1": "Cu această configurație, dimensiunea unui videoclip de 1 minut este aproximativ",
    "text2": "Această funcție utilizează resurse suplimentare de sistem",
    "low": "Scăzut",
    "mid": "Acasă",
    "high": "Înalt",
    "1080p": "Nativ",
    "RecordingFPS": "Înregistrează FPS",
    "bitRate": "Bitrate",
    "videoResolution": "Rezoluție video",
    "startStopRecord": "Începe/Opriți înregistrarea",
    "instantReplay": "Repetare imediată",
    "instantReplayTime": "Durata Redării Instantanee",
    "showIngame": "Deschide panoul de control din joc",
    "CaptureMode": "Metodă de captură",
    "gameWindow": "Fereastra jocului",
    "desktopWindow": "Fereastră de desktop",
    "fileSavePath": "Calea de stocare a fișierelor",
    "selectVideoSavePath": "Selectați calea de salvare a înregistrării",
    "diskFreeSpace": "Spațiu liber pe discul rigid:",
    "edit": "Modificați",
    "open": "Deschide",
    "displayMouse": "Afișează cursorul de la mouse",
    "recordMicrophone": "Înregistrare microfon",
    "gameGraphics": "Afișare joc original"
  },
  "Setting": {
    "common": "General",
    "personal": "Personalizare",
    "messageNotification": "Notificări",
    "sensorReading": "Valori senzor",
    "OLEDscreen": "Protejării împotriva burn-in-ului OLED",
    "performanceStatistics": "Statistici de performanță",
    "shortcut": "Scurtătură de la tastatură",
    "ingameSetting": "Salvează setările jocului",
    "other": "Alt",
    "otherSettings": "Alte setări",
    "GeneralSetting": "Setări generale",
    "softwareVersion": "Versiunea software",
    "checkForUpdates": "Verifică actualizările",
    "updateNow": "Actualizează acum",
    "currentVersion": "Versiunea curentă",
    "latestVersion": "Ultima versiune",
    "isLatestVersion": "Versiunea curentă este deja cea mai recentă versiune.",
    "functionModuleUpdate": "Actualizare modul funcțional",
    "alwaysUpdateModules": "Asigurați-vă că toate modulele funcționale instalate sunt actualizate",
    "lang": "Limba",
    "bootstrap": "Pornire automată",
    "powerOn_m1": "Pornire",
    "powerOn_m2": "Pornire automată după secunde",
    "defaultDelay": "Implicit: 40 de secunde",
    "followSystemScale": "Urmăriți scalarea sistemului",
    "privacySettings": "Setări de confidențialitate",
    "JoinGamePPPlan": "Alăturați-vă programului de îmbunătățire a experienței utilizatorului GamePP",
    "personalizedSetting": "Personalizare",
    "restoreDefault": "Restați la valorile implicite",
    "color": "Culoare",
    "picture": "Imagine",
    "video": "Videoclip",
    "browse": "Răsfoiește",
    "clear": "Șterge",
    "mp4VideoOrPNGImagesCanBeUploaded": "Încărcați videoclipuri MP4 sau imagini PNG",
    "transparency": "Transparență",
    "backgroundColor": "Culoare fundal",
    "textFont": "Fontul textului principal",
    "message": "Mesaj",
    "enableInGameNotifications": "Activează notificările în joc",
    "messagePosition": "Poziția afișării în joc",
    "leftTop": "Colțul stâng de sus",
    "leftCenter": "Centru stânga",
    "leftBottom": "Colțul stâng inferior",
    "rightTop": "colțul din dreapta sus",
    "rightCenter": "Dreapta Centru",
    "rightBottom": "colțul din dreapta jos",
    "noticeContent": "Conținutul notificării",
    "gameInjection": "Injecție joc",
    "ingameShow": "Arată în joc",
    "inGameMonitoring": "Monitorizare în joc",
    "gameFilter": "Filtru de jocuri",
    "start": "Pornește",
    "endMarkStatistics": "Statistici pentru marcator final",
    "readHwinfoFail": "HWINFO Nu s-a putut obține informația hardware!",
    "dataSaveDesktop": "Datele au fost salvate în clipboard și în fișierul de pe desktop.",
    "TheSensorCacheCleared": "Datele cache-ului senzorului au fost șterse",
    "defaultSensor": "Senzor implicit",
    "setSensor": "Selectați senzorul",
    "refreshTime": "Ora actualizării datelor",
    "recommend": "Implicit",
    "sensorMsg": "Cu cât intervalul de timp este mai scurt, cu atât consumul de performanță este mai mare. Vă rugăm să selectați cu atenție。",
    "exportData": "Exportați datele",
    "exportHwData": "Exportați datele informațiilor hardware",
    "sensorError": "Citire senzor anormală",
    "clearCache": "Ștergeți cache-ul",
    "littleTips": "Sfat: Raportul de performanță va fi generat 2 minute după pornirea jocului",
    "disableAutoShow": "Dezactivați deschiderea automată a ferestrei de statistică a performanței",
    "AutoClosePopUpWindow_m1": "Fereastra de statistică a performanței se va închide automat după timpul specificat:",
    "AutoClosePopUpWindow_m2": "secunde",
    "abnormalShutdownReport": "Raport de închidere anormală",
    "showWeaAndAddress": "Afișează informații despre vreme și locație",
    "autoScreenShots": "Captură automată a ecranului de joc atunci când este marcat",
    "keepRecent": "Numărul de înregistrări recente de păstrat:",
    "noLimit": "Nelimitat",
    "enableInGameSettingsSaving": "Activați salvarea setărilor în joc",
    "debugMode": "Mod de depanare",
    "enableDisableDebugMode": "Activează/Dezactivează Modul de Depanare",
    "audioCompatibilityMode": "Mod de compatibilitate audio",
    "quickClose": "Închidere rapidă",
    "closeTheGameQuickly": "Închideți rapid procesul de joc",
    "cancel": "Anulează",
    "confirm": "Confirmare",
    "MoveInterval_m1": "Monitorizarea desktopului și a jocului se va muta ușor:",
    "MoveInterval_m2": "minute",
    "text3": "După ieșirea din joc, fereastra de raport a performanței nu va apărea, doar fiind păstrate înregistrările istorice",
    "text5": "Un raport va fi generat automat după o închidere neașteptată. Activarea acestei funcții va consuma resurse suplimentare de sistem。",
    "text6": "Utilizarea funcțiilor de scurtături poate intra în conflict cu alte scurtături ale jocului. Vă rugăm să le setați cu atenție。",
    "text7": "Setați shortcut-ul de la tastatură la 'Nimic', vă rugăm să folosiți tasta Backspace",
    "text8": "Păstrați filtrele, monitorizarea în joc și alte stări funcționale în timpul jocului în funcție de numele proceselor",
    "text9": "Activarea va înregistra continuu datele de rulare; dezactivarea șterge fișierele de jurnal (Se recomandă să rămână dezactivată)",
    "text10": "După activare, senzorii de pe placa de bază vor deveni inaccesibili pentru a rezolva problemele audio cauzate de GamePP。",
    "text11": "Apăsați tasta Alt+F4 de două ori consecutiv pentru a ieși rapid din jocul curent",
    "text12": "Dorești să continui? Această modalitate necesită repornirea GamePP.",
    "openMainUI": "Arată aplicația",
    "setting": "Setări",
    "feedback": "Feedback",
    "help": "Ajutor",
    "sensorReadingSetting": "Setări pentru citirea senzorului",
    "searchlanguage": "Limba de căutare"
  },
  "GamePlusOne": {
    "year": "An",
    "month": "Lună",
    "day": "zi",
    "success": "Succes",
    "fail": "Eșuat",
    "will": "Curent",
    "missedGame": "Jocul nu a fost retras la timp",
    "text1": "Suma, aprox. ￥",
    "text2": "Total Jocuri Reclamate",
    "text3": "Versiune",
    "gamevalue": "Valoare joc",
    "gamevalue1": "Cerere",
    "total": "Total reclamat",
    "text4": "Jocuri, total salvate",
    "text6": "Produs, Valoare",
    "Platformaccountmanagement": "Gestionarea contului de pe platformă",
    "Missed1": "Nereclamat",
    "Received2": "（Primire cu succes）",
    "Receivedsoon2": "(Disponibil în prezent)",
    "Receivedsoon": "Disponibil",
    "Missed": "Colectare lipsă",
    "Received": "Reclamat cu succes",
    "Getaccount": "Revendică contul",
    "Worth": "Valoare",
    "Auto": "Automat",
    "Manual": "Manual",
    "Pleasechoose": "Vă rugăm să selectați un joc",
    "Receive": "Cerere acum",
    "Selected": "Selectat",
    "text5": "Jocuri",
    "Automatic": "Revendicare automată...",
    "Collecting": "Cerință în curs...",
    "ReceiveTimes": "Numărul lunar de cereri",
    "Thefirst": "#",
    "Week": "Săptămână",
    "weekstotal": "Total 53 săptămâni",
    "Return": "Pagina principală",
    "Solutionto": "Soluție pentru eșecul legăturii contului",
    "accounts": "Numărul de conturi legate",
    "Addaccount": "Adaugă cont",
    "Clearcache": "Șterge cache-ul",
    "Bindtime": "Timpul de legare",
    "Status": "Stare",
    "Normal": "Mod Normal",
    "Invalid": "Nevalid",
    "text7": "Jocuri, total salvate pentru tine",
    "Yuan": "Yuan",
    "untie": "Dezactivați",
    "disable": "Dezactivați",
    "enable": "Activează",
    "gamePlatform": "Platformă pentru jocuri",
    "goStorePage": "Mergi la pagina magazinului",
    "receiveEnd": "După termenul limită",
    "loginPlatformAccount": "Contul platformei logat",
    "waitReceive": "Cerere în așteptare",
    "receiveSuccess": "Succes",
    "accountInvalid": "Cont expirat",
    "alreadyOwn": "Deja deținut",
    "networkError": "Anomalia rețelei",
    "noGame": "Niciun Game Core",
    "manualReceiveInterrupt": "Întrerupere Manuală a Obținerii",
    "receiving": "Reclamație în desfășurare",
    "agree": "Sunt de acord să ader la programul de preluare gratuită GamePP.",
    "again": "Revendicați din nou"
  },
  "shutdownTimer": {
    "timedShutdown": "Dezactivare planificată",
    "currentTime": "Ora curentă:",
    "setCountdown": "Setați numărătoarea inversă",
    "shutdownInSeconds": "Închidere în X secunde",
    "shutdownIn": "După deconectare",
    "goingToBe": "va fi",
    "executionPlan": "Plan de execuție",
    "startTheClock": "Pornește cronometrul",
    "stopTheClock": "Anulează planul",
    "isShuttingDown": "Executarea planului de închidere programat:",
    "noplan": "Nu există un plan de oprire actual",
    "hour": "Ora",
    "min": "Minut",
    "sec": "Secundă",
    "ms": "Milisecundă",
    "year": "An",
    "month": "Lună",
    "day": "Zi",
    "hours": "Oră"
  },
  "screenshotpage": {
    "screenshot": "Captură de ecran",
    "screenshotFormat": "Proiectat special pentru capturarea ecranelor de joc, susține salvarea în formatele JPG/PNG/BMP, permite capturarea rapidă a ecranelor de joc și garantează o ieșire fără pierderi la rezoluție ridicată",
    "Turnon": "Activați capturarea automată a ecranului, fiecare",
    "seconds": "Secundă",
    "takeScreenshot": "Efectuați o captură de ecran automată",
    "screenshotSettings": "Această setare este nevalidă atunci când este activată în joc",
    "saveGameFilterAndMonitoring": "Salvează efectele 'Game Filter' și ale 'Monitorizării în Joc' în capturarea ecranului",
    "disableScreenshotSound": "Dezactivați notificarea sonoră pentru capturarea ecranului",
    "imageFormat": "Format imagine",
    "recommended": "Recomandă",
    "viewingdetails": "Păstrează detaliile calității imaginii, dimensiune moderată, potrivit pentru vizualizarea detaliilor",
    "saveSpace": "Calitatea imaginii reglabilă, dimensiunea minimă a fișierului, economie de spațiu",
    "ultraQuality": "Vizuale ultra clare, necomprimate, cu dimensiune mare a fișierului. Recomandat pentru jucătorii care prioriteză salvările de joc de înaltă calitate.",
    "fileSavePath": "Calea de salvare a fișierului",
    "hardDiskSpace": "Spațiu pe disc disponibil:",
    "minutes": "Minut"
  },
  "DesktopMonitoring": {
    "desktopMonitoring": "Monitorizare Desktop",
    "SomeSensors": "Recomandăm câteva senzori pentru monitorizare. Poți șterge sau adăuga",
    "AddComponent": "Adăugați un nou component",
    "Type": "Tip",
    "Remarks": "Notă",
    "AssociatedSensor": "Asociați un senzor",
    "Operation": "Operațiune",
    "Return": "Înapoi",
    "TimeSelection": "Alegerea timpului:",
    "Format": "Format:",
    "Rule": "Regulă：",
    "Coordinate": "Coordonate:",
    "CustomTextContent": "Conținut de text personalizat:",
    "SystemTime": "Ora sistemului",
    "China": "China",
    "Britain": "Marele Britanie",
    "America": "Statele Unite",
    "Russia": "Rusia",
    "France": "Franța",
    "DateAndTime": "Dată și oră",
    "Time": "Timp",
    "Date": "Dată",
    "Week": "Ziua săptămânii",
    "DateAndTimeAndWeek": "Dată+Ora+Ziua săptămânii",
    "TimeAndWeek": "Ora+Ziua săptămânii",
    "Hour12": "Formatul de 12 ore",
    "Hour24": "format de 24 ore",
    "SelectSensor": "Selectați senzorul:",
    "AssociatedSensor1": "Conectați senzorul:",
    "SensorUnit": "Unitatea senzorului：",
    "Second": "Secundă:",
    "Corner": "Colțuri rotunjite:",
    "BackgroundColor": "Culoare de fundal:",
    "ProgressColor": "Culoare de progres:",
    "Font": "Font：",
    "SelectFont": "Selectați fontul",
    "FontSize": "Dimensiunea fontului:",
    "Color": "Culoare：",
    "Style": "Stil:",
    "Bold": "Îngroșat",
    "Italic": "Cursiv",
    "Shadow": "Umbra",
    "ShadowPosition": "Poziție Umbră：",
    "ShadowEffect": "Efecte de umbră：",
    "Blur": "Încețoșare",
    "ShadowColor": "Culoare umbra：",
    "SelectFromLocalFiles": "Selectați din fișierele locale:",
    "UploadImageVideo": "Încarcă imagini/video",
    "UploadSVGFile": "Încărcați un fișier SVG",
    "Width": "Lățime:",
    "Height": "Înalt: ",
    "Effect": "Efect:",
    "Rotation": "Rotire:",
    "WhenTheSensorValue": "Valoarea senzorului este mai mare decât",
    "conditions": "Când condiția nu este îndeplinită (fără rotire când valoarea senzorului este 0)",
    "Clockwise": "În sensul acelor de ceas",
    "Counterclockwise": "În sens invers acelor de ceas",
    "QuickRotation": "Rotire rapidă",
    "SlowRotation": "Rotire lentă",
    "StopRotation": "Oprește rotirea",
    "StrokeColor": "Culoare de contur：",
    "Path": "Cale",
    "Color1": "Culoare",
    "ChangeColor": "Schimbă culoarea",
    "When": "Când",
    "SensorValue": "Valoarea senzorului este mai mare sau egală cu",
    "SensorValue1": "Valoarea senzorului este mai mică sau egală cu",
    "SensorValue2": "Valoarea senzorului este egală",
    "MonitoringSettings": "Setări pentru monitorizare",
    "RestoreDefault": "Restabilirea setărilor implicite",
    "Monitor": "Monitor",
    "AreaSize": "Dimensiunea zonei",
    "Background": "Fundal",
    "ImageVideo": "Imagini/Videoclipuri",
    "PureColor": "Culoare solidă",
    "Select": "Selectați",
    "ImageVideoDisplayMode": "Mod de afișare imagini/video",
    "Transparency": "Transparență",
    "DisplayPosition": "Afișează poziția",
    "Stretch": "Extindere",
    "Fill": "Umplere",
    "Adapt": "A adapta",
    "SelectThePosition": "Faceți clic pe celula pentru a selecta rapid poziția",
    "CurrentPosition": "Locația curentă:",
    "DragLock": "Blocare la Trăgerea",
    "LockMonitoringPosition": "Poziția monitorului este blocată (După blocare, monitorul nu poate fi mutat)",
    "Unlockinterior": "Permite glisarea elementelor interne",
    "Font1": "Font",
    "GameSettings": "Setări joc",
    "CloseDesktopMonitor": "Dezactivați automat monitorizarea desktopului când jocul este în execuție.",
    "OLED": "Protecție OLED Burn-in",
    "Display": "Afișează",
    "PleaseEnterContent": "Introduceți conținutul",
    "NextStep": "Următorul",
    "Add": "Adăugare",
    "StylesForYou": "Am recomandat câteva stiluri de monitorizare. Le puteți selecta și aplica. În viitor vor fi adăugate mai multe stiluri.",
    "EditPlan": "Editează profilul",
    "MonitoringStylePlan": "Schema de stil de monitorizare",
    "AddDesktopMonitoring": "Adăugare monitorizare desktop",
    "TextLabel": "Etichetă de text",
    "ImageVideo1": "Imagini, Vídeos",
    "SensorGraphics": "Grafică senzor",
    "SensorData": "Date de la senzor",
    "CustomText": "Text personalizat",
    "DateTime": "Dată și oră",
    "Image": "Imagine",
    "Video": "Videoclip",
    "SVG": "SVG",
    "ProgressBar": "Bară de progres",
    "Graphics": "Grafică",
    "UploadImage": "Încarcă imagine",
    "UploadVideo": "Încărcați videoclipul",
    "RealTimeMonitoring": "Monitorizare în timp real a temperaturii și utilizării CPU/GPU, personalizare layout prin tragere și plasare, setări stil personalizate – dominați performanța și estetica desktopului",
    "Chart": "Grafic",
    "Zigzagcolor": "Culoarea liniei (Început)",
    "Zigzagcolor1": "Culoare linie (Punct final)",
    "Zigzagcolor2": "Culoarea zonei din graficul cu linii (Început)",
    "Zigzagcolor3": "Culoarea zonei din graficul liniar (sfârșit)",
    "CustomMonitoring": "Monitorizare personalizată"
  }
}
//messageEnd 
 export default ro 