<script setup lang="ts">
import {hardware} from "@/modules/Game_Home/stores";
import ChartHW from "@/components/echarts/ChartHW.vue";
import {findValueByKeyAndType, SensorAddAverageData, toPercent} from "@/uitls/sensor";
import {defineProps,ref,onMounted,onBeforeUnmount,watch,computed} from "vue";
const $store = hardware()
const props = defineProps({
  changeMenuInfo: {
    type: Function,
    required: true
  }
})
let loading = ref(true)
let loading_count = 0
const activeCollapse_gpu = ref(["1"])
let recordPECoreTimer = null;
let Timer2:NodeJS.Timeout;
const gpu_temp_arr = ref<Array<number>>([])
const gpu_temp_arr1 = ref<Array<number>>([])
const gpu_temp_arr2 = ref<Array<number>>([])
const gpu_memory_usage_arr = ref<Array<number>>([])
const gpu_memory_usage_arr1 = ref<Array<number>>([])
const gpu_memory_usage_arr2 = ref<Array<number>>([])
const gpu_usage_total_arr = ref<Array<number>>([])
const gpu_usage_total_arr1 = ref<Array<number>>([])
const gpu_usage_total_arr2 = ref<Array<number>>([])
const gpu_usage_d3d_arr = ref<Array<number>>([])
const gpu_usage_d3d_arr1 = ref<Array<number>>([])
const gpu_usage_d3d_arr2 = ref<Array<number>>([])
const gpu_clock = ref<number | string>('')
const gpu_clock1 = ref<number | string>('')
const gpu_clock2 = ref<number | string>('')
const gpu_mem_clock = ref<number | string>('')
const gpu_men_clock1 = ref<number | string>('')
const gpu_mem_clock2 = ref<number | string>('')
const gpu_power = ref<number | string>('')
const gpu_power1 = ref<number | string>('')
const gpu_power2 = ref<number | string>('')
const noGpuDataArr = ref<Array<boolean>>([false,false,false])
const limit = 60
let GPU0Data: any = null, GPU1Data: any = null, GPU2Data: any = null, isGPUData = false;
let containsGPU = true;
let gpuName0: any = null, gpuName1: any = null, gpuName2: any = null;
let GlobalBaseJsonInfo: any = null;

const gpuDefault = {
  clock: null,
  d3d_usage: null,
  fan: null,
  hot_spot_temp:null,
  mem_clock: null,
  mem_size: null,
  mem_temp: null,
  mem_usage: null,
  mem_usage_mb: null,
  name: null,
  pl_max_operating_voltage: null,
  pl_reliability_voltage: null,
  pl_thermal: null,
  power: null,
  power_list: [],
  shadres: null,
  temp: null,
  thermal_hotspot: null,
  thermal_memory: null,
  total_usage: null,
  usage_str: null,
  videobus: null,
  voltage: null,
}
onMounted(() => {
  recordPECore();
  setTimeout(() => {
    emitChangeFn()
  }, 100)
  loadingFn();
  console.log($store)
  listenHardwareChangeMsg()
})
onBeforeUnmount(() => {
  recordPECoreTimer && clearInterval(recordPECoreTimer)
  Timer2 && clearInterval(Timer2)
})
function listenHardwareChangeMsg() {
  const hw = new BroadcastChannel('hw')
  hw.onmessage = (e:any)=>{
    if (e.data.action && e.data.action == 'change') {
      emitChangeFn();
    }
  }
}
const computedGPUInfo = computed(()=>{
  if ($store.originData.GPU && $store.originData.GPU.SubNode) {
    try {
      let gpu_detect_list_str = window.localStorage.getItem('gpu_detect_list') // 拿最优显卡数据
      let gpu_detect_list:any = []
      if (gpu_detect_list_str) {
        try {
          gpu_detect_list = JSON.parse(gpu_detect_list_str)
        }catch (e) {
          gpu_detect_list = []
        }
      }
      // 创建一个映射关系，记录 gpu_detect_list 中元素的位置索引
      const indexMap = new Map();
      gpu_detect_list.forEach((item:{name:string}, index:number) => {
        indexMap.set(item.name, index);
      });
      let originGPUArr = JSON.parse(JSON.stringify($store.originData.GPU.SubNode))
      originGPUArr = originGPUArr.map((item:any,i:number) => {
        item.index = i
        return item
      })
      // 根据 arr1 的顺序来排序 arr2
      const sortedArr2 = originGPUArr.sort((a:any, b:any) => {
        const indexA = indexMap.get(a.VideoChipset);
        const indexB = indexMap.get(b.VideoChipset);

        // 如果某个元素不在 arr1 中，可以认为它的索引为 Infinity 或者一个较大的数
        const indexAOrDefault = indexA !== undefined ? indexA : Infinity;
        const indexBOrDefault = indexB !== undefined ? indexB : Infinity;

        return indexAOrDefault - indexBOrDefault;
      });
      console.log(sortedArr2)
      return sortedArr2
    }catch (e) {
      return $store.originData.GPU.SubNode
    }
  }
  return []
})
const recordPECore = () => {
  Timer2 = setInterval(async() => {
    GlobalBaseJsonInfo = $store.originData
    let SensorInfoOriginal: any = await $store.getSensorInfo()
    if (Object.keys(SensorInfoOriginal).length > 1 && GlobalBaseJsonInfo.GPU &&  GlobalBaseJsonInfo.GPU.SubNode) {
      const SensorInfoKeys = Object.keys(SensorInfoOriginal)
      if (!isGPUData) {
        containsGPU = SensorInfoKeys.some(name => name.includes("GPU"));
        SensorInfoKeys.forEach(function (item) {
          if (item.startsWith("GPU [#0]")) {
            gpuName0 = item.split(':')[1].trim();
          } else if (item.startsWith("GPU [#1]")) {
            gpuName1 = item.split(':')[1].trim();
          } else if (item.startsWith("GPU [#2]")) {
            gpuName2 = item.split(':')[1].trim();
          }
        });
        GPU0Data = GetHardwareDetails('GPU', gpuName0)
        GPU1Data = GetHardwareDetails('GPU', gpuName1)
        GPU2Data = GetHardwareDetails('GPU', gpuName2)
        isGPUData = true
      }
      if (!containsGPU) {
        GPU0Data = GlobalBaseJsonInfo.GPU.SubNode[0]
        gpuName0 = GPU0Data['VideoCard']
      }
      gpu_clock.value = $store.bg_sensor_data.gpu_list[0].clock as any
      gpu_clock1.value = $store.bg_sensor_data.gpu_list[1].clock as any
      gpu_clock2.value = $store.bg_sensor_data.gpu_list[2].clock as any
      gpu_mem_clock.value = $store.bg_sensor_data.gpu_list[0].mem_clock as any
      gpu_men_clock1.value = $store.bg_sensor_data.gpu_list[1].mem_clock as any
      gpu_mem_clock2.value = $store.bg_sensor_data.gpu_list[2].mem_clock as any
      gpu_power.value = $store.bg_sensor_data.gpu_list[0].power as any
      gpu_power1.value = $store.bg_sensor_data.gpu_list[1].power as any
      gpu_power2.value = $store.bg_sensor_data.gpu_list[2].power as any
    }

  }, $store.refreshTime)
}
async function recordPECoreCallBack() {
  let bg_sensor_data = JSON.parse(localStorage.getItem('bg_sensor_data')!)
  if (bg_sensor_data && bg_sensor_data.gpu) {
    GlobalBaseJsonInfo = $store.originData
    let SensorInfoOriginal: any = await $store.getSensorInfo()
    if (Array.isArray(bg_sensor_data.gpu_list[0])) {
      bg_sensor_data.gpu_list[0] = gpuDefault
      noGpuDataArr.value[0] = true
    }else{
      noGpuDataArr.value[0] = false
    }
    if (Array.isArray(bg_sensor_data.gpu_list[1])) {
      bg_sensor_data.gpu_list[1] = gpuDefault
      noGpuDataArr.value[1] = true
    }else{
      noGpuDataArr.value[1] = false
    }
    if (Array.isArray(bg_sensor_data.gpu_list[2])) {
      bg_sensor_data.gpu_list[2] = gpuDefault
      noGpuDataArr.value[2] = true
    }else{
      noGpuDataArr.value[2] = false
    }
    const SensorInfoKeys = Object.keys(SensorInfoOriginal)
    let SensorInfo = SensorAddAverageData(SensorInfoOriginal, SensorInfoKeys)
    if (!isGPUData) {
      containsGPU = SensorInfoKeys.some(name => name.includes("GPU"));
      SensorInfoKeys.forEach(function (item) {
        if (item.startsWith("GPU [#0]")) {
          gpuName0 = item.split(':')[1].trim();
        } else if (item.startsWith("GPU [#1]")) {
          gpuName1 = item.split(':')[1].trim();
        } else if (item.startsWith("GPU [#2]")) {
          gpuName2 = item.split(':')[1].trim();
        }
      });
      GPU0Data = GetHardwareDetails('GPU', gpuName0)
      GPU1Data = GetHardwareDetails('GPU', gpuName1)
      GPU2Data = GetHardwareDetails('GPU', gpuName2)
      isGPUData = true
    }
    // 占用
    let gpu_total_usage: any = bg_sensor_data.gpu_list[0].total_usage
    let gpu_d3d_usage: any = bg_sensor_data.gpu_list[0].d3d_usage
    if (!containsGPU) {
      gpu_total_usage = findValueByKeyAndType(SensorInfo, 'CPU', 'GPU D3D Usage', 'usage')[1]
      gpu_d3d_usage = findValueByKeyAndType(SensorInfo, 'CPU', 'GPU D3D Usage', 'usage')[1]
      GPU0Data = GlobalBaseJsonInfo.GPU.SubNode[0]
      gpuName0 = GPU0Data.VideoCard
    }
    const gpu1_total_usage: any = bg_sensor_data.gpu_list[1].total_usage
    const gpu1_d3d_usage: any = bg_sensor_data.gpu_list[1].d3d_usage
    const gpu2_total_usage: any = bg_sensor_data.gpu_list[2].d3d_usage
    const gpu2_d3d_usage: any = bg_sensor_data.gpu_list[2].d3d_usage

    gpu_usage_total_arr.value.push(isNumber(gpu_total_usage)?gpu_total_usage:'无')
    gpu_usage_d3d_arr.value.push(isNumber(gpu_d3d_usage)?gpu_d3d_usage:'无')
    gpu_usage_total_arr1.value.push(isNumber(gpu1_total_usage)?gpu1_total_usage:'无')
    gpu_usage_d3d_arr1.value.push(isNumber(gpu1_d3d_usage)?gpu1_d3d_usage:'无')
    gpu_usage_total_arr2.value.push(isNumber(gpu2_total_usage)?gpu2_total_usage:'无')
    gpu_usage_d3d_arr2.value.push(isNumber(gpu2_d3d_usage)?gpu2_d3d_usage:'无')
    if (gpu_usage_total_arr.value.length > limit) gpu_usage_total_arr.value.shift() // 只记录5分钟
    if (gpu_usage_d3d_arr.value.length > limit) gpu_usage_d3d_arr.value.shift() // 只记录5分钟
    if (gpu_usage_total_arr1.value.length > limit) gpu_usage_total_arr1.value.shift() // 只记录5分钟
    if (gpu_usage_d3d_arr1.value.length > limit) gpu_usage_d3d_arr1.value.shift() // 只记录5分钟
    if (gpu_usage_total_arr2.value.length > limit) gpu_usage_total_arr2.value.shift() // 只记录5分钟
    if (gpu_usage_d3d_arr2.value.length > limit) gpu_usage_d3d_arr2.value.shift() // 只记录5分钟

    // 温度
    let gpu_temp: any = bg_sensor_data.gpu_list[0].temp
    const gpu1_temp: any = bg_sensor_data.gpu_list[1].temp
    const gpu2_temp: any = bg_sensor_data.gpu_list[2].temp
    gpu_temp_arr.value.push(isNumber(gpu_temp)?gpu_temp:'无')
    gpu_temp_arr1.value.push(isNumber(gpu1_temp)?gpu1_temp:'无')
    gpu_temp_arr2.value.push(isNumber(gpu2_temp)?gpu2_temp:'无')
    if (gpu_temp_arr.value.length > limit) gpu_temp_arr.value.shift() // 只记录5分钟
    if (gpu_temp_arr1.value.length > limit) gpu_temp_arr1.value.shift() // 只记录5分钟
    if (gpu_temp_arr2.value.length > limit) gpu_temp_arr2.value.shift() // 只记录5分钟

    // 显存占用
    let gpu_mem_usage: any = bg_sensor_data.gpu_list[0].mem_usage

    let gpu1_mem_usage: any = bg_sensor_data.gpu_list[1].mem_usage
    let gpu2_mem_usage: any = bg_sensor_data.gpu_list[2].mem_usage

    gpu_memory_usage_arr.value.push(isNumber(gpu_mem_usage)?gpu_mem_usage:'无')
    gpu_memory_usage_arr1.value.push(isNumber(gpu1_mem_usage)?gpu1_mem_usage:'无')
    gpu_memory_usage_arr2.value.push(isNumber(gpu2_mem_usage)?gpu2_mem_usage:'无')

    if (gpu_memory_usage_arr.value.length > limit) gpu_memory_usage_arr.value.shift() // 只记录5分钟
    if (gpu_memory_usage_arr1.value.length > limit) gpu_memory_usage_arr1.value.shift() // 只记录5分钟
    if (gpu_memory_usage_arr2.value.length > limit) gpu_memory_usage_arr2.value.shift() // 只记录5分钟

  }
}
watch(()=> $store.bg_sensor_data,async()=>{
  try {
    recordPECoreCallBack()
  }catch (e) {

  }
},{deep: true})
function isNumber(value:any):boolean {
  return !isNaN(Number(value)) && isFinite(value) && value !== null;
}
function GetHardwareDetails(type: any, name: any) {
  if (!name || !GlobalBaseJsonInfo[type] || !GlobalBaseJsonInfo[type].SubNode) return null;
  const node = GlobalBaseJsonInfo[type].SubNode.find((node: any) => node.VideoChipset === name || name.includes(node.VideoChipset));
  return node || null;
}

const emitChangeFn = () => {
  let h = 72
  if (activeCollapse_gpu.value.length === 0) {
    props.changeMenuInfo(2,h)
    return
  }
  if ($store.originData.GPU && $store.originData.GPU.SubNode.length > 0) {
    $store.originData.GPU.SubNode.forEach((item: any, index: any) => {
      if (!noGpuDataArr.value[index]) {
        h += 678
      }
    })
  }

  props.changeMenuInfo(2,h)
}

const loadingFn = () => {
  loading_count++
  if (loading_count > 10) {
    loading.value = false
    return
  }
  if ($store.HwInfo.GPU) {
    emitChangeFn()
    loading.value = false
  }else{
    setTimeout(()=>{loadingFn()},1000)
  }
}

const gpu_memory_size_type = (data: any) => {
  const regex = /\[(.+?)\]/g
  const VideoMemory = (data.VideoMemory).split(' ')
  const VideoMemoryBrandArr = (data.VideoMemory.match(regex))
  let VideoMemoryBrand = ''
  if (VideoMemoryBrandArr) {
    VideoMemoryBrand = (VideoMemoryBrandArr[VideoMemoryBrandArr.length - 1]).replace(/\[|]/g, '')
  }
  let VideoType = ''
  if (VideoMemory[3]) {
    VideoType = VideoMemory[3]
  }
  let typeBrand = ''
  if (VideoType || VideoMemoryBrand) {
    typeBrand = ' (' + VideoType + ' ' + VideoMemoryBrand + ')'
  }
  return Math.ceil(((VideoMemory[0] / 1024))) + 'G' + typeBrand
}
</script>

<template>
  <div class="HardwareGPU">
    <el-collapse v-model="activeCollapse_gpu" @change="emitChangeFn">
      <el-collapse-item title="GPU" name="1" class="as">
        <template #title>
          <div class="HardwareGPUTitle flex-items-center">
            <span class="iconfont icon-GPU" style="color: #3579D5;font-size: 24px;margin-right: 5px;"></span>
            <span style="color: #ffffff">{{ $t('hardwareInfo.graphicsCard') }}</span>

            <span class="ml-auto"></span>
          </div>
        </template>
        <div
            v-if="$store.originData.GPU"
            v-for="(item,index) in computedGPUInfo"
            :key="'hwg'+index"
        >
          <template v-if="!noGpuDataArr[item.index]">
            <div class="name">{{ item.VideoCard ? String(item.VideoCard).replace(/\(.*?\)/g, '').replace(/\[.*?\]/g, '').trim() : item.VideoChipSet }}</div>
            <div class="box" v-if="item.index === 0">
              <ChartHW :dataHtml="'hardwareInfo.D3D'" unit="%" :yAxisValue="gpu_usage_d3d_arr" :yAxisMax="100"></ChartHW>
              <ChartHW :dataHtml="'hardwareInfo.Total'" unit="%" :yAxisValue="gpu_usage_total_arr" :yAxisMax="100"></ChartHW>
            </div>
            <div class="box" v-if="item.index === 0">
              <ChartHW :dataHtml="'hardwareInfo.VRAM'" unit="%" :yAxisValue="gpu_memory_usage_arr" :yAxisMax="100"></ChartHW>
              <ChartHW :dataHtml="'hardwareInfo.temperature'" unit="℃" :tempWall="90" :yAxisValue="gpu_temp_arr" :yAxisMax="120"></ChartHW>
            </div>
            <div class="box flex-wrap" v-if="item.index === 0">
              <div class="card" v-if="('' + gpu_clock)">
                <span>{{$t('hardwareInfo.frequency')}}</span>
                <span>{{ gpu_clock }}MHz</span>
              </div>
              <div class="card" v-if="('' + gpu_mem_clock)">
                <span>{{$t('hardwareInfo.VRAMFrequency')}}</span>
                <span>{{ gpu_mem_clock }}MHz</span>
              </div>
              <div class="card" v-if="('' + gpu_power)">
                <span>{{$t('hardwareInfo.thermalPower')}}</span>
                <span>{{ gpu_power }}W</span>
              </div>
            </div>
            <div class="box" v-if="item.index === 1">
              <ChartHW :dataHtml="'hardwareInfo.D3D'" unit="%" :yAxisValue="gpu_usage_d3d_arr1" :yAxisMax="120"></ChartHW>
              <ChartHW :dataHtml="'hardwareInfo.Total'" unit="%" :yAxisValue="gpu_usage_total_arr1" :yAxisMax="120"></ChartHW>
            </div>
            <div class="box" v-if="item.index === 1">
              <ChartHW :dataHtml="'hardwareInfo.VRAM'" unit="%" :yAxisValue="gpu_memory_usage_arr1" :yAxisMax="120"></ChartHW>
              <ChartHW :dataHtml="'hardwareInfo.temperature'" unit="℃" :tempWall="90" :yAxisValue="gpu_temp_arr1" :yAxisMax="120"></ChartHW>
            </div>
            <div class="box flex-wrap" v-if="item.index === 1">
              <div class="card" v-if="('' + gpu_clock1)">
                <span>{{$t('hardwareInfo.frequency')}}</span>
                <span>{{ gpu_clock1 }}MHz</span>
              </div>
              <div class="card" v-if="('' + gpu_men_clock1)">
                <span>{{$t('hardwareInfo.VRAMFrequency')}}</span>
                <span>{{ gpu_men_clock1 }}MHz</span>
              </div>
              <div class="card" v-if="('' + gpu_power1)">
                <span>{{$t('hardwareInfo.thermalPower')}}</span>
                <span>{{ gpu_power1 }}W</span>
              </div>
            </div>
            <div class="box" v-if="item.index === 2">
              <ChartHW :dataHtml="'hardwareInfo.D3D'" unit="%" :yAxisValue="gpu_usage_d3d_arr2" :yAxisMax="120"></ChartHW>
              <ChartHW :dataHtml="'hardwareInfo.Total'" unit="%" :yAxisValue="gpu_usage_total_arr2" :yAxisMax="120"></ChartHW>
            </div>
            <div class="box" v-if="item.index === 2">
              <ChartHW :dataHtml="'hardwareInfo.VRAM'" unit="%" :yAxisValue="gpu_memory_usage_arr2" :yAxisMax="120"></ChartHW>
              <ChartHW :dataHtml="'hardwareInfo.temperature'" unit="℃" :tempWall="90" :yAxisValue="gpu_temp_arr2" :yAxisMax="120"></ChartHW>
            </div>
            <div class="box flex-wrap" v-if="item.index === 2">
              <div class="card" v-if="('' + gpu_clock2)">
                <span>{{$t('hardwareInfo.frequency')}}</span>
                <span>{{ gpu_clock2 }}MHz</span>
              </div>
              <div class="card" v-if="('' + gpu_mem_clock2)">
                <span>{{$t('hardwareInfo.VRAMFrequency')}}</span>
                <span>{{ gpu_mem_clock2 }}MHz</span>
              </div>
              <div class="card" v-if="('' + gpu_power2)">
                <span>{{$t('hardwareInfo.thermalPower')}}</span>
                <span>{{ gpu_power2 }}W</span>
              </div>
            </div>
          </template>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<style scoped lang="scss">
.HardwareGPU {
  width: 100%;
  background: rgba(45 ,46 ,57, 0.8);
  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  padding: 0 25px 0 20px;

  .HardwareGPUTitle {
    width: 100%;
    padding-right: 25px;
  }

  .name {
    margin-bottom: 10px;
  }

  .box {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
    margin-bottom: 20px;
  }

  .card {
    width: 270px;
    height: 60px;
    background: rgba(34, 35, 46, 0.8);
    box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;

    span:last-child {
      color: #35D5B1;
    }
  }

  .flex-wrap {
    flex-wrap: wrap !important;

    .card {
      margin-bottom: 20px;
    }
  }
}
</style>
