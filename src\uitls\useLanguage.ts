import { ref, onMounted, onUnmounted } from 'vue';

export function useLanguage() {
//   const currentLanguage = ref(localStorage.getItem('language') || 'CN');
  const loadLanguage = () => {
    const savedLanguage = localStorage.getItem('language');
    if (savedLanguage) {
    //   currentLanguage.value = savedLanguage;
      if (savedLanguage === 'AR') {
        document.body.setAttribute('dir', 'rtl');
      } else {
        document.body.removeAttribute('dir');
      }
    }
  };

  const handleStorageChange = (event: any) => {
    if (event.key === 'language') {
    //   currentLanguage.value = event.newValue;
      if (event.newValue === 'AR') {
        document.body.setAttribute('dir', 'rtl');
      } else {
        document.body.removeAttribute('dir');
      }
    }
  };

  onMounted(() => {
    loadLanguage();
    window.addEventListener('storage', handleStorageChange);
  });

  onUnmounted(() => {
    window.removeEventListener('storage', handleStorageChange);
  });

//   return { useLanguage };
}