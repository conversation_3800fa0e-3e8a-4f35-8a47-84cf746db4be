<template>
  <div class="setting">
    <SettingSidebar/>
    <SettingMain/>
  </div>
</template>

<script setup lang="ts">
import SettingSidebar from './components/setting-sidebar.vue'
import SettingMain from './components/setting-main.vue'
import {onBeforeMount, provide, ref} from 'vue'
import {gamepp} from 'gamepp'
import { useLanguage } from '@/uitls/useLanguage';
useLanguage()
// @ts-ignore
// const gamepp = window.gamepp
const NavList = ref([
  {
    name: 'Setting.common',
    target: '#ty',
    active: true,
  },
  {
    name: 'Setting.personal',
    target: '#gxh',
    active: false,
  },
  {
    name: 'Setting.messageNotification',
    target: '#xx',
    active: false,
  },
  {
    name: 'Setting.sensorReading',
    target: '#cgq',
    active: false,
  },
  {
    name: 'Setting.OLEDscreen',
    target: '#OLED',
    active: false,
  },
  {
    name: 'Setting.performanceStatistics',
    target: '#xn',
    active: false,
  },
  {
    name: 'Setting.shortcut',
    target: '#kjj',
    active: false,
  },
  {
    name: 'Setting.ingameSetting',
    target: '#yx',
    active: false,
  },
  // {
  //   name: '进程注入管理',
  //   target: '#jc',
  //   active: false,
  // },
  {
    name: 'Setting.other',
    target: '#qt',
    active: false,
  },
])
let zoomValue = ref<number>(1)
provide('NavList', NavList)


onBeforeMount(()=>{
  initZoom()
})

async function initZoom() {
  try {
    zoomValue.value = await gamepp.display.getScaleFromWindowInMonitor.promise();
    const zoomWithSystem = gamepp.setting.getInteger.sync(313)
    if (zoomWithSystem === 1) {
      // 设置body zoom
      document.body.style.zoom = zoomValue.value
      gamepp.webapp.windows.resize.sync('gamepp_config',Math.floor(1292 * zoomValue.value),Math.floor(732 * zoomValue.value))
    }
  }catch (e) {
    zoomValue.value = 1
  }

  try {
    gamepp.display.onDisplayMetricsChanged.addEventListener( async (scaleFactor:number)=>{
      const zoomWithSystem = gamepp.setting.getInteger.sync(313)
      if (zoomWithSystem === 1) {
        console.log('display',scaleFactor)
        // zoom.value = display.scaleFactor
        zoomValue.value = scaleFactor
        document.body.style.zoom = zoomValue.value
        try{
          gamepp.webapp.windows.resize.sync('gamepp_config',Math.floor(1292 * zoomValue.value),Math.floor(732 * zoomValue.value))
        }catch (e) {
          console.log('gamepp.webapp.windows.resize.sync(gamepp_config)',e)
        }
      }
    })
  }catch (e) {
    console.log(e)
  }
}
</script>

<style scoped lang="scss">
.setting {
  height: 720px;
  width: 1280px;

  background-color: #22232E;
  display: flex;
  flex-flow: row nowrap;

  box-shadow: 0 1px 6px rgb(0 0 0 / 60%);
  position: relative;
  border-radius: 2px;
  overflow: hidden;
  margin-left: 6px;
  margin-top: 6px;
}
</style>
<style lang="scss">
* {
  box-sizing: border-box;
}
html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,dd,dl,dt,li,ol,ul,input,select,button,textarea,img{padding:0;margin:0;border:0}input,button,select,textarea,a,img{outline:0}::-moz-focus-inner{border:0}body{-webkit-user-select:none;font-size:12px;width:100%;background:rgb(25,255,255,0);overflow-x:hidden;font-family:"Microsoft YaHei","微软雅黑",'Arial','PingFang SC','Helvetica Neue','Helvetica','STHeitiSC-Light',sans-serif;position:relative}html{width:100%}a img{border:0}img{vertical-align:middle}a{text-decoration:none;outline:0;padding:0;margin:0;cursor:pointer}ul,ul li,ol li,li{list-style:none}table{border-collapse:collapse;border-spacing:0}input,textarea{border:0}.clearfix:after{visibility:hidden;display:block;font-size:0;content:".";clear:both;height:0}* html .clearfix{zoom:1}*:first-child+html .clearfix{zoom:1}.fl{float:left}.fr{float:right}.none{display:none}.tx_c{text-align:center}.tx_l{text-align:left}.w_pb{width:1200px;margin:0 auto}i{font-style:normal}.clearfix{clear:both}.arrows{cursor:default}.smallImg{width:100%;height:100%}.layui-layer-loading2{width:64px!important;height:64px!important}.filter-title::selection{background:#21222a}body .Aero-class .layui-layer-title{height:30px;line-height:30px;font-size:12px;color:#c3cad5;background-color:#34343b;border-radius:0;border-bottom:0}body .Aero-class .layui-layer-setwin{top:-2px}body .Aero-class .layui-layer-content{background-color:#2c2c33;color:#c3cad5;font-size:14px;font-weight:400;line-height:0px}body .Aero-class .layui-layer-btn{background-color:#2c2c33;padding:14px 17px 16px;border-top:1px solid #34343b}.layui-form-checked i:before{content:"\e605"!important}.LayuiSwitch{margin:1px 0 0 15px}.subelement:hover::-webkit-scrollbar-thumb{background:#b5b4b4}.wrapAll{width:950px;height:628px;background:#21222a;position:relative}.subelement{height:538px;overflow:hidden;overflow-y:auto}.subelement::-webkit-scrollbar{width:6px}.subelement::-webkit-scrollbar-thumb{background:#858687;border-radius:200px;}.subelement::-webkit-scrollbar-track{background-color:#31333e;border-radius:100px}
:root {
  --font-color: #fff;
  --font-gray: #777777;
  --active-color: #3579D5;
  --active-sidebar-bg: #2D2E39;
  --sidebar-bg: #1C1C22;
  --setting-item-border-color: #343647;
  --el-color-primary: #3579D5;
  --warn-color: #BF4040;
}

html {
  font-size: 100px;
}

.scroll::-webkit-scrollbar {
  width: 5px;
  transition: 0.25s;
}

.scroll::-webkit-scrollbar-thumb {
  background: #71738C;
  border-radius: 3px;
}

.scroll::-webkit-scrollbar-thumb:hover {
  background-color: #71738C;
}

.scroll::-webkit-scrollbar-track {
  background-color: #2D2E39;
  width: 2px;
}
</style>
