body[dir=rtl] {
    header{
        padding-left: 0;
        div{
            justify-content: flex-start!important;
        }
        .name{
            padding-left:0;
            padding-right: 10px;
        }
    }
    ul.RightTopIcons{
        margin-left: inherit!important;
        margin-right: auto;
    }
    .el-button+.el-button{
        margin-left: 0;
        margin-right: 12px;
    }
    .chooseCPUSetDialog section header .icon-threads{
        margin-right:0;
        margin-left: 10px;
    }
    .chooseCPUSetDialog section header .icon-Close{
        margin-left:inherit!important;
        margin-right: auto;
    }
    .cores{
        margin-left: 10px;
        margin-right: 40px;
    }
    .thread{
        margin-left: 10px;
        margin-right: 20px;
    }
    .flex-items-center span:nth-child(7){
        margin-left: 0!important;
        margin-right: 20px;
    }
    .bar-content .el-dropdown:first-child{
        margin-left: 10px;
    }
    .bar-content .el-button:nth-child(1){
        margin-left: 0!important;
    }
    .dialog-container header .ml-auto{
        margin-left: inherit!important;
        margin-right: auto;
    }
    .dialog-container header .icon-Close{
        margin-left:0;
        margin-right: 20px;
    }
    .message-box-header img{
        margin-right:0;
        margin-left: 10px;
    }
    .message-box-header .icon-Close{
        margin-left: inherit!important;
        margin-right: auto;
    }
    .content{
        .left-layout{
            .bar-content{
                .el-button+.el-button{
                    margin-left: 0;
                    margin-right: 12px;
                } 
                .el-button:first-child{
                    margin-right: 12px;
                }
            }
        }
        .right-layout{
            .search-line{
                .tip{
                    margin-right:inherit;
                    margin-left: auto;
                }
                .showOrHideSystemProcess{
                    margin-left: 10px;
                    margin-right: 0;
                }
            }
            .wrap{
                .list-item{
                    div{
                        justify-content: flex-start;
                    }
                    .name{
                        padding-left:0;
                        padding-right: 10px;
                        right: 0;
                        text-align: right;
                    }
                    .thread{
                        // padding-right:0;
                        // padding-left: 5px;
                        right: var(--all-name-w);
                    }
                    .cpu{
                        right:  calc(var(--all-name-w) + var(--all-thread-w));;
                    }
                    .cpu-pp{
                        right: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w));
                    }
                    .gpu{
                        right: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) + var(--all-cpu-pp-w));
                    }
                    .gpu-pp{
                        right: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) + var(--all-cpu-pp-w) + var(--all-gpu-w));
                    }
                    .mem{
                        right:  calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) + var(--all-cpu-pp-w) + var(--all-gpu-w) + var(--all-gpu-pp-w));
                    }
                    .mem-pp{
                        right: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) + var(--all-cpu-pp-w) + var(--all-gpu-w) + var(--all-gpu-pp-w) + var(--all-mem-w));
                    }
                    .group{
                        right: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) + var(--all-cpu-pp-w) + var(--all-gpu-w) + var(--all-gpu-pp-w) + var(--all-mem-w) + var(--all-mem-pp-w));

                    }
                    .controls{
                        right:  calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) + var(--all-cpu-pp-w) + var(--all-gpu-w) + var(--all-gpu-pp-w) + var(--all-mem-w) + var(--all-mem-pp-w) + var(--all-group-w));
                    }
                }
            }
            // .all-process section .wrap .scroll-box .list-item .iconSort{
            //     margin-left: 0;
            //     margin-right: 5px;
            // }
        }
    }
    .all-process{
        .divider-line-name{
            right: calc(var(--all-name-w) - 16px);
        }
        .divider-line-thread {
            right: calc(var(--all-name-w) + var(--all-thread-w) - 16px);
        }
        .divider-line-cpu {
            right: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) - 16px);
        }
        .divider-line-cpu-pp {
            right: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) + var(--all-cpu-pp-w) - 16px);
        }
        .divider-line-gpu {
            right: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) + var(--all-cpu-pp-w) + var(--all-gpu-w) - 16px);
        }
        .divider-line-gpu-pp {
            right: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) + var(--all-cpu-pp-w) + var(--all-gpu-w) + var(--all-gpu-pp-w) - 16px);
        }
        .divider-line-mem {
            right: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) + var(--all-cpu-pp-w) + var(--all-gpu-w) + var(--all-gpu-pp-w) + var(--all-mem-w) - 16px);
        }
        .divider-line-mem-pp {
            right: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) + var(--all-cpu-pp-w) + var(--all-gpu-w) + var(--all-gpu-pp-w) + var(--all-mem-w) + var(--all-mem-pp-w) - 16px);
        }
        .divider-line-group {
            right: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) + var(--all-cpu-pp-w) + var(--all-gpu-w) + var(--all-gpu-pp-w) + var(--all-mem-w) + var(--all-mem-pp-w) + var(--all-group-w) - 16px);
        }
    }
    .right-layout{
        padding-right: 10px;
        padding-left: 0;
        .search-line{
            padding-right:0;
            padding-left: 10px;
        }
    }
    .group-process{
        .divider-line-name{
            right: calc(var(--name-w) - 16px);
        }
        .divider-line-thread {
            right: calc(var(--name-w) + var(--thread-w) - 16px);
        }
        .divider-line-cpu {
            right: calc(var(--name-w) + var(--thread-w) + var(--cpu-w) - 16px);
        }
        .divider-line-cpu-pp {
            right: calc(var(--name-w) + var(--thread-w) + var(--cpu-w) + var(--cpu-pp-w) - 16px);
        }
        .divider-line-gpu {
            right: calc(var(--name-w) + var(--thread-w) + var(--cpu-w) + var(--cpu-pp-w) + var(--gpu-w) - 16px);
        }
        .divider-line-gpu-pp {
            right: calc(var(--name-w) + var(--thread-w) + var(--cpu-w) + var(--cpu-pp-w) + var(--gpu-w) + var(--gpu-pp-w) - 16px);
        }
        .divider-line-mem {
            right:  calc(var(--name-w) + var(--thread-w) + var(--cpu-w) + var(--cpu-pp-w) + var(--gpu-w) + var(--gpu-pp-w) + var(--mem-w) - 16px);
        }
        .divider-line-mem-pp {
            right: calc(var(--name-w) + var(--thread-w) + var(--cpu-w) + var(--cpu-pp-w) + var(--gpu-w) + var(--gpu-pp-w) + var(--mem-w) + var(--mem-pp-w) - 16px);
        }
    }
     
}