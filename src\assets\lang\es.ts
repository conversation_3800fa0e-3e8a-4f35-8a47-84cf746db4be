const es = {
  "update": {
    "Game": "Game",
    "PP": "PP",
    "upgradingInProgress": "Actualización en curso",
    "theModuleIsBeingUpdated": "Actualizando módulo",
    "dataIsBeingUpdated": "Actualizando datos...",
    "checkingUpdate": "Comprobando actualizaciones",
    "checkingUpgrade": "Comprobando actualizaciones",
    "loadingProgramComponent": "Cargando componentes del programa...",
    "loadingHotkeyModules": "Cargando componente de atajos",
    "loadingGPPModules": "Cargando componentes de GamePP",
    "loadingBlackWhiteList": "Cargando lista negra y blanca",
    "loadingGameSetting": "Cargando parámetros de configuración del juego...",
    "loadingUserAbout": "Cargando datos de autenticación del usuario",
    "loadingGameBenchmark": "Cargando puntuación de juego",
    "loadingHardwareInfo": "Cargando componente de información de hardware",
    "loadingDBModules": "Cargando módulo de base de datos...",
    "loadingIGCModules": "Cargando módulo IGC",
    "loadingFTPModules": "Cargando módulo de soporte FTP",
    "loadingDialogModules": "Cargando módulo de cuadro de diálogo",
    "loadingDataStatisticsModules": "Cargando módulo de estadísticas",
    "loadingSysModules": "Cargando componentes del sistema",
    "loadingGameOptimization": "Cargando optimización del juego",
    "loadingGameAcceleration": "Cargando aceleración del juego",
    "loadingScreenshot": "Cargando captura de pantalla de la grabación",
    "loadingVideoComponent": "Cargando componente de compresión de vídeo",
    "loadingFileFix": "Cargando reparación de archivo",
    "loadingGameAI": "Cargando la calidad de la IA del juego",
    "loadingNVAPIModules": "Cargando módulo NVAPI",
    "loadingAMDADLModules": "Cargando módulo AMDADL",
    "loadingModules": "Cargando módulo"
  },
  "messages": {
    "append": "Agregar",
    "confirm": "Confirmar",
    "cancel": "Cancelar",
    "default": "Predeterminado",
    "quickSelect": "Selección rápida",
    "onoffingame": "Activar/Desactivar monitoreo en el juego:",
    "changeKey": "Haga clic para cambiar el atajo de teclado",
    "clear": "Vaciar",
    "hotkeyOccupied": "La tecla de acceso rápido ya está en uso, por favor, establezca una nueva.",
    "minimize": "Minimizar",
    "exit": "Salir",
    "export": "Exportar",
    "import": "Importar",
    "screenshot": "Captura de pantalla",
    "showHideWindow": "Mostrar/Ocultar ventana",
    "ingameControlPanel": "Panel de control en el juego",
    "openOrCloseGameInSettings": "Alternar panel de configuración en el juego",
    "openOrCloseGameInSettings2": "Presione esta combinación de teclas para activar",
    "openOrCloseGameInSettings3": "Activar/Desactivar monitoreo en el juego",
    "openOrCloseGameInSettings4": "Activar/desactivar filtro de juego",
    "startManualRecord": "Iniciar/Detener Grabación Manual de Estadísticas",
    "performanceStatisticsMark": "Marca de estadísticas de rendimiento",
    "EnableAIfilter": "El filtro de IA requiere presionar esta combinación de teclas para habilitarlo",
    "Start_stop": "Iniciar/Detener Registro Estadístico Manual",
    "pressureTest": "Prueba de estrés",
    "moduleNotInstalled": "Módulo funcional no instalado",
    "installingPressureTest": "Instalando módulo de prueba de estrés...",
    "importFailed": "Fallo en la importación",
    "gamepp": "GamePP",
    "copyToClipboard": "Copiado al portapapeles"
  },
  "home": {
    "homeTitle": "Inicio",
    "hardwareInfo": "Información de hardware",
    "functionIntroduction": "Funciones",
    "fixedToNav": "Anclar a la barra de navegación",
    "cancelFixedToNav": "Desanclar de la barra de navegación",
    "hardwareInfoLoading": "Cargando información del hardware...",
    "performanceStatistics": "Estadísticas de Rendimiento",
    "updateNow": "Actualizar ahora",
    "recentRun": "Actividad Reciente",
    "resolution": "Resolución:",
    "duration": "Duración:",
    "gameFilter": "Filtro de juego",
    "gameFilterHasAccompany": "El filtro del juego está ahora activo",
    "gameFilterHasAccompany2": "Los usuarios juegan a juegos como Cyberpunk, APEX y Hogwarts Legacy",
    "currentList": "Elementos de monitoreo en la lista actual",
    "moreFunction": "Benchmark, prueba de estrés, monitoreo de escritorio y más funciones están en desarrollo.",
    "newVersion": "¡Nueva versión disponible!",
    "discoverUpdate": "¡Actualización encontrada!",
    "downloading": "Descargando",
    "retry": "Reintentar",
    "erhaAI": "2HaAI",
    "recordingmodule": "Esta función depende del módulo de grabación",
    "superPower": "Modo Ultra",
    "autoRecord": "Registrar automáticamente momentos de kill en juegos y guardar fácilmente las mejores jugadas",
    "externalDevice": "Iluminación Dinámica de Periféricos",
    "linkage": "Activar escenas de kill en juegos y mostrarlas mediante periféricos conectados",
    "AI": "Prueba de rendimiento de AI",
    "test": "Prueba modelos de IA con GPU y visualiza la puntuación de rendimiento de IA",
    "supportedGames": "Juegos compatibles",
    "games": "Naraka: Bladepoint, Delta Force, FragPunk, League of Legends, PUBG, Valorant, The Finals, APEX Legends, COD20",
    "videoRecording": "Grabación de video",
    "videoRecording2": "Función de grabación de video basada en OBS, permite ajustar el bitrate de video y la tasa de cuadros (FPS) para satisfacer diferentes requisitos de calidad y fluidez; también admite \"Reproducción Instantánea\", presione la tecla de acceso rápido para guardar fragmentos destacados en cualquier momento.",
    "addOne": "Obtén gratis",
    "gamePlatform": "Plataforma de juegos",
    "goShop": "Ir a la página de la tienda",
    "receiveDeadline": "Límite de Reclamación Posterior",
    "2Ai": "2 Risa AI",
    "questionDesc": "Descripción del problema",
    "inputYourQuestion": "Por favor, introduzca aquí sus sugerencias o comentarios。",
    "uploadLimit": "Cargar hasta 3 imágenes locales en formato JPG/PNG/BMP",
    "email": "Correo electrónico",
    "contactWay": "Información de contacto",
    "qqNumber": "Número QQ (opcional)",
    "submit": "Enviar"
  },
  "hardwareInfo": {
    "hardwareOverview": "Visión General de Hardware",
    "copyAllHardwareInfo": "Copiar toda la información del hardware",
    "processor": "Procesador",
    "coreCount": "Núcleos:",
    "threadCount": "Número de Hilos:",
    "currentFrequency": "Frecuencia Actual:",
    "currentVoltage": "Tensión actual:",
    "copy": "Copiar",
    "releaseDate": "Fecha de lanzamiento",
    "codeName": "Nombre de código",
    "thermalDesignPower": "Potencia de Diseño Térmico",
    "maxTemperature": "Temperatura Máxima",
    "graphicsCard": "Tarjeta Gráfica",
    "brand": "Marca:",
    "streamProcessors": "Procesador de flujo:",
    "Videomemory": "Memoria de video:",
    "busSpeed": "Velocidad del Bus",
    "driverInfo": "Información del controlador",
    "driverInstallDate": "Fecha de instalación del controlador",
    "hardwareID": "ID de hardware",
    "motherboard": "Placa base",
    "chipGroup": "Chipset:",
    "BIOSDate": "Fecha BIOS",
    "BIOSVersion": "Versión BIOS",
    "PCIESlots": "ranura PCIe",
    "PCIEVersion": "Versión PCIe compatible",
    "memory": "Memoria",
    "memoryBarCount": "Cantidad:",
    "totalSize": "Tamaño:",
    "channelCount": "Canal:",
    "Specificmodel": "Modelo específico",
    "Pellet": "Fabricante de Partículas",
    "memoryBarEquivalentFrequency": "Frecuencia Efectiva de la Memoria:",
    "hardDisk": "Disco duro",
    "hardDiskCount": "Número de discos duros:",
    "actualCapacity": "Capacidad Real",
    "type": "Tipo",
    "powerOnTime": "Tiempo de encendido",
    "powerOnCount": "Ciclos de Encendido",
    "SSDRemainingLife": "Vida útil restante de SSD",
    "partitionInfo": "Información de partición",
    "hardDiskController": "Controlador de Disco Duro",
    "driverNumber": "Número de serie del disco",
    "display": "Monitor",
    "refreshRate": "Tasa de refresco:",
    "screenSize": "Tamaño de pantalla:",
    "inches": "Pulgadas",
    "productionDate": "Fecha de Producción",
    "supportRefreshRate": "Tasa de refresco compatible",
    "screenLongAndShort": "Tamaño de pantalla",
    "systemInfo": "Información del Sistema",
    "version": "Versión",
    "systemInstallDate": "Fecha de instalación del sistema",
    "systemBootTime": "Hora de Arranque Actual",
    "systemRunTime": "Tiempo de ejecución",
    "Poccupied": "P Uso",
    "Eoccupied": "E ocupado",
    "occupied": "Ocupado",
    "temperature": "Temperatura",
    "Pfrequency": "P Frecuencia",
    "Efrequency": "Frecuencia E",
    "thermalPower": "Consumo Térmico",
    "frequency": "Frecuencia",
    "current": "Actual",
    "noData": "Sin datos",
    "loadHwinfo_SDK": "Error al cargar Hwinfo_SDK.dll, no se pueden leer los datos de hardware/sensor",
    "loadHwinfo_SDK_reason": "Posibles causas de este problema:",
    "reason": "Causa",
    "BlockIntercept": "Bloqueado por software antivirus, p. ej.: Software Antivirus 2345 (Proceso de Defensa Activa 2345, Proceso de Defensa Activa McAfee)",
    "solution": "Solución:",
    "solution1": "Después de cerrar y desinstalar los procesos relacionados, reinicie GamePP",
    "solution2": "Desconecte los dispositivos asociados y reinicie GamePP",
    "RestartGamePP": "Desconecte el controlador, espere a que el dispositivo responda y luego reinicie GamePP",
    "HWINFOcannotrun": "Hwinfo no se puede ejecutar correctamente",
    "downloadHWINFO": "Descargar Hwinfo",
    "openHWINFO": "¿Puede abrirse normalmente al hacer clic en RUN después de abrir Hwinfo?",
    "hardwareDriverProblem": "Problemas del controlador de hardware",
    "checkHardwareManager": "Verifique el administrador de hardware para asegurarse de que los controladores de placa base y tarjeta gráfica estén instalados correctamente",
    "systemProblem": "Problemas del sistema, por ejemplo: El uso de herramientas de activación como Baofeng o Xiaoma puede provocar que los controladores no se carguen y las actualizaciones de Windows 7 no se instalen automáticamente",
    "reinstallSystem": "Reinstalar el sistema para activarlo, Descargar e instalar WIN7: Paquete de actualización *********",
    "Windows7": "Windows 7: Instale la actualización SHA-256, Windows 10: Active mediante certificado digital, Windows 11 Versión preliminar: Desactive la integridad de memoria",
    "ViolenceActivator": "Si ha utilizado herramientas de activación no autorizadas como Xiaoma, por favor repare o reinstale el sistema",
    "MultipleGraphicsCardDrivers": "Se han instalado controladores de tarjetas gráficas de diferentes marcas en el equipo, por ejemplo controladores AMD y Nvidia simultáneamente",
    "UninstallUnused": "Reinicie el ordenador tras desinstalar los controladores de gráficos innecesarios",
    "OfficialQgroup": "Ninguna de las razones anteriores se aplica. Únete a nuestro grupo oficial de QQ: 908287288 (Grupo 5) para resolver el problema.",
    "ExportHardwareData": "Exportar datos de hardware",
    "D3D": "Uso de D3D",
    "Total": "Uso Total",
    "VRAM": "Uso de VRAM",
    "VRAMFrequency": "Frecuencia de VRAM",
    "SensorData": "Datos del sensor",
    "CannotGetSensorData": "Error al obtener los datos del sensor",
    "LoadingHardwareInfo": "Cargando información del hardware...",
    "ScanTime": "Último escaneo:",
    "Rescan": "Reescanear",
    "Screenshot": "Captura de pantalla",
    "configCopyed": "La información de configuración se ha copiado al portapapeles.",
    "LegalRisks": "Se han detectado riesgos legales potenciales",
    "brandLegalRisks": "La visualización de la marca puede conllevar riesgos legales potenciales",
    "professionalVersion": "Edición Profesional",
    "professionalWorkstationVersion": "Edición Workstation Profesional",
    "familyEdition": "Edición Home",
    "educationEdition": "Edición Educativa",
    "enterpriseEdition": "Edición Empresarial",
    "flagshipEdition": "Edición Premium",
    "familyPremiumEdition": "Edición Familiar Premium",
    "familyStandardEdition": "Edición Estándar para Familia",
    "primaryVersion": "Versión Básica",
    "bit": "bit",
    "tempWall": "Pared de temperatura",
    "error": "Error",
    "screenshotSuccess": "Captura de pantalla guardada correctamente",
    "atLeastOneData": "Se debe conservar al menos 1 entrada de datos",
    "atMostSixData": "Añada un máximo de 6 entradas de datos",
    "screenNotActivated": "No activado"
  },
  "psc": {
    "processCoreAssign": "Asignación de núcleos de proceso",
    "CoreAssign": "Asignación de núcleos:",
    "groupName": "Nombre del grupo:",
    "notGameProcess": "Procesos no de juego",
    "unNamedProcess": "Grupo sin nombre",
    "Group2": "Grupo",
    "selectTheCore": "Seleccione el núcleo",
    "controls": "Operación",
    "tips": "Aviso",
    "search": "Buscar",
    "shiftOut": "Expulsar",
    "ppValue": "Valor PP",
    "ppDesc": "El valor PP refleja el consumo histórico de recursos de hardware. Cuanto mayor sea este valor, más recursos de hardware consumirá.",
    "littletips": "Sugerencia: Mantenga presionado el proceso para arrastrarlo al grupo izquierdo",
    "warning1": "Seleccionar hilos de núcleo entre grupos puede afectar al rendimiento. Se recomienda utilizar núcleos del mismo grupo.",
    "warning2": "¿Está seguro de dejar este nombre de grupo vacío?",
    "warning3": "El efecto de asignación de núcleos se invalidará tras la eliminación. ¿Está seguro de que desea eliminar este grupo?",
    "allprocess": "Todos los procesos",
    "pleaseCheckProcess": "Por favor, seleccione el proceso",
    "dataSaveDesktop": "Los datos se han guardado en el escritorio.",
    "createAGroup": "Crear grupo",
    "delGroup": "Eliminar grupo",
    "Group": "Grupo",
    "editGroup": "Modificar grupo",
    "groupinfo": "Información del Grupo",
    "moveOutGrouping": "Eliminar del grupo",
    "createANewGroup": "Crear nuevo grupo",
    "unallocatedCore": "Núcleo no asignado",
    "inactiveProcess": "Proceso inactivo",
    "importGroupingScheme": "Importar perfil de agrupación",
    "derivedPacketScheme": "Exportar configuración de grupo",
    "addNowProcess": "Agregar proceso actual en ejecución",
    "displaySystemProcess": "Mostrar procesos del sistema",
    "max64": "Máximo se pueden seleccionar 64 hilos",
    "processName": "Nombre del proceso",
    "chooseCurProcess": "Seleccionar proceso actual",
    "selectNoProcess": "No se ha seleccionado ningún proceso",
    "coreCount": "Núcleos",
    "threadCount": "Hilos",
    "process": "Proceso",
    "plzInputProcessName": "Introduzca el nombre del proceso para añadir manualmente",
    "has_allocation": "Procesos con esquemas de asignación de hilos",
    "not_made": "No ha asignado núcleos a ningún proceso",
    "startUse": "Activar Optimización",
    "stopUse": "Desactivar Optimización",
    "threadAllocation": "Asignación de Hilos",
    "configProcess": "Configuración de proceso",
    "selectThread": "Seleccionar Hilo",
    "hyperthreadingState": "Estado de Hyper-Threading",
    "open": "Activado",
    "notYetUnlocked": "Desactivado",
    "nonhyperthreading": "Sin Hyper-Threading",
    "intervalSelection": "Selección de intervalo",
    "invertSelection": "Invertir selección",
    "description": "Bloquea los procesos del juego en núcleos CPU específicos, aislándolos inteligentemente de interferencias de programas en segundo plano. Aumenta eficazmente el límite máximo de FPS y estabiliza el rendimiento del juego. Reduce el lag repentino y caídas bruscas de frame rate, liberando al máximo el potencial de los procesadores multinúcleo para garantizar una ejecución fluida con altas tasas de frames durante todo el juego.",
    "importSuccess": "Importación exitosa",
    "importFailed": "Importación fallida"
  },
  "InGameMonitor": {
    "onoffingame": "Activar/Desactivar Supervisión en el Juego:",
    "InGameMonitor": "Monitoreo en Juego",
    "CustomMode": "Modo Personalizado",
    "Developing": "En desarrollo...",
    "NewMonitor": "Agregar elemento de monitoreo",
    "Data": "Parámetros",
    "Des": "Nota",
    "Function": "Funcionalidad",
    "Editor": "Editar",
    "Top": "Fijar en la parte superior",
    "Delete": "Eliminar",
    "Use": "Usar",
    "DragToSet": "Una vez invocado el panel, puede arrastrar para configurar",
    "MonitorItem": "Elemento de monitoreo",
    "addMonitorItem": "Añadir elemento de monitoreo",
    "hide": "Ocultar",
    "show": "Mostrar",
    "generalstyle": "Configuración general",
    "restoredefault": "Restablecer predeterminado",
    "arrangement": "Diseño",
    "horizontal": "Horizontal",
    "vertical": "Vertical",
    "monitorposition": "Ubicación de Monitoreo",
    "canquickselectposition": "Seleccione rápidamente una ubicación en el mapa de la izquierda",
    "curposition": "Ubicación Actual:",
    "background": "Fondo",
    "backgroundcolor": "Color de Fondo:",
    "font": "Fuente",
    "fontStyle": "Estilo de fuente",
    "fontsize": "Tamaño de fuente:",
    "fontcolor": "Color de Fuente:",
    "style": "Estilo:",
    "style2": "Estilo",
    "performance": "Rendimiento",
    "refreshTime": "Tiempo de actualización:",
    "goGeneralSetting": "Ir a Configuración general",
    "selectMonitorItem": "Seleccionar elemento de monitoreo",
    "selectedSensor": "Sensor seleccionado:",
    "showTitle": "Mostrar título",
    "hideTitle": "Ocultar título",
    "showStyle": "Modo de visualización:",
    "remarkSize": "Tamaño de nota:",
    "remarkColor": "Color de Nota:",
    "parameterSize": "Tamaño del parámetro:",
    "parameterColor": "Color de Parámetros:",
    "lineChart": "Gráfico de Líneas",
    "lineColor": "Color de línea quebrada:",
    "lineThickness": "Grosor de Línea:",
    "areaHeight": "Altura de la Región:",
    "sort": "Ordenar",
    "displacement": "Desplazamiento:",
    "up": "Mover hacia arriba",
    "down": "Mover hacia abajo",
    "bold": "Negrita",
    "stroke": "Contorno",
    "text": "Texto",
    "textLine": "Texto + Gráfico de Líneas",
    "custom": "Personalizado",
    "upperLeft": "Superior izquierdo",
    "upper": "Centro Superior",
    "upperRight": "Superior derecho",
    "Left": "Centro Izquierda",
    "middle": "Centro",
    "Right": "Centro Derecho",
    "lowerLeft": "Abajo a la izquierda",
    "lower": "Medio Inferior",
    "lowerRight": "Abajo a la derecha",
    "notSupport": "El dispositivo periférico no admite mostrar u ocultar mediante clic",
    "notSupportRate": "La tasa de rendimiento no admite alternar con clic",
    "notFindSensor": "Sensor no encontrado. Haga clic para modificar.",
    "monitoring": "Supervisión",
    "condition": "Condición",
    "bigger": "Mayor que",
    "smaller": "Menor que",
    "biggerThan": "Excede el umbral",
    "biggerThanthreshold": "Mayor que el porcentaje de umbral",
    "smallerThan": "Menor que el umbral",
    "smallerThanthreshold": "Menos del porcentaje umbral",
    "biggerPercent": "Porcentaje de Disminución del Valor Actual",
    "smallerPercent": "Porcentaje de aumento del valor actual",
    "replay": "Función de Replay Instantáneo",
    "screenshot": "Captura de Pantalla",
    "text1": "Valor del sensor cuando",
    "text2": ", y en",
    "text3": "Tiempo de espera",
    "text4": "Si no aparece un valor más alto en los segundos especificados, se activará inmediatamente",
    "text5": "Después de cada activación de repetición, actualice el umbral al valor en el momento del disparo para reducir las activaciones frecuentes",
    "text6": "Mostrar el umbral actual para activar el replay",
    "text7": "Mostrar valores del sensor",
    "text8": "Exceder el umbral inicial",
    "text9": "Por debajo del umbral inicial",
    "text10": "Conteo de umbral inicial",
    "initThreshold": "Umbral inicial",
    "curThreshold": "Umbral actual:",
    "curThreshold2": "Umbral Actual",
    "resetCurThreshold": "Restablecer valor umbral actual",
    "action": "Activar Función",
    "times": "veces",
    "percentage": "Porcentaje",
    "uninstallobs": "Módulo de grabación no descargado",
    "install": "Descargar",
    "performanceAndAudioMode": "Modo de compatibilidad de rendimiento y audio",
    "isSaving": "Guardando",
    "video_replay": "Reproducción instantánea",
    "saved": "Guardado",
    "loadQualitysScheme": "Cargar configuración gráfica",
    "notSet": "No configurado",
    "mirrorEnable": "El filtro está activo",
    "canBeTurnedOff": "Volver",
    "mirrorClosed": "Filtro de juego desactivado",
    "closed": "Cerrado",
    "openMirror": "Activar filtro",
    "wonderfulScenes": "Destacados",
    "VulkanModeHaveProblem": "El modo Vulkan tiene problemas de compatibilidad",
    "suggestDxMode": "Se recomienda cambiar al modo Dx",
    "functionNotSupported": "Esta función no está soportada",
    "NotSupported": "No soportado",
    "gppManualRecording": "GamePP, grabación manual",
    "perfRecordsHaveBeenSaved": "Datos de rendimiento guardados",
    "redoClickF8": "Presione F8 nuevamente para continuar con la grabación.",
    "startIngameMonitor": "Activando la función de monitoreo dentro del juego",
    "inGameMarkSuccess": "La marca en el juego se realizó con éxito",
    "recordingFailed": "Grabación fallida",
    "recordingHasNotDownload": "La función de grabación no está descargada",
    "hotkeyDetected": "Se ha detectado un conflicto de teclas de función",
    "plzEditIt": "Por favor, realice los cambios en el software y luego úselo",
    "onePercentLowFrame": "1% Bajo fotograma",
    "pointOnePercentLowFrame": "0.1% Baja FPS",
    "frameGenerationTime": "Tiempo de generación de marco",
    "curTime": "Hora actual",
    "runTime": "Duración de la ejecución",
    "cpuTemp": "Temperatura de la CPU",
    "cpuUsage": "Uso de la CPU",
    "cpuFreq": "Frecuencia del CPU",
    "cpuPower": "Potencia térmica de la CPU",
    "gpuTemp": "Temperatura de la GPU",
    "gpuUsage": "Uso de la GPU",
    "gpuPower": "Potencia térmica GPU",
    "gpuFreq": "Frecuencia de la GPU",
    "memUsage": "Uso de memoria"
  },
  "LoginArea": {
    "login": "Iniciar sesión",
    "loginOut": "Cerrar sesión",
    "vipExpire": "Vencido",
    "remaining": "Restante",
    "day": "Cielo",
    "openVip": "Activar suscripción",
    "vipPrivileges": "Privilegios de miembro",
    "rechargeRenewal": "Recargar y Renovar",
    "Exclusivefilter": "Filtro",
    "configCloudSync": "Configuración de sincronización en la nube",
    "comingSoon": "Próximamente"
  },
  "GameMirror": {
    "filterStatus": "Estado del filtro",
    "filterPlan": "Preset de Filtro",
    "filterShortcut": "Atajos de filtro",
    "openCloseFilter": "Habilitar/Deshabilitar Filtro:",
    "effectDemo": "Demostración de Efectos",
    "demoConfig": "Configuración de Demostración",
    "AiFilter": "Los efectos del filtro de IA están sujetos a los efectos en el juego",
    "AiFilterFAQ": "Preguntas frecuentes sobre los filtros AI",
    "gamePPAiFilter": "Filtro AI de GamePP",
    "gamePPAiFilterVip": "Filtro de IA exclusivo para VIP de GamePP, ajusta dinámicamente los parámetros del filtro según los escenarios de juego para optimizar los efectos visuales y mejorar la experiencia de juego.",
    "AiMingliangTips": "Brillo AI: Recomendado para usar cuando la pantalla del juego esté demasiado oscura.",
    "AiBrightTips": "AI Vibrante: Se recomienda su uso cuando la pantalla del juego esté demasiado oscura.",
    "AiDarkTips": "AI Atenuación: Recomendado para usar cuando los gráficos del juego sean demasiado vibrantes.",
    "AiBalanceTips": "Equilibrio de IA: Adecuado para la mayoría de los escenarios de juego.",
    "AiTips": "Consejos: El filtro de IA debe utilizarse presionando la tecla de acceso directo en el juego",
    "AiFilterUse": "Por favor, utilice en el juego",
    "AiFilterAdjust": "Ajuste de filtro de AI mediante tecla de acceso directo",
    "Bright": "Vibrante",
    "Soft": "Suave",
    "Highlight": "Resaltar",
    "Film": "Película",
    "Benq": "BenQ",
    "AntiGlare": "Antideslumbrante",
    "HighSaturation": "Alta saturación",
    "Brightness": "Vibrante",
    "Day": "Día",
    "Night": "Noche",
    "Nature": "Natural",
    "smooth": "Detallado",
    "elegant": "Suave",
    "warm": "Tonos Cálidos",
    "clear": "Claro",
    "sharp": "Nitidez",
    "vivid": "Dinámico",
    "beauty": "Vibrante",
    "highDefinition": "Alta Definición",
    "AiMingliang": "AI Brillante",
    "AiBright": "IA Vibrante",
    "AiDark": "AI Atenuado",
    "AiBalance": "Balance de IA",
    "BrightTips": "El filtro vibrante es adecuado para juegos casuales, de acción o de aventura, mejorando la saturación de color para hacer los gráficos más dinámicos y atractivos.",
    "liangTips": "Se recomienda utilizar filtros cuando画面 del juego esté demasiado oscura.",
    "anTips": "Se recomienda usar el filtro cuando la pantalla del juego esté demasiado oscura.",
    "jianyiTips": "El filtro se recomienda usar cuando los gráficos del juego sean demasiado vibrantes.",
    "shiTips": "El filtro es adecuado para la mayoría de las escenas de juego.",
    "shi2Tips": "El filtro es adecuado para juegos casuales, de acción o de aventuras, mejora la saturación de color para hacer que los gráficos sean más vistosos y atractivos.",
    "ruiTips": "Colores suaves del filtro, iluminación equilibrada, adecuada para escenas oníricas, acogedoras o nostálgicas",
    "qingTips": "Tonos brillantes, alto contraste, detalles claros y nítidos, ideal para mostrar escenas vívidas y bien iluminadas.",
    "xianTips": "Ajustes de mayor contraste y brillo garantizan detalles claros en escenas oscuras sin distorsión y una visualización cómoda en escenas brillantes.",
    "dianTips": "Valquiria: Mejore moderadamente el brillo y la saturación de color para lograr una calidad visual cinematográfica",
    "benTips": "Reduce los efectos de luz blanca para minimizar el deslumbramiento en escenas de juego totalmente blancas",
    "fangTips": "Optimizado para juegos de mundo abierto y aventura, mejora brillo y contraste para imágenes más nítidas",
    "jiaoTips": "Adecuado para juegos de rol y simulación, tonos equilibrados, realismo visual mejorado",
    "jieTips": "Optimizado para juegos con historias ricas y emociones sutiles, mejorando detalles y suavidad para lograr visuales más refinados",
    "jingTips": "Optimizado para juegos de acción y competitivos, mejora la claridad y el contraste para imágenes más nítidas",
    "xiuTips": "Óptimo para juegos de curación y ocio, realza los tonos cálidos y suavidad, creando una atmósfera más acogedora",
    "qihuanTips": "Adecuado para escenas con elementos fantásticos y colores vibrantes, aumentando la saturación de color para crear un impacto visual intenso",
    "shengTips": "Intensifique colores y detalles para resaltar la vivacidad y realismo de la escena,",
    "sheTips": "Optimizado para juegos de FPS, puzzles o aventura, mejora los detalles y el contraste para incrementar la realismo del mundo de juego.",
    "she2Tips": "Adecuado para juegos de disparos, carreras o combate, destacando detalles en alta definición y rendimiento dinámico para mejorar la intensidad y efectos visuales de la experiencia de juego",
    "an2Tips": "Mejora la claridad de la escena en entornos oscuros, ideal para escenarios oscuros o nocturnos.",
    "wenTips": "Ideal para juegos artísticos, de aventura o relajantes, crea tonos suaves de color y efectos de luz y sombra para añadir elegancia y sensación de calidez a la escena。",
    "jing2Tips": "Adecuado para juegos de competición, ritmo musical o escenarios urbanos nocturnos, destacando colores vivos y efectos de iluminación,",
    "jing3Tips": "Diseñado para juegos competitivos, de acción o de fantasía, mejora el contraste de color para hacer que las imágenes sean más ricas y dinámicas.",
    "onlyVipCanUse": "Este filtro solo está disponible para usuarios VIP"
  },
  "GameRebound": {
    "noGame": "No hay registros de juegos",
    "noGameRecord": "¡Aún no tienes registros de juegos! ¡Vamos, empieza una partida!",
    "gameDuration": "Duración del juego hoy:",
    "gameElectricity": "Consumo Diario",
    "degree": "Grado",
    "gameCo2": "Emisión de CO₂ del día:",
    "gram": "Clave",
    "manualRecord": "Registro manual",
    "recordDuration": "Duración de grabación:",
    "details": "Detalles",
    "average": "Promedio",
    "minimum": "Valor mínimo",
    "maximum": "Valor Máximo",
    "occupancyRate": "Uso",
    "voltage": "Voltaje",
    "powerConsumption": "Consumo de Energía",
    "start": "Iniciar:",
    "end": "Finalizar",
    "Gametime": "Duración del juego:",
    "Compactdata": "Optimización de Datos",
    "FullData": "Datos Completos",
    "PerformanceAnalysis": "Análisis de rendimiento",
    "PerformanceAnalysis2": "Informe de evento",
    "HardwareStatus": "Estado del hardware",
    "totalPower": "Consumo eléctrico total",
    "TotalEmissions": "Emisión Total",
    "PSS": "Nota: Los datos del gráfico inferior representan valores promedio",
    "FrameGenerationTime": "Tiempo de Generación de Frames",
    "GameResolution": "Resolución del juego",
    "FrameGenerationTimeTips": "Este punto de datos es excepcionalmente alto y no se incluyó en las estadísticas",
    "FrameGenerationTimeTips2": "Este punto de datos es excepcionalmente bajo y, por lo tanto, no se incluye en las estadísticas",
    "noData": "Ninguno",
    "ProcessorOccupancy": "Uso de CPU",
    "ProcessorFrequency": "Frecuencia del procesador",
    "ProcessorTemperature": "Temperatura del procesador",
    "ProcessorHeatPower": "Potencia térmica del procesador",
    "GraphicsCardOccupancy": "Uso de D3D en la tarjeta gráfica",
    "GraphicsCardOccupancyTotal": "Uso total de la tarjeta gráfica",
    "GraphicsCardFrequency": "Frecuencia GPU",
    "GraphicsCardTemperature": "Temperatura de la GPU",
    "GraphicsCardCoreTemperature": "Temperatura del Punto Caliente del Núcleo de la Tarjeta Gráfica",
    "GraphicsCardHeatPower": "Potencia térmica de la GPU",
    "GraphicsCardMemoryTemperature": "Temperatura de la Memoria GPU",
    "MemoryOccupancy": "Uso de Memoria",
    "MemoryTemperature": "Temperatura de Memoria",
    "MemoryPageFaults": "Interrupción de paginación de memoria",
    "Duration": "Duración",
    "Time": "Tiempo",
    "StartStatistics": "Comenzar estadística",
    "Mark": "Etiqueta",
    "EndStatistics": "Finalizar estadísticas",
    "LineChart": "Gráfica de Líneas",
    "AddPointInGame_m1": "Presione dentro del juego",
    "AddPointInGame_m2": "Punto de marcado añadible",
    "LeftMouse": "Clic izquierdo para alternar mostrar/ocultar, clic derecho para cambiar color",
    "DeleteThisLine": "Eliminar esta polilínea",
    "AddCurve": "Agregar curva",
    "AllCurvesAreHidden": "Todos los gráficos de curvas están ocultos",
    "ThereAreSamplingData": "Datos de muestreo totales:",
    "Items": "Entrada",
    "StatisticsData": "Estadísticas",
    "electricity": "Consumo eléctrico",
    "carbonEmission": "Emisiones de Carbono",
    "carbonEmissionTips": "Emisiones de Dióxido de Carbono (kg) = Consumo eléctrico (kWh) × 0.785",
    "D3D": "Uso de D3D:",
    "TOTAL": "Uso total:",
    "Process": "Proceso:",
    "L3Cache": "Caché L3:",
    "OriginalFrequency": "Frecuencia original:",
    "MaximumBoostFrequency": "Turbo Boost Máximo:",
    "DriverVersion": "Versión del Controlador:",
    "GraphicsCardMemoryBrand": "Marca de memoria de video:",
    "Bitwidth": "Ancho de bus",
    "System": "Sistema:",
    "Screen": "Pantalla",
    "Interface": "Interfaz:",
    "Channel": "Canal:",
    "Timing": "Secuencia:",
    "Capacity": "Capacidad:",
    "Generation": "Álgebra",
    "AddPoint_m1": "Pulse en el juego",
    "AddPoint_m2": "Agregar punto de marca",
    "Hidden": "Ocultado",
    "Totalsampling": "Datos Totales de Muestreo:",
    "edition": "Versión del controlador:",
    "MainHardDisk": "Disco Duro Principal",
    "SetAsStartTime": "Establecer como hora de inicio",
    "SetAsEndTime": "Establecer como hora de finalización",
    "WindowWillBe": "La ventana de estadísticas de rendimiento estará en",
    "After": "Cerrar después",
    "NoLongerPopUpThisGame": "Este juego ya no aparecerá",
    "HideTemperatureReason": "Ocultar motivo de temperatura",
    "HideTemperatureReason2": "Ocultar informe de eventos",
    "HideOtherReason": "Ocultar Otras Razones",
    "CPUanalysis": "Análisis de rendimiento de CPU",
    "TemperatureCause": "Causa de temperatura",
    "tempSensorEvent": "Evento del Sensor de Temperatura",
    "NoTemperatureLimitation": "No se ha detectado limitación de rendimiento del CPU por temperatura. Su sistema de refrigeración puede satisfacer completamente los requisitos de este juego.",
    "NoTemperatureLimitation2": "No se detectaron eventos de sensor de temperatura, su sistema de refrigeración puede manejar perfectamente los requisitos de este juego.",
    "performanceis": "En seleccionado",
    "Inside": "Interno,",
    "TheStatisticsTimeOf": "El periodo estadístico cumple las condiciones de activación correspondientes. La razón con mayor frecuencia de activación es",
    "limited": "Porcentaje del tiempo total debido a limitaciones de rendimiento por temperatura",
    "SpecificReasons": "Causas específicas y su proporción en las causas de temperatura:",
    "OptimizationSuggestion": "Sugerencias de optimización:",
    "CPUtemperature": "La temperatura de la CPU está sobrecalentada. Por favor, verifique/mejore el entorno de refrigeración de la CPU.",
    "CPUoverheat": "La CPU se sobrecalienta debido a la alimentación de la placa base. Verifique los ajustes relacionados con la placa base o mejore el entorno de refrigeración.",
    "OtherReasons": "Otras razones",
    "NoPowerSupplyLimitation": "El rendimiento del CPU no está limitado por la alimentación/consumo de energía. Sus configuraciones de consumo de energía en el BIOS pueden satisfacer perfectamente los requisitos de este juego.",
    "PowerSupplyLimitation": "Debido a limitaciones de alimentación/consumo de energía",
    "SpecificReasonsInOtherReasons": "Causa específica y su proporción en otras causas:",
    "PleaseCheckTheMainboard": "Verifique el estado de alimentación de la placa base o ajuste la configuración de consumo de BIOS para resolver limitaciones de rendimiento del CPU causadas por otros factores",
    "CPUcoretemperature": "La temperatura del núcleo ha alcanzado Tj,Max y ha sido limitada",
    "CPUCriticalTemperature": "Temperatura crítica de CPU",
    "CPUCircuitTemperature": "Paquete de CPU/Bus Anular limitado al alcanzar Tj,Max",
    "CPUCircuitCriticalTemperature": "CPU Package/Ring Bus ha alcanzado la temperatura crítica",
    "CPUtemperatureoverheating": "Detección de sobrecalentamiento del CPU, que activará la reducción automática de frecuencia para disminuir la temperatura y prevenir fallos de hardware",
    "CPUoverheatingtriggered": "El CPU ajustará el voltaje y frecuencia al activarse el mecanismo de disipación de calor por sobrecalentamiento",
    "CPUPowerSupplyOverheating": "CPU está limitado debido al sobrecalentamiento grave del suministro de energía de la placa base",
    "CPUPowerSupplyLimitation": "CPU limitado por sobrecalentamiento de la alimentación de la placa base",
    "CPUMaximumPowerLimitation": "El núcleo está sujeto al límite máximo de consumo de energía",
    "CPUCircuitPowerLimitation": "El paquete de CPU/bus en anillo ha alcanzado el límite de potencia",
    "CPUElectricalDesignLimitation": "Activar restricciones de diseño eléctrico (incluye límite de corriente ICCmax, límite de potencia pico PL4, limitación de voltaje SVID, etc.)",
    "CPULongTermPowerLimitation": "Consumo energético prolongado de la CPU alcanzado el límite",
    "CPULongTermPowerinstantaneous": "El consumo de energía instantáneo de la CPU ha alcanzado el límite",
    "CPUPowerLimitation": "Mecanismo de degradación de frecuencia turbo de CPU, generalmente restringido por BIOS o software específico",
    "CPUPowerWallLimitation": "Límite de potencia de la CPU",
    "CPUcurrentwalllimit": "Límite de Muro de Corriente de CPU",
    "AiAgent": "Agente GamePP (AI Agent)",
    "AgentDesc": "Volver a la página principal",
    "fnBeta": "Esta función se encuentra actualmente en fase de prueba por invitación. Su cuenta de GamePP aún no ha recibido acceso a las pruebas.",
    "getAIReport": "Obtener informe de IA",
    "waitingAi": "Esperando a que se complete la generación del informe",
    "no15mins": "Duración del juego inferior a 15 minutos, no se puede obtener un informe AI válido",
    "timeout": "Solicitud al servidor agotó el tiempo de espera",
    "agentId": "Agente ID:",
    "reDo": "Regenerar informe",
    "text2": "Agente de GamePP: Informe de análisis de IA en línea, el siguiente contenido ha sido generado por IA y es solo para referencia.",
    "amdAiagentTitle": "Agente GamePP: Informe de análisis AMD Ryzen AI, el siguiente contenido ha sido generado por IA y es solo para referencia.",
    "noCurData": "No hay datos disponibles actualmente",
    "dataScreening": "Filtrado de datos",
    "dataScreeningDescription": "Esta función está diseñada para excluir las estadísticas de datos de los períodos de juego no efectivos, como la carga del mapa o el tiempo de espera en el lobby. 0 indica que no se realizará ninguna exclusión.",
    "excessivelyHighParameter": "Parámetros excesivos",
    "tooLowParameter": "Parámetros demasiado bajos",
    "theMaximumValueIs": "Valor máximo es",
    "theMinimumValueIs": "Valor mínimo es",
    "exclude": "Excluir",
    "dataStatisticsAtThatTime": "Estadísticas de datos en este momento",
    "itHasBeenGenerated": "Generación completada,",
    "clickToView": "Haga clic para ver",
    "onlineAnalysis": "Análisis en línea",
    "amdRyzenAILocalAnalysis": "AMD Ryzen AI Análisis local",
    "useTerms": "Condiciones de uso:",
    "term1": "1. Procesador Ryzen AI Max o Procesador de la serie Ryzen Al 300",
    "term2": "2.Versión del controlador AMD NPU",
    "term3": "3. Activar gráficos integrados",
    "conformsTo": "Conforme",
    "notInLineWith": "No compatible",
    "theVersionIsTooLow": "Versión obsoleta",
    "canNotUseAmdNpu": "Su configuración no cumple los requisitos. No se puede usar el análisis de entidades de AMD NPU。",
    "unusable": "No disponible",
    "downloadTheFile": "Descargar archivo",
    "downloadSource": "Fuente de descarga:",
    "fileSize": "Tamaño del archivo: aprox. 8,34 GB",
    "cancelDownload": "Cancelar descarga",
    "filePath": "Ubicación del archivo",
    "generateAReport": "Generar informe",
    "fileMissing": "Falta el archivo. Reinstalación necesaria.",
    "downloading": "Descargando...",
    "theModelConfigurationLoadingFailed": "Fallo al cargar la configuración del modelo",
    "theModelDirectoryDoesNotExist": "El directorio del modelo no existe",
    "thereIsAMistakeInReasoning": "Error de deducción",
    "theInputExceedsTheModelLimit": "La entrada excede el límite del modelo",
    "selectModelNotSupport": "El modelo de descarga seleccionado no está soportado",
    "delDirFail": "Error al eliminar el directorio del modelo existente",
    "failedCreateModelDir": "Fallo al crear el directorio del modelo",
    "modelNotBeenFullyDownload": "El modelo no se ha descargado completamente",
    "agentIsThinking": "El agente Jiajia está pensando",
    "reasoningModelFile": "Archivo de modelo para inferencia",
    "modelReasoningTool": "Herramienta de inferencia de modelo"
  },
  "SelectSensor": {
    "DefaultSensor": "Sensor predeterminado",
    "Change": "Cambiar",
    "FanSpeed": "Velocidad del ventilador",
    "MainGraphicsCard": "Tarjeta Gráfica Principal",
    "SetAsMainGraphicsCard": "Establecer como GPU principal",
    "GPUTemperature": "Temperatura de GPU",
    "GPUHeatPower": "Consumo térmico de la GPU",
    "GPUTemperatureD3D": "Uso de D3D en GPU",
    "GPUTemperatureTOTAL": "Uso total de GPU",
    "GPUTemperatureCore": "Temperatura del núcleo GPU Hotspot",
    "MotherboardTemperature": "Temperatura de la Placa Base",
    "MyAttention": "Mis Favoritos",
    "All": "Todo",
    "Unit": "Unidad:",
    "NoAttention": "Sensores no seguidos",
    "AttentionSensor": "Sensores monitoreados (Beta)",
    "GoToAttention": "Ir al Enfoque",
    "CancelAttention": "Dejar de seguir",
    "noThisSensor": "Sin Sensor",
    "deviceAbout": "Relacionado con periféricos",
    "deviceBattery": "Batería del dispositivo externo",
    "testFunction": "Función de prueba",
    "mouseEventRate": "Tasa de sondeo",
    "relatedWithinTheGame": "Relacionado con el juego",
    "winAbout": "Sistema",
    "trackDevicesBattery": "Seguir el nivel de batería de los dispositivos periféricos",
    "ingameRealtimeMouseRate": "Frecuencia de actualización del ratón utilizada en tiempo real durante el juego",
    "notfoundDevice": "No se ha encontrado ningún dispositivo compatible",
    "deviceBatteryNeedMythcool": "Lista de dispositivos con visualización de batería compatible: (requiere Myth.Cool)",
    "vkm1mouse": "Ratón Valkyrie M1",
    "vkm2mouse": "Valkyrie M2 Ratón",
    "vk99keyboard": "Valkyrie 99 Teclado de Eje Magnético",
    "logitechProWireless": "Logitech Pro Wireless",
    "logitechProXSUPERLIGHT": "Logitech PRO X SUPERLIGHT",
    "logitechProXSUPERLIGHT2": "Logitech PRO",
    "logitechPro2LIGHTSPEED": "Logitech PRO 2 LIGHTSPEED",
    "steelSeriesArctisGameBuds": "SteelSeries Arctis GameBuds",
    "steelSeriesArctis7PPlus": "SteelSeries Arctis 7P Plus",
    "razerV3": "Razer Valkyrie V3 Edición Profesional",
    "razerV2": "Razer Viper V2 Edición Profesional",
    "wireless": "Inalámbrico",
    "logitechNeedGhub": "Cuando Logitech no pueda recuperar el modelo del dispositivo, es necesario descargar GHUB",
    "chargingInProgress": "Cargando",
    "inHibernation": "En modo de suspensión"
  },
  "video": {
    "videoRecord": "Grabación de Video",
    "recordVideo": "Video grabado",
    "scheme": "Perfil",
    "suggestScheme": "Plan Recomendado",
    "text1": "Con esta configuración, el tamaño del video de 1 minuto es de aproximadamente",
    "text2": "Esta función utilizará recursos adicionales del sistema",
    "low": "Bajo",
    "mid": "Inicio",
    "high": "Alto",
    "1080p": "Nativo",
    "RecordingFPS": "Grabar FPS",
    "bitRate": "Tasa de bits de video",
    "videoResolution": "Resolución de vídeo",
    "startStopRecord": "Iniciar/Detener Grabación",
    "instantReplay": "Reproducción Instantánea",
    "instantReplayTime": "Duración de reproducción inmediata",
    "showIngame": "Lanzar panel de control en juego",
    "CaptureMode": "Método de captura",
    "gameWindow": "Ventana de Juego",
    "desktopWindow": "Ventana de escritorio",
    "fileSavePath": "Ruta de almacenamiento de archivos",
    "selectVideoSavePath": "Seleccionar Ruta de Guardado de Grabación",
    "diskFreeSpace": "Espacio libre en disco duro:",
    "edit": "Modificar",
    "open": "Abrir",
    "displayMouse": "Mostrar Cursor del Mouse",
    "recordMicrophone": "Grabar micrófono",
    "gameGraphics": "Visualización Original del Juego"
  },
  "Setting": {
    "common": "General",
    "personal": "Personalización",
    "messageNotification": "Notificaciones",
    "sensorReading": "Lecturas del Sensor",
    "OLEDscreen": "Protección contra el brillo OLED",
    "performanceStatistics": "Estadísticas de Rendimiento",
    "shortcut": "Acceso directo",
    "ingameSetting": "Guardar configuración del juego",
    "other": "Otro",
    "otherSettings": "Otras Configuraciones",
    "GeneralSetting": "Configuración General",
    "softwareVersion": "Versión del software",
    "checkForUpdates": "Buscar actualizaciones",
    "updateNow": "Actualizar ahora",
    "currentVersion": "Versión Actual",
    "latestVersion": "Última versión",
    "isLatestVersion": "La versión actual ya es la más reciente.",
    "functionModuleUpdate": "Actualización del Módulo Funcional",
    "alwaysUpdateModules": "Mantener todos los módulos de función instalados en la última versión",
    "lang": "Idioma",
    "bootstrap": "Iniciar automáticamente",
    "powerOn_m1": "Iniciar",
    "powerOn_m2": "Autoriniciar después de segundos",
    "defaultDelay": "Valor predeterminado: 40 segundos",
    "followSystemScale": "Seguir escala del sistema",
    "privacySettings": "Configuración de privacidad",
    "JoinGamePPPlan": "Únase al Programa de Mejora de la Experiencia del Usuario GamePP",
    "personalizedSetting": "Personalización",
    "restoreDefault": "Restablecer valores predeterminados",
    "color": "Color",
    "picture": "Imagen",
    "video": "Vídeo",
    "browse": "Navegar",
    "clear": "Limpiar",
    "mp4VideoOrPNGImagesCanBeUploaded": "Puede cargar videos MP4 o imágenes PNG",
    "transparency": "Transparencia",
    "backgroundColor": "Color de Fondo",
    "textFont": "Fuente del texto principal",
    "message": "Mensaje",
    "enableInGameNotifications": "Habilitar notificaciones en el juego",
    "messagePosition": "Posición de visualización en el juego",
    "leftTop": "Esquina superior izquierda",
    "leftCenter": "Centro izquierdo",
    "leftBottom": "Esquina Inferior Izquierda",
    "rightTop": "Esquina superior derecha",
    "rightCenter": "Centro Derecho",
    "rightBottom": "Esquina inferior derecha",
    "noticeContent": "Contenido de notificación",
    "gameInjection": "Inyección de Juego",
    "ingameShow": "Mostrar en juego",
    "inGameMonitoring": "Supervisión en el juego",
    "gameFilter": "Filtro de juego",
    "start": "Iniciar",
    "endMarkStatistics": "Estadísticas de Marca de Finalización",
    "readHwinfoFail": "HWINFO Fallo al leer la información del hardware",
    "dataSaveDesktop": "Los datos se han guardado en el portapapeles y el archivo de escritorio",
    "TheSensorCacheCleared": "Datos en caché del sensor borrados",
    "defaultSensor": "Sensor predeterminado",
    "setSensor": "Seleccionar Sensor",
    "refreshTime": "Hora de refresco de datos",
    "recommend": "Predeterminado",
    "sensorMsg": "Cuanto más corto sea el intervalo de tiempo, mayor será el consumo de rendimiento. Seleccione con cuidado.",
    "exportData": "Exportar datos",
    "exportHwData": "Exportar datos de información de hardware",
    "sensorError": "Lectura anormal del sensor",
    "clearCache": "Limpiar caché",
    "littleTips": "Nota: El informe de rendimiento se generará 2 minutos después de iniciar el juego",
    "disableAutoShow": "Desactivar la ventana emergente automática de estadísticas de rendimiento",
    "AutoClosePopUpWindow_m1": "La ventana de estadísticas de rendimiento se cerrará automáticamente después del tiempo especificado:",
    "AutoClosePopUpWindow_m2": "segundos",
    "abnormalShutdownReport": "Informe de apagado inesperado",
    "showWeaAndAddress": "Mostrar clima e información de ubicación",
    "autoScreenShots": "Capturar automáticamente la pantalla del juego al marcar",
    "keepRecent": "Número de registros recientes a conservar:",
    "noLimit": "Ilimitado",
    "enableInGameSettingsSaving": "Habilitar Guardado de Configuración en el Juego",
    "debugMode": "Modo de depuración",
    "enableDisableDebugMode": "Activar/desactivar modo de depuración",
    "audioCompatibilityMode": "Modo de compatibilidad de audio",
    "quickClose": "Cierre Rápido",
    "closeTheGameQuickly": "Cerrar rápido el proceso del juego",
    "cancel": "Cancelar",
    "confirm": "Confirmar",
    "MoveInterval_m1": "El monitoreo del escritorio y dentro del juego se moverá ligeramente:",
    "MoveInterval_m2": "minutos",
    "text3": "Al salir del juego, no se mostrará la ventana de informe de rendimiento, solo se conservarán los registros históricos",
    "text5": "Se generará automáticamente un informe tras un cierre inesperado. Habilitar esta función consumirá recursos del sistema adicionales.",
    "text6": "El uso de funciones de atajo puede generar conflictos con otros atajos de juego, configure con precaución.",
    "text7": "Establecer el atajo de teclado como \"Ninguno\", utilice la tecla Retroceso",
    "text8": "Conservar el estado de los filtros, la supervisión en juego y otras funciones durante la ejecución del juego según el nombre del proceso",
    "text9": "Activado registrará continuamente registros de ejecución; desactivado limpiará los archivos de registro (Se recomienda desactivar)",
    "text10": "Tras activar esta opción, los sensores de la placa base no estarán disponibles para resolver problemas de audio causados por GamePP",
    "text11": "Presione Alt+F4 dos veces consecutivas para salir rápidamente del juego actual",
    "text12": "¿Desea continuar? Este modo requiere reiniciar GamePP.",
    "openMainUI": "Mostrar aplicación",
    "setting": "Configuración",
    "feedback": "Retroalimentación de problemas",
    "help": "Ayuda",
    "sensorReadingSetting": "Ajustes de lectura del sensor",
    "searchlanguage": "Idioma de búsqueda"
  },
  "GamePlusOne": {
    "year": "Año",
    "month": "Mes",
    "day": "Día",
    "success": "Éxito",
    "fail": "Fallido",
    "will": "Actual",
    "missedGame": "Juegos perdidos",
    "text1": "Monto, aprox. ￥",
    "text2": "Total de Juegos Obtenidos",
    "text3": "Versión",
    "gamevalue": "Valor del Juego",
    "gamevalue1": "Obtener",
    "total": "Total Reclamado",
    "text4": "juegos, ahorro acumulado",
    "text6": "Producto, Valor",
    "Platformaccountmanagement": "Gestión de cuentas de plataforma",
    "Missed1": "(No reclamado)",
    "Received2": "(Éxito al recibir)",
    "Receivedsoon2": "Disponible ahora",
    "Receivedsoon": "Disponible ahora",
    "Missed": "Recogida Perdida",
    "Received": "Reclamado con éxito",
    "Getaccount": "Obtener cuenta",
    "Worth": "Valor",
    "Auto": "Automático",
    "Manual": "Manual",
    "Pleasechoose": "Seleccione un juego",
    "Receive": "Recibir ahora",
    "Selected": "Seleccionado",
    "text5": "Juegos",
    "Automatic": "Auto reclamando...",
    "Collecting": "Recogiendo...",
    "ReceiveTimes": "Veces recogidas este mes",
    "Thefirst": "º",
    "Week": "Semana",
    "weekstotal": "53 semanas en total",
    "Return": "Inicio",
    "Solutionto": "Fallo al vincular la cuenta - Solución",
    "accounts": "Número de cuentas vinculadas",
    "Addaccount": "Agregar cuenta",
    "Clearcache": "Limpiar caché",
    "Bindtime": "Tiempo de vinculación",
    "Status": "Estado",
    "Normal": "Normal",
    "Invalid": "Inválido",
    "text7": "juegos, en total hemos ahorrado",
    "Yuan": "Yuan",
    "untie": "Desvincular",
    "disable": "Desactivar",
    "enable": "Habilitar",
    "gamePlatform": "Plataforma de juegos",
    "goStorePage": "Ir a la página de la tienda",
    "receiveEnd": "Después del plazo límite",
    "loginPlatformAccount": "Cuenta de plataforma iniciada",
    "waitReceive": "Pendiente de reclamar",
    "receiveSuccess": "Recogido con éxito",
    "accountInvalid": "Cuenta expirada",
    "alreadyOwn": "Ya Poseído",
    "networkError": "Anomalía de Red",
    "noGame": "Sin Juego Base",
    "manualReceiveInterrupt": "Interrupción de Adquisición Manual",
    "receiving": "Reclamando",
    "agree": "Yo acepto unirme al programa de reclamación gratuita de GamePP.",
    "again": "Reclamar nuevamente"
  },
  "shutdownTimer": {
    "timedShutdown": "Apagado programado",
    "currentTime": "Hora Actual:",
    "setCountdown": "Configurar cuenta regresiva",
    "shutdownInSeconds": "Apagar en X segundos",
    "shutdownIn": "Después del apagado",
    "goingToBe": "Se realizará",
    "executionPlan": "Plan de Ejecución",
    "startTheClock": "Iniciar temporizador",
    "stopTheClock": "Cancelar plan",
    "isShuttingDown": "Ejecutando plan de apagado programado:",
    "noplan": "No hay plan de apagado actual",
    "hour": "Hora",
    "min": "Minuto",
    "sec": "Segundo",
    "ms": "Milisegundo",
    "year": "Año",
    "month": "Mes",
    "day": "Día",
    "hours": "Hora"
  },
  "screenshotpage": {
    "screenshot": "Captura de pantalla",
    "screenshotFormat": "Diseñado específicamente para la captura de pantallas de juegos, admite guardar en formatos JPG/PNG/BMP, permite capturar rápidamente las pantallas de los juegos y garantiza una salida en alta resolución sin pérdida de calidad",
    "Turnon": "Activar captura automática, cada",
    "seconds": "Segundo",
    "takeScreenshot": "Ejecutar una captura automática de pantalla",
    "screenshotSettings": "Esta configuración no tiene efecto cuando se activa en el juego",
    "saveGameFilterAndMonitoring": "Guardar los efectos 'Filtro de juego' y 'Supervisión en juego' en la captura de pantalla",
    "disableScreenshotSound": "Desactivar notificación de sonido de captura de pantalla",
    "imageFormat": "Formato de imagen",
    "recommended": "Recomendar",
    "viewingdetails": "Mantiene los detalles de calidad de imagen, tamaño moderado, adecuado para ver detalles",
    "saveSpace": "Calidad de imagen compresible, tamaño mínimo, ahorro de espacio",
    "ultraQuality": "Calidad de imagen ultra HD y sin compresión, tamaño de archivo muy grande. Recomendado para jugadores que buscan máxima calidad visual en sus partidas.",
    "fileSavePath": "Ruta de guardado de archivo",
    "hardDiskSpace": "Espacio libre en disco duro:",
    "minutes": "Minuto"
  },
  "DesktopMonitoring": {
    "desktopMonitoring": "Monitoreo del Escritorio",
    "SomeSensors": "Recomendamos algunos sensores para monitoreo. Puede eliminarlos o agregar otros",
    "AddComponent": "Añadir nuevo componente",
    "Type": "Tipo",
    "Remarks": "Nota",
    "AssociatedSensor": "Asociar sensor",
    "Operation": "Operación",
    "Return": "Volver",
    "TimeSelection": "Selección de tiempo:",
    "Format": "Formato:",
    "Rule": "Regla：",
    "Coordinate": "Coordenadas:",
    "CustomTextContent": "Contenido de texto personalizado:",
    "SystemTime": "Hora del sistema",
    "China": "China",
    "Britain": "Reino Unido",
    "America": "Estados Unidos",
    "Russia": "Rusia",
    "France": "Francia",
    "DateAndTime": "Fecha y hora",
    "Time": "Tiempo",
    "Date": "Fecha",
    "Week": "Día de la semana",
    "DateAndTimeAndWeek": "Fecha+Hora+Día de la semana",
    "TimeAndWeek": "Hora+Día de la semana",
    "Hour12": "Formato de 12 horas",
    "Hour24": "formato de 24 horas",
    "SelectSensor": "Seleccionar sensor:",
    "AssociatedSensor1": "Conectar el sensor:",
    "SensorUnit": "Unidad del sensor：",
    "Second": "Segundo:",
    "Corner": "Esquinas redondeadas:",
    "BackgroundColor": "Color de fondo :",
    "ProgressColor": "Color de progreso:",
    "Font": "Fuente：",
    "SelectFont": "Seleccionar fuente",
    "FontSize": "Tamaño de fuente:",
    "Color": "Color：",
    "Style": "Estilo:",
    "Bold": "Negrita",
    "Italic": "Cursiva",
    "Shadow": "Sombra",
    "ShadowPosition": "Posición de la Sombra：",
    "ShadowEffect": "Efectos de sombra：",
    "Blur": "Desenfoque",
    "ShadowColor": "Color de sombra：",
    "SelectFromLocalFiles": "Seleccionar desde archivos locales:",
    "UploadImageVideo": "Subir imagen/video",
    "UploadSVGFile": "Cargar un archivo SVG",
    "Width": "Anchura:",
    "Height": "Alto: ",
    "Effect": "Efecto:",
    "Rotation": "Rotación:",
    "WhenTheSensorValue": "El valor del sensor es mayor que",
    "conditions": "Cuando no se cumple la condición (sin rotación cuando el valor del sensor es 0)",
    "Clockwise": "En sentido horario",
    "Counterclockwise": "Antihorario",
    "QuickRotation": "Rotación rápida",
    "SlowRotation": "Rotación lenta",
    "StopRotation": "Detener la rotación",
    "StrokeColor": "Color del contorno：",
    "Path": "Ruta",
    "Color1": "Color",
    "ChangeColor": "Cambiar color",
    "When": "Cuando",
    "SensorValue": "Valor del sensor mayor o igual a",
    "SensorValue1": "Valor del sensor menor o igual que",
    "SensorValue2": "El valor del sensor es igual",
    "MonitoringSettings": "Configuración de monitoreo",
    "RestoreDefault": "Restablecer valores predeterminados",
    "Monitor": "Monitor",
    "AreaSize": "Tamaño del área",
    "Background": "Fondo",
    "ImageVideo": "Imágenes/Vídeos",
    "PureColor": "Color sólido",
    "Select": "Seleccionar",
    "ImageVideoDisplayMode": "Modo de visualización de imágenes/vídeo",
    "Transparency": "Transparencia",
    "DisplayPosition": "Mostrar posición",
    "Stretch": "Estirar",
    "Fill": "Rellenar",
    "Adapt": "Ajustar",
    "SelectThePosition": "Haga clic en la celda para seleccionar rápidamente la ubicación",
    "CurrentPosition": "Ubicación actual:",
    "DragLock": "Bloqueo de Arrastre",
    "LockMonitoringPosition": "Posición del monitor bloqueada (Después de bloquear, no se puede arrastrar el monitor)",
    "Unlockinterior": "Activar el arrastre de elementos internos",
    "Font1": "Fuente",
    "GameSettings": "Configuración del juego",
    "CloseDesktopMonitor": "Desactive automáticamente el seguimiento del escritorio cuando se esté ejecutando el juego.",
    "OLED": "Protección contra Quemadura de OLED",
    "Display": "Mostrar",
    "PleaseEnterContent": "Introduzca el contenido",
    "NextStep": "Siguiente",
    "Add": "Añadir",
    "StylesForYou": "Hemos recomendado algunos estilos de monitoreo para usted. Puede seleccionarlos y aplicarlos. Se añadirán más estilos en el futuro.",
    "EditPlan": "Editar perfil",
    "MonitoringStylePlan": "Esquema de estilo de monitoreo",
    "AddDesktopMonitoring": "Agregar monitoreo del escritorio",
    "TextLabel": "Etiqueta de texto",
    "ImageVideo1": "Imágenes, Vídeos",
    "SensorGraphics": "Gráficos del sensor",
    "SensorData": "Datos del sensor",
    "CustomText": "Texto personalizado",
    "DateTime": "Fecha y hora",
    "Image": "Imagen",
    "Video": "Vídeo",
    "SVG": "SVG",
    "ProgressBar": "Barra de progreso",
    "Graphics": "Gráficos",
    "UploadImage": "Cargar imagen",
    "UploadVideo": "Subir video",
    "RealTimeMonitoring": "Monitoreo en tiempo real de temperaturas y uso de CPU/GPU, personalización de diseño con arrastrar y soltar, ajustes de estilo personalizado – domine el rendimiento y la estética del escritorio",
    "Chart": "Gráfico",
    "Zigzagcolor": "Color de la línea (Inicio)",
    "Zigzagcolor1": "Color de línea (Extremo final)",
    "Zigzagcolor2": "Color del área del gráfico de líneas (Inicio)",
    "Zigzagcolor3": "Color del área del gráfico de líneas (fin)",
    "CustomMonitoring": "Monitoreo personalizado"
  }
}
//messageEnd 
 export default es 