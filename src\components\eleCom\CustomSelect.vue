<template>
  <div class="custom-select" :style="{'--height':height + 'px',width:width+'px',height:height+'px'}">
  <ElSelect
      :value="modelValue"
      @change="handleChange"
      :disabled="disabled"
      :multiple="multiple"
      :filterable="filterable"
      :style="{ width: `${width}px` }"
  >
    <ElOption
        v-for="(item, index) in options"
        :key="index"
        :label="item.label"
        :value="item.value"
    />
  </ElSelect>
  </div>
</template>

<script setup lang="ts">
import { ElSelect, ElOption } from 'element-plus';
import {defineProps, defineEmits} from 'vue';

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: '',
  },
  options: {
    type: Array as () => Array<{ label: string; value: any }>,
    default: () => [],
  },
  width: {
    type: Number,
    default: 120,
  },
  height: {
    type: Number,
    default: 30,
  },
  disabled: {
    type: Boolean,
    default: false
  },
  multiple: {
    type: Boolean,
    default: false
  },
  filterable: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['update:modelValue']);

const handleChange = (value: any) => {
  emit('update:modelValue', value);
};
</script>
<style lang="scss">

.custom-select {
  --height: 20px;
  .el-select__wrapper {
    height: var(--height);
  }
}
</style>



<!--使用案例-->

<!--<custom-select-->
<!--    :width="100"-->
<!--    :height="300"-->
<!--    v-model="selectedValue"-->
<!--    :options="selectOptions"-->
<!--    @change="onSelectChange"-->
<!--/>-->

