<!-- TableHeader.vue -->
<template>
    <header>
        <div class="name" @click="setSort('name')" :class="{ 'sortBy': data.sortBy[0] === 'name' }">
            <span class="iconfont icon-hideshow" :class="{ 'ro180': data.sortBy[1] === 'asc' }"
                v-show="data.sortBy[0] === 'name'"></span>
            <span @click.stop=""><el-checkbox v-model="data.full_checkbox"
                    @change="handleFullCheck"></el-checkbox></span>
            <span>{{$t('psc.processName')}}</span>
        </div>
        <div class="thread" style="color: #777777;" @click="setSort('thread')"
            :class="{ 'sortBy': data.sortBy[0] === 'thread' }">
            <span class="iconfont icon-hideshow" :class="{ 'ro180': data.sortBy[1] === 'asc' }"
                v-show="data.sortBy[0] === 'thread'"></span>
            {{$t('psc.threadCount')}}
        </div>
        <div class="cpu" @click="setSort('cpu_usage')" :class="{ 'sortBy': data.sortBy[0] === 'cpu_usage' }">
            <span class="iconfont icon-hideshow" :class="{ 'ro180': data.sortBy[1] === 'asc' }"
                v-show="data.sortBy[0] === 'cpu_usage'"></span>
            CPU{{$t('hardwareInfo.occupied')}}
        </div>
        <div class="cpu-pp" @click="setSort('cpu_pp')" :class="{ 'sortBy': data.sortBy[0] === 'cpu_pp' }">
            <span class="iconfont icon-hideshow" :class="{ 'ro180': data.sortBy[1] === 'asc' }"
                v-show="data.sortBy[0] === 'cpu_pp'"></span>
            <el-tooltip :hide-after="0" placement="top" popper-class="pp-tooltip" effect="light"
                :content="$t('psc.ppDesc')">
                <span>
                    <span class="iconfont icon-quest"></span>
                    CPU {{$t('psc.ppValue')}}
                </span>
            </el-tooltip>
        </div>
        <div class="gpu" @click="setSort('gpu_usage')" :class="{ 'sortBy': data.sortBy[0] === 'gpu_usage' }">
            <span class="iconfont icon-hideshow" :class="{ 'ro180': data.sortBy[1] === 'asc' }"
                v-show="data.sortBy[0] === 'gpu_usage'"></span>
            GPU{{$t('hardwareInfo.occupied')}}
        </div>
        <div class="gpu-pp" @click="setSort('gpu_pp')" :class="{ 'sortBy': data.sortBy[0] === 'gpu_pp' }">
            <span class="iconfont icon-hideshow" :class="{ 'ro180': data.sortBy[1] === 'asc' }"
                v-show="data.sortBy[0] === 'gpu_pp'"></span>
            <el-tooltip :hide-after="0" placement="top" popper-class="pp-tooltip" effect="light"
                :content="$t('psc.ppDesc')">
                <span>
                    <span class="iconfont icon-quest"></span>
                    GPU {{$t('psc.ppValue')}}
                </span>
            </el-tooltip>
        </div>
        <div class="mem" @click="setSort('mem_usage')" :class="{ 'sortBy': data.sortBy[0] === 'mem_usage' }">
            <span class="iconfont icon-hideshow" :class="{ 'ro180': data.sortBy[1] === 'asc' }"
                v-show="data.sortBy[0] === 'mem_usage'"></span>
            {{$t('GameRebound.MemoryOccupancy')}}
        </div>
        <div class="mem-pp" @click="setSort('mem_pp')" :class="{ 'sortBy': data.sortBy[0] === 'mem_pp' }">
            <span class="iconfont icon-hideshow" :class="{ 'ro180': data.sortBy[1] === 'asc' }"
                v-show="data.sortBy[0] === 'mem_pp'"></span>
            <el-tooltip :hide-after="0" placement="top" popper-class="pp-tooltip" effect="light"
                        :content="$t('psc.ppDesc')">
                <span>
                    <span class="iconfont icon-quest"></span>
                    {{$t('hardwareInfo.memory')}} {{$t('psc.ppValue')}}
                </span>
            </el-tooltip>
        </div>
        <div class="group" @click="setSort('groupName')" :class="{ 'sortBy': data.sortBy[0] === 'groupName' }">
            <span class="iconfont icon-hideshow" :class="{ 'ro180': data.sortBy[1] === 'asc' }"
                v-show="data.sortBy[0] === 'groupName'"></span>
            {{$t('psc.groupinfo')}}
        </div>
        <div class="controls">{{$t('psc.controls')}}</div>
    </header>
</template>

<script lang="ts">
export default {
    props: {
        data: {
            type: Object,
            required: true
        },
        setSort: {
            type: Function,
            required: true
        },
        handleFullCheck: {
            type: Function,
            required: true
        }
    },
    data() {
        return {
        }
    },
}
</script>

<style scoped lang="scss">
header {
    display: flex;
    flex-flow: row nowrap;
    width: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) + var(--all-cpu-pp-w) + var(--all-gpu-w) + var(--all-gpu-pp-w) + var(--all-mem-w) + var(--all-mem-pp-w) + var(--all-group-w) + var(--all-controls-w));
    height: 30px;
    background-color: #262734;
    border-radius: 4px 4px 0 0;

    .name,
    .thread,
    .cpu,
    .cpu-pp,
    .gpu,
    .gpu-pp,
    .mem,
    .mem-pp,
    .group {
        cursor: pointer;
        position: relative;

        &.sortBy {
            background-color: #393B50;
            color: #ffffff;
        }

        .icon-hideshow {
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 8px;
            display: inline-block;
        }

        .ro180 {
            transform: translateX(-50%) rotate(180deg);
        }
    }
}

.name,
.thread,
.cpu,
.cpu-pp,
.gpu,
.gpu-pp,
.mem,
.mem-pp,
.group,
.controls {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: flex-end;
    padding-right: 5px;
    gap: 10px;
    white-space: nowrap;
    overflow: hidden;
}

.cpu-pp,
.gpu-pp,
.mem-pp {
    .iconfont {
        font-size: 10px;
        display: inline-block;
        transform: translateY(-5px);
    }
}

.thread {
    color: #777777;

    &.is-active {
        color: #35D57D;
    }
}

.controls {
    justify-content: space-between;
    padding-left: 5px;

    span,
    .iconSort {
        display: flex;
        flex-flow: row nowrap;
        align-items: center;
        gap: 5px;
        cursor: pointer;
    }
}

.name {
    text-align: left;
    width: var(--all-name-w);
    justify-content: flex-start;
    padding-left: 10px;
    padding-right: 0;

    img {
        width: 20px;
        height: 20px;
    }

    .process-name {
        width: calc(var(--all-name-w) - 90px);
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        color: #888888;
    }

    .point {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #35D57D;

        &.inactive {
            background-color: #666666;
        }
    }
}

.thread {
    width: var(--all-thread-w);
}

.cpu {
    width: var(--all-cpu-w);
}

.cpu-pp {
    width: var(--all-cpu-pp-w);
}

.gpu {
    width: var(--all-gpu-w);
}

.gpu-pp {
    width: var(--all-gpu-pp-w);
}

.mem {
    width: var(--all-mem-w);
}

.mem-pp {
    width: var(--all-mem-pp-w);
}

.group {
    width: var(--all-group-w);
    justify-content: flex-start;
    padding-left: 5px;
    padding-right: 0;
}

.controls {
    width: var(--all-controls-w);
}
</style>
