import {hardware} from "@/modules/Game_Home/stores";
import {ref, onBeforeMount, reactive, watch} from "vue";
import {findValueByKeyAndType, SensorAddAverageData} from '@/uitls/sensor'

export function useSensor() {

// @ts-ignore
  const gamepp: any = window.gamepp as any
  const $store = hardware()

  let step = ref(0)

  let PE_status = ref('')

  const zoomValue = ref(1)
  watch(zoomValue,(newValue)=>{
    document.body.style.zoom = newValue
  })

  let SensorInfoData = reactive<any>({});
  let cpuName = ref(""), gpuName0 = ref(""), gpuName1 = ref(""), gpuName2 = ref(""), motherboardName = ref("");
  let gpu_index = ref(0);
  let setMasterGPUShow = ref(true)
  let collectedSensor = ref<Array<any>>([]) // 收藏的传感器
  let diskArr = ref<Array<any>>([]) // 硬盘
  let data_arr = reactive<any>({})
  onBeforeMount(() => {
    InitPage()
    gpu_index.value = parseInt(window.localStorage.getItem('gpu_index')!, 10) || 0
  })

  async function getSensorStatistics () {
    let hw_list_dataStr = window.localStorage.getItem('hw_list_data')
    if (!hw_list_dataStr) return
    const data = {
      hw_list: JSON.parse(hw_list_dataStr),
      version: gamepp.getPlatformVersion.sync()
    }
    console.log(data);
    try {
      const encodeData = gamepp.user.getEncodeInfo.sync(JSON.stringify(data))
      const resultStr = window.localStorage.getItem('sensorStatistics');
      console.log(resultStr)
      if (resultStr) {
        if (resultStr) {
          let result = JSON.parse(resultStr)
          if (result.default) {
            ModifyUserSensorToServerData(result.default)
          }
        }
      }
    } catch {
    }
  }

  async function ModifyUserSensorToServerData (data:any) {
    let chooseSensor_list_xStr = window.localStorage.getItem('chooseSensor_list_v1')
    if (chooseSensor_list_xStr) {
      let chooseSensor_list = JSON.parse(chooseSensor_list_xStr)
      let SensorInfoStr = await gamepp.hardware.getSensorInfo.promise()
      const SensorInfoOriginal = JSON.parse(SensorInfoStr);
      const SensorInfoKeys = Object.keys(SensorInfoOriginal)
      let SensorInfo = SensorAddAverageData(SensorInfoOriginal, SensorInfoKeys)
      let isSet = false
      for (let type in data) {
        if (type === 'motherboard_appoint') {
          let motherboard_appoint = data[type]
          if (Array.isArray(motherboard_appoint) && motherboard_appoint.length > 0) {
            (window as any).motherboard_appoint = motherboard_appoint
          }
        }else{
          for (let attribute in data[type]) {
            let SenName = data[type][attribute]
            let attribute_name = attribute
            if (attribute === 'temperature') {attribute_name = 'temp'}
            let type_name = type
            if (type === 'board') {type_name = 'Mainboard'}
            const Data = chooseSensor_list[type + 'Data']?.find((item:any) => item.name === attribute_name);
            if (Data && Data.isDef) {
              if (type_name !== 'Mainboard') {
                type_name = type_name.toUpperCase()
              }
              const sensor_index = getSensorIndexFromName(SensorInfo, type_name, SenName, attribute)
              if (sensor_index) {
                isSet = true
                Data.isDefServer = true
                Data.keyName = SenName
                Data.groupIndex = sensor_index[0]
                Data.itemIndex = sensor_index[1]
              }
            }
          }
        }

      }
      if (isSet) {
        window.localStorage.setItem('chooseSensor_list_v1', JSON.stringify(chooseSensor_list))
      }
    }
  }
  function getSensorIndexFromName (data:any, key:any, keySubstring:any, type:any) {
    if (key === 'Mainboard') {
      const keywords = ['CPU', 'GPU', 'Memory', 'S.M.A.R.T', 'System:', 'Windows Hardware', 'Drive', 'Network', 'DIMM','ACPI:']
      const filteredData:any = {}
      for (const key1 in data) {
        let shouldKeep = true
        for (const keyword of keywords) {
          if (data[key1] === null || key1.toLowerCase().includes(keyword.toLowerCase())) {
            shouldKeep = false
            break
          }
        }
        if (!shouldKeep) {
          filteredData[key1] = data[key1]
        } else {
          filteredData['Mainboard ' + key1] = data[key1]
        }
      }
      data = filteredData
    }
    const keys = Object.keys(data);
    for (let i = 0; i < keys.length; i++) {
      const parentKey = keys[i];
      const arr = data[parentKey];
      if (data[parentKey] && (parentKey.includes(key))) {
        for (const [j, obj] of arr.entries()) {
          const [itemKey, itemValue] = Object.entries(obj)[0];
          if (itemKey.includes(keySubstring) && type.includes(itemValue.type)) {
            return [i, j];
          }
        }
      }
    }
    return null
  }

  async function InitPage() {
    getSensorStatistics()

    let CPUPEStatus = window.localStorage.getItem('CPU_PE_Status');

    if (CPUPEStatus === 'PE') {
      PE_status.value = 'PE'
    } else if (CPUPEStatus === 'P') {
      PE_status.value = 'P'
    } else if (CPUPEStatus === 'E') {
      PE_status.value = 'E'
    } else {
      PE_status.value = ''
    }

    //监听关注传感器修改
    window.addEventListener('storage', event => {
      if (event.key === 'collected_sensor_list') {
        MyAttention()
      }
    });

    try {
      gamepp.webapp.onInternalAppEvent.addEventListener((value: any) => {
        console.log(value)
        if (value.action === 'ChangeMainPageSensor') {
          let chooseSensor_listStr = window.localStorage.getItem('chooseSensor_list_v1')
          if (chooseSensor_listStr) {
            let chooseSensorList = JSON.parse(chooseSensor_listStr)
            const type = value.Changeindex.type
            const index = value.Changeindex.key
            const chang_index1 = value.val.OutIndex
            const chang_index2 = value.val.InnerIndex
            const chang_name = value.val.name
            const chang_unit = value.val.unit
            const chang_outKey = value.val.outKey
            const chang_inKey = value.val.inKey
            chooseSensorList[type][index].Unit = chang_unit
            chooseSensorList[type][index].groupIndex = chang_index1
            chooseSensorList[type][index].itemIndex = chang_index2
            chooseSensorList[type][index].isDef = false
            chooseSensorList[type][index].keyName = chang_name
            chooseSensorList[type][index].outKey = chang_outKey
            chooseSensorList[type][index].inKey = chang_inKey
            const mergedObj: any = {}
            mergedObj.boardData = chooseSensorList.boardData
            mergedObj.cpuData = chooseSensorList.cpuData
            mergedObj.diskData = chooseSensorList.diskData
            mergedObj.gpu0Data = chooseSensorList.gpu0Data
            mergedObj.gpu1Data = chooseSensorList.gpu1Data
            mergedObj.gpu2Data = chooseSensorList.gpu2Data
            mergedObj.memoryData = chooseSensorList.memoryData
            mergedObj.networkData = chooseSensorList.networkData


            mergedObj.default = false
            window.localStorage.setItem('chooseSensor_list_v1', JSON.stringify(mergedObj))

            // 上传选择传感器到服务器
            if (!(['networkData', 'diskData'].includes(type))) {
              const hw_name = (Object.keys(SensorInfoData))[chang_index1]
              let hw_list_dataStr = window.localStorage.getItem('hw_list_data')
              let hw_list_data = JSON.parse(hw_list_dataStr!)
              UploadSensorDataServer(hw_name, chang_name, chang_unit, motherboardName.value, cpuName.value, hw_list_data.gpu_name[0])
            }
          }
        }
      })
    } catch (err) {
      // console.log(err)
    }
    //setInterval setTimeout
    GetHWTempInfo(true)
    const bc = new BroadcastChannel('bg_sensor_data');
    bc.onmessage = () => {
      GetHWTempInfo(false)
    }

    window.onkeydown = function (e) {
      if (e.keyCode === 27) {
        GPP_WindowClose('hardware_setupsensor')
      }
    }

    initZoom()
  }

  async function initZoom() {
    try {
      const zoomWithSystem = gamepp.setting.getInteger.sync(313)
      if (zoomWithSystem === 1) {
        // 设置body zoom
        // document.body.style.zoom = zoomValue.value
        zoomValue.value = await gamepp.display.getScaleFromWindowInMonitor.promise();
        gamepp.webapp.windows.setMinimumSize.sync('hardware_setupsensor', Math.floor(692 * zoomValue.value),Math.floor(712 * zoomValue.value))
        gamepp.webapp.windows.resize.sync('hardware_setupsensor',Math.floor(692 * zoomValue.value),Math.floor(712 * zoomValue.value))
      }
    }catch (e) {
      zoomValue.value = 1
    }

    try {
      gamepp.display.onDisplayMetricsChanged.addEventListener( async (scaleFactor:number)=>{
        const zoomWithSystem = gamepp.setting.getInteger.sync(313)
        if (zoomWithSystem === 1) {
          console.log('display',scaleFactor)
          zoomValue.value = scaleFactor
          try{
            gamepp.webapp.windows.setMinimumSize.sync('hardware_setupsensor', Math.floor(692 * zoomValue.value),Math.floor(712 * zoomValue.value))
            gamepp.webapp.windows.resize.sync('hardware_setupsensor',Math.floor(692 * zoomValue.value),Math.floor(712 * zoomValue.value))
          }catch (e) {
            console.log('gamepp.webapp.windows.resize.sync(gamepp_config)',e)
          }
        }
      })
    }catch (e) {
      console.log(e)

    }
  }

  async function UploadSensorDataServer(hw_name: any, sensor_name: any, sensor_unit: any, mainboard_name: any, cpu_name: any, gpu_name: any) {
    // console.log(hw_name + "\n" + sensor_name + "\n" + sensor_unit + "\n" + mainboard_name + "\n" + cpu_name + "\n" + gpu_name);
    // mid,hw_name,hw_type,sensor_name,version
    // CPU ,GPU ,System ,Mainboard ,S.M.A.R.T
    const getBaseJsonInfo = JSON.parse(await gamepp.hardware.getBaseJsonInfo.promise())
    let hw_type = 0
    if (hw_name.includes('CPU')) {
      hw_type = 1
    } else if (hw_name.includes('GPU')) {
      hw_type = 2
    } else if (hw_name.includes('S.M.A.R.T')) {
      hw_type = 3
    } else if (hw_name.includes('DIMM')) {
      hw_type = 5
    } else {
      hw_type = 4
    }
    let sensor_type = ''
    if (sensor_unit === ' ℃') {
      sensor_type = 'tempe'
    } else if (sensor_unit === ' V') {
      sensor_type = 'voltage'
    } else if (sensor_unit === ' RPM') {
      sensor_type = 'fan'
    } else if (sensor_unit === ' W') {
      sensor_type = 'power'
    } else if (sensor_unit === ' MHz') {
      sensor_type = 'clock'
    } else if (sensor_unit === ' %') {
      sensor_type = 'usage'
    }

    const data: any = {}
    data.mid = await gamepp.getMID.promise()
    data.mainboard_name = mainboard_name

    data.cpu_name = cpu_name
    data.gpu_name = gpu_name
    if (hw_type === 2) {
      data.gpu_name = hw_name.replace('GPU [#0]: ', '').replace('GPU [#1]: ', '').replace(': ', '')
    }
    data.disk_name = ''
    if (hw_type === 3) {
      data.disk_name = hw_name.replace('S.M.A.R.T.: ', '')
    }
    data.dram_name = getBaseJsonInfo.MEMORY.SubNode[0].ModuleManufacturer

    data.hw_name = hw_name
    data.hw_type = hw_type// hw_type 1:CPU 2:显卡 3:硬盘 4:主板 5:内存
    data.sensor_name = sensor_name
    data.sensor_type = sensor_type
    data.version = await gamepp.getPlatformVersion.promise()
    const isAppInDebuggingMode = await gamepp.isAppInDebuggingMode.promise()
    if (!isAppInDebuggingMode) {
      let res = await gamepp.utils.updateUserSensorInfo.promise(data)
    }
  }

// 取收藏的传感器数据
  function MyAttention() {
    let sensor_collected: any = window.localStorage.getItem('collected_sensor_list')
    if (sensor_collected) {
      sensor_collected = JSON.parse(sensor_collected) || []
    } else {
      sensor_collected = []
    }
    collectedSensor.value = []
    for (let i = 0; i < sensor_collected.length; i++) {
      let collected_data = sensor_collected[i]
      let outName = escapeSelector(collected_data['mainName']);
      let name = escapeSelector(collected_data['name']);
      collectedSensor.value.push({
        collected_data,
        outName,
        name,
        tname: sensor_collected[i]['name']
      })
    }
  }

// 取消收藏
  function collectSensor(OutIndex: any, InnerIndex: any, mainName: any, name: any) {
    let collectedSensor_local: any = window.localStorage.getItem('collected_sensor_list')
    if (collectedSensor_local) {
      collectedSensor_local = JSON.parse(collectedSensor_local) || []
    } else {
      collectedSensor_local = []
    }
    //取消收藏
    const findIndex = collectedSensor_local.findIndex((item: any) => {
      return item.name === name && item.mainName === mainName
    })
    collectedSensor_local.splice(findIndex, 1)
    window.localStorage.setItem('collected_sensor_list', JSON.stringify(collectedSensor_local))

    MyAttention()
  }

  function escapeSelector(selector: any) {
    return selector.replace(/([ #;&,.+*~':"!^$[\]()=>|/@])/g, '\\$1');
  }

  async function GetHWTempInfo(init: boolean) {
    let SensorInfoStr = null
    let cpuType = 'intel'
    try
    {
      SensorInfoStr = await gamepp.hardware.getSensorInfo.promise()
    }
    catch (err)
    {
      return
    }

    if (SensorInfoStr === '')
    {
      GetHWTempInfo(init)
      return
    }
    if (SensorInfoStr)
    {
      const localData = JSON.parse(window.localStorage.getItem('chooseSensor_list_v1')!)
      if (!localData) {
        setTimeout(() => {
          console.log('获取chooseSensor_list_失败')
          GetHWTempInfo(true)
        }, 1000)
        return
      }

      const SensorInfoOriginal = JSON.parse(SensorInfoStr);
      const SensorInfoKeys = Object.keys(SensorInfoOriginal)
      if (init) {
        SensorInfoKeys.forEach(function (item) {
          if (item.startsWith('CPU')) {
            if (String(item).toLowerCase().includes('intel')) {
              cpuType = 'intel'
            } else {
              cpuType = 'amd'
            }
          }
          if (item.startsWith("CPU [#0]:") && !cpuName.value) {
            if (cpuName.value === "") {
              cpuName.value = item.split(':')[1].trim();
            }
          } else if (item.startsWith("GPU [#0]")) {
            gpuName0.value = item.split(':')[1].trim();
          } else if (item.startsWith("GPU [#1]")) {
            gpuName1.value = item.split(':')[1].trim();
          } else if (item.startsWith("GPU [#2]")) {
            gpuName2.value = item.split(':')[1].trim();
          } else if (item.startsWith("System:") && !motherboardName.value) {
            motherboardName.value = item.split(':')[1].trim();
          }
        });

        const elementsCount: number = $store.bg_sensor_data.gpu_list.length;
        const onlyOneExists = elementsCount === 1;
        if (onlyOneExists) {
          setMasterGPUShow.value = false
        }
        MyAttention();
      }

      let SensorInfo: any = SensorAddAverageData(SensorInfoOriginal, SensorInfoKeys)
      SensorInfoData = SensorInfo
      let cpu_temp: Array<string|number> = ['','']
      if (cpuType === 'intel')
      { // Intel CPU 优先选择 CPU Package 或者 Core Max
        cpu_temp = findValueByKeyAndType(SensorInfo, 'CPU', 'CPU Package', 'temperature')
        if (cpu_temp[1] === '')
        {
          cpu_temp = findValueByKeyAndType(SensorInfo, 'CPU', 'Core Max', 'temperature')
        }
      }else{ // AMD CPU 优先选择 CPU Die (average) 或者 CPU (Tctl/Tdie)
        cpu_temp = findValueByKeyAndType(SensorInfo, 'CPU', 'CPU Die (average)', 'temperature')
        if (cpu_temp[1] === '')
        {
          cpu_temp = findValueByKeyAndType(SensorInfo, 'CPU', 'CPU (Tctl/Tdie)', 'temperature')
        }
      }
      if (cpu_temp[1] === '')
      { // 都没找到
        cpu_temp = findValueByKeyAndType(SensorInfo, 'CPU', false, 'temperature')
      }
      const cpu_temp_p = findValueByKeyAndType(SensorInfo, 'CPU', false, 'temperature')
      const cpu_temp_e = findValueByKeyAndType(SensorInfo, 'CPU', 'E-core', 'temperature')

      const cpu_clock = findValueByKeyAndType(SensorInfo, 'CPU', false, 'clock')
      const cpu_clock_p = findValueByKeyAndType(SensorInfo, 'CPU', 'P Core Clocks', 'clock')
      const cpu_clock_e = findValueByKeyAndType(SensorInfo, 'CPU', 'E Core Clocks', 'clock')

      // 电压先从主板找Vcore
      let cpu_voltage = findValueByKeyAndType(SensorInfo, 'Mainboard', 'Vcore', 'voltage')
      if (cpu_voltage[1] === '')
      { // 找不到从CPU里找
        cpu_voltage = findValueByKeyAndType(SensorInfo, 'CPU', false, 'voltage')
      }
      const cpu_voltage_p = findValueByKeyAndType(SensorInfo, 'CPU', 'P Core Max VID', 'voltage')
      const cpu_voltage_e = findValueByKeyAndType(SensorInfo, 'CPU', 'E Core Max VID', 'voltage')

      const cpu_usage = findValueByKeyAndType(SensorInfo, 'CPU', 'Total CPU Usage', 'usage')
      const cpu_usage_p = findValueByKeyAndType(SensorInfo, 'CPU', 'P Core Usages', 'usage')
      const cpu_usage_e = findValueByKeyAndType(SensorInfo, 'CPU', 'E Core Usages', 'usage')

      const cpu_fan = findValueByKeyAndType(SensorInfo, 'Mainboard', false, 'fan')
      const cpu_power = findValueByKeyAndType(SensorInfo, 'CPU', false, 'power')


      //================================================================   GPU   ================================================================
      const gpu_temp = findValueByKeyAndType(SensorInfo, 'GPU [#0]', false, 'temperature')
      const gpu_clock = findValueByKeyAndType(SensorInfo, 'GPU [#0]', false, 'clock')
      const gpu_mem_clock = findValueByKeyAndType(SensorInfo, 'GPU [#0]', 'Memory', 'clock')
      const gpu_power = findValueByKeyAndType(SensorInfo, 'GPU [#0]', false, 'power')
      let gpu_fan = findValueByKeyAndType(SensorInfo, 'GPU [#0]', false, 'fan')
      let gpu_fan_amd = findValueByKeyAndType(SensorInfo, 'GPU [#0]', 'Fan PWM', 'other')
      let gpu_fan_unit = 'RPM'
      if (gpu_fan[0] === '' && gpu_fan_amd[0] !== '') {
        gpu_fan = gpu_fan_amd;
        gpu_fan_unit = '%'
      }


      let gpu_mem_usage_unit = '%'
      let gpu_mem_usage = findValueByKeyAndType(SensorInfo, 'GPU [#0]', 'Memory Usage', 'usage')
      let gpu_mem_usage_amd = findValueByKeyAndType(SensorInfo, 'GPU [#0]', 'Memory Dedicated', 'other')
      if (gpu_mem_usage[0] === '' && gpu_mem_usage_amd[0] !== '') {
        gpu_mem_usage = gpu_mem_usage_amd;
        gpu_mem_usage_unit = 'MB'
      }


      const gpu_hot_spot_temp = findValueByKeyAndType(SensorInfo, 'GPU [#0]', 'Hot Spot Temperature', 'temperature')
      const gpu_total_usage = findValueByKeyAndType(SensorInfo, 'GPU [#0]', false, 'usage')
      const gpu_d3d_usage = findValueByKeyAndType(SensorInfo, 'GPU [#0]', 'D3D Usage', 'usage')
      const gpu_mem_temp = findValueByKeyAndType(SensorInfo, 'GPU [#0]', 'Memory Temperature|Memory Junction Temperature', 'temperature')


      const gpu1_temp = findValueByKeyAndType(SensorInfo, 'GPU [#1]', false, 'temperature')
      const gpu1_clock = findValueByKeyAndType(SensorInfo, 'GPU [#1]', false, 'clock')
      const gpu1_mem_clock = findValueByKeyAndType(SensorInfo, 'GPU [#1]', 'Memory', 'clock')
      const gpu1_power = findValueByKeyAndType(SensorInfo, 'GPU [#1]', false, 'power')
      let gpu1_fan = findValueByKeyAndType(SensorInfo, 'GPU [#1]', false, 'fan')
      let gpu1_fan_amd = findValueByKeyAndType(SensorInfo, 'GPU [#1]', 'Fan PWM', 'other')

      let gpu1_fan_unit = 'RPM'
      if (gpu1_fan[0] === '' && gpu1_fan_amd[0] !== '') {
        gpu1_fan = gpu1_fan_amd;
        gpu1_fan_unit = '%'
      }


      let gpu1_mem_usage_unit = '%'
      let gpu1_mem_usage = findValueByKeyAndType(SensorInfo, 'GPU [#1]', 'Memory Usage', 'usage')
      let gpu1_mem_usage_amd = findValueByKeyAndType(SensorInfo, 'GPU [#1]', 'Memory Dedicated', 'other')
      if (gpu1_mem_usage[0] === '' && gpu1_mem_usage_amd[0] !== '') {
        gpu1_mem_usage = gpu1_mem_usage_amd;
        gpu1_mem_usage_unit = 'MB'
      }

      const gpu1_hot_spot_temp = findValueByKeyAndType(SensorInfo, 'GPU [#1]', 'Hot Spot Temperature', 'temperature')
      const gpu1_total_usage = findValueByKeyAndType(SensorInfo, 'GPU [#1]', false, 'usage')
      const gpu1_d3d_usage = findValueByKeyAndType(SensorInfo, 'GPU [#1]', 'D3D Usage', 'usage')

      const gpu1_mem_temp = findValueByKeyAndType(SensorInfo, 'GPU [#1]', 'Memory Temperature|Memory Junction Temperature', 'temperature')

      const gpu2_temp = findValueByKeyAndType(SensorInfo, 'GPU [#2]', false, 'temperature')
      const gpu2_clock = findValueByKeyAndType(SensorInfo, 'GPU [#2]', false, 'clock')
      const gpu2_mem_clock = findValueByKeyAndType(SensorInfo, 'GPU [#2]', 'Memory', 'clock')
      const gpu2_power = findValueByKeyAndType(SensorInfo, 'GPU [#2]', false, 'power')
      let gpu2_fan = findValueByKeyAndType(SensorInfo, 'GPU [#2]', false, 'fan')
      let gpu2_fan_amd = findValueByKeyAndType(SensorInfo, 'GPU [#2]', 'Fan PWM', 'other')

      let gpu2_fan_unit = 'RPM'
      if (gpu1_fan[0] === '' && gpu2_fan_amd[0] !== '') {
        gpu1_fan = gpu2_fan_amd;
        gpu1_fan_unit = '%'
      }


      let gpu2_mem_usage_unit = '%'
      let gpu2_mem_usage = findValueByKeyAndType(SensorInfo, 'GPU [#2]', 'Memory Usage', 'usage')
      let gpu2_mem_usage_amd = findValueByKeyAndType(SensorInfo, 'GPU [#2]', 'Memory Dedicated', 'other')
      if (gpu2_mem_usage[0] === '' && gpu2_mem_usage_amd[0] !== '') {
        gpu2_mem_usage = gpu2_mem_usage_amd;
        gpu2_mem_usage_unit = 'MB'
      }

      const gpu2_hot_spot_temp = findValueByKeyAndType(SensorInfo, 'GPU [#2]', 'Hot Spot Temperature', 'temperature')
      const gpu2_total_usage = findValueByKeyAndType(SensorInfo, 'GPU [#2]', false, 'usage')
      const gpu2_d3d_usage = findValueByKeyAndType(SensorInfo, 'GPU [#2]', 'D3D Usage', 'usage')
      const gpu2_mem_temp = findValueByKeyAndType(SensorInfo, 'GPU [#2]', 'Memory Temperature|Memory Junction Temperature', 'temperature')
      //================================================================   GPUEnd   ================================================================


      const mainboard_temp = findValueByKeyAndType(SensorInfo, 'Mainboard', false, 'temperature')
      const drive_temp = findValueByKeyAndType(SensorInfo, 'S.M.A.R.T', false, 'temperature')

      const memory_usage = findValueByKeyAndType(SensorInfo, 'System', 'Physical Memory Load', 'other')
      const memory_temp = findValueByKeyAndType(SensorInfo, 'DIMM', false, 'temperature')

      const network_download = findValueByKeyAndType(SensorInfo, 'Network', 'Current DL', 'other')
      const network_upload = findValueByKeyAndType(SensorInfo, 'Network', 'Current UP', 'other')

      if (localData) {
        let diskHtmlResult = '';
        diskArr.value = []
        localData.diskData.forEach((disk: any, index: number) => {
          let keyName = 'Temp'
          let disk_value = disk.value
          let disk_unit = disk.Unit
          if (!disk.isDef) {
            disk_value = SensorInfoDataProcess(false, disk.groupIndex, disk.itemIndex, true)
            if (disk_value === '0') {
              disk_unit = '℃'
            } else {
              disk_unit = ''
            }
          } else {

            let disk_value_data = findValueByKeyAndType(SensorInfo, 'S.M.A.R.T.: ' + disk?.describe, false, 'temperature')
            disk_value = disk_value_data[1]
          }
          if (disk.keyName) {
            keyName = disk.keyName
          }
          diskArr.value.push({
            describe: disk.describe,
            keyName,
            disk_value,
            disk_unit
          })
        })
      }

      //================================================================   CPU   ================================================================
      const [temp, temp_p, temp_e, clock, clock_p, clock_e, load, load_p, load_e, voltage, voltage_p, voltage_e, power, fan] = localData.cpuData;

      const cpu_temp_value = (!temp.isDef || temp.isDefServer) ? SensorInfoDataProcess(false, temp.groupIndex, temp.itemIndex, true) : cpu_temp[1];
      let cpu_temp_value_p = (!temp_p.isDef || temp_p.isDefServer) ? SensorInfoDataProcess(false, temp_p.groupIndex, temp_p.itemIndex, true) : cpu_temp_p[1];
      let cpu_temp_value_e = (!temp_e.isDef || temp_e.isDefServer) ? SensorInfoDataProcess(false, temp_e.groupIndex, temp_e.itemIndex, true) : cpu_temp_e[1];

      let cpu_load_value = (!load.isDef || load.isDefServer) ? SensorInfoDataProcess(false, load.groupIndex, load.itemIndex, true) : cpu_usage[1];
      let cpu_load_value_p = (!load_p.isDef || load_p.isDefServer) ? SensorInfoDataProcess(false, load_p.groupIndex, load_p.itemIndex, true) : cpu_usage_p[1];
      let cpu_load_value_e = (!load_e.isDef || load_e.isDefServer) ? SensorInfoDataProcess(false, load_e.groupIndex, load_e.itemIndex, true) : cpu_usage_e[1];

      let cpu_clock_value = (!clock.isDef || clock.isDefServer) ? SensorInfoDataProcess(false, clock.groupIndex, clock.itemIndex, true) : cpu_clock[1];
      let cpu_clock_value_p = (!clock_p.isDef || clock_p.isDefServer) ? SensorInfoDataProcess(false, clock_p.groupIndex, clock_p.itemIndex, true) : cpu_clock_p[1];
      let cpu_clock_value_e = (!clock_e.isDef || clock_e.isDefServer) ? SensorInfoDataProcess(false, clock_e.groupIndex, clock_e.itemIndex, true) : cpu_clock_e[1];

      let cpu_voltage_value = (!voltage.isDef || voltage.isDefServer) ? SensorInfoDataProcess(false, voltage.groupIndex, voltage.itemIndex, true) : cpu_voltage[1];
      let cpu_voltage_value_p = (!voltage_p.isDef || voltage_p.isDefServer) ? SensorInfoDataProcess(false, voltage_p.groupIndex, voltage_p.itemIndex, true) : cpu_voltage_p[1];
      let cpu_voltage_value_e = (!voltage_e.isDef || voltage_e.isDefServer) ? SensorInfoDataProcess(false, voltage_e.groupIndex, voltage_e.itemIndex, true) : cpu_voltage_e[1];

      let cpu_power_value = (!power.isDef || power.isDefServer) ? SensorInfoDataProcess(false, power.groupIndex, power.itemIndex, true) : cpu_power[1];
      let cpu_fan_value = (!fan.isDef || fan.isDefServer) ? SensorInfoDataProcess(false, fan.groupIndex, fan.itemIndex, true) : cpu_fan[1];

      //================================================================   GPU   ================================================================
      const [gpu0_temp_l, gpu0_clock_l, gpu0_power_l, gpu0_load_d3d_l, gpu0_total_usage_l, gpu0_fan_l, gpu0_hot_temp_l, gpu0_mem_usage_l, gpu0_mem_clock_l, gpu0_mem_temp_l] = localData.gpu0Data;
      let gpu_temp_value = !gpu0_temp_l.isDef ? SensorInfoDataProcess(gpu0_temp_l.isDef, gpu0_temp_l.groupIndex, gpu0_temp_l.itemIndex, true) : gpu_temp[1];
      let gpu_power_value = !gpu0_power_l.isDef ? SensorInfoDataProcess(gpu0_power_l.isDef, gpu0_power_l.groupIndex, gpu0_power_l.itemIndex, true) : gpu_power[1];
      let gpu_hot_spot_temp_value = !gpu0_hot_temp_l.isDef ? SensorInfoDataProcess(gpu0_hot_temp_l.isDef, gpu0_hot_temp_l.groupIndex, gpu0_hot_temp_l.itemIndex, true) : gpu_hot_spot_temp[1];
      let gpu_clock_value = !gpu0_clock_l.isDef ? SensorInfoDataProcess(gpu0_clock_l.isDef, gpu0_clock_l.groupIndex, gpu0_clock_l.itemIndex, true) : gpu_clock[1];
      let gpu_d3d_usage_value = !gpu0_load_d3d_l.isDef ? SensorInfoDataProcess(gpu0_load_d3d_l.isDef, gpu0_load_d3d_l.groupIndex, gpu0_load_d3d_l.itemIndex, true) : gpu_d3d_usage[1];
      let gpu_total_usage_value = !gpu0_total_usage_l.isDef ? SensorInfoDataProcess(gpu0_total_usage_l.isDef, gpu0_total_usage_l.groupIndex, gpu0_total_usage_l.itemIndex, true) : gpu_total_usage[1];
      let gpu_fan_value = !gpu0_fan_l.isDef ? SensorInfoDataProcess(gpu0_fan_l.isDef, gpu0_fan_l.groupIndex, gpu0_fan_l.itemIndex, true) : gpu_fan[1];
      let gpu_mem_usage_value = !gpu0_mem_usage_l.isDef ? SensorInfoDataProcess(gpu0_mem_usage_l.isDef, gpu0_mem_usage_l.groupIndex, gpu0_mem_usage_l.itemIndex, true) : gpu_mem_usage[1];
      !gpu0_mem_usage_l.isDef && (gpu_mem_usage_unit = gpu0_mem_usage_l.Unit)
      let gpu_mem_clock_value = !gpu0_mem_clock_l.isDef ? SensorInfoDataProcess(gpu0_mem_clock_l.isDef, gpu0_mem_clock_l.groupIndex, gpu0_mem_clock_l.itemIndex, true) : gpu_mem_clock[1];
      let gpu_mem_temp_value = !gpu0_mem_temp_l.isDef ? SensorInfoDataProcess(gpu0_mem_temp_l.isDef, gpu0_mem_temp_l.groupIndex, gpu0_mem_temp_l.itemIndex, true) : gpu_mem_temp[1];


      const [gpu1_temp_l, gpu1_clock_l, gpu1_power_l, gpu1_load_d3d_l, gpu1_total_usage_l, gpu1_fan_l, gpu1_hot_temp_l, gpu1_mem_usage_l, gpu1_mem_clock_l, gpu1_mem_temp_l] = localData.gpu1Data;
      let gpu1_temp_value = !gpu1_temp_l.isDef ? SensorInfoDataProcess(gpu1_temp_l.isDef, gpu1_temp_l.groupIndex, gpu1_temp_l.itemIndex, true) : gpu1_temp[1];
      let gpu1_power_value = !gpu1_power_l.isDef ? SensorInfoDataProcess(gpu1_power_l.isDef, gpu1_power_l.groupIndex, gpu1_power_l.itemIndex, true) : gpu1_power[1];
      let gpu1_hot_spot_temp_value = !gpu1_hot_temp_l.isDef ? SensorInfoDataProcess(gpu1_hot_temp_l.isDef, gpu1_hot_temp_l.groupIndex, gpu1_hot_temp_l.itemIndex, true) : gpu1_hot_spot_temp[1];
      let gpu1_clock_value = !gpu1_clock_l.isDef ? SensorInfoDataProcess(gpu1_clock_l.isDef, gpu1_clock_l.groupIndex, gpu1_clock_l.itemIndex, true) : gpu1_clock[1];
      let gpu1_d3d_usage_value = !gpu1_load_d3d_l.isDef ? SensorInfoDataProcess(gpu1_load_d3d_l.isDef, gpu1_load_d3d_l.groupIndex, gpu1_load_d3d_l.itemIndex, true) : gpu1_d3d_usage[1];
      let gpu1_total_usage_value = !gpu1_total_usage_l.isDef ? SensorInfoDataProcess(gpu1_total_usage_l.isDef, gpu1_total_usage_l.groupIndex, gpu1_total_usage_l.itemIndex, true) : gpu1_total_usage[1];
      let gpu1_fan_value = !gpu1_fan_l.isDef ? SensorInfoDataProcess(gpu1_fan_l.isDef, gpu1_fan_l.groupIndex, gpu1_fan_l.itemIndex, true) : gpu1_fan[1];
      let gpu1_mem_usage_value = !gpu1_mem_usage_l.isDef ? SensorInfoDataProcess(gpu1_mem_usage_l.isDef, gpu1_mem_usage_l.groupIndex, gpu1_mem_usage_l.itemIndex, true) : gpu1_mem_usage[1];
      !gpu1_mem_usage_l.isDef && (gpu1_mem_usage_unit = gpu1_mem_usage_l.Unit)
      let gpu1_mem_clock_value = !gpu1_mem_clock_l.isDef ? SensorInfoDataProcess(gpu1_mem_clock_l.isDef, gpu1_mem_clock_l.groupIndex, gpu1_mem_clock_l.itemIndex, true) : gpu1_mem_clock[1];
      let gpu1_mem_temp_value = !gpu1_mem_temp_l.isDef ? SensorInfoDataProcess(gpu1_mem_temp_l.isDef, gpu1_mem_temp_l.groupIndex, gpu1_mem_temp_l.itemIndex, true) : gpu1_mem_temp[1];


      const [gpu2_temp_l, gpu2_clock_l, gpu2_power_l, gpu2_load_d3d_l, gpu2_total_usage_l, gpu2_fan_l, gpu2_hot_temp_l, gpu2_mem_usage_l, gpu2_mem_clock_l, gpu2_mem_temp_l] = localData.gpu2Data;
      let gpu2_temp_value = !gpu2_temp_l.isDef ? SensorInfoDataProcess(gpu2_temp_l.isDef, gpu2_temp_l.groupIndex, gpu2_temp_l.itemIndex, true) : gpu2_temp[1];
      let gpu2_power_value = !gpu2_power_l.isDef ? SensorInfoDataProcess(gpu2_power_l.isDef, gpu2_power_l.groupIndex, gpu2_power_l.itemIndex, true) : gpu2_power[1];
      let gpu2_hot_spot_temp_value = !gpu2_hot_temp_l.isDef ? SensorInfoDataProcess(gpu2_hot_temp_l.isDef, gpu2_hot_temp_l.groupIndex, gpu2_hot_temp_l.itemIndex, true) : gpu2_hot_spot_temp[1];
      let gpu2_clock_value = !gpu2_clock_l.isDef ? SensorInfoDataProcess(gpu2_clock_l.isDef, gpu2_clock_l.groupIndex, gpu2_clock_l.itemIndex, true) : gpu2_clock[1];
      let gpu2_d3d_usage_value = !gpu2_load_d3d_l.isDef ? SensorInfoDataProcess(gpu2_load_d3d_l.isDef, gpu2_load_d3d_l.groupIndex, gpu2_load_d3d_l.itemIndex, true) : gpu2_d3d_usage[1];
      let gpu2_total_usage_value = !gpu2_total_usage_l.isDef ? SensorInfoDataProcess(gpu2_total_usage_l.isDef, gpu2_total_usage_l.groupIndex, gpu2_total_usage_l.itemIndex, true) : gpu2_total_usage[1];
      let gpu2_fan_value = !gpu2_fan_l.isDef ? SensorInfoDataProcess(gpu2_fan_l.isDef, gpu2_fan_l.groupIndex, gpu2_fan_l.itemIndex, true) : gpu2_fan[1];
      let gpu2_mem_usage_value = !gpu2_mem_usage_l.isDef ? SensorInfoDataProcess(gpu2_mem_usage_l.isDef, gpu2_mem_usage_l.groupIndex, gpu2_mem_usage_l.itemIndex, true) : gpu2_mem_usage[1];
      !gpu2_mem_usage_l.isDef && (gpu2_mem_usage_unit = gpu2_mem_usage_l.Unit)
      let gpu2_mem_clock_value = !gpu2_mem_clock_l.isDef ? SensorInfoDataProcess(gpu2_mem_clock_l.isDef, gpu2_mem_clock_l.groupIndex, gpu2_mem_clock_l.itemIndex, true) : gpu2_mem_clock[1];
      let gpu2_mem_temp_value = !gpu2_mem_temp_l.isDef ? SensorInfoDataProcess(gpu2_mem_temp_l.isDef, gpu2_mem_temp_l.groupIndex, gpu2_mem_temp_l.itemIndex, true) : gpu2_mem_temp[1];


      //================================================================   硬盘   ================================================================
      const temp2 = localData.diskData[0]
      let drive_temp_value: any = drive_temp[1]
      if (temp2 && !temp2.isDef) {
        drive_temp_value = SensorInfoDataProcess(temp2.isDef, temp2.groupIndex, temp2.itemIndex, true)
      }

      //================================================================   主板   ================================================================

      const board_temp_l = localData.boardData[0]
      let mainboard_temp_value = (!board_temp_l.isDef || board_temp_l.isDefServer) ? SensorInfoDataProcess(false, board_temp_l.groupIndex, board_temp_l.itemIndex, true) : mainboard_temp[1];
      //================================================================   内存   ================================================================
      const memory_load_l = localData.memoryData[0]
      let memory_usage_value = !memory_load_l.isDef ? SensorInfoDataProcess(memory_load_l.isDef, memory_load_l.groupIndex, memory_load_l.itemIndex, true) : memory_usage[1];

      const memory_temp_l = localData.memoryData[1]
      let memory_temp_value = !memory_temp_l.isDef ? SensorInfoDataProcess(memory_temp_l.isDef, memory_temp_l.groupIndex, memory_temp_l.itemIndex, true) : memory_temp[1];

      //================================================================   网络   ================================================================
      const [network_dl_l, network_up_l] = localData.networkData;
      // @ts-ignore
      let network_dl_value = !network_dl_l.isDef ? SensorInfoDataProcess(network_dl_l.isDef, network_dl_l.groupIndex, network_dl_l.itemIndex, true, SensorInfo) : network_download[1];
      // @ts-ignore
      let network_up_value = !network_up_l.isDef ? SensorInfoDataProcess(network_up_l.isDef, network_up_l.groupIndex, network_up_l.itemIndex, true, SensorInfo) : network_upload[1];

      let obj: any = {
        cpu_temp: {key: cpu_temp[0], keyName: temp.keyName, value: cpu_temp_value, unit: temp.Unit, isDef: temp.isDef},
        cpu_temp_p: {
          key: cpu_temp_p[0],
          keyName: temp_p.keyName,
          value: cpu_temp_value_p,
          unit: temp_p.Unit,
          isDef: temp_p.isDef
        },
        cpu_temp_e: {
          key: cpu_temp_e[0],
          keyName: temp_e.keyName,
          value: cpu_temp_value_e,
          unit: temp_e.Unit,
          isDef: temp_e.isDef
        },
        cpu_clock: {
          key: cpu_clock[0],
          keyName: clock.keyName,
          value: cpu_clock_value,
          unit: clock.Unit,
          isDef: clock.isDef
        },
        cpu_clock_p: {
          key: cpu_clock_p[0],
          keyName: clock_p.keyName,
          value: cpu_clock_value_p,
          unit: clock_p.Unit,
          isDef: clock_p.isDef
        },
        cpu_clock_e: {
          key: cpu_clock_e[0],
          keyName: clock_e.keyName,
          value: cpu_clock_value_e,
          unit: clock_e.Unit,
          isDef: clock_e.isDef
        },
        cpu_usage: {
          key: cpu_usage[0],
          keyName: load.keyName,
          value: cpu_load_value,
          unit: load.Unit,
          isDef: load.isDef
        },
        cpu_usage_p: {
          key: cpu_usage_p[0],
          keyName: load_p.keyName,
          value: cpu_load_value_p,
          unit: load_p.Unit,
          isDef: load_p.isDef
        },
        cpu_usage_e: {
          key: cpu_usage_e[0],
          keyName: load_e.keyName,
          value: cpu_load_value_e,
          unit: load_e.Unit,
          isDef: load_e.isDef
        },
        cpu_voltage: {
          key: cpu_voltage[0],
          keyName: voltage.keyName,
          value: cpu_voltage_value,
          unit: voltage.Unit,
          isDef: voltage.isDef
        },
        cpu_voltage_p: {
          key: cpu_voltage_p[0],
          keyName: voltage_p.keyName,
          value: cpu_voltage_value_p,
          unit: voltage_p.Unit,
          isDef: voltage_p.isDef
        },
        cpu_voltage_e: {
          key: cpu_voltage_e[0],
          keyName: voltage_e.keyName,
          value: cpu_voltage_value_e,
          unit: voltage_e.Unit,
          isDef: voltage_e.isDef
        },
        cpu_power: {
          key: cpu_power[0],
          keyName: power.keyName,
          value: cpu_power_value,
          unit: power.Unit,
          isDef: power.isDef
        },
        cpu_fan: {key: cpu_fan[0], keyName: fan.keyName, value: cpu_fan_value, unit: fan.Unit, isDef: fan.isDef},

        //GPU0
        gpu0_temp: {
          key: gpu_temp[0],
          keyName: gpu0_temp_l.keyName,
          value: gpu_temp_value,
          unit: gpu0_temp_l.Unit,
          isDef: gpu0_temp_l.isDef
        },
        gpu0_hot_spot_temp: {
          key: gpu_hot_spot_temp[0],
          keyName: gpu0_hot_temp_l.keyName,
          value: gpu_hot_spot_temp_value,
          unit: gpu0_hot_temp_l.Unit,
          isDef: gpu0_hot_temp_l.isDef
        },
        gpu0_clock: {
          key: gpu_clock[0],
          keyName: gpu0_clock_l.keyName,
          value: gpu_clock_value,
          unit: gpu0_clock_l.Unit,
          isDef: gpu0_clock_l.isDef
        },
        gpu0_power: {
          key: gpu_power[0],
          keyName: gpu0_power_l.keyName,
          value: gpu_power_value,
          unit: gpu0_power_l.Unit,
          isDef: gpu0_power_l.isDef
        },
        gpu0_fan: {
          key: gpu_fan[0],
          keyName: gpu0_fan_l.keyName,
          value: gpu_fan_value,
          unit: gpu0_fan_l.Unit,
          isDef: gpu0_fan_l.isDef
        },
        gpu0_d3d_usage: {
          key: gpu_d3d_usage[0],
          keyName: gpu0_load_d3d_l.keyName,
          value: gpu_d3d_usage_value,
          unit: gpu0_load_d3d_l.Unit,
          isDef: gpu0_load_d3d_l.isDef
        },
        gpu0_total_usage: {
          key: gpu_total_usage[0],
          keyName: gpu0_total_usage_l.keyName,
          value: gpu_total_usage_value,
          unit: gpu0_total_usage_l.Unit,
          isDef: gpu0_total_usage_l.isDef
        },
        gpu0_mem_usage: {
          key: gpu_mem_usage[0],
          keyName: gpu0_mem_usage_l.keyName,
          value: gpu_mem_usage_value,
          unit: gpu_mem_usage_unit,
          isDef: gpu0_mem_usage_l.isDef
        },
        gpu0_mem_clock: {
          key: gpu_mem_clock[0],
          keyName: gpu0_mem_clock_l.keyName,
          value: gpu_mem_clock_value,
          unit: gpu0_mem_clock_l.Unit,
          isDef: gpu0_mem_clock_l.isDef
        },
        gpu0_mem_temp: {
          key: gpu_mem_temp[0],
          keyName: gpu0_mem_temp_l.keyName,
          value: gpu_mem_temp_value,
          unit: gpu0_mem_temp_l.Unit,
          isDef: gpu0_mem_temp_l.isDef
        },


        //GPU1
        gpu1_temp: {
          key: gpu1_temp[0],
          keyName: gpu1_temp_l.keyName,
          value: gpu1_temp_value,
          unit: gpu1_temp_l.Unit,
          isDef: gpu1_temp_l.isDef
        },
        gpu1_hot_spot_temp: {
          key: gpu1_hot_spot_temp[0],
          keyName: gpu1_hot_temp_l.keyName,
          value: gpu1_hot_spot_temp_value,
          unit: gpu1_hot_temp_l.Unit,
          isDef: gpu1_hot_temp_l.isDef
        },
        gpu1_clock: {
          key: gpu1_clock[0],
          keyName: gpu1_clock_l.keyName,
          value: gpu1_clock_value,
          unit: gpu1_clock_l.Unit,
          isDef: gpu1_clock_l.isDef
        },
        gpu1_power: {
          key: gpu1_power[0],
          keyName: gpu1_power_l.keyName,
          value: gpu1_power_value,
          unit: gpu1_power_l.Unit,
          isDef: gpu1_power_l.isDef
        },
        gpu1_fan: {
          key: gpu1_fan[0],
          keyName: gpu1_fan_l.keyName,
          value: gpu1_fan_value,
          unit: gpu1_fan_l.Unit,
          isDef: gpu1_fan_l.isDef
        },
        gpu1_d3d_usage: {
          key: gpu1_d3d_usage[0],
          keyName: gpu1_load_d3d_l.keyName,
          value: gpu1_d3d_usage_value,
          unit: gpu1_load_d3d_l.Unit,
          isDef: gpu1_load_d3d_l.isDef
        },
        gpu1_total_usage: {
          key: gpu1_total_usage[0],
          keyName: gpu1_total_usage_l.keyName,
          value: gpu1_total_usage_value,
          unit: gpu1_total_usage_l.Unit,
          isDef: gpu1_total_usage_l.isDef
        },
        gpu1_mem_usage: {
          key: gpu1_mem_usage[0],
          keyName: gpu1_mem_usage_l.keyName,
          value: gpu1_mem_usage_value,
          unit: gpu1_mem_usage_unit,
          isDef: gpu1_mem_usage_l.isDef
        },
        gpu1_mem_clock: {
          key: gpu1_mem_clock[0],
          keyName: gpu1_mem_clock_l.keyName,
          value: gpu1_mem_clock_value,
          unit: gpu1_mem_clock_l.Unit,
          isDef: gpu1_mem_clock_l.isDef
        },
        gpu1_mem_temp: {
          key: gpu1_mem_temp[0],
          keyName: gpu1_mem_temp_l.keyName,
          value: gpu1_mem_temp_value,
          unit: gpu1_mem_temp_l.Unit,
          isDef: gpu1_mem_temp_l.isDef
        },


        //GPU2
        gpu2_temp: {
          key: gpu2_temp[0],
          keyName: gpu2_temp_l.keyName,
          value: gpu2_temp_value,
          unit: gpu2_temp_l.Unit,
          isDef: gpu2_temp_l.isDef
        },
        gpu2_hot_spot_temp: {
          key: gpu2_hot_spot_temp[0],
          keyName: gpu2_hot_temp_l.keyName,
          value: gpu2_hot_spot_temp_value,
          unit: gpu2_hot_temp_l.Unit,
          isDef: gpu2_hot_temp_l.isDef
        },
        gpu2_clock: {
          key: gpu2_clock[0],
          keyName: gpu2_clock_l.keyName,
          value: gpu2_clock_value,
          unit: gpu2_clock_l.Unit,
          isDef: gpu2_clock_l.isDef
        },
        gpu2_power: {
          key: gpu2_power[0],
          keyName: gpu2_power_l.keyName,
          value: gpu2_power_value,
          unit: gpu2_power_l.Unit,
          isDef: gpu2_power_l.isDef
        },
        gpu2_fan: {
          key: gpu2_fan[0],
          keyName: gpu2_fan_l.keyName,
          value: gpu2_fan_value,
          unit: gpu2_fan_l.Unit,
          isDef: gpu2_fan_l.isDef
        },
        gpu2_d3d_usage: {
          key: gpu2_d3d_usage[0],
          keyName: gpu2_load_d3d_l.keyName,
          value: gpu2_d3d_usage_value,
          unit: gpu2_load_d3d_l.Unit,
          isDef: gpu2_load_d3d_l.isDef
        },
        gpu2_total_usage: {
          key: gpu2_total_usage[0],
          keyName: gpu2_total_usage_l.keyName,
          value: gpu2_total_usage_value,
          unit: gpu2_total_usage_l.Unit,
          isDef: gpu2_total_usage_l.isDef
        },
        gpu2_mem_usage: {
          key: gpu2_mem_usage[0],
          keyName: gpu2_mem_usage_l.keyName,
          value: gpu2_mem_usage_value,
          unit: gpu2_mem_usage_unit,
          isDef: gpu2_mem_usage_l.isDef
        },
        gpu2_mem_clock: {
          key: gpu2_mem_clock[0],
          keyName: gpu2_mem_clock_l.keyName,
          value: gpu2_mem_clock_value,
          unit: gpu2_mem_clock_l.Unit,
          isDef: gpu2_mem_clock_l.isDef
        },
        gpu2_mem_temp: {
          key: gpu2_mem_temp[0],
          keyName: gpu2_mem_temp_l.keyName,
          value: gpu2_mem_temp_value,
          unit: gpu2_mem_temp_l.Unit,
          isDef: gpu2_mem_temp_l.isDef
        },


        mainboard_temp: {
          key: mainboard_temp[0],
          keyName: board_temp_l.keyName,
          value: mainboard_temp_value,
          unit: board_temp_l.Unit,
          isDef: board_temp_l.isDef
        },

        drive_temp: {
          key: drive_temp[0],
          keyName: temp2.keyName,
          value: drive_temp_value,
          unit: temp2.Unit,
          isDef: temp2.isDef
        },

        memory_temp: {
          key: memory_temp[0],
          keyName: memory_temp_l.keyName,
          value: memory_temp_value,
          unit: memory_temp_l.Unit,
          isDef: memory_temp_l.isDef
        },
        memory_usage: {
          key: memory_usage[0],
          keyName: memory_load_l.keyName,
          value: memory_usage_value,
          unit: memory_load_l.Unit,
          isDef: memory_load_l.isDef
        },

        network_download: {
          key: network_download[0],
          keyName: network_dl_l.keyName,
          value: network_dl_value,
          unit: network_dl_l.Unit,
          isDef: network_dl_l.isDef
        },
        network_upload: {
          key: network_upload[0],
          keyName: network_up_l.keyName,
          value: network_up_value,
          unit: network_up_l.Unit,
          isDef: network_up_l.isDef
        }
      }

      Object.assign(data_arr, obj)
    }
  }

  function GPP_WindowClose(name: string) {
    gamepp.webapp.windows.close.promise(name)
  }

  function setMasterGPU(index: any) {
    if (index === gpu_index.value) return
    gpu_index.value = index
    window.localStorage.setItem('gpu_index', index)
    window.localStorage.setItem('gpu_index_manual', "1")
  }

  function changeSensor(type: any, key: any, filterType: any, unit: any) {
    console.log(type, key, filterType, unit)
    window.localStorage.setItem('SensorManageType', JSON.stringify('ChangeMainPageSensor')) // {{}}此时传感器操作的种类
    window.localStorage.setItem('SensorManageData', JSON.stringify({type: type, key: key})) // 更改此时传感器操作的种类
    window.localStorage.setItem('SensorManageFilter', JSON.stringify({filterType, unit})) // 更改此时传感器操作的种类
    try
    {
      gamepp.webapp.windows.show.promise('Sensorchoose').then(()=>{
        gamepp.webapp.windows.focus.promise('Sensorchoose')
      })
    } catch (e)
    {
      console.error("gamepp.webapp.windows.show.promise('Sensorchoose')",e)
    }
  }

  function handleCloseWindow() {
    gamepp.webapp.windows.close.promise('hardware_setupsensor')
  }

  function SensorInfoDataProcess(isDef:any, groupIndex:any, itemIndex:any, isUnit:any) {
    if (!isDef) {
      let ToFixed = 0
      let DataUnit = ''
      const obj = Object.keys(SensorInfoData)
      const groupKey = obj[groupIndex]
      const ChoseData = SensorInfoData[groupKey][itemIndex]
      if (!ChoseData) {
        return ''
      }
      const [itemKey, itemValue] = Object.entries(ChoseData)[0] as any;
      if (itemValue.type === 'temperature') {
        DataUnit = ' ℃'
      } else if (itemValue.type === 'voltage') {
        DataUnit = ' V'
        ToFixed = 3
      } else if (itemValue.type === 'fan') {
        DataUnit = ' RPM'
      } else if (itemValue.type === 'power') {
        DataUnit = ' W'
      } else if (itemValue.type === 'clock') {
        DataUnit = ' MHz'
      } else if (itemValue.type === 'usage' || itemKey.includes('Load')) {
        DataUnit = ' %'
      }

      return isUnit ? `${Number(parseFloat(itemValue.value).toFixed(ToFixed))}${DataUnit}`
        : itemKey + ': ' + Number(parseFloat(itemValue.value).toFixed(ToFixed)) + DataUnit
    }
  }

  function showSensorData(val: any) {
    if (!val) {
      return ''
    }
    if (val.value !== '') {
      if (!(val.value.toString()).includes(' ')) {
        if (val.keyName) {
          return val.keyName + ' : ' + val.value + ' ' + val.unit
        } else {
          return val.key + ' : ' + val.value + ' ' + val.unit
        }
      } else {
        return val.keyName + ' : ' + val.value
      }

    } else {
      return '无传感器'
    }
  }

  function RestoreDefault () {
    window.localStorage.removeItem('chooseSensor_list_v1');
    // @ts-nocheck
    ElMessage.success('已恢复默认');
  }

  function goConcern () {
    window.localStorage.removeItem('SensorManageType') // {{}}此时传感器操作的种类
    window.localStorage.removeItem('SensorManageData') // 更改此时传感器操作的种类
    window.localStorage.removeItem('SensorManageFilter') // 更改此时传感器操作的种类

    try {
      gamepp.webapp.windows.show.promise('Sensorchoose').then(()=>{
        gamepp.webapp.windows.focus.promise('Sensorchoose')
      })
    } catch (e){
      console.error("gamepp.webapp.windows.show.promise('Sensorchoose')",e)
    }
  }

  return {
    $store,
    step,
    PE_status,
    SensorInfoData,
    cpuName,
    gpuName0,
    gpuName1,
    gpuName2,
    motherboardName,
    gpu_index,
    setMasterGPUShow,
    collectedSensor,
    diskArr,
    data_arr,
    collectSensor,
    showSensorData,
    changeSensor,
    handleCloseWindow,
    setMasterGPU,
    RestoreDefault,
    goConcern
  }

}
