<!-- 游戏加加更新启动页 -->
<script setup lang="ts">
import type { CalendarDateType, CalendarInstance } from 'element-plus'
import { Minus, Close } from "@element-plus/icons-vue";
import {onMounted , reactive ,ref ,computed ,nextTick,watch,onBeforeMount} from "vue";
import {LoadModuleStepInterface} from "@/modules/Game_Update/types/Interface";
import { getGameList , getHistoryList  ,getTime ,getAllFreeGameList} from "./utils/receiveGame";
import { ElMessage } from "element-plus";
import RightTopIcons from "@/components/mainCom/RightTopIcons.vue";
import Account  from './views/Account.vue';
import GameItem from './views/GameItem.vue';
import { color } from 'echarts';
import axios from 'axios';
import Loginstatus from '@/components/LoginStatus/loginstatus.vue';
import { CountTo } from 'vue3-count-to';
import steamicon from './assets/icon/icon_steam.png'
import epicicon from './assets/icon/icon_epic.png'
import { AVGNum,MaxNum,MinNum,FormatSeconds2,FormatTimeCn,FormatTime,
         FormatTimePlus, DiskCapacityConversion,FormartMonthToNumber,RemoveAllSpace,RegExGetNum,gpu_brand,
         showNumberOfCPUCores,showNumberOfLogicalCpus,findMostFrequent} from '../../uitls/GameppTools'
import { findIndex } from 'lodash-es';
import { useLanguage } from '@/uitls/useLanguage';
useLanguage()
let FreeGamePlan = ref(false)
let FreeGameText = ref('')
let Planshow = ref(false)
//
let Planshow2 = ref(false)
let shade = ref(false)

let progressBarWidth:any = ref(0)
let progressInterval:any = ref(null)

let freegameVersion = ref(0)

let startCount = ref(0)
let Dateloading = ref(true)
let Gameloading = ref(true)
let isVIP:any = ref(false)
let isGaming =  ref(false)
let historykey = ref(0)
const nochangeYear  = ref<number>(2025)
let currentYear = ref<number>(2025)
let curWeek = ref(0)
let HashWeek = ref([])
let forceUpdate = ref(0)
let curWeekInfo:any = reactive([])
//领取游戏
let autoReceive = ref(false)
let chooseCount = ref(0)
let lineupList:any = reactive([])
let monthcount:any = ref(0)
//存在喜加一的周数
let acTiveWeek = ref([])
let RouteIndex = ref<number>(0)
let GameList:any = reactive([])
let HistoryList:any = ref([])
let steamList:any = ref([])
let epicList:any = ref([])
let AllFreeList = ref([])
let ReceiveGameInfo:any = ref([])
let ReceiveGameName:any = ref([])
let SzFreeGame:any = ref({
  missing:0,
  missing_price:0,
  ok:0,
  ok_price:0,
})

let QzFreeGame:any = ref({
  missing:0,
  missing_price:0,
  ok:0,
  ok_price:0,
})

let autoSeconds = ref(60*60*3)
let autoTimer:any = ref(null)
// @ts-ignore
const gamepp = window.gamepp as any;
onMounted (async() => 
{
  // handleAllGameList()
  gamepp.webapp.onInternalAppEvent.addEventListener((value:any) => {
    console.warn('value',value);
    
    if(value.action == 'updateAcoount')
    {
      console.warn('GameListEx',GameList);
      console.warn('更新账号',value);
      handleAllGameList()
      let index = GameList[0].accountList.findIndex((item:any)=>{
        return item.localIndex == value.index
      })
      GameList.forEach((item:any)=>
      {
        item.accountList[index].active = false
      })

      console.warn('GameList',GameList);
    }
  });


   
  let secondstime = JSON.parse(localStorage.getItem('autoSeconds')as any)
  if(!secondstime)
  {
      localStorage.setItem('autoSeconds',JSON.stringify(autoSeconds.value))
  }
  else
  {
      autoSeconds.value = secondstime
  }

   await getAllFreeGamelist_ ()
  //喜加一
   await INIT()
   isVIP.value = await gamepp.user.isVIP.promise();
   caculterAllPrice()
   updateHashWeek();
   
  //  animateValue(0,SzFreeGame.value.ok_price,1000);
  let Val = JSON.parse(localStorage.getItem('FreeGamePlan') as any)
  if(!Val)
  {
    FreeGamePlan.value = false
  }
  else
  {
    FreeGamePlan.value = true
  }

  let freegame = await gamepp.package.getversion.promise("GameFreeGame")
  freegameVersion.value = freegame.version
  const auto = localStorage.getItem('autoReceive')
   if(auto)
   {
    autoReceive.value = true
    console.warn('自动领取状态::',auto);
    
    autoTimer.value = setInterval(async() => {
      console.log('autoSeconds.value',autoSeconds.value);
      if(autoSeconds.value>1)
      {
        autoSeconds.value--
        localStorage.setItem('autoSeconds',JSON.stringify(autoSeconds.value))
      }
      else
      {
        autoSeconds.value = 60*60*3
        await autoFree() //执行领取
      }
    }, 1000);

    //  let needclose = JSON.parse(localStorage.getItem('needclose') as any)
    //  if(needclose == 1)
    //  {
    //   await autoFree()
    //   const obj ={action:'autoFree'}
    //   await gamepp.webapp.sendInternalAppEvent.promise('background', obj)
    //   localStorage.setItem('needclose',JSON.stringify(0))
    //  }
   
   }
})

function addStartAnimate () 
{
      progressInterval.value = setInterval(() => {
        if (progressBarWidth.value === 140) {
          progressBarWidth.value = 0
        } else {
          progressBarWidth.value++
        }
      }, 20)
}

 function  removeStartAnimate () 
 {
      clearInterval(progressInterval.value)
      progressInterval.value = null
      progressBarWidth.value = 0
}

const safeConfirm2 = () =>
{
  FreeGamePlan.value = true
  Planshow2.value = false
  localStorage.setItem('FreeGamePlan',JSON.stringify(true))
}

const safeConfirm  = async() =>
{
      if (FreeGameText.value === '同意' || FreeGameText.value === 'Y' || FreeGameText.value === 'y') {
        Planshow.value = false
        FreeGamePlan.value = true
        //
        console.log('sendConfirm');
        const obj = 
        {
          action:'AgreePlan'
        }
        await gamepp.webapp.sendInternalAppEvent.promise('FreeGame', obj)
      } 
      else 
      {
        return false
      }
    }

const closePlan2 = ()=>{
  Planshow2.value = false
}

const openPlan2 = ()=>
{
  Planshow2.value = true
}

const closePlan = ()=>
{
  Planshow.value = false
}

const openPlan = () =>
{
  Planshow.value = true
}

const handleopenshade= () =>
{
  shade.value = true
} 

const handlecloseshade= () =>
{
  shade.value = false
} 

const autoFree = async() =>
{
  for(const week of curWeekInfo)
  {
    if(week.freegamelist.length>0)
    {
      for(const item of week.freegamelist)
      {
          item.choosen = true
          chooseCount.value++
      }
    }
  }

  await getFreeGame(false)
}

const  toogleMode = async() =>
{
  clearInterval(autoTimer.value)
  autoTimer.value = null
  removeStartAnimate()
  for(const item of curWeekInfo)
  {
    if(item.freegamelist.length>0)
    {
       for(const i of item.freegamelist)
       {
          i.choosen = false
       }
    }
  }
  chooseCount.value = 0
  if(isVIP.value)
  {
    autoReceive.value = !autoReceive.value
    localStorage.setItem('autoReceive',JSON.stringify(autoReceive.value))
    if(autoReceive.value)
    {
      autoTimer.value = setInterval(async() => {
      if(autoSeconds.value>0)
      {
        autoSeconds.value--
      }
      else
      {
        autoSeconds.value = 60*60*3
        await autoFree() //执行领取
      }
    }, 1000);
       autoSeconds.value = 60*60*3
      //将所有游戏选中并领取
       await autoFree()
      //  addStartAnimate()
    }else
    {
      
    }
    }
    else
    {
      clearInterval(autoTimer.value)
      autoTimer.value = null
      removeStartAnimate()
      ElMessage({
              message: '该功能仅游戏加加VIP可使用',
              type: 'warning',
              grouping:true
            })
      return
    }
  }
// 监听 curWeekInfo 的变化
watch(curWeekInfo, () => {
      console.warn('curWeekInfochanged!!!!!!!!!!!!!!!!!!!!!!!');
      updateHashWeek();
    }, { deep: true });

const updateHashWeek = () => 
{
  console.warn('updateHashWeek !!!!!!!!!!!!!!!!!!!!!!!',HashWeek.value);
  HashWeek.value = curWeekInfo
    .filter(item => item.freegamelist.length > 0)
    .map(item => item.week);
};

const caculterAllPrice = () =>
{
  let ReceivedArr:any = []
  let Receivedcount:number = 0
  let ReceivedPrice:number = 0
  monthcount.value = []
  const result = HistoryList.value.forEach((item:any)=>
  {
    if(item.status == 200)
    {
      const givenTime:any = new Date(item.receive_time);
      const currentTime:any  = new Date();
      const timeDifference:any  = currentTime - givenTime;
      const daysDifference = timeDifference / (1000 * 60 * 60 * 24);

    if (daysDifference > 30) 
    {
       
    } 
    else 
    {
      monthcount.value++
    }
      Receivedcount += 1
      const price = Number(item.original_price.replace(/[^0-9.]/g, ''));
      ReceivedPrice += Math.floor(price); // 向下取整
      ReceivedArr.push(item.game_name)
    }
  })
  let missingcount = 0
  let missprice = 0
  console.warn('ReceivedArr',ReceivedArr);
  
  for(let game of AllFreeList.value)
  {

      if(ReceivedArr.includes(game.game_name))
      {
        game.status = 1
      }
      else{
        missingcount += 1
        missprice += Number(game.original_price.replace(/[^0-9.]/g, ''))
        game.status = 0
      }
  }
  QzFreeGame.value.missing = missingcount
  QzFreeGame.value.missing_price = missprice.toFixed(0)
  QzFreeGame.value.ok = Receivedcount
  QzFreeGame.value.ok_price = ReceivedPrice.toFixed(0)
}

const getAllFreeGamelist_ = async () =>
{
  const curTime = Date.now();
  const result = JSON.parse(localStorage.getItem('AllFree') as any);

  if (!result || result.timestamp + 86400000 < curTime) {
    // 如果没有缓存数据，或者缓存数据已过期（超过24小时）
    AllFreeList.value = await getAllFreeGameList();
    if (AllFreeList.value.length === 0) return; // 如果获取的游戏列表为空，直接返回

    const obj = {
      timestamp: curTime,
      GameList: AllFreeList.value,
    };

    handleFree(AllFreeList.value); // 处理游戏列表
    localStorage.setItem('AllFree', JSON.stringify(obj)); // 更新本地存储
  } else {
    // 如果缓存数据未过期
    console.warn('No Need to refresh GameList', result);
    AllFreeList.value = result.GameList; // 使用缓存的游戏列表
  }
  console.warn('AllFreeList:::',AllFreeList.value);
  caculterAllPrice()
}

const handleFree  = (AllFreeList:any) =>
{
  for(let i = 0;i<AllFreeList.length;i++)
  {
    let item = AllFreeList[i]
    const date:any = new Date(item.start_date)
    item.year = date.getFullYear()
    item.week = getWeekByDate(item.start_date)
  }
}

const chooseYear = async(type:number) =>
{
    // Dateloading.value = true
    lineupList = []
    chooseCount.value = 0
    currentYear.value += type
    
    console.warn('currentYear.value',currentYear.value);
    // await initGameList()
    console.warn('GameList',GameList);

    await handleCurWeekInfo(currentYear.value)
    // getActiveWeekByHistory(HistoryList)
    await handleWeeklyDetail(currentYear.value)
    //将免费游戏丢进周历
    await pushFreeGameInWeek()
    // Dateloading.value = false
    await nextTick(); 
} 

 const INIT = async() =>
{
  //获取历史记录
  HistoryList.value = await getHistoryList()
  console.warn('历史记录：：',HistoryList.value);
  
  //获取游戏列表
  await initGameList()
  //获取历史列表
  //处理领取游戏周数
  nochangeYear.value = (new Date()).getFullYear()
  currentYear.value = (new Date()).getFullYear()
  await handleCurWeekInfo(currentYear.value)
  await handleWeeklyDetail(currentYear.value)

 
  await pushFreeGameInWeek()

}

const handleAccount = async() =>
{
  const accountList = JSON.parse(localStorage.getItem('account') as any)
  for(const game of GameList)
  {
      for(const item of accountList)
      {
         const index = game.accountList.findIndex( (v:any) => 
        {
          return v.platform_name === item.platform_name
        })
        if(index == -1)
        {
          item.receive = 0
          if(item.platform_name !== ''){
            game.accountList.push(item)
            console.log('push active::',item);
          }
        }
      }
  }
  console.warn('处理之后的GameList');
  localStorage.setItem('allGameList',JSON.stringify(GameList))
  await INIT()
  forceUpdate.value = forceUpdate.value+1
}

const handledelete = async() =>
{
  const accountList = JSON.parse(localStorage.getItem('account') as any)
  for(const game of GameList)
  {
    game.accountList = game.accountList.filter(item => {
      // 查找item是否存在于accountList中
      const index = accountList.findIndex(v => v.platform_name === item.platform_name);
      return index !== -1; // 如果找到了，则返回true，保留该项；否则返回false，移除该项
    });
  }
  console.warn('处理之后的GameList');
  localStorage.setItem('allGameList',JSON.stringify(GameList))
  await INIT()
  forceUpdate.value = forceUpdate.value+1
}

const handleCurWeekInfo = async(year:number) =>
{
  curWeekInfo = []
  // await nextTick(); 
  console.warn('当前年份：：',year);
  let currentYear = (new Date()).getFullYear()
  curWeek.value= getWeekByDate(new Date())

  if(currentYear !== year)
  {
    curWeek.value = 53
  }
  console.warn('当前周数：：',curWeek);
  
  for(let i = 1 ;i <= curWeek.value;i++)
  { 
    let obj = {
      week:i,
      choosen:false,
      freegamelist:[],//By AllGameList
      gamelist:[],
      history:[],
      status:0,
    }
    curWeekInfo.unshift(obj)
  }
}

const initGameList = async() =>
{
  GameList = []
  let GameInfo = []
  try{
    GameInfo = await getGameList()
  }
  catch
  {
    GameInfo = await getGameList()
  }
 
  for(let i = 0;i<GameInfo.length;i++)
  {
    GameList.push(GameInfo[i])
  }
  console.warn('GameList',GameList);
  const result = handleAllGameList()
  return result
}

const getActiveWeekByHistory = (HistoryList:Array<any>) =>
{
    let activeWeek:any = {}
    const successList = HistoryList.filter((item:any)=>
    {
      if(item.status == 200)
      {
        let week = getWeekByDate(item.receive_time)
        let year = (new Date(item.receive_time)).getFullYear()
          if(!activeWeek.hasOwnProperty(year))
          {
            activeWeek[year] = {}
            activeWeek[year][week] = [item]
          }
          else
          {
            if(!activeWeek[year].hasOwnProperty(`${week}`))
            {
              activeWeek[year][week] = [item]
            }else
            {
              activeWeek[year][week].unshift(item)
            }
          }
      }
      return item.status == 200
    })
    return activeWeek
}

const handleWeeklyDetail = async (YEAR:number) =>
{   
   //处理当前周历细节 

  await getReceivedGame()

  Gameloading.value = false
  Dateloading.value = false
    let curData = AllFreeList.value.filter((item:any) => 
    {
      return item.year == YEAR
    })

      for(let i = 0 ;i < curData.length;i++)
      {
        let item:any = curData[i]
        for(let j = 0;j < curWeekInfo.length;j++)
        {
          let curInfo = curWeekInfo[j]
          //当前周的游戏
          if(item.week == curInfo.week)
          {
            curWeekInfo[j].gamelist.push(item)
          }
        }
      } 

      //处理gamelist里面游戏是否领取
      for(let j = 0;j < curWeekInfo.length;j++)
      {
        if(curWeekInfo[j].gamelist.length !== 0)
        {
           for(const item of curWeekInfo[j].gamelist)
           {
            let receiveArr = []

            let index = ReceiveGameInfo.value.findIndex((v=>{
              return  v.game_name === item.game_name
            }))
            
            
            for(const history of HistoryList.value)
            {
              if(item.game_name == history.game_name && history.status == 200)
              {
                receiveArr.push(history)
                // console.log('%chistory','color:red',history,item);
                
              }
            }

            if(index !== -1)
            {
                if(receiveArr.length > 1)
                {
                  let arr:any = []
                  receiveArr.forEach((item)=>{
                    arr.push(item.platform_name)
                  })
                  item.receivePlatformName = arr
                  item.platform_name = ReceiveGameInfo.value[index].platform_name +' 等'+ receiveArr.length + '个'
                  item.ssCount = '¥'+(Number(item.original_price.replace(/[^0-9.]/g, ''))*receiveArr.length).toFixed(0)
                  console.warn('ssCount',item.original_price,receiveArr.length,Number(item.original_price.replace(/[^0-9.]/g, ''))*receiveArr.length);
                  
                }
                else
                {
                  // console.warn('== 1个',ReceiveGameInfo.value[index]);
                  
                  item.platform_name = ReceiveGameInfo.value[index].platform_name
                  item.ssCount = item.original_price

                }
                item.receive_time = ReceiveGameInfo.value[index].receive_time
                item.platform = ReceiveGameInfo.value[index].platform
                item.status = 1
            }
            else
            {
                item.ssCount = item.original_price
                item.status = 0
            }
          }

          const reason = curWeekInfo[j].gamelist.every((item)=>{
            return item.status == 1
          })
          if(reason)
          {
            curWeekInfo[j].status = 1
          }
          
          curWeekInfo[j].gamelist.forEach((item:any)=>{
            //错过领取游戏
            if(item.status == 0)
            {
              SzFreeGame.value.missing += 1
              const price = Number(item.original_price.replace(/[^0-9.]/g, ''));
              SzFreeGame.value.missing_price += Math.floor(price); // 向下取整
            }
          })
        }
       
      }

      //处理周历圆点状态
      historykey.value++
      console.log('%c curWeekInfo','color:green',curWeekInfo);
}

const getReceivedGame = async () =>
{
  SzFreeGame.value = {
    missing:0,
    missing_price:0,
    ok:0,
    ok_price:0,
  }

  for(const item of HistoryList.value)
  {
    const date:any = new Date(item.receive_time);
    // 获取年份
    const year = date.getFullYear();
    console.warn('curYear:',year,currentYear.value);
    
    if(item.status == 200 &&  year == currentYear.value)
    // if(item.status == 200)
    {
      ReceiveGameInfo.value.push(item)
      SzFreeGame.value.ok += 1
      const price = Number(item.original_price.replace(/[^0-9.]/g, ''));
      SzFreeGame.value.ok_price += Math.floor(price); // 向下取整
      ReceiveGameName.value.push(item.game_name)
    }
  }

  ReceiveGameName.value = Array.from(new Set(ReceiveGameName.value));
  setTimeout(() => {
    startCount.value = SzFreeGame.value.ok_price
  }, 1000);
  console.warn('领取成功的游戏:',ReceiveGameName.value);
  console.warn('领取成功的游戏信息:',ReceiveGameInfo.value);
  //计算未领取过的游戏
}

const pushFreeGameInWeek = async() =>
{
  for(let item of GameList)
  {
    if(currentYear.value == (new Date(item.start_date)).getFullYear())
    {
      const week = getWeekByDate(item.start_date)
      const index = curWeekInfo.findIndex((v:any)=>
      {
        return v.week == week
      })

      curWeekInfo[index].freegamelist.push(item)
      curWeekInfo[index].freegamelist.forEach((v)=>{
        v.choosen = false
      })
    }
  }
 
  console.warn('处理之后的周历',curWeekInfo);
  forceUpdate.value = forceUpdate.value + 1

  for(const item of curWeekInfo)
  {
    if(item.freegamelist.length > 0)
    {
      console.log('%c status == 2 status == 2 status == 2 ','color:green',item);
      
      item.status = 2
    }
  }

}

const deleteDelayGame = () =>
{
  const date1 = new Date();
  GameList = GameList.filter((item:any)=>
  {
    const date2 = new Date(item.expired_date);
    return  date1 < date2
  })
}

const getWeekByDate = (dateStr:any) => {
    // 将日期字符串转换为Date对象
    const date:any = new Date(dateStr);
    // 获取年份
    const year = date.getFullYear();
    // 创建该年1月1日的Date对象
    const firstDayOfYear:any = new Date(year, 0, 1);
    // 计算从该年1月1日到指定日期的天数差
    const diffDays = (date - firstDayOfYear) / (24 * 60 * 60 * 1000) + 1;
    // 计算所在的周数
    const weekOfYear = Math.ceil(diffDays / 7);
    return weekOfYear;
}

async function setting(index:any)
{
  if(index == 0)
  { // 最小化
    await gamepp.webapp.windows.minimize.promise('FreeGame');
  }
  else if (index == 3)
  {
    await gamepp.webapp.windows.close.promise('FreeGame')
  }
  console.warn(index);
}


const  getAccountList =  ()  => 
{
  const accountList = JSON.parse(localStorage.getItem('account') as any)
  if(!accountList)
  {
    return []
  }
  if (accountList.every((v:any, i:number) => { return v.idle })) 
  {
    return []
  } 
  else 
  {
    const list = accountList.filter((v:any, i:number) => { return !v.idle })
    return list
  }
}

const handleAllGameList = () =>
{
  //筛选重复游戏
  if(GameList.length == 0) return false
  
  console.warn('GameList > 0');
  //
  let localGameList = JSON.parse(localStorage.getItem('allGameList') as any)
  if(!localGameList || localGameList.length == 0)
  {
    console.warn('set GameList');
    localStorage.setItem('allGameList',JSON.stringify(GameList))
    localGameList = []
  }
  //判断是否有新游戏 直接和allGameList比较
  GameList.forEach((item:any,index:number)=>
  {
    const isNew = !localGameList.some((v:any) => {
      return v.game_name === item.game_name
    })

   if(isNew) //如果是新游戏 则将账号信息丢进去
   {
      item.owned = false  // 游戏已拥有
      item.success = false // 领取成功
      item.received = false // 曾经领取
      item.noBasicGame = false // 无游戏本体
      item.noAccount = true // 未登陆账号
      item.choosen = false // 选中
      item.accountList = getAccountList()
      item.accountList.forEach((account:any)=>
      {
        account.receive = 0 //该游戏账号领取状态
      })
   }
   else
   {
     const index = localGameList.findIndex( (v:any) => 
     {
      return v.game_name == item.game_name
     })
     if(index == -1)return
     GameList[index] = localGameList[index] //将数据进行存储
     console.log('数据存储：：',localGameList[index],item);
     
   }
  })
  localStorage.setItem('allGameList',JSON.stringify(GameList))
  deleteDelayGame()
  return true
}

const startRecevieGame = async(isManual:boolean) =>
{
  await gamepp.freegame.updatePreload.promise()
  const localAccountList = JSON.parse(localStorage.getItem('account') as any)
  //
  for(let i = 0 ;i < GameList.length;i++)
  {
    let THEGAME = GameList[i]
    const param1 = THEGAME.platform === 'steam' ? THEGAME.sub_id : THEGAME.get_url
    const param2 = THEGAME.platform === 'steam' ? THEGAME.app_id : ''

    if(GameList.choosen)
    {
      if(GameList['accounList'].length == 0)
      {
        ElMessage({
            message: '请至少添加1个平台账号',
            type: 'warning',
            grouping:true
          })
        return
      }
      else
      {
        for(let j = 0;j < GameList['accounList'].length;j++)
        {
           let THEACCOUNT = GameList['accounList'][j]
           //判断是否禁用
           let LINEUPINFO = {
              gameindex:j,
              platform:THEACCOUNT.platform,
              param1: param1,
              param2: param2,
              platform_name: THEACCOUNT.platform_name,
              persist: THEACCOUNT.persist,
              id: THEGAME.id,
              game_name: THEGAME.game_name,
              receiving: false, // 当前任务领取状态 true 正在领取，false 尚未领取 || 领取完成
              received: false, // 当前任务领取状态 true 领取完成， false 尚未领取
              resCode: -1, // 领取结果返回码
              original_price:THEGAME.original_price,
              localIndex: THEACCOUNT.localIndex,
              cover: THEGAME.cover,
              CancelStatus:false
           }

           let res = await gamepp.freegame.getGame.promise(LINEUPINFO.platform, LINEUPINFO.param1, LINEUPINFO.param2, LINEUPINFO.persist, isManual, LINEUPINFO.id)
           if(res.code == 1 )
           {
            let TEMPLIST = JSON.parse(localStorage.getItem('account') as any)
            TEMPLIST[LINEUPINFO.localIndex].loginStatus = false
            localStorage.setItem('account', JSON.stringify(TEMPLIST))
           }

          LINEUPINFO.received = true
          LINEUPINFO.receiving = false
          LINEUPINFO.resCode = res.code

          let HistoryList = JSON.parse(localStorage.getItem('freeGameHistoryList') as any )
          if(HistoryList === null)
          {
            HistoryList = [{
              status:res.code,
              game_name:LINEUPINFO.game_name,
              platform:LINEUPINFO.platform,
              platform_name:LINEUPINFO.platform_name,
              receive_time: getTime(),
              original_price:LINEUPINFO.original_price
            }]
          }
            else
            {
               HistoryList.unshift({
              status:res.code,
              game_name:LINEUPINFO.game_name,
              platform:LINEUPINFO.platform,
              platform_name:LINEUPINFO.platform_name,
              receive_time: getTime(),
              original_price:LINEUPINFO.original_price
            })
          }
          localStorage.setItem('freeGameHistoryList', JSON.stringify(HistoryList))
          let LocalGame = JSON.parse(localStorage.getItem('allGameList'))
          //END
        }
      }
    }
  }
}

const chooseEvent = (zindex:number, index:number) => {
  const reversedCurWeekInfo = curWeekInfo;
  const selectedItem = reversedCurWeekInfo[zindex].freegamelist[index];
  console.warn('selectedItem',selectedItem);
  console.log('%c 选中项','color:green',selectedItem);
  // 直接修改 selectedItem 的 choosen 属性
  selectedItem.choosen = !selectedItem.choosen;
  if(selectedItem.choosen)
  {
    //reactive
    lineupList.push(selectedItem)
    if(chooseCount.value < GameList.length)
    {
      chooseCount.value++
    }
    
    console.log('%c 选中::领取','color:green',lineupList);
  } 
  else
  {
    if(chooseCount.value > 0)
    {
      chooseCount.value--
    }
    
    lineupList = lineupList.filter((item:any)=> {return item.game_name !== selectedItem.game_name})
    console.log('%c 取消选中::领取','color:green',lineupList);
  }
};


const containerRef = ref(null); 
const weekRefs:any = ref({}); 
const componentKey = ref(0)


const scrollToWeek =  (weekNumber,index) => {
  for(let i = 0; i < curWeekInfo.length; i++) {
    curWeekInfo[i].choosen = false;
  }
  
  curWeekInfo[index].choosen = true;
  componentKey.value++
  const targetElement = weekRefs.value[weekNumber];
  if (targetElement) {
  
    const targetTop = targetElement.offsetTop;
    const container:any = containerRef.value;
    
    container.scrollTo({
      top: targetTop,
      behavior: "smooth",
    });
  }
};

const setWeekRef = (week) => {
  return (el) => {
    if (el) {
      weekRefs.value[week] = el.$el; // 存储每个周的 DOM 元素
    }
  };
};

const getWeekRange = (year: number, weekNumber: number, t: any) => {
    // 创建一个日期对象，表示当年的1月1日
    const firstDayOfYear = new Date(year, 0, 1); // 1月1日是第一周的第一天

    // 计算第weekNumber周的开始日期
    const weekStart = new Date(firstDayOfYear.getTime() + (weekNumber - 1) * 7 * 24 * 60 * 60 * 1000);

    // 计算第weekNumber周的结束日期
    let weekEnd;
    if (weekNumber === 53) {
        // 如果是第53周，需要特殊处理
        const lastDayOfYear = new Date(year, 11, 31); // 12月31日
        weekEnd = lastDayOfYear;
    } else {
        weekEnd = new Date(weekStart.getTime() + 6 * 24 * 60 * 60 * 1000);
    }

    // 格式化日期为“M月D日”
    function formatDate(date: Date, t: (arg0: string) => any) {
        return `${date.getMonth() + 1}${t('GamePlusOne.month')}${date.getDate()}${t('GamePlusOne.day')}`;
    }

    // 返回格式化的字符串
    return `${formatDate(weekStart, t)} - ${formatDate(weekEnd, t)}`;
};

const handleUpdate = () =>
{
  console.warn('触发：：');
  RouteIndex.value = 0
  console.warn('RouteIndex.value',RouteIndex.value);
}

const RoAccount = () =>
{
  RouteIndex.value = 1
  console.warn('RoAccount.value',RouteIndex.value);
}

const getFreeGame = async (isManual:boolean) => //获取喜加一游戏
{

  if(isManual)
  {
    shade.value = true
  }
  
  console.warn('领取游戏开始::');
  isGaming.value = true
  if(!FreeGamePlan.value)
  {
    ElMessage({
        message: '请同意《喜加一领取计划》',
        type: 'warning',
        grouping:true
      });
      autoReceive.value = false
      isGaming.value = false
      shade.value = false
      removeStartAnimate()
      return
  }

  if(!gamepp.user.getToken.sync())
  {
    removeStartAnimate()
    ElMessage({
        message: '请登录游戏加加',
        type: 'warning',
        grouping:true
      });
      console.warn('请登录游戏加加');
      autoReceive.value = false
      isGaming.value = false
      shade.value = false
      return
  }

   if(!isVIP.value && monthcount.value > 3)
  {
    removeStartAnimate()
    ElMessage({
        message: '本月领取次数已达上限',
        type: 'warning',
        grouping:true
      });
      console.warn('本月领取次数已达上限');
      return
  }

  if(chooseCount.value == 0)
  {
    ElMessage({ message:'请选择游戏',type: 'warning',grouping:true})
    removeStartAnimate()
    clearInterval(progressInterval.value)
    progressInterval.value = null
    progressBarWidth.value = 0
    // autoReceive.value = false
    isGaming.value = false
  }
  else
  {
    addStartAnimate()
    for (let index = 0; index < curWeekInfo.length; index++) 
    {
       let curWeek = curWeekInfo[index]
       if(curWeekInfo[index].freegamelist.length>0)
       {
         for(let i = 0; i < curWeekInfo[index].freegamelist.length; i++)
         {
            let game = curWeekInfo[index].freegamelist[i]
            if(game.choosen)
            {
              console.warn('选中的免费游戏领取::',game);
              const param1 = game.platform === 'steam' ? game.sub_id : game.get_url
              const param2 = game.platform === 'steam' ? game.app_id : ''
            if(game['accountList'].length == 0)
            {
              ElMessage({message: '请至少添加1个平台账号',type: 'warning',grouping:true})
              autoReceive.value = false
              isGaming.value = false
              
            }
            else
            {
              for(let j = 0; j < game['accountList'].length; j++)
              {
                let account = game['accountList'][j]
                if(account.receive == 200 || account.receive == 3) {
                  // ElMessage({
                  //   message: `账号${account.platform_name} 已领取过`,
                  //   type: 'warning',
                  //   grouping: true
                  // });
                  continue; // 直接在此处返回，不再继续执行后续代码
                }
                if(!account.active)continue
                if(!account.loginStatus)
                {
                    ElMessage({
                      message: `账号${account.platform_name} 已失效`,
                      type: 'warning',
                      grouping: true
                    });
                    continue
                }
                  console.warn('当前领取账号::',account);
                  console.warn('当前领取游戏::',game);
                  account.receive = 10086
                  let res = await gamepp.freegame.getGame.promise(game.platform, param1, param2,account.persist,isManual, game.id)
                  console.log('%c 领取结果::','color:green',res);
                  account.receive = res.code
                  //存储历史记录
                if(res.code == 200 || res.code == 3)//分两种情况
                {
                  if(res.code == 200 )
                  { 
                      //总领取价值
                      const price = Number(game.original_price.replace(/[^0-9.]/g, ''));
                      SzFreeGame.value.ok_price += Math.floor(price);
                      setTimeout(() => {
                      startCount.value = SzFreeGame.value.ok_price
                      }, 1000);
                      monthcount.value++
                      //上传历史记录
                      postReceiveGameInfo(game.id,account.platform_name,res.code,res.msg,true)
                  }
                }
                else if(res.code == 1)
                {
                    //账号失效
                    const localAccountList = JSON.parse(localStorage.getItem('account') as any)
                    localAccountList.forEach((v:any)=>{
                      if(v.platform_name == account.platform_name)
                      {
                        v.Loginstatus = false
                      }
                    })
                    localStorage.setItem('account', JSON.stringify(localAccountList))
                }
              }
              //更新游戏里的accountList
              console.warn('更新before GameList::',GameList);
              GameList.forEach((item:any)=>
              {
                if(item.game_name == game['game_name'])
                {
                  item['accountList'] = game['accountList']
                }
              })
              console.warn('更新后before GameList::',GameList);
              localStorage.setItem('allGameList',JSON.stringify(GameList))
            }
            }
         }
       }
    }
  }
  shade.value = false
   //刷新历史记录
   HistoryList.value = await getHistoryList()  
   caculterAllPrice()
   console.warn('历史记录L：：',HistoryList.value);
   isGaming.value = false
   //刷新账号价值
   removeStartAnimate()
   ElMessage({
                message: `本轮领取已完成`,
                type: 'success',
                grouping: true
            });
   removeStartAnimate()
}

const  postReceiveGameInfo  = async(gid:any, platformName:string, code:number, errMessage:any, tryAgain:boolean) =>{
  const uid = gamepp.user.getUserID.sync(); // 假设 gamepp 是一个全局对象，用于获取用户信息
  const jsonObj = {
    is_cloud: 0,
    receiver_uid: -1,
    uid: uid,
    gid: gid,
    platform_name: platformName,
    status: code,
    errMessage: errMessage
  };

  console.warn('uploadData:', jsonObj);
  const encodeData = gamepp.user.getEncodeInfo.sync(JSON.stringify(jsonObj));
  try {
    const response = await axios.post('https://free-game.gamepp.com/game/addReceiveGameInfo', encodeData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    console.log('服务器返回:', response.data);
  } catch (error) {
    console.log('服务器返回fail', error);

    if (tryAgain) {
      setTimeout(() => {
        postReceiveGameInfo(gid, platformName, code, errMessage, false);
      }, 5000);
    }
  }
}

let zoomValue = ref<number>(1)
async function initZoom() {
  try {
    zoomValue.value = await gamepp.display.getScaleFromWindowInMonitor.promise();
    const zoomWithSystem = gamepp.setting.getInteger.sync(313)
    if (zoomWithSystem === 1) {
      // 设置body zoom
      document.body.style.zoom = zoomValue.value
      gamepp.webapp.windows.resize.sync('FreeGame',Math.floor(1312 * zoomValue.value),Math.floor(732 * zoomValue.value))
    }
  }catch (e) {
    zoomValue.value = 1
  }

  try {
    gamepp.display.onDisplayMetricsChanged.addEventListener( async (scaleFactor:number)=>{
      const zoomWithSystem = gamepp.setting.getInteger.sync(313)
      if (zoomWithSystem === 1) {
        console.log('display',scaleFactor)
        // zoom.value = display.scaleFactor
        zoomValue.value = scaleFactor
        document.body.style.zoom = zoomValue.value
        try{
          gamepp.webapp.windows.resize.sync('FreeGame',Math.floor(1312 * zoomValue.value),Math.floor(732 * zoomValue.value))
        }catch (e) {
          console.log(e)
        }
      }
    })
  }catch (e) {
    console.log(e)
  }
}

onBeforeMount(()=>{
    initZoom()
})
</script>

<template>
  <div class="FreeGame">
    <div class="shade" v-show="shade"></div>
    <div class="GreatPlan" v-show="Planshow">
      <div class="Plan" >
      <div class="PlanNav">
        <span>添加账号须知</span>
        <span class="iconfont icon-Close" style="cursor: pointer;" @click="closePlan"></span>
      </div>
      <div class="text1">
        游戏加加不会记录您的账号密码等信息，建议绑定平台令牌等账号安全保护工具，并开启登录验证、交易验证等功能加强账号安全。
      </div>
      <div class="text2">
        同时游戏加加建议，为了您的账户财产安全，建议使用没有购买行为的平台账号体验此功能。 目前仅支持已开启双重验证的账号进行绑定
      </div>
      <div class="text3">
        目前仅支持已开启双重验证的账号进行绑定
      </div>
      <div class="text4">
        <span style="color: #62A5FF;">请输入如下文字：</span><span>同意 / Y</span>
      </div>
      <div style="width:156px;height: 40px;"> <el-input v-model="FreeGameText"  placeholder="" /></div>
      <div class="confirm" :class="{'confirmActive': FreeGameText === '同意' || FreeGameText === 'Y' || FreeGameText === 'y'}" @click="safeConfirm()">
        确认
      </div>
    </div>
    </div>
    
    <div class="GreatPlan2" v-show="Planshow2">
      <div class="Plan" >
      <div class="PlanNav">
        <span>免责声明</span>
        <span class="iconfont icon-Close" style="cursor: pointer;" @click="closePlan2"></span>
      </div>
      <div class="text1">
        游戏加加喜加一领取计划
      </div>
      <div class="text2">
        1.游戏加加“喜加一领取计划”以下简称“计划”，在登录平台账号后保存账号凭证，并实现自动为玩家领取游戏。
      </div>
      <div class="text3">
        2.以任何方式通过游戏加加喜加一功能领取游戏即视为自愿加入计划，最终解释权归游戏加加所有
      </div>
      <div class="confirm"  @click="safeConfirm2()">
        同意
      </div>
    </div>
    </div>


    <div class="Nav">
      <div class="GPPInfo" style="width: 1100px;-webkit-app-region: drag;">
        <img src="../../assets/img/Public/logo_gpp.png" alt="">
        <span class="slogan">{{ $t('home.addOne') }}</span>
        <span class="slogan">V{{freegameVersion}}</span>
      </div>
      <RightTopIcons
              close-icon
              minimize-icon
              @close="setting(3)"
              @minimize="setting(0)"
              hover-color="#22232e"
          />
    </div>
    <div class="Content">
      <div class="FreeGame_Date" v-loading="Dateloading" element-loading-background="#2d2e39">
       <div class="DateNav">
        <el-icon  @click="chooseYear(-1)" color="#999999" :size="18"  style="margin-left: 30px;"><ArrowLeftBold /></el-icon>
        <span>{{ currentYear }}{{ $t('GamePlusOne.year') }} <span style="color: gray;font-size: 12px;margin-left: 3px;">{{ $t('GamePlusOne.weekstotal') }}</span></span>
        <el-icon v-show="nochangeYear > currentYear"  color="#999999" @click="chooseYear(1)" :size="18" style="margin-right: 30px;"><ArrowRightBold /></el-icon>
        <div  style="margin-right: 30px;width: 20px;height: 20px;" v-show="nochangeYear <= currentYear"></div>
       </div>
       <div class="DateTips">
        <div class="tipsitem"><div class="dot" style="background-color: green;"></div>{{ $t('GamePlusOne.success') }}</div>
        <div class="tipsitem"><div class="dot" style="background-color: #777777;"></div>{{ $t('GamePlusOne.fail') }}</div>
        <div class="tipsitem"><div class="dot" style="background-color: #3E7FE1;"></div>{{ $t('GamePlusOne.will') }}</div>
       </div>
      <div class="calenDer">
          <div class="Days" v-for="(item,index) in curWeekInfo"  :key="componentKey"  @click="scrollToWeek(item.week,index)" :class="[item.choosen?'choosen':'']">
            <p> {{ item.week }}</p>
            <div class="dots" :class="[item.status == 1?'sucesess':'',item.status == 0?'missing':'',item.status == 2?'ready':'']"></div>
        </div>
      </div>
      <div class="textLine line1"><span>{{ $t('GamePlusOne.missedGame') }}：</span><span style="color:#F14343;">{{SzFreeGame.missing}} {{ $t('GamePlusOne.text1') }} {{SzFreeGame.missing_price}}</span></div>
      <div class="textLine line2"><span>{{ $t('GamePlusOne.text2') }}：</span><span style="color:#35D57D;">{{SzFreeGame.ok}}{{ $t('GamePlusOne.text3') }}</span></div>
      <div class="GameValue">
         <div class="line_1">{{ $t('GamePlusOne.gamevalue') }}:</div>
         <div class="Total_price" style="color: #35D57D;">
          ￥ <CountTo :startVal="startCount" :endVal="SzFreeGame.ok_price" :duration="3000" />
          <!-- <div class="addPrice">+189</div> -->
        </div>
      </div>
     </div>
    <div class="FreeGame_Game">
      <!-- 喜加一 -->
      <div class="countOfGame"  v-show="RouteIndex == 0" style="margin-top: 10px;"><div><span>{{ $t('GamePlusOne.gamevalue1') }}<span style="color:#35D57D;margin: 0 5px;">{{QzFreeGame.ok }}</span>{{ $t('GamePlusOne.text4') }} <span style="color:#35D57D;margin: 0 5px;">￥{{ QzFreeGame.ok_price }}</span>{{ $t('GamePlusOne.Yuan') }}</span><span style="margin-left: 10px;">{{ $t('GamePlusOne.missedGame') }} <span style="color:#F14343;margin: 0 5px;">{{ QzFreeGame.missing }}</span>{{ $t('GamePlusOne.text6') }}<span style="color:#F14343;margin: 0 5px;">￥{{ QzFreeGame.missing_price }}</span>{{ $t('GamePlusOne.Yuan') }}</span></div><div class="platFrom"   v-show="RouteIndex == 0 && !Dateloading" @click.stop="RoAccount()">{{ $t('GamePlusOne.Platformaccountmanagement') }}</div></div>
      <div class="ReceiveGame scroll" v-show="RouteIndex == 0" ref="containerRef"  v-loading="Gameloading" element-loading-background="#2d2e39" >
        <div class="BIGWEEK" 
        v-for="(item,zindex) in curWeekInfo" 
        v-show="item.freegamelist.length>0 || item.gamelist.length>0"
        :key="forceUpdate"
        >
          <el-collapse  v-model="HashWeek">
            <el-collapse-item 
             :name="item.week"
             :ref="setWeekRef(item.week)"
            >
            <template #title>
              <div class="WEEK_" style="display: flex;align-items: center;">
                <div class="dots"  :class="[item.status == 1?'sucesess':'',item.status == 0?'missing':'',item.status == 2?'ready':'']"></div>
                <span style="margin-right: 5px;min-width: 50px;">
                  {{ $t('GamePlusOne.Thefirst') + item.week + $t('GamePlusOne.Week') }}
                </span>
                <span class="" style="min-width:130px;">  {{ getWeekRange(2025, item.week, $t) }}</span>
                <span v-show="item.freegamelist.length == 0" style="margin-left: 10px;background-color: transparent;" 
                  :class="[item.status == 1 ? 'sucesess' : '', item.status == 0 ? 'missing' : '', item.status == 2 ? 'ready' : '']">
                  {{
                    item.status == 0 ? $t('GamePlusOne.Missed1') :
                    item.status == 1 ? $t('GamePlusOne.Received2') :
                    $t('GamePlusOne.Receivedsoon2')
                  }}
                </span>
                <span  v-show="item.freegamelist.length >  0" style="margin-left: 10px;background-color: transparent;" class='ready'>{{ $t('GamePlusOne.Receivedsoon2') }}</span> 
              </div>
            
            </template>
                 <div class="FreeGameitem" v-show="item.freegamelist.length>0">
                   <GameItem  
                    v-for="(v,index) in item.freegamelist"
                    :key="index"
                    :GameInfo = v 
                    @click.stop="chooseEvent(zindex,index)"
                    :class="{ 'choosenOne': v.choosen }" 
                    >
                 </GameItem>
                 </div>
                 <div class="history" v-show="item.freegamelist.length == 0">
                    <div class="historyitem"   v-for="(hisItem,index) in item.gamelist" :key="historykey" style="position: relative;">
                       <img  style="width: 20px;height: 20px;" :src="item.platform =='epic'?epicicon:epicicon" alt="">
                       <span class="hisname">{{hisItem.game_name}}</span>                
                       <div style="height: 20px;width: 250px;position: relative;z-index: 1000;" @mouseenter="hisItem.showPlatformName = true"  @mouseleave="hisItem.showPlatformName = false" >
                         <span class="allname"  style=" /* 设置一个固定高度 */line-height: 20px;"><span  v-show="hisItem.status == 1" style="color: #999999;line-height: 20px;">{{ $t('GamePlusOne.Getaccount') }}：</span><span v-show="hisItem.status == 1" style="line-height: 20px">{{hisItem.platform_name}}</span></span>
                         <div class="platformName "  v-show="hisItem.showPlatformName && hisItem.receivePlatformName">
                          <div class="scroll" style="overflow: auto;width: 100%;height: 50px;">
                            <div class="innerPlat" v-for="(itemX,indexX) in hisItem.receivePlatformName">
                              {{ itemX }}
                            </div>
                          </div>
                         
                        </div>
                       </div>
                      
                       <span style="color: #35D57D;width: 100px;">{{ $t('GamePlusOne.Worth') }}:{{hisItem.ssCount}}</span>
                       <span style="color: #999999;width:220px;text-align: right;" v-show="hisItem.status == 1">{{hisItem.receive_time}} {{  $t('GamePlusOne.Received') }}</span>
                       <span style="color:#F14343;width: 220px;text-align: right;" v-show="hisItem.status == 0">{{  $t('GamePlusOne.Missed') }}</span>
                 </div>
                 </div>
            </el-collapse-item> 
          </el-collapse>
        </div>
        <!-- 游戏领取 -->
        
      <!-- 账号管理 -->
     
    </div>
    <Account 
    style="margin-top: 5px;"
    v-if="RouteIndex == 1" 
    @updateAccountInfo = "handleAccount()"
    @updateParentValue ="handleUpdate()" 
    @openshade = "handleopenshade()"
    @closeshade = "handlecloseshade()"
    @deleteAccountInfo = 'handledelete()'
    @openplan = 'openPlan()'
    >
    
    </Account>
    <div class="Free_bottom"  v-show="RouteIndex == 0">
      <div class="left">
      </div>
         <div class="right">
          <div style="margin-right: 10px;display: flex;align-items: center;height: 20px;">
            <el-checkbox v-model="FreeGamePlan" label=""/>
            <span style="cursor: pointer;" @click="openPlan2()" >{{ $t('GamePlusOne.agree') }}</span>
          </div>

          <div class="toogle" @click="toogleMode()">
            <img style="width: 14px;height: 14px;" src="./assets/icon/icon_switch_mode.png" alt="">
            {{ autoReceive ? $t('GamePlusOne.Auto') : $t('GamePlusOne.Manual') }}
          </div>
          <div>
            <div class="chooseBox"  v-show="!isGaming &&!autoReceive" :class="chooseCount == 0 ? 'zeroCount' : 'hasCount'" @click="getFreeGame(true)">
              <p style="font-size: 15px;">{{chooseCount==0? $t('GamePlusOne.Pleasechoose') : $t('GamePlusOne.Receive')}}</p>
              <p>{{ $t('GamePlusOne.Selected') }} <span>{{ chooseCount }}</span> {{ $t('GamePlusOne.text5') }}</p>
            </div>
            <div class="chooseBox hasCount" v-show="autoReceive">
              <p style="font-size: 15px;">{{$t('GamePlusOne.Automatic')}}</p>
              <p><span style="color: #35D57D;">{{ FormatSeconds2(autoSeconds) }}</span><span style="margin-left: 5px;color: white;">{{ $t('GamePlusOne.again') }}</span></p>
              <div class="progressBar" :style="{width: progressBarWidth + 'px'}"></div>
            </div>
            <div class="chooseBox hasCount" v-show="isGaming && !autoReceive ">
              <p style="font-size: 15px;">{{$t('GamePlusOne.Collecting')}}</p>
              <div class="progressBar" :style="{width: progressBarWidth + 'px'}"></div>
            </div>
            <div class="curMonth">{{ $t('GamePlusOne.ReceiveTimes') }} （{{monthcount}}/{{isVIP?'N':3}}）</div>
         </div>
         </div>
      </div>
  </div>
   
    </div>
  </div>
  
   
</template>
<style lang="scss">
   .FreeGame{
      width: 1280px;
      height: 700px;
      display: flex;
      flex-direction: column;
      background-color:#2d2e39;
      position: relative;
      border-radius: 6px;
      overflow: hidden;
      margin-left: 6px;
      margin-top: 6px;
      box-shadow: 0 1px 6px #0009;
      .GreatPlan2{
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.3);
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        .Plan
      { 
        box-shadow: 0px -5px 5px 0px rgba(0, 0, 0, 0.4);
        width: 650px;
        height: 400px;
        border-radius: 6px;
        background-color: #2B2C37;
        display: flex;
        flex-direction: column;
        align-items: center;
        z-index: 1000;
        font-size: 14px;
        color: #FFFFFF;
       
        .PlanNav
        {
          width: 95%;
          display: flex;
          justify-content: space-between;
          margin-top: 10px;
        }
        .text1{
          width: 90%;
          margin-top: 47px;
          font-size:18px;
          text-align: center;
          color: #62A5FF;
        }
        .text2{
          width: 90%;
          margin-top: 30px;
        }
        .text3{
          width: 90%;
          margin-top: 30px;
        }
       
        .confirm{
          margin-top: 110px;
          cursor: pointer;
          width:120px;
          height: 40px;
          background-color: #336AB5;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 6px;
        }
        .confirm:hover
        {
          background-color: #62A5FF;
        }

       
      }

      }
      .GreatPlan{
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.3);
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        .Plan
      { 
        box-shadow: 0px -5px 5px 0px rgba(0, 0, 0, 0.4);
        width: 650px;
        height: 400px;
        border-radius: 6px;
        background-color: #2B2C37;
        display: flex;
        flex-direction: column;
        align-items: center;
        z-index: 1000;
        font-size: 14px;
        color: #FFFFFF;
       
        .PlanNav
        {
          width: 95%;
          display: flex;
          justify-content: space-between;
          margin-top: 10px;
        }
        .text1{
          width: 80%;
          margin-top: 47px;
        }
        .text2{
          width: 80%;
          margin-top: 20px;
        }
        .text3{
          width: 80%;
          margin-top: 20px;
          color: #F14343;
        }
        .text4{
          margin-top: 40px;
          margin-bottom: 20px;
        }
        .confirm{
          margin-top: 20px;
          cursor: pointer;
          width:120px;
          height: 40px;
          background-color: #727281;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 6px;
        }

        .confirmActive{
          background-color: #36C78B;
        }
      }
      }
      
       .shade {
        z-index: 1000;
        width: 100%;
        height: 100%;
        border-radius: 6px;
        position: absolute;
        top: 0;
        left: 0;
        background-color: rgba(0, 0, 0, 0.3);
        }
      .Nav{
        width: 1280px;
        height: 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .GPPInfo{
          display: flex;
          align-items: center;
          img{
            width: 14px;
            height: 14px;
            margin: 0 10px;
          }
          .slogan{
            font-size: 14px;
            color: white;
            margin: 0 5px;
          }
        }
      }
      .Content{
        display: flex;
        background-color:#22232e;
        height: 100%;
        width: 100%;
        .calenDer{
          width: 300px;
          display: flex;
          flex-wrap: wrap;
          border-radius: 6px;
          margin: 10px 0 0 10px;
          .Days{
            border-radius: 6px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            width: 35px;
            height: 35px;
            color: white;
            margin-right: 6px;
            margin-top: 10px;
            .dots{
              width: 4px;
              height: 4px;
              border-radius: 50%;
              margin-top: 5px;
            }
            .sucesess{
              color:#35D57D ;
              background-color:#35D57D ;
            }
            .missing{
              color:#F14343;
              background-color: #999999;
            }
            .ready{
              color: #3E7FE1;
              background-color: #3E7FE1;
            }
          }
          .choosen{
            background-color:#3E404F;
          }
        }
      }
          .textLine{
            color: #FFFFFF;
            width: 265px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: absolute;
          }
          .line1{
            bottom: 140px;
            left: 15px;
          }
          .line2{
            bottom: 110px;
            left: 15px;
          }
          .GameValue{
            color: #FFFFFF;
            margin-left: 5px;
            width: 270px;
            height: 80px;
            background-color:#3A3B4C;
            border-radius: 4px;
            position: absolute;
            bottom: 10px;
            left: 10px;
            .line_1{
              margin: 10px 0 0 10px;
            }
            .Total_price{
              width: 100%;
              text-align: center;
              color: #35D57D;
              font-weight: 700;
              font-size:18px;
              .addPrice{
                color: #FFFFFF;
                font-size: 12px;
                margin-left: 1px;
              }
            }
          }
      .FreeGame_Date{
        height: 620px;
        width: 300px;
        background-color:#2B2C37;
        margin: 10px 20px 0 10px;
        border-radius: 6px;
        position: relative;
       
        .DateNav{
          width: 100%;
          display: flex;
          justify-content: space-between;
          margin-top: 20px;
          span{
            color: #FFFFFF;
            font-size: 14px;
          }
        }
        .DateTips{
          display: flex;
          margin-top: 15px;
          margin-left:30px;
          color: #FFFFFF;
          .tipsitem{
            margin-right: 30px;
            display: flex;
            align-items: center;
          }
          .dot{
            margin-right: 5px;
            width: 4px;
            height: 4px;
            border-radius: 50%;
          }
        }
      }
      .ReceiveGame
      {
        width: 943px;
        height: 500px;
        overflow-y: auto;
        position: relative;
        color: #FFFFFF;
        margin-top: 10px;
        border-radius: 6px;
        .BIGWEEK
        { width: 932px;
          border-radius: 5px;
          overflow: hidden;
          margin-bottom: 10px;
          .dots{
              width: 4px;
              height: 4px;
              border-radius: 50%;
            }
            .sucesess{
              color:#35D57D ;
              background-color:#35D57D ;
            }
            .missing{
              color:#F14343;
              background-color: #F14343;
            }
            .ready{
              color: #3E7FE1;
              background-color: #3E7FE1;
            }
        }
        .history{
          background-color: #2B2C37;
        }
      }
      
      .FreeGame_Game{
        height: 600px;
        width: 932px;
        .countOfGame{
          margin-bottom: 10px;
          color: white;
          width: 932px;
          display: flex;
          justify-content:space-between;
          align-items: center;
         .platFrom{
          min-width: 120px;
          height: 32px;
          border-radius: 4px;
          background-color: #336AB5;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          padding: 0 5px;
         }
         .platFrom:hover
         {
          background-color:#3579D5;
         }
        }
        .TabNav{
          display: flex;
          margin: 10px 0 0 10px;
          .NavItem
          {
            cursor: pointer;
            width: 100px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #FFFFFF;
            border-radius: 3px;
          }
          .Tabchoosen{
            background-color:#336AB5;
          }
        }
        .GameItem{
            width: 912px;
            margin-left: 13px;
            height: 212px;
            margin-top: 10px;
            box-sizing: border-box;
            background-color: #262731;
            border: 1px solid transparent;
            border-radius: 6px;
        }
       
      }
      .GameList{
        width: 100%;
        height: 30px;
        color: black;
      }
   }
 
  .history{
    display: flex;
    flex-direction: column;
    width: 100%;
    .historyitem{
      position: relative;
      span{
        display: inline-block; /* 或 block，根据需要选择 */
        white-space: nowrap; /* 防止内容换行 */
        overflow: hidden; /* 隐藏超出部分 */
        text-overflow: ellipsis; /* 超出部分显示省略号 */
      }
      .hisname{
        width: 280px;
      }
      padding: 0 18px;
      width: 97%;
      margin-top: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #FFFFFF;
    }
  }

  .choosenOne{
          border: 1px solid #3A79D0 !important;
      }
  .loginBtn{
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #3E4050;
    color: #999999;
    // width: 80px;
    height: 24px;
    border-radius: 6px;
    min-width: 80px;
    max-width: 200px;
    padding: 0 10px;
  }
  .loginBtn:hover{
    background-color: #4D5172;
    color: #FFFFFF;
  }
//底部
.Free_bottom{
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  color: #FFFFFF;
  margin-top: 20px;
  .toogle{
    min-width: 40px;
    height: 40px;
    border-radius: 4px;
    background-color:#2B2C37;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    cursor: pointer;
    padding: 0 5px;
  }
  .right{
    display: flex;
  }
 
  .chooseBox{
    .progressBar {
          left: 0;
          height: 50px;
          position: absolute;
          background: linear-gradient(to right, transparent ,rgba(255, 255, 255, 0.6));
        }
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 5px 0;
    // width: 140px;
    padding: 0 15px;
    height: 40px;
    border-radius: 4px;
    color: white;
    cursor: pointer;
  }
  .zeroCount{
    background-color: #666666;
  }
  .hasCount{
    background-color: #336AB5;
  }
  .curMonth{
    margin-top: 10px;
    width: 100%;
    text-align: center;
  }
}

   .scroll::-webkit-scrollbar {
    width: 5px;
    transition: 0.25s;
  }

  .scroll::-webkit-scrollbar-thumb {
    background: #71738C;
    border-radius: 3px;
  }

  .scroll::-webkit-scrollbar-thumb:hover {
    background-color: #71738C;
  }

  .scroll::-webkit-scrollbar-track {
    background-color: #2D2E39;
    width: 2px;
  }
  .el-icon.el-collapse-item__arrow{
    transform: rotate(90deg)
  }
  .el-icon.el-collapse-item__arrow.is-active {
    transform: rotate(270deg)
  } 
  
  .platformName
  {
    position: absolute;
    background-color: #3A3B4C;
    height: 50px !important;
    width: 200px;
    border-radius: 5px;
    top: 0px;
    left: 230px;
    flex-direction: column;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
  
    .innerPlat
    {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .allname:hover{
    cursor: pointer;
    color: #336AB5 !important;
  }
</style>
<style lang="scss">
 .ReceiveGame
      {
        .el-collapse{
          border: none;
        }
        .el-collapse-item
        {
          border: none;
          --el-collapse-header-height:30px;
          --el-collapse-header-text-color:#FFFFFF;
        }
        .el-collapse-item__header
        {
          background-color: #2B2C37;
          padding-left: 20px;
          border: none;
        }

        .el-collapse-item__wrap{
          background-color:#2B2C37;
          border-bottom-left-radius: 5px;
          border-bottom-right-radius: 5px;
          border-bottom: 1px solid #2B2C37;
       }
      .is-active{
          border-top-left-radius: 5px;
          border-top-right-radius: 5px;
          border-bottom-right-radius: 0px;
          border-bottom-left-radius: 0px;
      }
      }
      .Plan
      {
         .el-input__inner{
          text-align: center !important;
          color: #FFFFFF;
        }
        .el-input__wrapper{
          background-color:#3A3B4C;
        }
        .el-input{
          --el-input-border-color:#3A3B4C;
          --el-input-hover-border:#3A3B4C;
          --el-input-focus-border:#3A3B4C;
        }
      }

</style>
