<script setup lang="ts">
import {Core, CPUComplexInfo, processCoreAssignStore} from "../stores";
import {onMounted, watch, computed, onBeforeMount,ref} from "vue";
import {ElMessage} from "element-plus";
import {useI18n} from "vue-i18n";

interface computedCore extends Core {
    outerIndex: number;
    innerIndex: number;
    groupId: number;
}

const {t} = useI18n()
const $store = processCoreAssignStore();
let peCoreArr = ref<Array<number>>([0]); // 大核，小核，小小核

onBeforeMount(()=>{
    let tempArr: Array<number> = [];
    $store.cpuComplexInfo.forEach((item) => {
        item.Cores.forEach((core) => {
            tempArr.push(core.EfficiencyClass);
        })
    })
    tempArr = Array.from(new Set(tempArr)) // 去重
    tempArr.sort((a, b) => b - a)
    peCoreArr.value = tempArr
})

onMounted(() => {
    $store.getGroupCpuSet();
})

const cpuInfo = (ComplexInfo:CPUComplexInfo) => {
    if (ComplexInfo) {
        let threadCount = 0
        ComplexInfo.Cores.forEach(j => {
            threadCount += j.LogicalCores.length
        })
        return {
            l3Cache: Math.floor(ComplexInfo.CacheL3Size / 1048576),// 1048576 = 1MB
            coreCount: ComplexInfo.Cores.length,
            threadCount,
        };
    } else {
        return {
            l3Cache: 0,
            coreCount: 0,
            threadCount: 0
        };
    }
}

// 小小核cpu信息
const littleCoreCpuInfo = ((ComplexInfo:CPUComplexInfo) => {
    if (ComplexInfo) {
        let threadCount = 0
        ComplexInfo.Cores.forEach(j => {
            threadCount += j.LogicalCores.length
        })
        return {
            l3Cache: Math.floor(ComplexInfo.CacheL3Size / 1048576),// 1048576 = 1MB
            coreCount: ComplexInfo.Cores.length,
            threadCount,
        };
    } else {
        return {
            l3Cache: 0,
            coreCount: 0,
            threadCount: 0
        };
    }
})

const LargeAndSmallCore = (ComplexInfo:CPUComplexInfo)=>{
    let largeCores: Array<computedCore> = [];// 大核
    let smallCores: Array<computedCore> = [];// 小核
    let littleCores: Array<computedCore> = [];// 小小核
    ComplexInfo.Cores.forEach((core, innerIndex) => {
        if (core.EfficiencyClass === peCoreArr.value[0]) {
            largeCores.push({...core, outerIndex: 0, innerIndex,groupId: ComplexInfo.Group})
        } else if (peCoreArr.value.length >=2 && core.EfficiencyClass === peCoreArr.value[1]) {
            smallCores.push({...core, outerIndex: 0, innerIndex,groupId: ComplexInfo.Group})
        } else if (peCoreArr.value.length >=3 && core.EfficiencyClass === peCoreArr.value[2]) {
            littleCores.push({...core, outerIndex: 0, innerIndex,groupId: ComplexInfo.Group})
        }
    })
    return {
        largeCores,smallCores,littleCores
    }
}

const pCore = ((ComplexInfo:CPUComplexInfo) => {
    const largeCores = LargeAndSmallCore(ComplexInfo).largeCores;
    const count = largeCores.length;
    let threadCount = 0;
    largeCores.forEach((item:any) => {
        threadCount = threadCount + item.LogicalCores.length;
    })
    return {
        count,
        threadCount
    }
})

const eCore = ((ComplexInfo:CPUComplexInfo) => {
    const smallCores = LargeAndSmallCore(ComplexInfo).smallCores;
    const count = smallCores.length;
    let threadCount = 0;
    smallCores.forEach((item:any) => {
        threadCount = threadCount + item.LogicalCores.length;
    })
    return {
        count,
        threadCount
    }
})

const haveECore = (ComplexInfo:CPUComplexInfo) => {
    return LargeAndSmallCore(ComplexInfo).smallCores.length > 0;
}

const handleAddOrRemoveCpuSet = (arr: Array<any>,groupId:number) => {
    if ($store.curGroupCpuSet.length >= 64) {
        ElMessage.warning(t('psc.max64'))
        return
    }
    let bool = false;
    $store.curGroupCpuSet.forEach((item) => {
        if (arr.includes(item)) {
            bool = true;
        }
    })
    if (bool) {
        $store.removeCpuSet(arr);
    } else {
        // 检查是否选择了不同的分组
        if ($store.curGroupCpuSet.length > 0) {
            let findItem = $store.cpuComplexInfo.find((item) => {
                return item.Cores.find((core) => {
                    return core.LogicalCores.find((logicalCore) => {
                        return $store.curGroupCpuSet.includes(logicalCore.CPUSetId)
                    })
                })
            })
            if (findItem && findItem.Group !== groupId) {
                ElMessage.warning(t('psc.warning1'))
            }
        }
        $store.addCpuSet(arr);
    }
}
</script>

<template>
  <div class="intel-wrap">
      <template v-for="ComplexInfo in $store.cpuComplexInfo" >
          <div class="container-intel">
              <div class="cpu-info" v-if="!haveECore(ComplexInfo)">
                  <span>L3 Cache</span>
                  <span>{{ cpuInfo(ComplexInfo).l3Cache }} MB</span>
                  <span>{{$t('psc.coreCount')}}:</span>
                  <span>{{ cpuInfo(ComplexInfo).coreCount }}</span>
                  <span>{{$t('psc.threadCount')}}:</span>
                  <span>{{ cpuInfo(ComplexInfo).threadCount }}</span>
              </div>
              <div class="cpu-info" v-if="haveECore(ComplexInfo)">
                  <span>L3 Cache</span>
                  <span>{{ cpuInfo(ComplexInfo).l3Cache }} MB</span>
                  <span>{{$t('psc.coreCount')}}:</span>
                  <span>{{ cpuInfo(ComplexInfo).coreCount }} ({{pCore(ComplexInfo).count}}P + {{eCore(ComplexInfo).count}}E)</span>
                  <span>{{$t('psc.threadCount')}}:</span>
                  <span>{{ cpuInfo(ComplexInfo).threadCount }} ({{pCore(ComplexInfo).threadCount}}P + {{eCore(ComplexInfo).threadCount}}E)</span>
                  <span>{{$t('psc.Group2')}}:{{ComplexInfo.Group}}</span>
              </div>

              <div v-if="LargeAndSmallCore(ComplexInfo).largeCores.length > 0">
                  <div class="PEcore">
                      <span>{{ haveECore(ComplexInfo) ? 'P Core' : 'Core' }}</span>
                  </div>
                  <div class="pCoreList">
                      <div class="box" v-for="(item,index) in LargeAndSmallCore(ComplexInfo).largeCores" :key="'P'+index">
                          <div v-if="item.LogicalCores.length == 2">
                              <div
                                  class="core"
                                  :class="{'active': $store.isActiveCpuSet([item.LogicalCores[0].CPUSetId, item.LogicalCores[1].CPUSetId])}"
                                  @click="handleAddOrRemoveCpuSet([item.LogicalCores[0].CPUSetId, item.LogicalCores[1].CPUSetId],item.groupId)"
                              >
                                  <span>Core {{ item.Core }}</span>
                              </div>
                              <div
                                  class="t0"
                                  :class="{'active': $store.isActiveCpuSet([item.LogicalCores[0].CPUSetId])}"
                                  @click="handleAddOrRemoveCpuSet([item.LogicalCores[0].CPUSetId],item.groupId)"
                              >
                                  <span>T0</span>
                              </div>
                              <div
                                  class="t1"
                                  :class="{'active': $store.isActiveCpuSet([item.LogicalCores[1].CPUSetId])}"
                                  @click="handleAddOrRemoveCpuSet([item.LogicalCores[1].CPUSetId],item.groupId)"
                              >
                                  <span>T1</span>
                              </div>
                          </div>
                          <div
                              v-else
                              class="noThread"
                              :class="{'active': $store.isActiveCpuSet([item.LogicalCores[0].CPUSetId])}"
                              @click="handleAddOrRemoveCpuSet([item.LogicalCores[0].CPUSetId],item.groupId)"
                          >
                              <span>Core {{ item.Core }}</span>
                          </div>
                      </div>
                  </div>
              </div>

              <!-- 有小核 -->
              <div v-if="haveECore(ComplexInfo)">
                  <div class="PEcore">
                      <span>E Core</span>
                  </div>

                  <div class="pCoreList">
                      <div class="box" v-for="(item,index) in LargeAndSmallCore(ComplexInfo).smallCores" :key="'E'+index">
                          <div v-if="item.LogicalCores.length == 2">
                              <div
                                  class="core"
                                  :class="{'active': $store.isActiveCpuSet([item.LogicalCores[0].CPUSetId, item.LogicalCores[1].CPUSetId])}"
                                  @click="handleAddOrRemoveCpuSet([item.LogicalCores[0].CPUSetId, item.LogicalCores[1].CPUSetId],item.groupId)"
                              >
                                  <span>Core {{ item.Core }}</span>
                              </div>
                              <div
                                  class="t0"
                                  :class="{'active': $store.isActiveCpuSet([item.LogicalCores[0].CPUSetId])}"
                                  @click="handleAddOrRemoveCpuSet([item.LogicalCores[0].CPUSetId],item.groupId)"
                              >
                                  <span>T0</span>
                              </div>
                              <div
                                  class="t1"
                                  :class="{'active': $store.isActiveCpuSet([item.LogicalCores[1].CPUSetId])}"
                                  @click="handleAddOrRemoveCpuSet([item.LogicalCores[1].CPUSetId],item.groupId)"
                              >
                                  <span>T1</span>
                              </div>
                          </div>
                          <div
                              v-else
                              class="noThread"
                              :class="{'active': $store.isActiveCpuSet([item.LogicalCores[0].CPUSetId])}"
                              @click="handleAddOrRemoveCpuSet([item.LogicalCores[0].CPUSetId],item.groupId)"
                          >
                              <span>Core {{ item.Core }}</span>
                          </div>
                      </div>
                  </div>

              </div>
          </div>
          <div class="container-intel" v-if="LargeAndSmallCore(ComplexInfo).littleCores.length > 0" style="margin-top: 20px;">
              <div class="cpu-info">
                  <span>L3 Cache</span>
                  <span>{{ littleCoreCpuInfo(ComplexInfo).l3Cache }} MB</span>
                  <span>{{$t('psc.coreCount')}}:</span>
                  <span>{{ littleCoreCpuInfo(ComplexInfo).coreCount }}</span>
                  <span>{{$t('psc.threadCount')}}:</span>
                  <span>{{ littleCoreCpuInfo(ComplexInfo).threadCount }}</span>
              </div>

              <!-- 有小小核 -->
              <div v-if="LargeAndSmallCore(ComplexInfo).littleCores.length > 0">
                  <div class="PEcore">
                      <span>LP E Core</span>
                  </div>
                  <div class="pCoreList">
                      <div class="box" v-for="(item,index) in LargeAndSmallCore(ComplexInfo).littleCores" :key="'LE'+index">
                          <div v-if="item.LogicalCores.length == 2">
                              <div
                                  class="core"
                                  :class="{'active': $store.isActiveCpuSet([item.LogicalCores[0].CPUSetId, item.LogicalCores[1].CPUSetId])}"
                                  @click="handleAddOrRemoveCpuSet([item.LogicalCores[0].CPUSetId, item.LogicalCores[1].CPUSetId],item.groupId)"
                              >
                                  <span>Core {{ item.Core }}</span>
                              </div>
                              <div
                                  class="t0"
                                  :class="{'active': $store.isActiveCpuSet([item.LogicalCores[0].CPUSetId])}"
                                  @click="handleAddOrRemoveCpuSet([item.LogicalCores[0].CPUSetId],item.groupId)"
                              >
                                  <span>T0</span>
                              </div>
                              <div
                                  class="t1"
                                  :class="{'active': $store.isActiveCpuSet([item.LogicalCores[1].CPUSetId])}"
                                  @click="handleAddOrRemoveCpuSet([item.LogicalCores[1].CPUSetId],item.groupId)"
                              >
                                  <span>T1</span>
                              </div>
                          </div>
                          <div
                              v-else
                              class="noThread"
                              :class="{'active': $store.isActiveCpuSet([item.LogicalCores[0].CPUSetId])}"
                              @click="handleAddOrRemoveCpuSet([item.LogicalCores[0].CPUSetId],item.groupId)"
                          >
                              <span>Core {{ item.Core }}</span>
                          </div>
                      </div>
                  </div>
              </div>
          </div>
      </template>

  </div>
</template>

<style scoped lang="scss">
.intel-wrap {
  width: 100%;
  padding-right: 5px;
}
.container-intel {
  width: 100%;
  padding: 20px;
  background: #22232E;
  border-radius: 4px;
  &+.container-intel {
    margin-top: 10px;
  }

  .cpu-info {
    margin-bottom: 20px;

    span {
      margin-right: 10px;
    }
  }

  .PEcore {
    margin-bottom: 15px;
  }

  .pCoreList {
    display: flex;
    flex-flow: row wrap;

    .box {
      width: 82px;
      height: 62px;
      margin-bottom: 10px;

      &:nth-child(n+1) {
        margin-right: 20px;
      }
    }

    .noThread {
      width: 82px;
      height: 62px;
      background: #2B2C37;
      border-radius: 4px;
      border: 2px solid transparent;
      text-align: center;
      line-height: 62px;

      &:hover {
        background: #343647;
      }

      &.active {
        background: #3E4050;
        border: 2px solid #3579D5;
      }
    }

    .core {
      width: 82px;
      height: 20px;
      background: #2B2C37;
      margin-bottom: 2px;
      text-align: center;
      line-height: 20px;
      cursor: pointer;
      border: 2px solid transparent;

      &.active {
        border: 2px solid #3579D5;
        border-radius: 4px;
        background: #3E4050;
      }

      &:hover {
        background: #3E4050;
      }
    }

    .t0, .t1 {
      cursor: pointer;
      width: 40px;
      height: 40px;
      background: #2B2C37;
      display: inline-block;
      text-align: center;
      line-height: 40px;
      border: 2px solid transparent;

      &.active {
        border: 2px solid #3579D5;
        border-radius: 4px;
        background: #3E4050;
      }

      &:hover {
        background: #3E4050;
      }
    }

    .t1 {
      margin-left: 2px;
    }
  }
}
</style>
