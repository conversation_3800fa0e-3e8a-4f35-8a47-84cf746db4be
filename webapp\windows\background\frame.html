<!DOCTYPE html>
<html>
  <head>

    <meta charset="UTF-8" />

    <title>游戏加加弹出窗口</title>
	<style>
		*{
			box-sizing: border-box;
			font-family :Microsoft YaHei;
			-webkit-user-select: none;
			-moz-user-select: none;
			-o-user-select: none;
			user-select: none;
			/* overflow: hidden; */
		}

		#card {
			left: 20px;
			top: 20px;
			right: 20px;
			bottom: 20px;
			background-color: #ebf2f9;
			background: rgba(8,20,27,1);
			position: absolute;
			-webkit-transition: -webkit-transform .6s ease-in-out;
			transition: transform .6s ease-in-out;
			-webkit-transform-style: preserve-3d;
			transform-style: preserve-3d;border-radius: 4px;
			/*border-top-left-radius: 3px;*/
			/*border-top-right-radius: 3px;*/
		}

		.loader{
			background:rgba(255,255,255,1);
			top: 0;
			right: 0;
			bottom: 0;
			left:0;
			display: flex;
			flex-direction: column;
			font-size: 12px;
			color: rgba(77, 77, 77, 1);
			background: rgba(8,20,27,1);
			box-shadow: 0px 1px 6px rgba(0, 0, 0, .6);
			position: absolute;
            width: 100%;
            height: 100%;border-radius: 4px;
            /*border-top-left-radius: 3px;*/
			/*border-top-right-radius: 3px;*/
            /* border:1px solid  #34343B; */
		}
		.loader >div>img{
			width: 120px;
			height: 120px;
		}

		.header {
			padding:0 10px 0 15px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 30px;
			width: 100%;
			background: #34343B;
			border-top-left-radius: 3px;
			border-top-right-radius: 3px;
			font-size: 10px;
			-webkit-app-region:drag;
            border-bottom:1px solid  #34343B;
		}

		.header .left {
			display: flex;
    		align-items: center;
			color :rgba(255, 255, 255, 0.9)
		}

		.header .left>img{
			-webkit-app-region:no-drag;
			/*width:20px;*/
			margin-right: 10px;
		}

		.header .right-close {
			margin-right: -10px;
			cursor: pointer;
			-webkit-app-region:no-drag;
		}

		.content {
			flex : 1;
			display: flex;
			align-items: center;
			justify-content: center;
			flex-direction: column;
			background-color :rgba(33,34,42,1);
			border-bottom-left-radius: 4px;
			border-bottom-right-radius: 4px;
		}
		html,body{
			background:rgba(0,0,0,0);
			-webkit-app-region: drag;
		}
		#btnClose{width: 30px;height: 30px;margin-top: 3px;background: url("../../static/icon/Public/btn_close_0.png")no-repeat;background-size: 100% 100%;}
		#btnClose:hover{background: url("../../static/icon/Public/btn_close_1.png")no-repeat;background-size: 100% 100%;}
		#btnClose:active{background: url("../../static/icon/Public/btn_close_2.png")no-repeat;background-size: 100% 100%;}
		#loadingMore{position:relative;top:507px;width:100%;margin-left:0;z-index:101;height:3px}.content{-webkit-box-align:center;-webkit-box-pack:center;align-items:center;display:-webkit-box;display:flex;justify-content:center}.inner-1gJC7_{display:inline-block;height:32px;position:relative;width:32px}.wanderingCubesItem-WPXqao{-webkit-animation:spinner-wandering-cubes-1eyF3t 1.8s infinite ease-in-out;background-color:#108ff0;height:10px;left:0;position:absolute;top:0;width:10px}@-webkit-keyframes spinner-wandering-cubes-1eyF3t{25%{-webkit-transform:translateX(22px) rotate(-90deg) scale(.5)}50%{-webkit-transform:translateX(22px) translateY(22px) rotate(-180deg)}75%{-webkit-transform:translateX(0) translateY(22px) rotate(-270deg) scale(.5)}to{-webkit-transform:rotate(-1turn)}}.wanderingCubesItem-WPXqao:last-child{-webkit-animation-delay:-.9s}
	</style>
  </head>
  <body>
	<div id="card">
	  <div class="loader">
		  <div class="header">
			 <div class="left">
				 <img style="width: 20px;" src="../../static/img/Public/logo.ico"/>
				 <h3 id="frameTitle">游戏加加弹出窗口</h3>
			 </div>
			 <div class="right-close">
				 <div id="btnClose" onClick="handleClose()" ></div>
			 </div>
		  </div>
		  <div class="content">
			  <span class="inner-1gJC7_"><span class="wanderingCubesItem-WPXqao"></span><span class="wanderingCubesItem-WPXqao"></span></span>
			  <h3>loading...</h3>
		  </div>
	  </div>
	</div>
  </body>
  <script type="text/javascript" src="./frame.js"></script>
  <script>
    //  let lan = gamepp.getLanguage.sync();
	// 	if (lan == 'cn') {
	// 		document.querySelector('h3').innerText = '加载中...';
	// 	} else {
	// 		document.querySelector('h3').innerText = 'loading...';
	// 	}
  </script>
</html>
