<script setup lang="ts">
import {useReboundDetailStore} from "@/modules/Game_ReboundDetail/stores/reboundDetail";
import {onMounted, ref} from "vue";
import {ElMessage} from "element-plus";

const $store = useReboundDetailStore()
const radioVal = ref(0)
const dir = ref('')

onMounted(()=>{
    try {
        dir.value = gamepp.aiagent.getPublicLLMPath.sync()
    }catch (e) {
        dir.value = ''
    }
})

function GPP_ShowOpenDialog(title: any){
    gamepp.dialog.showOpenDialog.async(
        (value: { [x: string]: string[]; }[]) => ShowOpenDialogCallback(value[0]),
        { title: title, properties: ["openDirectory"] }
    );
}

function ShowOpenDialogCallback(Path: { [x: string]: string[]; }) {
    if (!Path['canceled']) {
        let regex = /^[a-zA-Z]:(([a-zA-Z]*)||([a-zA-Z]*\\))*/;
        let array = regex.exec(Path['filePaths'][0]);
        if (array === null) {ElMessage.error('错误');return;}
        console.log(Path['filePaths'][0])
        dir.value = Path['filePaths'][0]
        gamepp.aiagent.setPublicLLMPath.promise(Path['filePaths'][0])
    }
}

const GPP_OpenPicturePath = () => {
    const value = dir.value;
    const szSendData = {value: value};
    gamepp.shell.openPath(szSendData['value'])
}
</script>

<template>
    <div class="PPAgentDownloadDialog">
        <div class="dialog-container">
            <div class="dialog-header">
                <div class="close-dialog" @click="$store.ai_agent.downloadDialogShow = false">
                    <span class="iconfont icon-Close"></span>
                </div>
            </div>
            <div class="dialog-content">
                <p v-if="!$store.ai_agent.downloadDialogShowOnlyPath">{{$t('video.downloadSource')}}</p>

                <el-radio-group v-model="radioVal" v-if="!$store.ai_agent.downloadDialogShowOnlyPath">
                    <el-radio :value="0">HF-Mirror({{$t('messages.default')}})</el-radio>
                    <el-radio :value="1">ModelScope</el-radio>
                    <el-radio :value="2">HuggingFace</el-radio>
                </el-radio-group>

                <p>{{$t('video.fileSavePath')}}: </p>
                <div class="save-dir">
                    <div class="dir-text" :class="{'extra-width':$store.ai_agent.downloadDialogShowOnlyPath}">{{dir}}</div>
                    <el-button v-if="!$store.ai_agent.downloadDialogShowOnlyPath" type="primary" color="#336AB5" @click="GPP_ShowOpenDialog($t('video.fileSavePath'))">{{$t('video.edit')}}</el-button>
                    <el-button type="primary" color="#336AB5" @click="GPP_OpenPicturePath">{{$t('video.open')}}</el-button>
                </div>

                <p>{{$t('GameRebound.fileSize')}}</p>

                <div class="bottom">
                    <el-button style="min-width: 87px" v-if="!$store.ai_agent.downloadDialogShowOnlyPath" size="large" type="primary" color="#464960" @click="$store.ai_agent.downloadDialogShow = false">{{$t('Setting.cancel')}}</el-button>
                    <el-button style="min-width: 87px" v-if="!$store.ai_agent.downloadDialogShowOnlyPath" size="large" type="primary" color="#336AB5" @click="$store.downloadAmdRyzenAIModel(radioVal)">{{$t('Setting.confirm')}}</el-button>
                    <el-button style="min-width: 87px" v-if="$store.ai_agent.downloadDialogShowOnlyPath" size="large" type="primary" color="#336AB5" @click="$store.ai_agent.downloadDialogShow = false">{{$t('Setting.confirm')}}</el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped lang="scss">
.PPAgentDownloadDialog {
    position: absolute;
    top: 30px;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.4);
    z-index: 999999;
    display: flex;
    justify-content: center;
    align-items: center;

    .dialog-container {
        width: 450px;
        height: 300px;
        background: #2B2C37;
        box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.3);

        .dialog-header {
            height: 30px;
            display: flex;
            justify-content: flex-end;
        }

        .close-dialog {
            height: 30px;
            padding: 8px;
            cursor: pointer;
        }

        .icon-Close {
            color: #999999;
            font-size: 12px;
        }

        .dialog-content {
            display: flex;
            flex-flow: column nowrap;
            gap: 9px;
            padding: 0 20px 20px 20px;
            height: 270px;

            p {
                font-size: 12px;
                color: #999999;
            }

            .save-dir {
                display: flex;
                flex-flow: row nowrap;
                align-items: center;

                .dir-text {
                    padding: 7px;
                    width: 285px;
                    height: 30px;
                    border-radius: 4px;
                    overflow: hidden;
                    background: #22232E;
                    color: #ffffff;
                    font-size: 12px;
                    margin-right: 10px;
                    text-overflow: ellipsis;

                    &.extra-width {
                        width: 345px;
                    }
                }
            }

            .bottom {
                margin-top: auto;
                display: flex;
                align-items: center;
                flex-flow: row nowrap;
                justify-content: center;
                gap: 20px;
            }
        }
    }
}
</style>
