const BG = new Background();

let g_spark_time = 0

// /** @type (gamepp) */
// const gamepp = window.gamepp


/**
 * 游戏加加录像模块
 */
class Video {
    highLight_Url = ''
    ai_time = ''
    IsAiCapture = false;
    IsManualCapture = false;
    isInFullScreen = 0;
    isCapturingOBS = false;
    setInterval1 = null;
    obs_recording_time = 0;
    has_open_StartReplay = false;//是否开启回溯
    is_PREPARE = false;//是否设置OBS参数

    _hotkey_NormalVideoToggle = 0;
    _hotkey_ReplaySave = 0;
    // _hotkey_TurnOnorOffReplay = 0;
    _hotkey_ShotcutTriggered = 0;
    _hotkey_AIViesSHOW = 0;
    _hotkey_GameAnalysisMaster = 0;
    setInterval2 = null;//setInterval_IntelSparkle_STATE

    /**
     * 正常录制
     */
    async Hotkey_OnNormalVideoToggled() {
        const isExists = await gamepp.package.isexists.promise('obs')

        if (!isExists) {
            console.log("obs不存在")
            BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 36, '录像功能未下载', 3);
            return
        }else{
            const is_open_record = await gamepp.setting.getInteger.promise(41)
            if (is_open_record === 0) {
                console.log('没开视频录制开关')
                BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 36, '录像功能未启用', 3);
                return
            }
        }

        //录制模式(AMD, NVIDIA, GamePP, OBS)
        let captureId = await gamepp.setting.getInteger.promise(COMMANDID.CM_VR_VIDEO_RELIVE_SWITCH);

        let is_open_record = await gamepp.setting.getInteger.promise(COMMANDID.CM_VC_VIDEO_SWITCH);//是否录像功能

        let is_limit = true
        if (gameclient.GAME_ProcessName) {
            is_limit = await gamepp.database.queryProcessLimitAccesss.promise(gameclient.GAME_ProcessName, ProcessAcess.GPP_VIDEO_ACCESS);//是否限制录像功能
        }

        if (is_open_record && !is_limit) {
            if (captureId === 3) {
                //OBS模式
                if (this.isCapturingOBS === false) {
                    this.SendObsMessageP_StartRecord();
                } else {
                    this.SendObsMessageP_StopRecord();
                    BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 5, '', 3);
                }
            } else {
                // //原始录像模式
                // if (!gamepp.media.video.isCapturing()) {
                //     gamepp.media.video.startCapture();
                //     BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 3, '', 3);
                // } else {
                //     gamepp.media.video.stopCapture();
                //     BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 5, '', 3);
                // }
            }
        }


    }

    /**
     *回溯录像
     */
    async Hotkey_OnSaveReplayTriggered (res) {
        let captureId = await gamepp.setting.getInteger.promise(COMMANDID.CM_VR_VIDEO_RELIVE_SWITCH);

        let is_open_record = await gamepp.setting.getInteger.promise(COMMANDID.CM_VC_VIDEO_SWITCH);//是否录像功能

        let is_open_replay = await gamepp.setting.getInteger.promise(COMMANDID.CM_VC_STREAMING_MODE_SWITCH);//是否开启回溯功能

        let is_limit = false
        if (gameclient.GAME_ProcessName) {
            is_limit = await gamepp.database.queryProcessLimitAccesss.promise(gameclient.GAME_ProcessName, ProcessAcess.GPP_VIDEO_ACCESS);//是否限制录像功能
        }

        if (is_open_record && is_open_replay && !is_limit) {
            console.log('回溯录像');
            if (captureId === 3) {
                //OBS模式
                this.SendObsMessageP_SaveReplay(res);
            } else {
                //原始录像模式
                // gamepp.media.replay.saveReplay();
            }
        }
    }

    /**
     *
     */
    Hotkey_OnTurnOnorOffReplay()
    {
        // if (gamepp.media.replay.isCapturing())
        //     gamepp.media.replay.turnOff();
        // else
        //     gamepp.media.replay.turnOn();
    }

    /**
     * 游戏分析大师
     */
    async Hotkey_OnTriggerGameAnalysis() {
        console.log('Hotkey_OnTriggerGameAnalysis');
        // 录像

        //
    }

    /**
     *热键截图
     */
    async Hotkey_OnTriggerTakeScreenshot(isAuto = false) {

        let is_open_screenshot = await gamepp.setting.getInteger.promise(COMMANDID.CM_PC_PHOTO_SWITCH);//是否截图功能

        let is_limit = false
        if (gameclient.GAME_ProcessName)
        {
            is_limit = await gamepp.database.queryProcessLimitAccesss.promise(gameclient.GAME_ProcessName, ProcessAcess.GPP_PHOTO_ACCESS);//是否限制功能
        }

        if (is_open_screenshot && !is_limit)
        {
            //获取截图格式
            const ScreenshotFormatVal = gamepp.setting.getInteger.sync(COMMANDID.CM_PC_FILE_FORMAT_INDEX);


            //是否保存滤镜效果
            const excludeFilter = gamepp.setting.getInteger.sync(COMMANDID.CM_PS_SAVE_RESHADE_VIEW) === 1;

            //是否保存产品数据
            // const excludeGameinUI = gamepp.setting.getInteger(COMMANDID.CM_PC_INCLUDE_GAMEUI) !== 1;
            const excludeGameinUI = !excludeFilter;

            var ScreenshotFormatText = 'bmp';
            if (ScreenshotFormatVal === 1)
            {
                ScreenshotFormatText = 'bmp';
            } else if (ScreenshotFormatVal === 2)
            {
                ScreenshotFormatText = 'png'
            } else if (ScreenshotFormatVal === 3)
            {
                ScreenshotFormatText = 'jpg'
            }

            let fileExists = false;
            try {fileExists = await gamepp.checkFileExists.promise(gamepp.setting.getString.sync(COMMANDID.CM_SET_SCREENPATH));} catch {fileExists = false}
            console.log(fileExists)
            if (fileExists === true)
            {
              await gamepp.media.takeGameScreenshot.promise(ScreenshotFormatText, excludeGameinUI, 'manually', 'game-screen', excludeFilter);
                this.IsManualCapture = !isAuto;
            }
            else
            {
                //文件不存在
                BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 34, '截图文件不存在', 3);
            }
        }

    }


    /*
    AI画质处理
     */
    async AIViesProcess ()
    {
        if (await gamepp.user.isVIP.promise()) {
            console.log('截取AI场景');
            this.IsAiCapture = true;
            await gamepp.media.takeGameScreenshot.promise('', '', '', '', false);
        }
    }

    AppEvent_OnInGameThresholdTriggered(action) {
        if (!gameclient.GAME_ProcessName) return
        switch (action) {
            case 'replay':
                this.Hotkey_OnSaveReplayTriggered();
                break;
            case 'screenshot':
                this.Hotkey_OnTriggerTakeScreenshot(false);
                break;
        }
    }

    /*
    热键响应事件
     */
    AppEvent_OnHotkeyTrigggered (info) {
        if (!gameclient.GAME_ProcessName) return
        if (info.State !== "Keydown") return
        console.log(info);
        switch (info.id) {
            case this._hotkey_NormalVideoToggle:
                this.Hotkey_OnNormalVideoToggled();
                break;
            case this._hotkey_ReplaySave:
                this.Hotkey_OnSaveReplayTriggered();
                break;
            case this._hotkey_ShotcutTriggered:
                this.Hotkey_OnTriggerTakeScreenshot(false);
                break;
            case this._hotkey_AIViesSHOW:
                this.AIViesProcess();
                break;
            case this._hotkey_GameAnalysisMaster:
                this.Hotkey_OnTriggerGameAnalysis();
                break;
            default:
                return;
        }
    }

    /**
     *注册热键
     */
    async registerMediaHotkey()
    {
        const shotcutHotkey = await gamepp.setting.getString.promise(COMMANDID.CM_PC_NORMAL_MODE_HOTKEY_INFO);
        const normalVideotHotkey = await gamepp.setting.getString.promise(COMMANDID.CM_VC_NORMAL_HOTKEY_INFO);
        const saveReplayHotkey = await gamepp.setting.getString.promise(COMMANDID.CM_VC_STREAMING_HOTKEY_INFO);
        const _hotkey_AIViesSHOWKey = await gamepp.setting.getString.promise(COMMANDID.CM_AI_VIEW_SHOW_HOTKEY_TEXT);
        const gameAnalysisMasterKey = window.localStorage.getItem("gameAnalysisMasterKey");

        {
            this._hotkey_ShotcutTriggered = gamepp.game.ingame.registerHotkey.sync(shotcutHotkey);
            console.log("截图热键:", shotcutHotkey, "Id: ", this._hotkey_ShotcutTriggered);

            this._hotkey_NormalVideoToggle = gamepp.game.ingame.registerHotkey.sync(normalVideotHotkey);
            console.log("正常录制热键:", normalVideotHotkey, "Id: ", this._hotkey_NormalVideoToggle);

            this._hotkey_ReplaySave = gamepp.game.ingame.registerHotkey.sync(saveReplayHotkey);
            console.log("回溯录制热键:", saveReplayHotkey, "Id: ", this._hotkey_ReplaySave);

            this._hotkey_AIViesSHOW = gamepp.game.ingame.registerHotkey.sync(_hotkey_AIViesSHOWKey);
            console.log("应用AI滤镜热键:", _hotkey_AIViesSHOWKey, "Id: ", this._hotkey_AIViesSHOW);

            if (gameAnalysisMasterKey) {
                this._hotkey_GameAnalysisMaster = gamepp.game.ingame.registerHotkey.sync(gameAnalysisMasterKey);
                console.log("游戏分析大师热键:", gameAnalysisMasterKey, "Id: ", this._hotkey_GameAnalysisMaster);
            }
        }
        gamepp.game.ingame.onHotkeyTriggered.addEventListener((value) => this.AppEvent_OnHotkeyTrigggered(value));
    }

    async AppEvent_OnScrrenshotTaken (info) {
        console.log("Shotcut taken:", info);
        //AI截图
        if (this.IsAiCapture) {
            let StyleType = 0;
            let RESHADE_TYPE_Str = gamepp.setting.getString.sync(COMMANDID.CM_RESHADE_TYPE);
            console.log("Shotcut taken:", info);
            console.warn('RESHADE_TYPE_Str',RESHADE_TYPE_Str);
            if (RESHADE_TYPE_Str === 'gpp_ai_dim.ini') {StyleType = 1} else if (RESHADE_TYPE_Str === 'gpp_ai_balance.ini') {StyleType = 2} else if (RESHADE_TYPE_Str === 'gpp_ai_bright.ini') {StyleType = 3} else if (RESHADE_TYPE_Str === 'gpp_ai_colorful.ini') {StyleType = 4}

            if (!gamepp.visual.isInitAIStyle.sync()) {
                gamepp.visual.initAIStyle.sync();
            }

            let GenerateCode = await gamepp.visual.generateAIStyle.promise(info.filePath, RESHADE_TYPE_Str, StyleType);
            if (GenerateCode) {
                var RESHADE_TYPE = gamepp.setting.getString.sync(COMMANDID.CM_RESHADE_TYPE);
                await gamepp.game.ingame.setShaderPreset.promise(RESHADE_TYPE);
                BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 8, RESHADE_TYPE, 3);
            }

            this.IsAiCapture = false;
        } else if (this.IsManualCapture) {
            //游戏内截图提示
            if (info.succeed === 1) {
                BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 2, '', 3);
                //截图成功后保存剪贴板
                let image = gamepp.nativeImage.createFromPath.sync(info.filePath);
                gamepp.clipboard.writeImage.sync(image);

            } else {
                BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 34, '截图保存失败', 3);
            }
            this.IsManualCapture = false;
        }
    }


    AppEvent_OnCapturerStateChanged(info)
    {
        console.log("Capturer:", info);
    }

    AppEvent_OnMediaFileSaved(info)
    {
        console.log('Media file saved : ', info);
        BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 7, '', 3);
    }

    /*
    注册录像监听事件
     */
    async registerMediaStateListener() {
        gamepp.media.onScreenshotTaken.addEventListener((value) => this.AppEvent_OnScrrenshotTaken(value));
        // gamepp.media.onCapturerStateChanged.addEventListener(this.AppEvent_OnCapturerStateChanged, this);
        // gamepp.media.onMediaFileSaved.addEventListener(this.AppEvent_OnMediaFileSaved, this);

        // gamepp.media.video.onVideoStart.addEventListener(() => console.log("Normal video started."));
        // gamepp.media.video.onVideoEnded.addEventListener(this.AppEvent_OnVideoEnded,this);

        // gamepp.media.replay.onReplayStart.addEventListener(() => console.log("Replay video started."));
        // gamepp.media.replay.onReplayEnded.addEventListener(() => console.log("Replay video ended."));

        await gamepp.obs.onOBSMessage.addEventListener((value) => this.AppEvent_OBSMessageProcess(value));//OBS消息回调

        await gamepp.sparkserver.onSparkMessage.addEventListener((value) => this.AppEvent_intelSparkleMessageProcess(value));//尖峰时刻消息回调
    }

    /*
    正常录制结束
     */
    AppEvent_OnVideoEnded() {
        console.log("Normal video ended.")

        BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 5, '', 3);
    }

    AppEvent_OnVideoHotkeySettingChanged(type, commandId, value) {
        // CM_VC_NORMAL_HOTKEY_INFO
        if (commandId === COMMANDID.CM_VC_NORMAL_HOTKEY_INFO)
        {

        }

        // CM_VC_STREAMING_HOTKEY_INFO
        if (commandId === COMMANDID.CM_VC_STREAMING_HOTKEY_INFO)
        {

        }

        // CM_PC_NORMAL_MODE_HOTKEY_INFO
        if (commandId == COMMANDID.CM_PC_NORMAL_MODE_HOTKEY_INFO)
        {

        }
    }


    /*
    初始化设置录像截图参数
     */
    async setupMediaStartupParameters(isClose) {
        //视频录制总开关
        let videoOnorOff = await gamepp.setting.getInteger.promise(COMMANDID.CM_VC_VIDEO_SWITCH);
        let videoMode = await gamepp.setting.getInteger.promise(COMMANDID.CM_VR_VIDEO_RELIVE_SWITCH);
        if (videoMode === 3) {
            if (videoOnorOff) {
                let setTime = 0
                if (isClose) {
                    await gamepp.obs.stopOBSClient.promise();
                    setTime = 1000
                }
                setTimeout(async () => {
                    let isExit = await gamepp.obs.isOBSRunning.promise();
                    console.log(isExit)
                    if (!isExit) {await gamepp.obs.runOBSClient.promise();}
                }, setTime)
            }
        } else {
            // if (videoOnorOff) gamepp.media.startVideoCapturer();
            // else gamepp.media.stopVideoCapturer();
        }

        //录制模式(AMD, NVIDIA, GamePP)
        const captureId = await gamepp.setting.getInteger.promise(COMMANDID.CM_VR_VIDEO_RELIVE_SWITCH)
        {
            // const mode = captureId === 0 ? "AMD" : captureId === 1 ? "GamePP" : "NVIDIA";
            // gamepp.media.setCaptureMode(mode);
        }

        //捕捉模式(游戏窗口,桌面窗口)
        const captureTarget = await gamepp.setting.getInteger.promise(COMMANDID.CM_VIDEO_CAPTURE_MODEL_WINDOW)
        {
            if (captureTarget === 1)
            {
                // gamepp.media.setGameAsVideoCaptureTarget();
            }
            else
            {
                const screenIndex = await gamepp.setting.getInteger.promise(COMMANDID.CM_VIDEO_CAPTURE_SRCEEN_TYPE)
                // gamepp.media.setScreenAsVideoCaptureTarget(screenIndex);
            }
        }

        //截图保存路径
        const shotcutFilePath = await gamepp.setting.getString.promise(COMMANDID.CM_SET_SCREENPATH)
        {
            // gamepp.media.setShotcutFolderPath(shotcutFilePath);
        }

        //录制保存路径
        const videoFilePath = await gamepp.setting.getString.promise(COMMANDID.CM_SET_VIDEOPATH)
        {
            // gamepp.media.setVideoFolderPath(videoFilePath);
        }

        //录制方案
        const resolution = await gamepp.setting.getInteger.promise(COMMANDID.CM_VR_VIDEO_RESOLUTION)
        const fps = await gamepp.setting.getInteger.promise(COMMANDID.CM_VR_VIDEO_FPS)
        const bitrate = await gamepp.setting.getInteger.promise(COMMANDID.CM_VR_VIDEO_BITRATE)
        {
            // gamepp.media.setVideoQualityParams(resolution, fps, bitrate);
        }

        //回溯时间
        const durationSeconds = await gamepp.setting.getInteger.promise(COMMANDID.CM_VR_VIDEO_STREAMING_TIME)
        {
            // gamepp.media.setReplayDurationSeconds(durationSeconds);
        }
        //回溯录制开关
        const replayOnorOff = await gamepp.setting.getInteger.promise(COMMANDID.CM_VC_STREAMING_MODE_SWITCH);
        const IntelSparkSwitch = await gamepp.setting.getInteger.promise(COMMANDID.CM_VIDEO_IntelSpark_OPEN);
        {
            if (replayOnorOff && IntelSparkSwitch === 1) {
                // gamepp.media.replay.turnOn();
                let isRunning = await gamepp.sparkserver.isSparkServerRunning.promise()
                if (!isRunning) {
                   const mode = localStorage.getItem('ai_mode')
                     if(mode === 'RyzenServer') {
                         await gamepp.sparkserver.runRyzenAIClient.promise();
                     } else {
                         await gamepp.sparkserver.runClient.promise();
                     }
                }
            } else {
                // gamepp.media.replay.turnOff();
            }
        }

        // 设置是否捕获鼠标
        const CaptureMouse = await gamepp.setting.getInteger.promise(COMMANDID.CM_VIDEO_CAPTURE_MOUSE);
        {
            // if (CaptureMouse) gamepp.media.enableCaptureMouseOrNot(true);
            // else gamepp.media.enableCaptureMouseOrNot(false);
        }

        // 设置是否捕获麦克风
        const CaptureMicrophone = await gamepp.setting.getInteger.promise(COMMANDID.CM_VIDEO_CAPTURE_MICROPHONE);
        {
            // if (CaptureMicrophone) gamepp.media.enableCaptureMicrophoneOrNot(true);
            // else gamepp.media.enableCaptureMicrophoneOrNot(false);
        }

        // 设置是否加入视频水印
        const CaptureWatermark = await gamepp.setting.getInteger.promise(COMMANDID.CM_VIDEO_SHOW_WATERMARK_SWITCH);
        {
            // if (CaptureWatermark) gamepp.media.enableCaptureWatermarkOrNot(true);
            // else gamepp.media.enableCaptureWatermarkOrNot(false);
        }

        // 设置是否绘制鼠标点击效果
        const MouseWhenClick = await gamepp.setting.getInteger.promise(COMMANDID.CM_VR_VIDEO_INVERT_MOUSE_CLICK);
        {
            // if (MouseWhenClick) gamepp.media.enableInvertMouseEffectWhenClickOrNot(true);
            // else gamepp.media.enableInvertMouseEffectWhenClickOrNot(false);
        }
    }


    /**
     * *************************OBS处理
     */

    /**
     *监听OBS返回消息
     */
    async AppEvent_OBSMessageProcess(value) {
        let resultInfos = JSON.parse(value);
        console.log(resultInfos);
        let type = resultInfos["type"];
        let code = resultInfos["code"];
        if (type === "STATUS") {
            // 没有code返回
            let status = resultInfos['status']
            let r_type = resultInfos['r_type']
            if (r_type === 1) {
                // 录像
                if (status === 1) {
                    // 正在录像
                    console.log('正在录像')
                }else{
                    // 没有录像
                    console.log('没有录像')
                }
            }
            else {
                if (status === 1) {
                    // 回放功能已开启
                    console.log('回放功能已开启')
                }else{
                    // 回放功能未开启
                    console.log('回放功能未开启')
                }
            }
        } else if (type === 'PREPARE') {
            if (code === 0) {console.log('OBS_PREPARE_成功');this.is_PREPARE = true;} else {console.log('OBS_PREPARE_失败');}
        } else if (type === 'MUTE') {
            if (code === 0) {console.log('OBS_MUTE_成功');} else {console.log('OBS_MUTE_失败');}
        } else if (type === 'OVERLAY') {
            if (code === 0) {console.log('OBS_OVERLAY_成功');} else {console.log('OBS_OVERLAY_失败');}
        } else if (type === 'CURSOR') {
            if (code === 0) {console.log('OBS_CURSOR_成功');this.is_PREPARE = true;} else {console.log('OBS_CURSOR_失败');}
        } else if (type === 'ACTION')
        {
            let cmd = resultInfos["cmd"];
            if (cmd === 'StartRecord')
            {
                if (code === 0) {
                    console.log('OBS_开始录像(StartRecord)_成功');
                    this.isCapturingOBS = true;
                    BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 3, '', 3);
                    this.OBSRecordingTime();
                } else {
                    console.log('OBS_开始录像(StartRecord)_失败');
                    this.handleObsErrorMessage(resultInfos)
                    let code = resultInfos["code"];
                    if (!([-16,-21,-19,-23].includes(code)))
                    {
                        clearInterval(this.setInterval1)
                        this.isCapturingOBS = false;
                        // 发消息让游戏内显示的点变成黄色
                        this.obs_recording_time = 0;
                        let obj = {};
                        obj['action'] = 'obs_record_time';
                        obj['value'] = -1;
                        if (gamepp.webapp.windows.isVisible.sync(WindowName.IN_GAME_MONITOR)){
                            gamepp.webapp.sendInternalAppEvent.promise(WindowName.IN_GAME_MONITOR, obj);
                        }
                    }
                }
            }
            else if (cmd === 'StopRecord')
            {
                this.handleObsActionError(resultInfos)
            }
            else if (cmd === 'StartReplay')
            {
                // 有error字符串返回
                this.handleObsActionError(resultInfos)
            }
            else if (cmd === 'SaveReplay')
            {
                this.handleObsActionError(resultInfos)
            }
            else if (cmd === 'StopReplay')
            {
                this.handleObsActionError(resultInfos)
            }
        }
        else if (type === 'SaveRecordFile')
        {
            // SaveRecord 是保存的回调，首先会返回ACTION
            if (code === 0) {
                console.log('OBS_停止录像_成功');
                this.isCapturingOBS = false;
                BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 7, '', 3);
                this.OBSRestoreTime();
            } else {
                console.log('OBS_停止录像_失败');
                this.handleObsErrorMessage(resultInfos)
            }
        }
        else if (type === 'SaveReplayFile')
        {
            // SaveRecord 是保存的回调，首先会返回ACTION
            if (code === 0) {
                let video_time = gamepp.game.ingame.ffprobeExecute.sync(`ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "${this.highLight_Url}.mp4"`);
                video_time = parseFloat(video_time);
                let start_time = Math.round(this.ai_time - (video_time * 1000));
                let singleLog = [[]]
                let singleMouseLog = []
                let keysFlag  = false
                let mouseFlag = false
                console.log(keysEventsLog)
                keysEventsLog.forEach(item => {
                    if (item.timestamp > start_time && item.timestamp < this.ai_time) {
                        if (!keysFlag ) { // 如果还没有找到第一个符合条件的元素
                            // 为第一个符合条件的元素新增属性
                            item.start_time = start_time;
                            item.rounded_video_time = video_time.toFixed(1);
                            keysFlag  = true; // 标记已经处理了第一个符合条件的元素
                        }
                        singleLog[0].push(item); // 将符合条件的元素添加到单个日志数组中
                    }
                })
                singleLog.push([Kill_Time])
                mouseEventsLog.forEach(item => {
                    if (item.timestamp > start_time && item.timestamp < this.ai_time) {
                        if (!mouseFlag) { // 如果还没有找到第一个符合条件的鼠标事件元素
                            // 为第一个符合条件的鼠标事件元素新增属性
                            item.start_time = start_time;
                            item.rounded_video_time = video_time.toFixed(1);
                            mouseFlag = true; // 标记已经处理了第一个符合条件的鼠标事件元素
                        }
                        console.log('执行了')
                        singleMouseLog.push(item) // 将符合条件的鼠标事件元素添加到单个日志数组中
                    }
                })

                console.log(singleLog)
                console.log(singleMouseLog,'执行之后')
                console.log(this.highLight_Url + '.json')

                if(HighLight_Game_List[gameclient.GAME_ProcessName]) {
                    const ai_HighLight = JSON.parse(window.localStorage.getItem('ai_HighLight'))
                    let result = false
                    try {
                        result = ai_HighLight.switchGamepad.some(item=>item.value)
                    } catch (error) {
                        result = false
                    }
                    if (ai_HighLight && ai_HighLight['mouseswitch'] && result) { // 开关开了才保存键鼠操作
                        console.log('保存键盘鼠标操作')
                        gamepp.saveFileToDisk.sync(this.highLight_Url + 'keys' + '.json', JSON.stringify(singleLog))
                        gamepp.saveFileToDisk.sync(this.highLight_Url + 'mouse' + '.json', JSON.stringify(singleMouseLog))
                    }
                }
                console.log('OBS_回溯保存_成功');
                BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 7, '', 3);
                if (await gamepp.webapp.windows.isValid.promise('ai_lab')) {
                    gamepp.webapp.sendInternalAppEvent.promise('ai_lab','SaveRecord');
                }
                if (await gamepp.webapp.windows.isValid.promise('ingame_highlightsavetips')){
                    gamepp.webapp.sendInternalAppEvent.promise('ingame_highlightsavetips','SaveRecord');
                }
            } else {
                console.log('OBS_回溯保存_失败');
                this.handleObsErrorMessage(resultInfos)
                gamepp.webapp.sendInternalAppEvent.promise('ingame_highlightsavetips','SaveRecord_False');
            }
        }
    }

    // 处理OBS type='ACTION' && code != 0 的消息
    handleObsActionError (resultInfos) {
        const code = resultInfos['code'];
        if (code != 0)
        {
            if (!([-16,-21,-19,-23].includes(code)))
            {
                // 发消息让游戏内显示的点变成黄色
                this.obs_recording_time = 0;
                let obj = {};
                obj['action'] = 'obs_record_time';
                obj['value'] = -1;
                if (gamepp.webapp.windows.isVisible.sync(WindowName.IN_GAME_MONITOR)){
                    gamepp.webapp.sendInternalAppEvent.promise(WindowName.IN_GAME_MONITOR, obj);
                }
            }
            this.handleObsErrorMessage(resultInfos)
        }
    }

    GPP_OBS_ERROR = {
        '-1': 'OBS初始化失败',
        '-2': 'OBS录像需要的模块没找到或加载失败',
        '-3': '重置视频参数失败',
        '-4': '重置音频参数失败',
        '-5': '初始化录像输出失败',
        '-6': '初始化回放输出失败',
        '-7': '初始化编码器失败',
        '-8': '初始化渐变失败',
        '-9': '创建画布失败',
        '-10': '创建显示器捕获失败',
        '-11': '在画布中添加显示器捕获内容失败',
        '-12': '获取游戏窗口失败',
        '-13': '创建游戏画面捕获失败',
        '-14': '在画布中添加游戏画面捕获内容失败',
        '-15': '更新视频设置失败',
        '-16': '静音设置失败，无可用的源',
        '-17': '捕获游戏内显示设置不支持',
        '-18': '创建目录失败',
        '-20': '开启录像失败',
        '-22': '保存回放失败',
        '-30': '发送的参数无效',
    }

    // 给游戏内提示窗口发obs的消息
    handleObsErrorMessage(resultInfos) {
        let error = ''
        let code = resultInfos["code"];
        if (!([-16,-21,-19,-23].includes(code)))
        {
            if (resultInfos.hasOwnProperty('error'))
            {
                error = resultInfos["error"];
            }
            else
            {
                error = this.GPP_OBS_ERROR[`${code}`]
            }
            let str = `code:${code} ${error}`
            BG.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 35, `${str}`, 10);
        }
    }

    /**
     * 记录OBS录像时间
     */
    async OBSRecordingTime() {
        this.setInterval1 = setInterval(async () => {
            this.obs_recording_time++
            let Object = {};
            Object['action'] = 'obs_record_time';
            Object['value'] = this.obs_recording_time;
            if (gamepp.webapp.windows.isVisible.sync(WindowName.IN_GAME_MONITOR)) {
                await gamepp.webapp.sendInternalAppEvent.promise(WindowName.IN_GAME_MONITOR, Object);
            }
        }, 1000);
    }


    /**
     * OBS停止录像
     */
    async OBSRestoreTime() {
        clearInterval(this.setInterval1);
        this.obs_recording_time = 0;
        let Object = {};
        Object['action'] = 'obs_record_time';
        Object['value'] = 0;
        if (gamepp.webapp.windows.isVisible.sync(WindowName.IN_GAME_MONITOR)){
            await gamepp.webapp.sendInternalAppEvent.promise(WindowName.IN_GAME_MONITOR, Object);
        }
    }



    /**
     * 更新OBS参数
     */
    async SendObsMessageU_VIDEO() {
        let  CurrentConnectedClient= await gamepp.game.getCurrentConnectedClient.promise()
        let c_type = await gamepp.setting.getInteger.promise(COMMANDID.CM_VIDEO_CAPTURE_MODEL_WINDOW) === 1 ? 2 : 1;
        let U_VIDEOStr = {};
        U_VIDEOStr['type'] = 'U_VIDEO';
        U_VIDEOStr['width'] = gamepp.game.getWidth.sync();
        U_VIDEOStr['height'] = gamepp.game.getHeight.sync();
        U_VIDEOStr['FPS'] = gamepp.setting.getInteger.sync(COMMANDID.CM_VR_VIDEO_FPS);
        U_VIDEOStr['adapter'] = 0;
        U_VIDEOStr['bitrate'] = gamepp.setting.getInteger.sync(COMMANDID.CM_VR_VIDEO_BITRATE);
        U_VIDEOStr['pid'] = CurrentConnectedClient['newPid'];
        U_VIDEOStr['c_type'] = c_type;
        U_VIDEOStr['c_adapter'] = 0;
        U_VIDEOStr['isFullScreen'] = this.isInFullScreen;

        let U_VIDEOStrObjStr = JSON.stringify(U_VIDEOStr);
        console.log(U_VIDEOStrObjStr);
        await gamepp.obs.sendMessage.promise(U_VIDEOStrObjStr);
    }




    /**
     * 查询OBS状态
     */
    async SendObsMessageP_STATUS() {
        let Obj = {};
        Obj['type'] = "STATUS";
        Obj['r_type'] = 1;
        let JSONObj = JSON.stringify(Obj);
        console.log(JSONObj);
        await gamepp.obs.sendMessage.promise(JSONObj);
    }



    /**
     * 查询GPU_LIST
     */
    async SendObsMessageP_GPULIST() {
        let Obj = {};
        Obj['type'] = "GPULIST";
        let JSONObj = JSON.stringify(Obj);
        console.log(JSONObj);
        await gamepp.obs.sendMessage.promise(JSONObj);
    }


    /**
     * 开始录像
     */
    async SendObsMessageP_StartRecord() {
        //保存文件名称
        let FilePath = (gamepp.game.getFilePath.sync()).split('\\');
        let ProcessName = (FilePath[FilePath.length - 1]).replace('.exe', '');
        let date = new Date();
        let year = date.getFullYear(), month = date.getMonth() + 1,day = date.getDate();
        let hour = date.getHours(),minute = date.getMinutes(),Seconds = date.getSeconds();
        let FileName = ProcessName + '_' + year + '_' + month + '_' + day + '_' + hour + '_' + minute + '_' + Seconds + '_OBS';
        let P_ACTIONStr = {};
        P_ACTIONStr['type'] = 'ACTION';
        P_ACTIONStr['cmd'] = 'StartRecord';
        P_ACTIONStr['Directory'] = gamepp.setting.getString.sync(COMMANDID.CM_SET_VIDEOPATH);
        P_ACTIONStr['FileName'] = FileName;
        let JSON_P_ACTIONStr_Obj = JSON.stringify(P_ACTIONStr);
        console.log(JSON_P_ACTIONStr_Obj);
        gamepp.obs.sendMessage.sync(JSON_P_ACTIONStr_Obj);
    }

    /**
     *停止录像
     */
    async SendObsMessageP_StopRecord() {
        let P_ACTIONStr = {};
        P_ACTIONStr['type'] = 'ACTION';
        P_ACTIONStr['cmd'] = 'StopRecord';
        let JSON_P_ACTIONStr_Obj = JSON.stringify(P_ACTIONStr);
        console.log(JSON_P_ACTIONStr_Obj);
        await gamepp.obs.sendMessage.promise(JSON_P_ACTIONStr_Obj);
    }


    /**
     * OBS静音操作
     */
    async SendObsMessageP_MUTE() {
        let is_open_capture_microphone = gamepp.setting.getInteger.sync(COMMANDID.CM_VIDEO_CAPTURE_MICROPHONE) !== 0;
        let Obj = {};
        Obj['type'] = "MUTE";
        Obj['mute'] = !is_open_capture_microphone;
        Obj['a_type'] = 2;
        let JSONObj = JSON.stringify(Obj);
        console.log(JSONObj);
        await gamepp.obs.sendMessage.promise(JSONObj);
    }

    /**
     * OBS录制游戏内显示(滤镜);
     */

    async SendObsMessageP_OVERLAY () {
        let is_open_capture_reshade = gamepp.setting.getInteger.sync(COMMANDID.CM_VIDEO_CAPTURE_WITH_RESHADE_OPEN) === 0;
        let Obj = {};
        Obj['type'] = "OVERLAY";
        Obj['capture'] = is_open_capture_reshade;
        console.log(JSON.stringify(Obj));
        await gamepp.obs.sendMessage.promise(JSON.stringify(Obj));
    }


    /**
     * OBS录制鼠标点击;
     */

    async SendObsMessageP_CURSOR () {
        let is_open_cursor = gamepp.setting.getInteger.sync(COMMANDID.CM_VIDEO_CAPTURE_MOUSE) !== 0;
        let Obj = {};
        Obj['type'] = "CURSOR";
        Obj['capture'] = is_open_cursor;
        console.log(JSON.stringify(Obj));
        await gamepp.obs.sendMessage.promise(JSON.stringify(Obj));
    }


    /*******************OBS回溯模式*****************************/


    /**
     * 开启回溯
     */
    async SendObsMessageP_StartReplay() {
        let Obj = {};
        Obj['type'] = "ACTION";
        Obj['cmd'] = 'StartReplay';
        Obj['MaxSec'] = await gamepp.setting.getInteger.promise(56);
        Obj['MaxSizeMB'] = 2048;
        let JSONObj = JSON.stringify(Obj);
        console.log(JSONObj);
        await gamepp.obs.sendMessage.promise(JSONObj);
    }

    /**
     * 关闭回溯
     */
    async SendObsMessageP_StopReplay() {
        let Obj = {};
        Obj['type'] = "ACTION";
        Obj['cmd'] = 'StopReplay';
        let JSONObj = JSON.stringify(Obj);
        console.log(JSONObj);
        await gamepp.obs.sendMessage.promise(JSONObj);
    }

    /**
     * 保存回溯视频
     */
    async SendObsMessageP_SaveReplay(videoInfo) {
        let FilePath = (gamepp.game.getFilePath.sync()).split('\\');
        let ProcessName = ((FilePath[FilePath.length - 1]).replace('.exe', '')).replace(/ /g, '');
        console.log('processName', ProcessName)
        let date = new Date();
        const year = date.getFullYear()
        const month = (date.getMonth() + 1) <= 9 ? '0' + (date.getMonth() + 1) : (date.getMonth() + 1)
        const day = date.getDate() <= 9 ? '0' + date.getDate() : date.getDate()
        const hour = date.getHours() <= 9 ? '0' + date.getHours() : date.getHours()
        const minute = date.getMinutes() <= 9 ? '0' + date.getMinutes() : date.getMinutes()
        const Seconds = date.getSeconds() <= 9 ? '0' + date.getSeconds() : date.getSeconds()
        let FileName = ProcessName + '_' + year + '_' + month + '_' + day + '_' + hour + '_' + minute + '_' + Seconds + '_OBS_Replay';
        let Obj = {};
        Obj['type'] = "ACTION";
        Obj['cmd'] = 'SaveReplay';
        Obj['Directory'] = gamepp.setting.getString.sync(COMMANDID.CM_SET_VIDEOPATH);
        Obj['FileName'] = FileName;
        if (!!videoInfo) {
            if (videoInfo.type === 'INTEL_AI_SaveReplay') {
                this.SaveInformation_database(videoInfo, FileName)
            }
        }
        this.ai_time = Date.now()
        this.highLight_Url = Obj.Directory + '\\' + Obj.FileName
        // 保存回放的按键录制
        let JSONObj = JSON.stringify(Obj);
        console.log(JSONObj);
        await gamepp.obs.sendMessage.promise(JSONObj);
    }

    /**
    intel 回溯视频信息存储数据库
     **/
    async SaveInformation_database (videoInfo, filename) {
        try {
            console.log({
                name: filename,
                new: 1,
                type: 'video',
                kill: videoInfo.kill,
                timestamps: videoInfo.timestamps
            })
            const AppDataDir = gamepp.getAppDataDir.sync()
            let GPP_INTEL_AI_REPLAYDir = gamepp.setting.getString.sync(526);
            if (GPP_INTEL_AI_REPLAYDir == '') GPP_INTEL_AI_REPLAYDir = AppDataDir + '\\common\\Data'
            await gamepp.database.open.async((value) => {
                const DatabaseID = value
                const table = 'AIReplay'
                const game = filename.split('_')[0]
                console.log('databaseID', DatabaseID, 'table', table)
                const status = gamepp.database.exists.sync(DatabaseID, table)
                if (status) {
                    this.DataBaseInsert(DatabaseID, table, ['name', 'game', 'new', 'type', 'kill', 'timestamps'], [[filename, game, 1, 'video', videoInfo.kill, videoInfo.timestamps]])

                } else {
                    gamepp.database.create.async((value) => {}, DatabaseID, table, "(id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, name TEXT, game TEXT, new INT, type TEXT, kill INT, timestamps TEXT)")
                    setTimeout(() => {
                        this.DataBaseInsert(DatabaseID, table, ['name', 'game', 'new', 'type', 'kill', 'timestamps'], [[filename, game, 1, 'video', videoInfo.kill, videoInfo.timestamps]])
                    }, 100)
                }
            },GPP_INTEL_AI_REPLAYDir + '\\GPP_INTEL_AI_REPLAY.dll')
        } catch (err) {
            console.log('databaseError', err)
        }
    }

    DataBaseInsert (DatabaseID, table, field, value) {
        gamepp.database.insert.async((value) => {}, DatabaseID, table, field, value)
    }
    /**
     *游戏结束UNINIT
     */
    async SendObsMessageP_UNINIT(){
        this.has_open_StartReplay = false;
        this.isCapturingOBS = false;
        this.is_PREPARE = false;
        let Obj = {};
        // Obj['type'] = "RESET";
        Obj['type'] = "CLOSE";
        let JSONObj = JSON.stringify(Obj);
        console.log(JSONObj);
        await gamepp.obs.sendMessage.promise(JSONObj);
    }

    async SendObsMessageP_PREPARE() {
        let CurrentConnectedClient = await gamepp.game.getCurrentConnectedClient.promise()
        this.isCapturingOBS = false;
        clearInterval(this.setInterval1);
        this.obs_recording_time = 0;
        this.has_open_StartReplay = false;//是否开启回溯
        let c_type = await gamepp.setting.getInteger.promise(COMMANDID.CM_VIDEO_CAPTURE_MODEL_WINDOW) === 1 ? 2 : 1;
        let PREPAREStr = {};
        PREPAREStr['type'] = 'PREPARE';


        let c_adapter = 0;
        let isInFullScreen = 0;

        let Width = 0,Height = 0;
        //c_type 1 屏幕 2 游戏
        if (c_type === 1) {
            //录制桌面选择显示器序号
            c_adapter = await gamepp.setting.getInteger.promise(COMMANDID.CM_VIDEO_CAPTURE_SRCEEN_TYPE);

            let DisplayInfo = await gamepp.hardware.getDisplayCardInfo.promise();
            Width = DisplayInfo['Element'][c_adapter]['PelsWidth'];
            Height = DisplayInfo['Element'][c_adapter]['PelsHeight'];

        } else if (c_type === 2) {
            isInFullScreen = this.isInFullScreen;
            Width = await gamepp.game.getWidth.promise();
            Height = await gamepp.game.getHeight.promise();
        }


        // 画布
        PREPAREStr['width'] = Width;
        PREPAREStr['height'] = Height;
        PREPAREStr['FPS'] = await gamepp.setting.getInteger.promise(COMMANDID.CM_VR_VIDEO_FPS);
        PREPAREStr['s_adapter'] = 0;
        PREPAREStr['bitrate'] = await gamepp.setting.getInteger.promise(COMMANDID.CM_VR_VIDEO_BITRATE);

        //场景
        PREPAREStr['pid'] = CurrentConnectedClient['newPid'];
        PREPAREStr['c_type'] = c_type;
        PREPAREStr['c_adapter'] = c_adapter;
        PREPAREStr['isFullScreen'] = isInFullScreen;
        PREPAREStr['encoder'] = 0;
        let JSON_PREPAREStr_Obj = JSON.stringify(PREPAREStr);
        console.log(JSON_PREPAREStr_Obj);
        gamepp.obs.sendMessage.sync(JSON_PREPAREStr_Obj);
    }


    /**
     * *************************IntelSparkle处理
     */

    async AppEvent_intelSparkleMessageProcess(value) {
        let resultInfos = JSON.parse(value);
        console.log(resultInfos, '超能时刻返回信息');
        if (Object.prototype.toString.call(resultInfos) === '[object Object]' && resultInfos.hasOwnProperty('type')) {
            if (resultInfos.type === 'SparkReady') {
                console.log('执行gamepp.sparkserver.startIohook')
                gamepp.sparkserver.startIohook.promise()
            }else if (resultInfos.type === 'SparkError') {
                console.log('执行gamepp.sparkserver.stopIohook')
                gamepp.sparkserver.stopIohook.promise()
            }
        }
        try {
            var game_time;
            var game_name;
            if (resultInfos['type'] === "Detect") {

                if (resultInfos.game_type === 1) {
                    console.log('VALORANT');
                    game_name = 'valorant'
                    game_time = 500;
                } else if (resultInfos.game_type === 2) {
                    console.log('NARAKA');
                    game_name = 'naraka'
                    game_time = 1000;
                } else if(resultInfos.game_type === 3){
                    console.log('THE_FINALS');
                    game_name = 'thefinals'
                    game_time = 1300;
                }else if(resultInfos.game_type === 4){
                    console.log('COD20');
                    game_name = 'COD20'
                    game_time = 1430;
                }else if(resultInfos.game_type === 5){
                    console.log('APEX');
                    game_name = 'APEX'
                    game_time = 1550;
                }else if(resultInfos.game_type === 6){
                    console.log('PUBG');
                    game_name = 'PUBG'
                    game_time = 6000;
                }else if(resultInfos.game_type === 8){
                    console.log('CalabiYau');
                    game_name = 'CalabiYau'
                    game_time = 1550;
                }else if(resultInfos.game_type === 10){
                    console.log('bwk');
                    game_name = 'bwk'
                    game_time = 6000;
                }else if(resultInfos.game_type === 12){
                    console.log('EDR');
                    game_name = 'EDR'
                    game_time = 6000;
                }else if(resultInfos.game_type === 18){
                    console.log('RMNT2');
                    game_name = 'RMNT2'
                    game_time = 1550;
                }else if(resultInfos.game_type === 20){
                    console.log('WOWS');
                    game_name = 'WOWS'
                    game_time = 1550;
                }else if(resultInfos.game_type === 14){
                    console.log('HALO');
                    game_name = 'HALO'
                    game_time = 1550;
                }else if(resultInfos.game_type === 15){
                    console.log('HS');
                    game_name = 'HS'
                    game_time = 1550;
                }else if(resultInfos.game_type === 16){
                    console.log('MR');
                    game_name = 'MR'
                    game_time = 1550;
                }else if(resultInfos.game_type === 11){
                    console.log('DF');
                    game_name = 'DF'
                    game_time = 1550;
                }else if(resultInfos.game_type === 19){
                    console.log('SIX');
                    game_name = 'SIX'
                    game_time = 1550;
                }else if(resultInfos.game_type === 13){
                    console.log('FP');
                    game_name = 'FP'
                    game_time = 1550;
                }else if(resultInfos.game_type === 17) {
                    console.log('ow');
                    game_name = 'ow'
                    game_time = 1550;
                }else if(resultInfos.game_type === 21) {
                    console.log('wowp');
                    game_name = 'wowp'
                    game_time = 1550;
                }else if(resultInfos.game_type === 22) {
                    console.log('forhonor');
                    game_name = 'forhonor'
                    game_time = 1550;
                }else if(resultInfos.game_type === 23) {
                    console.log('Cuphead');
                    game_name = 'Cuphead'
                    game_time = 1550;
                }else if(resultInfos.game_type === 24) {
                    console.log('sekiro');
                    game_name = 'sekiro'
                    game_time = 1550;
                }else if(resultInfos.game_type === 25) {
                    console.log('darksoul1');
                    game_name = 'darksoul1'
                    game_time = 1550;
                }else if(resultInfos.game_type === 26) {
                    console.log('darksoul2');
                    game_name = 'darksoul2'
                    game_time = 1550;
                }else if(resultInfos.game_type === 27) {
                    console.log('darksoul3');
                    game_name = 'darksoul3'
                    game_time = 1550;
                }else if(resultInfos.game_type === 28) {
                    console.log('blasphemous');
                    game_name = 'blasphemous'
                    game_time = 1550;
                }else if(resultInfos.game_type === 29) {
                    console.log('blasphemous2');
                    game_name = 'blasphemous2'
                    game_time = 1550;
                }else if(resultInfos.game_type === 30) {
                    console.log('warthunder');
                    game_name = 'warthunder'
                    game_time = 1550;
                }else if(resultInfos.game_type === 31) {
                    console.log('pinocchio');
                    game_name = 'pinocchio'
                    game_time = 1550;
                }else if(resultInfos.game_type === 32) {
                    console.log('nioh');
                    game_name = 'nioh'
                    game_time = 1550;
                }else if(resultInfos.game_type === 33) {
                    console.log('nioh2');
                    game_name = 'nioh2'
                    game_time = 1550;
                }else if(resultInfos.game_type === 34) {
                    console.log('LOTF2');
                    game_name = 'LOTF2'
                    game_time = 1550;
                }else if(resultInfos.game_type === 35) {
                    console.log('WorldOfTanks');
                    game_name = 'WorldOfTanks'
                    game_time = 1550;
                }else if(resultInfos.game_type === 36) {
                    console.log('Steelrising');
                    game_name = 'Steelrising'
                    game_time = 1550;
                }else if(resultInfos.game_type === 37) {
                    console.log('nightreign');
                    game_name = 'nightreign'
                    game_time = 1550;
                }else if(resultInfos.game_type === 38) {
                    console.log('PlagueProject');
                    game_name = 'PlagueProject'
                    game_time = 1550;
                }else if(resultInfos.game_type === 39) {
                    console.log('CodeVein');
                    game_name = 'CodeVein'
                    game_time = 1550;
                }else if(resultInfos.game_type === 40) {
                    console.log('BBQ');
                    game_name = 'BBQ'
                    game_time = 1550;
                }else if(resultInfos.game_type === 41) {
                    console.log('hyxd');
                    game_name = 'hyxd'
                    game_time = 1550;
                }
                var current_spark_time = Date.now();
                const isGameValid = resultInfos['game_type'] !== 2 // 不是永劫无间
                const isClassValid = Array.isArray(resultInfos.cls) && resultInfos.cls.length > 0; // cls里有没有值
                // 永劫无间击杀同时判断cls包含0和1
                const isBothClassesValidForGameType2 = resultInfos.cls.includes(0) && resultInfos.cls.includes(1) && resultInfos.game_type === 2

                // 三角洲 cls 出现2或3为击杀人机不算成击杀
                const isDF = resultInfos['game_type'] === 11
                let isDFKill = true
                if (isDF && (resultInfos.cls.includes(2) || resultInfos.cls.includes(3))) {
                    isDFKill = false
                }

                if (isBothClassesValidForGameType2 || (isGameValid && isClassValid && isDFKill)) {
                    if ((current_spark_time - g_spark_time) >= game_time)
                    {
                        g_spark_time = current_spark_time;
                        console.log('击杀了')
                        // 超能时刻开关状态
                        let switchGamepad_HighLight = JSON.parse(window.localStorage.getItem('switchGamepad_aiSuperpower'));
                        if (!switchGamepad_HighLight) {
                            switchGamepad_HighLight = []
                        }
                        let findItem = switchGamepad_HighLight.find(item=> item.enname === game_name && item.value)
                        if (findItem && findItem.value){
                            InterSparkleMessage_Receive(resultInfos.game_type)
                        }
                    }
                }
            }
        } catch (error) {

        }
        if (resultInfos['type'] === 'START') {
            if (resultInfos['code'] === 0) {
                this.setInterval2 = setInterval(() => {
                    this.SendIntelSparkleMessage_STATE();
                }, 2000);
            }
        } else if (resultInfos['type'] === 'SparkEvent') {
                if (gamepp.setting.getBool.sync(406)) {
                    InterSparkleMessage_Receive()
                } else {
                    return false
                }
                // setTimeout(() => {
                //     this.Hotkey_OnSaveReplayTriggered();
                // },3000)
        } else if (resultInfos['type'] === 'SparkInfo') {
            if (resultInfos['code'] === -6) {
                console.log('GPU与显示器不匹配')
                const setRegisterValue = async (getValueFn, setValueFn) => {
                    const value = await getValueFn();
                    await setValueFn(value === 0 || value === 2 ? 1 : 2);
                };
                await setRegisterValue(gamepp.sparkserver.getRegisterValueDML.promise, gamepp.sparkserver.setRegisterValueDML.promise);
                await setRegisterValue(gamepp.sparkserver.getRegisterValueRyzenAI.promise, gamepp.sparkserver.setRegisterValueRyzenAI.promise);
                await this.SendIntelSparkleMessage_STOP(gameclient.sparkMessageListener)
                await this.SendIntelSparkleMessage_START(true, gameclient.sparkMessageListener)
            } else if (resultInfos['code'] === -7) {
                console.log('YOLO初始化失败')
                await this.SendIntelSparkleMessage_STOP(gameclient.sparkMessageListener)
                await this.SendIntelSparkleMessage_START(true, gameclient.sparkMessageListener)
            }
        } else if (resultInfos['type'] === 'SetSparkServerInfo') {
            if ("server_config" in resultInfos) {
                console.log('收到mythcool的server_config');
                if (!resultInfos.server_config) { // 防止server_config 为 null
                    resultInfos.server_config = {}
                }
                // 存local
                Object.keys(resultInfos.server_config).forEach(config_key =>{
                    window.localStorage.setItem(config_key, JSON.stringify(resultInfos.server_config[config_key]))
                })
                if (resultInfos.server_config.hasOwnProperty('ai_master')) {
                    gamepp.webapp.sendInternalAppEvent.promise('background','open_ingame_broadsword')
                }
            }
            setTimeout(()=>{
                handleSparkServerSwitch()
            },200)
        }
    }

    SendIntelSparkleMessage_START = async function(ProcessName, event) {
        console.log(ProcessName)
        if (ProcessName) {
            console.log('开启尖峰时刻服务');
            const mode = localStorage.getItem('lastSelectedType');
            if (mode !== 'btn') {
                await gamepp.sparkserver.runRyzenAIClient.promise();
            } else {
                await gamepp.sparkserver.runClient.promise();
            }
            let Obj = {
                'type': "StartSpark"
            };
            let JSONObj = JSON.stringify(Obj);
            console.log(JSONObj);
            await gamepp.sparkserver.sendMessage.promise(JSONObj);
            window.onSparkIohookMessageId = gamepp.sparkserver.onSparkIohookMessage.addEventListener(event)
        }
    };

    async SendIntelSparkleMessage_RESET() {
        let Obj = {};
        Obj['type'] = "RESET";
        let JSONObj = JSON.stringify(Obj);
        console.log(JSONObj);
        await gamepp.sparkserver.sendMessage.promise(JSONObj);
    }

    async SendIntelSparkleMessage_STATE() {
        let Obj = {};
        Obj['type'] = "STATE";
        let JSONObj = JSON.stringify(Obj);
        // console.log(JSONObj);
        await gamepp.sparkserver.sendMessage.promise(JSONObj);
    }

    async SendIntelSparkleMessage_STOP(sparkServer) {
        localStorage.setItem('endHighLight', Date.now().toString()) // 记录尖峰时刻结束时的时间轴以便绘制键盘长度
        keysEventsLog = [] // 尖峰时刻结束清空按键数组
        mouseEventsLog = []
        let Obj = {
            "type":'StopSpark'
        };
        let JSONObj = JSON.stringify(Obj);
        console.log(JSONObj);
        await gamepp.sparkserver.sendMessage.promise(JSONObj);
        if(sparkServer) {
            console.log('已经移除键盘监听')
            if (window.onSparkIohookMessageId) {
                gamepp.sparkserver.onSparkIohookMessage.removeEventListener(window.onSparkIohookMessageId);
                window.onSparkIohookMessageId = ''
            }
        }
        clearInterval(this.setInterval2);
    }

    async Run() {
        console.log("Initilizing video system....");

        // await this.setupMediaStartupParameters();

        await this.registerMediaHotkey();

        await this.registerMediaStateListener();

        // gamepp.setting.onConfigChanged.addEventListener(this.AppEvent_OnVideoHotkeySettingChanged, this);
    }
}

const video_client = new Video();
