<script setup lang="ts">
import {computed, CSSProperties, nextTick, onBeforeMount, onMounted, onUnmounted, reactive, ref, watchEffect,onBeforeUnmount} from 'vue';
import {gamepp} from 'gamepp'
import {Fps, SensorData} from "@/modules/Game_Home/types/InGameMonitor";
import {ProcessSensorUnit, SensorAddAverageData} from "@/uitls/sensor";
import {gameppBaseSetting , getInGameList, saveInGameList} from "@/modules/Game_Home/stores";
import echarts from "@/uitls/echarts";
import { useI18n } from 'vue-i18n';
import ThresholdMonitor from "@/inGame/inGame_monitor/components/ThresholdMonitor.vue";
const { t } = useI18n();
// import "@/modules/Game_Home/assets/theme.scss"
const gppSetting = gameppBaseSetting()
onBeforeMount(async() =>
{
  // let data_locatio = window.localStorage.getItem('ingame_monitor_location');
  // let XPositionConf = await gamepp.setting.getFloat.promise(130);
  // let YPositionConf = await gamepp.setting.getFloat.promise(131);
  // XPositionConf = 0
  // YPositionConf = 0
  // // 检查 data_locatio 是否为空 没有拖动过位置 就执行正上
  // if (!data_locatio && XPositionConf === 0 && YPositionConf === 0) {
  //   const Obj = { position: 'upper', x:'0', y: '0' };
  //   window.localStorage.setItem('ingame_monitor_location', JSON.stringify(Obj));
  // }
})
let res:Array<any> = []
const mouseEventRateDevices:any = {
    "31514026": "VK M1",
    "3151402D": "VK M1",
    "046DC539": "罗技 GPW1",
    "046DC547": "罗技 GPW2",
    "046DC54D": "罗技 GPW3",
    "046DC543": "罗技 GPW4",
    "153200A5": "雷蛇 Viper V2 PRO",
    "1532A5": "雷蛇 Viper V2 PRO",
    "153200A6": "雷蛇 Viper V2 PRO",
    "1532A6": "雷蛇 Viper V2 PRO",
    "153200B6": "雷蛇 DeathAdder V3 PRO",
    "1532B6": "雷蛇 DeathAdder V3 PRO",
    "153200B7": "雷蛇 DeathAdder V3 PRO",
    "1532B7": "雷蛇 DeathAdder V3 PRO",
    "374AA304": "Valkyrie VKM2 鼠标",
    "374AA305": "Valkyrie VKM2 鼠标",
    "374AA307": "Valkyrie VKM2Pro 鼠标",
    "374AA308": "Valkyrie VKM2Pro 鼠标",
}
const allDeviceName = ['Valkyrie 99 磁轴','Valkyrie M1 鼠标','PRO WIRELESS','PRO X SUPERLIGHT','PRO X SUPERLIGHT 2','PRO 2 LIGHTSPEED','PRO X WIRELESS']
const logitechs = [13,14,15,16]
const logitech_names: Record<typeof logitechs[number], string> = {
  13: 'PRO WIRELESS',
  14: 'PRO X SUPERLIGHT',
  15: 'PRO X SUPERLIGHT 2',
  16: 'PRO 2 LIGHTSPEED'
}
// const InGameList = getInGameList();
const isMouseDown = ref(false);
const containerRef = ref(null);
// const portraitRef = ref([]);
const containerShow = ref(false)
let intervalId = null;
let CenterSetting = ref(JSON.parse(localStorage.getItem('IngameAllSetting') as string))
const fpsValue = ref<Partial<Fps>>({})
let SensorValue = ref<SensorData[][]>(JSON.parse(localStorage.getItem('InGameList1') as string))
const activeValue = ref('activecross')
const echartsInstances = reactive<any>({})
const loadFromLocalStorage = async () => {
  console.log('loadFromLocalStorage')
  const storedData = localStorage.getItem('InGameList1');
  if (storedData) {
    try {
      SensorValue.value = JSON.parse(storedData) as SensorData[][];
      console.log('数据加载')
    } catch (error) {
      console.error('没有获取到传感器数据', error);
      SensorValue.value = [];
    }
  } else {
    SensorValue.value = [];
  }
  SensorValue.value.forEach((item)=>{
    console.log(item)
    item.forEach((item2,index)=>{
      if (item2.methodWay === 1) { // 需要折线图
        let divId = item2.name + index
          if (item2.hasOwnProperty('desc')) {
              divId = item2.desc + index
          }
        initEcharts(divId,item2)
      }
    })
  })

};

const getOuterDimensions = (element: Element) => {
  if (!element) return { width: 0, height: 0 };
  const style = window.getComputedStyle(element);
  const clientWidth = element.clientWidth;
  const clientHeight = element.clientHeight;

  const paddingLeft = parseFloat(style.paddingLeft);
  const paddingRight = parseFloat(style.paddingRight);
  const paddingTop = parseFloat(style.paddingTop);
  const paddingBottom = parseFloat(style.paddingBottom);

  const borderLeftWidth = parseFloat(style.borderLeftWidth);
  const borderRightWidth = parseFloat(style.borderRightWidth);
  const borderTopWidth = parseFloat(style.borderTopWidth);
  const borderBottomWidth = parseFloat(style.borderBottomWidth);

  const marginLeft = parseFloat(style.marginLeft);
  const marginRight = parseFloat(style.marginRight);
  const marginTop = parseFloat(style.marginTop);
  const marginBottom = parseFloat(style.marginBottom);

  const outerWidth = (
    clientWidth + paddingLeft + paddingRight +  borderLeftWidth + borderRightWidth + marginLeft + marginRight
  );
  const outerHeight = (
    clientHeight + paddingTop + paddingBottom + borderTopWidth + borderBottomWidth + marginTop + marginBottom
  );
  return { width: outerWidth, height: outerHeight };
};

// let appSize = {newWidth: -1,newHeight:-1}
const resizeFn = async () => {
  await nextTick();
  const container = containerRef.value;
  if (container) {
    const { width: newWidth, height: newHeight } = getOuterDimensions(container);
    // console.log(newWidth, newHeight);
    // appSize.newWidth = Math.floor(newWidth)
    // appSize.newHeight = Math.floor(newHeight)
    gamepp.webapp.windows.resize.sync('ingame_monitor',  Math.floor(newWidth), Math.floor(newHeight));
  }
}
const handleStorageChange = async (event: StorageEvent) => {
  if (event.key === 'InGameList1') {
    await nextTick()
    await loadFromLocalStorage();
    // await nextTick(resizeFn)
    // setTimeout(async () => {
    //   await  resizeFn()
    // },500)
    await resizeDefaultLocation()
    SensorValue.value.forEach(singleValue => {
      singleValue.forEach(item => {
         calculateWidth(item);
      });
    });

  }
  if (event.key === 'IngameAllSetting') {
    const storedData = localStorage.getItem('IngameAllSetting');
    CenterSetting.value = storedData ? JSON.parse(storedData) : {}
    console.log(CenterSetting.value!.bgColor)
    setTimeout(async () => {
      // await  resizeFn()
      await resizeDefaultLocation()
    },300)
  }
  if(event.key === 'ingameArrangement') {
    const newValue = event.newValue;
    if (newValue === t('InGameMonitor.horizontal')) {
      activeValue.value = 'activecross'
      flexDirection.value = 'row'
    }else if(newValue === t('InGameMonitor.vertical')) {
      activeValue.value = 'activeportrait'
      flexDirection.value = 'column'
    }
    console.log('触发了')
    setTimeout(async() => {
      // await  resizeFn()
      await resizeDefaultLocation()
    },300)
  }
  if(event.key === 'ingameSwitch') {
    if(event.newValue ===  'true'){
      setTimeout(() => {
         containerShow.value = true
      }, 900);
    }else{
      containerShow.value = false
    }
  }
  // if(event.key === 'ingame_monitor_location') {
  //   const newPosition = event.newValue;
  //   console.log()
  //  await setPosition(newPosition)
  // }
};

// 监控初始执行窗口位置
const resizeDefaultLocation = async() => {
  await nextTick()
  let XPosition, YPosition;
  const CurrentConnectedClient = await gamepp.game.getCurrentConnectedClient.promise();//当前游戏宽高数据
  // let CurrentBounds = await gamepp.webapp.windows.getBounds.promise('ingame_monitor');//当前窗口宽
  let XPositionConf = await gamepp.setting.getFloat.promise(130);
  let YPositionConf = await gamepp.setting.getFloat.promise(131);
  setTimeout(async() => {
      await resizeFn()
      let set_position = window.localStorage.getItem('ingame_monitor_location');
      // console.log('set_position',set_position)
      if (set_position !== null) {
          let Json = JSON.parse(set_position);
          setPosition(Json.position);
      } else {
        if (XPositionConf === 0 && YPositionConf === 0) {
          XPosition = 0;
          YPosition = 0;
          let CurrentBounds = await gamepp.webapp.windows.getBounds.promise('ingame_monitor');//当前窗口宽
          XPosition = Math.ceil((CurrentConnectedClient['viewportWidth'] - CurrentBounds['width']) / 2);
        } else {
          // isMouseDown.value = true;
          XPosition = CurrentConnectedClient['viewportWidth'] * XPositionConf;
          YPosition = CurrentConnectedClient['viewportHeight'] * YPositionConf;
        }
          //  gamepp.webapp.windows.show.sync('ingame_monitor',false)
          gamepp.webapp.windows.setPosition.sync("ingame_monitor", XPosition, YPosition);
      }
      console.log('窗口位置')
  }, 950);
}

const handleMouseDown= () =>{
  isMouseDown.value = true;
  console.log('鼠标按下');

}
const handleMouseUp= () =>{
  isMouseDown.value = false;
  // console.log('鼠标松开');
  // recalculateAllWidths();
}
//拖动松开重新计算宽度
// const recalculateAllWidths = () => {
//   SensorValue.value.forEach(singleValue => {
//       singleValue.forEach(item => {
//         calculateWidth(item);
//       });
//   });
// };
const currentPositionText = ref('');
const setPosition = async (site: string) => {
  // isMouseDown.value = false;
  console.log('设置位置', site);
  // 获取屏幕和窗口信息
  const PrimaryDisplay = await gamepp.game.getCurrentConnectedClient.sync();
  const PresentBounds =   await gamepp.webapp.windows.getBounds.promise('ingame_monitor');
  const DefaultWidth = PrimaryDisplay.viewportWidth - PresentBounds.width;
  const DefaultHeight = PrimaryDisplay.viewportHeight - PresentBounds.height;
  const halfWidth = Math.floor((PrimaryDisplay.viewportWidth / 2) - (PresentBounds.width / 2));
  const halfHeight = Math.floor((PrimaryDisplay.viewportHeight / 2) - (PresentBounds.height / 2));
  console.log('PrimaryDisplay',halfWidth);
  let x = 0;
  let y = 0;
  switch (site) {
    case 'upperLeft':
      currentPositionText.value = 'Upper Left';
      break;
    case 'upper':
      x = halfWidth;
      currentPositionText.value = 'Upper';
      break;
    case 'upperRight':
      x = DefaultWidth;
      currentPositionText.value = 'Upper Right';
      break;
    case 'left':
      y = halfHeight;
      currentPositionText.value = 'Left';
      break;
    case 'middle':
      x = halfWidth;
      y = halfHeight;
      currentPositionText.value = 'Middle';
      break;
    case 'right':
      x = DefaultWidth;
      y = halfHeight;
      currentPositionText.value = 'Right';
      break;
    case 'lowerLeft':
      y = DefaultHeight;
      currentPositionText.value = 'Lower Left';
      break;
    case 'lower':
      x = halfWidth;
      y = DefaultHeight;
      currentPositionText.value = 'Lower';
      break;
    case 'lowerRight':
      x = DefaultWidth;
      y = DefaultHeight;
      currentPositionText.value = 'Lower Right';
      break;
    default:
      break;
  }
  gamepp.webapp.windows.setPositionDesktopMonitor.sync("ingame_monitor", x, y, 1);

  const Obj = { position: site, x, y };
  setTimeout(() => {
    window.localStorage.setItem('ingame_monitor_location', JSON.stringify(Obj));
  }, 900);
};


let sensor_collected_list = ref<Array<any>>([])
let SensorList = ref<Array<any>>([])


const initEcharts = (el:string,item:SensorData) =>
{
  console.log(el)
  if (echartsInstances.hasOwnProperty(el)) {
    echartsInstances[el].params = item
    return
  }
  const dom = document.getElementById(el)
  if (!dom) {
    setTimeout(()=>{
      initEcharts(el,item)
    }, 200)
    return
  }
  let _arr = new Array(60).fill(null)

  if (el.includes('Frame Time')) {
    _arr = new Array(1000).fill(null)
  }else if (el.includes('mouseEventRate')) {
      _arr = new Array(60).fill(null)
  }
  echartsInstances[el] = {
    chart: echarts.init(dom),
    list:_arr,
    params: item,
  }
  console.warn(el,echartsInstances)
}
let xAxisData:Array<string> = new Array(60).fill('')
let xAxisData2:Array<string> = new Array(1000).fill('')

const drawEcharts = (instance: any, data: any,params: SensorData) => {
  let lineColor = '#ffffff';
  let lineSize = 2
  if (params.lineColor) lineColor = params.lineColor
  if (params.lineSize) lineSize = params.lineSize
  let option:any = {
    title: {},
    grid: {
      left: 0,
      right: 0,
      bottom: 0,
      top: 0
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: xAxisData,
        axisTick: { //x轴刻度线
          show: false
        }
      }
    ],
    yAxis: {
      show: false,
    },
    series: [
      {
        type: 'line',
        symbol: 'none',  //取消折点圆圈
        stack: '总量',
        data: data,
        cursor: 'default',
        lineStyle: {
          color: lineColor,
          width: lineSize,
        },
      }
    ],
    animation: false
  }
  if (params.name && params.name === 'Frame Time') {
    option.yAxis = {
      show: false,
      max: 100,
      min: 0
    };
    option.xAxis = [
      {
        type: 'category',
        boundaryGap: false,
        data:xAxisData2,
        axisTick: { //x轴刻度线
          show: false
        }
      },
    ]
    // console.log(option)
  }

  const chartH = instance.getHeight()
  let h = params.lineHeight || 30

  // const lineChartDom = document.querySelector('.line-chart')

  // let w = lineChartDom?.clientWidth || 'auto'
  // console.log(w)
  try{instance.resize({height: h,width:'auto'})}catch (e) {
  }
  instance.setOption(option)
}
const bc = new BroadcastChannel('bg_sensor_data');
let bgData:any = null

const dataProcessed = ref(false);
let mouseEventRateList:Array<number> = []
let mer = 0
onMounted(async () => {
  // await nextTick();
  // 监听游戏内窗口变化事件
  gamepp.game.ingame.onGameWindowStateChanged.addEventListener(() => {
    console.log('游戏窗口状态变化');
    resizeDefaultLocation()
  });
  const  IngameAllSettingdata = localStorage.getItem('ingameArrangement')
  if (IngameAllSettingdata) {
    if(IngameAllSettingdata === t('InGameMonitor.horizontal')) {
      activeValue.value = 'activecross'
      flexDirection.value = 'row'
    }else if(IngameAllSettingdata === t('InGameMonitor.vertical')) {
        activeValue.value = 'activeportrait'
        flexDirection.value = 'column';
    }
  }
  // flexDirection.value = JSON.parse(localStorage.getItem('IngameAllSetting'))?.showWay ? 'column' : 'row';
  gamepp.webapp.onInternalAppEvent.addEventListener(async value => {
    if (value === 'ingame_monitor_position') {
      console.log(value)
      let set_position = window.localStorage.getItem('ingame_monitor_location');
      if (set_position !== null) {
          let Json = JSON.parse(set_position);
          await setPosition(Json.position);
      }
    }
  })
  await gamepp.setting.setBool2.promise('window', 'ingame_monitor', true);
  let lastExecutionTime = 0; // 上次执行处理逻辑的时间戳
  var DataRefresh = gamepp.setting.getInteger.sync(450)
  var dataTime = JSON.stringify(DataRefresh);
  gamepp.game.ingame.onMajorFrameRateUpdated.addEventListener((values: Fps) => {
    // console.log('mouseEvent mouseEventRate',values.mouseEvent,values.mouseEventRate);

    // console.log(values.frameTimeList)
    let curPid = Number(gamepp.game.getCurrentClientPid.sync())
    if (curPid != values.processId) return
    const currentTime = Date.now();
    const timeSinceLastExecution = currentTime - lastExecutionTime;
    mer = values.mouseEventRate
      // console.log('------------',mer)

    // 如果距离上次执行超过或等于1000毫秒，则执行处理逻辑并更新时间戳
    if (timeSinceLastExecution >= Number(gppSetting.state.refreshTime)) {
      fpsValue.value = values;
      lastExecutionTime = currentTime;
    // }

      // 如果数据还没有被处理过，则执行处理逻辑
      if (!dataProcessed.value) {
        dataProcessed.value = true;
        processInitialData();
      }
    }
    Object.keys(echartsInstances).forEach(key => {
      if (key.includes('Frame Time')) {
        echartsInstances[key].list.push(...values.frameTimeList)
        echartsInstances[key].list.splice(0,values.frameTimeList.length)
        drawEcharts(echartsInstances[key].chart, echartsInstances[key].list,echartsInstances[key].params)
      }else if (key.includes('mouseEventRate')) {
        echartsInstances[key].list.push(mer)
        echartsInstances[key].list.splice(0,1)
          // console.log(echartsInstances[key].list)
        drawEcharts(echartsInstances[key].chart, echartsInstances[key].list,echartsInstances[key].params)
      }
    })
  });

  const processInitialData = async () => {
      // await ProcessSensor(true);
    bc.onmessage = async function(event) {
      bgData = event.data
      await ProcessSensor(true)
      // await resizeFn()
    };
    await loadFromLocalStorage();
  };
  window.addEventListener('storage', handleStorageChange);
  // resizeFn()
  // await setPosition(JSON.parse(localStorage.getItem('IngameAllSetting')).site)
  await resizeDefaultLocation()
  intervalId = setInterval(updateTime, 1000);

  const ingameSwitchBoolean = localStorage.getItem('ingameSwitch');
    if (ingameSwitchBoolean !== null  && ingameSwitchBoolean !== '') {
      if(ingameSwitchBoolean === 'true'){
        // gamepp.webapp.windows.hide.sync('ingame_monitor')
        setTimeout(() => {
          containerShow.value = true
        }, 900);
      }else{
        containerShow.value = false
      }
  }
})
const formattedTime = computed(() => formatTime(elapsedTime.value));
onUnmounted(() => {
  window.removeEventListener('storage', handleStorageChange);
  // clearInterval(intervalId);
})
async function ProcessSensor (init:boolean) {
  let SensorInfoStr = null
  try {SensorInfoStr = await gamepp.hardware.getSensorInfo.promise()} catch {

  }
  const SensorInfo = JSON.parse(SensorInfoStr);
  const booleanArr = ['Critical Temperature', 'Thermal Throttling', 'Power Limit Exceeded', 'IA: ', 'GT: ', 'RING:', 'Drive Failure', 'Drive Warning', 'Chassis Intrusion', 'Performance Limit']
  SensorList.value = []
  const SensorInfoKeys = Object.keys(SensorInfo)

  let SensorInfoTidyUp = SensorAddAverageData(SensorInfo, SensorInfoKeys)
  let sensor_collected:any = window.localStorage.getItem('collected_sensor_list')
  if (sensor_collected) {
    sensor_collected = JSON.parse(sensor_collected)
  }else{
    sensor_collected = []
  }
  sensor_collected_list.value = sensor_collected
  // console.log(SensorInfoTidyUp)

  // 循环标题列表

  const regex = /^(.*?)(:|\s|$)/ // 匹配第一个冒号或空格之前的内容
  for (let i = 0; i < SensorInfoKeys.length; i++) {
    const SensorInfoKey = SensorInfoKeys[i]
    let Datas = SensorInfoTidyUp[SensorInfoKey]
    const SensorObj:any = {}
    let SensorListText = ''
    SensorObj.name = SensorInfoKey
    const match = SensorInfoKey.match(regex)
    SensorObj.type = match ? match[1].trim() : ''
    SensorObj.Sensoritem = []
    // if (!Datas) continue
    if (!Datas) { Datas = [{ Null: '', name: '' }] }
    for (let j = 0; j < Datas.length; j++) {
      const Key = Object.keys(Datas[j])[0]
      const Data = Datas[j][Key]
      const SensoritemObj:any = {}
      const ProcessKey:any = ProcessSensorUnit(Key, Data.type)
      if (!SensorListText.includes(ProcessKey.UnitText)) {
        SensorListText += ProcessKey.UnitText
      }
      if (booleanArr.find(item => Key.includes(item))) {
        let booleanValue = 'No'
        if (Number(Data.value)) { booleanValue = 'Yes' }
        SensoritemObj.value = booleanValue
      } else {
        if (Data.value) { SensoritemObj.value = Number(Data.value).toFixed(ProcessKey.ToFixed) } else { SensoritemObj.value = 'Null' }
      }
      SensoritemObj.name = Key
      SensoritemObj.unit = ProcessKey.DataUnit
      SensoritemObj.choosen = false
      SensoritemObj.collect = false
      if (sensor_collected_list.value.length > 0) {
        const findIndex = sensor_collected_list.value.findIndex((item:any) => {
          return item.mainName === SensorInfoKey && item.name === Key
        })
        if (findIndex !== -1) SensoritemObj.collect = true

      }
      SensorObj.Sensoritem.push(SensoritemObj)
    }
    SensorObj.UnitText = SensorListText
    SensorList.value.push(SensorObj)

    // console.log(SensorObj,'数据')

  }
  const isMythCoolRunning = gamepp.utils.isMythCoolRunning.sync();
  let VKDevice = [];
  try {
    const str = gamepp.hardware.getMythCoolUSBSensorInfo.sync();
    if (str && isMythCoolRunning) {
      VKDevice = JSON.parse(str);
    }
  } catch (e) {
    console.log(e);
  }
  for (const item of VKDevice) {
    let name = '';
    if (item.type === 6 || item.type === 7) {
      name = 'Valkyrie M1 鼠标';
    }
    if (item.type === 8 || item.type === 9) {
      name = 'Valkyrie 99 磁轴';
    }
    if (logitechs.includes(item.type)) { // 罗技的键盘
      name = logitech_names[item.type];
    }
    if (item.hasOwnProperty('name')) {
      name = item.name
    }
    item.name = name
  }

  const ArctisName =['Arctis GameBuds Left','Arctis GameBuds Right','Arctis GameBuds Case','Arctis 7P Plus']
  const VKDeviceNames = VKDevice.map((device: { name: any; }) => device.name);
  const usbDevices = gamepp.queryUsbDevices.sync() as Array<any>
    // console.log(usbDevices)
  // console.log(VKDeviceNames,VKDevice)
  setTimeout(() => {
    SensorValue.value.forEach((group: any[]) => {
    group.forEach((item: any) => {
      const deviceExistsInVKDevice = VKDeviceNames.includes(item.name);
      const existsInAll = allDeviceName.includes(item.name);
      if (deviceExistsInVKDevice) {
        // console.log('1111', item.name);
        item.show = true;
        item.showOnlyHide = false;
        resizeFn();
      } else if (existsInAll && !deviceExistsInVKDevice) {
        item.show = false;
        item.showOnlyHide = true;
        resizeFn();
      }else {
        return item
      }
    });

    let have = false
    for (const usbDevice of usbDevices) {
        const Key:string = usbDevice.VendorID+usbDevice.ProductID
        if (mouseEventRateDevices.hasOwnProperty(Key)) {
            have = true;
        }
    }
    group.forEach((item: any) => {
        if (item.hasOwnProperty('desc') && item.desc === 'mouseEventRate') {
            if (have) {
                item.show = true;
                item.showOnlyHide = false;
                resizeFn();
            }else{
                item.show = false;
                item.showOnlyHide = true;
                resizeFn();
            }
        }

        //赛睿耳机是否显示
        if (res.length === 0) {
          if (ArctisName.includes(item.name)) {
                item.show = false;
                item.showOnlyHide = true;
                resizeFn();
                // resizeDefaultLocation()
            }
        } else {
          const resNames = res.map(item => item.name);
          // 获取所有不在 res 中的 ArctisName 设备名称
          const missingNames = ArctisName.filter(name => !resNames.includes(name));
          console.log(missingNames)
          const matchingResItem = res.find(resItem => resItem.name === item.name);
          if (missingNames.includes(item.name)) {
            item.show = false;
            item.showOnlyHide = true;
            resizeFn();
          }else{
            if ([0, 2].includes(matchingResItem.status)) {
              item.show = false;
              item.showOnlyHide = true;
              resizeFn();
            }else {
              item.show = true;
              item.showOnlyHide = false;
              resizeFn();
            }
          }
        }
    })
  });
  }, 1000);
// saveInGameList(SensorValue.value);

  // console.log(SensorList.value)

  for (const key in echartsInstances) {
    const instance = echartsInstances[key].chart
    const item = echartsInstances[key].params
    if (key.includes('Frame Time')) {

    }else{
      let data = getDef(item)
      if (data !== 0) {
        const curNum = parseInt(String(data).replace('°C','').replace('%','').replace('MHz','').replace('W',''))
        echartsInstances[key].list.shift()
        echartsInstances[key].list.push(curNum)
        drawEcharts(instance, echartsInstances[key].list,item)
      }else{
        try {
          data = SensorList.value[item.outIndex].Sensoritem[item.innerIndex].value
          echartsInstances[key].list.shift()
          echartsInstances[key].list.push(data)
          drawEcharts(instance, echartsInstances[key].list,item)
        }catch (e) {
          console.log(e)
        }
      }
    }
  }
}

const isDataAvailable = (data: any) => {
  if (data === undefined || data === null || data === '') {
    return false;
  }
  // if (typeof data === 'string' && data.toLowerCase().includes('v')) {
  //   return false;
  // }
  if (typeof data === 'string') {
    // 如果字符串包含字母
    if (/[a-zA-Z]/.test(data)) {
      return false;
    }
  }
  return true;
};
const getDef = (value: SensorData) => {
  // console.log(value,'getDef')
    nextTick()
    if(value.origin) {
    // if(value.lock) {
        switch (value.name) {
            case "FPS":
                return isDataAvailable(fpsValue.value.fps) ? fpsValue.value.fps : 0;
            case 'FPS 1% Low':
                return isDataAvailable(fpsValue.value.fps1low) ? fpsValue.value.fps1low : 0;
            case 'FPS 0.1% Low':
                return isDataAvailable(fpsValue.value.fps01low) ? fpsValue.value.fps01low : 0;
            case 'Frame Time':
                return isDataAvailable(fpsValue.value.frameTime) ? fpsValue.value.frameTime?.toFixed(1) + 'ms' : 0;
            case 'Current time':
                return getCurrentTimeIn24HourFormat();
            case 'Run time':
                return isDataAvailable(formattedTime.value) ? formattedTime.value : 0;
        }
      // console.log(value)
      let data
      if (bgData !== null) {data = bgData}
      else {data = JSON.parse(localStorage.getItem('bg_sensor_data')!) as object;}
      if(value.title.includes('CPU')) {
        if(value.bg === 'temp') {
          return data.cpu[value.bg] + ' °C'
        } else if(value.bg === 'usage') {
          return data.cpu[value.bg] + ' %'
        } else if (value.bg === 'clock') {
          return data.cpu[value.bg] + ' MHz'
        } else if (value.bg === 'power') {
          return data.cpu[value.bg] + ' W'
        }
      } if(value.title.includes('memory')){
        if(value.bg === 'usage') {
           return data.memory[value.bg] + ' %'
        }
      }else {
        if(value.bg === 'temp') {
          return data.gpu[value.bg] + ' °C'
        } else if(value.bg === 'total_usage') {
          return data.gpu[value.bg] + ' %'
        } else if (value.bg === 'clock') {
          return data.gpu[value.bg] + ' MHz'
        } else if (value.bg === 'power') {
          return data.gpu[value.bg] + ' W'
        }
      }
    // }

  } else {
        switch (value.name) {
            case "FPS":
                return isDataAvailable(fpsValue.value.fps) ? fpsValue.value.fps : 0;
            case 'FPS 1% Low':
                return isDataAvailable(fpsValue.value.fps1low) ? fpsValue.value.fps1low : 0;
            case 'FPS 0.1% Low':
                return isDataAvailable(fpsValue.value.fps01low) ? fpsValue.value.fps01low : 0;
            case 'Frame Time':
                return isDataAvailable(fpsValue.value.frameTime) ? fpsValue.value.frameTime?.toFixed(1) + 'ms' : 0;
            case 'Current time':
                return getCurrentTimeIn24HourFormat();
            case 'Run time':
                return isDataAvailable(formattedTime.value) ? formattedTime.value : 0;
        }
        if (value.hasOwnProperty('desc') && value.desc === 'mouseEventRate') {
            return mer+''
        }
        const isMythCoolRunning = gamepp.utils.isMythCoolRunning.sync();
        if (!isMythCoolRunning) {

        }
        let VKDevice = [];
        try {
            const str = gamepp.hardware.getMythCoolUSBSensorInfo.sync();
            if (str && isMythCoolRunning) {
                VKDevice = JSON.parse(str);
            }
        } catch (e) {
            console.log(e);
        }
        let haveType18 = false
        if (VKDevice.length > 0) {
            for (const item of VKDevice) {
                let name = '';
                let battery = '';
                let unit = '';
                console.log(item)
                if (item.type === 6 || item.type === 7) {
                    name = 'Valkyrie M1 Mouse';
                }
                if (item.type === 8 || item.type === 9) {
                    name = 'Valkyrie 99 Magnetic Axis';
                }
                if (logitechs.includes(item.type)) { // 罗技的键盘
                    name = logitech_names[item.type];
                }
                if (item.hasOwnProperty('name')) {
                    name = item.name
                }
                if (item.hasOwnProperty('charging')) {
                    // charging 0为没充电 1为充电中 2为充满了
                  if (item.charging === 0) {
                    if (item.battery === 0) {// 充电状态和电量同时为0
                      if ([6,7,8,9].includes(item.type)) {
                        battery = t('SelectSensor.chargingInProgress')
                      }else if (logitechs.includes(item.type)){ // 逻辑鼠标
                        battery = t('SelectSensor.inHibernation')
                      }
                    }else{
                      battery = item.battery
                      unit = '%'
                    }

                  }else if (item.charging === 1) {
                    battery = t('SelectSensor.chargingInProgress')
                  }else{
                    battery = '100'
                    unit = '%'
                  }
                }
                if (item.type === 18) {
                  haveType18 = true
                }
                let isAllPing = true;
                // 逻辑的单独处理
                // ping_status 1 WAP已连接 0 WAP断开连接
                if (item.hasOwnProperty('devices') && Array.isArray(item.devices)) {

                    isAllPing = item.devices.every(DEVICE=>{
                        if (!DEVICE.hasOwnProperty('ping_status')) {
                            return true;
                        }
                        return DEVICE['ping_status'] == 1;
                    })

                }
                if (!isAllPing) return ''
                const usbDevices = gamepp.queryUsbDevices.sync() as Array<any>
                const vid = item.vid.toString(16).toUpperCase()
                const pid = item.pid.toString(16).toUpperCase()
                const usbDevice = usbDevices.find(usbDevice => {
                    return usbDevice.VendorID === vid && usbDevice.ProductID === pid
                })
                if (!usbDevice) return ''
                // if (item.type === 19 && haveType18) return []
                const leftV:any = {name:"", value:0, unit:"", collect: false, choosen: false}
                const rightV:any = {name:"", value:0, unit:"", collect: false, choosen: false}
                const caseV:any = {name:"", value:0, unit:"", collect: false, choosen: false}
                res = [];
                // const _o = {leftV, rightV, caseV}
                  if (item.hasOwnProperty('devices') && Array.isArray(item.devices)) {
                    for (let i = 0; i < item.devices.length; i++) {
                      const device = item.devices[i];
                      if (device.name?.includes('Left')) {
                        leftV.name = device.name;
                        leftV.value = device.battery;
                        leftV.unit = '%';
                        leftV.status = device.status;
                        res.push(leftV);
                      } else if (device.name?.includes('Right')) {
                        rightV.name = device.name;
                        rightV.value = device.battery;
                        rightV.unit = '%';
                        rightV.status = device.status;
                        res.push(rightV);
                      } else {
                        caseV.name = device.name;
                        caseV.value = device.battery;
                        caseV.unit = '%';
                        caseV.status = device.status;
                        res.push(caseV);
                      }

                    }
                  }
                  console.log(res,'res')
                // for (let i = 0; i < res.length; i++) {
                //     if (value.name.includes(res[i].name)) {
                //         results.push(`${res[i].value} ${res[i].unit}`);
                //     }
                // }
                for (const entry of res) {
                  // console.log(entry.name,value.name, 'entry');
                  if (value.name === entry.name) {
                    console.log(entry.value, '111111111111');
                    return `${entry.value} ${entry.unit}`;
                  }
                }
                // return res
                // 如果设备名称匹配，则返回电池值
                if (value.name.includes(name)) {
                  // console.log(value.name, name, battery);
                  return battery ? battery + ' ' + unit : t('GameRebound.noData');
                }
            }

        }
    // }
    try {
      const sensorItem = SensorList.value[value.outIndex]?.Sensoritem[value.innerIndex];
      if (sensorItem) {
        const valueWithUnit = `${sensorItem.value} ${sensorItem.unit}`.replace('℃', '°C');
        return valueWithUnit;
      }
    }catch (e) {
      return t('GameRebound.noData')
    }
    return '';
  }
}

const getCurrentTimeIn24HourFormat = () => {
  const now = new Date();
  return now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
};
const flexDirection =  ref<CSSProperties['flexDirection']>('row')

const formatTime = (seconds: number) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secondsAsNumber = seconds % 60;
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secondsAsNumber.toString().padStart(2, '0')}`;
};

// 创建一个响应式的计时秒数
const elapsedTime = ref(0);

// 更新时间的函数
const updateTime = () => {
  elapsedTime.value++; // 增加秒数
};
let defaultH: number | null = null
function calcHeight(item:SensorData) {
  if (item.lineHeight) {
    return item.lineHeight
  }else{
    return 30
  }
}

//提取单位
function extractUnit(str: string) {
  const regex = /(\d+(\.\d+)?\s*)(\S+)/;
  const match = str.match(regex);
  if (match) {
      return match[3];
  }
  return '';
}

// const calculateWidth = (item: any) => {
//   const value = getDef(item) || (SensorList.value[item.outIndex]?.Sensoritem[item.innerIndex]?.value + SensorList.value[item.outIndex]?.Sensoritem[item.innerIndex]?.unit);
//   // console.log(value,'value')
//   if (!value) return 0;
//   const stringValue = String(value);
//   //提取单位
//   // const unit = stringValue.match(/[a-zA-Z]+$/)?.[0] || '';
//   const unit = extractUnit(stringValue);
//   // console.log(unit,'unit')
//   const fontSize = item.fontStyle;
//   // 判断数据是否有小数点
//   const hasDecimal = stringValue.includes('.');

//   if (['FPS', 'FPS 1% Low', 'FPS 0.1% Low'].includes(item.name)) {
//     const width = 2.78 * fontSize + 0.02;
//     return width;
//   }
//   if(['Current time', 'Run time'].includes(item.name)) {
//     const width = 4.17  * fontSize + 0.01;
//     return width;
//   }
//   if (unit === 'MHz') {
//     return 4.95 * fontSize - 0.02;
//   } else if (unit === '%' ) {
//     return 2.85 * fontSize - 0.17;
//   } else if (unit === '°C') {
//     return 3.06 * fontSize + 0.11;
//   } else if (unit === 'W') {
//     // return 2.89 * fontSize + 0.01;
//     if (hasDecimal) {
//       return 4.83 * fontSize + 0.09;
//     } else {
//       return 3.45 * fontSize - 0.04;
//     }
//   }else if (unit === 'V') {
//     return 3.45 * fontSize - 0.02;
//   } else if (unit === 'RPM') {
//     return 4.72 * fontSize + 0.06;
//   }else if(unit === 'ms'){
//     return 3.56 * fontSize - 0.03;
//   }else{
//     return
//   }
// };
// const widthMap = new Map();
const maxWidthMap = new Map(); // 存储最大宽度
let lastTotalWidth = 0; // 上一次计算的总宽度
const measureTextWidth = (text: string, fontSize: any, fontFamily: any) => {
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  context.font = `${fontSize}px ${fontFamily}`;
  return context.measureText(text).width;
};

const calculateWidth = (item: SensorData) => {
  if (isMouseDown.value) {
    console.log('停止计算宽度');
    return maxWidthMap.get(item) || 0;
  }
  // console.log('计算宽度');
  const value = getDef(item) || (SensorList.value[item.outIndex]?.Sensoritem[item.innerIndex]?.value + SensorList.value[item.outIndex]?.Sensoritem[item.innerIndex]?.unit);
  if (!value) return 0;
  const stringValue = String(value);
  const unit = extractUnit(stringValue);
  const fontSize = item.fontStyle;
  const fontFamily = item.fontSize || '微软雅黑';
  const fullText = `${stringValue.trim()}`;
  if (fullText) {
    const width = measureTextWidth(fullText, fontSize, fontFamily);
    const roundedWidth = Math.round(width);
    // 获取之前存储的最大宽度
    const maxWidth = maxWidthMap.get(item) || 0;
    // console.log(roundedWidth, maxWidth, value, item.name, 'width11');
    // console.log(maxWidthMap);

    // 仅当新宽度大于之前存储的最大宽度时更新
    if (roundedWidth > maxWidth) {
      maxWidthMap.set(item, roundedWidth);
    } else {
      // 否则，使用之前的最大宽度
      maxWidthMap.set(item, maxWidth);
    }
    resizeFn();
    updateAndCheckTotalWidth();
    return maxWidthMap.get(item);
  }
  return 0;
};

//总宽度变化，监控位置校准
const updateAndCheckTotalWidth = () => {
  if( isMouseDown.value){
    return
  }
  let currentTotalWidth = 0;
  maxWidthMap.forEach(maxWidth => {
    currentTotalWidth += maxWidth;
  });
  if (currentTotalWidth > lastTotalWidth) {
    resizeDefaultLocation();
    lastTotalWidth = currentTotalWidth;
  }
};
</script>
<template>
  <div class="container_box" ref="containerRef" v-show="containerShow"  :style="{background: CenterSetting?.bgColor}" @mousedown="handleMouseDown"
  @mouseup="handleMouseUp">
    <div class="allValue" :style="{ 'flex-direction': flexDirection }" v-for="(singleValue, index) in SensorValue" :class="activeValue" :key="index">
      <div class="SensorValue" v-for="(item, index2) in singleValue" :key="item.name" v-show="item.show">
        <div class="sensorList" :class="activeValue+'item'">
          <div class="portrait" ref="portraitRef">
            <span  v-show="item.desshow" :style="{ color: item.titlecolor, fontSize: item.TitlefontStyle + 'px', fontFamily: item.fontSize, fontWeight: item.fontBold ? 'bold' : 'normal' , textShadow: item.textShadow ? '0 1px rgb(0 0 0), 1px 0 rgb(0 0 0), -1px 0 rgb(0 0 0), 0 -1px rgb(0 0 0)' : 'none'}"> 
				{{ $t(String(item.des || item.name)) }}
			</span>
            <span class="Keepleft"  :style="{ color: item.color, fontSize: item.fontStyle + 'px', fontFamily: item.fontSize, fontWeight: item.fontBold ? 'bold' : 'normal', textShadow: item.textShadow ? '0 1px rgb(0 0 0), 1px 0 rgb(0 0 0), -1px 0 rgb(0 0 0), 0 -1px rgb(0 0 0)' : 'none', width: calculateWidth(item) + 'px' }">
              {{ getDef(item) || (SensorList[item.outIndex]?.Sensoritem[item.innerIndex]?.value + SensorList[item.outIndex]?.Sensoritem[item.innerIndex]?.unit) || '' }}
            </span>
          </div>
          <div v-show="item.methodWay === 1"
                class="line-chart"
               :style="{ height: calcHeight(item) + 'px' }"
          >
            <div :id="item.desc?item.desc+index2:item.name+index2" class="Brokenline" style="width: 100%;"></div>
          </div>
            <ThresholdMonitor :index1="index" :index2="index2" :all-data="item" :data-value="getDef(item) || (SensorList[item.outIndex]?.Sensoritem[item.innerIndex]?.value + SensorList[item.outIndex]?.Sensoritem[item.innerIndex]?.unit) || ''" />
        </div>
      </div>
    </div>
  </div>
</template>



<style lang="scss" scoped>
.sensorList span:last-child{
  margin-left: 10px;
}
.activecross .sensorList:last-child{
  margin-right: 3px;
}
.activecross .SensorValue:first-child{
   margin-left: 5px;
}
.activecross{
  .sensorList{
    padding: 6px 3px;
    box-sizing: border-box;
    span{
      display: inline-block;
    }
  }
  .line-chart {
    margin-left: 10px;
    width: 240px;
  }
}

.activeportrait{
  .Brokenline{
    min-width: 256px;
  }
  .sensorList{
    padding: 5px 10px;
    span{
      text-align: right;
    }
    .Keepleft{
      margin-left: 0;
    }
  }
  .line-chart {
    margin-top: 10px;
    width: 100%;
  }
  .portrait{
    align-items: center;
    display: flex;
    width: 100%;
    justify-content: space-between;
  }
}

.sensorList.activeportraititem {
  flex-direction: column;
  align-items: flex-start;
}
.sensorList {
  display: flex;
  align-items: center;
  justify-content: space-between;
  // padding: 5px 10px;
  white-space: nowrap;
  //gap: 66px;
  width: 100%;
}
.container_box {
  cursor: move;
  //display: flex;
  width: fit-content;
  //box-sizing: border-box;
  background: rgba(0, 0, 0, 0.3);
  .allValue {
    -webkit-app-region:drag;
    min-width: 200px;
    display: flex;
    flex-direction: row;
    .SensorValue {
      display: flex;
    }
  }
  .allValue2 {
    min-width: 302px;
    display: flex;
    .SensorValue {
      display: flex;
    }
  }

}
html.dark{color-scheme:dark;--el-color-primary:#409eff;--el-color-primary-light-3:#3375b9;--el-color-primary-light-5:#2a598a;--el-color-primary-light-7:#213d5b;--el-color-primary-light-8:#1d3043;--el-color-primary-light-9:#18222c;--el-color-primary-dark-2:#66b1ff;--el-color-success:#67c23a;--el-color-success-light-3:#4e8e2f;--el-color-success-light-5:#3e6b27;--el-color-success-light-7:#2d481f;--el-color-success-light-8:#25371c;--el-color-success-light-9:#1c2518;--el-color-success-dark-2:#85ce61;--el-color-warning:#e6a23c;--el-color-warning-light-3:#a77730;--el-color-warning-light-5:#7d5b28;--el-color-warning-light-7:#533f20;--el-color-warning-light-8:#3e301c;--el-color-warning-light-9:#292218;--el-color-warning-dark-2:#ebb563;--el-color-danger:#f56c6c;--el-color-danger-light-3:#b25252;--el-color-danger-light-5:#854040;--el-color-danger-light-7:#582e2e;--el-color-danger-light-8:#412626;--el-color-danger-light-9:#2b1d1d;--el-color-danger-dark-2:#f78989;--el-color-error:#f56c6c;--el-color-error-light-3:#b25252;--el-color-error-light-5:#854040;--el-color-error-light-7:#582e2e;--el-color-error-light-8:#412626;--el-color-error-light-9:#2b1d1d;--el-color-error-dark-2:#f78989;--el-color-info:#909399;--el-color-info-light-3:#6b6d71;--el-color-info-light-5:#525457;--el-color-info-light-7:#393a3c;--el-color-info-light-8:#2d2d2f;--el-color-info-light-9:#202121;--el-color-info-dark-2:#a6a9ad;--el-box-shadow:0px 12px 32px 4px rgba(0,0,0,0.36),0px 8px 20px rgba(0,0,0,0.72);--el-box-shadow-light:0px 0px 12px rgba(0,0,0,0.72);--el-box-shadow-lighter:0px 0px 6px rgba(0,0,0,0.72);--el-box-shadow-dark:0px 16px 48px 16px rgba(0,0,0,0.72),0px 12px 32px #000000,0px 8px 16px -8px #000000;--el-bg-color-page:#0a0a0a;--el-bg-color:#141414;--el-bg-color-overlay:#1d1e1f;--el-text-color-primary:#E5EAF3;--el-text-color-regular:#CFD3DC;--el-text-color-secondary:#A3A6AD;--el-text-color-placeholder:#8D9095;--el-text-color-disabled:#6C6E72;--el-border-color-darker:#636466;--el-border-color-dark:#58585B;--el-border-color:#4C4D4F;--el-border-color-light:#414243;--el-border-color-lighter:#363637;--el-border-color-extra-light:#2B2B2C;--el-fill-color-darker:#424243;--el-fill-color-dark:#39393A;--el-fill-color:#303030;--el-fill-color-light:#262727;--el-fill-color-lighter:#1D1D1D;--el-fill-color-extra-light:#191919;--el-fill-color-blank:transparent;--el-mask-color:rgba(0,0,0,0.8);--el-mask-color-extra-light:rgba(0,0,0,0.3)}html.dark .el-button{--el-button-disabled-text-color:rgba(255,255,255,0.5)}html.dark .el-card{--el-card-bg-color:var(--el-bg-color-overlay)}html.dark .el-empty{--el-empty-fill-color-0:var(--el-color-black);--el-empty-fill-color-1:#4b4b52;--el-empty-fill-color-2:#36383d;--el-empty-fill-color-3:#1e1e20;--el-empty-fill-color-4:#262629;--el-empty-fill-color-5:#202124;--el-empty-fill-color-6:#212224;--el-empty-fill-color-7:#1b1c1f;--el-empty-fill-color-8:#1c1d1f;--el-empty-fill-color-9:#18181a}
</style>
