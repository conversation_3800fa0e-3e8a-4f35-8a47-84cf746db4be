/**
 * 用于瀑布流
 * 例子：
 import {useWaterFall} from "./hooks/waterFall";
 const {menuInfo,waterFall} = useWaterFall([
 {height:260,top:0,left:0},
 {height:270,top:0,left:0},
 ])
 watch(menuInfo,() => {
 waterFall()
 })

 <template>
 ...
 <div class="item" :style="{height:menuInfo[0].height+'px',top:menuInfo[0].top+'px',left:menuInfo[0].left+'px'}">
 ...
 </template>
 */


import {ref, onBeforeMount,watch} from 'vue'
import { useZoomStore } from '../stores/zoomStore';
// interface WaterFallItem {
//     height: number;
//     top: number;
//     left: number;
// }
export function useWaterFall(menu: Array<any>) {
    const zoomStore = useZoomStore();

// export function useWaterFall(menu: WaterFallItem[]) {
    // let menuInfo = ref<WaterFallItem[]>([...menu]);
    let menuInfo = ref([...menu])

    const windowHeight = ref(0);

    const windowWidth = ref(0);

    let currentHeight = ref<Array<number>>([])//每列的高度

    onBeforeMount(() => {
        getWindowSize()
        window.addEventListener('resize', getWindowSize);
    })

    function getWindowSize() {
        windowWidth.value = window.innerWidth;
        windowHeight.value = window.innerHeight;
        waterFall()
    }

    watch(menuInfo.value,() => {
        waterFall()
    })

    watch(() => zoomStore.zoomLevel,()=>{
        waterFall()
    },{immediate:true})

    function waterFall() {
        currentHeight.value = []
        windowWidth.value = (window.innerWidth - (180 * zoomStore.zoomLevel)) / zoomStore.zoomLevel;
        // console.log(window.innerWidth)
        windowHeight.value = window.innerHeight  / zoomStore.zoomLevel;
        let columns = ref(Math.floor(windowWidth.value / 620))
        for (let i = 0; i < columns.value; i++) {
            currentHeight.value.push(0);
        }
        menuInfo.value.forEach((item, index) => {
            const columnIndex = currentHeight.value.indexOf(Math.min(...currentHeight.value)); //寻找高度最小的一列
            item.left = columnIndex * (620) + 20
            item.top = currentHeight.value[columnIndex] + 20;//某一列当前的高
            currentHeight.value[columnIndex] = item.top + item.height
        })
    }

    return {menuInfo,windowHeight,windowWidth,currentHeight,waterFall}
}
