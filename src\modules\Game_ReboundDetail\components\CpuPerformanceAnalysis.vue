<script setup lang="ts">
import {useReboundDetailStore, dropdown_menu} from "@/modules/Game_ReboundDetail/stores/reboundDetail";
import {storeToRefs} from "pinia";
import {computed, ref} from "vue";
import {
  getHoursMinutesSeconds,
  inputFormatter,
  displayHMSTime,
  totalSeconds
} from "@/modules/Game_ReboundDetail/components/someScripts";
import CpaCharts from "@/modules/Game_ReboundDetail/components/CPUPerformanceComponents/CpaCharts.vue";
import PerformanceAnalysis
  from "@/modules/Game_ReboundDetail/components/CPUPerformanceComponents/PerformanceAnalysis.vue";

const $store = useReboundDetailStore()
const {startTime, endTime, endTimeOriginal, gameTime} = storeToRefs($store)
const {handleInputTime, handlePointCommand} = $store
console.log($store)
const props = defineProps({
  hardwaerinfo: {
    type: Object,
    required: true
  },
  recentGameInfo: {
    type: Object,
    required: true
  },
  powerData: {
    type: Object,
    required: true
  },
})
const dropdownImgUrl = ref(''); // 设置时间的下拉菜单标记点的图片
const dropdown_point_img = ref(); // 控制dom用的
const dropdown_point_index = ref(-1); // 第几个标记点
const line_width = computed(() => { // dur-process 宽度
  const end = totalSeconds(endTime.value)
  const start = totalSeconds(startTime.value)
  return (end - start) / gameTime.value * 800 + 'px'
})
const line_left = computed(() => { // dur-process 位置
  const start = totalSeconds(startTime.value)
  return start / gameTime.value * 800 + 'px'
})
const line_item = computed(() => { // 标记点
  if (props.powerData && props.powerData.points) {
    let map: any = {}
    const totalLen = props.powerData.points.length
    for (let i = 0; i < totalLen; i++) {
      const item = props.powerData.points[i]
      if (item !== null) {
        const dur = Math.ceil(Number((gameTime.value / totalLen) * i))
        const hms = getHoursMinutesSeconds(dur)
        map[i] = {
          imgurl: item,
          time: {
            ...hms
          },
          left: `${i / totalLen * 800}px`,
        }
      }
    }
    return map
  }
  return {}
})

const handleDropdownPointMouseIn = (event: MouseEvent, imgurl: string, type: string, index: number) => {
  setTimeout(() => {
    dropdown_point_index.value = index
    if (imgurl) {
      const {clientY} = event;
      dropdownImgUrl.value = imgurl
      if (type === 'start') {
        dropdown_point_img.value.style.left = 211 + 'px'
      } else {
        dropdown_point_img.value.style.left = 650 + 'px'
      }
      dropdown_point_img.value.style.top = clientY - 80 + 'px'
      dropdown_point_img.value.style.display = 'block'
    }
  })
}
const handleDropdownPointMouseLeave = () => {
  dropdown_point_img.value.style.display = 'none'
  dropdownImgUrl.value = ''
  dropdown_point_index.value = -1
}
</script>

<template>
  <div class="CPU-performance-analysis">
    <div class="charts-dur">
      <el-dropdown trigger="click" max-height="200" @command="(e)=>handlePointCommand(e,'start')">
        <div class="start-time">
          <el-input v-model="startTime.h" :formatter="inputFormatter" size="small"
                    @change="(e)=>handleInputTime(e,'h','start')"/>
          <span>:</span>
          <el-input v-model="startTime.m" :formatter="inputFormatter" size="small"
                    @change="(e)=>handleInputTime(e,'m','start')"
          />
          <span>:</span>
          <el-input v-model="startTime.s" :formatter="inputFormatter" size="small"
                    @change="(e)=>handleInputTime(e,'s','start')"
          />
          <span class="iconfont icon-hideshow"></span>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item :command="{time: {h:0,m:0,s:0}}">
              <div class="dropdown_point">
                <span>{{ $t('GameRebound.StartStatistics') }}</span>
                <span>00 : 00 : 00</span>
              </div>
            </el-dropdown-item>
            <el-dropdown-item
                v-for="(item, index) in Object.keys(line_item)"
                :key="item"
                :command="line_item[item]"
            >
              <div class="dropdown_point"
                   @mouseenter="handleDropdownPointMouseIn($event, line_item[item].imgurl,'start',index)"
                   @mouseleave="handleDropdownPointMouseLeave">
                <span>{{ $t('GameRebound.Mark') }}{{ index + 1 }}</span>
                <span>{{ displayHMSTime(line_item[item].time) }}</span>
              </div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <div class="dur-process">
        <div class="line" :style="{width: line_width,left: line_left}"></div>
        <div
            v-for="(item,index) in Object.keys(line_item)"
            class="line-item"
            :class="{'is-active': (index === dropdown_point_index)}"
            :style="{left: line_item[item].left}"
            :key="index+'dpl'"
        >
        </div>
      </div>
      <el-dropdown trigger="click" max-height="200" @command="(e)=>handlePointCommand(e,'end')">
        <div class="end-time">
          <el-input v-model="endTime.h" :formatter="inputFormatter" size="small"
                    @change="(e)=>handleInputTime(e,'h','end')"
          />
          <span>:</span>
          <el-input v-model="endTime.m" :formatter="inputFormatter" size="small"
                    @change="(e)=>handleInputTime(e,'m','end')"
          />
          <span>:</span>
          <el-input v-model="endTime.s" :formatter="inputFormatter" size="small"
                    @change="(e)=>handleInputTime(e,'s','end')"
          />
          <span class="iconfont icon-hideshow"></span>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item :command="{time: endTimeOriginal}">
              <div class="dropdown_point">
                <span>{{ $t('GameRebound.EndStatistics') }}</span>
                <span>{{ displayHMSTime(endTimeOriginal) }}</span>
              </div>
            </el-dropdown-item>
            <el-dropdown-item
                v-for="(item, index) in Object.keys(line_item)"
                :key="item"
                :command="line_item[item]"
            >
              <div class="dropdown_point"
                   @mouseenter="handleDropdownPointMouseIn($event, line_item[item].imgurl,'end',index)"
                   @mouseleave="handleDropdownPointMouseLeave">
                <span>{{ $t('GameRebound.Mark') }}{{ index + 1 }}</span>
                <span>{{ displayHMSTime(line_item[item].time) }}</span>
              </div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <div class="charts-container">
      <CpaCharts/>
    </div>
    <div class="performance-analysis">
      <PerformanceAnalysis />
    </div>

    <div class="dropdown_point_img" ref="dropdown_point_img">
      <span class="img-name">
        {{dropdownImgUrl.split('\\')[dropdownImgUrl.split('\\').length-1]}}
      </span>
      <img :src="dropdownImgUrl" alt="">
    </div>
  </div>
</template>

<style scoped lang="scss">
.CPU-performance-analysis {
  width: 100%;
  padding: 10px 0 10px 20px;
  height: calc(100vh - 81px);
  display: flex;
  flex-direction: column;
  gap: 10px;
  background: #22232E;

  .charts-dur {
    :deep(.el-input__wrapper) {
      padding: 0;
    }

    :deep(.el-input-number--small) {
      width: 40px;
      line-height: 20px;
      font-size: 12px;
      text-align: center;
    }

    :deep(.el-input__inner) {
      text-align: center;
    }
  }

  .charts-container {
    width: 1240px;
    height: 300px;
    background: #2B2C37;
    border-radius: 4px;
    padding: 25px 20px 20px 20px;
  }

  .performance-analysis {
    width: 1240px;
    height: calc(100vh - 451px);
    background: #2B2C37;
    border-radius: 4px;
    overflow: auto;
    padding: 25px 0 20px 20px;
  }
}

.dropdown_point_img {
  display: none;
  position: absolute;
  background-color: #373946;
  padding: 10px;

  .img-name {
    position: absolute;
    top: 13px;
    left: 50%;
    transform: translateX(-50%);
    display: inline-block;
    width: 361px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #ffffff;
  }

  img {
    width: 400px;
    height: 225px;
  }
}
</style>
