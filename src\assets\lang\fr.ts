const fr = {
  "update": {
    "Game": "Game",
    "PP": "PP",
    "upgradingInProgress": "Mise à jour en cours",
    "theModuleIsBeingUpdated": "Mise à jour du module",
    "dataIsBeingUpdated": "Mise à jour des données en cours...",
    "checkingUpdate": "Vérification des mises à jour",
    "checkingUpgrade": "Vérification des mises à jour",
    "loadingProgramComponent": "Chargement des composants du programme...",
    "loadingHotkeyModules": "Chargement de la composante de raccourcis",
    "loadingGPPModules": "Chargement des composants GamePP",
    "loadingBlackWhiteList": "Chargement de la liste noire et blanche",
    "loadingGameSetting": "Chargement des paramètres de configuration du jeu...",
    "loadingUserAbout": "Chargement des données d'authentification utilisateur",
    "loadingGameBenchmark": "Chargement de la note de jeu",
    "loadingHardwareInfo": "Chargement de la composante d'informations matérielles",
    "loadingDBModules": "Chargement du module de base de données...",
    "loadingIGCModules": "Chargement du module IGC",
    "loadingFTPModules": "Chargement du module de support FTP en cours",
    "loadingDialogModules": "Chargement du module de fenêtre de dialogue",
    "loadingDataStatisticsModules": "Chargement du module de statistiques en cours",
    "loadingSysModules": "Chargement des composants du système en cours",
    "loadingGameOptimization": "Chargement de l'optimisation du jeu",
    "loadingGameAcceleration": "Chargement de l'accélération du jeu",
    "loadingScreenshot": "Chargement de la capture d'écran de l'enregistrement",
    "loadingVideoComponent": "Chargement de la composante de compression vidéo",
    "loadingFileFix": "Chargement de la réparation du fichier",
    "loadingGameAI": "Chargement de la qualité de l'IA du jeu",
    "loadingNVAPIModules": "Chargement du module NVAPI",
    "loadingAMDADLModules": "Chargement du module AMDADL en cours",
    "loadingModules": "Chargement du module"
  },
  "messages": {
    "append": "Ajouter",
    "confirm": "Confirmer",
    "cancel": "Annuler",
    "default": "Par défaut",
    "quickSelect": "Sélection rapide",
    "onoffingame": "Activer/Désactiver la surveillance en jeu:",
    "changeKey": "Cliquez pour modifier le raccourci clavier",
    "clear": "Vider",
    "hotkeyOccupied": "La touche de raccourci est déjà utilisée, veuillez en définir une nouvelle !",
    "minimize": "Minimiser",
    "exit": "Quitter",
    "export": "Exporter",
    "import": "Importer",
    "screenshot": "Capture d'écran",
    "showHideWindow": "Afficher/Masquer la fenêtre",
    "ingameControlPanel": "Panneau de contrôle en jeu",
    "openOrCloseGameInSettings": "Activer/Désactiver le panneau de paramètres en jeu",
    "openOrCloseGameInSettings2": "Appuyez sur ce raccourci clavier pour activer",
    "openOrCloseGameInSettings3": "Activer/Désactiver la surveillance en jeu",
    "openOrCloseGameInSettings4": "Activer/Désactiver filtre de jeu",
    "startManualRecord": "Démarrer/Arrêter l'enregistrement manuel des statistiques",
    "performanceStatisticsMark": "Marqueur de statistiques de performance",
    "EnableAIfilter": "Le filtre IA doit être activé en appuyant sur ce raccourci clavier",
    "Start_stop": "Démarrer/Arrêter l'enregistrement statistique manuel",
    "pressureTest": "Test de stress",
    "moduleNotInstalled": "Module fonctionnel non installé",
    "installingPressureTest": "Module de test de charge en cours d'installation...",
    "importFailed": "Échec de l'importation",
    "gamepp": "GamePP",
    "copyToClipboard": "Copié dans le presse-papiers"
  },
  "home": {
    "homeTitle": "Accueil",
    "hardwareInfo": "Informations matériel",
    "functionIntroduction": "Fonctionnalités",
    "fixedToNav": "Épingler à la barre de navigation",
    "cancelFixedToNav": "Désépingler de la barre de navigation",
    "hardwareInfoLoading": "Chargement des informations matérielles...",
    "performanceStatistics": "Statistiques de Performance",
    "updateNow": "Mettre à jour maintenant",
    "recentRun": "Activité Récente",
    "resolution": "Résolution:",
    "duration": "Durée:",
    "gameFilter": "Filtre de jeu",
    "gameFilterHasAccompany": "Le filtre de jeu est maintenant actif",
    "gameFilterHasAccompany2": "Les utilisateurs jouent à des jeux tels que Cyberpunk, APEX et Hogwarts Legacy",
    "currentList": "Éléments surveillés dans la liste actuelle",
    "moreFunction": "Benchmark, test de stress, surveillance du bureau et d'autres fonctionnalités sont en cours de développement.",
    "newVersion": "Nouvelle version disponible !",
    "discoverUpdate": "Mise à jour trouvée !",
    "downloading": "Téléchargement en cours",
    "retry": "Réessayer",
    "erhaAI": "2HaAI",
    "recordingmodule": "Cette fonction dépend du module d'enregistrement",
    "superPower": "Mode Ultra",
    "autoRecord": "Enregistrer automatiquement les moments de kill dans les jeux et sauvegarder facilement les highlights",
    "externalDevice": "Éclairage Dynamique des Périphériques",
    "linkage": "Déclencher les scènes de kill dans les jeux et les afficher via des périphériques connectés",
    "AI": "Test de performance de l'IA",
    "test": "Testez les modèles d'IA avec GPU et visualisez le score de performance d'IA",
    "supportedGames": "Jeux pris en charge",
    "games": "Naraka: Bladepoint, Delta Force, FragPunk, League of Legends, PUBG, Valorant, The Finals, APEX Legends, COD20",
    "videoRecording": "Enregistrement vidéo",
    "videoRecording2": "Fonction d'enregistrement vidéo basée sur OBS, prend en charge l'ajustement du débit binaire vidéo et du taux d'images (FPS) pour répondre aux différentes exigences de qualité et de fluidité ; prend également en charge la « relecture instantanée », appuyez sur la touche de raccourci pour enregistrer les moments clés à tout moment.",
    "addOne": "Obtenir gratuitement",
    "gamePlatform": "Plateforme de jeux",
    "goShop": "Accéder à la page du magasin",
    "receiveDeadline": "Délai de Réclamation Postérieur",
    "2Ai": "2 Rires AI",
    "questionDesc": "Description du problème",
    "inputYourQuestion": "Veuillez entrer vos suggestions ou commentaires ici。",
    "uploadLimit": "Télécharger jusqu'à 3 images locales au format JPG/PNG/BMP",
    "email": "Courriel",
    "contactWay": "Informations de contact",
    "qqNumber": "Numéro QQ (optionnel)",
    "submit": "Soumettre"
  },
  "hardwareInfo": {
    "hardwareOverview": "Vue d'ensemble du matériel",
    "copyAllHardwareInfo": "Copier toutes les informations du matériel",
    "processor": "Processeur",
    "coreCount": "Cœurs:",
    "threadCount": "Nombre de Threads:",
    "currentFrequency": "Fréquence Actuelle:",
    "currentVoltage": "Tension actuelle:",
    "copy": "Copier",
    "releaseDate": "Date de lancement",
    "codeName": "Nom de code",
    "thermalDesignPower": "Puissance de Conception Thermique",
    "maxTemperature": "Température maximale",
    "graphicsCard": "Carte graphique",
    "brand": "Marque:",
    "streamProcessors": "Processeur de flux:",
    "Videomemory": "Mémoire vidéo:",
    "busSpeed": "Vitesse du Bus",
    "driverInfo": "Informations sur le pilote",
    "driverInstallDate": "Date d'installation du pilote",
    "hardwareID": "ID matériel",
    "motherboard": "Carte mère",
    "chipGroup": "Chipset :",
    "BIOSDate": "Date BIOS",
    "BIOSVersion": "Version BIOS",
    "PCIESlots": "emplacement PCIe",
    "PCIEVersion": "Version PCIe supportée",
    "memory": "Mémoire",
    "memoryBarCount": "Quantité:",
    "totalSize": "Taille:",
    "channelCount": "Canal:",
    "Specificmodel": "Modèle spécifique",
    "Pellet": "Fabricant de Particules",
    "memoryBarEquivalentFrequency": "Fréquence Effective de la Mémoire:",
    "hardDisk": "Disque dur",
    "hardDiskCount": "Nombre de disques durs:",
    "actualCapacity": "Capacité Réelle",
    "type": "Type",
    "powerOnTime": "Durée d'allumage",
    "powerOnCount": "Cycles d'alimentation",
    "SSDRemainingLife": "Durée de vie restante du SSD",
    "partitionInfo": "Informations de partition",
    "hardDiskController": "Contrôleur de Disque Dur",
    "driverNumber": "Numéro de série du disque",
    "display": "Moniteur",
    "refreshRate": "Taux de rafraîchissement:",
    "screenSize": "Taille de l'écran:",
    "inches": "Pouces",
    "productionDate": "Date de Production",
    "supportRefreshRate": "Taux de rafraîchissement pris en charge",
    "screenLongAndShort": "Taille de l'écran",
    "systemInfo": "Informations Système",
    "version": "Version",
    "systemInstallDate": "Date d'installation du système",
    "systemBootTime": "Heure de Démarrage Actuelle",
    "systemRunTime": "Durée d'exécution",
    "Poccupied": "P Utilisation",
    "Eoccupied": "E occupé",
    "occupied": "Occupé",
    "temperature": "Température",
    "Pfrequency": "P Fréquence",
    "Efrequency": "Fréquence E",
    "thermalPower": "Puissance Thermique",
    "frequency": "Fréquence",
    "current": "Actuel",
    "noData": "Pas de données",
    "loadHwinfo_SDK": "Échec du chargement de Hwinfo_SDK.dll, impossible de lire les données matérielles/capteurs",
    "loadHwinfo_SDK_reason": "Causes possibles de ce problème :",
    "reason": "Cause",
    "BlockIntercept": "Bloqué par le logiciel antivirus, par exemple : 2345 Logiciel Antivirus (Processus de Défense Active 2345, Processus de Défense Active McAfee)",
    "solution": "Solution :",
    "solution1": "Après avoir fermé et désinstallé les processus associés, redémarrez GamePP",
    "solution2": "Débranchez les appareils associés, puis redémarrez GamePP",
    "RestartGamePP": "Débranchez la manette, attendez que l'appareil réponde, puis redémarrez GamePP",
    "HWINFOcannotrun": "Hwinfo ne peut pas s'exécuter correctement",
    "downloadHWINFO": "Télécharger Hwinfo",
    "openHWINFO": "Après avoir ouvert Hwinfo, le clic sur RUN permet-il de lancer le programme normalement?",
    "hardwareDriverProblem": "Problèmes de pilote matérielle",
    "checkHardwareManager": "Vérifiez le gestionnaire de matériel pour vous assurer que les pilotes de la carte mère et de la carte graphique sont correctement installés",
    "systemProblem": "Problèmes système, par exemple : L'utilisation d'outils d'activation tels que Baofeng ou Xiaoma peut entraîner un échec du chargement des pilotes et l'incapacité d'installer automatiquement les correctifs système Windows 7",
    "reinstallSystem": "Réinstaller le système pour l'activer, Télécharger et installer WIN7 : Mise à jour *********",
    "Windows7": "Windows 7 : Installez le correctif SHA-256, Windows 10 : Activez à l'aide d'un certificat numérique, Windows 11 Version préliminaire : Désactivez l'intégrité de la mémoire",
    "ViolenceActivator": "Si vous avez utilisé des outils d'activation non autorisés tels que Xiaoma, veuillez réparer ou réinstaller le système",
    "MultipleGraphicsCardDrivers": "Des pilotes de cartes graphiques de marques différentes sont installés sur l'ordinateur, par exemple les pilotes AMD et Nvidia installés simultanément",
    "UninstallUnused": "Redémarrez l'ordinateur après avoir désinstallé les pilotes graphiques inutiles",
    "OfficialQgroup": "Aucune des raisons ci-dessus ne s'applique. Rejoignez notre groupe QQ officiel : 908287288 (Groupe 5) pour résoudre le problème.",
    "ExportHardwareData": "Exporter les données du matériel",
    "D3D": "Utilisation de D3D",
    "Total": "Utilisation Totale",
    "VRAM": "Utilisation de VRAM",
    "VRAMFrequency": "Fréquence VRAM",
    "SensorData": "Données du capteur",
    "CannotGetSensorData": "Échec de récupération des données du capteur",
    "LoadingHardwareInfo": "Chargement des informations matérielles...",
    "ScanTime": "Dernier scan :",
    "Rescan": "Réanalyser",
    "Screenshot": "Capture d'écran",
    "configCopyed": "Les informations de configuration ont été copiées dans le presse-papier.",
    "LegalRisks": "Risques juridiques potentiels détectés",
    "brandLegalRisks": "L'affichage de la marque peut entraîner des risques juridiques potentiels",
    "professionalVersion": "Édition Professionnelle",
    "professionalWorkstationVersion": "Édition Workstation Professionnelle",
    "familyEdition": "Édition Home",
    "educationEdition": "Édition Éducative",
    "enterpriseEdition": "Édition Entreprise",
    "flagshipEdition": "Édition Premium",
    "familyPremiumEdition": "Édition Familiale Premium",
    "familyStandardEdition": "Édition Standard Famille",
    "primaryVersion": "Version de base",
    "bit": "bit",
    "tempWall": "Mur de température",
    "error": "Erreur",
    "screenshotSuccess": "Capture d'écran sauvegardée avec succès",
    "atLeastOneData": "Au moins une entrée de données doit être conservée",
    "atMostSixData": "Ajoutez jusqu'à 6 entrées de données",
    "screenNotActivated": "Non activé"
  },
  "psc": {
    "processCoreAssign": "Allocation des cœurs de processus",
    "CoreAssign": "Allocation de noyaux:",
    "groupName": "Nom du groupe :",
    "notGameProcess": "Processus non jeu",
    "unNamedProcess": "Groupe non nommé",
    "Group2": "Groupe",
    "selectTheCore": "Sélectionner le noyau",
    "controls": "Opération",
    "tips": "Notification",
    "search": "Rechercher",
    "shiftOut": "Éjecter",
    "ppValue": "Valeur PP",
    "ppDesc": "La valeur PP reflète la consommation historique des ressources matérielles. Plus cette valeur est élevée, plus les ressources matérielles sont sollicitées.",
    "littletips": "Astuce : Maintenez enfoncé le processus pour le faire glisser vers le groupe gauche",
    "warning1": "La sélection de threads de processeur entre différents groupes peut affecter les performances. Il est recommandé d'utiliser des processeurs du même groupe.",
    "warning2": "Etes-vous sûr de laisser ce nom de groupe vide ?",
    "warning3": "L'effet d'allocation des cœurs sera invalidé après la suppression. Êtes-vous sûr de vouloir supprimer ce groupe ?",
    "allprocess": "Tous les processus",
    "pleaseCheckProcess": "Veuillez cocher le processus",
    "dataSaveDesktop": "Les données ont été enregistrées sur le bureau.",
    "createAGroup": "Créer un groupe",
    "delGroup": "Supprimer le groupe",
    "Group": "Groupe",
    "editGroup": "Modifier le groupe",
    "groupinfo": "Informations du Groupe",
    "moveOutGrouping": "Supprimer du groupe",
    "createANewGroup": "Créer un nouveau groupe",
    "unallocatedCore": "Core non attribué",
    "inactiveProcess": "Processus inactif",
    "importGroupingScheme": "Importer un profil de regroupement",
    "derivedPacketScheme": "Exporter la configuration du groupe",
    "addNowProcess": "Ajouter le processus en cours d'exécution",
    "displaySystemProcess": "Afficher les processus système",
    "max64": "Sélection maximale de 64 threads",
    "processName": "Nom du processus",
    "chooseCurProcess": "Sélectionner le processus actuel",
    "selectNoProcess": "Aucun processus sélectionné",
    "coreCount": "Cœurs",
    "threadCount": "Threads",
    "process": "Processus",
    "plzInputProcessName": "Entrez le nom du processus pour l'ajouter manuellement",
    "has_allocation": "Processus avec schémas d'allocation de threads",
    "not_made": "Vous n'avez pas attribué de noyaux à aucun processus",
    "startUse": "Activer l'optimisation",
    "stopUse": "Désactiver l'optimisation",
    "threadAllocation": "Allocation de Threads",
    "configProcess": "Configuration du processus",
    "selectThread": "Sélectionner le thread",
    "hyperthreadingState": "Statut Hyper-Threading",
    "open": "Activé",
    "notYetUnlocked": "Désactivé",
    "nonhyperthreading": "Sans Hyper-Threading",
    "intervalSelection": "Sélection d'intervalle",
    "invertSelection": "Inverser la sélection",
    "description": "Verrouille les processus de jeu sur les cœurs CPU spécifiés et isole intelligemment les interférences des programmes en arrière-plan. Augmente efficacement la limite maximale de FPS et stabilise le taux d'images. Réduit les saccades soudaines et les chutes abruptes de frame rate, libérant pleinement les performances des processeurs multi-cœurs pour assurer un gameplay fluide avec un taux d'images élevé en permanence.",
    "importSuccess": "Importation réussie",
    "importFailed": "Échec de l'importation"
  },
  "InGameMonitor": {
    "onoffingame": "Activer/Désactiver Supervision en Jeu:",
    "InGameMonitor": "Surveillance en Jeu",
    "CustomMode": "Mode Personnalisé",
    "Developing": "En développement...",
    "NewMonitor": "Ajouter un élément de surveillance",
    "Data": "Paramètres",
    "Des": "Remarque",
    "Function": "Fonctionnalité",
    "Editor": "Éditer",
    "Top": "Épingler en haut",
    "Delete": "Supprimer",
    "Use": "Utiliser",
    "DragToSet": "Une fois le panneau invoqué, vous pouvez faire glisser pour configurer",
    "MonitorItem": "Élément de surveillance",
    "addMonitorItem": "Ajouter un élément de surveillance",
    "hide": "Cacher",
    "show": "Afficher",
    "generalstyle": "Paramètres généraux",
    "restoredefault": "Restaurer les paramètres par défaut",
    "arrangement": "Mise en page",
    "horizontal": "Horizontal",
    "vertical": "Vertical",
    "monitorposition": "Emplacement de Surveillance",
    "canquickselectposition": "Sélectionnez rapidement un emplacement sur la carte de gauche",
    "curposition": "Position Actuelle:",
    "background": "Arrière-plan",
    "backgroundcolor": "Couleur d'Arrière-plan:",
    "font": "Police",
    "fontStyle": "Style de police",
    "fontsize": "Taille de la police:",
    "fontcolor": "Couleur de la Police :",
    "style": "Style:",
    "style2": "Style",
    "performance": "Performance",
    "refreshTime": "Temps d'actualisation:",
    "goGeneralSetting": "Accéder aux Paramètres généraux",
    "selectMonitorItem": "Sélectionner l'élément de surveillance",
    "selectedSensor": "Capteur sélectionné :",
    "showTitle": "Afficher le titre",
    "hideTitle": "Masquer le titre",
    "showStyle": "Mode d'affichage:",
    "remarkSize": "Taille de la note:",
    "remarkColor": "Couleur de la Note :",
    "parameterSize": "Taille du paramètre:",
    "parameterColor": "Couleur des Paramètres :",
    "lineChart": "Graphique en Ligne",
    "lineColor": "Couleur de la ligne brisée:",
    "lineThickness": "Épaisseur de Ligne:",
    "areaHeight": "Hauteur de la Région :",
    "sort": "Trier",
    "displacement": "Déplacement:",
    "up": "Déplacer vers le haut",
    "down": "Déplacer vers le bas",
    "bold": "Gras",
    "stroke": "Contour",
    "text": "Texte",
    "textLine": "Texte + Graphique Linéaire",
    "custom": "Personnalisé",
    "upperLeft": "En haut à gauche",
    "upper": "Milieu Supérieur",
    "upperRight": "En haut à droite",
    "Left": "Centre gauche",
    "middle": "Centre",
    "Right": "Centre Droit",
    "lowerLeft": "En bas à gauche",
    "lower": "Moyen-Bas",
    "lowerRight": "En bas à droite",
    "notSupport": "Le périphérique ne prend pas en charge l'affichage ou le masquage par clic",
    "notSupportRate": "Le taux de rendement ne prend pas en charge l'activation par clic",
    "notFindSensor": "Capteur non trouvé. Cliquez pour modifier.",
    "monitoring": "Surveillance",
    "condition": "Condition",
    "bigger": "Supérieur à",
    "smaller": "Inférieur à",
    "biggerThan": "Dépasse le seuil",
    "biggerThanthreshold": "Supérieur au pourcentage du seuil",
    "smallerThan": "Inférieur au seuil",
    "smallerThanthreshold": "Inférieur au pourcentage seuil",
    "biggerPercent": "Pourcentage de baisse de la valeur actuelle",
    "smallerPercent": "Pourcentage d'augmentation de la valeur actuelle",
    "replay": "Fonction de Replay en Temps Réel",
    "screenshot": "Capture d'Écran",
    "text1": "Valeur du capteur lorsque",
    "text2": ", et dans",
    "text3": "Temps d'attente",
    "text4": "Si aucune valeur plus élevée n'apparaît en X secondes, déclenche immédiatement",
    "text5": "Après chaque déclenchement de replay, mettez à jour le seuil avec la valeur au moment du déclenchement pour réduire les activations fréquentes",
    "text6": "Afficher le seuil actuel pour déclencher le replay",
    "text7": "Afficher les valeurs du capteur",
    "text8": "Dépasser le seuil initial",
    "text9": "En dessous du seuil initial",
    "text10": "Compteur seuil initial",
    "initThreshold": "Seuil initial",
    "curThreshold": "Seuil actuel :",
    "curThreshold2": "Seuil actuel",
    "resetCurThreshold": "Réinitialiser le seuil actuel",
    "action": "Activer la Fonction",
    "times": "fois",
    "percentage": "Pourcentage",
    "uninstallobs": "Module d'enregistrement non téléchargé",
    "install": "Télécharger",
    "performanceAndAudioMode": "Mode de compatibilité performance et audio",
    "isSaving": "Enregistrement en cours",
    "video_replay": "Répétition instantanée",
    "saved": "Sauvegardé",
    "loadQualitysScheme": "Charger le préréglage graphique",
    "notSet": "Non configuré",
    "mirrorEnable": "Le filtre est activé",
    "canBeTurnedOff": "Retour",
    "mirrorClosed": "Filtre de jeu désactivé",
    "closed": "Fermé",
    "openMirror": "Activer le filtre",
    "wonderfulScenes": "Moments marquants",
    "VulkanModeHaveProblem": "Le mode Vulkan présente des problèmes de compatibilité",
    "suggestDxMode": "Nous vous recommandons de passer au mode Dx",
    "functionNotSupported": "Fonction non prise en charge",
    "NotSupported": "Non pris en charge",
    "gppManualRecording": "GamePP, enregistrement manuel",
    "perfRecordsHaveBeenSaved": "Les données de performance ont été enregistrées",
    "redoClickF8": "Appuyez à nouveau sur F8 pour continuer l'enregistrement.",
    "startIngameMonitor": "Activation de la fonction de surveillance en jeu",
    "inGameMarkSuccess": "Le marquage dans le jeu a été réussi",
    "recordingFailed": "Échec de l'enregistrement",
    "recordingHasNotDownload": "La fonction d'enregistrement n'a pas été téléchargée",
    "hotkeyDetected": "Conflit de raccourcis de fonction détecté",
    "plzEditIt": "Veuillez modifier dans le logiciel, puis l'utiliser",
    "onePercentLowFrame": "1% Faible trame",
    "pointOnePercentLowFrame": "0.1% Faibles images",
    "frameGenerationTime": "Temps de génération de trame",
    "curTime": "Heure actuelle",
    "runTime": "Durée d'exécution",
    "cpuTemp": "Température du CPU",
    "cpuUsage": "Utilisation du CPU",
    "cpuFreq": "Fréquence CPU",
    "cpuPower": "Puissance thermique du CPU",
    "gpuTemp": "Température GPU",
    "gpuUsage": "Taux d'utilisation de la GPU",
    "gpuPower": "Puissance thermique GPU",
    "gpuFreq": "Fréquence GPU",
    "memUsage": "Utilisation de la mémoire"
  },
  "LoginArea": {
    "login": "Connexion",
    "loginOut": "Se déconnecter",
    "vipExpire": "Expiré",
    "remaining": "Restant",
    "day": "Ciel",
    "openVip": "Activer l'abonnement",
    "vipPrivileges": "Privileges d'abonné",
    "rechargeRenewal": "Recharger et Renouveler",
    "Exclusivefilter": "Filtre",
    "configCloudSync": "Configuration de la synchronisation cloud",
    "comingSoon": "À venir"
  },
  "GameMirror": {
    "filterStatus": "État du filtre",
    "filterPlan": "Preset de Filtre",
    "filterShortcut": "Raccourcis de filtre",
    "openCloseFilter": "Activer/Désactiver le filtre:",
    "effectDemo": "Démonstration d'Effets",
    "demoConfig": "Configuration de Démonstration",
    "AiFilter": "Les effets du filtre d'IA sont soumis aux effets en jeu",
    "AiFilterFAQ": "Problèmes courants des filtres AI",
    "gamePPAiFilter": "Filtre AI de GamePP",
    "gamePPAiFilterVip": "Filtre IA VIP exclusif de GamePP, ajuste en temps réel des paramètres du filtre en fonction des scénarios de jeu pour optimiser les effets visuels et améliorer l'expérience de jeu.",
    "AiMingliangTips": "Luminosité AI : Recommandé lorsque l'écran du jeu est trop sombre.",
    "AiBrightTips": "AI Vive : Recommandé pour une utilisation lorsque l'affichage du jeu est trop sombre.",
    "AiDarkTips": "AI Dimming : Recommandé pour une utilisation lorsque les visuels du jeu sont trop éclatants.",
    "AiBalanceTips": "Équilibre de l'IA : adapté à la plupart des scénarios de jeu.",
    "AiTips": "Conseils : Le filtre IA doit être utilisé en appuyant sur la touche de raccourci dans le jeu",
    "AiFilterUse": "Veuillez utiliser dans le jeu",
    "AiFilterAdjust": "Ajustement du filtre AI via le raccourci clavier",
    "Bright": "Vibrant",
    "Soft": "Doux",
    "Highlight": "Surligner",
    "Film": "Film",
    "Benq": "BenQ",
    "AntiGlare": "Anti-éblouissement",
    "HighSaturation": "Haut niveau de saturation",
    "Brightness": "Vif",
    "Day": "Jour",
    "Night": "Nuit",
    "Nature": "Naturel",
    "smooth": "Fin",
    "elegant": "Austère",
    "warm": "Teinte Chaude",
    "clear": "Clair",
    "sharp": "Netteté",
    "vivid": "Dynamique",
    "beauty": "Vibrant",
    "highDefinition": "Haute Définition",
    "AiMingliang": "IA Brillante",
    "AiBright": "IA Vive",
    "AiDark": "AI Assombri",
    "AiBalance": "Équilibre IA",
    "BrightTips": "Le filtre vif convient aux jeux de détente, d'action ou d'aventure, en renforçant la saturation des couleurs pour rendre l'affichage plus vivant et captivant.",
    "liangTips": "Les filtres sont recommandés lorsque画面 du jeu est trop sombre.",
    "anTips": "Le filtre est recommandé pour une utilisation lorsque l'écran du jeu est trop sombre.",
    "jianyiTips": "Le filtre est recommandé lorsque les graphismes du jeu sont trop vibrants.",
    "shiTips": "Le filtre convient à la plupart des scènes de jeu.",
    "shi2Tips": "Le filtre convient aux jeux de détente, d'action ou d'aventure, améliore la saturation des couleurs pour rendre l'affichage plus vivant et captivant.",
    "ruiTips": "Couleurs délicates du filtre, éclairage doux, adapté aux scènes rêveuses, chaleureuses ou nostalgiques",
    "qingTips": "Ton clair, contraste élevé, détails nets et précis, adapté à l'affichage de scènes vivantes et lumineuses.",
    "xianTips": "Paramètres de contraste et luminosité élevés assurant une restitution fidèle des détails dans les scènes sombres et un confort visuel dans les scènes lumineuses.",
    "dianTips": "Améliorez modérément la luminosité et les couleurs de l'écran pour obtenir un rendu visuel proche du cinéma",
    "benTips": "Réduit les effets de lumière blanche pour atténuer l'éblouissement dans les environnements de jeu entièrement blancs",
    "fangTips": "Optimisé pour les jeux open world et d'aventure, améliore la luminosité et le contraste pour des visuels plus nets",
    "jiaoTips": "Adapté aux jeux de rôle et de simulation, tons équilibrés, réalisme visuel amélioré",
    "jieTips": "Optimisé pour les jeux riches en scénario et émotions nuancées, renforçant les détails et la douceur pour des visuels plus élaborés",
    "jingTips": "Optimisé pour les jeux d'action et compétitifs, améliore la clarté et le contraste pour des visuels plus nets",
    "xiuTips": "Optimisé pour les jeux de guérison et les jeux décontractants, renforce les tons chauds et la douceur, créant une ambiance plus chaleureuse",
    "qihuanTips": "Adapté aux scènes riches en éléments fantastiques et aux couleurs vives, renforçant la saturation des couleurs pour produire un impact visuel marqué",
    "shengTips": "Renforcez les couleurs et détails pour mettre en évidence la vivacité et le réalisme de la scène,",
    "sheTips": "Optimisé pour les jeux de type FPS, réflexion ou aventure, améliore les détails et le contraste pour renforcer le réalisme du monde de jeu.",
    "she2Tips": "Convient aux jeux de tir, de course ou de combat, met en valeur les détails haute définition et la performance dynamique pour renforcer l'intensité et l'impact visuel de l'expérience de jeu",
    "an2Tips": "Améliore la clarté des scènes sombres, adapté aux environnements sombres ou nocturnes.",
    "wenTips": "Convient aux jeux artistiques, d'aventure ou de détente, crée des nuances de couleurs douces et des effets de lumière et d'ombre pour ajouter une élégance et une chaleur à la scène。",
    "jing2Tips": "Adapté aux jeux compétitifs, jeux de rythme musical ou scénarios urbains nocturnes, mettant en évidence des couleurs vives et des effets d'éclairage,",
    "jing3Tips": "Optimisé pour les jeux de compétition, d'action ou de fantasy, renforce le contraste des couleurs pour rendre l'image plus riche et dynamique.",
    "onlyVipCanUse": "Ce filtre n'est disponible qu'aux utilisateurs VIP"
  },
  "GameRebound": {
    "noGame": "Aucun enregistrement de jeux",
    "noGameRecord": "Aucun historique de jeu enregistré ! Commencez une session dès maintenant !",
    "gameDuration": "Durée de jeu aujourd'hui:",
    "gameElectricity": "Consommation Quotidienne",
    "degree": "Degré",
    "gameCo2": "Émission de CO₂ aujourd'hui :",
    "gram": "Clé",
    "manualRecord": "Enregistrement manuel",
    "recordDuration": "Durée d'enregistrement:",
    "details": "Détails",
    "average": "Moyenne",
    "minimum": "Valeur minimale",
    "maximum": "Valeur Maximale",
    "occupancyRate": "Utilisation",
    "voltage": "Tension",
    "powerConsumption": "Consommation d'Énergie",
    "start": "Démarrer:",
    "end": "Terminer",
    "Gametime": "Durée de jeu:",
    "Compactdata": "Optimisation des Données",
    "FullData": "Données Complètes",
    "PerformanceAnalysis": "Analyse de performance",
    "PerformanceAnalysis2": "Rapport d'événement",
    "HardwareStatus": "État du matériel",
    "totalPower": "Consommation d'énergie totale",
    "TotalEmissions": "Émission Totale",
    "PSS": "Remarque : Les données ci-dessous du graphique représentent des valeurs moyennes",
    "FrameGenerationTime": "Temps de génération de trames",
    "GameResolution": "Résolution du jeu",
    "FrameGenerationTimeTips": "Cette valeur est anormalement élevée et n'a pas été incluse dans les statistiques",
    "FrameGenerationTimeTips2": "Ce point de données est anormalement bas et n'est donc pas inclus dans les statistiques",
    "noData": "Aucun",
    "ProcessorOccupancy": "Utilisation de la CPU",
    "ProcessorFrequency": "Fréquence du processeur",
    "ProcessorTemperature": "Température du processeur",
    "ProcessorHeatPower": "Puissance thermique du processeur",
    "GraphicsCardOccupancy": "Utilisation de D3D sur la carte graphique",
    "GraphicsCardOccupancyTotal": "Utilisation totale de la carte graphique",
    "GraphicsCardFrequency": "Fréquence GPU",
    "GraphicsCardTemperature": "Température du GPU",
    "GraphicsCardCoreTemperature": "Température du Point Chaud du Noyau de la Carte Graphique",
    "GraphicsCardHeatPower": "Puissance thermique du GPU",
    "GraphicsCardMemoryTemperature": "Température de la mémoire GPU",
    "MemoryOccupancy": "Utilisation de la mémoire",
    "MemoryTemperature": "Température de la mémoire",
    "MemoryPageFaults": "Interruption de pagination de mémoire",
    "Duration": "Durée",
    "Time": "Temps",
    "StartStatistics": "Démarrer les statistiques",
    "Mark": "Étiquette",
    "EndStatistics": "Terminer les statistiques",
    "LineChart": "Graphique en Ligne",
    "AddPointInGame_m1": "Appuyez dans le jeu",
    "AddPointInGame_m2": "Point de repère ajoutable",
    "LeftMouse": "Clic gauche pour afficher/masquer, clic droit pour changer la couleur",
    "DeleteThisLine": "Supprimer la polyligne",
    "AddCurve": "Ajouter courbe",
    "AllCurvesAreHidden": "Tous les graphiques de courbes sont masqués",
    "ThereAreSamplingData": "Données d'échantillonnage totales:",
    "Items": "Entrée",
    "StatisticsData": "Statistiques",
    "electricity": "Consommation électrique",
    "carbonEmission": "Émissions de Carbone",
    "carbonEmissionTips": "Émissions de dioxyde de carbone (kg) = Consommation électrique (kWh) × 0.785",
    "D3D": "Utilisation de D3D:",
    "TOTAL": "Utilisation totale:",
    "Process": "Processus:",
    "L3Cache": "Cache L3 :",
    "OriginalFrequency": "Fréquence d'origine:",
    "MaximumBoostFrequency": "Turbo Boost Maximum:",
    "DriverVersion": "Version du pilote :",
    "GraphicsCardMemoryBrand": "Marque de la mémoire vidéo :",
    "Bitwidth": "Largeur du bus",
    "System": "Système:",
    "Screen": "Écran",
    "Interface": "Interface:",
    "Channel": "Canal:",
    "Timing": "Séquence:",
    "Capacity": "Capacité:",
    "Generation": "Algèbre",
    "AddPoint_m1": "Appuyez dans le jeu",
    "AddPoint_m2": "Ajouter un point de repère",
    "Hidden": "Masqué",
    "Totalsampling": "Données Totales d'Échantillonnage:",
    "edition": "Version du pilote:",
    "MainHardDisk": "Disque Dur Principal",
    "SetAsStartTime": "Définir comme heure de début",
    "SetAsEndTime": "Définir comme heure de fin",
    "WindowWillBe": "La fenêtre de statistiques de performances sera dans",
    "After": "Fermer après",
    "NoLongerPopUpThisGame": "Ce jeu ne s'affichera plus",
    "HideTemperatureReason": "Masquer la raison de la température",
    "HideTemperatureReason2": "Masquer le rapport d'événements",
    "HideOtherReason": "Masquer Autres Raisons",
    "CPUanalysis": "Analyse des performances du CPU",
    "TemperatureCause": "Cause de température",
    "tempSensorEvent": "Événement du capteur de température",
    "NoTemperatureLimitation": "Aucune limitation thermique des performances du CPU détectée. Votre système de refroidissement est parfaitement adapté aux exigences de ce jeu.",
    "NoTemperatureLimitation2": "Aucun événement de capteur de température, votre système de refroidissement peut parfaitement gérer les exigences de ce jeu.",
    "performanceis": "Dans sélectionné",
    "Inside": "Interne,",
    "TheStatisticsTimeOf": "La période statistique satisfait aux conditions de déclenchement associées. La raison ayant la fréquence de déclenchement la plus élevée est",
    "limited": "Pourcentage du temps total dû aux limitations de performances causées par la température",
    "SpecificReasons": "Causes spécifiques et leur proportion dans les causes liées à la température :",
    "OptimizationSuggestion": "Suggestions d'optimisation:",
    "CPUtemperature": "La température du CPU est trop élevée. Veuillez vérifier/améliorer l'environnement de refroidissement du CPU.",
    "CPUoverheat": "Surchauffe du CPU due à l'alimentation de la carte mère. Vérifiez les paramètres liés à la carte mère ou améliorez l'environnement de refroidissement.",
    "OtherReasons": "Autres raisons",
    "NoPowerSupplyLimitation": "La performance du CPU n'est pas limitée par l'alimentation/consommation d'énergie. Vos paramètres de consommation d'énergie dans le BIOS sont parfaitement adaptés aux besoins de ce jeu.",
    "PowerSupplyLimitation": "Dû à des limitations d'alimentation/consommation d'énergie",
    "SpecificReasonsInOtherReasons": "Cause spécifique et sa proportion par rapport aux autres causes :",
    "PleaseCheckTheMainboard": "Vérifiez l'alimentation de la carte mère ou ajustez les paramètres de puissance du BIOS pour résoudre les limitations de performance du CPU dues à d'autres causes",
    "CPUcoretemperature": "La température du noyau a atteint Tj,Max et a été limitée",
    "CPUCriticalTemperature": "Température critique du CPU",
    "CPUCircuitTemperature": "Emballage CPU/Bus à anneaux limité par l'atteinte de Tj,Max",
    "CPUCircuitCriticalTemperature": "CPU Package/Ring Bus a atteint la température critique",
    "CPUtemperatureoverheating": "Surchauffe du CPU détectée, entraînant une réduction automatique de la fréquence pour abaisser la température et éviter les pannes matériels",
    "CPUoverheatingtriggered": "En cas de surchauffe activant le mécanisme de refroidissement, le processeur ajustera la tension et la fréquence pour réduire la consommation d'énergie et la température",
    "CPUPowerSupplyOverheating": "CPU est limité en raison d'une surchauffe critique de l'alimentation de la carte mère",
    "CPUPowerSupplyLimitation": "CPU limité en raison de la surchauffe de l'alimentation de la carte mère",
    "CPUMaximumPowerLimitation": "Le noyau est soumis à la limite maximale de consommation d'énergie",
    "CPUCircuitPowerLimitation": "Le package CPU/bus en anneau a atteint la limite de puissance",
    "CPUElectricalDesignLimitation": "Activer les restrictions de conception électrique (incluant limite de courant ICCmax, mur de puissance crête PL4, limitation de tension SVID, etc.)",
    "CPULongTermPowerLimitation": "Consommation électrique prolongée du CPU atteinte la limite",
    "CPULongTermPowerinstantaneous": "La consommation d'énergie CPU instantanée a atteint la limite",
    "CPUPowerLimitation": "Mécanisme de dégradation de la fréquence Turbo CPU, généralement restreint par le BIOS ou un logiciel spécifique",
    "CPUPowerWallLimitation": "Limite de puissance du CPU",
    "CPUcurrentwalllimit": "Limite de Mur d'Intensité CPU",
    "AiAgent": "Agent GamePP (AI Agent)",
    "AgentDesc": "Retour à la page d'accueil",
    "fnBeta": "Cette fonction est actuellement en phase de test sur invitation. Votre compte GamePP n'a pas encore reçu l'accès au test.",
    "getAIReport": "Obtenir le rapport d'IA",
    "waitingAi": "En attente de la finalisation de la génération du rapport",
    "no15mins": "Durée de jeu inférieure à 15 minutes, impossible de récupérer un rapport AI valide",
    "timeout": "La demande au serveur a expiré",
    "agentId": "ID d'Agent:",
    "reDo": "Re-générer le rapport",
    "text2": "Agent GamePP : Rapport d'analyse IA en ligne, le contenu suivant est généré par l'IA et à titre informatif uniquement.",
    "amdAiagentTitle": "Agent GamePP : Rapport d'analyse AMD Ryzen AI, le contenu suivant est généré par l'IA et à titre indicatif uniquement.",
    "noCurData": "Données actuelles indisponibles",
    "dataScreening": "Filtrage des données",
    "dataScreeningDescription": "Cette fonction vise à exclure les statistiques de données provenant des périodes de jeu non efficaces telles que le chargement de la carte ou l'attente dans la salle d'attente. 0 indique qu'aucune exclusion n'est appliquée.",
    "excessivelyHighParameter": "Paramètres excessifs",
    "tooLowParameter": "Paramètres trop bas",
    "theMaximumValueIs": "Valeur maximale est",
    "theMinimumValueIs": "Valeur minimale est",
    "exclude": "Exclure",
    "dataStatisticsAtThatTime": "Statistiques de données à ce moment",
    "itHasBeenGenerated": "Génération terminée,",
    "clickToView": "Cliquez pour afficher",
    "onlineAnalysis": "Analyse en ligne",
    "amdRyzenAILocalAnalysis": "AMD Ryzen AI Analyse locale",
    "useTerms": "Conditions d'utilisation:",
    "term1": "1. Processeur Ryzen AI Max ou Processeur de la série Ryzen Al 300",
    "term2": "2.Version du pilote AMD NPU",
    "term3": "3. Activer les graphiques intégrés",
    "conformsTo": "Conforme",
    "notInLineWith": "Invalide",
    "theVersionIsTooLow": "Version obsolète",
    "canNotUseAmdNpu": "Votre configuration ne répond pas aux exigences. L'analyse d'entité AMD NPU ne peut pas être utilisée。",
    "unusable": "Non disponible",
    "downloadTheFile": "Télécharger le fichier",
    "downloadSource": "Source de téléchargement :",
    "fileSize": "Taille du fichier : environ 8,34 Go",
    "cancelDownload": "Annuler le téléchargement",
    "filePath": "Emplacement du fichier",
    "generateAReport": "Générer un rapport",
    "fileMissing": "Fichier manquant. Réinstallation nécessaire.",
    "downloading": "Téléchargement en cours...",
    "theModelConfigurationLoadingFailed": "Impossible de charger la configuration du modèle",
    "theModelDirectoryDoesNotExist": "Le répertoire de modèle n'existe pas",
    "thereIsAMistakeInReasoning": "Erreur de déduction",
    "theInputExceedsTheModelLimit": "L'entrée dépasse la limite du modèle",
    "selectModelNotSupport": "Le modèle de téléchargement sélectionné n’est pas supporté",
    "delDirFail": "Échec de la suppression du répertoire de modèle existant",
    "failedCreateModelDir": "Échec de la création du répertoire modèle",
    "modelNotBeenFullyDownload": "Le modèle n'a pas été entièrement téléchargé",
    "agentIsThinking": "L'agent Jiajia réfléchit",
    "reasoningModelFile": "Fichier de modèle pour l'inférence",
    "modelReasoningTool": "Outil d'inférence de modèle"
  },
  "SelectSensor": {
    "DefaultSensor": "Capteur par défaut",
    "Change": "Modifier",
    "FanSpeed": "Vitesse du ventilateur",
    "MainGraphicsCard": "Carte graphique principale",
    "SetAsMainGraphicsCard": "Définir comme GPU principal",
    "GPUTemperature": "Température du GPU",
    "GPUHeatPower": "Consommation thermique du GPU",
    "GPUTemperatureD3D": "Utilisation de D3D par le GPU",
    "GPUTemperatureTOTAL": "Utilisation totale du GPU",
    "GPUTemperatureCore": "Température du core GPU Hotspot",
    "MotherboardTemperature": "Température de la Carte Mère",
    "MyAttention": "Mes Favoris",
    "All": "Tout",
    "Unit": "Unité:",
    "NoAttention": "Capteurs non suivis",
    "AttentionSensor": "Capteurs suivis (Bêta)",
    "GoToAttention": "Accéder au Focus",
    "CancelAttention": "Ne plus suivre",
    "noThisSensor": "Pas de capteur",
    "deviceAbout": "Lié aux périphériques",
    "deviceBattery": "Batterie des périphériques",
    "testFunction": "Fonction de test",
    "mouseEventRate": "Taux de poll",
    "relatedWithinTheGame": "Lié au jeu",
    "winAbout": "Système",
    "trackDevicesBattery": "Suivre le niveau de batterie des périphériques",
    "ingameRealtimeMouseRate": "Taux d'échantillonnage de la souris utilisé en temps réel pendant le jeu",
    "notfoundDevice": "Aucun appareil compatible trouvé",
    "deviceBatteryNeedMythcool": "Liste des périphériques d'affichage de batterie pris en charge : (nécessite Myth.Cool)",
    "vkm1mouse": "Souris Valkyrie M1",
    "vkm2mouse": "Valkyrie M2 Souris",
    "vk99keyboard": "Valkyrie 99 Clavier à Axes Magnétiques",
    "logitechProWireless": "Logitech Pro Wireless",
    "logitechProXSUPERLIGHT": "Logitech PRO X SUPERLIGHT",
    "logitechProXSUPERLIGHT2": "Logitech PRO",
    "logitechPro2LIGHTSPEED": "Logitech PRO 2 LIGHTSPEED",
    "steelSeriesArctisGameBuds": "SteelSeries Arctis GameBuds",
    "steelSeriesArctis7PPlus": "SteelSeries Arctis 7P Plus",
    "razerV3": "Razer Valkyrie V3 Édition Professionnelle",
    "razerV2": "Razer Viper V2 Édition Professionnelle",
    "wireless": "Sans fil",
    "logitechNeedGhub": "Lorsque Logitech ne peut pas récupérer le modèle de l'appareil, téléchargez GHUB",
    "chargingInProgress": "Chargement",
    "inHibernation": "En mode veille"
  },
  "video": {
    "videoRecord": "Enregistrement Vidéo",
    "recordVideo": "Vidéo enregistrée",
    "scheme": "Profil",
    "suggestScheme": "Plan recommandé",
    "text1": "Dans cette configuration, la taille de la vidéo d'1 minute est d'environ",
    "text2": "Cette fonctionnalité utilise des ressources système supplémentaires",
    "low": "Faible",
    "mid": "Accueil",
    "high": "Élevé",
    "1080p": "Natif",
    "RecordingFPS": "Enregistrer FPS",
    "bitRate": "Taux de bits vidéo",
    "videoResolution": "Résolution vidéo",
    "startStopRecord": "Démarrer/Arrêter l'enregistrement",
    "instantReplay": "Relecture Instantanée",
    "instantReplayTime": "Durée de lecture immédiate",
    "showIngame": "Lancer le panneau de contrôle en jeu",
    "CaptureMode": "Méthode de capture",
    "gameWindow": "Fenêtre de Jeu",
    "desktopWindow": "Fenêtre de bureau",
    "fileSavePath": "Chemin de stockage des fichiers",
    "selectVideoSavePath": "Sélectionner le Chemin d'Enregistrement",
    "diskFreeSpace": "Espace libre sur le disque dur:",
    "edit": "Modifier",
    "open": "Ouvrir",
    "displayMouse": "Afficher le curseur de la souris",
    "recordMicrophone": "Enregistrer le microphone",
    "gameGraphics": "Affichage Original du Jeu"
  },
  "Setting": {
    "common": "Général",
    "personal": "Personnalisation",
    "messageNotification": "Notifications",
    "sensorReading": "Lectures du Capteur",
    "OLEDscreen": "Protection contre le brûlage OLED",
    "performanceStatistics": "Statistiques de Performance",
    "shortcut": "Raccourci",
    "ingameSetting": "Enregistrer les paramètres du jeu",
    "other": "Autre",
    "otherSettings": "Autres Paramètres",
    "GeneralSetting": "Paramètres Généraux",
    "softwareVersion": "Version du logiciel",
    "checkForUpdates": "Vérifier les mises à jour",
    "updateNow": "Mettre à jour maintenant",
    "currentVersion": "Version Actuelle",
    "latestVersion": "Dernière version",
    "isLatestVersion": "La version actuelle est déjà la dernière.",
    "functionModuleUpdate": "Mise à jour du Module de Fonctionnalités",
    "alwaysUpdateModules": "Maintenir tous les modules fonctionnels installés à la dernière version",
    "lang": "Langue",
    "bootstrap": "Démarrage automatique",
    "powerOn_m1": "Démarrer",
    "powerOn_m2": "Démarrage automatique après secondes",
    "defaultDelay": "40 secondes par défaut",
    "followSystemScale": "Suivre le zoom système",
    "privacySettings": "Paramètres de confidentialité",
    "JoinGamePPPlan": "Rejoignez le programme d'amélioration de l'expérience utilisateur GamePP",
    "personalizedSetting": "Personnalisation",
    "restoreDefault": "Restaurer les paramètres par défaut",
    "color": "Couleur",
    "picture": "Image",
    "video": "Vidéo",
    "browse": "Naviguer",
    "clear": "Effacer",
    "mp4VideoOrPNGImagesCanBeUploaded": "Vous pouvez charger des vidéos MP4 ou des images PNG",
    "transparency": "Transparence",
    "backgroundColor": "Couleur d'arrière-plan",
    "textFont": "Police du texte principal",
    "message": "Message",
    "enableInGameNotifications": "Activer les notifications dans le jeu",
    "messagePosition": "Position d'affichage dans le jeu",
    "leftTop": "Coin supérieur gauche",
    "leftCenter": "Centre gauche",
    "leftBottom": "Coin Inférieur Gauche",
    "rightTop": "Coin supérieur droit",
    "rightCenter": "Centre droit",
    "rightBottom": "Coin inférieur droit",
    "noticeContent": "Contenu de la notification",
    "gameInjection": "Injection de Jeu",
    "ingameShow": "Afficher dans le jeu",
    "inGameMonitoring": "Surveillance en jeu",
    "gameFilter": "Filtre de jeu",
    "start": "Démarrer",
    "endMarkStatistics": "Statistiques de Marque de Fin",
    "readHwinfoFail": "HWINFO Échec de lecture des informations matérielles",
    "dataSaveDesktop": "Les données ont été enregistrées dans le presse-papiers et le fichier du bureau",
    "TheSensorCacheCleared": "Données du cache du capteur effacées",
    "defaultSensor": "Capteur par défaut",
    "setSensor": "Sélectionner le Capteur",
    "refreshTime": "Heure de rafraîchissement des données",
    "recommend": "Par défaut",
    "sensorMsg": "Plus l'intervalle de temps est court, plus la consommation de ressources est élevée. Veuillez choisir avec prudence.",
    "exportData": "Exporter des données",
    "exportHwData": "Exporter les données d'information matérielle",
    "sensorError": "Lecture anormale du capteur",
    "clearCache": "Nettoyer le cache",
    "littleTips": "Remarque : Le rapport de performance sera généré 2 minutes après le lancement du jeu",
    "disableAutoShow": "Désactiver la fenêtre contextuelle automatique des statistiques de performances",
    "AutoClosePopUpWindow_m1": "La fenêtre des statistiques de performance se fermera automatiquement après la période définie :",
    "AutoClosePopUpWindow_m2": "secondes",
    "abnormalShutdownReport": "Rapport d'arrêt anormal",
    "showWeaAndAddress": "Afficher les informations météo et d'emplacement",
    "autoScreenShots": "Capturer automatiquement l'écran du jeu lors du marquage",
    "keepRecent": "Nombre d'enregistrements récents à conserver :",
    "noLimit": "Illimité",
    "enableInGameSettingsSaving": "Activer la Sauvegarde des Paramètres en Jeu",
    "debugMode": "Mode Débogage",
    "enableDisableDebugMode": "Activer/désactiver le mode de débogage",
    "audioCompatibilityMode": "Mode de compatibilité audio",
    "quickClose": "Fermeture rapide",
    "closeTheGameQuickly": "Fermer rapidement le processus du jeu",
    "cancel": "Annuler",
    "confirm": "Confirmer",
    "MoveInterval_m1": "Le suivi du bureau et du jeu se déplacera légèrement :",
    "MoveInterval_m2": "minutes",
    "text3": "Après avoir quitté le jeu, la fenêtre de rapport de performances ne s'affichera pas, seuls les enregistrements historiques seront conservés",
    "text5": "Un rapport sera généré automatiquement après un arrêt imprévu. L'activation de cette fonction consommera des ressources système supplémentaires.",
    "text6": "L'utilisation des raccourcis clavier pourrait entrer en conflit avec d'autres raccourcis de jeux, veuillez configurer avec précaution.",
    "text7": "Définir le raccourci clavier comme \"Aucun\", utilisez la touche Retour arrière",
    "text8": "Conserver l'état des filtres, de la surveillance en jeu et des autres fonctions lors de l'exécution du jeu en fonction du nom du processus",
    "text9": "L'activation enregistrera en continu les logs d'exécution ; la désactivation effacera les fichiers logs (Recommandé de désactiver)",
    "text10": "Après activation, l'accès aux capteurs de la carte mère sera désactivé pour résoudre les problèmes audio causés par GamePP",
    "text11": "Appuyez deux fois sur Alt+F4 pour quitter rapidement le jeu actuel",
    "text12": "Voulez-vous continuer ? Ce mode nécessite le redémarrage de GamePP.",
    "openMainUI": "Afficher l'application",
    "setting": "Paramètres",
    "feedback": "Rétroaction des problèmes",
    "help": "Aide",
    "sensorReadingSetting": "Réglages des lectures des capteurs",
    "searchlanguage": "Langue de recherche"
  },
  "GamePlusOne": {
    "year": "Année",
    "month": "Mois",
    "day": "Jour",
    "success": "Succès",
    "fail": "Échec",
    "will": "Actuel",
    "missedGame": "Jeux manqués",
    "text1": "Montant, env. ￥",
    "text2": "Total des Jeux Réclamés",
    "text3": "Version",
    "gamevalue": "Valeur du Jeu",
    "gamevalue1": "Obtenir",
    "total": "Total Réclamé",
    "text4": "jeux, économie cumulative",
    "text6": "Produit, Valeur",
    "Platformaccountmanagement": "Gestion des comptes de la plateforme",
    "Missed1": "(Non réclamé)",
    "Received2": "(Réception réussie)",
    "Receivedsoon2": "Actuellement disponible",
    "Receivedsoon": "Disponible maintenant",
    "Missed": "Collecte Ratée",
    "Received": "Réclamation réussie",
    "Getaccount": "Obtenir le compte",
    "Worth": "Valeur",
    "Auto": "Automatique",
    "Manual": "Manuel",
    "Pleasechoose": "Sélectionnez un jeu",
    "Receive": "Récupérer maintenant",
    "Selected": "Sélectionné",
    "text5": "Jeux",
    "Automatic": "Récupération automatique en cours...",
    "Collecting": "En cours de récupération...",
    "ReceiveTimes": "Nombre de récupérations ce mois-ci",
    "Thefirst": "n°",
    "Week": "Semaine",
    "weekstotal": "Total de 53 semaines",
    "Return": "Accueil",
    "Solutionto": "Échec de la liaison du compte - Solution",
    "accounts": "Nombre de comptes liés",
    "Addaccount": "Ajouter un compte",
    "Clearcache": "Vider le cache",
    "Bindtime": "Durée de liaison",
    "Status": "État",
    "Normal": "Normal",
    "Invalid": "Invalide",
    "text7": "jeux, économie totale réalisée",
    "Yuan": "Yuan",
    "untie": "Dissocier",
    "disable": "Désactiver",
    "enable": "Activer",
    "gamePlatform": "Plateforme de jeux",
    "goStorePage": "Aller à la page du magasin",
    "receiveEnd": "Après la date limite",
    "loginPlatformAccount": "Compte de plateforme connecté",
    "waitReceive": "En attente de réclamation",
    "receiveSuccess": "Succès de collecte",
    "accountInvalid": "Compte expiré",
    "alreadyOwn": "Déjà Possédé",
    "networkError": "Anomalie Réseau",
    "noGame": "Aucun Jeu de Base",
    "manualReceiveInterrupt": "Interruption de l'Acquisition Manuelle",
    "receiving": "En cours de réclamation",
    "agree": "Je consens à rejoindre le programme de réclamation gratuite GamePP.",
    "again": "Réclamer à nouveau"
  },
  "shutdownTimer": {
    "timedShutdown": "Arrêt programmé",
    "currentTime": "Heure actuelle :",
    "setCountdown": "Configurer le compte à rebours",
    "shutdownInSeconds": "Arrêt dans X secondes",
    "shutdownIn": "Après l'arrêt",
    "goingToBe": "sera",
    "executionPlan": "Plan d'exécution",
    "startTheClock": "Démarrer le chronomètre",
    "stopTheClock": "Annuler le plan",
    "isShuttingDown": "Exécution du plan d'arrêt programmé :",
    "noplan": "Aucun plan d'arrêt actuel",
    "hour": "Heure",
    "min": "Minute",
    "sec": "Seconde",
    "ms": "Milliseconde",
    "year": "Année",
    "month": "Mois",
    "day": "Jour",
    "hours": "Heure"
  },
  "screenshotpage": {
    "screenshot": "Capture d'écran",
    "screenshotFormat": "Conçu spécifiquement pour la capture d'écrans de jeu, il prend en charge l'enregistrement au format JPG/PNG/BMP, permet de capturer rapidement les écrans de jeu, et garantit une sortie en haute résolution sans perte de qualité",
    "Turnon": "Activer capture automatique, toutes les",
    "seconds": "Seconde",
    "takeScreenshot": "Exécuter une capture d'écran automatique",
    "screenshotSettings": "Ce paramètre est sans effet lorsqu'il est coché dans le jeu",
    "saveGameFilterAndMonitoring": "Enregistrer les effets 'Filtre de jeu' et 'Surveillance en jeu' dans la capture d'écran",
    "disableScreenshotSound": "Désactiver la notification sonore des captures d'écran",
    "imageFormat": "Format d'image",
    "recommended": "Recommander",
    "viewingdetails": "Conserve les détails de la qualité d'image, taille modérée, adapté pour visualiser les détails",
    "saveSpace": "Qualité d'image compressible, taille minimale, économie d'espace",
    "ultraQuality": "Image UHD non compressée avec taille de fichier volumineuse. Recommandé pour les joueurs souhaitant conserver la meilleure qualité visuelle des parties.",
    "fileSavePath": "Chemin d'enregistrement du fichier",
    "hardDiskSpace": "Espace libre sur le disque dur:",
    "minutes": "Minute"
  },
  "DesktopMonitoring": {
    "desktopMonitoring": "Surveillance du Bureau",
    "SomeSensors": "Nous vous recommandons certains capteurs pour le suivi. Vous pouvez les supprimer ou en ajouter",
    "AddComponent": "Ajouter un nouveau composant",
    "Type": "Type",
    "Remarks": "Remarque",
    "AssociatedSensor": "Associer un capteur",
    "Operation": "Opération",
    "Return": "Retour",
    "TimeSelection": "Sélection de l'heure:",
    "Format": "Format :",
    "Rule": "Règle：",
    "Coordinate": "Coordonnées :",
    "CustomTextContent": "Contenu de texte personnalisé:",
    "SystemTime": "Heure système",
    "China": "Chine",
    "Britain": "Royaume-Uni",
    "America": "États-Unis",
    "Russia": "Russie",
    "France": "France",
    "DateAndTime": "Date et heure",
    "Time": "Temps",
    "Date": "Date",
    "Week": "Jour de la semaine",
    "DateAndTimeAndWeek": "Date+Heure+Jour de la semaine",
    "TimeAndWeek": "Heure+Jour de la semaine",
    "Hour12": "Format 12 heures",
    "Hour24": "format 24 heures",
    "SelectSensor": "Sélectionner le capteur :",
    "AssociatedSensor1": "Associer le capteur :",
    "SensorUnit": "Unité du capteur：",
    "Second": "Seconde :",
    "Corner": "Coins arrondis:",
    "BackgroundColor": "Couleur d'arrière-plan :",
    "ProgressColor": "Couleur de progression :",
    "Font": "Police：",
    "SelectFont": "Sélectionner la police",
    "FontSize": "Taille de police:",
    "Color": "Couleur：",
    "Style": "Style :",
    "Bold": "Gras",
    "Italic": "Italique",
    "Shadow": "Ombre",
    "ShadowPosition": "Position de l'ombre：",
    "ShadowEffect": "Effets d'ombre：",
    "Blur": "Flou",
    "ShadowColor": "Couleur d'ombre：",
    "SelectFromLocalFiles": "Sélectionner à partir des fichiers locaux :",
    "UploadImageVideo": "Télécharger des images/Vidéos",
    "UploadSVGFile": "Télécharger un fichier SVG",
    "Width": "Largeur:",
    "Height": "Haut: ",
    "Effect": "Effet:",
    "Rotation": "Rotation :",
    "WhenTheSensorValue": "La valeur du capteur est supérieure à",
    "conditions": "Quand la condition n'est pas remplie (aucune rotation lorsque la valeur du capteur est 0)",
    "Clockwise": "Dans le sens horaire",
    "Counterclockwise": "Antihoraire",
    "QuickRotation": "Rotation rapide",
    "SlowRotation": "Rotation lente",
    "StopRotation": "Arrêter la rotation",
    "StrokeColor": "Couleur de contour：",
    "Path": "Chemin",
    "Color1": "Couleur",
    "ChangeColor": "Changer de couleur",
    "When": "Quand",
    "SensorValue": "Valeur du capteur supérieure ou égale à",
    "SensorValue1": "Valeur du capteur inférieure ou égale à",
    "SensorValue2": "La valeur du capteur est égale",
    "MonitoringSettings": "Paramètres de surveillance",
    "RestoreDefault": "Restaurer les paramètres par défaut",
    "Monitor": "Moniteur",
    "AreaSize": "Taille de la zone",
    "Background": "Arrière-plan",
    "ImageVideo": "Images/Vidéos",
    "PureColor": "Couleur unie",
    "Select": "Sélectionner",
    "ImageVideoDisplayMode": "Mode d'affichage des images/vidéos",
    "Transparency": "Transparence",
    "DisplayPosition": "Afficher la position",
    "Stretch": "Étirer",
    "Fill": "Remplir",
    "Adapt": "Adapter",
    "SelectThePosition": "Cliquez sur la cellule pour sélectionner rapidement l'emplacement",
    "CurrentPosition": "Emplacement actuel :",
    "DragLock": "Verrouillage par glissement",
    "LockMonitoringPosition": "Position du moniteur bloquée (Impossible de faire glisser le moniteur après le blocage)",
    "Unlockinterior": "Activer le glissement des éléments internes",
    "Font1": "Police",
    "GameSettings": "Paramètres du jeu",
    "CloseDesktopMonitor": "Désactiver automatiquement le suivi du bureau lors de l'exécution du jeu.",
    "OLED": "Protection OLED Burn-in",
    "Display": "Afficher",
    "PleaseEnterContent": "Veuillez entrer du contenu",
    "NextStep": "Suivant",
    "Add": "Ajouter",
    "StylesForYou": "Nous vous recommandons certains styles de surveillance. Vous pouvez les sélectionner et les appliquer. D'autres styles seront ajoutés ultérieurement.",
    "EditPlan": "Modifier le profil",
    "MonitoringStylePlan": "Schéma de style de surveillance",
    "AddDesktopMonitoring": "Ajouter la surveillance du bureau",
    "TextLabel": "Étiquette de texte",
    "ImageVideo1": "Images, Vidéos",
    "SensorGraphics": "Graphiques des capteurs",
    "SensorData": "Données du capteur",
    "CustomText": "Texte personnalisé",
    "DateTime": "Date et heure",
    "Image": "Image",
    "Video": "Vidéo",
    "SVG": "SVG",
    "ProgressBar": "Barre de progression",
    "Graphics": "Graphismes",
    "UploadImage": "Télécharger l'image",
    "UploadVideo": "Télécharger la vidéo",
    "RealTimeMonitoring": "Surveillance en temps réel des températures et utilisations de la CPU/GPU, personnalisation de l'agencement par glisser-déposer, paramétrage du style personnalisé – vous maîtrisez performances et esthétique du bureau",
    "Chart": "Graphique",
    "Zigzagcolor": "Couleur de la ligne (Début)",
    "Zigzagcolor1": "Couleur de la ligne (Point final)",
    "Zigzagcolor2": "Couleur de la zone du graphique en ligne (Début)",
    "Zigzagcolor3": "Couleur de la zone dans le graphique en ligne (fin)",
    "CustomMonitoring": "Surveillance personnalisée"
  }
}
//messageEnd 
 export default fr 