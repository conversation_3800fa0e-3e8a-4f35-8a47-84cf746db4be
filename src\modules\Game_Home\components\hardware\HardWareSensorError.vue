<template>
  <div class="HardWareError-container">
    <el-collapse v-model="activeCollapse_error" @change="emitChangeFn">
      <el-collapse-item :title="$t('hardwareInfo.hardwareOverview')" name="1" class="as">
        <template #title>
          <div class="hardware-all-l2-title flex-items-center">
            <span class="iconfont icon-sensor" style="color: #3579D5;font-size: 24px;margin-right: 5px;"></span>
            <span style="color: #ffffff">{{$t('hardwareInfo.SensorData')}}</span>

            <span class="ml-auto"></span>
          </div>
        </template>
        <div class="errBox">
          <el-icon :size="30" color="#BF4040">
            <Warning />
          </el-icon>
          <span>{{$t('hardwareInfo.CannotGetSensorData')}}</span>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup lang="ts">
import {defineProps, onMounted, ref} from "vue";
import {Warning} from "@element-plus/icons-vue";

const activeCollapse_error = ref(["1"])
const props = defineProps({
  changeMenuInfo: {
    type: Function,
    required: true
  }
})

onMounted(() => {
  setTimeout(()=>{
    emitChangeFn();
  },200)
})

const emitChangeFn = () => {
  const h = activeCollapse_error.value.length > 0 ? 300 : 72
  props.changeMenuInfo(1, h);
}

</script>

<style scoped lang="scss">
.HardWareError-container {
  width: 100%;
  background: rgba(45 ,46 ,57, 0.8);
  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  padding: 0 25px 0 20px;
  font-size: 12px;

  .hardware-all-l2-title {
    width: 100%;
  }

  .errBox {
    width: 560px;
    height: 210px;
    background: #22232E;
    box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.3);
    border-radius: 4px;
    display: flex;
    flex-flow: column nowrap;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    span {
      display: block;
      text-align: center;
      margin-top: 20px;
      font-weight: 400;
      font-size: 12px;
      color: #BF4040;
      line-height: 20px;
    }
  }
}

.child-space-right-10 {
  & > * {
    margin-right: 10px;
  }
}

.child-space-right-5 {
  & > * {
    margin-right: 5px;
  }
}

.color777 {
  color: #777777;
}
</style>

<style>
.HardWareError-container{
    .el-button {
        border: 2px solid #3579D5;
        color: #3579D5;
        background: transparent;
        &:hover {
            background-color: #3579D5;
            border-color: #3579D5;
            color: #ffffff;
        }
    }
}
</style>
