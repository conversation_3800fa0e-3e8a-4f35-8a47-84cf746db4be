
// import fs from "fs";
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path, {resolve} from 'path'
import compression from 'vite-plugin-compression' //gzip/br 压缩
import AutoImport from 'unplugin-auto-import/vite' // 自动引入
import Components from 'unplugin-vue-components/vite' // 组件自动引入
import Icons from 'unplugin-icons/vite'
import IconsResolver from 'unplugin-icons/resolver'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

// import obfuscator from 'rollup-plugin-obfuscator';
// import chalk from 'chalk' // console高亮
import  fs from 'fs';

// 获取npm run dev后缀 配置的环境变量
const npm_config_page = process.env.npm_config_page || ''
const npm_config_base_folder = process.env.npm_config_base_folder || '';
// 命令行报错提示
// const errorLog = (error: string) => console.log(chalk.red(`${error}`))


//获取指定的单页面入口
const getEnterPages = () => {
  if (!npm_config_page) {
    // errorLog(
    //     '⚠️ 警告 -- 请在命令行后以 `--page=页面名称` 格式指定页面名称！'
    // );
    return null;
  }

  // 尝试构造两个可能的路径
  const modulesPath = path.resolve(__dirname, `src/modules/${npm_config_page}/index.html`);
  const ingamePath = path.resolve(__dirname, `src/inGame/${npm_config_page}/index.html`);
  const DeskPath = path.resolve(__dirname, `src/onDesk/${npm_config_page}/index.html`);

  // 检查哪个路径存在
  if (fs.existsSync(modulesPath)) {
    return {
      [npm_config_page]: modulesPath
    };
  } else if (fs.existsSync(ingamePath)) {
    return {
      [npm_config_page]: ingamePath
    };
  }  else if (fs.existsSync(DeskPath)) {
    return {
      [npm_config_page]: DeskPath
    };
  } else {
    // errorLog(`页面 ${npm_config_page} 既不存在于 modules 文件夹，也不存在于 ingame 文件夹中！`);
    // return null;
  }
};

const getPageRoot = (pageName: string) => {
  // 尝试构造两个可能的路径
  const modulesPath = path.resolve(__dirname, `src/modules/${npm_config_page}/index.html`);
  const ingamePath = path.resolve(__dirname, `src/inGame/${npm_config_page}/index.html`);
  const DeskPath = path.resolve(__dirname, `src/onDesk/${npm_config_page}/index.html`);
  // 假设页面名称格式为ingame"，通过名称前缀区分
  if (fs.existsSync(ingamePath))
  {
    return path.resolve(__dirname, `./src/inGame/${pageName}`);
  }
  else if (fs.existsSync(modulesPath))
  {
    // 默认或未明确标识为模块的页面视为moduleName
    return path.resolve(__dirname, `./src/modules/${pageName}`);
  }
  else if(fs.existsSync(DeskPath))
  {
    return path.resolve(__dirname, `./src/onDesk/${pageName}`);
  }
  else {
    // errorLog(`页面 ${pageName} 既不存在于 modules 文件夹，也不存在于 ingame 文件夹中！`);
    return null;
  }
};

// 打包提示
// const buildEndFn = (name:any)=>{
//   console.log(`🚀🚀🚀 ${chalk.green.bold('项目构建')} ➡️   ${chalk.white.bgGreen.bold(` ${name} `)} 🇨🇳`);
// }

export default defineConfig({
  root:getPageRoot(process.env.npm_config_page || ''),
  base: './',
  envDir: path.resolve(__dirname), //用于加载 .env 文件的目录。可以是一个绝对路径，也可以是相对于项目根的路径。
  plugins: [
    vue(),
    AutoImport({
      // 自动导入 Vue 相关函数，如：ref, reactive, toRef 等
      imports: ['vue', 'vue-router'],
      dts: path.resolve(__dirname, './auto-import.d.ts'),
      eslintrc: {
        enabled: false, // 是否自动生成 eslint 规则，建议生成之后设置 false
        filepath: path.resolve(__dirname, './.eslintrc-auto-import.json'), // 指定自动导入函数 eslint 规则的文件
        globalsPropValue: true,
      },
      resolvers: [
        IconsResolver({ prefix: 'Icon', }), // 自动导入图标组件
        ElementPlusResolver() // // 自动导入 Element Plus 相关函数，如：ElMessage, ElMessageBox...
      ],
      // vueTemplate: true,
      // dts: false, // 配置文件生成位置(false:关闭自动生成) 关闭dev时自动生成components.d.ts
      // dts: path.resolve(pathSrc, "types", "auto-imports.d.ts"), // 指定自动导入函数TS类型声明文件路径
    }),
    Components({
      resolvers: [
        IconsResolver({ enabledCollections: ['ep'], }), // 自动注册图标组件
        ElementPlusResolver() // 自动导入 Element Plus 组件
      ],
      dirs: ['src/**/components'], // 指定自定义组件位置(默认:src/components)
      dts: false, // 配置文件位置(false:关闭自动生成)
      // dts: path.resolve(pathSrc, "types", "components.d.ts"), // 指定自动导入组件TS类型声明文件路径
    }),
    Icons({
      autoInstall: true,
    }),
    // gzip格式
    // compression({
    //   threshold: 1024 * 1000, // 体积大于 threshold 才会被压缩,单位 b
    //   ext: '.gz', // 压缩文件格式
    //   deleteOriginFile: true // 是否删除源文件
    // }),
  ],
  css:{
    preprocessorOptions: {
      scss: {
      }
    }
  },
  resolve: {
    alias: {
      '@': path.join(__dirname, './src'),
      '@modules': path.join(__dirname, './src/modules')
    }
  },
  server: {
    host: 'localhost', // 指定服务器主机名
    port: 8080, // 指定服务器端口
    hmr: true,  // 开启热更新
    open: true, // 在服务器启动时自动在浏览器中打开应用程序
    https: false // 是否开启 https
  },
  build: {
    sourcemap: true,//开发调试时使用打印对应文件行数，提交打包注销此行
    outDir: path.resolve(__dirname, `webapp/windows/pages/${npm_config_page}`), // 指定输出路径
    assetsInlineLimit: 4096, //小于此阈值的导入或引用资源将内联为 base64 编码，以避免额外的 http 请求
    emptyOutDir: true, //Vite 会在构建时清空该目录
    cssCodeSplit:false,
    terserOptions: {
      compress: {
        keep_infinity: true, // 防止 Infinity 被压缩成 1/0，这可能会导致 Chrome 上的性能问题
        drop_console: true, // 生产环境去除 console
        drop_debugger: true, // 生产环境去除 debugger
      },
      format: {
        comments: false, // 删除注释
      },
    },
    rollupOptions: {  //自定义底层的 Rollup 打包配置
      input: getEnterPages(),
      // buildEnd: buildEndFn(npm_config_page),
      output: {
        assetFileNames: (assetInfo) => {
          console.log('assetsInfo:',assetInfo);
          const { name } = assetInfo;
          // 如果是 CSS 文件，则使用特定的命名规则
          if (name.endsWith('.css')) {
            if (npm_config_page) {
              return `css/${npm_config_page}.css`;
            }
            return '[ext]/[name].css';
          }
          // 对于其他类型的资产，使用默认规则
          return '[ext]/[name].[ext]';
        }, //静态文件输出的文件夹名称
        // chunkFileNames: 'js/[name].js',  //chunk包输出的文件夹名称
        // cssFileName:'css/[name].css',
        entryFileNames: 'js/[name].js',  //入口文件输出的文件夹名称
        compact: true,
        // manualChunks: (id) => {
        //   if (id.includes('node_modules')) {
        //     return id
        //         .toString()
        //         .split('node_modules/')[1]
        //         .split('/')[0]
        //         .toString() // 拆分多个vendors
        //   }
        // }
      }
    },
  }
})
