<script setup lang="ts">
import { ref,watch,computed,onBeforeMount,onUnmounted,onMounted, watchEffect } from 'vue';
import sideBar from '@/components/mainCom/sideBar.vue'

import GameMirror from '@/modules/Game_Mirror/GameMirror.vue';

import { gameppBaseSetting , getInGameList, saveInGameList} from './stores';
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import { gamepp } from 'gamepp'
// import { defineStore } from "pinia"
import emitter from '../../uitls/emitter'
import {QuestionFilled, Setting, SwitchButton, User,ChatLineSquare} from "@element-plus/icons-vue";
import { useZoomStore } from './stores/zoomStore';
import { el } from 'element-plus/es/locale';
import {SensorData} from "@/modules/Game_Home/types/InGameMonitor";
import Feedback from "@/modules/Game_Home/components/feedback.vue";
import NotInjectTip from "@/modules/Game_Home/components/NotInjectTip.vue";
import RightTopIcons from "@/components/mainCom/RightTopIcons.vue";

const version = ref(0)
const gameppConfig = gameppBaseSetting()
const videoElement = ref(null);
const I18n = useI18n();
const router = useRouter()
let { locale } = I18n;
const settings = ref([ {name:'最小化',icon: "icon-minimize"},{name:'最大化', icon: "icon-Maximize"},{name:'还原', icon: "icon-normalize"},{name:'退出',icon:"icon-Close"},])
const settingsIngame = ref([{name:'退出',icon:"icon-Close"},])
let   fullScreen = ref(false)
const showIframe = ref(false);
const homeiframeSrc = ref('https://client-v3.gamepp.com/account/user.html')
const codeslayershow = ref(false)
const iframeUserRef:any =  ref(null);
const LoginLogout = ref(false)
const zoomStore = useZoomStore();
let home_opacity  =  ref(1)
const ingame_now = ref(false)
let dragChunk    =    ref(false)
const InGameList = ref<SensorData[][]>([]);
// let InGameList = ref<SensorData[][]>([[
//   {name: 'FPS',des: 'FPS', show: true, color:'rgba(56, 255, 152, 1)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: true,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: false, title: 'FPS', unit:'',textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgba(56, 255, 152, 1)'},
//   {name: 'FPS 1% Low',des: '1%Low帧',bg: '', show: true, color:'rgba(56, 255, 152, 1)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: true,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: false,title: 'FPS 1% Low', unit:'',textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)'},
//   {name: 'FPS 0.1% Low',des: '0.1%Low帧',bg: 'FPS 0.1% Low', show: false, color:'rgba(56, 255, 152, 1)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: true,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: true,title: 'FPS 0.1% Low', unit:'',textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)'},
//   {name: 'Frame Time',des: '帧生成时间',bg: 'Frame Time', show: true, color:'rgba(56, 255, 152, 1)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: true,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: false,title: '', unit:'',textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)'},
//   {name: 'Current time',des: '当前时间',bg: 'Current time', show: false, color:'rgb(255,255,255)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: true,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: true,title: '', unit:'',textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)'},
//   {name: 'Run time',des: '运行时长',bg: 'Run time', show: false, color:'rgb(255,255,255)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: true,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: true,title: '', unit:'',textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)'},
//   {name: 'CPU Temp',des: 'CPU温度', bg: 'temp', show: true, color:'rgba(87, 172, 255, 1)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: false,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: false, title: 'CPU', unit:'℃' , key: 0,textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)'},
//   {name: 'CPU Usage',des: 'CPU占用',bg: 'usage', show: true, color:'rgba(87, 172, 255, 1)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: false,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: false, title: 'CPU', unit:'%',key: 6,textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)'},
//   {name: 'CPU Clock',des: 'CPU频率',bg: 'clock', show: true, color:'rgba(87, 172, 255, 1)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: false,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: false, title: 'CPU', unit:'MHz',key: 3,textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)'},
//   {name: 'CPU Power',des: 'CPU热功耗',bg: 'power', show: false, color:'rgba(87, 172, 255, 1)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: false,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: true, title: 'CPU', unit:'W',key: 12,textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)'},
//   {name: 'GPU Temp',des: 'GPU温度',bg: 'temp', show: true, color:'rgba(255, 177, 60, 1)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: false,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: false, title: 'GPU',unit:'℃', key: 0,textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)'},
//   {name: 'GPU Usage',des: 'GPU占用率',bg: 'total_usage', show: true, color:'rgba(255, 177, 60, 1)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: false,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: false, title: 'GPU', unit:'%', key: 4,textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)'},
//   {name: 'GPU Power',des: 'GPU热功耗',bg: 'power', show: false, color:'rgba(255, 177, 60, 1)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: false,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: true, title: 'GPU', unit:'MHz', key: 2,textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)'},
//   {name: 'GPU Clock',des: 'GPU频率',bg: 'clock', show: false, color:'rgba(255, 177, 60, 1)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: false,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: true, title: 'GPU', unit:'W', key: 1,textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)'}
// ]])
const windowInfo =  ref({
  "desktop":{width:1440,height:810},

  "game_mirror":{width:1280,height:720},
  "game_rebound":{width:1280,height:720},
  "rebound_details_v2":{width:1250,height:750},
});
const setIntervalUserRefresh:any = ref<NodeJS.Timer | null>(null);
// const ToggleiframeSrc =ref('../Game_Mirror/index.html')
// const showToggleIframe = ref(true)
const showGameMirror = ref(false)
const moduleInfo = ref([]);
const isGameMirrorInstalled = ref(false);
const activeMode = ref('');
const showFeedBack = ref(false)

const loadModuleInfo = () => {
  const moduleInfoString = localStorage.getItem('moduleInfosetting');
  if (moduleInfoString) {
    try {
      moduleInfo.value = JSON.parse(moduleInfoString);
      isGameMirrorInstalled.value = moduleInfo.value.find((item: { name: string}) => item.name === "游戏滤镜")?.download ?.installed ?? false;
      // isGameMirrorInstalled.value = true
    } catch (error) {
    }
  }
};

window.addEventListener('storage', (event) => {
  if (event.key === 'moduleInfosetting') {
    loadModuleInfo();
  }
});

watch(isGameMirrorInstalled, (newValue, oldValue) => {
  if (newValue === false && oldValue === true) {
    handleCustommode();
  }
});

watch(() => zoomStore.zoomLevel,(newZoomLevel:any) => {
    document.body.style.zoom = newZoomLevel
  }
);

// 测试隐藏显示嵌套的个人中心
watchEffect(() => {
  if ( router.currentRoute.value.path === '/HardWare' || router.currentRoute.value.path === '/' || router.currentRoute.value.path === '/InGameMonitor') {
    showIframe.value = false;
  }
});


onBeforeMount(() =>
{

})

//////////////////////////不要在周期函数里面写代码 写方法！！！！/////////////////////////////////
onMounted(async() =>
{

  try{
    createListener() //监听器
    await init()
    loadModuleInfo();
    getWindowSize()
    setTimeout(() => {
      getdpi()
    }, 1000);
    if(!ingame_now.value)
    {
      initial_Zoom()
    }
    await gamepp.user.refreshUserToken.promise(true);
    if(ingame_now.value)
    {
      dragChunk.value = !dragChunk.value
      setTimeout(() => {
        dragChunk.value = !dragChunk.value
      }, 1000);
    }
    const currentPath = router.currentRoute.value.path;
    if (currentPath === '/Game_Mirror')
    {
      activeMode.value = 'mirror';
    }
    else if (currentPath === '/InGameMonitor')
    {
      activeMode.value = 'custom';
    }
  }catch{}
  // 清理监听器
  return () => {
    window.removeEventListener('storage', loadModuleInfo);
  };
})

// 在组件卸载时解绑事件
onUnmounted(()=>{
  emitter.off('Active-UserName')
})

//dpi缩放窗口居中
const getdpi = async () => {
  const dpidata = await gamepp.display.getScaleFromWindowInMonitor.promise();
  let DisplayBounds = gamepp.getPrimaryDisplayBounds.sync();//当前屏幕宽高
  let PresentBounds = gamepp.webapp.windows.getBounds.sync('desktop');//获取窗口大小
  let halfWidth = Math.floor(Number(DisplayBounds['width'] / 2 - PresentBounds['width'] / 2));
  let halfHeight = Math.floor(Number(DisplayBounds['height'] / 2 - PresentBounds['height'] / 2));
  if (dpidata > 1) {
    console.log('dpidata',dpidata);
    gamepp.webapp.windows.setPosition.sync("desktop", halfWidth, halfHeight);
  }
}


function listenDomClick ():void
{
  document.addEventListener('click', (event:any) =>
  { // 监听整个DOM的点击事件
    console.log('event',event);
    const target:HTMLElement = event.target;
    console.log('event.target',target);
    console.log('event.target.innerText',target.innerText);
    if (target.innerText) {
      // ...
    }
  });
}

const  handleGameMirror = async() =>
{
  showGameMirror.value = true
  activeMode.value = 'mirror';
  router.push('/Game_Mirror');
}

const handleCustommode = async() =>
{
  showGameMirror.value = false
  activeMode.value = 'custom';
  router.push('/InGameMonitor');
}


const createListener = () =>
{
  gamepp.webapp.windows.onWindowWillMaximize.addEventListener( () => {
    console.log('onWindowWillMaximize');
    fullScreen.value = true
  })

  gamepp.webapp.windows.onWindowWillUnmaximize.addEventListener( () => {
    console.log('onWindowWillUnmaximize');
    fullScreen.value = false
  })
  gamepp.webapp.onInternalAppEvent.addEventListener(async value => {
      console.log('value',value)
      if(value === 'changeOpcity')
      {
        home_opacity.value = 0.9
      }

      if(value === 'closeLogin')
      {
        showIframe.value = false;
        LoginLogout.value = true;
        if (setIntervalUserRefresh.value) {
          clearInterval(setIntervalUserRefresh.value);
          setIntervalUserRefresh.value = null;
        }
       //删除Cookie
        await gamepp.webapp.windows.cleanCookie.promise('desktop');
      }
      if( value == 'frameClose')
      {
        codeslayershow.value = false
      }
      try
      {
        if(value['action'] == 'LoginSuccessful')
      {
        showIframe.value = false;
        codeslayershow.value = false
        LoginLogout.value = false
      }
      if(value['action'] == 'closeUserCenter')
      {
        showIframe.value = false;
      }
      if(value['action'] == 'LogBackIn')
      {
        showIframe.value = false;
      }
      if(value['action'] == 'layercodes')
      {
        codeslayershow.value = true
      }
      if(value['action'] == 'closelayercodes')
      {
        codeslayershow.value = false
      }
      if(value['action'] == 'RegisterBindOpenid')
      {
          console.log(value)
          iframeUserRef.value.contentWindow.postMessage(value, '*');
      }
      if(value['action'] == 'showFeedback')
      {
          onClickDropMenu('feedback')
      }
      }catch
      {

      }

    })
}

const initial_Zoom = async () =>
{
    const initialZoom = await gamepp.display.getScaleFromWindowInMonitor.promise();
    zoomStore.setZoomLevel(initialZoom);
    setWindowMinSize('desktop', Math.round(1440 * initialZoom), Math.floor(810 * initialZoom));

    if(initialZoom >= 1)
    {
      setTimeout(() => {
        getWindowSize()
      }, 800);
    }

    gamepp.display.onDisplayMetricsChanged.addEventListener( async (ZoomLevel:number)=>
    {
      console.log('display',ZoomLevel)
      zoomStore.setZoomLevel(ZoomLevel);
      setWindowMinSize('desktop', Math.floor(1440 * ZoomLevel), Math.floor(810 * ZoomLevel))
    })

    const data_arr = await gamepp.user.loadBaseInfo.promise();
    if (data_arr['online'] === -1)
    {
      LoginLogout.value = true
    }
    else
    {
      LoginLogout.value = false
    }
    document.body.style.zoom = initialZoom
}

const setWindowMinSize = (windowname:string,width:number,height:number) => //更改窗口最小尺寸
{
	   console.log('width',width,'height',height)
     gamepp.webapp.windows.setMinimumSize.sync(windowname, width, height)
     gamepp.webapp.windows.resize.sync('desktop', 1440 , 810)
}

const handleLogout = async () =>
{
  LoginLogout.value = true;
  emitter.emit('Setup-Logout',false)
  if (setIntervalUserRefresh.value)
  {
    clearInterval(setIntervalUserRefresh.value);
    setIntervalUserRefresh.value = null;
  }
  //删除Cookie
  await gamepp.webapp.windows.cleanCookie.promise('desktop');

};

const init = async () =>
{
  console.log(gamepp.webapp.windows.getTitle.sync());
  if(gamepp.webapp.windows.getTitle.sync() === '游戏加加游戏内主窗口')
  {
    ingame_now.value = true
    // ingameStore.setIngameNow(true);
    console.warn('进入游戏内主窗口');
    router.push({
      path:'/InGameMonitor'
  })
  }
  else
  {
    console.warn('进入主页');
    router.push({
      path:'/'
  })

  }
  if(!ingame_now.value)
  {
    window.addEventListener('resize', getWindowSize);
  }
  version.value = gamepp.getPlatformVersion.sync()
  if ( gamepp.user.getUserID.sync() !== 0 &&   gamepp.user.getUserID.sync() !== -1) {
    setIntervalUserRefresh.value = setInterval(async () => {
      await gamepp.user.refreshUserToken.promise(false);
    }, 3600000); // 每1小时刷新一次
  }

  if(gamepp.webapp.windows.isValid.sync('ingame_main'))
  {
    await gamepp.webapp.sendInternalAppEvent.promise('ingame_main', 'changeOpcity');
  }
  InGameList.value = getInGameList();
  if(!localStorage.getItem('InGameList')) {
    // localStorage.setItem('InGameList', JSON.stringify(InGameList.value))
    InGameList.value = getInGameList();
    let ingameListNameNumber = 1;
    localStorage.setItem('ingameListNameNumber', ingameListNameNumber.toString());
  }
}


const getWindowSize = () =>

{
      if(!ingame_now.value)
      {
        gameppConfig.gameppWidth = window.innerWidth
        gameppConfig.gameppHeight = window.innerHeight
      }
};

const ingameContainerStyle = computed(() =>
{
  try{
    return {
          width: `1273px`,
          height: `810px`,
          backgroundColor: `transparent`,
          // opacity:home_opacity.value
        };
  }catch{

  }
});

const gameContainerStyle = computed(() =>
{
  try
  {
    let bgColor = gameppConfig.gameppBgColor
    if (gameppConfig.bgOpacity2)
    {
      bgColor = updateColorWithOpacity(bgColor,1)
    }
    return {
          width: `${gameppConfig.gameppWidth / zoomStore.zoomLevel}px`,
          height: `${gameppConfig.gameppHeight / zoomStore.zoomLevel }px`,
          backgroundColor: `${bgColor}`,
        };
  }
  catch
  {

  }
});
function updateColorWithOpacity(color:string, opacity:number)
{
    // 移除可能存在的前导'#'
    color = color.replace(/^#/, '');

    const alpha = Math.round(opacity * 255).toString(16).padStart(2, '0');
    // 检查颜色格式
    if (color.length === 6) { // 16进制RGB
        return `#${color}${alpha}`;
    } else if (color.length === 8) { // 16进制RGBA
        return `#${color.slice(0, 6)}${alpha}`;
    } else if (color.startsWith('rgba')) { // CSS RGBA
        const match = color.match(/rgba$(\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3}),\s*[\d.]+$/);
        if (match) {
            const [r, g, b] = match.slice(1, 4).map(Number);
            return `rgba(${r}, ${g}, ${b}, ${opacity})`;
        }else{
          return '#22232e'; // 默认颜色
        }
    } else if (color.startsWith('rgb')) { // CSS RGB
        const match = color.match(/rgb$(\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})$/);
        if (match) {
            const [r, g, b] = match.slice(1, 4).map(Number);
            return `rgba(${r}, ${g}, ${b}, ${opacity})`;
        }else{
          return '#22232e'; // 默认颜色
        }
    } else {
        return '#22232e'; // 默认颜色
    }
}

const gameContainerStyle2 = computed(() =>
{
  try
  {
    return {width: `${(gameppConfig.gameppWidth - (160 * zoomStore.zoomLevel) - 7) / zoomStore.zoomLevel}px`,};
  }
  catch
  {

  }
});

async function setting(index:any)
{
  if(index == 0)
  { // 最小化
    await gamepp.webapp.windows.minimize.promise('desktop');
  }
  else if(index == 1)
  { // 最大化
    fullScreen.value = true
    await gamepp.webapp.windows.maximize.promise('desktop');

    setTimeout(() =>
    {
      gameppConfig.gameppWidth = window.innerWidth
      gameppConfig.gameppHeight = window.innerHeight
    }, 100);
  }
  else if(index == 2)
  { // 还原
    fullScreen.value = false
    await gamepp.webapp.windows.unmaximize.promise('desktop');
  }else if (index == 3)
  {
    await gamepp.webapp.windows.close.promise('desktop')
  }
  console.warn(index);
}

function settingShow(index:number)
{
  if (index === 2)
  {
    return fullScreen.value
  }else if (index === 1)
  {
    return !fullScreen.value
  }else
  {
    return true
  }
}

async function settingIngame(index:any)
{
  if(index == 0)
  {
    await gamepp.webapp.windows.close.promise('ingame_main')
  }
  console.warn(index);
}

function settingShowIngame(index:number)
{
  if (index == 0)
  {
    return true
  }
}

async function onClickDropMenu(action:string)
{
  // switch(action)
  // {
  //   case 'setting':
  //   await gamepp.webapp.windows.show.sync('gamepp_config','desktop');
  //   break;
  // }
  if (action === 'setting')
  {
    await gamepp.webapp.windows.show.sync('gamepp_config','desktop');
  }
  else if (action === 'help')
  {
    GPP_OpenURL('https://gamepp.com/support.html')
  }
  else if (action === 'quit')
  {
    // let Obj = {action:'ExitGamePP'};
    // await gamepp.webapp.sendInternalAppEvent.promise('background', Obj);
    await gamepp.lansevice.stop.promise()
    await gamepp.exit.promise();
  }
  else if (action === 'feedback')
  {
    showFeedBack.value = true
  }
}
/**
 * 外部浏览器打开URL
 * @param url
 */
function GPP_OpenURL(url:string) {
  try {
    gamepp.shell.openExternal(url);
  } catch (error) {
    window.open(url)
  }
}

emitter.on('Active-UserName',(value:boolean)=>{
  const randomNum = Math.random();
  homeiframeSrc.value = `https://client-v3.gamepp.com/account/user.html?timestamp=${randomNum}`;
  showIframe.value = value
})

let InMax = ref(false)



const checkInMax = () =>
{

}
</script>
<template>
  <!-- <div class="container"   :class="[fullScreen?'InMaxBoxShadow':'NotInMaxBoxShadow']" :style="ingame_now?ingameContainerStyle:gameContainerStyle"> -->
    <div class="container InMaxBoxShadow"  :style="ingame_now?ingameContainerStyle:gameContainerStyle">
    <div class="dragChunk" v-show="dragChunk" style="width: 1px;height: 1px;position: absolute;-webkit-app-region: drag;"></div>
    <!-- 扫码遮罩层 -->
    <div class="codeslayer" v-if="codeslayershow"></div>
    <!-- 背景视频 -->
    <video v-if="gameppConfig.gameppVideo != '' && gameppConfig.colorOrImg == 1" ref="videoElement" :style="{opacity:gameppConfig.bgOpacity}" :src="gameppConfig.gameppVideo" autoplay loop muted></video>
    <!-- 背景图片 -->
    <img   v-if="gameppConfig.gameppImg != '' && gameppConfig.colorOrImg == 1" class="bgImg" :style="{opacity:gameppConfig.bgOpacity}" :src="gameppConfig.gameppImg" alt="">
      <!-- 左侧导航栏 -->
      <!-- <sideBar></sideBar> -->
      <sideBar v-show="!ingame_now"></sideBar>
      <!-- 右侧路由 -->
      <div class="right" :style="ingame_now?'':gameContainerStyle2"  :class="{ 'ingame-class': ingame_now }">

        <iframe class="iframeuserbar"  v-if="showIframe" ref="iframeUserRef"  :src="homeiframeSrc" frameborder="0" width="100%"  scrolling="auto"></iframe>
        <header class="nav" v-show="!ingame_now">
        <!-- <div class="drag-bar"></div> -->
        <div class="setting">
          <p  style="color:#FFFFFF;font-size: 12px;margin-right: 20px;text-wrap: nowrap;"><span>V</span>{{version}}</p>
          <el-dropdown trigger="click" popper-class="setting-dropdown" v-if="!ingame_now">
            <div class="item">
              <span class="iconfont icon-parameter"></span>
            </div>
            <template #dropdown>
              <el-dropdown-menu :append-to-body="true">
                <el-dropdown-item @click="onClickDropMenu('setting')"><span class="iconfont icon-option"></span>设置</el-dropdown-item>
                <el-dropdown-item @click="onClickDropMenu('help')"><span class="iconfont icon-quest"></span>帮助</el-dropdown-item>
                <el-dropdown-item @click="onClickDropMenu('feedback')"><span class="iconfont icon-feedback"></span>问题反馈</el-dropdown-item>
                <el-dropdown-item v-if="LoginLogout" @click="emitter.emit('Setup-Login');"><span class="iconfont icon-user"></span>登录</el-dropdown-item>
                <el-dropdown-item v-else   @click="handleLogout"><span class="iconfont icon-user"></span>退出登录</el-dropdown-item>
                <el-dropdown-item @click="onClickDropMenu('quit')"><span class="iconfont icon-quit"></span>退出</el-dropdown-item>
              </el-dropdown-menu>

            </template>
          </el-dropdown>
          <RightTopIcons
              close-icon
              maximize-icon
              minimize-icon
              :is-full-screen="fullScreen"
              @close="setting(3)"
              @maximize="setting(1)"
              @minimize="setting(0)"
              @unmaximize="setting(2)"
          />
          <!--<div class="item" v-for="(item,index) in settings" :key="index" @click="setting(index)"-->
          <!--     :class="{'last':item.icon == 'icon-Close'}"-->
          <!--      v-show="settingShow(index)">-->
          <!--    <span :class="['iconfont',item.icon,{'last-child': item.icon == 'icon-Close'}]"></span>-->
          <!--  </div>-->
          </div>
        </header>
        <header class="nav2 ingamenav" v-show="ingame_now">
          <div class="ingamenav_box">
            <div class="nav_left">
              <img src="../../components/assets/img/gamepp.png" alt="">
              <p class="ingameversion">{{version}}</p>
            </div>

            <div class="nav_right">
              <p>按下 Ctrl+Tab 显示/隐藏窗口</p>
            </div>
          </div>
          <div class="item" v-for="(item,index) in settingsIngame" :key="index" @click="settingIngame(index)"
               :class="{'last':item.icon == 'icon-Close'}"
              v-show="settingShowIngame(index)">
            <span :class="['iconfont',item.icon,{'last-child': item.icon == 'icon-Close'}]"></span>
          </div>
        </header>
        <div class="Switchlayer_box" v-show="ingame_now">
          <div class="Switchlayer" >
           <div class="Switchlayer_item" @click="handleCustommode()" :class="{ active: activeMode === 'custom' }">
              <p>游戏内监控</p>
           </div>
           <div class="Switchlayer_item" @click="handleGameMirror()" v-show="isGameMirrorInstalled" :class="{ active: activeMode === 'mirror' }">
              <p>游戏滤镜</p>
           </div>
          </div>
          <!-- <iframe class="iframeIngameToggle"  v-if="showToggleIframe"   :src="ToggleiframeSrc" frameborder="0" width="100%"  scrolling="auto"></iframe> -->
          <div v-if="showGameMirror" class="IngameToggle">
            <game-mirror />
          </div>
        </div>
        <feedback v-if="showFeedBack" @close="showFeedBack = false" />
        <router-view v-slot="{ Component }" v-if="!showIframe">
          <keep-alive :include="['HardWare']">
            <component :is="Component" />
          </keep-alive>
        </router-view>
        <NotInjectTip />
      </div>
  </div>
</template>



<style lang="scss" scoped>
.NotInMaxBoxShadow{
  // box-shadow: 0 1px 6px rgb(0 0 0 / 60%);
  // margin-left: 5px;
  // margin-top: 5px;
  border-radius: 6px;
}

.InMaxBoxShadow{
  // box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.3);
  border-radius: 6px;
}

.custom-notification {
  background-color: #ff0000; /* 你想要的背景颜色 */
}
.container{
  display: flex;
  position: relative;
  background-color: #22232e;

  .codeslayer{
    width: 100%;
    height: 100%;
    position: absolute;
    top:0;
    z-index: 999999;
    background: rgb(24 24 24 / 50%);
  }
  // box-shadow: 1px 0px 6px rgba(0, 0, 0, .6);
  overflow: hidden;
  .bgImg{
    z-index: 1;
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 10px;
  }
  video{
    width: 100%;
    height: 100%;
    position: absolute;
    object-fit: fill;
    border-radius: 10px;
  }
  .right{
    // width:100%;
    height: 100%;
    z-index: 15;
    position: relative;
    .nav{
      // .drag-bar{
      // width: 90%;
      // height: 20px;
      // -webkit-app-region: drag;
      // }
      // width: 100%;
      height: 50px;
      // border: 1px solid #FFFFFF;
      display: flex;
      align-items: center;
      justify-content: space-between;
      -webkit-app-region: drag;
      cursor: pointer;
      .setting{
        width: auto;
        display: flex;
        margin-left: auto;
        align-items: center;
        margin-top: -8px;
        margin-right: -6px;
        .item{
          width: 40px;
          height: 40px;
          text-align: center;
          line-height: 40px;
          cursor: pointer;
          color: #3579D5;
          -webkit-app-region: no-drag;
          .iconfont {
            font-size: 14px;
          }

          &:hover{
            background: #2D2E39;
            .iconfont {
              color: #ffffff;;
            }
          }
        }
      }
    }
    .iframeuserbar{
      height: calc(100% - 30px);
      position: absolute;
      top:30px;
      z-index: 9999;
    }
    .iframeIngameToggle{
      height: 400px;
      position: absolute;
      top:100px;
      left: -9px;
      z-index: 9999;
    }

    // border: 1px solid green
  }
  .ingame-class{
    background: rgba(34, 35, 46, .6);
  }
  .nav2{
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .ingamenav{
    background: rgba(34, 35, 46,.6);
    height: 40px!important;
    .ingamenav_box{
      -webkit-app-region: drag;
      display: flex;
      justify-content: space-between;
      width: 1230px;
    }
    img{
      margin-left: 20px;
      width: 90px;
    }
    .nav_left{
      display: flex;
      align-items: center;
      .ingameversion{
        color:#FFFFFF;
        font-size: 12px;
        margin-right: 20px;
        text-wrap: nowrap;
        margin-left: 20px;
      }
    }

    .nav_right{
      font-size: 12px;
      color: #ffffff;
      // padding-right: 25px;
      display: flex;
      align-items: center;

    }
    .item{
        margin-right: 20px;
        color: #fff;
        cursor: pointer;
        width: 20px;
        height: 20px;
        text-align: center;
        line-height: 20px;
        &:hover {
          background: #BF4040;
        }
      }

  }
  .Switchlayer_box{
    margin-top: 15px;
    margin-bottom: 15px;
  }
  .Switchlayer{
    display: flex;
    align-items: center;
    width: fit-content;
    margin-left: 20px;
    .Switchlayer_item{
      width: 120px;
      height: 40px;
      background: #2B2C37;
      border-radius: 4px;
      color: #ffffff;
      font-size: 12px;
      margin-right: 10px;
      border-radius: 4px;
      border: 2px solid transparent;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
    .active{
      border: 2px solid #3579D5;
    }

  }
  .IngameToggle{
    margin-left: -10px;
  }
}
/* Your styles here */
</style>

<style lang="scss">
.setting-dropdown {
  width: 120px;
  box-shadow: #343647 !important;
  border-radius: 4px;
  .el-dropdown-menu {
    background:#343647;
    color:#3579D5;

    .iconfont {
      margin-right: 12px;
    }
  }
  .el-dropdown-menu__item {
    color:#3579D5;
  }
  &.el-dropdown__popper.el-popper {
    background:#343647;
    border: 0;
  }
  .el-dropdown-menu__item:not(.is-disabled):focus{
    background:#3E4050;
    color:#FFFFFF;
  }
  .el-icon {
    margin-right: 10px;
  }
}
.el-dropdown__popper.el-popper .el-popper__arrow:before {
  border: 1px solid #343647;
}
.el-popper.is-light .el-popper__arrow:before {
  background: #343647;
  border: 1px solid #343647;
  right: 0;
}
.el-dropdown-menu {
  zoom: var(--el-dropdown-zoom);
}
.forzoom1{
  zoom:1;
}
.forzoom2{
  zoom:1.25;
  background: red;
}
</style>

