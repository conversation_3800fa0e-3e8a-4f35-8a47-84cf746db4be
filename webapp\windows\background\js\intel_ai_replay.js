const saveTime_before_kill = 5000 // 击杀前保留时间长度
let saveTime_after_kill = 6000 // 击杀后保留时间长度
let replayTime =(gamepp.setting.getInteger.sync(56)) * 1000 // 回溯时间

let multiKill_Switch = false // 多杀开关

let replay_timestamp_pool = [] // 时间戳池

// setInterval(() => {
//     console.log(Date.parse(new Date()))
// }, 1000);

function InterSparkleMessage_Receive (game_type) {
    replayTime =(gamepp.setting.getInteger.sync(56)) * 1000
    game_type = game_type+''
    // let aiModelInfo
    // try {
    //     aiModelInfo = JSON.parse(localStorage.getItem('aiModelInfo'));
    // }catch(e){
    //     aiModelInfo = {}
    // }
    // if (aiModelInfo && aiModelInfo[game_type]) {
    //     aiModelInfo[game_type].forEach(item=>{
    //         if (item.label.includes('连杀间隔时间')) {
    //             saveTime_after_kill = item.value*1000
    //         }
    //     })
    // }
    Kill_Time = Date.now()
    console.log('已记录击杀')
    try{gamepp.webapp.sendInternalAppEvent.promise('ingame_highlightsavetips', 'kill');}catch {}
    // 是否包含多杀
    replay_timestamp_pool.length === 0 ? multiKill_Switch = false : multiKill_Switch = true

    //记录本次击杀信息
    const event_timestamp = Date.parse(new Date())
    const event_before_timestamp = event_timestamp - saveTime_before_kill
    const event_after_timestamp = event_timestamp + saveTime_after_kill

    if (replay_timestamp_pool.length > 0) {
        console.log(replay_timestamp_pool[0], {event_before_timestamp, event_timestamp, event_after_timestamp})
        if (((event_timestamp - replay_timestamp_pool[0].before) + (saveTime_after_kill * 2)) >= replayTime) {
            const videoInfo = {
                type: 'INTEL_AI_SaveReplay',
                kill: replay_timestamp_pool.length,
                timestamps: JSON.stringify(replay_timestamp_pool)
            }
            video_client.Hotkey_OnSaveReplayTriggered(videoInfo)
            console.log('视频长度无法容纳下一个。', replay_timestamp_pool)
            initSaveReplayOption()
        } else if (((event_timestamp - replay_timestamp_pool[0].before) + (saveTime_after_kill * 2)) < replayTime) {
            const beforeLength = replay_timestamp_pool.length
            multiKill_Switch = false

            console.log('视频长度可以容纳下一个。', replay_timestamp_pool)

            replay_timestamp_pool.push({
                before: event_before_timestamp,
                current: event_timestamp,
                after: event_after_timestamp
            })

            setTimeout(() => {
                console.log('6s after', multiKill_Switch)
                if (replay_timestamp_pool.length === (beforeLength + 1)) {
                    const videoInfo = {
                        type: 'INTEL_AI_SaveReplay',
                        kill: replay_timestamp_pool.length,
                        timestamps: JSON.stringify(replay_timestamp_pool)
                    }
                    console.log('时限内无新的击杀事件', replay_timestamp_pool)
                    video_client.Hotkey_OnSaveReplayTriggered(videoInfo)
                    initSaveReplayOption()
                }
            }, saveTime_after_kill)
        }
    } else {
        replay_timestamp_pool.push({
            before: event_before_timestamp,
            current: event_timestamp,
            after: event_after_timestamp
        })
        setTimeout(() => {
            console.log('6s after', multiKill_Switch)
            if (replay_timestamp_pool.length === 1) {
                const videoInfo = {
                    type: 'INTEL_AI_SaveReplay',
                    kill: replay_timestamp_pool.length,
                    timestamps: JSON.stringify(replay_timestamp_pool)
                }
                console.log('时限内无新的击杀事件', replay_timestamp_pool)
                video_client.Hotkey_OnSaveReplayTriggered(videoInfo)
                initSaveReplayOption()
            }
        }, saveTime_after_kill)
    }
}

function initSaveReplayOption () {
    console.log('初始化')
    multiKill_Switch = false
    waiting_Switch = false
    replay_timestamp_pool = []
}
