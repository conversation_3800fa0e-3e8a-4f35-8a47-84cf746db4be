import {defineStore} from 'pinia'
import {reactive} from "vue";

export const obsStore = defineStore('videoRecord', () => {
    const state = reactive({
        main_switch:false
    })

    async function getSettingsValue() {
        const id41 = await GPP_GetInteger(41)
        state.main_switch = id41 === 1;
    }

    return {state,getSettingsValue}
})

export async function GPP_GetInteger(id) {
    let value = 0;
    try {
        value = await gamepp.setting.getInteger.promise(id);
    } catch {
    }
    return value;
}

export async function GPP_WriteInteger(id, value) {
    let old_value = await GPP_GetInteger(Number(id))
    if (Number(old_value) !== Number(value)) {
        console.log('setInteger,' + '' + Number(id) + ',' + Number(value));
        try {
            await gamepp.setting.setInteger.promise(Number(id), Number(value));
        } catch (error) {
        }
    }
}

export async function GPP_CheckedSendData(id, field) {
    const ivalue = field ? 1 : 0;
    await GPP_WriteInteger(id,ivalue);
}

export async function SetVideoQualityParams() {
    let resolution = gamepp.setting.getInteger.sync(53);
    let fps = gamepp.setting.getInteger.sync(54);
    let bitrate = gamepp.setting.getInteger.sync(55);
    {
        //OBS更新
        let isInGame = gamepp.isDesktopMode.sync();
        if (isInGame === false) {
            let CurrentConnectedClient = await gamepp.game.getCurrentConnectedClient.promise()

            let c_type = await gamepp.setting.getInteger.promise(280) === 1 ? 2 : 1;
            let c_adapter = 0;
            let Width = 0,Height = 0;
            //c_type 1 屏幕 2 游戏
            if (c_type === 1) {
                //录制桌面选择显示器序号
                c_adapter = await gamepp.setting.getInteger.promise(281);
                let DisplayInfo = await gamepp.hardware.getDisplayCardInfo.promise();
                Width = DisplayInfo['Element'][c_adapter]['PelsWidth'];
                Height = DisplayInfo['Element'][c_adapter]['PelsHeight'];
            } else if (c_type === 2) {
                Width = await gamepp.game.getWidth.promise();
                Height = await gamepp.game.getHeight.promise();
            }
            let U_VIDEOStr = {};
            U_VIDEOStr['type'] = 'U_VIDEO';
            U_VIDEOStr['width'] = Width;
            U_VIDEOStr['height'] = Height;
            U_VIDEOStr['FPS'] = gamepp.setting.getInteger.sync(54);
            U_VIDEOStr['adapter'] = 0;
            U_VIDEOStr['bitrate'] = bitrate;

            U_VIDEOStr['pid'] = CurrentConnectedClient['newPid'];
            U_VIDEOStr['c_type'] = c_type;
            U_VIDEOStr['c_adapter'] = c_adapter;
            U_VIDEOStr['isFullScreen'] = 0;
            U_VIDEOStr['encoder'] = 0;

            let U_VIDEOStrObjStr = JSON.stringify(U_VIDEOStr);
            console.log(U_VIDEOStrObjStr);
            await gamepp.obs.sendMessage.promise(U_VIDEOStrObjStr);
        }
    }
}
