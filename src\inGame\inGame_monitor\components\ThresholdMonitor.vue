<script setup lang="ts">
import {computed, defineProps, nextTick, ref, watch} from 'vue';
import {SensorData} from "@/modules/Game_Home/types/InGameMonitor";
import {useI18n} from "vue-i18n";

const {t} = useI18n()
const props = defineProps<{
    allData: SensorData,
    dataValue: any,
    index1: number,
    index2: number
}>()

const allData = computed(() => {
    return props.allData
})

const isShow = computed(() => {
    return props.allData.monitor && props.allData.monitor.switch
})

const monitorConfig = computed({
    get: () => {
        return props.allData.monitor;
    },
    set: (val)=>{
        props.allData.monitor = val;
    }
})

const bigOrSmallThanCount = ref(0) // 超过阈值的次数

let lastValue = -1;
let actionTimer:null|number = null;
let lastDataValue = -1
let lastCondition = ''
watch(()=>props.dataValue, (newValue) => {
    if (newValue && isShow.value) {
        let number = 0
        if (typeof newValue === 'number') {
            number = newValue
        }else{
            number = Number(newValue.replace(/[^0-9.]/g, '')) // 提取数字,保留小数点
        }
        const condition = props.allData.monitor.condition
        const cur_threshold = props.allData.monitor.cur_threshold
        const init_threshold = props.allData.monitor?.initial_threshold
        const Judgment_threshold = props.allData.monitor.threshold
        let isTrigger = false
        if (lastCondition === '') lastCondition = condition
        if (lastCondition !== condition) {
            bigOrSmallThanCount.value = 0
            lastCondition = condition
        }
        if (!condition.includes('Percent')) {
            if (condition.includes('big')) {
                if (number > init_threshold) {
                    bigOrSmallThanCount.value++
                }
            }else{
                if (number < init_threshold) {
                    bigOrSmallThanCount.value++
                }
            }
        }
        if (condition === 'bigger') {
            if (number > cur_threshold) {
                isTrigger = true
                console.log('bigger')
            }
        } else if (condition === 'smaller') {
            if (number < cur_threshold) {
                isTrigger = true
                console.log('smaller')
            }
        } else if (condition === 'biggerThanthreshold') { // 大于阈值百分比
            if (number > cur_threshold * (1 + Judgment_threshold / 100)) {
                isTrigger = true
                console.log('biggerThanthreshold')
            }
        } else if (condition === 'smallerThanthreshold') { // 小于阈值百分比
            if (number < cur_threshold * (1 - Judgment_threshold / 100)) {
                isTrigger = true
                console.log('smallerThanthreshold')
            }
        } else if (condition === 'biggerPercent') { // 当前值涨幅百分比
            if (lastDataValue !== -1 && number > lastDataValue * (1 + Judgment_threshold / 100)) {
                isTrigger = true
                console.log('biggerPercent')
            }
        } else if (condition === 'smallerPercent') { // 当前值降幅百分比
            if (lastDataValue !== -1 && number < lastDataValue * (1 - Judgment_threshold / 100)) {
                isTrigger = true
                console.log('smallerPercent')
            }
        }
        lastDataValue = number
        if (isTrigger) {
            setActionTimer(condition,number)
        }
    }
},{ flush: 'post' }) // 和上次值相同的也触发

function setActionTimer(condition:string,value:number) {
    if (lastValue !== -1) {
        if (condition.includes('big')) {
            if (value > lastValue) {
                lastValue = value
                clearTimeout(actionTimer)
            }else{
                return;
            }
        } else {
            if (value < lastValue) {
                lastValue = value
                clearTimeout(actionTimer)
            }else{
                return;
            }
        }
    }else{
        lastValue = value
    }
    actionTimer = setTimeout(async ()=>{
        const v = lastValue
        lastValue = -1
        if (props.allData.monitor?.is_update_threshold) {
            // 需要更新阈值
            props.allData.monitor.cur_threshold = v;
            console.log(v)
            const deepCloneData = JSON.parse(JSON.stringify(monitorConfig.value));
            deepCloneData.cur_threshold = v
            monitorConfig.value = deepCloneData
            const InGameList = JSON.parse(localStorage.getItem('InGameList1'))
            if (InGameList) {
                InGameList[props.index1][props.index2].monitor.cur_threshold = v
                localStorage.setItem('InGameList1', JSON.stringify(InGameList))
            }
        }
        if (props.allData.monitor?.do_what) {
            await sleep(100)
            switch (props.allData.monitor?.do_what) {
                case 'replay': // 执行即时回放
                    gamepp.webapp.sendInternalAppEvent.promise('background',{action:"ThresholdTriggered",value:"replay"})
                    break;
                case 'screenshot': // 执行截图
                    gamepp.webapp.sendInternalAppEvent.promise('background',{action:"ThresholdTriggered",value:"screenshot"})
                    break;
            }
        }
    },props.allData.monitor?.wait_time * 1000)
}

function sleep (time:number) {
    return new Promise(resolve => {
        setTimeout(() => {
            resolve(true)
        }, time)
    })
}

const showBiggerOrSmallCount = computed(()=>{
    if (monitorConfig.value?.condition.includes('Percent')) {
        return ''
    }else{
        if (monitorConfig.value?.condition.includes('big')) {
            return t('InGameMonitor.bigger') + monitorConfig.value.initial_threshold
        }else{
            return t('InGameMonitor.smaller') +  monitorConfig.value.initial_threshold
        }
    }
})
</script>

<template>
    <div v-if="isShow && monitorConfig && monitorConfig.is_show_threshold && !monitorConfig.condition.includes('Percent')" class="threshold-monitor">
        <span :style="{ color: allData.titlecolor, fontSize: allData.TitlefontStyle + 'px', fontFamily: allData.fontSize, fontWeight: allData.fontBold ? 'bold' : 'normal' , textShadow: allData.textShadow ? '0 1px rgb(0 0 0), 1px 0 rgb(0 0 0), -1px 0 rgb(0 0 0), 0 -1px rgb(0 0 0)' : 'none'}">{{
                $t('InGameMonitor.curThreshold2')
            }}</span>
        <span class="Keepleft"
              :style="{ color: allData.color, fontSize: allData.fontStyle + 'px', fontFamily: allData.fontSize, fontWeight: allData.fontBold ? 'bold' : 'normal', textShadow: allData.textShadow ? '0 1px rgb(0 0 0), 1px 0 rgb(0 0 0), -1px 0 rgb(0 0 0), 0 -1px rgb(0 0 0)' : 'none' }">
              {{ monitorConfig.cur_threshold }}
        </span>
    </div>
    <div v-if="isShow && monitorConfig && monitorConfig.is_init_threshold_big1000 && !monitorConfig.condition.includes('Percent')" class="threshold-monitor">
        <span
              :style="{ color: allData.titlecolor, fontSize: allData.TitlefontStyle + 'px', fontFamily: allData.fontSize, fontWeight: allData.fontBold ? 'bold' : 'normal' , textShadow: allData.textShadow ? '0 1px rgb(0 0 0), 1px 0 rgb(0 0 0), -1px 0 rgb(0 0 0), 0 -1px rgb(0 0 0)' : 'none'}">
            {{showBiggerOrSmallCount}}</span>
        <span class="Keepleft"
              :style="{ color: allData.color, fontSize: allData.fontStyle + 'px', fontFamily: allData.fontSize, fontWeight: allData.fontBold ? 'bold' : 'normal', textShadow: allData.textShadow ? '0 1px rgb(0 0 0), 1px 0 rgb(0 0 0), -1px 0 rgb(0 0 0), 0 -1px rgb(0 0 0)' : 'none' }">
              {{ bigOrSmallThanCount }}
        </span>
    </div>
</template>

<style scoped lang="scss">
.threshold-monitor {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    width: 100%;
    padding: 2px 0;

    span:last-child {
        margin-left: 10px;
    }
}

.activecross {
    .threshold-monitor {
        span:first-child {
            margin-left: 5px;
        }
    }
}
</style>
