<template>
    <div class="fps-limit-dialog">
        <div class="dialog-content">
            <div class='dialog-header'>
                <img src="../../../../assets/img/Public/logo_gpp.png" alt="">
                <p style="margin-right: auto;">{{$t('GameRebound.dataScreening')}}</p>

                <RightTopIcons close-icon @close="$store.closeFpsLimitDialog" :item-h="30" hover-color="#22232e"/>
            </div>
            <div class="dialog-body">
                <p class="description">{{$t('GameRebound.dataScreeningDescription')}}</p>
                <div class="b1">
                    <span>{{$t('GameRebound.excessivelyHighParameter')}}</span>
                    <span>{{$t('GameRebound.theMaximumValueIs')}} <text>{{ maxFps }}</text> FPS</span>
                </div>
                <div class="b2">
                    <span>{{$t('GameRebound.exclude')}} FPS ≥ </span>
                    <div class="input-number-box">
                        <el-input-number
                            :controls="false"
                            :min="0"
                            :max="maxFps"
                            v-model="data.max_limit"
                            @change="(e)=>methods.handleChangeInputMax(e,maxFps)"
                        />
                    </div>
                    <span>{{$t('GameRebound.dataStatisticsAtThatTime')}}</span>
                    <span class="clear" @click="methods.clear('max')">{{$t('messages.clear')}}</span>
                </div>
                <div class="b1">
                    <span>{{$t('GameRebound.tooLowParameter')}}</span>
                    <span>{{$t('GameRebound.theMinimumValueIs')}} <text>{{ minFps }}</text> FPS</span>
                </div>
                <div class="b2">
                    <span>{{$t('GameRebound.exclude')}} FPS ≤ </span>
                    <div class="input-number-box">
                        <el-input-number
                            :controls="false"
                            :min="0"
                            :max="maxFps"
                            v-model="data.min_limit"
                            @change="(e)=>methods.handleChangeInputMin(e,minFps)"
                        />
                    </div>
                    <span>{{$t('GameRebound.dataStatisticsAtThatTime')}}</span>
                    <span class="clear" @click="methods.clear('min')">{{$t('messages.clear')}}</span>
                </div>
            </div>
            <div class="dialog-footer">
                <el-button type="primary" @click="methods.confirm">{{$t('Setting.confirm')}}</el-button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import {useReboundDetailStore} from "@/modules/Game_ReboundDetail/stores/reboundDetail";
import {computed, nextTick, onMounted, reactive, defineEmits} from "vue";
import RightTopIcons from "@/components/mainCom/RightTopIcons.vue";

const $store = useReboundDetailStore()

const maxFps = computed(() => {
  return $store.powerData.fps?.max || 0
})
const minFps = computed(() => {
  return $store.powerData.fps?.min || 0
})

const data = reactive({
  max_limit: 0,
  min_limit: 0,
})

const methods = {
  handleChangeInputMax: (value: number | undefined, defaultNumber: number) => {
    if (value) {
      data.max_limit = value
    } else {
      data.max_limit = defaultNumber
    }
  },
  handleChangeInputMin: (value: number | undefined, defaultNumber: number) => {
    if (value) {
      data.min_limit = value
    } else {
      data.min_limit = defaultNumber
    }
  },
  confirm() {
    $store.setMaxLimit(data.max_limit)
    $store.setMinLimit(data.min_limit)
    const errs = []
    const errs_reason = []
    const fps_process = $store.powerData.fps.detail
    for (let i = 0; i < fps_process.length; i++) {
      let fps_current = fps_process[i];
      let delete_flag = false;
      if (fps_current <= data.min_limit && data.min_limit !== 0) {
        delete_flag = true;
      } else if (fps_current >= data.max_limit && data.max_limit !== 0) {
        delete_flag = true;
      }
      if (delete_flag) {
        errs.push(i)
        if (fps_current < data.min_limit) {
          errs_reason.push('min')
        } else {
          errs_reason.push('max')
        }
      }
    }
    $store.powerData.errs = errs
    $store.powerData.errs_reason = errs_reason
    $store.closeFpsLimitDialog()
    const bc = new BroadcastChannel('fps_limit')
    bc.postMessage({max_limit: data.max_limit, min_limit: data.min_limit})
  },
  clear(type: string) {
    if (type === 'max') {
      data.max_limit = 0
    } else {
      data.min_limit = 0
    }
  },
}

onMounted(() => {
  nextTick(() => {
    data.max_limit = $store.max_limit
    data.min_limit = $store.min_limit
  })
})
</script>

<style scoped lang="scss">
.fps-limit-dialog {
  position: absolute;
  top: 30px;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 999999;

  .dialog-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 420px;
    height: 360px;
    background: #22232e;
    box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
  }

  .dialog-header {
    width: 420px;
    height: 30px;
    background: #343647;
    box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px 4px 0 0;
    color: #fff;
    font-size: 12px;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    line-height: 30px;
    overflow: hidden;

    img {
      width: 18px;
      height: 18px;
      margin-right: 9px;
      margin-left: 10px;
    }

    .close-wrap {
      margin-left: auto;
      width: 40px;
      height: 30px;
      margin-right: 10px;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 2px;

      .icon-Close {
        color: #3579d5;
        font-size: 14px;
      }

      &:hover {
        background-color: #22232e;
        .icon-Close {
          color: #ffffff;
        }
      }
    }
  }

  .dialog-body {
    box-sizing: border-box;
    padding: 24px 20px 0 20px;

    .description {
      font-weight: 400;
      font-size: 14px;
      color: #777777;
      line-height: 30px;
      text-indent: 1em;
    }

    .b1 {
      font-size: 14px;
      color: #777777;
      display: flex;
      flex-flow: row nowrap;
      justify-content: space-between;
      margin-bottom: 13px;
      margin-top: 20px;

      text {
        color: #ffffff;
      }
    }

    .b2 {
      height: 30px;
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      color: #ffffff;

      .clear {
        color: #3579D5;
        cursor: pointer;
        margin-left: 9px;
      }

      .input-number-box {
        width: 90px;
        height: 30px;
        margin: 0 10px;

        :deep(.el-input-number) {
          width: 90px;
        }
      }
    }
  }

  .dialog-footer {
    margin-bottom: 30px;
    margin-top: 20px;
    text-align: center;

    :deep(.el-button) {
      width: 90px;
    }
  }
}
</style>
