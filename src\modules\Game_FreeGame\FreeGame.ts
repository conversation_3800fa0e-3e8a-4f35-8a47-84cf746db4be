
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import App from './FreeGame.vue'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import './utils/reset.css'
import './utils/font.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import '../../assets/iconfont/iconfont.css'
import '../../assets/iconfont/iconfont.js'
import './Game_FreeGame_RTL.scss'
import i18n from '../../assets/lang'
import { dayjs } from "element-plus";
import countTo from 'vue3-count-to';
dayjs.en.weekStart = 1;
const app = createApp(App)
app.use(ElementPlus, {
    locale: zhCn,
  })
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }
app.use(createPinia())
app.use(countTo);
app.use(i18n)
app.mount('#app')