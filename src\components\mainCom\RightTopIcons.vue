<template>
    <ul class="RightTopIcons" :style="{ '--hoverColor': props.hoverColor,'--itemH':props.itemH+'px', '--itemW':props.itemW+'px' }">
        <li v-if="props.minimizeIcon" @click="emits('minimize')">
            <span class="iconfont icon-minimize"></span>
        </li>
        <li v-if="props.maximizeIcon && !props.isFullScreen" @click="emits('maximize')">
            <span class="iconfont icon-Maximize"></span>
        </li>
        <li v-if="props.maximizeIcon && props.isFullScreen" @click="emits('unmaximize')">
            <span class="iconfont icon-normalize"></span>
        </li>
        <li v-if="props.closeIcon" @click="emits('close')">
            <span class="iconfont icon-Close"></span>
        </li>
    </ul>
</template>

<script setup lang="ts">
import '../../assets/iconfont/iconfont.css'
import '../../assets/iconfont/iconfont.js'
import { defineProps,defineEmits } from 'vue';

const props = withDefaults(defineProps<{
    closeIcon?: boolean;
    minimizeIcon?: boolean;
    maximizeIcon?: boolean;
    isFullScreen?: boolean;
    hoverColor?: string;
    itemH?: number;
    itemW?: number;
}>(), {
    close: false,
    minimize: false,
    maximize: false,
    isFullScreen: false,
    hoverColor: '#2d2e39',
    itemH: 40,
    itemW: 40,
});

const emits = defineEmits(['close', 'minimize', 'maximize','unmaximize'])
</script>

<style scoped lang="scss">
ul.RightTopIcons {
    --hoverColor: #2d2e39;
    --itemH: 40px;
    --itemW: 40px;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    -webkit-app-region: no-drag;
    height: 100%;
    max-height: 40px;

    li {
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        width: var(--itemW);
        height: var(--itemH);
        border-radius: 0;

        .iconfont {
            color: #777;
            font-size: 14px;
        }

        &:hover {
            background-color: var(--hoverColor);

            .iconfont {
                color: #ffffff;
            }
        }
    }
}
</style>
