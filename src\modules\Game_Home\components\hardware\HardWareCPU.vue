<script setup lang="ts">
import {hardware} from "@/modules/Game_Home/stores";
import {defineProps,ref,onMounted,watch} from "vue";
import ChartHW from "@/components/echarts/ChartHW.vue";

const $store = hardware()
const props = defineProps({
  changeMenuInfo: {
    type: Function,
    required: true
  }
})
let loading = ref(true)
let loading_count = 0
let activeCollapse_cpu = ref(["1"])
let tempWall = ref(100)
const eUsage = ref<Array<number>>([]) // 小核占用
const pUsage = ref<Array<number>>([]) // 大核占用
const Usage = ref<Array<number>>([]) // 占用
const cpu_temp = ref<Array<number>>([]) // cpu温度
const limit = 60
onMounted(()=>{
  setTimeout(()=>{
    emitChangeFn()
  },100)
  loadingFn();
  listenHardwareChangeMsg()
})
watch(() => $store.bg_sensor_data, () => {
  try {
    recordPECoreCallBack()
  }catch(e){

  }
},{deep: true})

function listenHardwareChangeMsg() {
  const hw = new BroadcastChannel('hw')
  hw.onmessage = (e:any)=>{
    if (e.data.action && e.data.action == 'change') {
      emitChangeFn();
    }
  }
}

async function recordPECoreCallBack() {
  if ($store.bg_sensor_data.cpu.usage) {
    // 记录大核小核占用
    const cpu_usage:any = $store.bg_sensor_data.cpu.usage
    const cpu_usage_p:any = $store.bg_sensor_data.cpu.usageP
    const cpu_usage_e:any = $store.bg_sensor_data.cpu.usageE

    pUsage.value.push(cpu_usage_p || 0)
    eUsage.value.push(cpu_usage_e || 0)
    Usage.value.push(cpu_usage || 0)
    if (pUsage.value.length > limit) pUsage.value.shift() // 只记录5分钟
    if (eUsage.value.length > limit) eUsage.value.shift() // 只记录5分钟
    if (Usage.value.length > limit) Usage.value.shift() // 只记录5分钟
    // 记录温度
    const cpuTemp:any = $store.bg_sensor_data.cpu.temp
    cpu_temp.value.push(cpuTemp || 0)
    if (cpu_temp.value.length > limit) cpu_temp.value.shift() // 只记录5分钟
  }
}

const emitChangeFn = () => {
  // 带大小核的占用高度493 不带大小和占用高度343
  if (activeCollapse_cpu.value.length === 0) {
    props.changeMenuInfo(1, 72)
    return
  } else {
    let h = 77
    if ($store.originData.CPU && $store.originData.CPU.SubNode.length > 0) {
      $store.originData.CPU.SubNode.forEach((item:any)=>{
        if (item.NumberofCPUCoresEfficient > 0) { // 带大小核
          h += 493
        }else{
          h += 343
        }
      })
      props.changeMenuInfo(1, h)
    }
  }
}
const loadingFn = () => {
  loading_count++
  if (loading_count > 10) {
    loading.value = false
    return
  }
  if ($store.HwInfo.CPU) {
    emitChangeFn()
    loading.value = false
    getCPUTempWall()
  }else{
    setTimeout(()=>{loadingFn()},1000)
  }
}

function getCPUTempWall () { // 获取CPU温度墙
  console.log($store.HwInfo.CPU);
  if ($store.HwInfo.CPU.SubNode && Array.isArray($store.HwInfo.CPU.SubNode))
  {
    const tw = $store.HwInfo.CPU.SubNode[0]['CPUMax.JunctionTemperature(Tj,max)']
    if (tw)
    {
      tempWall.value = Number(tw.replace(/[^0-9.]/g, ''))
    }
    else
    {
      tempWall.value = 100
    }
  }
}

</script>

<template>
  <div class="HardwareCPU">
    <el-collapse v-model="activeCollapse_cpu" @change="emitChangeFn">
      <el-collapse-item title="CPU" name="1" class="as">
        <template #title>
          <div class="HardwareCPUTitle flex-items-center">
            <span class="iconfont icon-CPU" style="color: #3579D5;font-size: 24px;margin-right: 5px;"></span>
            <span style="color: #ffffff">{{ $t('hardwareInfo.processor') }}</span>

            <span class="ml-auto"></span>
          </div>
        </template>
        <div
            v-if="$store.originData.CPU"
            v-for="(item,index) in $store.originData.CPU.SubNode"
            :key="'hwc'+index"
        >
          <div class="HardwareCPU-cpuname">{{ item.ProcessorName }}</div>
          <!-- 大小核占用 -->
          <div class="PEcore" v-if="item.NumberofCPUCoresEfficient > 0">
            <ChartHW :dataHtml="'hardwareInfo.Poccupied'" unit="%" :yAxisValue="pUsage" :yAxisMax="100"></ChartHW>
            <ChartHW :dataHtml="'hardwareInfo.Eoccupied'" unit="%" :yAxisValue="eUsage" :yAxisMax="100"></ChartHW>
          </div>
          <div class="usageAndTemp" v-else>
            <ChartHW :dataHtml="'hardwareInfo.occupied'" unit="%" :yAxisValue="Usage" :yAxisMax="100"></ChartHW>
            <ChartHW :dataHtml="'hardwareInfo.temperature'" unit="℃" :tempWall="tempWall" :yAxisValue="cpu_temp" :yAxisMax="120"></ChartHW>
          </div>
          <div class="PEcore" v-if="item.NumberofCPUCoresEfficient > 0">
            <div>
              <ChartHW :dataHtml="'hardwareInfo.temperature'" unit="℃" :tempWall="tempWall" :yAxisValue="cpu_temp" :yAxisMax="120"></ChartHW>
            </div>
            <div class="flex-col">
              <div class="HardwareCPU-card">
                <span>{{ $t('hardwareInfo.Pfrequency') }}</span>
                <span v-if="$store.bg_sensor_data.cpu">{{$store.bg_sensor_data.cpu.clockP}}MHz</span>
              </div>
              <div class="HardwareCPU-card">
                <span>{{ $t('hardwareInfo.Efrequency') }}</span>
                <span v-if="$store.bg_sensor_data.cpu">{{$store.bg_sensor_data.cpu.clockE}}MHz</span>
              </div>
              <div class="HardwareCPU-card">
                <span>{{ $t('hardwareInfo.thermalPower') }}</span>
                <span v-if="$store.bg_sensor_data.cpu">{{$store.bg_sensor_data.cpu.power}}W</span>
              </div>
            </div>
          </div>
          <div class="clockAndPower" v-else>
            <div class="HardwareCPU-card">
              <span>{{ $t('hardwareInfo.frequency') }}</span>
              <span v-if="$store.bg_sensor_data.cpu">{{$store.bg_sensor_data.cpu.clock}}MHz</span>
            </div>
            <div class="HardwareCPU-card">
              <span>{{ $t('hardwareInfo.thermalPower') }}</span>
              <span v-if="$store.bg_sensor_data.cpu">{{$store.bg_sensor_data.cpu.power}}W</span>
            </div>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<style scoped lang="scss">
.HardwareCPU {
  width: 100%;
  background: rgba(45 ,46 ,57, 0.8);
  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  padding: 0 25px 0 20px;

  .HardwareCPUTitle {
    width: 100%;
    padding-right: 25px;
  }

  .HardwareCPU-cpuname {
    margin-bottom: 10px;
  }

  .PEcore {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
    margin-bottom: 20px;
  }

  .clockAndPower,.usageAndTemp {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
    margin-bottom: 20px;
  }

  .flex-col{
    display: flex;
    flex-flow: column nowrap;
    justify-content: space-between;
  }

  .HardwareCPU-card {
    width: 270px;
    height: 60px;
    padding: 0 20px;
    background: rgba(34, 35, 46, 0.8);
    box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.3);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    span:last-child {
      color: #35D5B1;
    }
  }
}
</style>
