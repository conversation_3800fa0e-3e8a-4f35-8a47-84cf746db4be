<script setup lang="ts">
import {defineProps, onMounted, reactive, ref} from 'vue'
import {QuestionFilled} from '@element-plus/icons-vue'
import {getHoursMinutesSeconds, totalSeconds,MathArrayAvg2,MathArrayMax2,MathArrayMin2} from "./someScripts"
import SimpleDataCharts from "@/modules/Game_ReboundDetail/components/SimpleDataCharts.vue";
import {useReboundDetailStore} from "@/modules/Game_ReboundDetail/stores/reboundDetail";
import {storeToRefs} from "pinia";

const $store = useReboundDetailStore()
const props = defineProps({
  recentGameInfo: {
    type: Object,
    required: true
  },
  hardwaerinfo: {
    type: Object,
    required: true
  },
  powerData: {
    type: Object,
    required: true
  }
})

let CPUEstimatePower:any
let GameTimeH
let GPUEstimatePower:any
let cpupower1 = ref('')
let cpupower2 = ref('')
let cpupower3 = ref(0)
let gpupower1 = ref('')
let gpupower2 = ref('')
let gpupower3 = ref(0)
let mempower1 = ref('')
let mempower2 = ref('')
let mempower3 = ref(0)

const {startTime,endTime,endTimeOriginal,gameTime} = storeToRefs($store)

onMounted(()=>{
  getEndTime();
})
let try_count = 0
function getEndTime() {
  if (props.recentGameInfo.starttime) {
    gameTime.value = props.recentGameInfo.endtime - props.recentGameInfo.starttime
    const t = getHoursMinutesSeconds(gameTime.value)
    endTimeOriginal.value.h = t.h
    endTimeOriginal.value.m = t.m
    endTimeOriginal.value.s = t.s
  } else {
    try_count++
    if (try_count > 30) return
    setTimeout(getEndTime, 100)
  }
}
function showCpuPower(powerData:any) {
  if (!(powerData.cpuload && powerData.cpuload.avg)) {
    return 0
  }
  let cpuload = powerData.cpuload.avg;
  if (cpuload <= 25) {
    CPUEstimatePower = (powerData.cpupower['avg'] * 1.5) * 1.25;
  } else if (cpuload > 25 && cpuload <= 50) {
    CPUEstimatePower = (powerData.cpupower['avg'] * 1.5) * 1.2;
  } else if (cpuload > 50 && cpuload <= 75) {
    CPUEstimatePower = (powerData.cpupower['avg'] * 1.5) * 1.15;
  } else if (cpuload > 75 && cpuload <= 100) {
    CPUEstimatePower = (powerData.cpupower['avg'] * 1.5) * 1.05;
  }
  GameTimeH = (((props.recentGameInfo.endtime - props.recentGameInfo.starttime) / 3600));
  cpupower1.value = parseFloat((CPUEstimatePower / 1000).toFixed(5)) + 'KW '
  cpupower2.value = parseFloat(GameTimeH.toFixed(5)) + ' h ';
  cpupower3.value = parseFloat(((CPUEstimatePower / 1000) * GameTimeH).toFixed(4));
  return parseFloat(((CPUEstimatePower / 1000) * GameTimeH).toFixed(4));
}
function showGpuPower(powerData:any) {
  if (!(powerData.gpupower && powerData.gpupower.avg)) {
    return 0
  }

  let gpuload = powerData.gpuload.avg;
  if (gpuload <= 25) {
    GPUEstimatePower = powerData.gpupower['avg'] * 1.5;
  } else if (gpuload > 25 && gpuload <= 50) {
    GPUEstimatePower = powerData.gpupower['avg'] * 1.35;
  } else if (gpuload > 50 && gpuload <= 75) {
    GPUEstimatePower = powerData.gpupower['avg'] * 1.25;
  } else if (gpuload > 75 && gpuload <= 100) {
    GPUEstimatePower = powerData.gpupower['avg'] * 1.15;
  }
  GameTimeH = (((props.recentGameInfo.endtime - props.recentGameInfo.starttime) / 3600));

  gpupower1.value = parseFloat((GPUEstimatePower / 1000).toFixed(4)) + 'KW '
  gpupower2.value = parseFloat(GameTimeH.toFixed(4)) + ' h '
  gpupower3.value = parseFloat(((GPUEstimatePower / 1000) * GameTimeH).toFixed(4));

  return parseFloat(((GPUEstimatePower / 1000) * GameTimeH).toFixed(4));
}

function getMemoryPower (recentGameInfo:any) {
  try {
    let MemoryPower = 0;
    let data_arr = JSON.parse(gamepp.hardware.getBaseJsonInfo.sync());
    data_arr['MEMORY']['SubNode'].forEach((MemoryKV:any)=>{
      let MemorySize = ((MemoryKV.ModuleSize).split(' ')[0]) * 1024;
      if (MemorySize === 2048) {
        MemoryPower += 1.5;
      } else if (MemorySize === 4096) {
        MemoryPower += 3;
      } else if (MemorySize === 8192) {
        MemoryPower += 6;
      } else if (MemorySize === 16384) {
        MemoryPower += 12;
      } else if (MemorySize === 32768) {
        MemoryPower += 18;
      }
    })
    GameTimeH = (((recentGameInfo.endtime - recentGameInfo.starttime) / 3600));
    mempower1.value = parseFloat((MemoryPower / 1000).toFixed(4)) + 'KW '
    mempower2.value = parseFloat(GameTimeH.toFixed(4)) + ' h '
    mempower3.value = parseFloat(((MemoryPower / 1000) * GameTimeH).toFixed(4))
    return parseFloat(((MemoryPower / 1000) * GameTimeH).toFixed(4))
  }catch (e) {
    return 0;
  }
}

function totalPower (powerData:any) {
  return showCpuPower(powerData) + showGpuPower(powerData) + getMemoryPower(props.recentGameInfo)
}

function gpuMemAndMemUsage(gpuMemUsage:number,MemUsage:number) {
  const resultArr = ['','']
  const hardwaerinfo = props.hardwaerinfo
  try {
    const TotalMemorySizeMB = hardwaerinfo['TotalMemorySize[MB]'] * 1
    const TotalMemorySizeGB = hardwaerinfo['Memory_size']
    let usedGB = TotalMemorySizeMB / 1024 * MemUsage / 100
    resultArr[1] = usedGB.toFixed(2) + '/' + TotalMemorySizeGB
  }catch (e) {
    resultArr[1] = '';
  }
  try {
    let gpuMemSize = 0
    if (hardwaerinfo['VideoMemoryNumber']) {
      gpuMemSize = hardwaerinfo['VideoMemoryNumber'] * 1
    }
    const TotalMemorySizeGB = gpuMemSize / 1024
    const usedGB = TotalMemorySizeGB * gpuMemUsage / 100
    resultArr[0] = usedGB.toFixed(2) + '/' + TotalMemorySizeGB.toFixed(1) + 'GB'
  }catch (e) {
    resultArr[0] = '';
  }
  return resultArr
}

function computedFullDataAvg(dataList:Array<number>) {
  if (!Array.isArray(dataList)) return 0
  if (dataList.length == 0) return 0
  let startIndex = 0
  let endIndex = 0
  if (startTime.value.h != 0 || startTime.value.m != 0 || startTime.value.s != 0) {
    startIndex = Math.ceil(totalSeconds(startTime.value) / gameTime.value * dataList.length)
  }
  if (startTime.value.h == 0 && startTime.value.m == 0 && startTime.value.s == 0) {
    startIndex = 0
  }
  if (totalSeconds(endTime.value) < totalSeconds(endTimeOriginal.value)) {
    endIndex = Math.ceil(totalSeconds(endTime.value) / gameTime.value * dataList.length)
  } else {
    endIndex = dataList.length
  }
  try {
    return MathArrayAvg2(dataList.slice(startIndex, endIndex),startIndex,props.powerData.errs)
  }catch (e) {
    return 0
  }
}
</script>

<template>
  <div class="ReboundDetailSimpleData">
    <div class="SimpleData-top">
      <h1>{{ $t('GameRebound.HardwareStatus') }}</h1>
      <!-- 耗电和二氧化碳排放 -->
      <div class="cost">
        <span class="iconfont icon-power"></span>
        <span>Power ≈ </span>
        <span class="power-color" v-if="props.powerData.cpuload">{{ totalPower(props.powerData).toFixed(5) }}KW.h</span>
        <el-tooltip effect="light" :content="$t('GameRebound.totalPower')">
          <el-icon color="#CABB3D" class="no-inherit">
            <QuestionFilled/>
          </el-icon>
        </el-tooltip>

        <div class="space"></div>
        <span class="iconfont icon-co2"></span>
        <span >CO2 ≈ </span>
        <span v-if="props.powerData.cpuload" class="CO2-color">{{ parseFloat(((totalPower(props.powerData) * 0.785)).toFixed(5)) }}KG</span>
        <el-tooltip effect="light" :content="$t('GameRebound.TotalEmissions')">
          <el-icon color="#CABB3D" class="no-inherit">
            <QuestionFilled/>
          </el-icon>
        </el-tooltip>
        <div class="space"></div>
        <span>{{ $t('GameRebound.PSS') }}</span>
      </div>
    </div>
    <div class="dashboard-list">
      <div class="dashboard-Frame">
        <h2>Frame</h2>
        <div class="right-line"></div>
        <div class="dashboard-content">
          <div class="FPS">
            <p>FPS</p>
            <h3 v-if="props.powerData.fps && props.powerData.fps.detail">{{computedFullDataAvg(props.powerData.fps.detail).toFixed(0)}}</h3>
          </div>
          <div class="FPS-low">
            <p>1% Low</p>
            <h4 v-if="props.powerData.fps1 && props.powerData.fps1.detail">{{computedFullDataAvg(props.powerData.fps1.detail).toFixed(0)}}</h4>
            <p style="margin-top: 10px;">0.1% Low</p>
            <h4 v-if="props.powerData.fps01 && props.powerData.fps01.detail">{{computedFullDataAvg(props.powerData.fps01.detail).toFixed(0)}}</h4>
          </div>
        </div>
        <div class="description">
          <span class="iconfont icon-power"></span>
          <span>{{props.hardwaerinfo.game_resolutions}}</span>
          <el-tooltip effect="light" :content="$t('GameRebound.GameResolution')">
            <el-icon size="16" color="#CABB3D" class="no-inherit">
              <QuestionFilled/>
            </el-icon>
          </el-tooltip>
        </div>
      </div>
      <div class="dashboard-CPU">
        <h2>CPU</h2>
        <div class="right-line"></div>
        <div class="dashboard-content">
          <div class="cpu-usage">
            <div class="dashboard-CPU-content">
              <p v-if="props.powerData.cpuload && props.powerData.cpuload.detail">{{computedFullDataAvg(props.powerData.cpuload.detail).toFixed(0)}}%</p>
              <span>{{ $t('hardwareInfo.occupied') }}</span>
            </div>
            <div v-if="props.powerData.cpuload && props.powerData.cpuload.detail" class="progress-circle" :style="{'--percent':computedFullDataAvg(props.powerData.cpuload.detail)}">
              <svg>
                <defs>
                  <!-- 定义一个线性渐变 -->
                  <linearGradient id="gradientColor" gradientTransform="rotate(90)">
                    <!-- 三个颜色停止点 -->
                    <stop offset="0%" style="stop-color:#E44A30; stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#20A668; stop-opacity:1" />
                  </linearGradient>
                </defs>
                <circle stroke="var(--inactive-color)"/>
                <circle stroke="url(#gradientColor)"
                        class="progress-valueprogress-value"
                        style="stroke-dasharray: calc(2 * 3.1415 * var(--r) * (var(--percent) / 100)), 1000"
                />
              </svg>
            </div>
          </div>
          <div class="cpu-temp">
            <div class="dashboard-CPU-content">
              <p v-if="props.powerData.cputemperature && props.powerData.cputemperature.detail">{{computedFullDataAvg(props.powerData.cputemperature.detail).toFixed(0)}}℃</p>
              <span>{{ $t('hardwareInfo.temperature') }}</span>
            </div>
            <div v-if="props.powerData.cputemperature && props.powerData.cputemperature.detail" class="progress-circle" :style="{'--percent':computedFullDataAvg(props.powerData.cputemperature.detail)}">
              <svg>
                <defs>
                  <!-- 定义一个线性渐变 -->
                  <linearGradient id="gradientColor" gradientTransform="rotate(90)">
                    <!-- 三个颜色停止点 -->
                    <stop offset="0%" style="stop-color:#E44A30; stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#20A668; stop-opacity:1" />
                  </linearGradient>
                </defs>
                <circle stroke="var(--inactive-color)"/>
                <circle stroke="url(#gradientColor)"
                        class="progress-value"
                        style="stroke-dasharray: calc(2 * 3.1415 * var(--r) * (var(--percent) / 100)), 1000"
                />
              </svg>
            </div>
          </div>
        </div>
        <div class="description" v-if="props.powerData && props.powerData.cpuload">
          <span class="iconfont icon-power"></span>
          <span>Power ≈ </span>
          <span style="color: #CABB3D">{{showCpuPower(props.powerData)}}KW.h</span>
          <el-tooltip effect="light" :content="cpupower1 + '· ' + cpupower2 + ' ≈ ' + cpupower3+ $t('GameRebound.degree')">
            <el-icon size="16" color="#CABB3D" class="no-inherit">
              <QuestionFilled/>
            </el-icon>
          </el-tooltip>
        </div>
      </div>
      <div class="dashboard-GPU">
        <h2>GPU</h2>
        <div class="right-line"></div>
        <div class="dashboard-content">
          <div class="gpu-usage">
            <div class="dashboard-GPU-content" v-if="props.powerData.gpuload && props.powerData.gpuload.detail">
              <p>D:{{computedFullDataAvg(props.powerData.gpuload.detail).toFixed(0)}}%</p>
              <p v-if="props.powerData.gpuload1 && props.powerData.gpuload1.avg">T:{{computedFullDataAvg(props.powerData.gpuload1.detail).toFixed(0)}}%</p>
              <span>{{ $t('hardwareInfo.occupied') }}</span>
            </div>
            <div class="progress-circle half-circle-left-box" v-if="props.powerData.gpuload && props.powerData.gpuload.detail" :style="{'--percent':computedFullDataAvg(props.powerData.gpuload.detail)/2,'left': -2+'px'}">
              <svg>
                <defs>
                  <!-- 定义一个线性渐变 -->
                  <linearGradient id="gradientColor" gradientTransform="rotate(90)">
                    <!-- 三个颜色停止点 -->
                    <stop offset="0%" style="stop-color:#E44A30; stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#20A668; stop-opacity:1" />
                  </linearGradient>
                </defs>
                <circle stroke="var(--inactive-color)"/>
                <circle stroke="url(#gradientColor)"
                        class="progress-value"
                        style="stroke-dasharray: calc(2 * 3.1415 * var(--r) * (var(--percent) / 100)), 1000"
                />
              </svg>
            </div>

            <div class="progress-circle half-circle-right-box" v-if="props.powerData.gpuload1 && props.powerData.gpuload1.detail" :style="{'--percent':computedFullDataAvg(props.powerData.gpuload1.detail)/2,'right': -2+'px'}">
              <svg>
                <defs>
                  <!-- 定义一个线性渐变 -->
                  <linearGradient id="gradientColor" gradientTransform="rotate(90)">
                    <!-- 三个颜色停止点 -->
                    <stop offset="0%" style="stop-color:#E44A30; stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#20A668; stop-opacity:1" />
                  </linearGradient>
                </defs>
                <circle stroke="var(--inactive-color)"/>
                <circle stroke="url(#gradientColor)"
                        class="progress-value"
                        style="stroke-dasharray: calc(2 * 3.1415 * var(--r) * (var(--percent) / 100)), 1000"
                />
              </svg>
            </div>
          </div>
          <div class="gpu-temp">
            <div class="dashboard-GPU-content" v-if="props.powerData.gputemperature && props.powerData.gputemperature.detail">
              <p>{{computedFullDataAvg(props.powerData.gputemperature.detail).toFixed(0)}}℃</p>
              <span style="margin-top: 10px;">{{ $t('hardwareInfo.temperature') }}</span>
            </div>
            <div v-if="props.powerData.gputemperature && props.powerData.gputemperature.detail" class="progress-circle" :style="{'--percent':computedFullDataAvg(props.powerData.gputemperature.detail)}">
              <svg>
                <defs>
                  <!-- 定义一个线性渐变 -->
                  <linearGradient id="gradientColor" gradientTransform="rotate(90)">
                    <!-- 三个颜色停止点 -->
                    <stop offset="0%" style="stop-color:#E44A30; stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#20A668; stop-opacity:1" />
                  </linearGradient>
                </defs>
                <circle stroke="var(--inactive-color)"/>
                <circle stroke="url(#gradientColor)"
                        class="progress-value"
                        style="stroke-dasharray: calc(2 * 3.1415 * var(--r) * (var(--percent) / 100)), 1000"
                />
              </svg>
            </div>
          </div>
          <div class="gpu-mem">
            <div class="dashboard-GPU-content" v-if="props.powerData.gpumemoryload && props.powerData.gpumemoryload.detail">
              <p>{{props.powerData.gpumemoryload.avg}}%</p>
              <span style="margin-top: 10px;">{{gpuMemAndMemUsage(computedFullDataAvg(props.powerData.gpumemoryload.detail),computedFullDataAvg(props.powerData.memory.detail))[0]}}</span>
            </div>
            <div v-if="props.powerData.gpumemoryload && props.powerData.gpumemoryload.detail" class="progress-circle" :style="{'--percent':computedFullDataAvg(props.powerData.gpumemoryload.detail).toFixed(0)}">
              <svg>
                <defs>
                  <!-- 定义一个线性渐变 -->
                  <linearGradient id="gradientColor" gradientTransform="rotate(90)">
                    <!-- 三个颜色停止点 -->
                    <stop offset="0%" style="stop-color:#E44A30; stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#20A668; stop-opacity:1" />
                  </linearGradient>
                </defs>
                <circle stroke="var(--inactive-color)"/>
                <circle stroke="url(#gradientColor)"
                        class="progress-value"
                        style="stroke-dasharray: calc(2 * 3.1415 * var(--r) * (var(--percent) / 100)), 1000"
                />
              </svg>
            </div>
          </div>
        </div>
        <div class="description" v-if="props.powerData && props.powerData.gpupower">
          <span class="iconfont icon-power"></span>
          <span>Power ≈ </span>
          <span style="color: #CABB3D">{{showGpuPower(props.powerData)}}KW.h</span>
          <el-tooltip effect="light" :content="gpupower1 + '· ' + gpupower2 + ' ≈ ' + gpupower3+ $t('GameRebound.degree')">
            <el-icon size="16" color="#CABB3D" class="no-inherit">
              <QuestionFilled/>
            </el-icon>
          </el-tooltip>
        </div>
      </div>
      <div class="dashboard-MEM">
        <h2>MEM</h2>
        <div class="dashboard-content">
          <div class="mem-usage">
            <div class="dashboard-MEM-content" v-if="props.powerData.memory && props.powerData.memory.detail">
              <p>{{computedFullDataAvg(props.powerData.memory.detail).toFixed(0)}}%</p>
              <span>{{gpuMemAndMemUsage(computedFullDataAvg(props.powerData.gpumemoryload.detail),computedFullDataAvg(props.powerData.memory.detail))[1]}}</span>
            </div>
            <div v-if="props.powerData.memory && props.powerData.memory.detail" class="progress-circle" :style="{'--percent':computedFullDataAvg(props.powerData.memory.detail)}">
              <svg>
                <defs>
                  <!-- 定义一个线性渐变 -->
                  <linearGradient id="gradientColor" gradientTransform="rotate(90)">
                    <!-- 三个颜色停止点 -->
                    <stop offset="0%" style="stop-color:#E44A30; stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#20A668; stop-opacity:1" />
                  </linearGradient>
                </defs>
                <circle stroke="var(--inactive-color)"/>
                <circle stroke="url(#gradientColor)"
                        class="progress-value"
                        style="stroke-dasharray: calc(2 * 3.1415 * var(--r) * (var(--percent) / 100)), 1000"
                />
              </svg>
            </div>
          </div>
        </div>
        <div class="description" v-if="props.recentGameInfo && props.recentGameInfo.starttime">
          <span class="iconfont icon-power"></span>
          <span>Power ≈ </span>
          <span style="color: #CABB3D">{{getMemoryPower(props.recentGameInfo)}}KW.h</span>
          <el-tooltip effect="light" :content="mempower1 + '· ' + mempower2 + ' ≈ ' + mempower3+ $t('GameRebound.degree')">
            <el-icon size="16" color="#CABB3D" class="no-inherit">
              <QuestionFilled/>
            </el-icon>
          </el-tooltip>
        </div>
      </div>
    </div>

    <simple-data-charts :power-data="props.powerData" :hardwaerinfo="props.hardwaerinfo" :recentGameInfo="props.recentGameInfo" />
  </div>
</template>

<style scoped lang="scss">
@font-face{
  font-family: 'Quartz';
  src: url('../assets/QuartzRegular.ttf');
}
.ReboundDetailSimpleData {
  width: 1280px;
  height: calc(100vh - 81px);
  padding: 0 20px;
  display: flex;
  flex-flow: column nowrap;
  max-height: 750px;
  background: #22232e;

  .SimpleData-top {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    padding-top: 14px;
    padding-bottom: 15px;

    h1 {
      color: #1193F8;
      font-size: 16px;
    }

    .cost {
      margin-left: auto;
      display: flex;
      align-items: center;
      color: #717180;
      gap: 5px;

      .space {
        margin-right: 15px;
      }

      span {
        white-space: nowrap;
      }

      .power-color {
        color: #CABB3D;
      }

      .CO2-color {
        color: #aa523a;
      }
    }
  }

  .dashboard-list {
    display: flex;
    flex-flow: row nowrap;

    .dashboard-Frame, .dashboard-CPU, .dashboard-GPU, .dashboard-MEM {
      position: relative;
      padding: 0 38px;

      h2 {
        font-size: 18px;
        color: #BBBBBB;
        text-align: center;
        margin-bottom: 12px;
      }
      p {
        font-weight: bold;
      }
      .right-line {
        width: 2px;
        height: 90px;
        background: #555555;
        border-radius: 1px;
        position: absolute;
        top: 50px;
        right: 0;
      }

      .dashboard-content {
        display: flex;
        flex-flow: row nowrap;
        margin-bottom: 16px;
      }

      .description {
        color: #777777;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 5px;
        white-space: nowrap;
      }
    }

    .FPS, .FPS-low {
      width: 100px;
      height: 100px;
      border-radius: 50px;
      border: 1px dashed #FFB400;
      display: flex;
      flex-flow: column nowrap;
      align-items: center;
    }

    .FPS {
      justify-content: center;
      p {
        font-size: 12px;
        color: #777777;
      }

      h3 {
        font-family: Quartz;
        font-weight: 400;
        font-size: 32px;
        color: #FFB400;
      }

      h4 {
        font-weight: bold;
        font-size: 12px;
        color: #ffffff;
      }
    }

    .FPS-low {
      border-color: #777777;
      margin-left: 30px;
      color: #777777;
      justify-content: center;

      h4 {
        color: #ffffff;
        font-weight: bold;
        font-size: 16px;
      }
    }

    .dashboard-CPU {
      .cpu-usage,.cpu-temp {
        width: 100px;
        height: 100px;
        position: relative;
      }
      .dashboard-CPU-content {
        position: absolute;
        display: flex;
        flex-flow: column;
        justify-content: center;
        width: 100px;
        height: 100px;

        p {
          color: #ffffff;
          font-size: 16px;
          text-align: center;
          margin-bottom: 10px;
        }
        span {
          color: #777777;
          font-size: 12px;
          text-align: center;
        }
      }
      .cpu-temp {
        margin-left: 30px;
      }
    }

    .dashboard-GPU {
      .gpu-usage,.gpu-temp,.gpu-mem {
        width: 100px;
        height: 100px;
        position: relative;
      }
      .gpu-temp,.gpu-mem {
        margin-left: 30px;
      }

      .dashboard-GPU-content {
        position: absolute;
        width: 100px;
        height: 100px;
        display: flex;
        align-items: center;
        padding-top: 30px;
        flex-flow: column;

        p {
          color: #ffffff;
          font-size: 16px;
          font-weight: bold;
        }

        span {
          font-size: 12px;
          color: #777777;
        }
      }
    }

    .dashboard-MEM {
      display: flex;
      flex-flow: column nowrap;
      align-items: center;
      .mem-usage {
        width: 100px;
        height: 100px;
        position: relative;

        .dashboard-MEM-content {
          position: absolute;
          display: flex;
          flex-flow: column nowrap;
          justify-content: center;
          align-items: center;
          width: 100px;
          height: 100px;

          p {
            font-weight: bold;
            font-size: 18px;
            color: #FFFFFF;
          }

          span {
            font-size: 12px;
            color: #777777;
          }
        }
      }
    }
  }
}

.progress-circle {
  --percent: 0; /* 百分数 */
  --size: 100px; /* 尺寸大小 */
  --border-width: 5px; /* 环宽（粗细） */
  --inactive-color: #2D2E39; /* 辅助色 */
  --color: linear-gradient(#20A668, #D5BC49, #E44A30);
  position: absolute;
  width: var(--size);
  height: var(--size);
  border-radius: 50%;
}

.progress-circle.half-circle-left-box {
  // 切割右边盒子
  clip-path: polygon(0 0, 50% 0, 50% 100%, 0 100%);
}
.progress-circle.half-circle-right-box {
  // 切割左边盒子
  clip-path: polygon(50% 0, 100% 0, 100% 100%, 50% 100%);
}


/* SVG 容器 */
.progress-circle > svg {
  width: 100%;
  height: 100%;
  transform: rotate(90deg);
}
.progress-circle.half-circle-right-box > svg {
  transform: rotate(90deg) rotateX(180deg);
}
  /* 进度条圆环 */
.progress-circle circle {
  --r: calc((var(--size) - var(--border-width)) / 2);

  cx: calc(var(--size) / 2);
  cy: calc(var(--size) / 2);
  r: var(--r);
  fill: none;
  stroke-width: var(--border-width);
  stroke-linecap: butt;
  transition: all .5s;
  transition-delay: .4s;
}

.no-inherit {
  cursor: pointer;
}
</style>
