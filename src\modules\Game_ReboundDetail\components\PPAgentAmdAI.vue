<script setup lang="ts">
import {computed, onMounted, reactive} from 'vue'
import {useReboundDetailStore} from "@/modules/Game_ReboundDetail/stores/reboundDetail";
import {useI18n} from "vue-i18n";

const props = defineProps({
    no15Mins: {
        type: Boolean
    }
})
const {t} = useI18n()
const $store = useReboundDetailStore()
const data = reactive({
    isAMDAICPU: false,
    isCurNPUDriverVersion: false,
    integratedGraphicsOn: false,
    npuDriverVersion: "",
})

const canUse = computed(() => {
    return data.isAMDAICPU || data.isCurNPUDriverVersion || data.integratedGraphicsOn
})

const downloadingText = computed(() => {
    let str = t('reasoningModelFile') + ' '
    str += $store.ai_agent.downloadLLMProgress.percent.toFixed(2) + '%  '
    const total_num = $store.ai_agent.downloadLLMProgress.total_num
    const current_download_num = $store.ai_agent.downloadLLMProgress.current_download_num
    if (total_num !== 0) str += current_download_num + '/' + total_num
    return str
})
const downloadingExeText = computed(() => {
    let str = t('GameRebound.modelReasoningTool') + '.exe' + ' '
    str += $store.ai_agent.downloadExeProgress.percent + '%  '
    return str
})
const downloadMaskStyle = computed(()=>{
    if ($store.ai_agent.downloadingExe) {
        return {
            'transform': `translateX(${$store.ai_agent.downloadExeProgress.percent}%)`
        }
    }else {
        const total_num = $store.ai_agent.downloadLLMProgress.total_num
        const current_download_num = $store.ai_agent.downloadLLMProgress.current_download_num
        return {
            'transform': `translateX(${current_download_num / total_num * 100}%)`
        }
    }
})

onMounted(async () => {
    await checkEverythingIsReady()
    // 判断有无核显
    let HwInfoJsonStr = ''
    try {
        HwInfoJsonStr = await gamepp.hardware.getBaseJsonInfo.promise();
    } catch {
    }
    if (HwInfoJsonStr) {
        const HwInfo = JSON.parse(HwInfoJsonStr)
        checkIntegratedGraphics(HwInfo)
        checkIsAmdAICPU(HwInfo)
        if (HwInfo.NPU && HwInfo.NPU.SubNode) {
            try {
                HwInfo.NPU.SubNode.forEach((item: any) => {
                    if (item.DriverVersion && typeof item.DriverVersion === 'string') {
                        data.npuDriverVersion = item.DriverVersion
                        if (isVersionGreaterOrEqual(item.DriverVersion)) {
                            data.isCurNPUDriverVersion = true;
                            throw new Error('')
                        }
                    }
                })
            } catch (e) {

            }
        }
    }

    console.log(data)
})

async function checkEverythingIsReady() {
    const packageName = 'gppaiagent_ryzenai'
    const moduleExists = await gamepp.package.isexists.promise(packageName);
    if (moduleExists) {
        await gamepp.aiagent.runAIAgentClient.promise(0);
        $store.sendAIAgentMsg({"type": "CheckLLM"})
    }
}

function checkIsAmdAICPU(HwInfo: any) {
    const CPUSubNodes = HwInfo.CPU?.SubNode
    if (CPUSubNodes) {
        for (let i = 0; i < CPUSubNodes.length; i++) {
            const CPUINFO = CPUSubNodes[i];
            if (CPUINFO.ProcessorName && typeof CPUINFO.ProcessorName === 'string') {
                const CPUNAME = CPUINFO.ProcessorName.toLowerCase()
                if ((CPUNAME.includes('amd') || CPUNAME.includes('ryzen')) && CPUNAME.includes('ai')) {
                    data.isAMDAICPU = true
                }
            }
        }
    }
}

function checkIntegratedGraphics(HwInfo: any) {
    const GPUSubNodes = HwInfo.GPU.SubNode
    const Integrated = (GPUSubNodes).some((item: any) => Object.values(item).some(value => String(value).includes('Internal') || String(value).includes('Integrated')))
    if (Integrated) data.integratedGraphicsOn = true;
    (GPUSubNodes).some((item: any) => {
        if (item.VideoCard && typeof item.VideoCard === 'string') {
            const videocard = item.VideoCard.toLowerCase();
            const isAMD = videocard.includes('amd') || videocard.includes('radeon');
            // 判断是否为 AMD 核显
            const isAMDIntegrated = (
                isAMD && (
                    // 1. 名称包含 M 后缀（如 890M）
                    videocard.includes('m') ||
                    // 2. 显存 ≤ 2GB
                    (item.VideoMemoryNumber && item.VideoMemoryNumber <= 2048) ||
                    // 3. 显存描述包含 "shared" 或 "integrated"
                    (item.VideoMemory && item.VideoMemory.toLowerCase().includes('shared')) ||
                    // 4. 如 Radeon 8060S Graphics
                    videocard.includes('graphics') ||
                    videocard.includes('integrated') ||
                    videocard.includes('internal')
                )
            );
            if (isAMDIntegrated) {
                data.integratedGraphicsOn = true;
            }
        }
    })
}

function isVersionGreaterOrEqual(versionStr: string) {
    const targetVersion = [32, 0, 203, 257];
    const inputVersion = versionStr.split('.').map(Number);

    // 确保两个版本号长度一致（不足的部分补0）
    const maxLength = Math.max(targetVersion.length, inputVersion.length);
    while (targetVersion.length < maxLength) targetVersion.push(0);
    while (inputVersion.length < maxLength) inputVersion.push(0);

    // 逐位比较
    for (let i = 0; i < maxLength; i++) {
        if (inputVersion[i] > targetVersion[i]) {
            return true;
        } else if (inputVersion[i] < targetVersion[i]) {
            return false;
        }
    }

    // 所有位都相等
    return true;
}

function openDownloadDialog(onlyPath = false) {
    if (!canUse) return;
    $store.ai_agent.downloadDialogShowOnlyPath = onlyPath
    $store.ai_agent.downloadDialogShow = true;
}

function stopDownload() {
    $store.sendAIAgentMsg({"type": "CancelDownloadLLM"})
}

async function generateReport() {
    if (!canUse || props.no15Mins) return;
    $store.ai_agent.isGenerating = true;
    $store.ai_agent.RunLLMError = ""
    const str = $store.getSendStrAmdRyzenAI();
    $store.sendAIAgentMsg({
        "type":"RunLLM",
        "prompt":"<|im_start|>system\n" +
            "你是游戏加加AI特工，擅长通过游戏加加中性能统计功能提供的游戏帧数和硬件传感器数据分析此电脑在游戏时的状态。\n" +
            "\n" +
            "游戏加加(GamePP)是一款综合性软件，如果进行游戏加加的功能建议，你通常会介绍游戏加加的游戏内显示功能，此功能支持在游戏画面中实时显示游戏的帧数、CPU的占用率和温度、GPU占用率和温度、GPU的显存占用率和内存占用率等硬件传感器数据，帮助用户及时查看硬件状态。\n" +
            "\n" +
            "你通过下面几点对数据进行分析：\n" +
            "一. CPU是否存在温度过高？\n" +
            "一般情况下Intel的CPU温度不建议超过100℃，AMD的CPU不建议超过95℃，实际的温度限制与CPU型号有关。为了保障CPU的长时间稳定运行，CPU温度超过了设定的阈值，会强制CPU降低频率以降低功耗，进而帮助降低温度。\n" +
            "如果温度达到限制则会触发高温降频，影响游戏帧数，只要游戏时没有出现温度过高的事件则CPU不存在温度过高的问题。\n" +
            "如果是台式电脑出现温度影响CPU的性能，则推荐改善机箱风道、检查CPU的散热器和更换硅脂、清理散热器。\n" +
            "如果是笔记本电脑出现温度影响CPU性能，则推荐对笔记本电脑进行清灰、更换硅脂、降低室内温度（开启空调）。\n" +
            "\n" +
            "二. CPU性能是否足够？\n" +
            "如果CPU的占用率大于80%，说明当前CPU的计算性能不足，游戏的帧数可能受到CPU的计算性能限制，建议降低依赖CPU计算的游戏画面显示效果，例如降低同屏人数、物理效果、体积云(雾)等画面显示效果，在调整时可以开启游戏加加的游戏内显示功能并显示游戏的帧数和CPU的占用率辅助调整游戏的画面效果设置。\n" +
            "\n" +
            "三. 内存容量是否足够？\n" +
            "如果内存占用率长期大于90%，说明当前内存的容量不足，游戏的帧数受到内存的容量限制，建议适当调整游戏占用内存的画面设置选项，例如降低阴影、抗锯齿、画面渲染分辨率、各向异性过滤等画面设置选项，在游戏时可以开启游戏加加的游戏内显示功能并显示游戏的帧数和内存占用率辅助调整游戏的画面效果设置。\n" +
            "\n" +
            "四. GPU的显存容量是否足够？\n" +
            " 如果GPU的显存占用率长期大于95%，说明当前GPU的显存容量已经饱和，游戏的帧数受到GPU的显存容量限制，建议适当降低游戏依赖GPU显存的画面设置选项，例如降低纹理、屏幕空间反射、体积云(雾)、多重采样等游戏的画面显示效果，在游戏时可以开启游戏加加的游戏内显示功能并显示游戏的帧数和GPU的显存占用率辅助调整游戏的画面效果设置。\n" +
            "\n" +
            "五. GPU的性能是否完全释放？\n" +
            "1. 如果GPU的占用率大于90%，说明GPU性能基本上完全释放，GPU占用率越高说明GPU性能释放越好，游戏帧数越依赖GPU性能，游戏帧数没有受到CPU性能和内存性能等影响。一般情况下GPU完全释放时通常建议：\n" +
            "如果游戏平均帧数大于60，不建议进行任何游戏画面调整，当前游戏体验已经属于流畅；如果游戏平均帧数小于60，虽然GPU性能基本完全释放，但是GPU本身性能可能不足以支撑此游戏画面的计算量，建议降低依赖GPU计算性能的游戏画面显示效果，例如降低阴影、屏幕空间反射、抗锯齿、画面渲染分辨率等游戏的画面显示效果，使游戏帧数大于60，在游戏时可以开启游戏加加的游戏内显示功能并显示游戏的帧数和GPU的占用率辅助调整游戏的画面效果设置。\n" +
            "2. 如果GPU的占用率小于70%，说明GPU性能没有完全释放，GPU性能还有很大冗余，游戏帧数的瓶颈不在GPU上，如果游戏平均帧数大于60，不建议进行任何游戏画面调整，当前游戏体验已经属于流畅；如果游戏平均帧数小于60，建议检查其他影响游戏帧数的硬件，例如CPU单核/多核性能、内存性能等。\n" +
            "\n" +
            "以上分析的结果需要满足下面的要求：\n" +
            "1. 仅对当前硬件传感器数据和游戏帧数进行分析，不要出现任何包含升级CPU、升级GPU、更换GPU、通过软件调整GPU的核心频率与GPU的显存频率、在BIOS中调整CPU的频率、电压、CPU的温度限制、CPU的功耗限制等涉及CPU超频的信息，尤其是对CPU和GPU进行超频调整是风险极高的操作并且可能会导致CPU和GPU失去保修。\n" +
            "2. 不要出现任何游戏名称相关的信息和例子。\n" +
            "3. 你不推荐使用MSI Afterburner、HWiNFO和AIDA64等软件查看硬件传感器数据，你只推荐使用游戏加加(GamePP)，在你所有的描述中都不要出现MSI Afterburner、HWiNFO和AIDA64等与硬件相关软件。\n" +
            "4. 回复的内容中尤其是硬件相关信息不要出现提供的数据中不存在的内容或推测，尤其是CPU的热设计功耗和上限、GPU的热设计功耗和上限等数据。\n" +
            "5. 回复的内容中不要包含你采用的分析规则，也不要透露你作为阈值或限制的具体数值。\n" +
            "6. 回复的格式使用Markdown格式。\n" +
            "<|im_end|>\n" +
            "<|im_start|>user\n" +
            str +
            "<|im_end|>\n" +
            "<|im_start|>assistant"
    })
}
</script>

<template>
    <div class="PPAgentAmdAI">
        <div class="useTerms">
            <!--使用条件-->
            <p>{{ $t('GameRebound.useTerms') }}</p>
            <p>
                <span>{{ $t('GameRebound.term1') }}</span>
                <span class="green" v-if="data.isAMDAICPU">{{ $t('GameRebound.conformsTo') }}</span>
                <span class="red" v-if="!data.isAMDAICPU">{{ $t('GameRebound.notInLineWith') }}</span>
            </p>
            <p>
                <span>{{ $t('GameRebound.term2') }} <span class="blue">{{ data.npuDriverVersion }}</span></span>
                <span class="green" v-if="data.isCurNPUDriverVersion">{{ $t('GameRebound.conformsTo') }}</span>
                <span class="red" v-if="!data.isCurNPUDriverVersion">{{ $t('GameRebound.theVersionIsTooLow') }}</span>
            </p>
            <p>
                <span>{{ $t('GameRebound.term3') }}</span>
                <span class="green" v-if="data.integratedGraphicsOn">{{ $t('GameRebound.conformsTo') }}</span>
                <span class="red" v-if="!data.integratedGraphicsOn">{{ $t('GameRebound.notInLineWith') }}</span>
            </p>
        </div>

        <div class="download-box">
            <p class="cannotuse" v-if="!canUse">{{ $t('GameRebound.canNotUseAmdNpu') }}</p>
            <p class="cannotuse" v-if="$store.ai_agent.netError">{{ $t('GamePlusOne.networkError') }}</p>
            <p class="cannotuse" v-if="$store.ai_agent.downloadLLMProgress.error">
                {{ $t($store.ai_agent.downloadLLMProgress.error) }}</p>
            <p class="download-info" v-if="$store.ai_agent.downloading">{{ downloadingText }}</p>
            <p class="download-info" v-if="$store.ai_agent.downloadingExe">{{ downloadingExeText }}</p>
            <div class="download-btn-area" v-if="!$store.ai_agent.downloaded">
                <div class="download-btn" :class="{'disable': !canUse}" @click="openDownloadDialog(false)">
                    <div class="download-mask" v-show="$store.ai_agent.downloading || $store.ai_agent.downloadingExe" :style="downloadMaskStyle"></div>
                    <span v-if="canUse && !$store.ai_agent.downloading && !$store.ai_agent.downloadingExe">{{ $t('GameRebound.downloadTheFile') }}</span>
                    <span v-if="$store.ai_agent.downloading || $store.ai_agent.downloadingExe">{{ $t('GameRebound.downloading') }}</span>
                    <span v-if="!canUse">{{ $t('GameRebound.unusable') }}</span>
                </div>
                <div class="cancel-download" @click="stopDownload" v-if="$store.ai_agent.downloading">
                    <span>{{ $t('GameRebound.cancelDownload') }}</span>
                </div>
            </div>

            <p class="cannotuse" v-if="$store.ai_agent.RunLLMError">{{ $t($store.ai_agent.RunLLMError) }}</p>
            <p class="cannotuse" v-if="props.no15Mins">{{ $t('GameRebound.no15mins') }}</p>
            <div class="downloaded-area" v-if="$store.ai_agent.downloaded">
                <div class="generateReport" :class="{'disable': !canUse || props.no15Mins}" @click="generateReport">
                    <span>{{$t('')}}</span>
                    <span v-if="!$store.ai_agent.isGenerating">{{ $t('GameRebound.generateAReport') }}</span>
                    <span v-if="$store.ai_agent.isGenerating">{{ $t('GameRebound.agentIsThinking') }}</span>
                </div>
                <div class="filePath" @click="openDownloadDialog(true)">
                    <span>{{ $t('GameRebound.filePath') }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped lang="scss">
.PPAgentAmdAI {
    .useTerms {
        width: 600px;
        display: flex;
        flex-flow: column nowrap;
        gap: 22px;

        p {
            display: flex;
            flex-flow: row nowrap;
            font-size: 14px;

            span:first-child {
                width: 450px;
                margin-right: auto;
                white-space: normal;
            }

            span.blue {
                color: #66A6FF;
            }

            span.green {
                color: #46FFBE;
            }

            span.red {
                color: #F14343;
            }
        }
    }

    .cannotuse {
        color: #F14343;
        font-size: 12px;
        margin-bottom: 12px;
        text-align: center;
    }

    .download-box {
        display: flex;
        align-items: center;
        flex-flow: column nowrap;
        margin-top: 45px;

        .download-info {
            color: #478DED;
            white-space: nowrap;
            font-size: 12px;
            margin-bottom: 10px;
        }

        .downloaded-area {
            position: relative;

            .generateReport {
                min-width: 150px;
                height: 40px;
                line-height: 40px;
                border-radius: 4px;
                text-align: center;
                background: #336AB5;
                display: inline-block;
                cursor: pointer;
                overflow: hidden;
                position: relative;
                &.disable {
                    background: #393B4A;
                    cursor: no-drop;
                }
            }

            .filePath {
                position: absolute;
                left: calc(100% + 20px);
                top: 50%;
                transform: translateY(-50%);
                color: #478DED;
                text-decoration: underline #478DED;
                cursor: pointer;
                white-space: nowrap;
            }
        }

        .download-btn-area {
            position: relative;

            .download-btn {
                min-width: 150px;
                height: 40px;
                line-height: 40px;
                border-radius: 4px;
                text-align: center;
                background: #336AB5;
                display: inline-block;
                cursor: pointer;
                overflow: hidden;
                position: relative;

                span {
                    position: relative;
                    z-index: 3;
                }

                .download-mask {
                    position: absolute;
                    width: 100%;
                    height: 40px;
                    background: #393B4A;
                    z-index: 1;
                    transition: all 0.2s;
                    transform: translateX(0%);
                }

                &:hover {
                    background: #4f86da;
                }

                &.disable {
                    background: #393B4A;
                    cursor: no-drop;
                }
            }

            .cancel-download {
                position: absolute;
                left: calc(100% + 20px);
                top: 50%;
                transform: translateY(-50%);
                color: #F14343;
                text-decoration: underline #F14343;
                cursor: pointer;
                white-space: nowrap;
            }
        }
    }
}
</style>
