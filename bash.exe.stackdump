Stack trace:
Frame         Function      Args
0007FFFFABB0  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFABB0, 0007FFFF9AB0) msys-2.0.dll+0x2118E
0007FFFFABB0  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFAE88) msys-2.0.dll+0x69BA
0007FFFFABB0  0002100469F2 (00021028DF99, 0007FFFFAA68, 0007FFFFABB0, 000000000000) msys-2.0.dll+0x69F2
0007FFFFABB0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFABB0  00021006A545 (0007FFFFABC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFAE90  00021006B9A5 (0007FFFFABC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF96FA70000 ntdll.dll
7FF96DDF0000 KERNEL32.DLL
7FF96CB80000 KERNELBASE.dll
7FF96EEB0000 USER32.dll
7FF96D080000 win32u.dll
7FF96ED50000 GDI32.dll
7FF96CF60000 gdi32full.dll
000210040000 msys-2.0.dll
7FF96D410000 msvcp_win.dll
7FF96D5F0000 ucrtbase.dll
7FF96ED80000 advapi32.dll
7FF96E9F0000 msvcrt.dll
7FF96F070000 sechost.dll
7FF96D360000 bcrypt.dll
7FF96F3F0000 RPCRT4.dll
7FF96C280000 CRYPTBASE.DLL
7FF96D390000 bcryptPrimitives.dll
7FF96EE60000 IMM32.DLL
