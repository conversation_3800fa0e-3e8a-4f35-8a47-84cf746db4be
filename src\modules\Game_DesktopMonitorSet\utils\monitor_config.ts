import fgcp_bg from '../../../assets/img/monitoring/img3/fgcp_bg.webp';
export const defalutDatas: any[] = [
    {
        id: 1,
        window_settings: {
            size: {
                w: 468,
                h: 650,
            },
            background: { // 背景
                type: 'color', // 什么类型 color 颜色，img/video 图片/视频
                color: 'rgba(255, 255, 255, 0)',
                opacity: 1,
                img: "",
                video: "",
                img_video_name:"",
                img_video_display_type: "填充", // 拉伸/填充/适应
            },
            font: {
                font_family: "QuartzRegular",
            },
            position: {
                screen: "",  // 屏幕
                position: "upperRight",  // 位置
                x: -1,
                y: -1,
            }
        },
        components: []
    },
    {
        id: 2,
        window_settings: {
            size: {
                w: 310,
                h: 670,
            },
            background: { // 背景
                type: 'color', // 什么类型 color 颜色，img/video 图片/视频
                color: 'rgba(0, 0, 0, 0)',
                opacity: 1,
                img: "",
                video: "",
                img_video_name:"",
                img_video_display_type: "填充", // 拉伸/填充/适应
            },
            font: {
                font_family: "Hybriddd",
            },
            position: {
                screen: "",  // 屏幕
                position: "upperRight",  // 位置
                x: -1,
                y: -1,
            }
        },
        components: []
    },
    {
        id: 3,
        window_settings: {
            size: {
                w: 960,
                h: 360,
            },
            background: {
                type: 'img/video',
                color: 'rgba(0, 0, 0, 0)',
                opacity: 1,
                img: fgcp_bg,
                video: "",
                img_video_name:fgcp_bg,
                img_video_display_type: "填充",
            },
            font: {
                font_family: "AeeAndCui",
            },
            position: {
                screen: "", 
                position: "upperRight", 
                x: -1,
                y: -1,
            }
        },
        components: []
    },
]
