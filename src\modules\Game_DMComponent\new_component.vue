<template>
  <div class="new_component">
    <div class="main">
      <div class="left">
        <div class="Fordrag"></div>
        <p class="title">{{ $t('DesktopMonitoring.AddComponent') }}</p>
        <div class="list">
          <div class="expand-trigger" @click="togglePanel('expand1')">
            <!-- <i :class="['el-icon-arrow-down', { 'rotate-180': expandedPanels.expand1 }]"></i> -->
            <el-icon :class="{ 'rotate-180': expandedPanels.expand1 }">
              <ArrowDownBold />
            </el-icon>
            <span :title="$t('DesktopMonitoring.TextLabel')">{{ $t('DesktopMonitoring.TextLabel') }}</span>
          </div>
          <transition name="el-collapse">
            <div v-show="expandedPanels.expand1" class="expand-content">
              <div
                v-for="item in list1"
                :key="item.id"
                :class="['panel-item', { 'selected': isSelected('list1', item.id) }]"
                @click="selectItem('list1', item.id)"
                class="list-item"
              >
                {{ $t(item.name) }}
              </div>
            </div>
          </transition>
        </div>

        <div class="list">
          <div class="expand-trigger" @click="togglePanel('expand2')">
            <!-- <i :class="['el-icon-arrow-down', { 'rotate-180': expandedPanels.expand2 }]"></i> -->
            <el-icon :class="{ 'rotate-180': expandedPanels.expand2 }">
              <ArrowDownBold />
            </el-icon>
            <span :title="$t('DesktopMonitoring.ImageVideo')">{{ $t('DesktopMonitoring.ImageVideo1') }}</span>
          </div>
          <transition name="el-collapse">
            <div v-show="expandedPanels.expand2" class="expand-content">
              <div
                v-for="item in list2"
                :key="item.id"
                :class="['panel-item', { 'selected': isSelected('list2', item.id) }]"
                @click="selectItem('list2', item.id)"
                class="list-item"
              >
                {{  $t(item.name) }}
              </div>
            </div>
          </transition>
        </div>

        <div class="list">
          <div class="expand-trigger" @click="togglePanel('expand3')">
            <!-- <i :class="['el-icon-arrow-down', { 'rotate-180': expandedPanels.expand2 }]"></i> -->
            <el-icon :class="{ 'rotate-180': expandedPanels.expand3 }">
              <ArrowDownBold />
            </el-icon>
            <span :title="$t('DesktopMonitoring.SensorGraphics')">{{ $t('DesktopMonitoring.SensorGraphics') }}</span>
          </div>
          <transition name="el-collapse">
            <div v-show="expandedPanels.expand3" class="expand-content">
              <div
                v-for="item in list3"
                :key="item.id"
                :class="['panel-item', { 'selected': isSelected('list3', item.id) }]"
                @click="selectItem('list3', item.id)"
                class="list-item"
              >
                {{  $t(item.name) }}
              </div>
            </div>
          </transition>
        </div>
      </div>
      <div class="right">
        <div class="header">
          <div class="close" @click="close">
            <img src="../../assets/img/close.png" alt="">
          </div>
        </div>
        <div class="">
          <ComponentPage v-if="currentType" :type2="currentType" :htmlshow="htmlshow"   @toggleHtmlShow="htmlshow = !htmlshow" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref , onBeforeMount ,watch} from 'vue';
import ComponentPage from './components/ComponentPage.vue';
import {gamepp} from 'gamepp'
import {ArrowDownBold} from "@element-plus/icons-vue";
import { useLanguage } from '@/uitls/useLanguage';
useLanguage()
interface ListItem {
  id: number
  name: string
  type2: string,
  htmlshow?: boolean
}
const list1 = ref<ListItem[]>([
  { id: 1, name: 'DesktopMonitoring.SensorData', type2: 'sensor' },
  { id: 2, name: 'DesktopMonitoring.CustomText', type2: 'text' },
  { id: 3, name: 'DesktopMonitoring.DateTime', type2: 'time' }
])

const list2 = ref<ListItem[]>([
  { id: 1, name: 'DesktopMonitoring.Image', type2: 'img' },
  { id: 2, name: 'DesktopMonitoring.Video', type2: 'video' },
  { id: 3, name: 'DesktopMonitoring.SVG', type2: 'svg' }
])

const list3 = ref<ListItem[]>([
  { id: 1, name: 'DesktopMonitoring.ProgressBar', type2: 'progress', htmlshow: true },
  { id: 2, name: 'DesktopMonitoring.Graphics', type2: 'graphic', htmlshow: true },
  { id: 3, name: 'DesktopMonitoring.Chart', type2: 'chart', htmlshow: true },
])

const expandedPanels = ref({
  expand1: true,
  expand2: false,
  expand3: false
})

interface SelectedItems {
  list1: number | null;
  list2: number | null;
  list3: number | null;
}

const selectedItems = ref<SelectedItems>({
  list1: 1,
  list2: null ,
  list3: null
})

const currentType = ref<string | null>('sensor')
const htmlshow = ref<boolean>(false)
const zoomValue = ref(1)

onBeforeMount(async () => {
    try {
        const zoomWithSystem = gamepp.setting.getInteger.sync(313) // 是否点了跟随系统缩放开关
        if (zoomWithSystem === 1) {
            zoomValue.value = await gamepp.display.getScaleFromWindowInMonitor.promise();
            gamepp.webapp.windows.resize.sync('new_component', Math.floor(762 * zoomValue.value), Math.floor(512 * zoomValue.value))
        }
        gamepp.display.onDisplayMetricsChanged.addEventListener(async (scaleFactor: number) => {
            const zoomWithSystem = gamepp.setting.getInteger.sync(313)
            if (zoomWithSystem === 1) {
                zoomValue.value = scaleFactor
                gamepp.webapp.windows.resize.sync('new_component', Math.floor(762 * zoomValue.value), Math.floor(512 * zoomValue.value))
            }
        })
    } catch {

    }

})

watch(zoomValue, (newValue) => {
    (document.body.style as any).zoom = newValue + ''
})

const togglePanel =  (panelName: 'expand1' | 'expand2' | 'expand3') => {
  expandedPanels.value[panelName] = !expandedPanels.value[panelName]
}

const selectItem = (listName: string, itemId: number) => {
  Object.keys(selectedItems.value).forEach(key => {
    selectedItems.value[key as keyof typeof selectedItems.value] = key === listName ? itemId : null;
  });
  const currentList = listName === 'list1' ? list1.value : listName === 'list2' ? list2.value : list3.value;
  const selectedItem = currentList.find(item => item.id === itemId);
  currentType.value = selectedItem?.type2 || null;
  htmlshow.value = selectedItem?.htmlshow || false;
};

const isSelected = (listName: 'list1' | 'list2' | 'list3', itemId: number) => {
  return selectedItems.value[listName] === itemId;
};

const close = () => {
  gamepp.webapp.windows.close.sync('new_component')
};
</script>

<style scoped lang="scss">
.header .close:hover {
    background-color: #22232e;
}
.hover:hover{
  filter: brightness(1.2);
  -webkit-filter: brightness(1.2);
  -webkit-tap-highlight-color: transparent;
}
.Extrawidth{
  width: 188px!important;
}
.new_component{
  position: relative;
  width: 750px;
  height: 500px;
  border-radius: 4px;
  background: #2B2C37;
  box-shadow: 0 1px 6px #0009;
  margin-left: 6px;
  margin-top: 6px;
  .main{
    display: flex;
    align-items: center;
    .left{
      width: 150px;
      height: 500px;
      background: #393A49;
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
      .Fordrag{
        width: 100%;
        height: 30px;
        // app-region: drag;
      }
      .title{
        margin-left: 20px;
        color: #fff;
        font-size: 14px;
      }
      .list{
        .expand-trigger{
           padding-left: 20px;
           box-sizing: border-box;
           height: 30px;
           display: flex;
           align-items: center;
           font-size: 14px;
           color: #fff;
           cursor: pointer;
           transition: all 0.3s ease;
           span{
            margin-left: 10px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
           }
        }
        .expand-content{
          .list-item{
            height: 30px;
            display: flex;
            align-items: center;
            // justify-content: center;
            padding-left: 45px;
            font-size: 12px;
            color: #999;
            cursor: pointer;
            &:hover{
              background: rgba(83, 165, 247,.8);
              color: #fff;
            }
          }
          .selected{
            color: #fff;
            background: #409EFF;
          }
        }
      }
    }
    .right{
      width: 600px;
      height: 500px;
      .header{
        width: 100%;
        height: 30px;
        display: flex;
        align-items: center;
        // padding: 0 10px;
        box-sizing: border-box;
        -webkit-app-region: drag;
        .close{
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          margin-left: auto;
          -webkit-app-region: no-drag;
        }
      }
    }

    .expand-trigger:hover {
      // background-color: #e4e7ed;
    }

    .rotate-180 {
      transform: rotate(180deg);
      transition: transform 0.3s;
    }
    .el-collapse-enter-active,
    .el-collapse-leave-active {
      transition: all 0.3s ease;
    }

    .el-collapse-enter,
    .el-collapse-leave-to {
      opacity: 0;
      transform: translateY(-10px);
    }
  }




  .editor{
    display: flex;
    margin-left: 10px;
    .screen_content{
      width: 596px;
      height: 512px;
      border-radius: 4px;
      background: #202630;
      .list-title{
        width: 100%;
        display: flex;
        align-items: center;
        gap: 2px;
        .titledes{
          width: 100px;
          height: 30px;
          border-radius: 4px;
          background: #2F3643;
          font-size: 12px;
          color: #FFFFFF;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .add-monitor{
        width: 100%;
        border-radius: 4px;
        cursor: pointer;
        height: 30px;
        border: 1px dashed #505050;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 5px;
        .add-monitor-text{
          font-size: 12px;
          color: #999999;
          margin-left: 10px;
        }
        .custom-icon{
          font-size: 16px;
        }
        .custom-icon::before {
          color: #999999;
        }
      }
      .Sensor_data_list{
        width: 100%;
        margin-top: 2px;
        overflow-y: auto;
        overflow-x: hidden;
        height: 440px;
        li{
          width: 100%;
          height: 30px;
          display: flex;
          cursor: pointer;
          border: 1px solid transparent;
          .wide{
            width: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #fff;
          }
          .operation{
            gap: 10px;
            i{
              cursor: pointer;
            }
          }
          &:hover{
            border: 1px dashed #7389B2;
          }
        }
        .highlighted {
          border: 1px dashed #409EFF;
        }
      }
    }
    .Settings{
      width: 596px;
      height: 512px;
      border-radius: 4px;
      background: #202630;
      padding: 20px 20px 10px 20px;
      box-sizing: border-box;
      .goback{
        display: inline-flex;
        align-items: center;
        color: #409EFF;
        font-size: 12px;
        cursor: pointer;
        // margin: 15px 0 0 10px;
      }
      .Set_item{
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 20px;
        p{
          font-size: 12px;
          color: #999999;
        }
        .itemdata{
          display: flex;
          align-items: center;
          gap: 20px;
          .displacement{
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #fff;
            .Colorbox{
              margin-right: 10px;
            }
             span{
              margin-right: 10px;
             }
             strong{
              margin-left: 5px;
              font-weight: normal;
             }
            .Inputcontent{
              width: 70px;
              height: 28px;
              border-radius: 4px;
              background: #171B20;
              border: none;
            }
            .single-des{
              width: 220px;
              height: 28px;
              border-radius: 4px;
            }
          }
        }
      }
    }
  }
  .preview{
    position: relative;
    margin-left: 10px;
    .preview_header{
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: absolute;
      width: 100%;
      // z-index: 999;
      p{
        font-size: 14px;
        color: #fff;
      }
      .Drop-down_box{
        display: flex;
        align-items: center;
        position: relative;
        z-index: 999;
        cursor: pointer;
        span{
          color: #999999;
          font-size: 12px;
          min-width: 70px;
        }
      }
    }
    .Screensize{
      width: 640px;
      height: 480px;
      border: 1px solid #666666;
      border-radius: 4px;
      box-sizing: border-box;
      margin-top: 3px;
      position: relative;
      overflow: visible;
    }
  }
}
</style>
<style lang="scss">
.monitoring-module {
  position: absolute;
  will-change: transform; 
  touch-action: none;
}
.scroll::-webkit-scrollbar {
    width: 5px;
    transition: 0.25s;
}
.scroll::-webkit-scrollbar-thumb {
    background: #71738C;
    border-radius: 3px;
}
.scroll::-webkit-scrollbar-thumb:hover {
    background-color: #71738C;
}
.scroll::-webkit-scrollbar-track {
    background-color: #2D2E39;
    width: 2px;
}
.preview .el-tabs__header{
  border-bottom: 0 solid #E4E7ED;
  margin: 0 0 0 45px;
}
.preview  .el-tabs__item{
  height: 30px;
  line-height: 30px;
}
.preview .el-tabs--card>.el-tabs__header .el-tabs__item.is-active{
  border-bottom-color:transparent;
  background: #354054;
  border-radius: 4px;
}
.preview .el-tabs--card>.el-tabs__header .el-tabs__nav{
  border: 0 solid #E4E7ED;
}
.preview .el-tabs--card>.el-tabs__header .el-tabs__item{
  border-left: 0 solid #E4E7ED;
}
.Inputcontent  .el-input__inner{
  width: 70px;
  height: 28px;
  border-radius: 4px;
  background: #171B20;
  border: none;
  color: #fff;
  text-align: center;
}
.single-des .el-input__inner{
  width: 220px;
  height: 28px;
  border-radius: 4px;
  background: #171B20;
  border: none;
  color: #fff;
  text-align: center;
}
.Inputsize .el-input__inner{
  width: 100px;
  height: 28px;
  border-radius: 4px;
  background: #171B20;
  border: none;
  color: #fff;
  text-align: center;
}
.Set_item .el-checkbox{
  color: #fff;
}
.Colorbox .el-input__inner{
  width: 180px;
  height: 28px;
  border-radius: 4px;
  background: #171B20!important;
  color: #fff;
  border: none;
  padding: 0 10px;
}
.displacement .el-color-picker__trigger{
  width: 30px;
  height: 30px;
}
.displacement .el-color-picker{
  height: 30px;
}
.displacement .el-button{
  width: 220px;
  height: 28px;
  border-radius: 4px;
  background: #354054;
  color: #fff;
  padding: 0;
  font-weight: 400;
  border: none;
}
.displacement .el-radio{
  color: #fff;
}
.displacement .el-slider{
  width: 300px;

}
.displacement .el-slider__bar{
  z-index: 9;
}
.displacement .el-slider__runway{
  background-color: #3E4050;
}
.preview .el-tabs__item{
  color: #fff;
}
.displacement .font-select .el-input__inner{
  width: 220px;
  height: 28px;
  border-radius: 4px;
  background: #354054;
  color: #fff;
  font-size: 12px;
  line-height: 28px;
  border: none;
}
.displacement .font-select .el-input__icon{
  line-height: 28px;
}

</style>
