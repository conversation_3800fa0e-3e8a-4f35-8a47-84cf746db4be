<script setup lang="ts">
import {ref, onMounted, onUnmounted, reactive, watch, watchEffect, nextTick, computed,inject} from 'vue';
import {ListItem, MonitorStyle, FontData, SensorData, Overall, Fps} from "@/modules/Game_Home/types/InGameMonitor";
import {gameppBaseSetting,hardware,getInGameList, saveInGameList} from "@/modules/Game_Home/stores";
import {findValueByKeyAndType, SensorAddAverageData} from "@/uitls/sensor";
import {Operation, ArrowLeft, ArrowRight, CirclePlus, Plus} from "@element-plus/icons-vue";
import { useZoomStore } from '../stores/zoomStore';
import { gamepp } from "gamepp"
import { ElMessage } from 'element-plus';
import shortcut from '@/components/eleCom/shortcut.vue'
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

let addType = ref('')
const selectedItem = ref(1);
let XYPosition = ref('InGameMonitor.custom')
const positionBox1 = ref(null);
const zoomStore = useZoomStore();
const windowHeight = ref(0);
const windowWidth = ref(0);
const singleFamily = ref('')
const singleBold = ref(false)
const singleShadow = ref(false)
const singleDes = ref<string | number>('')
const lineSize = ref(2)
const lineColor = ref('rgb(255,255,255)')
const lineHeight = ref(50)
let chooseList = ref(-1)
const methodText = ref('InGameMonitor.text')
const methodWay = ref(0)
let singleName = ref<string | number>('');
let singleUnit = ref<string>('');
let singleKey  = ref<string | number | undefined>('');
let singletype = ref<string | number>('');
let singleTitle = ref<string | number>('');
const singleColor = ref('rgb(255,255,255)')
const singleTitleColor = ref('rgb(255,255,255)')
const listIndex = ref(0)
const changeCustom = (index:number) => {
  listIndex.value = index

}
const allDeviceName = ['Valkyrie 99 磁轴','Valkyrie M1 鼠标','PRO WIRELESS','PRO X SUPERLIGHT','PRO X SUPERLIGHT 2','PRO 2 LIGHTSPEED','PRO X WIRELESS','Arctis GameBuds Case','Arctis GameBuds Left','Arctis GameBuds Right','Arctis 7P Plus']

const ingame_hotkey_setting = ref('')
const ingame_hotkey = ref('')
const singleSize = ref(12)
const singleTitleSize = ref(12)
const currentPositionText = ref('');
const positionElements = ref<HTMLElement[]>([]);
const currentPosition = ref('');
const predefineColors = ref([
  '#ff4500',
  '#ff8c00',
  '#ffd700',
  '#90ee90',
  '#00ced1',
  '#1e90ff',
  '#c71585',
  'rgba(255, 69, 0, 0.68)',
  'rgb(255, 120, 0)',
  'hsv(51, 100, 98)',
  'hsva(120, 40, 94, 0.5)',
  'hsl(181, 100%, 37%)',
  'hsla(209, 100%, 56%, 0.73)',
  '#c7158577',
])
const singleColorDisplay =  ref(singleColor.value.toUpperCase());
const lineColorDisplay =  ref(lineColor.value.toUpperCase());
const singledesshow = ref(true)
let bgcolorpicker = ref()
const listNumber = ref(1);
let isMythCoolRunning = ref<boolean>(false);
const pollInterval = 4000;
let pollTimer: number | null = null;
const SensorbuttonColor = ref('rgb(255,255,255)')
let single_setting_monitor_item:any = {} ;
let single_setting_monitor_lock:any = false ;
const isObsExists = ref(true);
const obsDownloading = ref(false);
const single_setting_monitor = reactive({
	switch: false,
	condition:"biggerThanthreshold", // 条件
	threshold:2, // 大于/小于 xx% 阈值
	initial_threshold:0, // 初始阈值
	cur_threshold:0, // 当前阈值
	is_update_threshold: false, // 触发后是否更新阈值
	wait_time: 10, // 等待时间 s
	do_what: "replay", // 触发功能
	is_show_threshold:false, // 是否显示当前用于回放的阈值
	is_init_threshold_big1000:false, // 是否显示超过初始阈值1000的次数
})
//初始化位置样式
const initPosition = async () => {
    XYPosition.value = 'InGameMonitor.custom';
    let location = window.localStorage.getItem('ingame_monitor_location');
    if (location !== null) {
        let locationJson = JSON.parse(location);
        XYPosition.value = locationJson.position;
        switch (XYPosition.value) {
            case "upperLeft":
                activatePosition(0);
                XYPosition.value = 'InGameMonitor.upperLeft'
                break;
            case "upper":
                activatePosition(1);
                 XYPosition.value = 'InGameMonitor.upper'
                break;
            case "upperRight":
                  activatePosition(2)
                  XYPosition.value =  'InGameMonitor.upperRight'
                break;
            case "left":
                  activatePosition(3)
                  XYPosition.value = 'InGameMonitor.Left'
                break;
            case "middle":
                 activatePosition(4)
                 XYPosition.value = 'InGameMonitor.middle'
                break;
            case "right":
                activatePosition(5)
                XYPosition.value = 'InGameMonitor.Right'
                break;
            case "lowerLeft":
                activatePosition(6)
                XYPosition.value = 'InGameMonitor.lowerLeft'
                break;
            case "lower":
                activatePosition(7)
                XYPosition.value = 'InGameMonitor.lower'
                break;
            case "lowerRight":
                activatePosition(8)
                XYPosition.value = 'InGameMonitor.lowerRight'
                break;
            default:
                break;
        }
    } else {
        //初次安装设置默认位置
        let XPositionConf = await gamepp.setting.getFloat.promise(130);
        let YPositionConf = await gamepp.setting.getFloat.promise(131);
        if (XPositionConf === 0 && YPositionConf === 0) {
          XYPosition.value = 'InGameMonitor.custom';
        }else{
          if (positionBox1.value) {
            const positionBoxes = positionBox1.value.querySelectorAll('.positionBox');
            positionBoxes.forEach((box: HTMLElement) => box.classList.remove('positionActive'));
          }
        }
    }
    // window.localStorage.setItem('ingame_monitor_location', JSON.stringify(Obj));
}
const activatePosition = (index: number) => {
  if (positionBox1.value) {
    const positionBoxes = positionBox1.value.querySelectorAll('.positionBox');
    positionBoxes.forEach((box: HTMLElement) => box.classList.remove('positionActive'));
    positionBoxes[index].classList.add('positionActive');
  }
};

// let latestPositionSettings  = ref(null)
interface PositionSettings {
  element: HTMLElement;
  site: string;
  send_id: number;
  index: number;
}
const latestPositionSettings = ref<null | PositionSettings>(null);
const setPosition = async (element: HTMLElement, site: string, send_id: number, index:number) => {
  const positionActiveClass = 'positionActive';

  // 移除所有元素的激活状态
  positionElements.value.forEach(el => {
    el.classList.remove(positionActiveClass);
  });

  // 设置当前元素的激活状态
  element.classList.add(positionActiveClass);
  overallSetting.value.position = index
  overallSetting.value.site = site
  // 获取窗口信息
  // const PrimaryDisplay = await gamepp.game.getCurrentConnectedClient.sync();
  // const PresentBounds = await gamepp.webapp.windows.getBounds.sync('ingame_monitor');

  // const DefaultWidth = PrimaryDisplay.viewportWidth - PresentBounds.width;
  // const DefaultHeight = PrimaryDisplay.viewportHeight - PresentBounds.height;
  // const halfWidth = Math.floor((PrimaryDisplay.viewportWidth / 2) - (PresentBounds.width / 2));
  // const halfHeight = Math.floor((PrimaryDisplay.viewportHeight / 2) - (PresentBounds.height / 2));
  let x = 0;
  let y = 0;

  switch (site) {
    case 'upperLeft':
      currentPositionText.value = 'Upper Left';
      XYPosition.value = 'InGameMonitor.upperLeft'
      break;
    case 'upper':
      // x = halfWidth;
      currentPositionText.value = 'Upper';
      XYPosition.value = 'InGameMonitor.upper'
      break;
    case 'upperRight':
      // x = DefaultWidth;
      currentPositionText.value = 'Upper Right';
      XYPosition.value ='InGameMonitor.upperRight'
      break;
    case 'left':
      // y = halfHeight;
      currentPositionText.value = 'Left';
      XYPosition.value ='InGameMonitor.Left'
      break;
    case 'middle':
      // x = halfWidth;
      // y = halfHeight;
      currentPositionText.value = 'Middle';
      XYPosition.value ='InGameMonitor.middle'
      break;
    case 'right':
      // x = DefaultWidth;
      // y = halfHeight;
      currentPositionText.value = 'Right';
      XYPosition.value = 'InGameMonitor.Right'
      break;
    case 'lowerLeft':
      // y = DefaultHeight;
      currentPositionText.value = 'Lower Left';
      XYPosition.value = 'InGameMonitor.lowerLeft'
      break;
    case 'lower':
      // x = halfWidth;
      // y = DefaultHeight;
      currentPositionText.value = 'Lower';
      XYPosition.value = 'InGameMonitor.lower'
      break;
    case 'lowerRight':
      // x = DefaultWidth;
      // y = DefaultHeight;
      currentPositionText.value = 'Lower Right';
      XYPosition.value = 'InGameMonitor.lowerRight'
      break;
    default:
      break;
  }
  console.log(site)
  console.log('改变了位置')
  // const refs = ['box1', 'box2', 'box3', 'box4', 'box5', 'box6', 'box7', 'box8', 'box9'];
  let elementRef = element.getAttribute('data-ref') || '';
  latestPositionSettings.value = { elementRef, site, send_id, index };
  window.localStorage.setItem('latestPositionSettings', JSON.stringify(latestPositionSettings.value));

  const Obj = { position: site, x, y };
  window.localStorage.setItem('ingame_monitor_location', JSON.stringify(Obj));
  if(gamepp.webapp.windows.isValid.sync('ingame_monitor')) {
    // gamepp.webapp.windows.setPositionDesktopMonitor.sync("ingame_monitor", x, y, 1);
    gamepp.webapp.sendInternalAppEvent.promise('ingame_monitor', 'ingame_monitor_position');
  }
  await gamepp.utils.sendstatics.promise(send_id);
};
const $gamepp = gameppBaseSetting()
const $store = hardware()
const ingame_now = ref(false);
let InGameList = ref<SensorData[][]>([[]])
const ingameListNameNumber = ref(parseInt(localStorage.getItem('ingameListNameNumber') || '1', 10));
const ingamelistName = async () =>{
  let SensorInfoStr = null
  let cpuType = 'intel'
    try {
      SensorInfoStr = await gamepp.hardware.getSensorInfo.promise()
    } catch (err) {
      return
    }
    if (SensorInfoStr === '') {
        ingamelistName()
      return
    }

  // 找出 bg_sensor_data.gpu_list 中有数据的下标
  const bg_sensor_data_str = localStorage.getItem('bg_sensor_data');
  let bg_sensor_data;
  if(bg_sensor_data_str){
    bg_sensor_data = JSON.parse(bg_sensor_data_str);
  }else{
    ingamelistName()
    return
  }
  const dataIndices = bg_sensor_data.gpu_list.reduce((acc: any[], current: {}, index: any) => {
    if (Object.keys(current).length !== 0) {
      acc.push(index);
    }
    return acc;
  }, []);

  const SensorInfoOriginal = JSON.parse(SensorInfoStr);
  const SensorInfoKeys = Object.keys(SensorInfoOriginal)
  console.log('SensorInfoOriginal', SensorInfoOriginal)
  SensorInfoKeys.forEach(function (item) {
    if (item.startsWith('CPU')) {
      if (String(item).toLowerCase().includes('intel')) {
        cpuType = 'intel'
      } else {
        cpuType = 'amd'
      }
    }
  })
  let SensorInfo = SensorAddAverageData(SensorInfoOriginal, SensorInfoKeys)
  let chooseSensor_list_v1data = JSON.parse(window.localStorage.getItem('chooseSensor_list_v1'));
  console.log('chooseSensor_list_v1data', chooseSensor_list_v1data)
 if(ingameListNameNumber.value === 1){
    // 更新 CPU 传感器选中的数据 或默认 name 传给 InGameList
    if (chooseSensor_list_v1data && chooseSensor_list_v1data.cpuData) {
    chooseSensor_list_v1data.cpuData.forEach((item: { name: string | undefined; isDef: any; keyName: any; }) => {
      const key = item.name === 'temp' ? 'temperature' : item.name;
      const index = InGameList.value.findIndex((subList) =>
          Array.isArray(subList) && subList.some((subItem) => subItem.bg === item.name && subItem.name.startsWith('CPU'))
      );
      console.log('CPU index:', index);
      if (index !== -1) {
        if (item.isDef) {
          let newValue = findValueByKeyAndType(SensorInfo, 'CPU', false, key);
          let sensorType = 'CPU';
          singletype.value = 'cpuData'
          // 根据硬件类型和传感器类型获取值
          if (key === 'temperature') {
            if (cpuType === 'intel') { // Intel CPU 优先选择 CPU Package 或者 Core Max
              let cpuPackageTemp = findValueByKeyAndType(SensorInfo, sensorType, 'CPU Package', key);
              if (cpuPackageTemp[1] !== '') {
                newValue = cpuPackageTemp;
              } else {
                let coreMaxTemp = findValueByKeyAndType(SensorInfo, sensorType, 'Core Max', key);
                if (coreMaxTemp[1] !== '') {
                  newValue = coreMaxTemp;
                }
              }
            } else { // AMD CPU 优先选择 CPU Die (average) 或者 CPU (Tctl/Tdie)
              let cpuDieTemp = findValueByKeyAndType(SensorInfo, sensorType, 'CPU Die (average)', key);
              if (cpuDieTemp[1] !== '') {
                newValue = cpuDieTemp;
              } else {
                let tctlTemp = findValueByKeyAndType(SensorInfo, sensorType, 'CPU (Tctl/Tdie)', key);
                if (tctlTemp[1] !== '') {
                  newValue = tctlTemp;
                }
              }
            }
            if (!newValue || newValue[1] === '') { // 都没找到
              newValue = findValueByKeyAndType(SensorInfo, sensorType, false, key);
            }
          } else if(key === 'usage'){
            newValue = findValueByKeyAndType(SensorInfo, sensorType, 'Total CPU Usage', key);
          }else {
            newValue = findValueByKeyAndType(SensorInfo, sensorType, false, key);
          }
          if (newValue && newValue.length > 0) {
            InGameList.value[index].forEach(subItem => {
              if (subItem.bg === item.name && subItem.name.startsWith('CPU')) {
                subItem.name = newValue[0];
              }
            });
          }
        } else {
          InGameList.value[index].forEach(subItem => {
            if (subItem.bg === item.name && subItem.name.startsWith('CPU')) {
              subItem.name = `${item.keyName}`;
            }
          });
        }
      } else {
        console.warn(`未找到匹配的CPU ${item.name}`);
      }
    });
    }
   // 更新 GPU 传感器选中的数据 或默认 name 传给 InGameList
    dataIndices.forEach((index: number) => {
    // 根据索引动态获取 gpuData 的键名
    const gpuDataKey = `gpu${index}Data`;
    singletype.value = gpuDataKey
    console.log('gpuDataKey', gpuDataKey)
    const gpuData = chooseSensor_list_v1data[gpuDataKey];
    if (!gpuData) return;

    gpuData.forEach((item: { name: string; isDef: any; describe: any; keyName: any; }) => {
      const key = item.name === 'temp' ? 'temperature' : item.name;
      const sensorIndex = `GPU [#${index}]`;

      const index2 = InGameList.value.findIndex((subList) =>
          Array.isArray(subList) && subList.some((subItem) => subItem.bg === item.name && subItem.name.startsWith('GPU'))
      );
      if (index2 !== -1) {
        if (item.isDef) {
          const newValue = findValueByKeyAndType(SensorInfo, sensorIndex, false, key);
          console.log(key, newValue);
          InGameList.value[index2].forEach(subItem => {
            if (subItem.bg ===  item.name && subItem.name.startsWith('GPU')) {
              // subItem.name = `${sensorIndex} ${item.describe}: ${newValue[0]}`;
              subItem.name = newValue[0];
            }
          });
        } else {
          InGameList.value[index2].forEach(subItem => {
            if (subItem.bg === item.name && subItem.name.startsWith('GPU')) {
              subItem.name = `${item.keyName}`;
            }
          });
        }
      } else {
        console.warn(`未找到匹配的${sensorIndex} ${item.name}`);
      }
    });
    });
    ingameListNameNumber.value = 2
    localStorage.setItem('ingameListNameNumber', ingameListNameNumber.value.toString());
 }

  localStorage.setItem('InGameList1', JSON.stringify(InGameList.value))
}


const init = async () =>
{
  ingame_now.value = gamepp.webapp.windows.getTitle.sync() !== '游戏加加游戏内主窗口';
}
// const customList = ref<ListItem[]>([{title:'第一列',data:{}}])
// const addList = () => {
//   const newTitle = `第${customList.value.length + 1}列`
//   customList.value.push({ title: newTitle, data: {} })
//   InGameList.value.push([])
// }
// const deleteList = (index:number) => {
//   if(index === 0) {
//     ElMessage('至少保留一个配置')
//     return
//   } else {
//     customList.value.splice(index, 1)
//     InGameList.value.splice(index, 1)
//   }
// }

const initHotkey = async () => {
  const setDefaultHotkey = async (key: number, defaultValue: string) => {
    const value = await gamepp.setting.getString.promise(key);
    return (value === '无' || value === 'None_cn') ? defaultValue : value;
  };

  ingame_hotkey_setting.value = await setDefaultHotkey(9, '无');
  ingame_hotkey.value = await setDefaultHotkey(15, '无');
};

const setAllDef = () => {
  InGameList.value[0].forEach(item => {
    item.fontStyle = 12
    item.TitlefontStyle = 12
    item.fontBold = false
    item.fontSize = 'Microsoft YaHei'
    item.methodWay = 0
    item.color = 'rgb(255,255,255)'
    item.titlecolor = 'rgb(255,255,255)'
    item.top = false
    item.lineColor = 'rgb(255,255,255)'
    item.lineHeight = 30
    item.lineSize = 2
    item.textShadow = false
    item.desshow = true
    if (item.bg === 'FPS 0.1% Low' || item.bg === 'Current time' || item.bg === 'Run time' || item.bg === 'power' || (item.title === 'GPU' && item.bg === 'clock')) {
      item.show = false;
      item.showOnlyHide = true
      // item.group = true
    }else {
      item.show = true;
      item.showOnlyHide = false;
    }
  })
  overallSetting.value.bgColor = 'rgba(0,0,0,0.3)'
  overallSetting.value.fontFamily = 'Microsoft YaHei'
  overallSetting.value.fontSize = 12
  overallSetting.value.fontColor = 'rgba(255,255,255,1)'
  overallSetting.value.fontStyle = false
  overallSetting.value.textShadow = false
  methodWay.value = 0
  //双向绑定数据恢复默认
  color1.value = 'rgba(0,0,0,0.3)'
  selected.value = 'Microsoft YaHei'
  textSize.value = 12
  color2.value = 'rgb(255,255,255)'
  font_style.value = false
  text_Shadow.value = false

  options[0].checked = true
  options[1].checked = false
  selectedItem.value = 1;
  localStorage.setItem('ingameArrangement', t('InGameMonitor.horizontal'))
  // formatColor('overallSetting.bgColor');
  // formatColor('color1');
  // formatColor('color2');
  // formatColor('singleColor');
  // formatColor('lineColor');
  localStorage.setItem('InGameList1', JSON.stringify(InGameList.value));
  localStorage.setItem('IngameAllSetting', JSON.stringify(overallSetting.value));

}
const setDef = () => {
    const itemData = InGameList.value[listIndex.value][chooseList.value];
    itemData.fontStyle = 12
    itemData.fontBold = false
    itemData.fontSize = 'Microsoft YaHei'
    itemData.methodWay = 0
    itemData.color = 'rgb(255,255,255)'
    itemData.top = false
    itemData.lineColor = 'rgb(255,255,255)'
    itemData.lineHeight = 30
    itemData.lineSize = 2
    itemData.textShadow = false
    itemData.titlecolor = 'rgb(255,255,255)'
    methodText.value = 'InGameMonitor.text'
    methodWay.value = 0
    singleFamily.value = 'Microsoft YaHei'
    singleSize.value = 12
    singleTitleSize.value = 12
    singleColor.value = 'rgb(255,255,255)'
    singleTitleColor.value = 'rgb(255,255,255)'
    singleBold.value = false
    singleShadow.value = false
    lineColor.value = 'rgb(255,255,255)'
    lineSize.value = 2
    lineHeight.value = 30
    singledesshow.value = true
  // formatColor('color1');
  // formatColor('color2');
  // formatColor('singleColor');
  // formatColor('lineColor');
  if (itemData.bg === 'FPS 0.1% Low' || itemData.bg === 'Current time' || itemData.bg === 'Run time' || itemData.bg === 'power' || (itemData.title === 'GPU' && itemData.bg === 'clock')) {
    itemData.show = false;
    itemData.showOnlyHide = true;
    console.log('itemData.showOnlyHide', itemData.showOnlyHide)
  } else {
    itemData.show = true;
    itemData.showOnlyHide = false;
  }
  localStorage.setItem('InGameList1', JSON.stringify(InGameList.value));
}
const getWindowSize = () => {
  windowWidth.value = window.innerWidth;
  windowHeight.value = window.innerHeight;
}
const allAlphaChange = (value:number) => {
  overallSetting.value.Alpha = value
  InGameList.value
}
const allBgChange = (value: string) => {
  overallSetting.value.bgColor = value
  // color1.value = color1.value.toUpperCase();

};



// 将颜色值转换为大写
// const formatColor = (colorRefName: string | number) => {
//   const colorRef = refMap[colorRefName];
//   if (colorRef) {
//     colorRef.value = colorRef.value.toUpperCase();
//   }
// };
const allFontFamilyChange = (value: any) => {
  InGameList.value.forEach(innerArray => {
    innerArray.forEach(item => {
      item.fontSize = value;
    });
  });
  overallSetting.value.fontFamily = value
}
const changeView = (index: number) => {
  const currentList = InGameList.value[listIndex.value];
  // 检查当前列表中可见的传感器数量
  const visibleCount = currentList.filter(item => item.show).length;
  if (currentList[index].show && visibleCount <= 1) {
    ElMessage({
      message: '至少保留一个可见传感器',
      type: 'warning',
    });
    return;
  }
  console.log(currentList[index].show);
  currentList[index].show = !currentList[index].show;
  currentList[index].showOnlyHide = !currentList[index].showOnlyHide;
  localStorage.setItem('InGameList1', JSON.stringify(InGameList.value));
}
const changeTop = (index:number) => {
  indexKey.value = index;
  if (indexKey.value > 0) {
    const currentList = InGameList.value[0];
    currentList.unshift(...currentList.splice(indexKey.value, 1));
    indexKey.value = 0;
    localStorage.setItem('InGameList1', JSON.stringify(InGameList.value));
  }
}
const deleteSensor = (index:number) => {
  // Back()
  console.log('删除传感器')
  allSetting.value = true
  singleSetting.value = false
  InGameList.value[listIndex.value].splice(index, 1)
  localStorage.setItem('InGameList1', JSON.stringify(InGameList.value));
}

const allFontsizeChange = (value: any) => {
  InGameList.value.forEach(innerArray => {
    innerArray.forEach(item => {
      item.fontStyle = value;
      item.TitlefontStyle = value
    });
  });
  overallSetting.value.fontSize = value
}
const allFontcolorChange = (value: any) => {
  InGameList.value.forEach(innerArray =>
      innerArray.forEach(item => {
        item.color = value;
        item.titlecolor = value
      })
  )
  overallSetting.value.fontColor = value
  // color2.value = color2.value.toUpperCase();
}
const allBoldChange = () => {
  InGameList.value.forEach(innerArray => {
    innerArray.forEach(item => {
      item.fontBold = font_style.value;
    });
  });
  overallSetting.value.fontStyle = font_style.value
}

const allStrokeChange = () => {
  InGameList.value.forEach(innerArray => {
    innerArray.forEach(item => {
      item.textShadow = text_Shadow.value;
    });
  });
  overallSetting.value.textShadow = text_Shadow.value
}

let MonitorButton = ref<Array<string>>(['InGameMonitor.CustomMode','InGameMonitor.Developing'])
const options = reactive([
  { name: 'InGameMonitor.horizontal', id: 1, checked: true },
  { name: 'InGameMonitor.vertical', id: 2, checked: false }
]);
const bgAlpha = ref(0)
const color1 = ref('rgba(0, 0, 0, 0.3')
const color2 = ref('rgba(255,255,255,.1)')
const textSize = ref(14)
let fontValues = ref<{ fontFamily: string; variants: string[] }[]>([]);
let showMethod = ref([
  {name: 'InGameMonitor.text', value: 0},
  {name: 'InGameMonitor.textLine', value: 1}
]);
let showRange = ref([
  {name: 'InGameMonitor.ColorCondition1', value: 0},
  {name: 'InGameMonitor.ColorCondition2', value: 1},
  {name: 'InGameMonitor.ColorCondition3', value: 2}
])
// const refMap = {
//   color1: color1,
//   color2: color2,
//   singleColor: singleColor,
//   lineColor: lineColor,
// };
let selected = ref('Microsoft YaHei')
const font_style = ref(false)
const text_Shadow = ref(false)
// const selectFont = await window.queryLocalFonts()
const selectFamily = () => {
  console.log('选中的字体已改变')
  InGameList.value[listIndex.value][chooseList.value].fontSize = singleFamily.value
}
const sizeChange = () => {
  InGameList.value[listIndex.value][chooseList.value].fontStyle = singleSize.value
}

const TitlesizeChange = () => {
  InGameList.value[listIndex.value][chooseList.value].TitlefontStyle = singleTitleSize.value
}

const colorChange = () => {
  InGameList.value[listIndex.value][chooseList.value].color = singleColor.value
  singleColor.value = singleColor.value.toUpperCase();
}

const TitlecolorChange = () => {
  InGameList.value[listIndex.value][chooseList.value].titlecolor = singleTitleColor.value
  singleTitleColor.value = singleTitleColor.value.toUpperCase();
}
const boldChange = () => {
  InGameList.value[listIndex.value][chooseList.value].fontBold = singleBold.value
}
const StrokeChange = () => {
  InGameList.value[listIndex.value][chooseList.value].textShadow = singleShadow.value
}
function isValidColor(color: string) {
  const regExpHex3 = /^#([0-9A-F]{3})$/i; // 3位16进制颜色 如#fff
  const regExpHex6 = /^#([0-9A-F]{6})$/i; // 6位16进制颜色 如rgb(255,255,255)
  const regExpHex8 = /^#([0-9A-F]{8})$/i; // 带透明度的8位16进制颜色 如#999999ff
  const regExpRgb = /^rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)$/; // 对rgb颜色检验
  const regExpRgba = /^rgba\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*([\d\.]+)\s*\)$/; // 对rgba颜色检验

  return regExpHex3.test(color) || regExpHex6.test(color) || regExpHex8.test(color) || regExpRgb.test(color) || regExpRgba.test(color);
}
const lineColorChange = () => {
  if (isValidColor(lineColor.value)) {
    InGameList.value[listIndex.value][chooseList.value].lineColor = lineColor.value
  }else{
    ElMessage('请输入正确的颜色值')
    lineColor.value = 'rgb(255,255,255)'
  }
  lineColor.value = lineColor.value.toUpperCase();
}
const lineHeightChange = () => {
  InGameList.value[listIndex.value][chooseList.value].lineHeight = lineHeight.value
}
const lineSizeChange = () => {
  InGameList.value[listIndex.value][chooseList.value].lineSize = lineSize.value
}

const toMove = () => {
  if (indexKey.value > 0) {
    const currentList = InGameList.value[0];
    // 将当前项移动到数组的第一个位置,并将原来的第一项开始的所有项向后移动一位
    currentList.unshift(...currentList.splice(indexKey.value, 1));
    indexKey.value = 0;
    localStorage.setItem('InGameList1', JSON.stringify(InGameList.value));
  }
};
const upMove = () => {
  if (indexKey.value > 0 && InGameList.value.length > 0) {
    const currentList = InGameList.value[0];
    const temp = currentList[indexKey.value];
    currentList[indexKey.value] = currentList[indexKey.value - 1];
    currentList[indexKey.value - 1] = temp;
    // 更新 indexKey 为前一项的索引
    indexKey.value -= 1;
    localStorage.setItem('InGameList1', JSON.stringify(InGameList.value));
  }
};
const downMove = () => {
  if (indexKey.value < InGameList.value[0].length - 1) {
    const currentList = InGameList.value[0];
    const temp = currentList[indexKey.value];
    currentList[indexKey.value] = currentList[indexKey.value + 1];
    currentList[indexKey.value + 1] = temp;
    indexKey.value += 1;
    localStorage.setItem('InGameList1', JSON.stringify(InGameList.value));
  }
};


const overallSetting = ref<Overall>({showWay:0, position:1, Alpha:0, bgColor:'rgba(0, 0, 0, 0.3)', fontFamily:'Microsoft YaHei', fontSize:12, fontColor: 'rgb(255,255,255)', fontStyle: false, site: '',textShadow:false})
const handleChange = (item:MonitorStyle, index:number) => {
  // 先重置所有选项的选中状态
  // options.forEach(el => {
  //   if (el !== item) {
  //     el.checked = false;
  //   }
  // });
  options.forEach(el => {
    el.checked = false;
  });
  // 确保当前点击的项被选中
  item.checked = true;
  // overallSetting.value.showWay = index
  let Obj = {
    action: 'ingame_monitor_show',
    value: index
  }
  gamepp.webapp.sendInternalAppEvent.promise('ingame_monitor', Obj);//发送到Background关闭
  localStorage.setItem('ingameArrangement', t(item.name))
  selectedItem.value = item.id;
};
const clickShow = ref<boolean>(false)
const allSetting = ref(true)
const singleSetting = ref(false)
const Back = () => {
  allSetting.value = true
  singleSetting.value = false
  InGameList.value[listIndex.value].forEach(item => {
    item.active = false
  })
}

let indexKey = ref<number>(0)
const sensorSetting = (item: SensorData, index: number) => {
  console.log(item, 'item')
  if (item.origin) {}
  if (item.notFound) {
    SensorbuttonColor.value = '#d72a2a';
  } else {
    SensorbuttonColor.value = 'rgb(255,255,255)';
  }
  if (item.hasOwnProperty('monitor')) {
      single_setting_monitor_item = item;
      Object.assign(single_setting_monitor,item.monitor)
  }else if (item.name !== 'Current time' && item.name !== 'Run time'){
      item.monitor = {	switch: false,
          condition:"biggerThanthreshold", // 条件
          threshold:2, // 大于/小于 xx% 阈值
          initial_threshold:0, // 初始阈值
          cur_threshold:0, // 当前阈值
          is_update_threshold: false, // 触发后是否更新阈值
          wait_time: 10, // 等待时间 s
          do_what: "replay", // 触发功能
          is_show_threshold:false, // 是否显示当前用于回放的阈值
          is_init_threshold_big1000:false, // 是否显示超过初始阈值1000的次数
      }
  }
  indexKey.value = index
  allSetting.value = false
  singleSetting.value = true
  clickShow.value = !clickShow.value;
  chooseList.value = index
  InGameList.value[listIndex.value].forEach(item => {
    item.active = false
  })
  singleDes.value = t(item.des || item.name)
  singledesshow.value = item.desshow
  singleColor.value = item.color
  singleTitleColor.value = item.titlecolor
  singleName.value = item.name
  singleUnit.value = item.unit
  singleKey.value = item.key
  methodWay.value = item.methodWay
  if(item.title.includes('CPU')){
    singletype.value = 'cpuData'
  }else if(item.title.includes('MainboardName')){
    singletype.value = 'boardData'
  }else if(item.title.includes('Drive')){
    singletype.value = 'diskData'
  }else if(item.title.includes('Memory')){
    singletype.value = 'memoryData'
  }else if(item.title.includes('VKDevice')){
    singletype.value = 'VKDevice'
  }
  singleTitle.value = item.title
  console.log(item.title,'item.title')
  console.log( singletype.value,' singletype.value')
  singleSize.value = item.fontStyle
  singleTitleSize.value = item.TitlefontStyle
  singleFamily.value = item.fontSize
  singleBold.value = item.fontBold
  singleShadow.value = item.textShadow

  if (item.name === 'Run time' || item.name === 'Current time') {
    methodText.value = 'InGameMonitor.text';
    methodWay.value = 0;
    showMethod.value =[{name: 'InGameMonitor.text', value: 0}]
  } else {
    if (item.methodWay) {
      showMethod.value =[{name: 'InGameMonitor.text', value: 0}, {name: 'InGameMonitor.textLine', value: 1}]
      methodText.value = showMethod.value[item.methodWay].name;
      methodWay.value = item.methodWay;
    } else {
      methodText.value = item.methodWay === 0 ? 'InGameMonitor.text' : 'InGameMonitor.textLine';
      showMethod.value =[{name: 'InGameMonitor.text', value: 0}, {name: 'InGameMonitor.textLine', value: 1}]
    }
  }
  // methodText.value = item.methodWay === 0 ? '文本' : '文本'+'+'+'折线'
  // if (item.methodWay) {
  //   methodText.value = showMethod.value[item.methodWay].name
  //   methodWay.value = item.methodWay
  // }
  if (item.lineSize) lineSize.value = item.lineSize
  if (item.lineColor)lineColor.value = item.lineColor
  if (item.lineHeight)lineHeight.value = item.lineHeight
  item.active = true
  item.rangeColor = [{
    range:'',
    rangeNum:'',
    rangeColor:'',
  }]
  // formatColor('color1');
  // formatColor('color2');
  // formatColor('singleColor');
  // formatColor('lineColor');
  localStorage.setItem('InGameList1', JSON.stringify(InGameList.value))
}
const switchBtn = ref(false)
const changeText = () => {
  if (singleDes.value === '') {
    singleDes.value = singleName.value;
  }
  InGameList.value[listIndex.value][chooseList.value].des = t(singleDes.value)
  localStorage.setItem('InGameList1', JSON.stringify(InGameList.value))
}

const toggleNameShow = () => {
  singledesshow.value = !singledesshow.value;
  if(singledesshow.value === true){
    InGameList.value[listIndex.value][chooseList.value].desshow =  true
  }else{
    InGameList.value[listIndex.value][chooseList.value].desshow = false
  }
  localStorage.setItem('InGameList1', JSON.stringify(InGameList.value))
}

async function getDistinctFontVariants(): Promise<{ fontFamily: string; variants: string[] }[]> {
  try {
    // 请求查询本地字体的权限
    const hasPermission = await navigator.permissions.query({ name: 'local-fonts' });
    if (hasPermission.state === 'granted') {
      // 查询所有本地字体
      const allFonts: FontData[] = await window.queryLocalFonts();

      // 使用Map来组织字体，key为fontFamily，值为一个Set存储不同的style和weight组合
      const fontVariantsMap = new Map<string, Set<string>>();

      allFonts.forEach((fontData: FontData) => {
        const key = fontData.family;
        if (!fontVariantsMap.has(key)) {
          fontVariantsMap.set(key, new Set<string>());
        }
        // 将style和weight组合成一个标识符加入Set中，以确保唯一性
        fontVariantsMap.get(key).add(`${fontData.style} ${fontData.weight}`);
      });

      // 转换结果为对象数组，展示每个fontFamily及其不同变体
      const distinctFontVariants = Array.from(fontVariantsMap, ([family, variantsSet]) => ({
        fontFamily: family,
        variants: Array.from(variantsSet),
      }));

      console.log(distinctFontVariants);
      return distinctFontVariants;
    } else {
      console.error('Permission to access local fonts was denied.');
      return [];
    }
  } catch (error) {
    console.error('An error occurred while querying local fonts:', error);
    return [];
  }
}

const changeSwitch = (value:boolean) => {
  if(value) {
    gamepp.webapp.windows.show.sync('ingame_monitor',false)
    gamepp.setting.setInteger.sync(3, 1)
  } else {
    if(gamepp.webapp.windows.isVisible.sync('ingame_monitor'))
    {
      // gamepp.webapp.windows.minimize.sync('ingame_monitor');
      gamepp.webapp.windows.close.sync('ingame_monitor');
    }
    gamepp.setting.setInteger.sync(3, 0)
  }

  localStorage.setItem('ingameSwitch', JSON.stringify(switchBtn.value));
}
function showSetting () {
  window.localStorage.setItem('setting_anchorPoint','#cgq')
  gamepp.webapp.windows.show.sync('gamepp_config', false)
}
function  showOLEDSetting() {
  window.localStorage.setItem('setting_anchorPoint','#OLED')
  gamepp.webapp.windows.show.sync('gamepp_config', false)
}

function showSensor ()
{
  window.localStorage.setItem('isIngameSet', '1');
  if(listNumber.value === 1){
    if(gamepp.webapp.windows.isValid.sync("Sensorchoose")){
        gamepp.webapp.windows.close.sync('Sensorchoose')
        setTimeout(() => {
            gamepp.webapp.windows.show.sync('Sensorchoose',false)
        }, 300);
    }
    gamepp.webapp.windows.show.sync('Sensorchoose',false)
  }else{
    gamepp.webapp.windows.show.sync('Sensorchoose',false)
    gamepp.webapp.windows.hide.sync('Sensorchoose')
    setTimeout(() => {
      gamepp.webapp.windows.close.sync('Sensorchoose')
    }, 2000);
  }
    window.localStorage.removeItem('SensorManageType') // {{}}此时传感器操作的种类
    window.localStorage.removeItem('SensorManageData') // 更改此时传感器操作的种类
    window.localStorage.removeItem('SensorManageFilter') // 更改此时传感器操作的种类
    gamepp.webapp.windows.close.sync('hardware_setupsensor')
    localStorage.setItem('InGameList1', JSON.stringify(InGameList.value))
}
function handleShowMethodsChange(value:number) {
    InGameList.value[listIndex.value][chooseList.value].methodWay = value
    methodWay.value = value;
    methodText.value = value === 0 ? 'InGameMonitor.text' : 'InGameMonitor.textLine';
}
function handleShowRangeChange(value:number) {
  console.log(value);
    // InGameList.value[listIndex.value][chooseList.value].methodWay = value
    // methodWay.value = value;
    // methodText.value = value === 0 ? 'InGameMonitor.text' : 'InGameMonitor.textLine';
}

//检查是否是MythCool运行中
const checkIsMythCoolRunning = () => {
  const newValue = gamepp.utils.isMythCoolRunning.sync();
  // if (newValue !== isMythCoolRunning.value) {
  //   isMythCoolRunning.value = newValue;
  //   if (isMythCoolRunning.value) {
  //     updateInGameList();
  //   }
  // }
  if (newValue === true) {
    // updateInGameList();
    InGameList.value[listIndex.value].forEach(item => {
      if (item.name.includes('Valkyrie')) {
          item.notFound = false;
      }
    })
  }else{
    InGameList.value[listIndex.value].forEach(item => {
      if (item.name.includes('Valkyrie')) {
          item.notFound = true;
      }
    })
  }
  localStorage.setItem('InGameList1', JSON.stringify(InGameList.value));
};
// const currentLanguage = ref('CN');

const currentLanguage = inject('currentLanguage');

const fpsValue = ref<Partial<Fps>>({})
let lastExecutionTime = 0;
let intervalId = null;
const getCurrentTimeIn24HourFormat = () => {
  const now = new Date();
  return now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
};
const currentTime = ref(getCurrentTimeIn24HourFormat());
let timeUpdateInterval: number | null = null;
// 在组件挂载后获取尺寸
onMounted(async () => {
  gamepp.game.ingame.onMajorFrameRateUpdated.addEventListener((values: Fps) => {
    // 检查进程ID是否匹配当前游戏
    let curPid = Number(gamepp.game.getCurrentClientPid.sync());
    if (curPid != values.processId) return;
    // 获取当前时间
    const currentTime = Date.now();
    // 计算距离上次执行的时间
    const timeSinceLastExecution = currentTime - lastExecutionTime;

    // 如果距离上次执行超过或等于1000毫秒，则执行处理逻辑并更新时间戳
    if (timeSinceLastExecution >= 1000) {
      // 更新FPS数据
      fpsValue.value = values;
      // console.log('FPS数据更新:', values);
      // 更新上次执行时间
      lastExecutionTime = currentTime;
    }
  })
  timeUpdateInterval = setInterval(() => {
    currentTime.value = getCurrentTimeIn24HourFormat();
  }, 1000);
  intervalId = setInterval(updateTime, 1000);
    try{
        isObsExists.value = await gamepp.package.isexists.promise('GameObs');
    }catch (e) {

    }
  const storedList = localStorage.getItem('InGameList1');
   if (storedList && storedList !== 'null' && storedList !== '') {
       InGameList.value = JSON.parse(storedList);
   } else {
       // 如果存储的列表不存在或无效，使用默认值
       InGameList.value = getInGameList();
       console.log('InGameList loading:', InGameList.value);
       ingameListNameNumber.value = 1;
       localStorage.setItem('ingameListNameNumber', ingameListNameNumber.value.toString());
   }
  await init()
  await initHotkey()
  await initPosition()
  handleIngameIngameArrangement()
  // switchBtn.value = !!gamepp.setting.getInteger.sync(3)
  console.log(switchBtn.value)
  const ingameSwitchBoolean = localStorage.getItem('ingameSwitch');
    if (ingameSwitchBoolean !== null  && ingameSwitchBoolean !== '') {
      if(ingameSwitchBoolean === 'true'){
        switchBtn.value = true
        // gamepp.webapp.windows.show.sync('ingame_monitor',true)
      }else{
        switchBtn.value = false
        // gamepp.webapp.windows.close.sync('ingame_monitor');
      }
  }
  const item = localStorage.getItem('IngameAllSetting');
  if (item !== null  && item !== '') {
    overallSetting.value = JSON.parse(item);
    startReady()
  }
  positionElements.value = Array.from(document.querySelectorAll('.positionBox'));

  await ingamelistName()
  console.log(JSON.stringify(InGameList.value));
  if (window.lister) {
    gamepp.webapp.onInternalAppEvent.removeEventListener(window.listenr)
  }
  window.listenr = (value: any) => {
    console.log(value)
    // InGameList.value[listIndex.value][chooseList.value].notFound = false
    if(value && value.obj && value.obj.action && value.obj.action === 'IngameMonitor') {
      if(dataChange.value) {
        InGameList.value[listIndex.value][chooseList.value].origin = false
        InGameList.value[listIndex.value][chooseList.value].outIndex = value.obj.value.OutIndex
        InGameList.value[listIndex.value][chooseList.value].innerIndex = value.obj.value.InnerIndex
        InGameList.value[listIndex.value][chooseList.value].name = value.obj.value.name
        InGameList.value[listIndex.value][chooseList.value].notFound = false
        singleName.value = value.obj.value.name
        // singleUnit.value = value.obj.value.Unit
        dataChange.value = false
      } else {
        console.log('InGameList',JSON.stringify(InGameList.value));
        if (InGameList.value === null) {
          const storedList = localStorage.getItem('InGameList1');
          console.log('storedList',storedList);
          if (storedList !== null) {
              InGameList.value = JSON.parse(storedList);
          }
        }
        if(value.obj.value.all.type === 'CPU') {
          addType.value = 'CPU'
        } else if (value.obj.value.all.type === 'GPU') {
          addType.value = 'GPU'
        } else if (value.obj.value.all.type === "Memory" || value.obj.value.all.type === "DIMM") {
          addType.value = 'Memory'
        } else if (value.obj.value.all.type === "Drive" || value.obj.value.all.type === "S.M.A.R.T.") {
          addType.value = 'Drive'
        } else if (value.obj.value.all.type === "MainboardName"){
          addType.value = 'MainboardName'
        }else{
          addType.value = 'VKDevice'
        }
        console.log(listIndex.value)
        const o = {name: value.obj.value.name,des: '', show: true, color:'rgb(255,255,255)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 14, fontBold: false,outIndex:value.obj.value.OutIndex,innerIndex:value.obj.value.InnerIndex, origin: false,top:false,lock: false,lineColor: 'rgb(255,255,255)',lineHeight:null,lineSize:2,showOnlyHide: false, key:'', unit:value.obj.value.unit, title: addType.value,textShadow:false,desshow:true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)',notFound:false}
        if (value.obj.value.hasOwnProperty('desc')) {
            o.desc = value.obj.value.desc
        }
        InGameList.value[listIndex.value].push(o)
      }
      console.log(value)
      // localStorage.setItem('InGameList', JSON.stringify(InGameList.value))
      saveInGameList(InGameList.value)
      InGameList.value[listIndex.value].forEach(item => {
      if (item.notFound) {
          SensorbuttonColor.value = '#d72a2a';
        } else {
          SensorbuttonColor.value = 'rgb(255,255,255)';
        }
      })

    } else if (value === 'vkDeviceUpdate') {
      setTimeout(() => {
        InGameList.value = getInGameList();
      }, 1000);
    }else {

    }
  }
  gamepp.webapp.onInternalAppEvent.addEventListener(window.listenr)
  getWindowSize(); // 首次获取
  try {
    // 通过 .value 属性来修改响应式引用的值
    fontValues.value = await getDistinctFontVariants();
  } catch (error) {
    console.error('Failed to load font variants:', error);
    // 在发生错误时设置默认值
    fontValues.value = [];
  }
  // 添加监听器以在窗口大小变化时更新尺寸
  window.addEventListener('resize', getWindowSize);
  InGameList.value[listIndex.value].forEach(item => {
    item.active = false
  })
  // startTimer();
  // 指定图表的配置项和数据
  // Object.keys(refMap).forEach((key) => {
  //   formatColor(key);
  // });

  // onUnmounted(() => {
  //   document.removeEventListener('mousemove', dragging);
  //   document.removeEventListener('mouseup', endDrag);
  // });

  //测试导入的鼠标变化
  // checkIsMythCoolRunning();
  // pollTimer = window.setInterval(checkIsMythCoolRunning, pollInterval);
  // loadLanguage();
});

let box1 = ref()
let box2 = ref()
let box3 = ref()
let box4 = ref()
let box5 = ref()
let box6 = ref()
let box7 = ref()
let box8 = ref()
let box9 = ref()
let allBox = {
  box1,
  box2,
  box3,
  box4,
  box5,
  box6,
  box7,
  box8,
  box9,
}
// 清理：在组件卸载前移除监听器，防止内存泄漏
onUnmounted(() => {
  if (pollTimer !== null) {
    clearInterval(pollTimer);
  }
  localStorage.setItem('IngameAllSetting', JSON.stringify(overallSetting.value))
  positionElements.value = [];
  window.removeEventListener('resize', getWindowSize);
  if (debounceTimeoutInGameList.value !== null) {
    clearTimeout(debounceTimeoutInGameList.value);
  }
  if (debounceTimeoutInGameTitle.value !== null) {
    clearTimeout(debounceTimeoutInGameTitle.value);
  }
  window.removeEventListener('storage', handleIngameAllSetting);
});
// const loadLanguage = () => {
//   const savedLanguage = localStorage.getItem('language');
//   if (savedLanguage) {
//     currentLanguage.value = savedLanguage;
//   }
// };
const handleIngameIngameArrangement = () => {
  const ingameArrangementData = localStorage.getItem('ingameArrangement');
  if (ingameArrangementData) {
    if(ingameArrangementData === t('InGameMonitor.horizontal')){
      options[0].checked = true
      options[1].checked = false
      selectedItem.value = 1;
    }else if(ingameArrangementData === t('InGameMonitor.vertical')){
      options[1].checked = true
      options[0].checked = false
      selectedItem.value = 2;
    }
  }
}
const handleIngameAllSetting = async (event:any) => {
  // console.log('开关触发了', event)
  if (event.key === 'IngameAllSetting') {
    overallSetting.value = JSON.parse(event.newValue);
    startReady();
  }
  if (event.key === 'ingameSwitch' && event.newValue !== null) {
      switchBtn.value = JSON.parse(event.newValue);
  }
  if(event.key === 'InGameList1') {
    if(InGameList.value !== null) {
      InGameList.value = JSON.parse(event.newValue);
      if (single_setting_monitor_item) {
          InGameList.value.forEach((item)=>{
              item.forEach(item2=>{
                  if (item2.name === single_setting_monitor_item.name && item2.monitor) {
                      single_setting_monitor_lock = true
                      Object.assign(single_setting_monitor,item2.monitor)
                  }
              })
          })
      }
    }else{
      const storedList = localStorage.getItem('InGameList1');
      console.log('storedList',storedList);
      if (storedList !== null) {
          InGameList.value = JSON.parse(storedList);
      }
    }
  }
  if(event.key === 'ingame_monitor_location') {
  //  await initPosition()
  }
  //同步游戏内外页面位置
  if(event.key === 'latestPositionSettings') {
    let latestPositionSettingsData = window.localStorage.getItem('latestPositionSettings')
    if (latestPositionSettingsData) {
      const parsedData = JSON.parse(latestPositionSettingsData) as PositionSettings;
      const element = document.querySelector(`[data-ref="${parsedData.elementRef}"]`) as HTMLElement | null;
      if (element) {
        await setPosition(element, parsedData.site, parsedData.send_id, parsedData.index);
      }
    }
  }
  if(event.key === 'ingameArrangement') {
    handleIngameIngameArrangement()
  }
  // if(event.key === 'language') {
  //   console.log('event.key',event.newValue);
  //   currentLanguage.value = event.newValue || 'CN';
  // }
}

window.addEventListener('storage', handleIngameAllSetting)

const startReady = () => {
  // options.forEach(el => {
  //   el.checked = false;
  // });
  // options[overallSetting.value.showWay].checked = true // 排列方式
  color1.value = overallSetting.value.bgColor //背景颜色
  selected.value = overallSetting.value.fontFamily //字体
  textSize.value = overallSetting.value.fontSize // 字体大小
  color2.value = overallSetting.value.fontColor //字体颜色
  font_style.value = overallSetting.value.fontStyle //字体加粗
  text_Shadow.value = overallSetting.value.textShadow //字体描边
}

// let intervalId;

// async function checkAndExecute() {
//   try {
//     const isVisible = await gamepp.webapp.windows.isVisible.promise('ingame_monitor');
//     if (isVisible) {
//       await setPosition(allBox['box' + overallSetting.value.position].value, overallSetting.value.site, 9321, 1);
//       stopTimer()
//     }
//   } catch (error) {
//     // console.error('Error checking visibility:', error);
//   }
// }

// function startTimer() {
//   intervalId = setInterval(async () => {
//     await checkAndExecute();
//   }, 1000); // 每秒检查一次
// }

// function stopTimer() {
//   if (intervalId) {
//     clearInterval(intervalId);
//   }
// }
const debounceTimeoutInGameList = ref(null);
const debounceTimeoutInGameTitle = ref(null);

// 创建两个独立的 debounce 函数
const debouncedLocalStorageSetItemInGameList = (key: string, value: string) => {
  if (debounceTimeoutInGameList.value !== null) {
    clearTimeout(debounceTimeoutInGameList.value);
  }
  debounceTimeoutInGameList.value = setTimeout(() => {
    localStorage.setItem(key, value);
  }, 1000);
};

const debouncedLocalStorageSetItemInGameTitle = (key: string, value: string) => {
  if (debounceTimeoutInGameTitle.value !== null) {
    clearTimeout(debounceTimeoutInGameTitle.value);
  }
  debounceTimeoutInGameTitle.value = setTimeout(() => {
    localStorage.setItem(key, value);
  }, 1000);
};


// 使用 watchEffect 监听 InGameList 和 customList 的变化
watchEffect(() => {
  debouncedLocalStorageSetItemInGameList('InGameList1', JSON.stringify(InGameList.value));
  debouncedLocalStorageSetItemInGameTitle('IngameAllSetting', JSON.stringify(overallSetting.value));
});

// watchEffect(() => {
//
// });
const dataChange = ref(false)
// const changeSensor = () => {
//   if(InGameList.value[listIndex.value][chooseList.value].lock) {
//     ElMessage.warning('无法更改默认传感器的数据')
//     return
//   }
//   window.localStorage.setItem('isIngameSet', '1');
//   gamepp.webapp.windows.show.sync('Sensorchoose', false)
//   dataChange.value = true
// }

const changeSensor = async(type: any, key: any, filterType: any, unit: any)=>{
    if (ingame_now.value) {
      dataChange.value = true
      window.localStorage.setItem('isIngameSet', '1');
      if(InGameList.value[listIndex.value][chooseList.value].lock) {
        ElMessage.warning('无法更改默认传感器的数据')
        return
      }
      console.log(type, key, filterType, unit)
      window.localStorage.setItem('SensorManageType', JSON.stringify('ChangeMainPageSensor')) // {{}}此时传感器操作的种类
      window.localStorage.setItem('SensorManageData', JSON.stringify({type: type, key: key})) // 更改此时传感器操作的种类
      window.localStorage.setItem('SensorManageFilter', JSON.stringify({filterType, unit})) // 更改此时传感器操作的种类
      try {
        gamepp.webapp.windows.show.promise('Sensorchoose').then(()=>{
          gamepp.webapp.windows.focus.promise('Sensorchoose')
        })
      } catch (e){
        console.error("gamepp.webapp.windows.show.promise('Sensorchoose')",e)
      }
    } else {
      ElMessage({
        message: '游戏内无法更改传感器，请切换到主程序更改。',
        type: 'warning',
      });
    }
}

const handleClick = (index: number) =>{
    if (index === 0) {
      allSetting.value = true
      singleSetting.value = false
      InGameList.value[listIndex.value].forEach(item => {
        item.active = false
      })
    }
}

//拖动列表排序
const draggingIndex = ref<number>(-1);
const targetIndex = ref<number>(-1);
const offsetY = ref<number>(0);
let longPressTimer: number | null = null;
const listItems = ref<HTMLLIElement[]>([]);
const LongPressDelay = 500;

watch(InGameList, () => {
  listItems.value = [];
}, { deep: true });

const startLongPressDrag = (index: number, event: MouseEvent) => {
  // 游戏内列表不执行拖动
  if (!ingame_now.value) return;
  event.preventDefault();
  longPressTimer = window.setTimeout(() => {
    startDrag(index, event);
    allSetting.value = true
    singleSetting.value = false
    InGameList.value[listIndex.value].forEach(item => {
      item.active = false
    })
  }, LongPressDelay);

  const mouseupHandler = () => {
    if (longPressTimer !== null) {
      clearTimeout(longPressTimer);
      longPressTimer = null;
    }
    // if (draggingIndex.value !== -1) {
    //   endDrag();
    // }
  };

  document.addEventListener('mouseup', mouseupHandler);
};

const startDrag = async (index: number, event: MouseEvent) => {
  if (!ingame_now.value) return;
  draggingIndex.value = index;
  await nextTick();
  const element = listItems.value[index];
  const rect = element.getBoundingClientRect();

  // 获取滚动容器的滚动偏移量
  const ulElement = document.querySelector('.all-sensor') as HTMLElement;
  const scrollTop = ulElement.scrollTop;
  // 计算逻辑偏移量时考虑滚动偏移量
  const logicalOffsetY = event.clientY - (rect.top * zoomStore.zoomLevel) + (scrollTop * zoomStore.zoomLevel);
  // const logicalOffsetY = (event.clientY + scrollTop) - (rect.top * zoomStore.zoomLevel);
  offsetY.value = logicalOffsetY;

  element.style.position = 'absolute';
  element.style.zIndex = '1000';
  element.style.opacity = '0.6';

  document.addEventListener('mousemove', dragging);
  document.addEventListener('mouseup', endDrag);
};

const dragging = (event: MouseEvent) => {
  if (!ingame_now.value || draggingIndex.value === -1) return;

  const listItems = document.querySelectorAll('.game_list_item');
  const draggingElement = listItems[draggingIndex.value] as HTMLElement;
  const ulElement = document.querySelector('.all-sensor') as HTMLElement;
  const ulRect = ulElement.getBoundingClientRect();
  // const scrollTop = ulElement.scrollTop;
  // 获取滚动偏移量
  const scrollTop = ulElement.scrollTop * zoomStore.zoomLevel;
  // const physicalY = event.clientY;
  const physicalY = event.clientY + scrollTop;
  let newTop = (physicalY - offsetY.value) / zoomStore.zoomLevel;
  draggingElement.style.top = `${newTop}px`;

  const scrollTop2 = ulElement.scrollTop;
  const scrollLeft = ulElement.scrollLeft;

  // 检查是否在容器all-sensor内拖动
  const physicalTop = (ulRect.top + scrollTop2) * zoomStore.zoomLevel;
  const physicalBottom = (ulRect.bottom + scrollTop2) * zoomStore.zoomLevel;
  const physicalLeft = (ulRect.left + scrollLeft) * zoomStore.zoomLevel;
  const physicalRight = (ulRect.right + scrollLeft) * zoomStore.zoomLevel;

  // 判断是否超出实际可见区域
  if (
    physicalY < physicalTop ||
    physicalY > physicalBottom ||
    event.clientX < physicalLeft ||
    event.clientX > physicalRight
  ) {
    endDrag();
    return;
  }

  // if (physicalY < ulRect.top * zoomStore.zoomLevel || physicalY > ulRect.bottom * zoomStore.zoomLevel || event.clientX < ulRect.left * zoomStore.zoomLevel || event.clientX > ulRect.right * zoomStore.zoomLevel) {
  //   endDrag();
  //   return;
  // }

  // 计算目标索引，移动到新位置在 highlight 上方
  targetIndex.value = Array.from(listItems).findIndex((item: any, idx: number) => {
    const rect = item.getBoundingClientRect();
    // const centerY = rect.top + rect.height / 2;
    const centerY = rect.top + scrollTop + rect.height / 2;
    return physicalY / zoomStore.zoomLevel < centerY && idx !== draggingIndex.value;
  });

  // 如果没有找到合适的目标索引设置为列表的最后一项
  if (targetIndex.value === -1) {
    targetIndex.value = listItems.length;
  }

  listItems.forEach((item, index) => {
    if (index === targetIndex.value) {
      item.classList.add('highlight');
    } else {
      item.classList.remove('highlight');
    }
  });
};

const endDrag = () => {
  if (!ingame_now.value) return;
  console.log('结束拖动');
  if (draggingIndex.value === -1 || targetIndex.value === -1) return;
  const validTargetIndex = Math.max(0, Math.min(targetIndex.value, InGameList.value[listIndex.value].length - 1));
  const draggedItem = InGameList.value[listIndex.value][draggingIndex.value];
  InGameList.value[listIndex.value].splice(draggingIndex.value, 1);
  InGameList.value[listIndex.value].splice(validTargetIndex, 0, draggedItem);
  localStorage.setItem('InGameList1', JSON.stringify(InGameList.value));
  draggingIndex.value = -1;
  targetIndex.value = -1;
  document.querySelectorAll('.game_list_item').forEach(item => {
    item.classList.remove('highlight');
    (item as any).style.position = '';
    (item as any).style.transform = '';
    (item as any).style.opacity= '';
    // (item as any).style.cssText = '';
  });
  document.removeEventListener('mousemove', dragging);
  document.removeEventListener('mouseup', endDrag);

  if (longPressTimer !== null) {
    clearTimeout(longPressTimer);
    longPressTimer = null;
  }
};
const formattedTime = computed(() => formatTime(elapsedTime.value));
const elapsedTime = ref(0);
const updateTime = () => {
  elapsedTime.value++; // 增加秒数
};
const formatTime = (seconds: number) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secondsAsNumber = seconds % 60;
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secondsAsNumber.toString().padStart(2, '0')}`;
};

const isDataAvailable = (data: any) => {
  if (data === undefined || data === null || data === '') {
    return false;
  }
  if (typeof data === 'string') {
    // 如果字符串包含字母
    if (/[a-zA-Z]/.test(data)) {
      return false;
    }
  }
  return true;
};
const getMonitorItemValue = (item: SensorData) => {
  if (!item) return 0;
  console.log(fpsValue,'+++++++');
  console.log(item,'+++++++');
  // 处理FPS相关数据
  if (item.name === "FPS") {
    return fpsValue.value?.fps;
  } else if (item.name === 'FPS 1% Low') {
    return fpsValue.value?.fps1low;
  } else if (item.name === 'FPS 0.1% Low') {
    return fpsValue.value?.fps01low;
  } else if (item.name === 'Frame Time') {
    return fpsValue.value?.frameTime ? `${fpsValue.value.frameTime.toFixed(1)}ms` : 0;
  } else if (item.name === 'Current time') {
    return currentTime.value;
  } else if (item.name === 'Run time') {
    return isDataAvailable(formattedTime.value) ? formattedTime.value : 0;
  }
  
  // 处理CPU、GPU等硬件数据
  if (item.origin && item.bg) {
    const bgData = JSON.parse(localStorage.getItem('bg_sensor_data') || '{}');
    if (item.title.includes('CPU')) {
      if (bgData.cpu && bgData.cpu[item.bg]) {
        return bgData.cpu[item.bg];
      }
    } else if (item.title.includes('GPU')) {
      if (bgData.gpu && bgData.gpu[item.bg]) {
        return bgData.gpu[item.bg];
      }
    } else if (item.title.includes('memory')) {
      if (bgData.memory && bgData.memory[item.bg]) {
        return bgData.memory[item.bg];
      }
    }
  }
  
  // 处理其他传感器数据
  try {
    // 尝试从SensorList中获取数据
    const SensorInfo = JSON.parse(localStorage.getItem('SensorInfo') || '{}');
    if (SensorInfo && item.outIndex !== undefined && item.innerIndex !== undefined) {
      const sensorItem = SensorInfo[item.outIndex]?.Sensoritem[item.innerIndex];
      if (sensorItem) {
        return `${sensorItem.value} ${sensorItem.unit}`;
      }
    }
  } catch (e) {
    console.error('获取传感器数据失败', e);
  }
  
  return 0;
};

const hardwareStyle = computed(() => {
  return {
    height:  `${($gamepp.gameppHeight - (60*zoomStore.zoomLevel)) / zoomStore.zoomLevel}px`,
  }
})
const hardwareStyleIngame = computed(() => {
  return {
    height:  `750px`,
  }
})
// const openSetting = async () => {
//   window.localStorage.setItem('setting_anchorPoint', '#kjj')
//   await gamepp.webapp.windows.show.promise('gamepp_config');
// }
const hardwareContentStyle = computed(() => {
  return {
    height: `${($gamepp.gameppHeight - 158*zoomStore.zoomLevel) / zoomStore.zoomLevel}px`,
  }
})
const hardwareContentStyleIngame = computed(() => {
  return {
    height: `652px`,
  }
})
let allFontcolorpicker = ref()
let singleColorpicker = ref()
let defaultColorpicker = ref()
let singleTitleColorpicker = ref()
function handleClick1(fn: () => void) {
  setTimeout(()=>{
    fn()
  },100)
}

// 导出数据
const exportInGameList = () => {
  const inGameData = InGameList.value;
  const arrangementData = localStorage.getItem('ingameArrangement');
  //合并数据
  const exportData = {
    version: '2.0',
    timestamp: new Date().toISOString(),
    data: inGameData,
    layout: arrangementData
  };
  const dataStr = JSON.stringify(exportData, null, 2);
  const blob = new Blob([dataStr], { type: 'application/json' });
  const href = URL.createObjectURL(blob);
  // 创建一个隐藏的 <a> 元素来触发下载
  const link = document.createElement('a');
  link.href = href;
  link.download = 'GameMonitorSetup.json';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(href);
};

// 导入数据
const importInGameList = async(event: Event) => {
  listNumber.value = 2;
  showSensor();
  // let SensorList = JSON.parse(window.localStorage.getItem('SensorList')) || [];
  // 确保 SensorList 数据加载完毕后再继续
  const loadSensorList = (): Promise<any[]> => {
    return new Promise((resolve, reject) => {
      try {
      setTimeout(() => {
        const storedData = window.localStorage.getItem('SensorList');
        const sensorList = storedData ? JSON.parse(storedData) : [];
        resolve(sensorList);
      },1000)
      } catch (error) {}
    });
  };

  let SensorList;
  try {
    SensorList = await loadSensorList();
  } catch (error) {
    console.error('加载失败', error);
    return; // 如果加载失败，直接返回
  }

  const input = event.target as HTMLInputElement;
  if (!input.files?.[0]) return;

  let presetNames  = Object.freeze([
    'FPS',
    'FPS 1% Low',
    'FPS 0.1% Low',
    'Frame Time',
    'Current time',
    'Run time',
  ]);

  const usbDevices = gamepp.queryUsbDevices.sync() as Array<any>
  const mouseEventRateDevices:any = {
    "31514026": "VK M1",
    "3151402D": "VK M1",
    "046DC539": "罗技 GPW1",
    "046DC547": "罗技 GPW2",
    "046DC54D": "罗技 GPW3",
    "046DC543": "罗技 GPW4",
    "153200B6": "雷蛇 DeathAdder V3 PRO",
    "153200B7": "雷蛇 DeathAdder V3 PRO",
  }
  let preset_name = new Set(presetNames)
  for (const usbDevice of usbDevices) {
      const Key:string = usbDevice.VendorID+usbDevice.ProductID
      if (mouseEventRateDevices.hasOwnProperty(Key)) {
        preset_name = new Set([...presetNames, '鼠标回报率']);
        break;
      }
  }
  const allDeviceName = ['Valkyrie 99 磁轴','Valkyrie M1 鼠标','PRO WIRELESS','PRO X SUPERLIGHT','PRO X SUPERLIGHT 2','PRO 2 LIGHTSPEED','PRO X WIRELESS']
  const datastr = gamepp.hardware.getMythCoolUSBSensorInfo.sync();
  let VKDevice = [];
  console.log(VKDevice.length);
  if (datastr) {
    VKDevice = JSON.parse(datastr);
  }
  console.log(datastr);
  const file = input.files[0];
  const reader = new FileReader();

  reader.onload = (e) => {
    try {
      const result = e.target?.result;
      if (typeof result !== 'string') return;
      const importedData = JSON.parse(result);
      if (!importedData.data) {
        // ElMessage({
        //   message: '导入的数据不正确',
        //   type: 'warning',
        // });
        console.error('导入的数据不正确');
      }
      // const uploadedMatrix = JSON.parse(result);
      const uploadedMatrix = importedData.data;

      // 生成所有传感器名称索引
      const sensorNames = new Set(
        SensorList.flatMap((sensor: { Sensoritem: any[]; }) =>
          sensor.Sensoritem.map(item => item.name)
        )
      );
      uploadedMatrix.forEach((group: any[]) => {
      group.forEach((item: any) => {
        const existsInAll = allDeviceName.includes(item.name);
        if(existsInAll  && VKDevice.length > 0){
          preset_name = new Set([...preset_name, item.name]);
          console.log(preset_name);
        }
        const isFontExists = fontValues.value.some(font => font.fontFamily === item.fontSizes);
        if (!isFontExists) {
          item.fontSizes = 'Microsoft YaHei';
        }

      })
     })
      const processedMatrix = uploadedMatrix.map((subArray: any[]) =>
        subArray.map(item => {
          const isPreset = preset_name.has(item.name);
          return {
            ...item,
            notFound: isPreset ? false : !sensorNames.has(item.name)
          };
        })
      );
      InGameList.value = processedMatrix;
      localStorage.setItem('InGameList1', JSON.stringify(processedMatrix));

      if (importedData.layout) {
        localStorage.setItem('ingameArrangement', importedData.layout);
        handleIngameIngameArrangement();
      }
    } catch (error) {
      ElMessage({
        message: '导入的数据不正确',
        type: 'warning',
      });
      console.error('数据处理失败:', error);
    } finally {
       // 重置文件输入元素的值
      input.value = '';
    }
  };

  reader.readAsText(file);
  listNumber.value = 1;
};

// 监控阈值相关的东西
watch(single_setting_monitor,(newValue:any)=>{
    console.log('single_setting_monitor::',newValue)
    if (!single_setting_monitor_lock) {
        if (InGameList.value[listIndex.value][chooseList.value].monitor) {
            Object.assign(InGameList.value[listIndex.value][chooseList.value].monitor,newValue);
        }
        localStorage.setItem('InGameList1', JSON.stringify(InGameList.value))
    }else{
        single_setting_monitor_lock = false
    }
})
function handleInputThreshold(value:any) {
	const newValue = Number(value);
	if (typeof newValue === 'number' && !Number.isNaN(newValue)) {
	    single_setting_monitor.threshold = newValue;
	}else{
		single_setting_monitor.threshold = 2;
	}
}
async function downloadObs() {
    obsDownloading.value = true
    try{
        const OBSexist = await gamepp.package.isexists.promise('obs')
        console.warn('exist',OBSexist);
        if(!OBSexist) {
            let bUpdate = await gamepp.package.checkupdate.promise('obs')
            console.warn('开始下载',bUpdate);
            if(bUpdate.result)
            {
                let result = await gamepp.package.startdownload.promise('obs',bUpdate.url,bUpdate.md5,bUpdate.version)
                if(result) //下载完成
                {
                    await gamepp.obs.runOBSClient.promise()
                    const checkUpdate = await gamepp.package.checkupdate.promise('GameObs');
                    console.warn('checkUpdate', checkUpdate);
                    if (checkUpdate.result)
                    {
                        const result = await gamepp.package.startdownload.promise('GameObs', checkUpdate.url, checkUpdate.md5, checkUpdate.version);
                        console.warn('result', result);
                        if (result)
                        { //下载完成
                            ElMessage({message:'下载完成',type:'success',grouping:true});
                            isObsExists.value = true;
                            await gamepp.setting.setInteger.promise(41,1);
                            await gamepp.setting.setInteger.promise(45,1);
                        }
                        else
                        {
                            throw new Error('Download failed');
                        }
                    }
                    else
                    {
                        throw new Error('No update available');
                    }
                }
                else
                {
                    ElMessage.error('下载失败，请检查网络环境')
                }
            }else{
                ElMessage.error('下载失败，请检查网络环境')
            }
        }else{
            const checkUpdate = await gamepp.package.checkupdate.promise('GameObs');
            console.warn('checkUpdate', checkUpdate);
            if (checkUpdate.result)
            {
                const result = await gamepp.package.startdownload.promise('GameObs', checkUpdate.url, checkUpdate.md5, checkUpdate.version);
                console.warn('result', result);
                if (result)
                { //下载完成
                    ElMessage({message:'下载完成',type:'success',grouping:true});
                    isObsExists.value = true;
                    await gamepp.setting.setInteger.promise(41,1);
                    await gamepp.setting.setInteger.promise(45,1);
                }
                else
                {
                    throw new Error('Download failed');
                }
            }
            else
            {
                throw new Error('No update available');
            }
        }
    }catch (e) {
        obsDownloading.value = false;
        ElMessage({message:'下载或更新失败：请检查网络状况',type:'warning',grouping:true});
    }
}

async function handleChangeSingeleMonitorDoWhat () {
    if (single_setting_monitor.do_what === 'replay') {
        if (isObsExists.value) {
            await gamepp.setting.setInteger.promise(41,1);
            await gamepp.setting.setInteger.promise(45,1);
        }
    }else{

    }
}

async function handleChangeSingleSettingMonitorSwitch () {
    if (single_setting_monitor.switch) {
        if (single_setting_monitor.do_what === 'replay') {
            if (isObsExists.value) {
                await gamepp.setting.setInteger.promise(41,1);
                await gamepp.setting.setInteger.promise(45,1);
            }
        }
    }
}
</script>

<template>
  <div class="monitor-container dark" :style="ingame_now?hardwareStyle:hardwareStyleIngame">
    <div class="top_setting">
      <div class="top_switch">{{ $t("InGameMonitor.InGameMonitor") }}
        <el-switch v-model="switchBtn" @change="changeSwitch" style="margin-left: 10px;" />
      </div>
      <div class="hotkey_set">
        <div class="ingame_hotkey">
          <span>{{$t("messages.onoffingame")}}</span>
          <shortcut :id="15"></shortcut>
          <!-- <el-button class="Sensor-button2" @click="openSetting">{{ingame_hotkey}}</el-button> -->
        </div>
        <div class="tab_hotkey" v-show="ingame_now">
          <span>{{$t("messages.ingameControlPanel")}}</span>
          <!-- <el-button class="Sensor-button2" @click="openSetting">{{ingame_hotkey_setting}}</el-button> -->
          <shortcut :id="9"></shortcut>

        </div>
      </div>
    </div>

    <div class="all-Button" v-show="ingame_now">
      <el-button color="#343647" v-for="(item, index) in MonitorButton" :key="index" :class="{ 'first-button': index === 0 }" @click="handleClick(index)">{{$t(item)}}</el-button>
    </div>
    <div class="content-all scroll" :style="ingame_now?hardwareContentStyle:hardwareContentStyleIngame">
      <div class="content-left">
        <div class="left-title">
          <div style="display: flex;">
            <el-icon size="20" color="rgba(53, 121, 213, 1)"><Operation /></el-icon>
             <p>{{ $t("InGameMonitor.MonitorItem") }}</p>
          </div>
            <!-- 导入 导出 游戏内不显示-->
          <div class="import-export" v-show="ingame_now">
            <div class="import-button">
              <button @click="exportInGameList" class="Sensor-buttonMove Sensor-buttonMove2 hover">
                <el-icon style="color: #3579d5;font-size: 17px;"><Upload /></el-icon>
                <span class="guide">{{ $t('messages.export') }}</span>
              </button>
            </div>
            <div class="import-button">
              <label for="fileInput" class="custom-file-input Sensor-buttonMove Sensor-buttonMove2 hover">
                <el-icon style="color: #3579d5;font-size: 17px;"><Download /></el-icon>
                <span class="guide">{{ $t('messages.import') }}</span>
              </label>
              <input id="fileInput" type="file" accept=".json"  @change="importInGameList" style="display: none;"/>
            </div>
          </div>
        </div>
        <!--      <div class="select-all">-->
        <!--        <div class="left-button border-transition"><el-icon><ArrowLeft /></el-icon></div>-->
        <!--        <div class="all-list">-->
        <!--          <div class="list border-transition"  @click="changeCustom(index)" v-for="(item, index) in customList" :key="item.title"><p>{{item.title}}</p> <el-icon @click="deleteList(index)"><Delete /></el-icon></div>-->
        <!--          <div class="add-button border-transition" @click="addList"><el-icon><CirclePlus /></el-icon></div>-->
        <!--        </div>-->
        <!--        <div class="right-button border-transition"><el-icon><ArrowRight /></el-icon></div>-->
        <!--      </div>-->
        <div class="add-monitor border-transition" @click="showSensor" v-show="ingame_now">
          <el-icon><CirclePlus /></el-icon>
          <p class="add-monitor-text" >{{ $t("InGameMonitor.addMonitorItem") }}</p>
        </div>

        <div class="list-data">
          <div class="list-title">
            <div class="data"><span>{{ $t("InGameMonitor.Data") }}</span></div>
            <div class="des"><span>{{ $t("InGameMonitor.Des") }}</span></div>
            <div class="fn"><span>{{ $t("InGameMonitor.Function") }}</span></div>
          </div>
          <div class="all-sensor scroll" >
            <ul class="game_list_ul">
              <li v-for="(item, index) in InGameList[listIndex]"
              @click="sensorSetting(item, index)"  ref="listItems"
              :class="{ active: item.active,'can-drag': ingame_now, 'not-found':item.notFound }"
              :key="item.name"
              class="game_list_item"
              @mousedown="(event) => startLongPressDrag(index, event)"
              @mouseenter="item.group = true"
              @mouseleave="item.group = false"
             >
               <div class="parcel">
                <span class="data_value">
                  {{ item.notFound ? 'Null' : item.name }}
                </span>
                <el-tooltip v-if="item.notFound"  effect="dark" :content="$t('InGameMonitor.notFindSensor')" placement="top" :delay="0" :duration="0"  :hide-after="0" :enterable="false">
                    <div class="not-found-mask"></div>
                </el-tooltip>
                <span class="des_value">
                  <!-- {{ item.des ? item.des : item.name }} -->
                  {{ $t(String(item.des || item.name)) }}
                </span>
                <div class="icon-group" v-show="item.group || item.showOnlyHide">
                  <!-- <div class="Mousedevice"></div> -->
                  <el-tooltip  effect="dark" :content="$t('InGameMonitor.notSupport')" placement="top" :delay="0" :duration="0"  :hide-after="0" :enterable="false"  popper-class="custom_tooltip allht" >
                    <div class="Mousedevice" v-show="allDeviceName.includes(item.name)"></div>
                  </el-tooltip>
                    <el-tooltip  effect="dark" :content="$t('InGameMonitor.notSupportRate')" placement="top" :delay="0" :duration="0"  :hide-after="0" :enterable="false"  popper-class="custom_tooltip allht" >
                        <div class="Mousedevice" v-show="item.hasOwnProperty('desc') && item.desc === 'mouseEventRate'"></div>
                    </el-tooltip>
                  <el-tooltip effect="dark" :content="$t('InGameMonitor.hide')" placement="top" :delay="0" :duration="0"  :hide-after="0" :enterable="false"  popper-class="custom_tooltip allht" >
                    <div class="icon-group-item" v-show="item.showOnlyHide"  @click.stop="changeView(index)">
                      <el-icon><Hide /></el-icon>
                    </div>
                  </el-tooltip>
                  <el-tooltip effect="dark" :content="$t('InGameMonitor.show')" placement="top" :delay="0" :duration="0"  :hide-after="0" :enterable="false" popper-class="custom_tooltip allht" >
                    <div class="icon-group-item" v-show="item.show&& item.group"  @click.stop="changeView(index)">
                      <el-icon><View /></el-icon>
                    </div>
                  </el-tooltip>
                  <el-tooltip effect="dark" :content="$t('InGameMonitor.Top')" placement="top" :delay="0" :duration="0" :hide-after="0" :enterable="false" popper-class="custom_tooltip allht" >
                    <div class="icon-group-item" v-show="!item.top&& item.group && index !== 0"  @click.stop="changeTop(index)">
                      <el-icon><Upload /></el-icon>
                    </div>
                    <div class="icon-group-item" v-show="item.top&& item.group && index !== 0"  @click.stop="changeTop(index)">
                      <el-icon class="icon-Upload"><Upload /></el-icon>
                    </div>
                  </el-tooltip>
                  <div class="icon-group-item" @mouseenter="item.hovering = true" @mouseleave="item.hovering = false" v-show="!item.hovering && !item.lock&& item.group">
                    <el-icon><Delete /></el-icon>
                  </div>
                  <el-tooltip effect="dark" :content="$t('InGameMonitor.Delete')" placement="top" :delay="0" :duration="0" :hide-after="0" :enterable="false" popper-class="custom_tooltip allht" >
                    <div class="icon-group-item" @mouseenter="item.hovering = true" @mouseleave="item.hovering = false" v-show="item.hovering && !item.lock&&item.group" @click.stop="deleteSensor(index)">
                      <el-icon><DeleteFilled /></el-icon>
                    </div>
                  </el-tooltip>
                </div>
               </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="content-right scroll">
        <div class="default_setting" v-show="allSetting">
          <div class="right-title">
            <div>
            <span class="iconfont icon-style"></span>
            <p>{{ $t("InGameMonitor.generalstyle") }}</p>
            </div>
            <div @click="setAllDef" class="hoverlighten">
              <el-icon><RefreshLeft /></el-icon>
              <p>{{ $t("InGameMonitor.restoredefault") }}</p>
            </div>
          </div>
          <div class="arranged">
            <p>{{ $t("InGameMonitor.arrangement") }}</p>
            <div>
              <!-- <el-checkbox
                  v-for="(item, index) in options"
                  :key="item.id"
                  class="check-box-item"
                  :class="{ 'active': item.checked }"
                  v-model="item.checked"
                  :label="item.name"
                  @update:modelValue="() => handleChange(item, index)"
              ></el-checkbox> -->
              <el-radio
                v-for="(item, index) in options"
                :key="item.id"
                class="radio-item"
                :class="{ 'active': selectedItem === item.id }"
                v-model="selectedItem"
                :label="item.id"
                @change="handleChange(item, index)"
              >
                {{$t( item.name) }}
              </el-radio>
            </div>
          </div>
          <div class="monitor-position">
            <p class="monitor-title">{{ $t("InGameMonitor.monitorposition") }}</p>
            <div class="set-position">
              <div class="position-box">
                <ul class="SudokuPosition SudokuPosition2" id="positionBox1" ref="positionBox1">
                  <li>
                    <div ref="box1" data-ref="box1" class="positionBox" @click="setPosition($event.currentTarget as HTMLElement, 'upperLeft',9321, 1)"></div>
                  </li>
                  <li>
                    <div ref="box2" data-ref="box2" class="positionBox" @click="setPosition($event.currentTarget as HTMLElement, 'upper',9322, 2)"></div>
                  </li>
                  <li>
                    <div ref="box3" data-ref="box3" id="upperRight_init" class="positionBox" @click="setPosition($event.currentTarget as HTMLElement, 'upperRight',9323, 3)"></div>
                  </li>
                  <li>
                    <div ref="box4" data-ref="box4" class="positionBox" @click="setPosition($event.currentTarget as HTMLElement, 'left',9324, 4)"></div>
                  </li>
                  <li>
                    <div ref="box5" data-ref="box5" class="positionBox" @click="setPosition($event.currentTarget as HTMLElement, 'middle',9325, 5)"></div>
                  </li>
                  <li>
                    <div ref="box6" data-ref="box6" class="positionBox" @click="setPosition($event.currentTarget as HTMLElement, 'right',9326, 6)"></div>
                  </li>
                  <li>
                    <div ref="box7" data-ref="box7" class="positionBox" @click="setPosition($event.currentTarget as HTMLElement, 'lowerLeft',9327, 7)"></div>
                  </li>
                  <li>
                    <div ref="box8" data-ref="box8" class="positionBox" @click="setPosition($event.currentTarget as HTMLElement, 'lower',9328, 8)"></div>
                  </li>
                  <li>
                    <div ref="box9" data-ref="box9" class="positionBox" @click="setPosition($event.currentTarget as HTMLElement, 'lowerRight',9329, 9)"></div>
                  </li>
                </ul>
                <!-- <div id="SudokuShade"></div> -->
              </div>
              <p class="position-text">
                <span>{{ $t("InGameMonitor.canquickselectposition") }}</span>
                <span>{{ $t("InGameMonitor.curposition") }}<span>{{$t(XYPosition)}}</span></span>
              </p>
            </div>
          </div>
          <div class="monitor-bg">
            <p class="bg-title">{{ $t("InGameMonitor.background") }}</p>
            <div class="change-bg">
              <div class="bg-color" style="position: relative;  overflow: visible;">
                <span>{{ $t("InGameMonitor.backgroundcolor") }}</span>
                <el-input class="rgb-color" v-model="color1" @change="allBgChange"  :formatter="(e:string|number)=>String(e).toUpperCase()" :parser="(e:string)=>e.toLowerCase()"   />
                <el-color-picker :show-alpha="true" popper-class="dark_color_picker"    ref="bgcolorpicker" :show="true" v-model="color1" @change="allBgChange"   :predefine="predefineColors" />
                <div class="tranparent" style="background-color:transparent;width: 30px;height: 30px;left:300px;position: absolute;z-index: 99999;" @click="handleClick1(()=>{bgcolorpicker.show();bgcolorpicker.focus()})"></div>
              </div>
            </div>
          </div>
          <div class="monitor-bg">
            <p class="bg-title">{{ $t("InGameMonitor.font") }}</p>
            <div class="change-bg">
              <div class="bg-color">
                <span class="font-family">{{ $t("InGameMonitor.font") }}：</span>
                <el-select
                    v-model="selected"
                    filterable
                    popper-class="select-family"
                    size="small"
                    style="width: 240px;
                    background: inherit"
                    @change="allFontFamilyChange"
                >
                  <el-option
                      v-for="item in fontValues"
                      :key="item.fontFamily"
                      :label="item.fontFamily"
                      :value="item.fontFamily"
                  >
                    <span>{{ item.fontFamily }}</span>
                    <span :style="{
                      float: 'right',
                      color: 'var(--el-text-color-secondary)',
                      'font-size': '13px',
                      'font-family': item.fontFamily  // 绑定 font-family 为 item 的 fontFamily 属性
                    }"
                    >
                      {{ item.fontFamily }}
                    </span>
                  </el-option>
                </el-select>
              </div>
              <div class="bg-color">
                <span>{{ $t("InGameMonitor.fontsize") }}</span>
                <el-input class="rgb-color" v-model="textSize" @change="allFontsizeChange"   />
                <span>px</span>
              </div><div class="bg-color" style="position: relative;">
              <span>{{ $t("InGameMonitor.fontcolor") }}</span>
              <!-- <el-input class="rgb-color" v-model="color2" @change="allFontcolorChange"   @input="formatColor('color2')"  /> -->
              <el-input class="rgb-color" v-model="color2" @change="allFontcolorChange"  :formatter="(e:string|number)=>String(e).toUpperCase()" :parser="(e:string)=>e.toLowerCase()"   />
              <el-color-picker :show-alpha="true" ref="allFontcolorpicker" popper-class="dark_color_picker2"   :teleported="false" :show="true" @change="allFontcolorChange" v-model="color2" :predefine="predefineColors"  />
              <div class="tranparent" style="background-color:transparent;width: 30px;height: 30px;left:300px;position: absolute;z-index: 99999;" @click="handleClick1(()=>{allFontcolorpicker.show();allFontcolorpicker.focus()})"></div>
            </div><div class="bg-color">
              <span>{{ $t("InGameMonitor.style") }}</span>
              <div class="font-style">
                <el-checkbox v-model="font_style" @change="allBoldChange" :label="$t('InGameMonitor.bold')" size="large" />
              </div>
              <div class="font-style">
                <el-checkbox v-model="text_Shadow" @change="allStrokeChange" :label="$t('InGameMonitor.stroke')" size="large" />
              </div>
            </div>
            </div>
          </div>
          <div class="monitor-bg">
            <p class="bg-title">{{ $t("InGameMonitor.performance") }}</p>
            <div class="change-bg">
              <div class="bg-color">
                <span class="font-family">{{ $t("InGameMonitor.refreshTime") }}</span>
                <el-button @click="showSetting" type="primary" size="small">{{ $t("InGameMonitor.goGeneralSetting") }}</el-button>
              </div>

              <div class="bg-color">
                <span class="font-family">{{ $t("Setting.OLEDscreen") }}</span>
                <el-button @click="showOLEDSetting" type="primary" size="small">{{ $t("InGameMonitor.goGeneralSetting") }}</el-button>
              </div>
            </div>
          </div>
        </div>
        <div class="single_setting" v-show="singleSetting">
          <div class="single_title">
          <el-breadcrumb :separator-icon="ArrowRight">
            <el-breadcrumb-item  :to="{ path: '' }" @click="Back"><span style="color:#606266 ">{{ $t("InGameMonitor.generalstyle") }}</span></el-breadcrumb-item>
            <el-breadcrumb-item ><span style="color:white">{{ $t("InGameMonitor.selectMonitorItem") }}</span></el-breadcrumb-item>
          </el-breadcrumb>
            <div @click="setDef" class="hoverlighten">
              <el-icon><RefreshLeft /></el-icon>
              <p>{{ $t("InGameMonitor.restoredefault") }}</p>
            </div>
          </div>

            <!--样式-->
            <el-collapse>
                <el-collapse-item :title="$t('InGameMonitor.style2')" name="1">
                    <div class="monitor-bg">
                      <div class="change-bg">
                        <div class="bg-color" style="position: relative;">
                          <span>{{ $t("InGameMonitor.defaultValueColor") }}</span>
                          <el-input class="rgb-color" v-model="singleColor" @change="colorChange"  :formatter="(e:string|number)=>String(e).toUpperCase()" :parser="(e:string)=>e.toLowerCase()"   />
                          <el-color-picker ref="defaultColorpicker" :show-alpha="true" popper-class="dark_color_picker3"   :teleported="false"  @change="colorChange" :show="true" v-model="singleColor" :predefine="predefineColors" />
                          <div class="tranparent" style="background-color:transparent;width: 30px;height: 30px;left:300px;position: absolute;z-index: 99999;" @click="handleClick1(()=>{defaultColorpicker.show();defaultColorpicker.focus()})"></div>
                        </div>
                        <div class="bg-color" style="position: relative;">
                          <span>{{ $t("InGameMonitor.text1") }}</span>
                          <span style="color: #3579d5; margin-left: 25px;"> {{ getMonitorItemValue(InGameList[listIndex][chooseList]) || 0 }}</span>
                        </div>
                        <div class="bg-color">
                          <div class="addColorBox" style="width: 180px; height: 30px; border-radius: 4px; background-color: #458FF4;">
                              <el-icon class="addColorIcon"><Plus /></el-icon>
                              <span style="color: #FFF;"> {{ $t("InGameMonitor.addColorCondition") }} </span>
                          </div>
                        </div>
                      </div>
                      <div class="change-bg">
                        <div v-for="(con,index) in (InGameList[listIndex][chooseList].rangeColor)" :key="index">
                            {{ con.range }}
                        </div>
                        <div class="bg-color" style="position: relative;justify-content: space-between;">
                          <span class="colorCondition">{{ $t("InGameMonitor.ColorCondition1")}}</span>
                          <span class="deleteColorCondition">{{ $t("InGameMonitor.deleteColorCondition") }}</span>
                        </div>
                        <div class="bg-color">
                            <span>{{ $t("InGameMonitor.colorRange") }}</span>
                            <el-select
                                    filterable
                                    size="small"
                                    popper-class="select-family"
                                    style="width: 150px;background: inherit;"
                                    @change="handleShowRangeChange"
                                >
                                    <el-option
                                        v-for="item in showRange"
                                        :key="item.value"
                                        :label="item.name"
                                        :value="item.value"
                                    >
                                        <span>{{ $t(item.name) }}</span>
                                    </el-option>
                            </el-select>
                            <el-input class="rgb-color" style="margin-left: 10px;" v-model="singleColor" @change="colorChange"  :formatter="(e:string|number)=>String(e).toUpperCase()" :parser="(e:string)=>e.toLowerCase()"   />
                        </div>
                        <div class="bg-color" style="position: relative;">
                           <span>{{ $t("InGameMonitor.defaultValueColor") }}</span>
                          <el-input class="rgb-color" v-model="singleColor" @change="colorChange"  :formatter="(e:string|number)=>String(e).toUpperCase()" :parser="(e:string)=>e.toLowerCase()"   />
                          <el-color-picker ref="defaultColorpicker" :show-alpha="true" popper-class="dark_color_picker3"   :teleported="false"  @change="colorChange" :show="true" v-model="singleColor" :predefine="predefineColors" />
                          <div class="tranparent" style="background-color:transparent;width: 30px;height: 30px;left:300px;position: absolute;z-index: 99999;" @click="handleClick1(()=>{defaultColorpicker.show();defaultColorpicker.focus()})"></div>
                        </div>
                        <div style="border: 1px dashed #555; margin-top: 20px; width: 549px;"></div>
                      </div>
                    </div>
                    
                    <!-- <div class="monitor-bg">
                        <div class="change-bg">
                            <div class="bg-color">
                                <span class="chooseSensor">{{ $t("InGameMonitor.selectedSensor") }}</span>
                                <el-button @click="changeSensor(singletype , singleKey ,{type: singleTitle, type1: null}, singleUnit )" :style="{'color': SensorbuttonColor}"   class="Sensor-button">{{singleName}}</el-button>
                            </div><div class="bg-color">
                            <span>{{ $t("InGameMonitor.Des") }}：</span>
                            <el-input class="rgb-color single-des" @change="changeText()" v-model="singleDes"   />
                            <div class="titlenameshow" @click="toggleNameShow()">
                                <el-tooltip effect="dark" :content="$t('InGameMonitor.showTitle')" placement="top" :enterable="false" popper-class="custom_tooltip allht" >
                                    <el-icon v-show="singledesshow"><View /></el-icon>
                                </el-tooltip>
                                <el-tooltip effect="dark" :content="$t('InGameMonitor.hideTitle')" placement="top" :enterable="false" popper-class="custom_tooltip allht" >
                                    <el-icon v-show="!singledesshow"><Hide /></el-icon>
                                </el-tooltip>
                            </div>
                        </div>
                            <div class="bg-color">
                                <span>{{ $t("InGameMonitor.showStyle") }}</span>
                                <el-select
                                    :model-value="$t(methodText)"
                                    filterable
                                    size="small"
                                    popper-class="select-family"
                                    style="width: 240px;background: inherit; margin-left: 25px"
                                    @change="handleShowMethodsChange"
                                >
                                    <el-option
                                        v-for="item in showMethod"
                                        :key="item.value"
                                        :label="item.name"
                                        :value="item.value"
                                    >
                                        <span>{{ $t(item.name) }}</span>
                                    </el-option>
                                </el-select>
                            </div>
                        </div>
                    </div>
                    <div class="monitor-bg">
                        <p class="bg-title">{{ $t("InGameMonitor.fontStyle") }}</p>
                        <div class="change-bg">
                            <div class="bg-color">
                                <span class="font-family">{{ $t("InGameMonitor.font") }}：</span>
                                <el-select
                                    :popper-append-to-body="false"
                                    v-model="singleFamily"
                                    filterable
                                    size="small"
                                    popper-class="select-family"
                                    @change="selectFamily"
                                    style="width: 240px; background: inherit"
                                >
                                    <el-option
                                        v-for="item in fontValues"
                                        :key="item.fontFamily"
                                        :label="item.fontFamily"
                                        :value="item.fontFamily"
                                    >
                                        <span>{{ item.fontFamily }}</span>
                                        <span :style="{
                      float: 'right',
                      color: 'var(--el-text-color-secondary)',
                      'font-size': '13px',
                      'font-family': item.fontFamily  // 绑定 font-family 为 item 的 fontFamily 属性
                    }"
                                        >
                      {{ item.fontFamily }}
                    </span>
                                    </el-option>
                                </el-select>
                            </div>
                            <div class="bg-color">
                                <span>{{ $t("InGameMonitor.remarkSize") }}</span>
                                <el-input class="rgb-color" @input="TitlesizeChange" v-model="singleTitleSize"   />
                                <span>px</span>
                            </div>
                            <div class="bg-color">
                                <span>{{ $t("InGameMonitor.parameterSize") }}</span>
                                <el-input class="rgb-color" @input="sizeChange" v-model="singleSize"   />
                                <span>px</span>
                            </div>
                            <div class="bg-color" style="position: relative;">
                                <span>{{ $t("InGameMonitor.remarkColor") }}</span>
                                <el-input class="rgb-color" v-model="singleTitleColor" @change="TitlecolorChange"  :formatter="(e:string|number)=>String(e).toUpperCase()" :parser="(e:string)=>e.toLowerCase()"   />
                                <el-color-picker ref="singleTitleColorpicker" :show-alpha="true" popper-class="dark_color_picker2"   :teleported="false" @change="TitlecolorChange" :show="true" v-model="singleTitleColor" :predefine="predefineColors" />
                                <div class="tranparent" style="background-color:transparent;width: 30px;height: 30px;left:300px;position: absolute;z-index: 99999;" @click="handleClick1(()=>{singleTitleColorpicker.show();singleTitleColorpicker.focus()})"></div>
                            </div>
                            <div class="bg-color" style="position: relative;">
                                <span>{{ $t("InGameMonitor.parameterColor") }}</span>
                                <el-input class="rgb-color" v-model="singleColor" @change="colorChange"  :formatter="(e:string|number)=>String(e).toUpperCase()" :parser="(e:string)=>e.toLowerCase()"   />
                                <el-color-picker ref="singleColorpicker" :show-alpha="true" popper-class="dark_color_picker2"   :teleported="false"  @change="colorChange" :show="true" v-model="singleColor" :predefine="predefineColors" />
                                <div class="tranparent" style="background-color:transparent;width: 30px;height: 30px;left:300px;position: absolute;z-index: 99999;" @click="handleClick1(()=>{singleColorpicker.show();singleColorpicker.focus()})"></div>
                            </div>
                            <div class="bg-color">
                                <span>{{ $t("InGameMonitor.style") }}</span>
                                <div class="font-style">
                                    <el-checkbox @change="boldChange" v-model="singleBold" :label="$t('InGameMonitor.bold')"  size="large" />
                                </div>
                                <div class="font-style">
                                    <el-checkbox v-model="singleShadow" @change="StrokeChange" :label="$t('InGameMonitor.stroke')"  size="large" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="monitor-bg lineChart" v-show="methodWay === 1 && (!String(singleName).toLowerCase().includes('time') || singleName === 'Frame Time')">
                        <p class="bg-title">{{ $t("InGameMonitor.lineChart") }}</p>
                        <div class="change-bg">
                            <div class="bg-color" style="position: relative;">
                                <span>{{ $t("InGameMonitor.lineColor") }}</span>
                                <el-input class="rgb-color" v-model="lineColor" @change="lineColorChange"  :formatter="(e:string|number)=>String(e).toUpperCase()" :parser="(e:string)=>e.toLowerCase()"   />
                                <el-color-picker :show-alpha="true" popper-class="dark_color_picker2"   :teleported="false" :show="true" @change="lineColorChange" v-model="lineColor" :predefine="predefineColors" />
                            </div>
                            <div class="bg-color">
                                <span>{{ $t("InGameMonitor.lineThickness") }}</span>
                                <el-slider @change="lineSizeChange" :min="2" :max="9" v-model="lineSize" />
                                <span>px</span>
                            </div>
                            <div class="bg-color">
                                <span>{{ $t("InGameMonitor.areaHeight") }}</span>
                                <el-slider @change="lineHeightChange" :min="20" :max="120" v-model="lineHeight" />
                                <span>px</span>
                            </div>
                        </div>
                    </div> -->
                </el-collapse-item>
            </el-collapse>
            <!--排序-->
            <el-collapse>
                <el-collapse-item :title="$t('InGameMonitor.sort')" name="1">
                    <div class="monitor-bg">
                        <!--<p class="bg-title">{{ $t("InGameMonitor.sort") }}</p>-->
                        <div class="change-bg">
                            <div class="bg-color" style="flex-wrap: wrap;gap: 5px;">
                                <span>{{ $t("InGameMonitor.displacement") }}</span>
                                <el-button  @click="toMove()"  class="Sensor-buttonMove hoverlighten">{{ $t("InGameMonitor.Top") }}</el-button>
                                <el-button  @click="upMove()"  class="Sensor-buttonMove hoverlighten">{{ $t("InGameMonitor.up") }}</el-button>
                                <el-button  @click="downMove()"  class="Sensor-buttonMove hoverlighten">{{ $t("InGameMonitor.down") }}</el-button>
                            </div>
                        </div>
                    </div>
                </el-collapse-item>
            </el-collapse>
            <!--监控-->
            <el-collapse v-if="singleName !== 'Current time' && singleName !== 'Run time'">
                <el-collapse-item :title="$t('InGameMonitor.monitoring')" name="1">
                    <template #title="{ isActive }">
                        <div>
                          <span class="mr-10">{{$t('InGameMonitor.monitoring')}}</span>
                          <el-switch @click.stop="" active-color="#3579D5" @change="handleChangeSingleSettingMonitorSwitch" v-model="single_setting_monitor.switch"></el-switch>
                        </div>
                    </template>
                    <div class="monitor-bg">
                        <div class="calcResult">
                            <span note="当传感器数值">{{ $t('InGameMonitor.text1') }}</span>
                            <span v-show="single_setting_monitor.condition.includes('Thanthreshold')">
                                <span class="white-font" v-show="single_setting_monitor.condition.includes('big')">{{$t('InGameMonitor.biggerThan')}} </span>
                                <span class="white-font" v-show="single_setting_monitor.condition.includes('small')">{{$t('InGameMonitor.smallerThan')}} </span>
                                <span class="blue-font">{{single_setting_monitor.cur_threshold}}</span>
                                <span class="white-font" style="margin: 0 5px;">{{$t('InGameMonitor.percentage')}}</span>
                                <span class="blue-font">{{single_setting_monitor.threshold}}%</span>
                            </span>
                            <span v-show="!single_setting_monitor.condition.includes('Thanthreshold')">
                                <span class="white-font">{{$t('InGameMonitor.'+single_setting_monitor.condition)}}</span>
							                  <span class="blue-font">{{single_setting_monitor.condition.includes('Percent')?single_setting_monitor.threshold + '%':single_setting_monitor.cur_threshold}}</span>
                            </span>
                            <span note="，并且在">{{ $t('InGameMonitor.text2') }}</span>
							              <span class="blue-font" note1="等待时间" note2="秒">{{ $t('InGameMonitor.text3') }}{{single_setting_monitor.wait_time }}</span>
                            <span class="first-word-blue" note="秒内没有出现更高数值时，立即触发">{{ $t('InGameMonitor.text4') }}</span>
                            <span class="blue-font">{{ $t('InGameMonitor.'+single_setting_monitor.do_what) }}</span>
                        </div>
                        <div class="monitor-options">
                            <p class="option-title">
                                {{$t('InGameMonitor.condition')}}
                            </p>
                            <div class="option-box">
                              <el-select v-model="single_setting_monitor.condition" popper-class="select-family">
                                <el-option
                                  :label="$t('InGameMonitor.bigger')"
                                  value="bigger"
                                ></el-option>
                                <el-option
                                  :label="$t('InGameMonitor.smaller')"
                                  value="smaller"
                                ></el-option>
                                <el-option
                                  :label="$t('InGameMonitor.biggerThanthreshold')"
                                  value="biggerThanthreshold"
                                ></el-option>
                                <el-option
                                  :label="$t('InGameMonitor.smallerThanthreshold')"
                                  value="smallerThanthreshold"
                                ></el-option>
                                <el-option
                                  :label="$t('InGameMonitor.smallerPercent')"
                                  value="smallerPercent"
                                ></el-option>
                                <el-option
                                  :label="$t('InGameMonitor.biggerPercent')"
                                  value="biggerPercent"
                                ></el-option>
                              </el-select>
                            </div>
                        </div>
                        <div class="monitor-options"
                             v-show="single_setting_monitor.condition !== 'bigger' && single_setting_monitor.condition !== 'smaller'"
                        >
                            <p class="option-title">{{$t('InGameMonitor.'+single_setting_monitor.condition)}}</p>
                            <div class="option-box">
                                <el-input
                                    @input="handleInputThreshold"
                                    v-model="single_setting_monitor.threshold"
                                    :formatter="(value:any) => value + '%'"
                                    :parser="(value:string) => Number(value.replace('%', ''))"
                                >
                                </el-input>
                            </div>
                        </div>
                        <div class="monitor-options" v-show="!single_setting_monitor.condition.includes('Percent')">
                            <p class="option-title" note="初始阈值">{{$t('InGameMonitor.initThreshold')}}</p>
                            <div class="option-box">
                                <el-input-number
                                    :min="0"
                                    :step="1"
                                    :controls="false"
                                    step-strictly
                                    @change="single_setting_monitor.cur_threshold = single_setting_monitor.initial_threshold"
                                    v-model="single_setting_monitor.initial_threshold">
                                </el-input-number>
                            </div>
                            <span note="当前阈值" style="text-align: center">{{$t('InGameMonitor.curThreshold')}} {{single_setting_monitor.cur_threshold}}</span>
                            <el-button size="small" color="#3579D5" note="重置当前阈值" @click="single_setting_monitor.cur_threshold = single_setting_monitor.initial_threshold">{{$t('InGameMonitor.resetCurThreshold')}}</el-button>
                        </div>
                        <div class="monitor-options" v-show="!single_setting_monitor.condition.includes('Percent')">
                            <el-checkbox size="small" :label="$t('InGameMonitor.text5')" v-model="single_setting_monitor.is_update_threshold"></el-checkbox>
                        </div>
                        <div class="monitor-options">
                            <p class="option-title" note="等待时间">{{$t('InGameMonitor.text3')}}</p>
                            <div class="option-box">
                                <el-input-number
                                    :min="1"
                                    :step="1"
                                    :controls="false"
                                    step-strictly
                                    v-model="single_setting_monitor.wait_time">
                                </el-input-number>
                            </div>
                            <span note="秒">{{ $t('shutdownTimer.sec') }}</span>
                        </div>
                        <div class="monitor-options">
                            <p class="option-title">
                                {{$t('InGameMonitor.action')}}
                            </p>
                            <div class="option-box">
                                <el-select @change="handleChangeSingeleMonitorDoWhat" v-model="single_setting_monitor.do_what" popper-class="select-family">
                                    <el-option
                                        :label="$t('video.instantReplay')"
                                        value="replay"
                                    ></el-option>
                                    <el-option
                                        :label="$t('screenshotpage.screenshot')"
                                        value="screenshot"
                                    ></el-option>
                                </el-select>
                            </div>
                            <div v-show="single_setting_monitor.do_what === 'replay' && !isObsExists" class="install_info">
                                <span style="color: #bf4040;">{{$t('InGameMonitor.uninstallobs')}}</span>
                                <span class="downloading-btn" @click="downloadObs">{{$t('InGameMonitor.install')}}</span>
                                <span v-show="obsDownloading" class="iconfont icon-holding"></span>
                            </div>
                        </div>
                        <div class="monitor-options" style="flex-wrap: wrap;" v-show="!single_setting_monitor.condition.includes('Percent')" >
                            <el-checkbox size="small" :label="$t('InGameMonitor.text6')" v-model="single_setting_monitor.is_show_threshold"></el-checkbox>
                            <el-checkbox size="small" v-model="single_setting_monitor.is_init_threshold_big1000">
                                <span>{{$t('InGameMonitor.text7')}}</span>
                                <span style="color: #3579d5;margin: 0 3px;">{{ $t('InGameMonitor.'+single_setting_monitor.condition)}}</span>
                                <span>{{$t('InGameMonitor.text10')}}</span>
                            </el-checkbox>
                        </div>
                    </div>
                </el-collapse-item>
            </el-collapse>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss">
.dark_color_picker3 {
    inset: auto!important;
    top: 39px!important;
    left: 140px!important;
}
.dark_color_picker,.dark_color_picker3 {

  &.el-popper.is-light {
    background: #1C1C22;
    border-color: #1C1C22;

    .el-color-dropdown__link-btn {
      display: none;
    }
    .el-button.is-plain {
      --el-button-hover-bg-color: #d7d7d7
    }
  }
}

.dark_color_picker2 {
    inset: auto!important;
    bottom: 39px!important;
    left: 140px!important;
}
.dark_color_picker,.dark_color_picker2 {

  &.el-popper.is-light {
    background: #1C1C22;
    border-color: #1C1C22;

    .el-color-dropdown__link-btn {
      display: none;
    }
    .el-button.is-plain {
      --el-button-hover-bg-color: #d7d7d7
    }
  }
}

.single_setting.el-breadcrumb__item:last-child .el-breadcrumb__inner {
  color: white;
}
.el-select__popper.el-popper {
  border: none;
}
.single_setting .el-collapse + .el-collapse {
    margin-top: 10px;
}
.select-family {
  background: #313242;
  .el-select-dropdown__item.is-hovering {
    background-color: rgb(256,256,256,0.3);
    color: white;
  }



  .el-select-dropdown__list {
    background: #313242;
  }
  .el-select-dropdown__item {
    color: white;
  }
  .el-select-dropdown__item.is-selected {
    color: white;
  }
}
.import-button:last-child {
  margin-left: 15px;
}
.monitor-container {
  color: rgb(255,255,255);
  .import-export{
    display: flex;
    .import-button{
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .Sensor-buttonMove{
      cursor: pointer;
    }
  }
  .custom-file-input {
    // padding: 7px 16px!important;
    border: none;
    // margin-left: 20px;
  }
  .el-button.is-plain {
    background-color: #1193F8;
    box-shadow: none;
    color: white;
  }
  .el-button.is-text:not(.is-disabled):hover {
    background-color: #1193F8;
    color: white;

  }
  .el-select__placeholder {
    color: white;
  }
  .el-input__inner {
    color: white;
  }
  .el-input--small .el-input__wrapper {
    background-color: #22232E;
    box-shadow: none;
  }
  .el-color-picker__panel.el-popper {
    background: #313242;
    border-color: #313242 !important;
  }
  // .el-icon svg{
  //   font-size: 30px;
  // }
}

</style>
<style lang="scss" scoped>
.dark{margin-top: 10px;}
.single_setting {
    .mr-10 {
        margin-right: 10px;
    }
  .single_title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    div {
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-bottom: 30px;
      i,p {
        color: rgba(53, 121, 213, 1);
      }
      i{
        margin-right: 5px;
      }
    }
  }

    :deep(.el-collapse) {
        --el-collapse-border-color: #323340;
        --el-collapse-header-bg-color: #323340;
        --el-collapse-content-bg-color: #323340;
        --el-collapse-header-height: 34px;
        border-radius: 2px;
        overflow: hidden;
    }

    :deep(.el-collapse-item__header) {
        padding-left: 10px;
    }
    :deep(.el-collapse-item__content) {
        padding-left: 10px;
        padding-bottom: 10px;
    }
}
.not-found-mask{
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  // z-index: 1;
}
.dark-color-picker {
  background-color: #343647;
  border: none;
}
.el-popper.is-light {
  --el-bg-color-overlay: #343647;
  background: var(--el-bg-color-overlay);
}
.top_setting {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 40px;
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
  // margin-top: 3px;
  .top_switch{
    display: flex;
    align-items: center;
    color: #3579d5;
  }
  .hotkey_set{
    display: flex;

    .ingame_hotkey {
      margin-right: 40px;
      display: flex;
      font-size: 12px;
      align-items: center;
    }
    .tab_hotkey{
      display: flex;
      font-size: 12px;
      align-items: center;
      span:last-child {
        margin-left: 10px;
      }
    }
  }
}
.el-breadcrumb {
  margin-bottom: 30px;
}
.chooseSensor {
  margin-right: 25px;
}
.Sensor-button {
  border: none;
  width: 240px;
  height: 30px;
  background: #343647;
  color: white;
  border-radius: 2px;
  span {
    white-space: break-spaces;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
}
.Sensor-buttonMove{
  border: none;
  padding: 0 16px 0 10px;
  height: 30px;
  background: #343647;
  color: white;
  border-radius: 2px;
  display: flex;
  align-items: center;
  span {
    white-space: break-spaces;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
  .guide{margin-left: 5px;color: #3579D5;}
}
.Sensor-buttonMove2{
  background: transparent!important;
}
.Sensor-button2 {
  margin-left: 11px;
  border: none;
  width: 180px;
  height: 30px;
  background: #343647;
  color: white;
  border-radius: 2px;
}
.single-des {
  width: 240px!important;
}
.game_list_item {
  width: 548px;
  height: 30px;
  border-radius: 4px;
  transition: background-color 0.1s ease;
  cursor: pointer!important;
}
.can-drag{
  cursor: move!important;
}
.game_list_item:hover {
  background-color: rgba(62, 64, 80, 1);
}
.game_list_item.active {
  background-color: rgba(62, 64, 80, 1);
  cursor: grabbing;
}
.icon-group {
  margin: 0 auto;
  display: inline-flex;
  cursor: pointer;
  position: relative;
  .Mousedevice{
    width: 25px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    z-index: 9999;
    // border: 1px solid red;
  }
  // gap: 6px;
  .icon-group-item{
    width: 25px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    .icon-Upload{
      // color: #3579D5;
    }
  }
}

.secondary-icon {
  display: none; /* 初始时第二个图标不显示 */
}
.font-style {
  margin-left: 25px;
}
.SudokuPosition {
  width: 62px;
  height: 60px;
  border: 1px solid #3579D5;
  border-radius: 4px;
  display: flex;
  flex-wrap: wrap;
}
.SudokuPosition>li {
  width: 20px;
  height: 20px;
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(17, 147, 248, 0.3);
  padding: 1px;
}
.SudokuPosition>li>div{
  width: 100%;
  height: 100%;
  border-radius: 4px;
  cursor: pointer;
}
.SudokuPosition>li>div:hover{
  background-color: #1C4468;
}
.SudokuPosition>li>.positionActive:hover{
  background-color: #3579D5;
}
.positionActive{
  background-color: #3579D5;
}
::v-deep(.el-checkbox__label) {
  color: white;
}

html {
  box-sizing: border-box;
}

.border-transition {
  transition: border-color 0.1s ease;
  border: 2px solid transparent; /* 初始状态边框为透明 */

}

.border-transition:hover {
  border: 2px solid #3579D5;
}

*, *::before, *::after {
  box-sizing: inherit;
}
.scroll::-webkit-scrollbar {
  width: 5px;
  transition: 0.25s;
}

.scroll::-webkit-scrollbar-thumb {
  background: #71738C;
  border-radius: 3px;
}

.scroll::-webkit-scrollbar-thumb:hover {
  background-color: #71738C;
}

.scroll::-webkit-scrollbar-track {
  background-color: #2D2E39;
  width: 2px;
}

.scroll::-webkit-scrollbar {
  width: 5px;
  transition: 0.25s;
}

.scroll::-webkit-scrollbar-thumb {
  background: #71738C;
  border-radius: 3px;
}

.scroll::-webkit-scrollbar-thumb:hover {
  background-color: #71738C;
}

.scroll::-webkit-scrollbar-track {
  background-color: #2D2E39;
  width: 2px;
}
.all-Button {
  .el-button {
    // width: 120px;
    padding: 0 16px;
    height: 40px;
  }
}

// .el-button:hover {
//   background-color: rgba(62, 64, 80, 1);
//   border-color: rgba(53, 121, 213, 1);
// }
.el-button>span {
  font-size: 12px;
}
.monitor-container{
  box-sizing: border-box;
  padding-left: 20px;
  padding-right: 20px;
  //  overflow-y: auto;
  overflow: hidden;
  .all-Button {
    padding-top: 15px;
    .first-button{
      border: 1px solid #3579D5;
    }
  }
  .title {
    font-size: 14px;
    color: #3579D5;
    line-height: 25px;
  }
  .content-all {
    padding-top: 10px;
    //height: 85%;
    display: flex;
    // justify-content: space-between;
    .content-left {
      min-height: 630px;
      padding: 25px 20px 20px;
      height: 98%;
      width: 600px;
      box-shadow: 0 0 5px 0 rgba(0,0,0,0.3);
      border-radius: 4px;
      display: flex;
      flex-flow: column nowrap;
      background: rgba(45, 46, 57, .8);
      .list-data {
        margin-top: 10px;
        width: 560px;
        height: 100%;
        overflow: auto;
        background: rgba(34, 35, 46,.8);
        border-radius: 4px;
        .list-title {
          padding-top: 2px;
          padding-left: 2px;
          padding-right: 2px;
          display: flex;
          gap: 2px;
          span {
            font-size: 12px;
            color: #727281;
            line-height: 1;
          }
          div {
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .data {
            width: 260px;
            height: 20px;
            background: #343647;
            border-radius: 4px;
          }
          .des {
            width: 150px;
            height: 20px;
            background: #343647;
            border-radius: 4px;
          }
          .fn {
            width: 142px;
            height: 20px;
            background: #343647;
            border-radius: 4px;
          }
        }
        .all-sensor {
          height: 95%;
          overflow: auto;
          overflow-x: hidden;
          li {
            margin-top: 6px;
            margin-left: 2px;
            margin-right: 2px;
            display: flex;
            gap: 2px;
            align-items: center;
            cursor: pointer;
            .parcel{
              width: 548px;
              height: 30px;
              display: flex;
              align-items: center;
              // cursor: pointer;
              position: relative;
            }
            span {
              text-align: center;
              font-size: 12px;
            }
          }
        }
        .not-found{
          .data_value{
            color: #D72A2A;
          }
          .des_value{
            color: #D72A2A;
          }
        }
        .data_value {
          width: 260px;
        }
        .des_value {
          width: 150px;
          text-overflow: ellipsis;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          display: -webkit-inline-box;
          overflow: hidden;
        }
        .fn_value {
          width: 142px;
        }
      }
      .select-all {
        display: flex;
        margin-top: 10px;
        .left-button {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 20px;
          height: 40px;
          background: #343647;
          border-radius: 4px;
        }
        .right-button {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-left: auto;
          width: 20px;
          height: 40px;
          background: #343647;
          border-radius: 4px;
        }
        .all-list {
          margin-left: 10px;
          display: flex;
          gap: 10px;
          .list {
            display: flex;
            justify-content: center;
            align-items: center;
            padding-left: 9px;
            padding-right: 9px;
            gap: 25px;
            width: 100px;
            height: 40px;
            background: rgba(52, 54, 71, 1);
            border-radius: 4px;
            p {
              line-height: 40px;
              font-size: 12px;
              color: rgb(255,255,255);
            }
          }
        }
        .add-button {
          width: 40px;
          height: 40px;
          background: #343647;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .add-monitor {
        margin-top: 10px;
        width: 560px;
        height: 30px;
        background: #343647;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        cursor: pointer;
        .add-monitor-text {
          font-size: 12px;
          color: rgb(255,255,255);
          line-height: 30px;
        }
        .add-monitor-text:hover {
          cursor: pointer;
        }
      }
      .left-title {
        display: flex;
        align-items: center;
        justify-content: space-between;

        p {
          padding-left: 10px;
          font-size: 12px;
          color: rgba(255, 255, 255, 1);
          font-weight: bold;
          line-height: 20px;
        }
      }
    }
    .content-right {
      font-size: 12px;
      min-height: 630px;
      padding: 25px 20px 20px;
      margin-left: 20px;
      width: 620px;
      height: 98%;
      box-shadow: 0 0 5px 0 rgba(0,0,0,0.3);
      border-radius: 4px;
      background: rgba(45, 46, 57, .8);
      overflow: auto;
      .right-title {
        div {
          display: flex;
          align-items: center;
        }
        div:last-child {
          cursor: pointer;
          i,p {
            color: #3579D5;
          }
          i{
            margin-right: -5px;
          }
        }
        display: flex;
        align-items: center;
        justify-content: space-between;
        span {
          color: #3579D5;
        }
        p {
          padding-left: 10px;
          font-size: 12px;
          color: rgba(255, 255, 255, 1);
          //font-weight: bold;
          line-height: 20px;
        }
      }
      .arranged {
        display: flex;
        margin-top: 10px;
        align-items: center;
        p {
          font-size: 12px;
          margin-right: 78px;
        }
        span {
          color: white;
        }

        :deep(.radio-item) {
          --el-color-primary: #3579D5;
        }
      }
      .monitor-position {
        margin-top: 15px;
        .monitor-title {
          font-size: 12px;
        }
        .set-position {
          margin-top: 15px;
          display: flex;
          margin-bottom: 20px;
          .position-box {

          }
          .position-text {
            margin-left: 72px;
            display: flex;
            flex-direction: column;
            line-height: 16px;
            span:nth-child(2) {
              margin-top: 12px;
            }
            span {
              span {
                color: #3579D5;
              }
            }
          }
        }
      }
      .monitor-bg {
        //margin-top: 15px;
        .bg-title {
          margin-bottom: 5px;
        }
        .change-bg {
          display: flex;
          flex-direction: column;
          margin-bottom: 10px;
          ::v-deep(.el-input-number--small) {
            width: 60px;
          }
          ::v-deep(.el-slider) {
            width: 360px;
            margin-left: 25px;
          }
          ::v-deep(.show-input) {
            background: rgba(52, 54, 71, 1);
          }
          ::v-deep(.el-input__wrapper) {
            background-color: rgba(34, 35, 46, 1);
          }
          ::v-deep(.el-input__inner) {
            color: white;
          }
          ::v-deep(.el-slider__button) {
            width: 15px;
            height: 15px;
          }
          ::v-deep(.el-input__wrapper) {
            box-shadow: none;
          }
          ::v-deep(.el-select__wrapper) {
            min-height: 30px;
            background-color: rgba(52, 54, 71, 1);
            cursor: pointer;
          }
          ::v-deep(.el-select__caret) {
            color: #3579D5;
          }

          ::v-deep(.el-select__wrapper) {
            box-shadow: none;
          }

          .bg-alpha {
            display: flex;
            align-items: center;
          }
          .bg-color:not(:first-child) {
            margin-top: 10px;

          }
          .bg-color {
            display: flex;
            align-items: center;
            .addColorBox{
              width: 180px;
              height: 30px;
              border-radius: 4px;
              background-color: rgb(69, 143, 244);
              display: flex;
              align-items: center;
              cursor: pointer;
              .addColorIcon{
                margin-left: 26px;
                margin-right: 10px;
                border: 1px solid;
                border-radius: 4px;
              }
            }
            .addColorBox:hover{
              background-color: rgb(55, 114, 197) !important;
            }
            .colorCondition{
              width: 37px;
              height: 11px;
              font-weight: 400;
              font-size: 12px;
              color: #458FF4;
              line-height: 16px;
            }
            .deleteColorCondition{
              width: 52px;
              height: 13px;
              font-weight: 400;
              font-size: 12px;
              color: #458FF4;
              line-height: 16px;
              text-decoration-line: underline;
              text-align: right;
              margin-right: 19px;
              cursor: pointer;
            }
            span {
              min-width: 112px;
              color: rgba(114, 114, 129, 1);
            }
            .rgb-color {
              font-size: 12px;
              margin-left: 25px;
              margin-right: 11px;
              width: 150px;
              height: 30px;
              background: #22232E;
              border-radius: 2px;
            }
            .font-family {
              margin-right: 25px;
            }
          }
        }
        .titlenameshow{
          cursor: pointer;
          .el-icon{
            font-size: 14px;
          }
        }
            .calcResult {
            font-size: 12px;
            color: #999999;
                .white-font {
                    color: #ffffff;
                    margin: 0 3px;
                }
                .blue-font {
                    color: #3579D5;
                    margin: 0 3px;
                }
            }
            .monitor-options {
                margin-top: 10px;
                font-size: 12px;
                display: flex;
                flex-flow: row nowrap;
                align-items: center;
                gap: 10px;

                p.option-title {
                    color: #999999;
                    width: 106px;
                }

                .option-box {
                    width: 160px;
                    height: 30px;
                    :deep(.el-select) {
                      --el-border-color-hover: transparent;
                      --el-border-color: transparent;
                    }
                    :deep(.el-select__wrapper) {
                      background-color: #3F4054;
                    }
                    :deep(.el-select__wrapper.is-focused) {
                      box-shadow: none;
                    }
                    :deep(.el-input__wrapper) {
                      background-color: #22232E;
                      box-shadow: none;
                    }
                    :deep(.el-input-number) {
                        width: 160px;
                    }
                }
                :deep(.el-checkbox.el-checkbox--small .el-checkbox__label) {
                    color: #999999;
                }
                .install_info {
                    display: flex;
                    align-items: center;
                    span:first-child {
                        color: #ffffff;
                    }
                    .downloading-btn {
                        margin-left:5px;
                        cursor: pointer;
                        color: #3579d5;
                    }
                    .iconfont {
                        color: #3579d5;
                        animation: 3s linear infinite ro360;
                    }
                }
            }
      }

      .monitor-bg.lineChart {
        ::v-deep(.el-slider) {
          width: 100px;
          margin-left: 25px;
          margin-right: 25px;
        }
      }
    }
  }
.highlight {
  background-color: #363030;
  opacity: 0.5;
  transform: scale(1.05);
  transition: transform 0.2s ease;
}
.dragging {
  opacity: 0.5;
  transform: scale(1.05);
  transition: transform 0.2s ease;
}
.shadow-item{
  display: flex;
}
}


</style>
<style lang="scss">
@keyframes ro360 {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
.monitor-container.dark,.dark-color-pick{color-scheme:dark;--el-color-primary:#3579D5;--el-color-primary-light-3:#3375b9;--el-color-primary-light-5:#2a598a;--el-color-primary-light-7:#213d5b;--el-color-primary-light-8:#1d3043;--el-color-primary-light-9:#18222c;--el-color-primary-dark-2:#66b1ff;--el-color-success:#67c23a;--el-color-success-light-3:#4e8e2f;--el-color-success-light-5:#3e6b27;--el-color-success-light-7:#2d481f;--el-color-success-light-8:#25371c;--el-color-success-light-9:#1c2518;--el-color-success-dark-2:#85ce61;--el-color-warning:#e6a23c;--el-color-warning-light-3:#a77730;--el-color-warning-light-5:#7d5b28;--el-color-warning-light-7:#533f20;--el-color-warning-light-8:#3e301c;--el-color-warning-light-9:#292218;--el-color-warning-dark-2:#ebb563;--el-color-danger:#f56c6c;--el-color-danger-light-3:#b25252;--el-color-danger-light-5:#854040;--el-color-danger-light-7:#582e2e;--el-color-danger-light-8:#412626;--el-color-danger-light-9:#2b1d1d;--el-color-danger-dark-2:#f78989;--el-color-error:#f56c6c;--el-color-error-light-3:#b25252;--el-color-error-light-5:#854040;--el-color-error-light-7:#582e2e;--el-color-error-light-8:#412626;--el-color-error-light-9:#2b1d1d;--el-color-error-dark-2:#f78989;--el-color-info:#909399;--el-color-info-light-3:#6b6d71;--el-color-info-light-5:#525457;--el-color-info-light-7:#393a3c;--el-color-info-light-8:#2d2d2f;--el-color-info-light-9:#202121;--el-color-info-dark-2:#a6a9ad;--el-box-shadow:0px 12px 32px 4px rgba(0,0,0,0.36),0px 8px 20px rgba(0,0,0,0.72);--el-box-shadow-light:0px 0px 12px rgba(0,0,0,0.72);--el-box-shadow-lighter:0px 0px 6px rgba(0,0,0,0.72);--el-box-shadow-dark:0px 16px 48px 16px rgba(0,0,0,0.72),0px 12px 32px #000000,0px 8px 16px -8px #000000;--el-bg-color-page:#0a0a0a;--el-bg-color:#141414;--el-bg-color-overlay:#1d1e1f;--el-text-color-primary:#E5EAF3;--el-text-color-regular:#CFD3DC;--el-text-color-secondary:#A3A6AD;--el-text-color-placeholder:#8D9095;--el-text-color-disabled:#6C6E72;--el-border-color-darker:#636466;--el-border-color-dark:#58585B;--el-border-color:#4C4D4F;--el-border-color-light:#414243;--el-border-color-lighter:#363637;--el-border-color-extra-light:#2B2B2C;--el-fill-color-darker:#424243;--el-fill-color-dark:#39393A;--el-fill-color:#303030;--el-fill-color-light:#262727;--el-fill-color-lighter:#1D1D1D;--el-fill-color-extra-light:#191919;--el-fill-color-blank:transparent;--el-mask-color:rgba(0,0,0,0.8);--el-mask-color-extra-light:rgba(0,0,0,0.3)}html.dark .el-button{--el-button-disabled-text-color:rgba(255,255,255,0.5)}html.dark .el-card{--el-card-bg-color:var(--el-bg-color-overlay)}html.dark .el-empty{--el-empty-fill-color-0:var(--el-color-black);--el-empty-fill-color-1:#4b4b52;--el-empty-fill-color-2:#36383d;--el-empty-fill-color-3:#1e1e20;--el-empty-fill-color-4:#262629;--el-empty-fill-color-5:#202124;--el-empty-fill-color-6:#212224;--el-empty-fill-color-7:#1b1c1f;--el-empty-fill-color-8:#1c1d1f;--el-empty-fill-color-9:#18181a}
.top_setting .el-switch.is-checked .el-switch__core{
  background-color: #3579D5!important;
  border:1px solid #3579D5!important;
}
.content-right .el-button>span{
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  display: block;
  height: 30px;
  line-height: 30px;
}
.hoverlighten:hover {
    filter: brightness(1.2);
    -webkit-filter: brightness(1.2);
    -webkit-tap-highlight-color: transparent;
}
.monitor-container .el-input__inner {
    color: #fff !important;
    text-align: center;
}
.monitor-container .shortcut{
  margin: 0 15px;
}
.el-popper.is-light .el-popper__arrow:before {
    border: 1px solid #343647 !important;
}
</style>




