const it = {
  "update": {
    "Game": "Game",
    "PP": "PP",
    "upgradingInProgress": "Aggiornamento in corso",
    "theModuleIsBeingUpdated": "Aggiornamento del modulo",
    "dataIsBeingUpdated": "Aggiornamento dati in corso...",
    "checkingUpdate": "Verifica degli aggiornamenti",
    "checkingUpgrade": "Verifica aggiornamenti",
    "loadingProgramComponent": "Caricamento dei componenti del programma...",
    "loadingHotkeyModules": "Componente Hotkey in caricamento",
    "loadingGPPModules": "Caricamento dei componenti GamePP",
    "loadingBlackWhiteList": "Elenco nero/Elenco bianco in caricamento",
    "loadingGameSetting": "Caricamento dei parametri di configurazione del gioco...",
    "loadingUserAbout": "Caricamento dei dati di autenticazione utente",
    "loadingGameBenchmark": "Caricamento punteggio gioco",
    "loadingHardwareInfo": "Caricamento della componente delle informazioni hardware",
    "loadingDBModules": "Caricamento modulo database in corso...",
    "loadingIGCModules": "Caricamento modulo IGC",
    "loadingFTPModules": "Caricamento del modulo di supporto FTP in corso",
    "loadingDialogModules": "Caricamento modulo finestra di dialogo",
    "loadingDataStatisticsModules": "Caricamento del modulo di statistiche in corso",
    "loadingSysModules": "Sto caricando i componenti del sistema",
    "loadingGameOptimization": "Caricamento dell'ottimizzazione del gioco",
    "loadingGameAcceleration": "Caricamento dell'accelerazione del gioco",
    "loadingScreenshot": "Caricamento screenshot del video",
    "loadingVideoComponent": "Caricamento del componente di compressione video",
    "loadingFileFix": "Caricamento della riparazione del file",
    "loadingGameAI": "Caricamento della qualità dell'IA del gioco",
    "loadingNVAPIModules": "Caricamento del modulo NVAPI",
    "loadingAMDADLModules": "Caricamento del modulo AMDADL in corso",
    "loadingModules": "Caricamento modulo in corso"
  },
  "messages": {
    "append": "Aggiungi",
    "confirm": "Conferma",
    "cancel": "Annulla",
    "default": "Predefinito",
    "quickSelect": "Selezione rapida",
    "onoffingame": "Abilita/Disabilita monitoraggio in-game:",
    "changeKey": "Clicca per modificare la combinazione di tasti",
    "clear": "Svuota",
    "hotkeyOccupied": "Il tasto di scelta rapida è già in uso, impostare un nuovo tasto!",
    "minimize": "Minimizza",
    "exit": "Esci",
    "export": "Esporta",
    "import": "Importa",
    "screenshot": "Screenshot",
    "showHideWindow": "Visualizza/Nascondi finestra",
    "ingameControlPanel": "Pannello di controllo nel gioco",
    "openOrCloseGameInSettings": "Attiva/Disattiva pannello impostazioni in-game",
    "openOrCloseGameInSettings2": "Premi questo tasto di scelta rapida per abilitare",
    "openOrCloseGameInSettings3": "Attiva/Disattiva monitoraggio nel gioco",
    "openOrCloseGameInSettings4": "Attiva/Disattiva filtro di gioco",
    "startManualRecord": "Avvia/Arresta Registrazione Statistiche Manuale",
    "performanceStatisticsMark": "Marcatore di statistiche delle prestazioni",
    "EnableAIfilter": "È necessario premere questa combinazione di tasti per attivare il filtro AI",
    "Start_stop": "Avvia/Ferma Registrazione Statistica Manuale",
    "pressureTest": "Test di stress",
    "moduleNotInstalled": "Modulo funzionale non installato",
    "installingPressureTest": "Installazione del modulo di test di stress in corso...",
    "importFailed": "Importazione non riuscita",
    "gamepp": "GamePP",
    "copyToClipboard": "Copiato negli appunti"
  },
  "home": {
    "homeTitle": "Home",
    "hardwareInfo": "Informazioni hardware",
    "functionIntroduction": "Funzionalità",
    "fixedToNav": "Fissa alla barra di navigazione",
    "cancelFixedToNav": "Sganciare dalla barra di navigazione",
    "hardwareInfoLoading": "Caricamento informazioni hardware...",
    "performanceStatistics": "Statistiche di Rendimento",
    "updateNow": "Aggiorna ora",
    "recentRun": "Attività Recente",
    "resolution": "Risoluzione:",
    "duration": "Durata:",
    "gameFilter": "Filtro di gioco",
    "gameFilterHasAccompany": "Il filtro del gioco è ora attivo",
    "gameFilterHasAccompany2": "Gli utenti giocano a giochi come Cyberpunk, APEX e Hogwarts Legacy",
    "currentList": "Elementi di monitoraggio nella lista attuale",
    "moreFunction": "Benchmark, test di stress, monitoraggio del desktop e altre funzionalità sono in fase di sviluppo.",
    "newVersion": "Nuova versione disponibile !",
    "discoverUpdate": "Aggiornamento trovato!",
    "downloading": "Download in corso",
    "retry": "Riprova",
    "erhaAI": "2HaAI",
    "recordingmodule": "Questa funzione dipende dal modulo di registrazione",
    "superPower": "Modo Ultra",
    "autoRecord": "Registra automaticamente i momenti di kill nel gioco e salva facilmente i momenti salienti",
    "externalDevice": "Illuminazione Dinamica delle Periferiche",
    "linkage": "Attivare scene di kill nei giochi e visualizzarle tramite dispositivi periferici connessi",
    "AI": "Test delle prestazioni dell'AI",
    "test": "Testa i modelli di IA con GPU e visualizza il punteggio delle prestazioni IA",
    "supportedGames": "Giochi supportati",
    "games": "Naraka: Bladepoint, Delta Force, FragPunk, League of Legends, PUBG, Valorant, The Finals, APEX Legends, COD20",
    "videoRecording": "Registrazione video",
    "videoRecording2": "Funzione di registrazione video basata su OBS, supporta l'aggiustamento del bitrate video e dei fotogrammi al secondo (FPS) per soddisfare diverse esigenze di qualità e fluidità; inoltre supporta \"Riproduzione Istantanea\", premi il tasto di scelta rapida per salvare i momenti salienti in qualsiasi momento!",
    "addOne": "Ottieni gratis",
    "gamePlatform": "Piattaforma di giochi",
    "goShop": "Vai alla pagina del negozio",
    "receiveDeadline": "Termine per la Richiesta Successiva",
    "2Ai": "2 Risate AI",
    "questionDesc": "Descrizione del problema",
    "inputYourQuestion": "Inserire qui le proprie suggerimenti o commenti。",
    "uploadLimit": "Caricare fino a 3 immagini locali in formato JPG/PNG/BMP",
    "email": "E-mail",
    "contactWay": "Informazioni di contatto",
    "qqNumber": "Numero QQ (opzionale)",
    "submit": "Invia"
  },
  "hardwareInfo": {
    "hardwareOverview": "Panoramica Hardware",
    "copyAllHardwareInfo": "Copia tutte le informazioni hardware",
    "processor": "Processore",
    "coreCount": "Core:",
    "threadCount": "Numero di Thread:",
    "currentFrequency": "Frequenza Attuale:",
    "currentVoltage": "Tensione attuale:",
    "copy": "Copia",
    "releaseDate": "Data di lancio",
    "codeName": "Nome in codice",
    "thermalDesignPower": "Potenza di Design Termico",
    "maxTemperature": "Temperatura Massima",
    "graphicsCard": "Scheda grafica",
    "brand": "Marca:",
    "streamProcessors": "Processore di flusso:",
    "Videomemory": "Memoria video:",
    "busSpeed": "Velocità del Bus",
    "driverInfo": "Informazioni del driver",
    "driverInstallDate": "Data di installazione del driver",
    "hardwareID": "ID hardware",
    "motherboard": "Scheda madre",
    "chipGroup": "Chipset:",
    "BIOSDate": "Data BIOS",
    "BIOSVersion": "Versione BIOS",
    "PCIESlots": "slot PCIe",
    "PCIEVersion": "Versione PCIe supportata",
    "memory": "Memoria",
    "memoryBarCount": "Quantità:",
    "totalSize": "Dimensione:",
    "channelCount": "Canale:",
    "Specificmodel": "Modello specifico",
    "Pellet": "Produttore di Particelle",
    "memoryBarEquivalentFrequency": "Frequenza Effettiva della Memoria:",
    "hardDisk": "Disco rigido",
    "hardDiskCount": "Numero di dischi rigidi:",
    "actualCapacity": "Capacità Effettiva",
    "type": "Tipo",
    "powerOnTime": "Tempo di accensione",
    "powerOnCount": "Cicli di Accensione",
    "SSDRemainingLife": "Durata di vita residua SSD",
    "partitionInfo": "Informazione partizione",
    "hardDiskController": "Controller del Disco Rigido",
    "driverNumber": "Numero di serie del disco",
    "display": "Monitor",
    "refreshRate": "Frequenza di aggiornamento:",
    "screenSize": "Dimensione schermo:",
    "inches": "Pollici",
    "productionDate": "Data di Produzione",
    "supportRefreshRate": "Frequenza di aggiornamento supportata",
    "screenLongAndShort": "Dimensioni dello schermo",
    "systemInfo": "Informazioni di Sistema",
    "version": "Versione",
    "systemInstallDate": "Data di installazione del sistema",
    "systemBootTime": "Ora di Avvio Corrente",
    "systemRunTime": "Tempo di esecuzione",
    "Poccupied": "P Utilizzo",
    "Eoccupied": "E Occupato",
    "occupied": "Occupato",
    "temperature": "Temperatura",
    "Pfrequency": "P Frequenza",
    "Efrequency": "Frequenza E",
    "thermalPower": "Potenza Termica",
    "frequency": "Frequenza",
    "current": "Corrente",
    "noData": "Nessun dato",
    "loadHwinfo_SDK": "Impossibile caricare Hwinfo_SDK.dll, impossibile leggere i dati di hardware/sensore",
    "loadHwinfo_SDK_reason": "Cause possibili di questo problema:",
    "reason": "Motivo",
    "BlockIntercept": "Bloccato dal software antivirus, ad esempio: 2345 Software Antivirus (Processo di Difesa Attiva 2345, Processo di Difesa Attiva McAfee)",
    "solution": "Soluzione:",
    "solution1": "Dopo aver chiuso e disinstallato i processi correlati, riavviare GamePP",
    "solution2": "Scollegare i dispositivi associati e riavviare GamePP",
    "RestartGamePP": "Scollega il controller, attendi la risposta del dispositivo, quindi riavvia GamePP",
    "HWINFOcannotrun": "Hwinfo non può essere eseguito correttamente",
    "downloadHWINFO": "Scarica Hwinfo",
    "openHWINFO": "Dopo aver aperto Hwinfo, è possibile fare clic su RUN per aprirlo normalmente?",
    "hardwareDriverProblem": "Problemi del driver hardware",
    "checkHardwareManager": "Verifica il gestore hardware per assicurarti che i driver della scheda madre e della scheda video siano installati correttamente",
    "systemProblem": "Problemi di sistema, ad esempio: L'utilizzo di tool di attivazione come Baofeng o Xiaoma può causare il mancato caricamento dei driver e l'impossibilità di installare automaticamente le patch di sistema Windows 7",
    "reinstallSystem": "Reinstallare il sistema per attivarlo, Scaricare e installare WIN7: Aggiornamento *********",
    "Windows7": "Windows 7: Installa patch SHA-256, Windows 10: Attiva utilizzando certificato digitale, Windows 11 Versione preliminare: Disattiva integrità memoria",
    "ViolenceActivator": "Se hai utilizzato strumenti di attivazione non autorizzati come Xiaoma, effettua una riparazione o reinstalla il sistema",
    "MultipleGraphicsCardDrivers": "Sono installati driver di schede grafiche di marche diverse sul computer, ad esempio i driver AMD e Nvidia installati contemporaneamente",
    "UninstallUnused": "Riavvia il computer dopo aver disinstallato i driver grafici non necessari",
    "OfficialQgroup": "Nessuna delle ragioni sopra indicate è applicabile. Unisciti al nostro gruppo QQ ufficiale: 908287288 (Gruppo 5) per risolvere il problema.",
    "ExportHardwareData": "Esporta dati hardware",
    "D3D": "Utilizzo di D3D",
    "Total": "Utilizzo Totale",
    "VRAM": "Utilizzo di VRAM",
    "VRAMFrequency": "Frequenza VRAM",
    "SensorData": "Dati del sensore",
    "CannotGetSensorData": "Impossibile recuperare i dati del sensore",
    "LoadingHardwareInfo": "Caricamento informazioni hardware...",
    "ScanTime": "Ultima scansione:",
    "Rescan": "Riscansiona",
    "Screenshot": "Screenshot",
    "configCopyed": "Informazioni di configurazione copiate negli Appunti.",
    "LegalRisks": "Rischi legali potenziali rilevati",
    "brandLegalRisks": "L'esposizione del marchio comporta rischi legali potenziali",
    "professionalVersion": "Edizione Professionale",
    "professionalWorkstationVersion": "Edizione Workstation Professionale",
    "familyEdition": "Edizione Home",
    "educationEdition": "Edizione Educativa",
    "enterpriseEdition": "Edizione Enterprise",
    "flagshipEdition": "Edizione Premium",
    "familyPremiumEdition": "Edizione Familiare Premium",
    "familyStandardEdition": "Edizione Standard Famiglia",
    "primaryVersion": "Versione base",
    "bit": "bit",
    "tempWall": "Muro di temperatura",
    "error": "Errore",
    "screenshotSuccess": "Screenshot salvato correttamente",
    "atLeastOneData": "Deve essere conservato almeno un record dati",
    "atMostSixData": "Aggiungi fino a 6 voci di dati",
    "screenNotActivated": "Non attivato"
  },
  "psc": {
    "processCoreAssign": "Assegnazione dei core ai processi",
    "CoreAssign": "Allocazione core:",
    "groupName": "Nome del gruppo:",
    "notGameProcess": "Processi non gioco",
    "unNamedProcess": "Gruppo non nominato",
    "Group2": "Gruppo",
    "selectTheCore": "Seleziona il nucleo",
    "controls": "Operazione",
    "tips": "Prompt",
    "search": "Cerca",
    "shiftOut": "Espelli",
    "ppValue": "Valore PP",
    "ppDesc": "Il valore PP rappresenta il consumo storico delle risorse hardware. Valori più elevati indicano un utilizzo maggiore delle risorse hardware.",
    "littletips": "Suggerimento: Tieni premuto il processo per trascinarlo nel gruppo sinistro",
    "warning1": "Selezionare thread del core tra gruppi diversi può influenzare le prestazioni. Si consiglia di utilizzare core dello stesso gruppo.",
    "warning2": "Sei sicuro di voler lasciare questo nome del gruppo vuoto?",
    "warning3": "L'effetto di allocazione del core verrà invalidato dopo l'eliminazione. Vuoi eliminare il gruppo?",
    "allprocess": "Tutti i processi",
    "pleaseCheckProcess": "Seleziona il processo",
    "dataSaveDesktop": "I dati sono stati salvati sul desktop.",
    "createAGroup": "Crea gruppo",
    "delGroup": "Elimina gruppo",
    "Group": "Gruppo",
    "editGroup": "Modifica gruppo",
    "groupinfo": "Informazioni Gruppo",
    "moveOutGrouping": "Rimuovi dal gruppo",
    "createANewGroup": "Crea nuovo gruppo",
    "unallocatedCore": "Core non assegnato",
    "inactiveProcess": "Processo inattivo",
    "importGroupingScheme": "Importa profilo di raggruppamento",
    "derivedPacketScheme": "Esporta configurazione gruppo",
    "addNowProcess": "Aggiungi processo attualmente in esecuzione",
    "displaySystemProcess": "Mostra processi di sistema",
    "max64": "Selezione massima di 64 thread",
    "processName": "Nome processo",
    "chooseCurProcess": "Seleziona il processo attuale",
    "selectNoProcess": "Nessun processo selezionato",
    "coreCount": "Core",
    "threadCount": "Threads",
    "process": "Processo",
    "plzInputProcessName": "Inserisci il nome del processo per aggiungerlo manualmente",
    "has_allocation": "Processi con schemi di allocazione dei thread",
    "not_made": "Non hai assegnato core a nessun processo",
    "startUse": "Attiva Ottimizzazione",
    "stopUse": "Disattiva ottimizzazione",
    "threadAllocation": "Assegnazione Thread",
    "configProcess": "Configurazione processo",
    "selectThread": "Seleziona thread",
    "hyperthreadingState": "Stato Hyper-Threading",
    "open": "Attivato",
    "notYetUnlocked": "Disattivato",
    "nonhyperthreading": "Senza Hyper-Threading",
    "intervalSelection": "Selezione intervallo",
    "invertSelection": "Inverti selezione",
    "description": "Blocca i processi del gioco su core CPU specificati e isola intelligentemente i programmi in background. Aumenta efficacemente il limite massimo di FPS e stabilizza i frame rate del gioco! Riduce i rallentamenti improvvisi e le cadute brusche del frame rate, liberando pienamente le potenzialità dei processori multi-core per garantire un'esperienza di gioco sempre fluida e ad alto frame rate.",
    "importSuccess": "Importazione riuscita",
    "importFailed": "Importazione non riuscita"
  },
  "InGameMonitor": {
    "onoffingame": "Attiva/Disattiva Supervisione nel Gioco:",
    "InGameMonitor": "Monitoraggio in-game",
    "CustomMode": "Modalità Personalizzata",
    "Developing": "In sviluppo...",
    "NewMonitor": "Aggiungi elemento di monitoraggio",
    "Data": "Parametri",
    "Des": "Nota",
    "Function": "Funzionalità",
    "Editor": "Modifica",
    "Top": "Fissa in alto",
    "Delete": "Elimina",
    "Use": "Utilizzare",
    "DragToSet": "Dopo aver richiamato il pannello, è possibile trascinare per configurare",
    "MonitorItem": "Elemento di monitoraggio",
    "addMonitorItem": "Aggiungi elemento di monitoraggio",
    "hide": "Nascondi",
    "show": "Visualizza",
    "generalstyle": "Impostazioni generali",
    "restoredefault": "Ripristina impostazioni predefinite",
    "arrangement": "Disposizione",
    "horizontal": "Orizzontale",
    "vertical": "Verticale",
    "monitorposition": "Posizione di Monitoraggio",
    "canquickselectposition": "è possibile selezionare rapidamente la posizione nel grafico a sinistra",
    "curposition": "Posizione Attuale:",
    "background": "Sfondo",
    "backgroundcolor": "Colore di Sfondo:",
    "font": "Font",
    "fontStyle": "Stile del carattere",
    "fontsize": "Dimensione del carattere:",
    "fontcolor": "Colore del Carattere:",
    "style": "Stile:",
    "style2": "Stile",
    "performance": "Prestazioni",
    "refreshTime": "Tempo di aggiornamento:",
    "goGeneralSetting": "Vai alle Impostazioni generali",
    "selectMonitorItem": "Seleziona elemento di monitoraggio",
    "selectedSensor": "Sensore selezionato:",
    "showTitle": "Visualizza titolo",
    "hideTitle": "Nascondi titolo",
    "showStyle": "Modalità di visualizzazione:",
    "remarkSize": "Dimensione nota:",
    "remarkColor": "Colore della Nota:",
    "parameterSize": "Dimensione del parametro:",
    "parameterColor": "Colore Parametri:",
    "lineChart": "Grafico a Linee",
    "lineColor": "Colore della linea spezzata:",
    "lineThickness": "Spessore Linea:",
    "areaHeight": "Altezza della Regione:",
    "sort": "Ordina",
    "displacement": "Spostamento:",
    "up": "Sposta in alto",
    "down": "Sposta verso il basso",
    "bold": "Grassetto",
    "stroke": "Contorno",
    "text": "Testo",
    "textLine": "Testo + Grafico a Linee",
    "custom": "Personalizzato",
    "upperLeft": "In alto a sinistra",
    "upper": "Medio Superiore",
    "upperRight": "Alto a destra",
    "Left": "Centro sinistro",
    "middle": "Centro",
    "Right": "Centro Destro",
    "lowerLeft": "In basso a sinistra",
    "lower": "Medio Basso",
    "lowerRight": "In basso a destra",
    "notSupport": "Il dispositivo periferico non supporta la visualizzazione o l'occultamento con un clic",
    "notSupportRate": "Il tasso di rendimento non supporta l'attivazione/disattivazione con un clic",
    "notFindSensor": "Sensore non trovato. Fare clic per modificare.",
    "monitoring": "Monitoraggio",
    "condition": "Condizione",
    "bigger": "Maggiore di",
    "smaller": "Minore di",
    "biggerThan": "Supera la soglia",
    "biggerThanthreshold": "Maggiore della percentuale di soglia",
    "smallerThan": "Minore della soglia",
    "smallerThanthreshold": "Meno della percentuale soglia",
    "biggerPercent": "Percentuale di diminuzione del valore attuale",
    "smallerPercent": "Percentuale di aumento del valore attuale",
    "replay": "Funzione di Replay Istantaneo",
    "screenshot": "Cattura Schermo",
    "text1": "Valore del sensore quando",
    "text2": ", e in",
    "text3": "Tempo di attesa",
    "text4": "Se non viene visualizzato un valore più alto entro i secondi specificati, attiva immediatamente",
    "text5": "Dopo ogni attivazione del replay, aggiorna la soglia al valore registrato al momento del trigger per ridurre le attivazioni frequenti",
    "text6": "Visualizza la soglia attuale per attivare il replay",
    "text7": "Visualizza valori sensore",
    "text8": "Superare la soglia iniziale",
    "text9": "Al di sotto della soglia iniziale",
    "text10": "Conteggio soglia iniziale",
    "initThreshold": "Soglia iniziale",
    "curThreshold": "Soglia attuale:",
    "curThreshold2": "Soglia attuale",
    "resetCurThreshold": "Reimposta soglia attuale",
    "action": "Attivare la Funzione",
    "times": "volte",
    "percentage": "Percentuale",
    "uninstallobs": "Modulo di registrazione non scaricato",
    "install": "Scarica",
    "performanceAndAudioMode": "Modalità di compatibilità prestazioni e audio",
    "isSaving": "Salvataggio in corso",
    "video_replay": "Riproduzione istantanea",
    "saved": "Salvato",
    "loadQualitysScheme": "Carica impostazioni grafiche",
    "notSet": "Non configurato",
    "mirrorEnable": "Il filtro è attivo",
    "canBeTurnedOff": "Torna",
    "mirrorClosed": "Filtro del gioco disattivato",
    "closed": "Chiuso",
    "openMirror": "Attiva filtro",
    "wonderfulScenes": "Highlights",
    "VulkanModeHaveProblem": "Il modo Vulkan presenta problemi di compatibilità",
    "suggestDxMode": "Si consiglia di passare alla modalità Dx",
    "functionNotSupported": "Funzione non supportata",
    "NotSupported": "Non supportato",
    "gppManualRecording": "GamePP, registrazione manuale",
    "perfRecordsHaveBeenSaved": "Dati di prestazione salvati",
    "redoClickF8": "Premere nuovamente F8 per continuare la registrazione.",
    "startIngameMonitor": "Attivazione della funzione di monitoraggio all'interno del gioco",
    "inGameMarkSuccess": "Etichettatura nel gioco riuscita",
    "recordingFailed": "Registrazione fallita",
    "recordingHasNotDownload": "La funzione di registrazione non è stata scaricata",
    "hotkeyDetected": "Rilevato conflitto tra funzioni di scelta rapida",
    "plzEditIt": "Modificare all'interno del software e quindi utilizzarlo",
    "onePercentLowFrame": "1% Basso frame",
    "pointOnePercentLowFrame": "0.1% Basse immagini",
    "frameGenerationTime": "Tempo di generazione del frame",
    "curTime": "Ora corrente",
    "runTime": "Durata dell'esecuzione",
    "cpuTemp": "Temperatura CPU",
    "cpuUsage": "Utilizzo CPU",
    "cpuFreq": "Frequenza CPU",
    "cpuPower": "Potenza termica della CPU",
    "gpuTemp": "Temperatura GPU",
    "gpuUsage": "Utilizzo della GPU",
    "gpuPower": "Potenza termica GPU",
    "gpuFreq": "Frequenza GPU",
    "memUsage": "Utilizzo della memoria"
  },
  "LoginArea": {
    "login": "Accedi",
    "loginOut": "Disconnettersi",
    "vipExpire": "Scaduto",
    "remaining": "Rimanente",
    "day": "Cielo",
    "openVip": "Attiva l'abbonamento",
    "vipPrivileges": "Privilegi del membro",
    "rechargeRenewal": "Ricarica e Rinnovo",
    "Exclusivefilter": "Filtro",
    "configCloudSync": "Configurazione della sincronizzazione cloud",
    "comingSoon": "Disponibile a breve"
  },
  "GameMirror": {
    "filterStatus": "Stato del filtro",
    "filterPlan": "Preset Filtro",
    "filterShortcut": "Scorciatoie del filtro",
    "openCloseFilter": "Attiva/Disattiva Filtro:",
    "effectDemo": "Dimostrazione di Effetti",
    "demoConfig": "Configurazione Demo",
    "AiFilter": "Gli effetti del filtro AI sono soggetti agli effetti nel gioco",
    "AiFilterFAQ": "Problemi comuni dei filtri AI",
    "gamePPAiFilter": "Filtro AI di GamePP",
    "gamePPAiFilterVip": "Filtro AI riservato ai VIP di GamePP, regola dinamicamente i parametri del filtro in base agli scenari di gioco per ottimizzare gli effetti visivi e migliorare l'esperienza di gioco.",
    "AiMingliangTips": "Luminosità AI: Consigliata per uso quando lo schermo del gioco è troppo scuro.",
    "AiBrightTips": "AI Vivido: Consigliato da utilizzare quando il display del gioco appare troppo scuro.",
    "AiDarkTips": "AI Dimming: Consigliato per l'uso quando i grafici del gioco sono eccessivamente brillanti.",
    "AiBalanceTips": "Equilibrio IA: adatto alla maggior parte degli scenari di gioco",
    "AiTips": "Suggerimenti: Il filtro AI deve essere utilizzato premendo il tasto di scelta rapida nel gioco",
    "AiFilterUse": "Utilizzare in-game",
    "AiFilterAdjust": "Regolazione del filtro AI tramite tasto di scelta rapida",
    "Bright": "Vibrante",
    "Soft": "Morbido",
    "Highlight": "Evidenzia",
    "Film": "Film",
    "Benq": "BenQ",
    "AntiGlare": "Antiriflesso",
    "HighSaturation": "Alta saturazione",
    "Brightness": "Vivido",
    "Day": "Giorno",
    "Night": "Notte",
    "Nature": "Naturale",
    "smooth": "Dettagliato",
    "elegant": "Sobria",
    "warm": "Toni Caldi",
    "clear": "Chiaro",
    "sharp": "Nitidezza",
    "vivid": "Dinamico",
    "beauty": "Vivid",
    "highDefinition": "Alta Definizione",
    "AiMingliang": "AI Brillante",
    "AiBright": "AI Vivido",
    "AiDark": "AI Scurito",
    "AiBalance": "Bilanciamento AI",
    "BrightTips": "Il filtro vivido è adatto ai giochi casual, d'azione o d'avventura, migliorando la saturazione dei colori per rendere i grafici più dinamici e coinvolgenti.",
    "liangTips": "Si consiglia di utilizzare i filtri quando画面 del gioco è troppo scura.",
    "anTips": "È consigliato utilizzare il filtro quando lo schermo di gioco è troppo scuro.",
    "jianyiTips": "Si consiglia di utilizzare il filtro quando i grafici del gioco sono troppo vivaci.",
    "shiTips": "Il filtro è adatto alla maggior parte delle scene di gioco.",
    "shi2Tips": "Il filtro è adatto ai giochi casual, d'azione o d'avventura, migliora la saturazione dei colori per rendere i grafici più vividi e coinvolgenti.",
    "ruiTips": "Colori raffinati del filtro, effetti luminosi moderati, ideali per rappresentare scene oniriche, accoglienti o nostalgiche",
    "qingTips": "Toni luminosi, alto contrasto, dettagli nitidi, ideale per visualizzare scene vivaci e ben illuminate.",
    "xianTips": "Impostazioni di contrasto e luminosità superiori assicurano dettagli nitidi nelle scene scure senza distorsioni e una visione confortevole nelle scene luminose.",
    "dianTips": "Aumenta moderatamente luminosità e colori dello schermo per ottenere una qualità visiva cinematografica",
    "benTips": "Riduce l'effetto della luce bianca, rendendo le scene di gioco completamente bianche meno abbaglianti",
    "fangTips": "Ottimizzato per giochi open world e di avventura, migliora luminosità e contrasto per immagini più nitide",
    "jiaoTips": "Adatto per giochi di ruolo e simulazione, toni equilibrati, realismo visivo migliorato",
    "jieTips": "Ottimizzato per giochi con trame ricche e emozioni dettagliate, miglioramento dei dettagli e della morbidezza per visivi più raffinati",
    "jingTips": "Ottimizzato per giochi d'azione e competitivi, migliora la chiarezza e il contrasto per immagini più nitide",
    "xiuTips": "Ottimizzato per giochi di guarigione e casual, accentua i toni caldi e la morbidezza, creando un'atmosfera più accogliente",
    "qihuanTips": "Adatto alle scene ricche di elementi fantastici e colori vivaci, aumenta la saturazione cromatica per creare un impatto visivo significativo",
    "shengTips": "Potenzia colori e dettagli per evidenziare vivacità e realismo della scena,",
    "sheTips": "Adatto a giochi FPS, di logica o avventura, migliora dettagli e contrasto per aumentare il realismo del mondo di gioco.",
    "she2Tips": "Adatto ai giochi sparatutto, corse o combattimento, evidenzia dettagli in alta definizione e prestazioni dinamiche per migliorare l'intensità e l'impatto visivo dell'esperienza di gioco",
    "an2Tips": "Migliora la chiarezza delle scene in ambienti bui, ideale per scene oscure o notturne.",
    "wenTips": "Adatto ai giochi artistici, di avventura o rilassanti, crea toni di colori morbidi ed effetti di luce e ombra per aggiungere eleganza e calore alla scena。",
    "jing2Tips": "Adatto ai giochi competitivi, ai giochi con ritmo musicale o ai giochi ambientati in scenari urbani notturni, con colori vivaci ed effetti di illuminazione evidenziati,",
    "jing3Tips": "Ottimizzato per giochi competitivi, d'azione o fantasy, potenzia il contrasto cromatico rendendo i grafici più ricchi e dinamici.",
    "onlyVipCanUse": "Questo filtro è disponibile solo per gli utenti VIP"
  },
  "GameRebound": {
    "noGame": "Nessun registro di gioco",
    "noGameRecord": "Nessun record di gioco disponibile! Inizia una partita ora!",
    "gameDuration": "Durata del gioco oggi:",
    "gameElectricity": "Consumo Energetico Giornaliero",
    "degree": "Grado",
    "gameCo2": "Emissioni di CO₂ di oggi:",
    "gram": "Chiave",
    "manualRecord": "Registrazione manuale",
    "recordDuration": "Durata della registrazione:",
    "details": "Dettagli",
    "average": "Media",
    "minimum": "Valore minimo",
    "maximum": "Valore Massimo",
    "occupancyRate": "Utilizzo",
    "voltage": "Tensione",
    "powerConsumption": "Consumo Energetico",
    "start": "Avvia:",
    "end": "Termina",
    "Gametime": "Durata del gioco:",
    "Compactdata": "Ottimizzazione Dati",
    "FullData": "Dati Completati",
    "PerformanceAnalysis": "Analisi delle prestazioni",
    "PerformanceAnalysis2": "Rapporto evento",
    "HardwareStatus": "Stato dell'hardware",
    "totalPower": "Consumo energetico totale",
    "TotalEmissions": "Emissione Totale",
    "PSS": "Nota: I dati del grafico sottostante rappresentano la media",
    "FrameGenerationTime": "Tempo di generazione dei frame",
    "GameResolution": "Risoluzione del gioco",
    "FrameGenerationTimeTips": "Questo punto dati è eccezionalmente alto e non è stato incluso nelle statistiche",
    "FrameGenerationTimeTips2": "Questo punto dati è eccezionalmente basso e quindi non incluso nelle statistiche",
    "noData": "Nessuno",
    "ProcessorOccupancy": "Utilizzo della CPU",
    "ProcessorFrequency": "Frequenza del processore",
    "ProcessorTemperature": "Temperatura del processore",
    "ProcessorHeatPower": "Thermal Design Power del processore",
    "GraphicsCardOccupancy": "Utilizzo di D3D sulla scheda grafica",
    "GraphicsCardOccupancyTotal": "Utilizzo totale della scheda grafica",
    "GraphicsCardFrequency": "Frequenza GPU",
    "GraphicsCardTemperature": "Temperatura della GPU",
    "GraphicsCardCoreTemperature": "Temperatura del Punto Caldo del Core della Scheda Grafica",
    "GraphicsCardHeatPower": "Potenza termica della GPU",
    "GraphicsCardMemoryTemperature": "Temperatura Memoria GPU",
    "MemoryOccupancy": "Utilizzo della memoria",
    "MemoryTemperature": "Temperatura della memoria",
    "MemoryPageFaults": "Interruzione di paging della memoria",
    "Duration": "Durata",
    "Time": "Tempo",
    "StartStatistics": "Inizia statistiche",
    "Mark": "Tag",
    "EndStatistics": "Terminare statistiche",
    "LineChart": "Grafico a Linee",
    "AddPointInGame_m1": "Premi all'interno del gioco",
    "AddPointInGame_m2": "Punto di marcatura aggiungibile",
    "LeftMouse": "Clic sinistro per alternare mostra/nascondi, clic destro per cambiare colore",
    "DeleteThisLine": "Elimina questa polilinea",
    "AddCurve": "Aggiungi curva",
    "AllCurvesAreHidden": "Tutti i grafici delle curve sono nascosti",
    "ThereAreSamplingData": "Dati di campionamento totali:",
    "Items": "Voce",
    "StatisticsData": "Statistiche",
    "electricity": "Consumo elettrico",
    "carbonEmission": "Emissioni di Carbonio",
    "carbonEmissionTips": "Emissioni di Anidride Carbonica (kg) = Consumo Elettrico (kWh) × 0.785",
    "D3D": "Utilizzo di D3D:",
    "TOTAL": "Utilizzo totale:",
    "Process": "Processo:",
    "L3Cache": "Cache L3:",
    "OriginalFrequency": "Frequenza originale:",
    "MaximumBoostFrequency": "Turbo Boost Massimo:",
    "DriverVersion": "Versione del driver:",
    "GraphicsCardMemoryBrand": "Marca della memoria video:",
    "Bitwidth": "Larghezza del bus",
    "System": "Sistema:",
    "Screen": "Schermo",
    "Interface": "Interfaccia:",
    "Channel": "Canale:",
    "Timing": "Sequenza:",
    "Capacity": "Capacità:",
    "Generation": "Algebra",
    "AddPoint_m1": "Premi in gioco",
    "AddPoint_m2": "Aggiungi punto di marcatura",
    "Hidden": "Nascosto",
    "Totalsampling": "Dati Totali del Campionamento:",
    "edition": "Versione del driver:",
    "MainHardDisk": "Disco Rigido Principale",
    "SetAsStartTime": "Imposta come ora di inizio",
    "SetAsEndTime": "Imposta come ora di fine",
    "WindowWillBe": "La finestra delle statistiche delle prestazioni si troverà in",
    "After": "Chiudere dopo",
    "NoLongerPopUpThisGame": "Questo gioco non verrà più visualizzato",
    "HideTemperatureReason": "Nascondi motivo temperatura",
    "HideTemperatureReason2": "Nascondi il rapporto degli eventi",
    "HideOtherReason": "Nascondi Altri Motivi",
    "CPUanalysis": "Analisi delle prestazioni della CPU",
    "TemperatureCause": "Causa temperatura",
    "tempSensorEvent": "Evento Sensore di Temperatura",
    "NoTemperatureLimitation": "Nessun rallentamento termico delle prestazioni CPU rilevato. Il tuo sistema di raffreddamento è perfettamente in grado di soddisfare i requisiti di questo gioco.",
    "NoTemperatureLimitation2": "Nessun evento del sensore di temperatura, il tuo sistema di raffreddamento può gestire perfettamente i requisiti del gioco.",
    "performanceis": "Nel selezionato",
    "Inside": "Interno,",
    "TheStatisticsTimeOf": "Il periodo statistico soddisfa le condizioni di attivazione previste. La ragione con frequenza di attivazione più alta è",
    "limited": "Percentuale del tempo totale dovuta a limitazioni di prestazioni causate dalla temperatura",
    "SpecificReasons": "Cause specifiche e loro proporzione tra le cause legate alla temperatura:",
    "OptimizationSuggestion": "Suggerimenti di ottimizzazione:",
    "CPUtemperature": "La temperatura della CPU è troppo alta. Si consiglia di verificare/migliorare l'ambiente di raffreddamento della CPU.",
    "CPUoverheat": "La CPU si surriscalda a causa dell'alimentazione della scheda madre. Verifica le impostazioni correlate alla scheda madre o migliora l'ambiente di raffreddamento.",
    "OtherReasons": "Altri motivi",
    "NoPowerSupplyLimitation": "La prestazione della CPU non è limitata da alimentazione/consumo energetico. Le impostazioni di consumo energetico del BIOS possono gestire perfettamente i requisiti di questo gioco.",
    "PowerSupplyLimitation": "Dovuto a limitazioni di alimentazione/consumo energetico",
    "SpecificReasonsInOtherReasons": "Causa specifica e la sua proporzione rispetto ad altre cause:",
    "PleaseCheckTheMainboard": "Verifica lo stato di alimentazione della scheda madre o regola le impostazioni di consumo del BIOS per risolvere le limitazioni di prestazioni della CPU causate da altri fattori",
    "CPUcoretemperature": "La temperatura del core ha raggiunto Tj,Max ed è stata limitata",
    "CPUCriticalTemperature": "Temperatura critica della CPU",
    "CPUCircuitTemperature": "Pacchetto CPU/Bus Anulare limitato dal raggiungimento di Tj,Max",
    "CPUCircuitCriticalTemperature": "CPU Package/Ring Bus ha raggiunto la temperatura critica",
    "CPUtemperatureoverheating": "Rilevato surriscaldamento della CPU, che attiverà una riduzione automatica della frequenza per abbassare la temperatura e prevenire guasti hardware",
    "CPUoverheatingtriggered": "In caso di surriscaldamento che attiva il meccanismo di raffreddamento, la CPU aggiusterà tensione e frequenza per ridurre il consumo energetico e la temperatura",
    "CPUPowerSupplyOverheating": "CPU limitata a causa di un surriscaldamento grave dell'alimentazione della scheda madre",
    "CPUPowerSupplyLimitation": "CPU limitato a causa del surriscaldamento dell'alimentazione della scheda madre",
    "CPUMaximumPowerLimitation": "Il core è soggetto al limite massimo di consumo energetico",
    "CPUCircuitPowerLimitation": "Il package CPU/bus ad anello ha raggiunto il limite di potenza",
    "CPUElectricalDesignLimitation": "Attiva restrizioni di progettazione elettrica (inclusi limite di corrente ICCmax, limite di potenza di picco PL4, limitazione di tensione SVID, ecc.)",
    "CPULongTermPowerLimitation": "Consumo energetico prolungato della CPU raggiunto il limite",
    "CPULongTermPowerinstantaneous": "Il consumo di energia istantaneo della CPU ha raggiunto il limite",
    "CPUPowerLimitation": "Meccanismo di degradazione della frequenza Turbo CPU, generalmente limitato da BIOS o software specifico",
    "CPUPowerWallLimitation": "Limite di potenza della CPU",
    "CPUcurrentwalllimit": "Limite di Corrente CPU",
    "AiAgent": "Agente GamePP (AI Agent)",
    "AgentDesc": "Torna alla pagina principale",
    "fnBeta": "Questa funzione si trova attualmente nella fase di test su invito. Il tuo account GamePP non ha ancora ricevuto l'accesso al test.",
    "getAIReport": "Ottieni rapporto di IA",
    "waitingAi": "Sto attendendo il completamento della generazione del rapporto",
    "no15mins": "Durata del gioco inferiore a 15 minuti, impossibile ottenere un rapporto AI valido",
    "timeout": "Richiesta al server scaduta",
    "agentId": "ID Agente:",
    "reDo": "Rigenera rapporto",
    "text2": "Agente GamePP: Report di analisi AI online, il contenuto seguente è stato generato dall'AI e serve solo come riferimento.",
    "amdAiagentTitle": "Agente GamePP: Rapporto di analisi AMD Ryzen AI, il contenuto seguente è generato dall'IA e serve solo a scopi di riferimento.",
    "noCurData": "Nessun dato disponibile al momento",
    "dataScreening": "Filtraggio dati",
    "dataScreeningDescription": "Questa funzione è progettata per escludere le statistiche dei dati durante i periodi di gioco non efficaci come il caricamento della mappa o l'attesa in lobby. 0 indica che non verrà effettuata alcuna esclusione.",
    "excessivelyHighParameter": "Parametri eccessivi",
    "tooLowParameter": "Parametri troppo bassi",
    "theMaximumValueIs": "Valore massimo è",
    "theMinimumValueIs": "Valore minimo è",
    "exclude": "Escludi",
    "dataStatisticsAtThatTime": "Statistiche dei dati a questo momento",
    "itHasBeenGenerated": "Generazione completata,",
    "clickToView": "Fai clic per visualizzare",
    "onlineAnalysis": "Analisi online",
    "amdRyzenAILocalAnalysis": "AMD Ryzen AI Analisi locale",
    "useTerms": "Condizioni di utilizzo:",
    "term1": "1. Processore Ryzen AI Max o Processore Ryzen Al 300 Series",
    "term2": "2.Versione del driver AMD NPU",
    "term3": "3. Attivare la grafica integrata",
    "conformsTo": "Conforme",
    "notInLineWith": "Non conforme",
    "theVersionIsTooLow": "Versione obsoleta",
    "canNotUseAmdNpu": "La sua configurazione non soddisfa i requisiti. L'analisi dell'entità AMD NPU non può essere utilizzata。",
    "unusable": "Non disponibile",
    "downloadTheFile": "Scarica file",
    "downloadSource": "Fonte del download:",
    "fileSize": "Dimensione file: ca. 8,34 GB",
    "cancelDownload": "Annulla download",
    "filePath": "Posizione del file",
    "generateAReport": "Genera rapporto",
    "fileMissing": "File mancante. È necessaria una reinstallazione.",
    "downloading": "Download in corso...",
    "theModelConfigurationLoadingFailed": "Impossibile caricare la configurazione del modello",
    "theModelDirectoryDoesNotExist": "La directory del modello non esiste",
    "thereIsAMistakeInReasoning": "Errore di deduzione",
    "theInputExceedsTheModelLimit": "L'input supera il limite del modello",
    "selectModelNotSupport": "Il modello di download selezionato non è supportato",
    "delDirFail": "Impossibile eliminare la directory del modello esistente",
    "failedCreateModelDir": "Creazione della directory del modello non riuscita",
    "modelNotBeenFullyDownload": "Non tutti i modelli sono stati scaricati",
    "agentIsThinking": "L'agente Jiajia sta riflettendo",
    "reasoningModelFile": "File del modello per l'analisi",
    "modelReasoningTool": "Strumento di inferenza del modello"
  },
  "SelectSensor": {
    "DefaultSensor": "Sensore predefinito",
    "Change": "Modifica",
    "FanSpeed": "Velocità del ventilatore",
    "MainGraphicsCard": "Scheda grafica principale",
    "SetAsMainGraphicsCard": "Imposta come GPU principale",
    "GPUTemperature": "Temperatura GPU",
    "GPUHeatPower": "Consumo termico della GPU",
    "GPUTemperatureD3D": "Utilizzo D3D della GPU",
    "GPUTemperatureTOTAL": "Utilizzo totale della GPU",
    "GPUTemperatureCore": "Temperatura del Core GPU Hotspot",
    "MotherboardTemperature": "Temperatura Scheda Madre",
    "MyAttention": "I Miei Preferiti",
    "All": "Tutto",
    "Unit": "Unità:",
    "NoAttention": "Sensori non seguiti",
    "AttentionSensor": "Sensori monitorati (Beta)",
    "GoToAttention": "Vai al Focus",
    "CancelAttention": "Smettere di seguire",
    "noThisSensor": "Nessun sensore",
    "deviceAbout": "Correlato alle periferiche",
    "deviceBattery": "Livello batteria dispositivo periferico",
    "testFunction": "Funzione di test",
    "mouseEventRate": "Frequenza di polling",
    "relatedWithinTheGame": "Correlato al gioco",
    "winAbout": "Sistema",
    "trackDevicesBattery": "Traccia il livello batteria dei dispositivi periferici",
    "ingameRealtimeMouseRate": "Frequenza di polling del mouse attualmente utilizzata in tempo reale durante il gioco",
    "notfoundDevice": "Nessun dispositivo supportato trovato",
    "deviceBatteryNeedMythcool": "Elenco dei dispositivi con supporto visualizzazione batteria: (richiede Myth.Cool)",
    "vkm1mouse": "Mouse Valkyrie M1",
    "vkm2mouse": "Valkyrie M2 Mouse",
    "vk99keyboard": "Valkyrie 99 Tastiera ad Asse Magnetico",
    "logitechProWireless": "Logitech Pro Wireless",
    "logitechProXSUPERLIGHT": "Logitech PRO X SUPERLIGHT",
    "logitechProXSUPERLIGHT2": "Logitech PRO",
    "logitechPro2LIGHTSPEED": "Logitech PRO 2 LIGHTSPEED",
    "steelSeriesArctisGameBuds": "SteelSeries Arctis GameBuds",
    "steelSeriesArctis7PPlus": "SteelSeries Arctis 7P Plus",
    "razerV3": "Razer Valkyrie V3 Edizione Professionale",
    "razerV2": "Razer Viper V2 Edizione Professionale",
    "wireless": "Senza fili",
    "logitechNeedGhub": "Quando Logitech non riesce a recuperare il modello del dispositivo, è necessario scaricare GHUB",
    "chargingInProgress": "Caricamento",
    "inHibernation": "In modalità sospesa"
  },
  "video": {
    "videoRecord": "Registrazione Video",
    "recordVideo": "Video registrato",
    "scheme": "Profilo",
    "suggestScheme": "Piano consigliato",
    "text1": "Nella configurazione, la dimensione del video di 1 minuto è circa",
    "text2": "Questa funzione utilizza risorse di sistema aggiuntive",
    "low": "Basso",
    "mid": "Home",
    "high": "Alto",
    "1080p": "Nativo",
    "RecordingFPS": "Registra FPS",
    "bitRate": "Bitrate video",
    "videoResolution": "Risoluzione video",
    "startStopRecord": "Avvia/Ferma Registrazione",
    "instantReplay": "Riproduzione Istantanea",
    "instantReplayTime": "Durata riproduzione istantanea",
    "showIngame": "Avvia pannello di controllo durante il gioco",
    "CaptureMode": "Metodo di cattura",
    "gameWindow": "Finestra di Gioco",
    "desktopWindow": "Finestra del desktop",
    "fileSavePath": "Percorso di archiviazione file",
    "selectVideoSavePath": "Seleziona Percorso di Salvataggio Registrazione",
    "diskFreeSpace": "Spazio libero su disco rigido:",
    "edit": "Modifica",
    "open": "Apri",
    "displayMouse": "Mostra cursore del mouse",
    "recordMicrophone": "Registra microfono",
    "gameGraphics": "Visualizzazione Originale del Gioco"
  },
  "Setting": {
    "common": "Generale",
    "personal": "Personalizzazione",
    "messageNotification": "Notifiche",
    "sensorReading": "Lettura del Sensore",
    "OLEDscreen": "Protezione da bruciatura OLED",
    "performanceStatistics": "Statistiche delle Prestazioni",
    "shortcut": "Scorciatoia",
    "ingameSetting": "Salva impostazioni gioco",
    "other": "Altro",
    "otherSettings": "Altre Impostazioni",
    "GeneralSetting": "Impostazioni Generali",
    "softwareVersion": "Versione del software",
    "checkForUpdates": "Verifica aggiornamenti",
    "updateNow": "Aggiorna ora",
    "currentVersion": "Versione Attuale",
    "latestVersion": "Versione più recente",
    "isLatestVersion": "La versione attuale è già l'ultima.",
    "functionModuleUpdate": "Aggiornamento del Modulo Funzionale",
    "alwaysUpdateModules": "Mantenere tutti i moduli funzionali installati all'ultima versione",
    "lang": "Lingua",
    "bootstrap": "Avvio automatico",
    "powerOn_m1": "Avvia",
    "powerOn_m2": "Avvio automatico dopo secondi",
    "defaultDelay": "Predefinito: 40 secondi",
    "followSystemScale": "Segui ridimensionamento sistema",
    "privacySettings": "Impostazioni privacy",
    "JoinGamePPPlan": "Partecipa al programma di miglioramento dell'esperienza utente GamePP",
    "personalizedSetting": "Personalizzazione",
    "restoreDefault": "Ripristina impostazioni predefinite",
    "color": "Colore",
    "picture": "Immagine",
    "video": "Video",
    "browse": "Esplora",
    "clear": "Pulisci",
    "mp4VideoOrPNGImagesCanBeUploaded": "Carica video MP4 o immagini PNG",
    "transparency": "Trasparenza",
    "backgroundColor": "Colore di Sfondo",
    "textFont": "Tipo di carattere principale",
    "message": "Messaggio",
    "enableInGameNotifications": "Abilita notifiche nel gioco",
    "messagePosition": "Posizione di visualizzazione nel gioco",
    "leftTop": "Angolo in alto a sinistra",
    "leftCenter": "Centro sinistro",
    "leftBottom": "Angolo in basso a sinistra",
    "rightTop": "Angolo in alto a destra",
    "rightCenter": "Centro Destro",
    "rightBottom": "Angolo in basso a destra",
    "noticeContent": "Contenuto della notifica",
    "gameInjection": "Iniezione di Gioco",
    "ingameShow": "Visualizza in gioco",
    "inGameMonitoring": "Monitoraggio in-game",
    "gameFilter": "Filtro di gioco",
    "start": "Iniziare",
    "endMarkStatistics": "Statistiche del Marcatore di Fine",
    "readHwinfoFail": "HWINFO Lettura non riuscita delle informazioni hardware",
    "dataSaveDesktop": "I dati sono stati salvati negli appunti e nel file del desktop",
    "TheSensorCacheCleared": "Dati della cache del sensore cancellati",
    "defaultSensor": "Sensore predefinito",
    "setSensor": "Seleziona Sensore",
    "refreshTime": "Ora di aggiornamento dati",
    "recommend": "Predefinito",
    "sensorMsg": "Più breve è l'intervallo di tempo, maggiore è il consumo di risorse. Seleziona attentamente.",
    "exportData": "Esportare dati",
    "exportHwData": "Esporta dati informazioni hardware",
    "sensorError": "Lettura anomala del sensore",
    "clearCache": "Pulisci cache",
    "littleTips": "Nota: Il report sulle prestazioni verrà generato 2 minuti dopo l'avvio del gioco",
    "disableAutoShow": "Disattiva finestra popup automatica delle statistiche sulle prestazioni",
    "AutoClosePopUpWindow_m1": "La finestra delle statistiche delle prestazioni verrà chiusa automaticamente dopo il tempo specificato:",
    "AutoClosePopUpWindow_m2": "secondi",
    "abnormalShutdownReport": "Rapporto di chiusura anomala",
    "showWeaAndAddress": "Mostra condizioni meteorologiche e informazioni sulla posizione",
    "autoScreenShots": "Cattura automaticamente lo schermo del gioco al momento della marcatura",
    "keepRecent": "Numero di record recenti da conservare:",
    "noLimit": "Illimitato",
    "enableInGameSettingsSaving": "Abilita Salvataggio Impostazioni in Gioco",
    "debugMode": "Modalità Debug",
    "enableDisableDebugMode": "Attiva/disattiva modalità di debug",
    "audioCompatibilityMode": "Modalità di compatibilità audio",
    "quickClose": "Chiusura rapida",
    "closeTheGameQuickly": "Chiudi rapidamente il processo di gioco",
    "cancel": "Annulla",
    "confirm": "Conferma",
    "MoveInterval_m1": "Il monitoraggio desktop e all'interno del gioco si sposterà leggermente:",
    "MoveInterval_m2": "minuti",
    "text3": "Dopo aver chiuso il gioco, la finestra del rapporto sulle prestazioni non verrà visualizzata, saranno conservati solo i record storici",
    "text5": "La segnalazione verrà generata automaticamente dopo un arresto imprevisto. L'attivazione di questa funzione consumerà risorse di sistema aggiuntive.",
    "text6": "L'utilizzo delle funzioni di scelta rapida potrebbe entrare in conflitto con altri shortcut di gioco, impostare con cautela.",
    "text7": "Imposta il collegamento rapido a \"Nessuno\", utilizza il tasto Indietro",
    "text8": "Mantenere lo stato dei filtri, della supervisione in-game e altre funzioni durante l'esecuzione del gioco in base al nome del processo",
    "text9": "Attivato registra continuamente i log operativi; disattivato elimina i file log (Consigliato disattivare)",
    "text10": "Dopo l'attivazione, non sarà possibile accedere ai sensori della scheda madre per risolvere i problemi audio causati da GamePP",
    "text11": "Premere Alt+F4 due volte consecutive per uscire rapidamente dal gioco attuale",
    "text12": "Vuoi continuare? Questa modalità richiede il riavvio di GamePP.",
    "openMainUI": "Mostra l'applicazione",
    "setting": "Impostazioni",
    "feedback": "Feedback dei problemi",
    "help": "Aiuto",
    "sensorReadingSetting": "Impostazioni di lettura del sensore",
    "searchlanguage": "Lingua di ricerca"
  },
  "GamePlusOne": {
    "year": "Anno",
    "month": "Mese",
    "day": "Giorno",
    "success": "Successo",
    "fail": "Fallito",
    "will": "Corrente",
    "missedGame": "Giochi persi",
    "text1": "Importo, ca. ￥",
    "text2": "Totale Giochi Ottenuti",
    "text3": "Versione",
    "gamevalue": "Valore del Gioco",
    "gamevalue1": "Ottenere",
    "total": "Totale Richiesto",
    "text4": "giochi, risparmio cumulativo",
    "text6": "Prodotto, Valore",
    "Platformaccountmanagement": "Gestione account della piattaforma",
    "Missed1": "(Non reclamato)",
    "Received2": "(Ricezione riuscita)",
    "Receivedsoon2": "Disponibile ora",
    "Receivedsoon": "Disponibile ora",
    "Missed": "Raccolta Mancata",
    "Received": "Acquisizione riuscita",
    "Getaccount": "Ottieni account",
    "Worth": "Valore",
    "Auto": "Automatico",
    "Manual": "Manuale",
    "Pleasechoose": "Seleziona un gioco",
    "Receive": "Riscuoti ora",
    "Selected": "Selezionato",
    "text5": "Giochi",
    "Automatic": "Auto-riscatto in corso...",
    "Collecting": "In corso di ritiro...",
    "ReceiveTimes": "Prelievi mensili",
    "Thefirst": "n.",
    "Week": "Settimana",
    "weekstotal": "53 settimane in totale",
    "Return": "Home",
    "Solutionto": "Errore di associazione account - Soluzione",
    "accounts": "Numero di account collegati",
    "Addaccount": "Aggiungi account",
    "Clearcache": "Pulisci cache",
    "Bindtime": "Tempo di associazione",
    "Status": "Stato",
    "Normal": "Normale",
    "Invalid": "Non valido",
    "text7": "giochi, risparmio totale realizzato",
    "Yuan": "Yuan",
    "untie": "Scollegare",
    "disable": "Disattiva",
    "enable": "Attiva",
    "gamePlatform": "Piattaforma di gioco",
    "goStorePage": "Vai alla pagina del negozio",
    "receiveEnd": "Dopo la scadenza",
    "loginPlatformAccount": "Account della piattaforma effettuato",
    "waitReceive": "In attesa di riscossione",
    "receiveSuccess": "Riscossione riuscita",
    "accountInvalid": "Account scaduto",
    "alreadyOwn": "Già Posseduto",
    "networkError": "Anomalía di Rete",
    "noGame": "Nessun Gioco Base",
    "manualReceiveInterrupt": "Interruzione dell'Acquisizione Manuale",
    "receiving": "In corso di ritiro",
    "agree": "Acconsento a unirmi al programma di prenotazione gratuita GamePP.",
    "again": "Riclaimare"
  },
  "shutdownTimer": {
    "timedShutdown": "Arresto programmato",
    "currentTime": "Ora Attuale:",
    "setCountdown": "Configura il conto alla rovescia",
    "shutdownInSeconds": "Arresto in X secondi",
    "shutdownIn": "Dopo lo spegnimento",
    "goingToBe": "sarà",
    "executionPlan": "Piano di Esecuzione",
    "startTheClock": "Avvia timer",
    "stopTheClock": "Annullare il piano",
    "isShuttingDown": "Esecuzione piano di spegnimento programmato:",
    "noplan": "Nessun piano di spegnimento attuale",
    "hour": "Ora",
    "min": "Minuto",
    "sec": "Secondo",
    "ms": "Millisecondo",
    "year": "Anno",
    "month": "Mese",
    "day": "Giorno",
    "hours": "Ora"
  },
  "screenshotpage": {
    "screenshot": "Screenshot",
    "screenshotFormat": "Progettato specificamente per la cattura degli schermi dei giochi, supporta il salvataggio in formato JPG/PNG/BMP, consente di catturare rapidamente gli schermi dei giochi e garantisce un output in alta risoluzione senza perdita di qualità",
    "Turnon": "Attiva screenshot automatico, ogni",
    "seconds": "Secondo",
    "takeScreenshot": "Esegui una cattura schermo automatica",
    "screenshotSettings": "Questa impostazione non ha effetto quando selezionata nel gioco",
    "saveGameFilterAndMonitoring": "Salva gli effetti 'Filtro di gioco' e 'Monitoraggio in gioco' nella schermata",
    "disableScreenshotSound": "Disattiva la notifica sonora per le schermate",
    "imageFormat": "Formato immagine",
    "recommended": "Consigliato",
    "viewingdetails": "Mantiene i dettagli della qualità dell'immagine, dimensioni moderate, adatto per visualizzare dettagli",
    "saveSpace": "Qualità immagine comprimibile, dimensione minima, risparmio spazio",
    "ultraQuality": "Grafica ultra HD senza compressione, dimensione file molto grande. Consigliato per giocatori che richiedono la massima qualità visiva nelle partite salvate.",
    "fileSavePath": "Percorso di salvataggio del file",
    "hardDiskSpace": "Spazio libero su disco rigido:",
    "minutes": "Minuto"
  },
  "DesktopMonitoring": {
    "desktopMonitoring": "Monitoraggio del Desktop",
    "SomeSensors": "Consigliamo alcuni sensori per il monitoraggio. È possibile eliminarli o aggiungerne di nuovi",
    "AddComponent": "Aggiungi nuovo componente",
    "Type": "Tipo",
    "Remarks": "Nota",
    "AssociatedSensor": "Associa sensore",
    "Operation": "Operazione",
    "Return": "Indietro",
    "TimeSelection": "Selezione dell'ora:",
    "Format": "Formato:",
    "Rule": "Regola：",
    "Coordinate": "Coordinate:",
    "CustomTextContent": "Contenuto testo personalizzato:",
    "SystemTime": "Ora sistema",
    "China": "Cina",
    "Britain": "Regno Unito",
    "America": "Stati Uniti",
    "Russia": "Russia",
    "France": "Francia",
    "DateAndTime": "Data e ora",
    "Time": "Tempo",
    "Date": "Data",
    "Week": "Giorno della settimana",
    "DateAndTimeAndWeek": "Data+Ora+Giorno della settimana",
    "TimeAndWeek": "Ora+Giorno della settimana",
    "Hour12": "Formato 12 ore",
    "Hour24": "formato 24 ore",
    "SelectSensor": "Seleziona sensore:",
    "AssociatedSensor1": "Collega il sensore:",
    "SensorUnit": "Unità del sensore：",
    "Second": "Secondo:",
    "Corner": "Angoli arrotondati:",
    "BackgroundColor": "Colore di sfondo :",
    "ProgressColor": "Colore avanzamento:",
    "Font": "Fonte：",
    "SelectFont": "Seleziona font",
    "FontSize": "Dimensione fonte:",
    "Color": "Colore：",
    "Style": "Stile:",
    "Bold": "Grassetto",
    "Italic": "Corsivo",
    "Shadow": "Ombra",
    "ShadowPosition": "Posizione dell'ombra：",
    "ShadowEffect": "Effetti d'ombra：",
    "Blur": "Sfocatura",
    "ShadowColor": "Colore dell'ombra：",
    "SelectFromLocalFiles": "Seleziona da file locali:",
    "UploadImageVideo": "Carica immagini/video",
    "UploadSVGFile": "Carica un file SVG",
    "Width": "Larghezza:",
    "Height": "Alto: ",
    "Effect": "Effetto:",
    "Rotation": "Rotazione:",
    "WhenTheSensorValue": "Il valore del sensore è maggiore di",
    "conditions": "Quando la condizione non è soddisfatta (nessuna rotazione quando il valore del sensore è 0)",
    "Clockwise": "In senso orario",
    "Counterclockwise": "Contro orario",
    "QuickRotation": "Rotazione rapida",
    "SlowRotation": "Rotazione lenta",
    "StopRotation": "Ferma la rotazione",
    "StrokeColor": "Colore del contorno：",
    "Path": "Percorso",
    "Color1": "Colore",
    "ChangeColor": "Cambia colore",
    "When": "Quando",
    "SensorValue": "Valore del sensore maggiore o uguale a",
    "SensorValue1": "Valore del sensore minore o uguale a",
    "SensorValue2": "Il valore del sensore è uguale",
    "MonitoringSettings": "Impostazioni di monitoraggio",
    "RestoreDefault": "Ripristina predefiniti",
    "Monitor": "Monitor",
    "AreaSize": "Dimensioni della zona",
    "Background": "Sfondo",
    "ImageVideo": "Immagini/Video",
    "PureColor": "Colore solido",
    "Select": "Seleziona",
    "ImageVideoDisplayMode": "Modalità di visualizzazione immagini/video",
    "Transparency": "Trasparenza",
    "DisplayPosition": "Mostra posizione",
    "Stretch": "Stirare",
    "Fill": "Riempi",
    "Adapt": "Adattare",
    "SelectThePosition": "Fare clic sulla cella per selezionare rapidamente la posizione",
    "CurrentPosition": "Posizione corrente:",
    "DragLock": "Blocco Trascinamento",
    "LockMonitoringPosition": "Posizione del monitor bloccata (Dopo il blocco, il monitor non può essere trascinato)",
    "Unlockinterior": "Abilita lo spostamento degli elementi interni",
    "Font1": "Font",
    "GameSettings": "Impostazioni del gioco",
    "CloseDesktopMonitor": "Disattivare automaticamente il monitoraggio del desktop quando il gioco è in esecuzione.",
    "OLED": "Protezione Burn-in OLED",
    "Display": "Mostra",
    "PleaseEnterContent": "Immettere il contenuto",
    "NextStep": "Avanti",
    "Add": "Aggiungi",
    "StylesForYou": "Vi consigliamo alcuni stili di monitoraggio. Potete selezionarli e applicarli. Ne verranno aggiunti di nuovi in futuro.",
    "EditPlan": "Modifica profilo",
    "MonitoringStylePlan": "Schema di stile di monitoraggio",
    "AddDesktopMonitoring": "Aggiungi monitoraggio del desktop",
    "TextLabel": "Etichetta di testo",
    "ImageVideo1": "Immagini, Video",
    "SensorGraphics": "Grafica del sensore",
    "SensorData": "Dati del sensore",
    "CustomText": "Testo personalizzato",
    "DateTime": "Data ed ora",
    "Image": "Immagine",
    "Video": "Video",
    "SVG": "SVG",
    "ProgressBar": "Barra di avanzamento",
    "Graphics": "Grafica",
    "UploadImage": "Carica immagine",
    "UploadVideo": "Carica video",
    "RealTimeMonitoring": "Monitoraggio in tempo reale delle temperature e dell'utilizzo di CPU/GPU, layout personalizzabili con trascinamento, impostazioni stile personalizzate – padroneggia prestazioni ed estetica del desktop",
    "Chart": "Grafico",
    "Zigzagcolor": "Colore della linea (Inizio)",
    "Zigzagcolor1": "Colore della linea (Fine)",
    "Zigzagcolor2": "Colore dell'area del grafico a linee (Inizio)",
    "Zigzagcolor3": "Colore dell'area nel grafico a linee (fine)",
    "CustomMonitoring": "Monitoraggio personalizzato"
  }
}
//messageEnd 
 export default it 