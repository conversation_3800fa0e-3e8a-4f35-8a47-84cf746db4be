<script setup lang="ts">
import {defineProps, ref, onMounted, computed, watch} from 'vue'
import {hardware} from "@/modules/Game_Home/stores";
import { GPP_SendStatics } from '@/uitls/sendstatics';
import { useI18n } from 'vue-i18n';
const i18n = useI18n();
// @ts-ignore
const gamepp = window.gamepp as any
const $store = hardware()
const props = defineProps({
  changeMenuInfo: {
    type: Function,
    required: true
  }
})
let loading = ref(true)
let loading_count = 0
const memoryShow = ref(false)
const diskShow = ref(false)
let curH = ref(695)
let defalutH = 695
const activeCollapse_all = ref(["1"])

onMounted(() => {
  setTimeout(() => {
    emitChangeFn();
  },100)
  loadingFn();
  listenHardwareChangeMsg()
})

function listenHardwareChangeMsg() {
  const hw = new BroadcastChannel('hw')
  hw.onmessage = (e:any)=>{
    if (e.data.action && e.data.action == 'change') {
      emitChangeFn();
    }
  }
}

const computedGPUInfo = computed(()=>{
  if ($store.originData.GPU && $store.originData.GPU.SubNode) {
    try {
      let gpu_detect_list_str = window.localStorage.getItem('gpu_detect_list') // 拿最优显卡数据
      let gpu_detect_list:any = []
      if (gpu_detect_list_str) {
        try {
          gpu_detect_list = JSON.parse(gpu_detect_list_str)
        }catch (e) {
          gpu_detect_list = []
        }
      }
      // 创建一个映射关系，记录 gpu_detect_list 中元素的位置索引
      const indexMap = new Map();
      gpu_detect_list.forEach((item:{name:string}, index:number) => {
        indexMap.set(item.name, index);
      });
      let originGPUArr = JSON.parse(JSON.stringify($store.originData.GPU.SubNode))
      originGPUArr = originGPUArr.map((item:any,i:number) => {
        item.index = i
        return item
      })
      // 根据 arr1 的顺序来排序 arr2
      const sortedArr2 = originGPUArr.sort((a:any, b:any) => {
        const indexA = indexMap.get(a.VideoChipset);
        const indexB = indexMap.get(b.VideoChipset);

        // 如果某个元素不在 arr1 中，可以认为它的索引为 Infinity 或者一个较大的数
        const indexAOrDefault = indexA !== undefined ? indexA : Infinity;
        const indexBOrDefault = indexB !== undefined ? indexB : Infinity;

        return indexAOrDefault - indexBOrDefault;
      });
      console.log(sortedArr2)
      return sortedArr2
    }catch (e) {
      return $store.originData.GPU.SubNode
    }
  }
  return []
})

const all_disk_size = computed(() => {
  let v = 0;
  if ($store.originData.DRIVES){
    $store.originData.DRIVES.SubNode.forEach((item:any)=>{
      v += parseInt(String(item['DriveCapacity[MB]'] / 1024))
    })
  }
  return v
})

const SystemInfo = computed(() => {
  if(!$store.originData.COMPUTER) {
    return {
      WinName:"",
      SystemVer:"",
      SystemBit:"",
      SystemVer2:"",
    }
  }
  let HWInfoV = $store.originData.COMPUTER;
  let SystemBit = '';
  let WinName = '';
  let SystemVer = '';
  let SystemVerNum;
  let WinSystemVer = ["Professional", "ProfessionalWorkstation", "Home", "Education", "Enterprise", "Ultimate", "HomePremium", "HomeBasic", "Starter"];
  let WinSystemVer2 = ["hardwareInfo.professionalVersion", "hardwareInfo.professionalWorkstationVersion", "hardwareInfo.familyEdition", "hardwareInfo.educationEdition", "hardwareInfo.enterpriseEdition", "hardwareInfo.flagshipEdition", "hardwareInfo.familyPremiumEdition", "hardwareInfo.familyStandardEdition", "hardwareInfo.primaryVersion"];
    console.log(WinSystemVer2)
  let SystemName = (HWInfoV.OperatingSystem.replace('Home Basic', 'HomeBasic').replace('Home Premium', 'HomePremium').replace('Professional Workstation', 'ProfessionalWorkstation')).split(' ');
  if (HWInfoV.OperatingSystem.indexOf('(x64)') !== -1) {
    SystemBit = '64';
  } else if (HWInfoV.OperatingSystem.indexOf('(x32)')) {
    SystemBit = '32';
  }
  if (RemoveAllSpace(HWInfoV.OperatingSystem).indexOf('Windows11') !== -1) {
    WinName = 'Windows 11';
    SystemVer = '';
    SystemName.forEach((SystemNameV:string) => {
      let v = WinSystemVer.findIndex(e=>e==SystemNameV)
      if (v !== -1) {
        SystemVer = i18n.t(WinSystemVer2[v]);
      }
    })
    let SystemName1 = (HWInfoV.OperatingSystem).split('Build');
    return {
      WinName,
      SystemVer,
      SystemBit,
      SystemVer2: SystemName1[1],
    }
  }else if (RemoveAllSpace(HWInfoV.OperatingSystem).indexOf('Windows10') !== -1) {
    WinName = 'Windows 10';
    SystemVer = '';
    SystemName.forEach((SystemNameV:string) => {
      let v = WinSystemVer.findIndex(e=>e==SystemNameV)
      if (v !== -1) {
        SystemVer = i18n.t(WinSystemVer2[v]);
      }
    });
    let SystemName1 = (HWInfoV.OperatingSystem).split('Build');
    return {
      WinName,
      SystemVer,
      SystemBit,
      SystemVer2: SystemName1[1],
    }
  } else if (RemoveAllSpace(HWInfoV.OperatingSystem).indexOf('Windows7') !== -1) {
    SystemVer = i18n.t(WinSystemVer2[0]);
    SystemBit = String(RegExGetNum(SystemName[4]));
    SystemVerNum = (SystemName[6]);
    return {
      WinName: 'Windows 7',
      SystemVer,
      SystemBit,
      SystemVer2: SystemVerNum
    }
  } else if (RemoveAllSpace(HWInfoV.OperatingSystem).indexOf('WindowsServer') !== -1) {
    SystemVer = "";
    try {
        return {
            WinName: 'Windows Server',
            SystemVer,
            SystemBit,
            SystemVer2: (((HWInfoV.OperatingSystem).split('Build'))[1].replace(/\(.*?\)/g, ''))
        }
    }catch {
        return {
            WinName: 'Windows Server',
            SystemVer,
            SystemBit,
            SystemVer2: "null",
        }
    }
  } else if (RemoveAllSpace(HWInfoV.OperatingSystem).indexOf('Windows8') !== -1) {
    WinName = 'Windows 8.1';
    SystemVer = '';
    SystemName.forEach((SystemNameV:string) => {
      let v = WinSystemVer.findIndex(e=>e==SystemNameV)
      if (v !== -1) {
        SystemVer = WinSystemVer2[v];
      }
    });
    let SystemName1 = (HWInfoV.OperatingSystem).split('Build');
    return {
      WinName,
      SystemVer,
      SystemBit,
      SystemVer2: SystemName1[1]
    }
  } else if (RemoveAllSpace(HWInfoV.OperatingSystem).indexOf('WindowsVista') !== -1) {
    WinName = 'Windows Vista';
    SystemVer = '';
    SystemName.forEach((SystemNameV:string) => {
      let v = WinSystemVer.findIndex(e=>e==SystemNameV)
      if (v !== -1) {
        SystemVer = WinSystemVer2[v];
      }
    });
    let SystemName1 = (HWInfoV.OperatingSystem).split('Build');
    return {
      WinName,
      SystemVer,
      SystemBit,
      SystemVer2: SystemName1[1]
    }
  }else{
    return {
      WinName:"",
      SystemVer:"",
      SystemBit:"",
      SystemVer2:"",
    }
  }
})

const emitChangeFn = () => {
    if (!$store.originData.hasOwnProperty('CPU')) return
    if (i18n.locale.value === 'CN' || i18n.locale.value === 'ZH') {
        defalutH = 665
    }else{
        defalutH = 695
    }
  curH.value = defalutH
  // 计算CPU占用高度
  if ($store.originData.CPU && $store.originData.CPU.SubNode.length > 1) {
    curH.value += ($store.originData.CPU.SubNode.length - 1) * 47
  }
  // 计算GPU占用高度
  if ($store.originData.GPU && $store.originData.GPU.SubNode.length > 1) {
    // 内存数量小于等于4
    curH.value += ($store.originData.GPU.SubNode.length - 1) * 50
  }
  // 计算硬盘占用高度
  if (diskShow.value) { // 展开了
    curH.value += $store.originData.DRIVES.SubNode.length * 30
  } else {
    if ($store.originData.DRIVES && $store.originData.DRIVES.SubNode.length > 0 && $store.originData.DRIVES.SubNode.length <= 3) {
      // 数量小于等于3
      curH.value += $store.originData.DRIVES.SubNode.length * 30
    } else if ($store.originData.DRIVES && $store.originData.DRIVES.SubNode.length > 0 && $store.originData.DRIVES.SubNode.length > 3) {
      // 数量大于3 但是没展开
      curH.value += 30 * 3
    }
  }
  // 计算显示器占用高度
  if ($store.originData.MONITOR && $store.originData.MONITOR.SubNode.length > 1) {
    curH.value += ($store.originData.MONITOR.SubNode.length - 1) * 46
  }
  // 计算内存占用高度
  if (memoryShow.value) { // 内存展开了
    curH.value += $store.originData.MEMORY.SubNode.length * 30
  } else {
    if ($store.originData.MEMORY && $store.originData.MEMORY.SubNode.length > 0 && $store.originData.MEMORY.SubNode.length <= 4) {
      // 内存数量小于等于4
      curH.value += $store.originData.MEMORY.SubNode.length * 30
    } else if ($store.originData.MEMORY && $store.originData.MEMORY.SubNode.length > 0 && $store.originData.MEMORY.SubNode.length > 4) {
      // 内存数量大于4
      curH.value += 30 * 4
    }
  }
  const h = activeCollapse_all.value.length > 0 ? curH.value : 72
  props.changeMenuInfo(0, h);
}

const showOrHideMemory = () => {
  memoryShow.value = !memoryShow.value
  emitChangeFn();
}

const showOrHideDisk = () => {
  diskShow.value = !diskShow.value
  emitChangeFn();
}
const display_video_card = (VideoCard:string)=>{
  // 移除()和[]和两侧空白
  VideoCard = VideoCard.replace(/\(.*?\)/g, '').replace(/\[.*?\]/g, '').trim()
  return VideoCard
}
const gpu_brand = (data: any) => {
  const CPUSubvendor = (data.VideoCard).split(' ')
  const regex = /\[(.+?)\]/g
  const VideoBrandArr = (data.VideoCard.match(regex))
  let VideoBrand = ''
  if (VideoBrandArr) {
    VideoBrand = (VideoBrandArr[VideoBrandArr.length - 1]).replace(/\[|]/g, '')
  }
  if (VideoBrand === '') {
    VideoBrand = CPUSubvendor[0]
  }
    const VideoBrand1Lower =VideoBrand.toLowerCase()
    if (VideoBrand1Lower.includes('清华同方') || VideoBrand1Lower.includes('tongfang') ||  VideoBrand1Lower.includes('thtf')) {
        return i18n.t('hardwareInfo.brandLegalRisks')
    }
  return i18n.t('hardwareInfo.brand') + VideoBrand;
}

const gpu_memory_size_type = (data: any) => {
  const regex = /\[(.+?)\]/g
  const VideoMemory = (data.VideoMemory).split(' ')
  const VideoMemoryBrandArr = (data.VideoMemory.match(regex))
  let VideoMemoryBrand = ''
  if (VideoMemoryBrandArr) {
    VideoMemoryBrand = (VideoMemoryBrandArr[VideoMemoryBrandArr.length - 1]).replace(/\[|]/g, '')
  }
  let VideoType = ''
  if (VideoMemory[3]) {
    VideoType = VideoMemory[3]
  }
  let typeBrand = ''
  if (VideoType || VideoMemoryBrand) {
    typeBrand = ' (' + VideoType + ' ' + VideoMemoryBrand + ')'
  }
  let n = 1024
  if (data.VideoMemory.includes('MBytes')) {
    n = 1024
  } else if (data.VideoMemory.includes('KBytes')) {
    n = 1024*1024
  } else if (data.VideoMemory.includes('GBytes')) {
    n = 1
  }
  return Math.ceil(((VideoMemory[0] / n))) + 'G' + typeBrand
}
const monitorInfo = gamepp.hardware.getDisplayCardInfo.sync()['Element']
const monitor_RefreshFrequency = (data:any) => {
    const mname = monitor_name(data);
    const findItem = monitorInfo.find(item=>item.MonitorModal === mname);
    if (findItem) {
        return findItem['CurrentDisplayFrequency']
    }else{
        return ''
    }
}
const monitor_name = (data:any) => {
  let MonitorName = ''
  let MonitorNameStr = ''
  if (data.MonitorName !== 'Unknown') {
    MonitorName = data.MonitorName.replace(/\[.*?\]/g, '')
  }
  if (data['MonitorName(Manuf)']) {
    const brand = MonitorName.replace(/\s+/g, '')
    const model = data['MonitorName(Manuf)'].replace(/\s+/g, '')
    if (brand.toLowerCase() === model.toLowerCase()) {
      MonitorNameStr = data.MonitorName
    } else {
      MonitorNameStr = MonitorName + data['MonitorName(Manuf)']
    }
  } else {
    MonitorNameStr = data.MonitorName
  }
  return MonitorNameStr
}
const monitor_size = (data:any) => {
  let MonitorSize
  if (data['Max.HorizontalSize'] && data['Max.VerticalSize']) {
    const HorizontalSize = (data['Max.HorizontalSize']).replace(/[^0-9]/ig, '')
    const VerticalSize = (data['Max.VerticalSize']).replace(/[^0-9]/ig, '')
    MonitorSize = parseFloat((Math.sqrt(Math.pow(HorizontalSize, 2) + Math.pow(VerticalSize, 2)) / 2.54).toFixed(1))
  }
  return MonitorSize
}

function RemoveAllSpace(str:string) {
  return str.replace(/\s+/g, "");
}
function RegExGetNum(str:string) {
  return Number(str.replace(/[^0-9]/ig, ""));
}

const loadingFn = () => {
  loading_count++
  if (loading_count > 10) {
    loading.value = false
    emitChangeFn()
    return
  }
  if ($store.HwInfo.CPU) {
    emitChangeFn()
    loading.value = false
  }else{
    setTimeout(()=>{loadingFn()},1000)
  }
}

function GPP_Copy() {
  GPP_SendStatics(100405)
  if (!$store.bg_sensor_data.cpu.name) {
    $store.getBgSensorData()
  }
  let threads = 0
  let cores = 0
  Object.values($store.bg_sensor_data.cpu.thread).forEach((v:any)=>{
    cores += 1
    threads += Object.keys(v).length
  })
  let CPUTechnology = ''
  $store.HwInfo.CPU.SubNode.forEach((v:any)=>{
    if (v.ProcessorName.includes($store.bg_sensor_data.cpu.name)) {
      CPUTechnology = v.CPUTechnology
    }
  })
  let GPUname = ''
  let gpuMemorySizeType = ''
  $store.HwInfo.GPU.SubNode.forEach((v:any)=>{
    if (v.VideoChipset.includes($store.bg_sensor_data.gpu.name)) {
      GPUname = v.VideoCard || v.VideoChipset
      gpuMemorySizeType = gpu_memory_size_type(v)
    }
  })
  let disk = ''
  $store.originData.DRIVES.SubNode.forEach((v:any)=>{
    disk += v.DriveModel + ' ' + i18n.t('hardwareInfo.actualCapacity')+parseInt(String(v['DriveCapacity[MB]'] / 1024)) + 'GB '
  })
  let monitor = ''
  $store.originData.MONITOR.SubNode.forEach((v:any)=>{
    monitor += monitor_name(v)+i18n.t('home.resolution')+v['Resolutions']+ ' ' + i18n.t('hardwareInfo.refreshRate')+v.RefreshFrequency+ ' ' + i18n.t('hardwareInfo.screenSize') + ' '+ monitor_size(v)+i18n.t('hardwareInfo.inches')
  })
  let memory = ''
  memory += i18n.t('GameRebound.Capacity')+ $store.originData?.MEMORY.Property["TotalMemorySize[MB]"] / 1024 + 'GB '
  memory += i18n.t('GameRebound.Channel')+$store.originData?.MEMORY.Property.MemoryChannelsActive
  memory += i18n.t('hardwareInfo.frequency') + ": " + ((Math.ceil($store.originData?.MEMORY.Property['CurrentMemoryClock'].split('MHz')[0])) * 2) + "MHz \n"
  $store.originData.MEMORY.SubNode.forEach((v:any)=>{
    memory += v.ModuleManufacturer + ' ' + ((v.MemorySpeed).match(/\((.+?)\)/g)[0]).split('/')[0].replace('(', '') +v.ModuleSize.split(' ')[0]+'GB  '
  })
  const LegalRisks = i18n.t('hardwareInfo.LegalRisks')
  let Text = `${i18n.t('GameRebound.System')} ${SystemInfo.value.WinName} ${SystemInfo.value.SystemVer} ${SystemInfo.value.SystemBit}${i18n.t('hardwareInfo.bit')} ${i18n.t('hardwareInfo.version')} ${SystemInfo.value.SystemVer2}\n` +
    `${i18n.t('hardwareInfo.processor')} : ${$store.bg_sensor_data.cpu.name} ${i18n.t('hardwareInfo.coreCount')} ${cores} ${i18n.t('hardwareInfo.threadCount')} ${threads}${i18n.t('GameRebound.Process')} ${CPUTechnology}\n` +
    `${i18n.t('hardwareInfo.graphicsCard')}   : ${GPUname} ${i18n.t('hardwareInfo.streamProcessors')}  ${$store.bg_sensor_data.gpu.shadres} ${i18n.t('hardwareInfo.Videomemory')}  ${gpuMemorySizeType}\n` +
    `${i18n.t('hardwareInfo.motherboard')}   : ${$store.originData?.MOBO['Mainboard']['MainboardName']} ${($store.originData?.MOBO['Mainboard']['MainboardManufacturer']).replace('Technology', '').replace('And', '').replace('Development', '').replace('Computer', '').replace('COMPUTER', '').replace('Co.,LTD', '').replace('INC.', '')} ${i18n.t('hardwareInfo.chipGroup')} ${$store.originData?.MOBO['Property']['MotherboardChipset'].replace(/\(.*?\)/g, '')} \n` +
    `${i18n.t('hardwareInfo.hardDisk')}   : ${disk}\n` +
    `${i18n.t('hardwareInfo.display')} : ${monitor}\n` +
    `${i18n.t('hardwareInfo.memory')}   : ${memory}`
    Text = Text.replace('TONGFANG',LegalRisks).replace('tongfang',LegalRisks)
        .replace('清华同方',LegalRisks).replace('THTF',LegalRisks).replace('thtf',LegalRisks)
  navigator.clipboard.writeText(Text)
  ElMessage.success(i18n.t('hardwareInfo.configCopyed'))

  // let FileName = 'GamePP--Hwinfo'
  // let AppDataDir = gamepp.getAppDataDir.sync()
  // let save_path = AppDataDir.replace('AppData', 'Desktop').split('\\').slice(0, 4).join('\\') + '\\' + FileName;
  // try {
  //   gamepp.saveFileToDisk.promise(save_path + '.txt', Text)
  // } catch {
  //
  // }
}
function GPP_Copy_CPU() {
  GPP_SendStatics(100406)
  if (!$store.bg_sensor_data.cpu.name) {
    $store.getBgSensorData()
  }
  let threads = 0
  let cores = 0
  Object.values($store.bg_sensor_data.cpu.thread).forEach((v:any)=>{
    cores += 1
    threads += Object.keys(v).length
  })
  let CPUTechnology = ''
  $store.HwInfo.CPU.SubNode.forEach((v:any)=>{
    if (v.ProcessorName.includes($store.bg_sensor_data.cpu.name)) {
      CPUTechnology = v.CPUTechnology
    }
  })
  let Text = `${i18n.t('hardwareInfo.processor')} : ${$store.bg_sensor_data.cpu.name} ${i18n.t('hardwareInfo.coreCount')} ${cores} ${i18n.t('hardwareInfo.threadCount')} ${threads}${i18n.t('hardwareInfo.Process')} ${CPUTechnology}\n`

  navigator.clipboard.writeText(Text)
  ElMessage.success(i18n.t('hardwareInfo.configCopyed'))
}

function GPP_Copy_Memory(v:any) {
  GPP_SendStatics(100409)
  let memory = ''
  memory += v.ModuleManufacturer + ' ' + ((v.MemorySpeed).match(/\((.+?)\)/g)[0]).split('/')[0].replace('(', '') + v.ModuleSize.split(' ')[0]+'GB  '
  let Text = `${i18n.t('hardwareInfo.memory')}   : ${memory}`

  navigator.clipboard.writeText(Text)
    ElMessage.success(i18n.t('hardwareInfo.configCopyed'))
}
function GPP_Copy_GPU(v:any) {
  GPP_SendStatics(100407)
  console.log(v)
  if (!$store.bg_sensor_data.cpu.name) {
    $store.getBgSensorData()
  }
  let GPUname = ''
  let gpuMemorySizeType = ''
  GPUname = v.VideoCard
  gpuMemorySizeType = gpu_memory_size_type(v)
  let Text = `${i18n.t('hardwareInfo.graphicsCard')}   : ${GPUname}`
  if (v.NumberOfUnifiedShaders || v['NumberOfALUs(cores)']) {
    Text += ` ${i18n.t('hardwareInfo.streamProcessors')}  ${v.NumberOfUnifiedShaders || v['NumberOfALUs(cores)']}`
  }
  Text += ` ${i18n.t('hardwareInfo.Videomemory')}  ${gpuMemorySizeType}\n`
    const LegalRisks = i18n.t('hardwareInfo.LegalRisks')
    Text = Text.replace('TONGFANG',LegalRisks).replace('tongfang',LegalRisks)
        .replace('清华同方',LegalRisks).replace('THTF',LegalRisks).replace('thtf',LegalRisks)

  navigator.clipboard.writeText(Text)
  ElMessage.success(i18n.t('hardwareInfo.configCopyed'))

  let FileName = 'GamePP--Hwinfo'
  let AppDataDir = gamepp.getAppDataDir.sync()
  let save_path = AppDataDir.replace('AppData', 'Desktop').split('\\').slice(0, 4).join('\\') + '\\' + FileName;
  try {
    gamepp.saveFileToDisk.sync(save_path + '.txt', Text)
  } catch {

  }
}
function GPP_Copy_Disk(v:any) {
  GPP_SendStatics(100410)
  console.log(v);
  if (!$store.bg_sensor_data.cpu.name) {
    $store.getBgSensorData()
  }
  let disk = ''

  disk += v.DriveModel + ' ' +i18n.t('hardwareInfo.actualCapacity')+ parseInt(String(v['DriveCapacity[MB]'] / 1024)) + 'GB '

  let Text = `${i18n.t('hardwareInfo.hardDisk')}   : ${disk}\n`
  navigator.clipboard.writeText(Text)
  ElMessage.success(i18n.t('hardwareInfo.configCopyed'))
}
function GPP_Copy_Mainboard() {
  GPP_SendStatics(100408)
  let Text = `${i18n.t('hardwareInfo.motherboard')}   : ${$store.originData?.MOBO['Mainboard']['MainboardName']} ${($store.originData?.MOBO['Mainboard']['MainboardManufacturer']).replace('Technology', '').replace('And', '').replace('Development', '').replace('Computer', '').replace('COMPUTER', '').replace('Co.,LTD', '').replace('INC.', '')} ${i18n.t('hardwareInfo.chipGroup')} ${$store.originData?.MOBO['Property']['MotherboardChipset'].replace(/\(.*?\)/g, '')}`
  navigator.clipboard.writeText(Text)
    ElMessage.success(i18n.t('hardwareInfo.configCopyed'))
}
function GPP_Copy_System(){
  GPP_SendStatics(100412)
  let Text = `${i18n.t('GameRebound.System')}   : ${SystemInfo.value.WinName} ${SystemInfo.value.SystemVer} ${SystemInfo.value.SystemBit}位 版本号 ${SystemInfo.value.SystemVer2}`
  navigator.clipboard.writeText(Text)
    ElMessage.success(i18n.t('hardwareInfo.configCopyed'))
}
function GPP_Copy_Moniter(v:any){
  GPP_SendStatics(100411)
  console.log(v);

  let monitor = ''
  monitor += monitor_name(v)+i18n.t('home.resolution')+v['Resolutions']+ ' ' + i18n.t('hardwareInfo.refreshRate')+v.RefreshFrequency + ' ' + i18n.t('hardwareInfo.screenSize') + ' '+ monitor_size(v)+i18n.t('hardwareInfo.inches')
  let Text = `${i18n.t('hardwareInfo.display')} : ${monitor}`
  navigator.clipboard.writeText(Text)
    ElMessage.success(i18n.t('hardwareInfo.configCopyed'))
}

function FormartMonthToNumber(Month:any) {
  let date = new Date(Month);
  if ((date.toDateString()).indexOf('Invalid') === -1) {
    date = new Date(Month);
  } else {
    date = new Date(Month.replace(/\\/g, ""));
  }
  let Y = date.getFullYear() + '-';
  let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
  let D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate() + ' ';
  return Y + M + D;
}
function numFormat(num:any){
  if (!num) return ''
  var res=num.toString().replace(/\d+/, function(n:any){
    return n.replace(/(\d)(?=(\d{3})+$)/g,function($1:any){
      return $1+",";
    });
  })
  return res;
}

function powerTime(drivesV:any) {
  let PowerTime = null
  if (drivesV['Power-onHours']) {
    PowerTime = drivesV['Power-onHours']
  } else if (drivesV['PowerOnHours']) {
    PowerTime = drivesV['PowerOnHours']
  } else if (drivesV['[09]Power-onHours/CycleCount']) {
    let data = (drivesV['[09]Power-onHours/CycleCount']).match(/\(([^)]+)\)/)[1];
    PowerTime = (data.split('/'))[0]
  }
  if (PowerTime && !PowerTime.includes('hours')) {
    PowerTime += ' ' + '小时'
  } else if (PowerTime && PowerTime.includes('hours')) {
    PowerTime = PowerTime.replace('hours', '小时')
  }
  if (PowerTime !== null) {
    // if(Languagevalue === 'cn'){
      let TotalHours = PowerTime.replace('小时', '')
    // }else{
    //   var TotalHours = PowerTime.replace('Hour', '')
    // }
    let years =Math.floor(TotalHours / (24 * 365))
    let days = Math.floor((TotalHours / 24) % 365)
    let hours = Math.floor(TotalHours % 24)
    if(years == 0){
      return days + i18n.t('LoginArea.day') + hours +i18n.t('shutdownTimer.hours') +' ('+ numFormat(PowerTime) + ') '
    }else{
      return years + i18n.t('GamePlusOne.year') + days + i18n.t('LoginArea.day') + hours + i18n.t('shutdownTimer.hours') +' ('+ numFormat(PowerTime) + ') '
    }
  }else {
    return ''
  }
}

function powerCount(drivesV:any) {
  let PowerCount = null, PowerOnSHtml = '';
  if (drivesV['LifetimePower-OnResets']) {PowerCount = drivesV['LifetimePower-OnResets']} else if (drivesV['PowerCycles']) {PowerCount = drivesV['PowerCycles']} else if (drivesV['[0C]PowerCycleCount']) {
    let data = (drivesV['[0C]PowerCycleCount']).match(/\(([^)]+)\)/)[1];
    PowerCount = data.replace('Data = ', '')
  }

  if (PowerCount !== null) {
    return numFormat(PowerCount)
  }else {
    return ''
  }
}

function ssdRemainingLife(drivesV:any) {
  let DriveRemainingLifeHtml = ''
  if (drivesV.DriveRemainingLife) {
    DriveRemainingLifeHtml = '' + drivesV.DriveRemainingLife
  } else if (drivesV.DeviceHealth) {
    DriveRemainingLifeHtml = '' + drivesV.DeviceHealth
  }
  return DriveRemainingLifeHtml
}
function DiskCapacityConversion (str:any,standard:any) {
  if (!str) return ''
  const regex1 = /\((.+)\)/g;
  let Capacity:any = '';
  let point = 1;
  if (str.match(regex1)) {Capacity = Number(((str.match(regex1))[0]).replace(/[^0-9]/ig, ""));} else {Capacity = Number(str);}
  if (Capacity >= standard * 1000) {point = 100}
  let k = standard;
  let sizes = [' MB',' GB', ' TB',' PB',' EB',' ZB',' YB'];
  let i = Math.floor(Math.log(Capacity) / Math.log(k));
  return (Math.round((Capacity / Math.pow(k, i)) * point)) / point + sizes[i];
}
function HDD_Or_SSD (DRIVESDataV:any) {
  if ((DRIVESDataV.MediaRotationRate && (DRIVESDataV.MediaRotationRate).indexOf('SSD') !== -1) || (DRIVESDataV.DriveController)?.includes('NVMe') || (DRIVESDataV.Interface)?.includes('NVMe')) {
    return 'SSD'
  }else{
    return 'HDD'
  }
}
//分区信息
function getPartition(drivesV:any,index = null) {
  let Partition = ''
  let diskData = gamepp.getDiskInfo.sync();
  if (diskData) {
    diskData.forEach((drive:any)=>{
      if (drivesV['DriveModel'].includes(drive.diskName)){
        if (Partition != '') Partition += '、'
        Partition += drive.partitionName + drive.partitionSize+'GB'
      }
    })
    return Partition
  }else{
    return ''
  }
}
//驱动器控制器
function biosData(str:string) {
  let BIOSDateArr = str.split('/');
  return BIOSDateArr[2] + '-' + BIOSDateArr[0] + '-' + BIOSDateArr[1]
}
// 支持刷新率
function getVerticalFrequency(monitorV:any) {
  let VerticalFrequency = 'null'
  if (monitorV.VerticalFrequency) {
    VerticalFrequency = monitorV.VerticalFrequency
  }
  if (monitorV.LowestDisplayFrequency && monitorV.HighestDisplayFrequency) {
    VerticalFrequency = monitorV.LowestDisplayFrequency + 'Hz' + '-' + monitorV.HighestDisplayFrequency + 'Hz'
  }
  return VerticalFrequency
}
// 屏幕生产日期
function getMonitorDate(monitorV:any) {
  let ProductionDateHtml = '';
  if (monitorV['DateOfManufacture']) {
    let Data = monitorV['DateOfManufacture'].split(',')
    let Year:string|number = '', Week:string|number = '';
    for (let i = 0; i < Data.length; i++) {
      if (Data[i].includes('Year')) {Year = Number(RegExGetNum(Data[i]))}
      if (Data[i].includes('Week')) {Week = Number(RegExGetNum(Data[i]))}
    }
    return getDateOfISOWeek(Week, Year)
  }else{
    return ''
  }
}
/**
 * 根据周数获取日期
 * @param w 周
 * @param y 年
 * @returns {string}
 */
function getDateOfISOWeek(w, y) {
  var simple = new Date(y, 0, 1 + (w - 1) * 7);
  var dow = simple.getDay();
  var ISOweekStart = simple;
  if (dow <= 4)
    ISOweekStart.setDate(simple.getDate() - simple.getDay() + 1);
  else
    ISOweekStart.setDate(simple.getDate() + 8 - simple.getDay());
  var Mon = (ISOweekStart.getMonth() + 1 < 10 ? '0' + (ISOweekStart.getMonth() + 1) : ISOweekStart.getMonth() + 1) + '-';
  var Day = ISOweekStart.getDate() < 10 ? '0' + ISOweekStart.getDate() : ISOweekStart.getDate() + '';
  return ISOweekStart.getFullYear() + "-" + Mon + Day;
}
//转换时间戳
function FormatTimestampYH (time:any) {

  if (!time) {
    // @ts-ignore
    time = Date.parse(new Date()) / 1000
  }

  let date = new Date(time * 1000);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
  let Y = date.getFullYear() + '-';
  let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
  let D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate() + '';
  // h = ' ' + date.getHours() + ':';
  let h = ' ' + date.getHours().toString().padStart(2, 0) + ':';
  let m = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
  return Y + M + D + h + m;
}
//格式化秒数显示
function FormatSecondsToDHMS (second_time:any) {
  let time = parseInt(second_time) + i18n.t('shutdownTimer.sec');
  if (parseInt(second_time) > 60) {
    var second = parseInt(second_time) % 60;
    var min = parseInt(String(second_time / 60));
    time = min + i18n.t('shutdownTimer.min') + second + i18n.t('shutdownTimer.sec');
    if (min > 60) {
      min = parseInt(String(second_time / 60)) % 60;
      let hour = parseInt(String(parseInt(String(second_time / 60)) / 60));
      time = hour + i18n.t('shutdownTimer.hours') + min + i18n.t('shutdownTimer.min') + second + i18n.t('shutdownTimer.sec');
      if (hour > 24) {
        hour = parseInt(String(parseInt(String(second_time / 60)) / 60)) % 24;
        var day = parseInt(String(parseInt(String(parseInt(String(second_time / 60)) / 60)) / 24));
        time = day + i18n.t('shutdownTimer.day') + hour + i18n.t('shutdownTimer.hours') + min + i18n.t('shutdownTimer.min') + second + i18n.t('shutdownTimer.sec');
      }
    }
  }
  return time;
}

// 有大小核时显示的CPU核心数
function showNumberOfCPUCores (CPUSubNode: any) {
  let CPUCoresPEHtml = '';
  // 大小核处理
  if (CPUSubNode.NumberofCPUCoresPerformance && CPUSubNode.NumberofCPUCoresEfficient) {
    if (CPUSubNode.NumberofCPUCoresPerformance !== 0 || CPUSubNode.NumberofCPUCoresEfficient !== 0) {
      if (CPUSubNode.NumberofCPUCoresLowPowerEfficient) {
        CPUCoresPEHtml = ' (' + CPUSubNode.NumberofCPUCoresPerformance + 'P+' + CPUSubNode.NumberofCPUCoresEfficient + 'E+' + CPUSubNode.NumberofCPUCoresLowPowerEfficient+')'
      } else {
        CPUCoresPEHtml = ' (' + CPUSubNode.NumberofCPUCoresPerformance + 'P+' + CPUSubNode.NumberofCPUCoresEfficient + 'E)'
      }
    }
  }
  // AMD AI 300系列
  if (CPUSubNode.NumberofCPUCoresClassic || CPUSubNode.NumberofCPUCoresCompact) {
    if (CPUSubNode.NumberofCPUCoresClassic !== 0 || CPUSubNode.NumberofCPUCoresCompact !== 0) {
      CPUCoresPEHtml += ' ('
      if (CPUSubNode.NumberofCPUCoresClassic && CPUSubNode.NumberofCPUCoresClassic !== 0) {
        CPUCoresPEHtml += CPUSubNode.NumberofCPUCoresClassic
      }
      if (CPUSubNode.NumberofCPUCoresCompact && CPUSubNode.NumberofCPUCoresCompact !== 0) {
        if (CPUSubNode.NumberofCPUCoresClassic && CPUSubNode.NumberofCPUCoresClassic !== 0) {
          CPUCoresPEHtml += '+'
        }
        CPUCoresPEHtml += CPUSubNode.NumberofCPUCoresCompact+'C'
      }
      CPUCoresPEHtml += ')'
    }
  }
  return CPUCoresPEHtml;
}

// 有大小核时显示的CPU线程数
function showNumberOfLogicalCpus(CPUSubNode: any) {
  let CPULogicalPEHtml = '';
  if (CPUSubNode.NumberofLogicalCPUsPerformance && CPUSubNode.NumberofLogicalCPUsEfficient) {
    if (CPUSubNode.NumberofLogicalCPUsPerformance !== 0 || CPUSubNode.NumberofLogicalCPUsEfficient !== 0) {
      CPULogicalPEHtml = ' (' + CPUSubNode.NumberofLogicalCPUsPerformance + 'P+' + CPUSubNode.NumberofLogicalCPUsEfficient + 'E)'
    }
  }
  if (CPUSubNode.NumberofLogicalCPUsClassic || CPUSubNode.NumberofLogicalCPUsCompact) {
    if (CPUSubNode.NumberofLogicalCPUsClassic !== 0 || CPUSubNode.NumberofLogicalCPUsCompact !== 0) {
      CPULogicalPEHtml = ' ('
      let haveNumberofLogicalCPUsClassic = (CPUSubNode.NumberofLogicalCPUsClassic && CPUSubNode.NumberofLogicalCPUsClassic !== 0)
      if (haveNumberofLogicalCPUsClassic) {
        CPULogicalPEHtml += CPUSubNode.NumberofLogicalCPUsClassic
      }
      if (CPUSubNode.NumberofLogicalCPUsCompact && CPUSubNode.NumberofLogicalCPUsCompact !== 0) {
        if (haveNumberofLogicalCPUsClassic) {
          CPULogicalPEHtml += '+'
        }
        CPULogicalPEHtml += CPUSubNode.NumberofLogicalCPUsCompact+'C'
      }
      CPULogicalPEHtml += ')'
    }
  }

  return CPULogicalPEHtml
}

function showVideoBus (gpuV:any) {
  let VideoBus = gpuV.VideoBus;
  if (VideoBus === "Integrated") {VideoBus = gpuV.VideoBusInterface;}
  return VideoBus || "Integrated"
}

function showMemoryManufacturingDate (MemoryV:any) {
  if (MemoryV['ModuleManufacturingDate'] && MemoryV['ModuleManufacturingDate'] !== 'N/A') {
    let Data = MemoryV['ModuleManufacturingDate'].split(',')
    let Year:any = '', Week:any = '';
    for (let i = 0; i < Data.length; i++) {
      if (Data[i].includes('Year')) {Year = Number(RegExGetNum(Data[i]))}
      if (Data[i].includes('Week')) {Week = Number(RegExGetNum(Data[i]))}
    }
    return getDateOfISOWeek(Week, Year)
  }else{
    return 'N/A'
  }
}

function showMemoryCurrentTiming() {
  const SensorData = $store.bg_sensor_data
  if (SensorData['memory']['tcas'] === null) {
    return ''
  }
  return SensorData['memory']['tcas'] + '-' + SensorData['memory']['trcd'] + '-' + SensorData['memory']['trp'] + '-' + SensorData['memory']['tras']
}

const showMemoryClock = computed(() => {
    if ($store.originData && $store.originData?.MEMORY) {
        if ($store.originData?.MEMORY.SubNode && $store.originData?.MEMORY.SubNode[0] && $store.originData?.MEMORY.SubNode[0]['RowSize']) {
            return Math.ceil($store.originData?.MEMORY.Property['CurrentMemoryClock'].split('MHz')[0] * $store.originData?.MEMORY.SubNode.length * 2)
        }
        return (Math.ceil($store.originData?.MEMORY.Property['CurrentMemoryClock'].split('MHz')[0])) * 2
    }else {
        return 0
    }

})

watch(i18n.locale, ()=>{
    emitChangeFn();
})
</script>

<template>
  <!-- 硬件概览 -->
  <div class="HardWareAll-container">
    <el-collapse v-model="activeCollapse_all" @change="emitChangeFn">
      <el-collapse-item :title="$t('hardwareInfo.hardwareOverview')" name="1" class="as">
        <template #title>
          <div class="hardware-all-l2-title flex-items-center" style="margin: 0;">
            <span class="iconfont icon-hardware" style="color: #3579D5;font-size: 24px;margin-right: 5px;"></span>
            <span style="color: #ffffff">{{ $t('hardwareInfo.hardwareOverview') }}</span>

            <span class="ml-auto"></span>
            <el-tooltip effect="dark" :content="$t('hardwareInfo.copyAllHardwareInfo')" placement="top" popper-class="custom_tooltip allht" >
              <div class="hardware-iconfont-wrap" style="margin-left: 20px;" @click.stop="GPP_Copy">
                <span style="font-size:14px;color: var(--hardware-color-primary);" class="iconfont icon-Copy"></span>
              </div>
            </el-tooltip>
          </div>
        </template>
        <!-- CPU -->
        <div class="HardWareAll-CPU">
          <div class="hardware-all-l2-title flex-items-center" style="margin-top: 0;">
            <span class="iconfont icon-CPU" style="margin-right: 13px;"></span><span class="color777">{{ $t('hardwareInfo.processor') }}</span>
          </div>
          <div class="HardWareAll-info" v-if="$store.originData.CPU"
               v-for="(item,index) in $store.originData.CPU.SubNode" :index="'cpu'+index">
              <div class="hardware-box">
                <span>{{ item.ProcessorName }}</span>
                <div class="child-space-right-10">
                  <span>{{ $t('hardwareInfo.coreCount') }}{{ item.NumberofCPUCores }} {{showNumberOfCPUCores(item)}}</span>
                  <span>{{ $t('hardwareInfo.threadCount') }}{{ item.NumberofLogicalCPUs }} {{showNumberOfLogicalCpus(item)}}</span>
                  <span
                      v-if="$store.bg_sensor_data.cpu && $store.bg_sensor_data.cpu.clock">{{ $t('hardwareInfo.currentFrequency') }} {{
                      $store.bg_sensor_data.cpu.clock
                    }}MHz</span>
                  <span
                      v-if="$store.bg_sensor_data.cpu && $store.bg_sensor_data.cpu.voltage">{{ $t('hardwareInfo.currentVoltage') }}{{
                      $store.bg_sensor_data.cpu.voltage
                    }}V</span>
                </div>
              </div>
              <div>
                <el-tooltip effect="dark" :content="$t('hardwareInfo.copy')" placement="top" popper-class="custom_tooltip" :show-after="200">
                  <div class="hardware-iconfont-wrap" @click="GPP_Copy_CPU">
                    <span class="iconfont icon-Copy"></span>
                  </div>
                </el-tooltip>
              </div>
              <div class="hw_hover cpu_hover">
                <p v-if="item['ReleaseDate']">
                  <span>{{ $t('hardwareInfo.releaseDate') }}</span>
                  <text>{{item['ReleaseDate']}}</text>
                </p>
                <p v-if="item['CPUCodeName']">
                  <span>{{ $t('hardwareInfo.codeName') }}</span>
                  <text>{{item['CPUCodeName']}}</text>
                </p>
                <p v-if="item['CPUThermalDesignPower(TDP)']">
                  <span>{{ $t('hardwareInfo.thermalDesignPower') }}</span>
                  <text>{{item['CPUThermalDesignPower(TDP)']}}</text>
                </p>
                <p v-if="item['CPUMax.JunctionTemperature(Tj,max)']">
                  <span>{{ $t('hardwareInfo.maxTemperature') }}</span>
                  <text>{{item['CPUMax.JunctionTemperature(Tj,max)']}}</text>
                </p>
                <p v-if="item.CPUID">
                  <span>CPU ID</span>
                  <text>{{item.CPUID}}</text>
                </p>
              </div>
          </div>
        </div>
        <!-- CPU -->
        <!-- GPU显卡 -->
        <div class="HardWareAll-GPU">
          <div class="hardware-all-l2-title flex-items-center">
            <span class="iconfont icon-GPU" style="margin-right: 13px;"></span>
            <span>{{ $t('hardwareInfo.graphicsCard') }}</span>
          </div>
          <div class="HardWareAll-info"
               v-if="$store.originData.GPU"
               v-for="(item,index) in computedGPUInfo"
               :key="'gpu'+index"
          >
            <div class="hardware-box">
              <div>{{ display_video_card(item.VideoCard) }}</div>
              <span class="child-space-right-5">
              <span>{{ gpu_brand(item) }}</span>
              <span v-if="item.NumberOfUnifiedShaders || item['NumberOfALUs(cores)']">{{ $t('hardwareInfo.streamProcessors') }}{{ item.NumberOfUnifiedShaders || item['NumberOfALUs(cores)'] }}</span>
              <span>{{ $t('hardwareInfo.Videomemory') }}{{ gpu_memory_size_type(item) }}</span>
            </span>
            </div>
            <div>
              <el-tooltip effect="dark" :content="$t('hardwareInfo.copy')" placement="top" popper-class="custom_tooltip" :show-after="200">
                <div class="hardware-iconfont-wrap" @click="GPP_Copy_GPU(item)">
                  <span class="iconfont icon-Copy"></span>
                </div>
              </el-tooltip>
            </div>
            <div class="hw_hover gpu_hover">
              <p v-if="item['ReleaseDate']">
                <span>{{ $t('hardwareInfo.releaseDate') }}</span>
                <text>{{item['ReleaseDate']}}</text>
              </p>
              <p v-if="item['VideoChipsetCodename']">
                <span>{{ $t('hardwareInfo.codeName') }}</span>
                <text>{{item['VideoChipsetCodename']}}</text>
              </p>
              <p>
                <span>{{ $t('hardwareInfo.busSpeed') }}</span>
                <text>{{showVideoBus(item)}}</text>
              </p>
              <p v-if="item['DriverVersion']">
                <span>{{ $t('hardwareInfo.driverInfo') }}</span>
                <text>{{item['DriverVersion']}}</text>
              </p>
              <p v-if="item['DriverDate']">
                <span>{{ $t('hardwareInfo.driverInstallDate') }}</span>
                <text>{{FormartMonthToNumber(item['DriverDate'])}}</text>
              </p>
              <p v-if="item['HardwareID']">
                <span>{{ $t('hardwareInfo.hardwareID') }}</span>
                <text>{{item['HardwareID']}}</text>
              </p>
            </div>
          </div>
        </div>
        <!-- GPU显卡 -->
        <!-- 主板 -->
        <div class="HardWareAll-mainboard">
          <div class="hardware-all-l2-title flex-items-center">
            <span class="iconfont icon-board" style="margin-right: 13px;"></span>
            <span>{{ $t('hardwareInfo.motherboard') }}</span>
          </div>
          <div class="HardWareAll-info" v-if="$store.originData.MOBO">
            <div class="hardware-box">
              <span>{{ $store.originData?.MOBO['Mainboard']['MainboardName'] }}</span>
              <br />
              <span class="child-space-right-5">
              <span v-if="$store.originData.MOBO['Mainboard']['MainboardManufacturer'] === 'Notebook'">{{ $t('hardwareInfo.brand') }}{{$store.originData?.MOBO['System']['SystemManufacturer']}}</span>
              <span v-else>{{ $t('hardwareInfo.brand') }}{{($store.originData?.MOBO['Mainboard']['MainboardManufacturer']).replace('Technology', '').replace('And', '').replace('Development', '').replace('Computer', '').replace('COMPUTER', '').replace('Co.,LTD', '').replace('INC.', '')}}</span>
              <span>{{ $t('hardwareInfo.chipGroup') }}{{$store.originData?.MOBO['Property']['MotherboardChipset'].replace(/\(.*?\)/g, '')}}</span>
            </span>
            </div>
            <div>
              <el-tooltip effect="dark" :content="$t('hardwareInfo.copy')" placement="top" popper-class="custom_tooltip" :show-after="200">
                <div class="hardware-iconfont-wrap" @click="GPP_Copy_Mainboard">
                  <span class="iconfont icon-Copy"></span>
                </div>
              </el-tooltip>
            </div>
            <div class="hw_hover mainboard_hover">
              <p>
                <span>{{ $t('hardwareInfo.BIOSDate') }}</span>
                <text>{{biosData($store.originData.MOBO.BIOS.BIOSReleaseDate)}}</text>
              </p>
              <p>
                <span>{{ $t('hardwareInfo.BIOSVersion') }}</span>
                <text>{{$store.originData.MOBO.Property.BIOSVersion}}</text>
              </p>
              <p>
                <span>{{ $t('hardwareInfo.PCIESlots') }}</span>
                <text>{{$store.originData.MOBO.Property.MotherboardSlots}}</text>
              </p>
              <p>
                <span>{{ $t('hardwareInfo.PCIEVersion') }}</span>
                <text>{{$store.originData.MOBO.Property.PCIExpressVersionSupported}}</text>
              </p>
            </div>
          </div>
        </div>
        <!-- 主板 -->
        <!-- 内存 -->
        <div class="HardWareAll-memory">
          <div class="hardware-all-l2-title flex-wrap" :style="{'--mem-span-text-ml--': ($i18n.locale !== 'CN' && $i18n.locale !== 'ZH') ? '5px' : '-2px'}">
            <span class="iconfont icon-Dram" style="margin-right: 13px;"></span>
            <span>{{ $t('hardwareInfo.memory') }}</span>
            <span
                class="ml-auto"
                  v-if="$store.originData.MEMORY">{{ $t('hardwareInfo.memoryBarCount') }}<text style="color: #ffffff;">{{ $store.originData?.MEMORY.SubNode.length }}</text>
            </span>
            <span
                v-if="$store.originData.MEMORY" class="ml-5">{{ $t('hardwareInfo.totalSize') }}<text style="color: #ffffff;">{{$store.originData?.MEMORY.Property["TotalMemorySize[MB]"] / 1024 }}GB</text>
              </span>
            <span v-if="$store.originData.MEMORY"
                  class="ml-5">{{ $t('hardwareInfo.channelCount') }}<text style="color: #ffffff;">{{ $store.originData?.MEMORY.Property.MemoryChannelsActive }}</text>
            </span>
            <span v-if="$store.originData.MEMORY" class="ml-5">{{ $t('hardwareInfo.currentFrequency') }}<text style="color: #ffffff;">{{ showMemoryClock }}MHz</text>
            </span>
            <span v-if="$store.originData.MEMORY && showMemoryCurrentTiming() != ''" class="ml-5">{{ $t('GameRebound.Timing') }}<text style="color: #ffffff;">{{showMemoryCurrentTiming()}}</text>
            </span>
            <span
                class="hideBtn iconfont icon-hideshow"
                :class="{'icon_180deg':memoryShow}"
                @click="showOrHideMemory"
                v-if="$store.originData.MEMORY && $store.originData.MEMORY.SubNode.length > 4"
            ></span>
          </div>
            <template v-if="$store.originData.MEMORY" v-for="(item,index) in $store.originData.MEMORY.SubNode">
                <div class="HardWareAll-info" v-if="$store.originData.MEMORY && (index <= 3 || memoryShow)"
                     :key="'memory'+index">
                    <div class="hardware-box" >
                          <span class="child-space-right-5">
                              <span>{{ item.ModuleManufacturer }}</span>
                              <span v-if="item.MemorySpeed">{{ ((item.MemorySpeed).match(/\((.+?)\)/g)[0]).split('/')[0].replace('(', '') }}</span>
                              <span v-if="!item.MemorySpeed && item.RowSize">{{item.MemoryType + ' '}}{{ showMemoryClock + 'MHz' }}</span>
                              <!--              <span>({{ Math.floor(item.MemorySpeed.split(' ')[0]) }} MHz)</span>-->
                              <span v-if="item.ModuleSize">{{ item.ModuleSize.split(' ')[0] }}GB</span>
                          </span>
                        <div class="hw_hover memory_hover">
                            <p>
                                <span>{{ $t('hardwareInfo.productionDate') }}</span>
                                <text>{{showMemoryManufacturingDate(item)}}</text>
                            </p>
                            <p>
                                <span>{{ $t('hardwareInfo.Pellet') }}</span>
                                <text>{{item['SDRAMManufacturer']}}</text>
                            </p>
                            <p>
                                <span>{{ $t('hardwareInfo.memoryBarEquivalentFrequency') }}</span>
                                <text>{{item['MemorySpeed']}}</text>
                            </p>
                            <p>
                                <span>{{ $t('hardwareInfo.hardwareID') }}</span>
                                <text>{{item['ModuleSerialNumber']}}</text>
                            </p>
                        </div>
                    </div>
                    <div>
                        <el-tooltip effect="dark" :content="$t('hardwareInfo.copy')" placement="top" popper-class="custom_tooltip" :show-after="200">
                            <div class="hardware-iconfont-wrap" @click="GPP_Copy_Memory(item)">
                                <span class="iconfont icon-Copy"></span>
                            </div>
                        </el-tooltip>
                    </div>
                </div>
            </template>

        </div>
        <!-- 内存 -->
        <!-- 硬盘 -->
        <div class="HardWareAll-disk">
          <div class="hardware-all-l2-title flex-items-center">
            <span class="iconfont icon-Harddisk" style="margin-right: 13px;"></span>
            <span>{{ $t('hardwareInfo.hardDisk') }}</span>
            <span v-if="$store.originData.DRIVES" class="ml-auto">{{ $t('hardwareInfo.hardDiskCount') }}{{$store.originData?.DRIVES.SubNode.length}}</span>
            <span style="margin-left: 10px;">{{ $t('hardwareInfo.totalSize') }}{{all_disk_size}}GB</span>
            <span
                class="hideBtn iconfont icon-hideshow"
                :class="{'icon_180deg':diskShow}"
                @click="showOrHideDisk"
                v-if="$store.originData.DRIVES && $store.originData.DRIVES.SubNode.length > 3"
            ></span>
          </div>
          <template v-if="$store.originData.DRIVES" v-for="(item, index) in $store.originData.DRIVES.SubNode">
              <div class="HardWareAll-info" v-if="$store.originData.DRIVES && (index < 3 || diskShow)" :key="'drive'+index">
                  <div class="hardware-box">
              <span
                  class="flex-items-center"
              >
                <span>{{ item.DriveModel }}</span>
                <span class="ml-auto">{{ $t('hardwareInfo.actualCapacity') }}{{ parseInt(String(item['DriveCapacity[MB]'] / 1024)) }}GB</span>
                <span style="margin-left: 10px">{{ $t('hardwareInfo.type') }}{{HDD_Or_SSD(item)}}</span>
              </span>
                  </div>
                  <div>
                      <el-tooltip effect="dark" :content="$t('hardwareInfo.copy')" placement="top" popper-class="custom_tooltip" :show-after="200">
                          <div class="hardware-iconfont-wrap" @click="GPP_Copy_Disk(item)">
                              <span class="iconfont icon-Copy"></span>
                          </div>
                      </el-tooltip>
                  </div>
                  <div class="hw_hover disk_hover">
                      <p>
                          <span>{{ $t('hardwareInfo.powerOnTime') }} </span>
                          <text>{{powerTime(item)}}</text>
                      </p>
                      <p>
                          <span>{{ $t('hardwareInfo.powerOnCount') }} </span>
                          <text>{{powerCount(item)}}</text>
                      </p>
                      <p v-if="ssdRemainingLife(item) !== ''">
                          <span>{{ $t('hardwareInfo.SSDRemainingLife') }} </span>
                          <text>{{ssdRemainingLife(item)}}</text>
                      </p>
                      <p>
                          <span>{{ $t('hardwareInfo.partitionInfo') }} </span>
                          <text>{{getPartition(item)}}</text>
                      </p>
                      <p>
                          <span>{{ $t('hardwareInfo.hardDiskController') }}</span>
                          <text>{{item['DriveController']}}</text>
                      </p>
                      <p>
                          <span>{{ $t('hardwareInfo.driverNumber') }}</span>
                          <text>{{item['DriveSerialNumber']}}</text>
                      </p>
                  </div>
              </div>
          </template>
        </div>
        <!-- 硬盘 -->
        <!-- 显示器 -->
        <div class="HardWareAll-monitor">
          <div class="hardware-all-l2-title flex-items-center">
            <span class="iconfont icon-Monitor" style="margin-right: 13px;"></span>
            <span>{{ $t('hardwareInfo.display') }}</span>
          </div>
          <div class="HardWareAll-info"
               v-if="$store.originData.MONITOR"
               v-for="(item,index) in $store.originData.MONITOR.SubNode"
               :key="'monitor'+index"
          >
            <div class="hardware-box">
              <span>{{monitor_name(item)}}</span> <span v-if="!monitor_RefreshFrequency(item)"> ({{ $t('hardwareInfo.screenNotActivated') }})</span>
              <br/>
              <span class="child-space-right-5">
                <span>{{ $t('home.resolution') }}{{item.Resolutions}} </span>
                <span> {{ $t('hardwareInfo.screenSize') }}{{ monitor_size(item) }}{{ $t('hardwareInfo.inches') }}</span>
                <span v-if="monitor_RefreshFrequency(item)">{{ $t('hardwareInfo.refreshRate') }}{{monitor_RefreshFrequency(item)}}Hz</span>
              </span>
            </div>
            <div>
              <el-tooltip effect="dark" :content="$t('hardwareInfo.copy')" placement="top" popper-class="custom_tooltip" :show-after="200">
                <div class="hardware-iconfont-wrap">
                  <span @click="GPP_Copy_Moniter(item)" class="iconfont icon-Copy"></span>
                </div>
              </el-tooltip>
            </div>
            <div class="hw_hover monitor_hover">
              <p>
                <span>{{ $t('hardwareInfo.productionDate') }}</span>
                <text>{{getMonitorDate(item)}}</text>
              </p>
              <p v-if="getVerticalFrequency(item) !== null">
                <span>{{ $t('hardwareInfo.supportRefreshRate') }}</span>
                <text>{{getVerticalFrequency(item)}}</text>
              </p>
              <p>
                <span>{{ $t('hardwareInfo.screenLongAndShort') }}</span>
                <text>{{item['Max.HorizontalSize'] + '、'+item['Max.VerticalSize']}}</text>
              </p>
              <p>
                <span>{{ $t('hardwareInfo.driverNumber') }}</span>
                <text>{{item['MonitorHardwareID']}}</text>
              </p>
            </div>
          </div>
        </div>
        <!-- 显示器 -->
        <!-- 系统信息 -->
        <div class="HardWareAll-system">
          <div class="hardware-all-l2-title flex-items-center">
            <span class="iconfont icon-nav_toolbox" style="margin-right: 13px;"></span>
            <span>{{ $t('hardwareInfo.systemInfo') }}</span>
          </div>
          <div class="HardWareAll-info" v-if="$store.originData.COMPUTER">
            <div class="hardware-box">
              <span class="child-space-right-5">
                <span>{{SystemInfo.WinName}}</span>
                <span>{{SystemInfo.SystemVer}}</span>
                <span>{{SystemInfo.SystemBit}}位</span>
              </span>
              <br />
              <span>{{ $t('hardwareInfo.version') }} {{SystemInfo.SystemVer2}}</span>
            </div>
            <div>
              <el-tooltip effect="dark" :content="$t('hardwareInfo.copy')" placement="top" popper-class="custom_tooltip" :show-after="200">
                <div class="hardware-iconfont-wrap" @click="GPP_Copy_System">
                  <span class="iconfont icon-Copy"></span>
                </div>
              </el-tooltip>
            </div>
            <div class="hw_hover system_hover">
              <p>
                <span>{{ $t('hardwareInfo.systemInstallDate') }}</span>
                <text>{{FormatTimestampYH($store.originData.COMPUTER.ComputerInstallDate)}}</text>
              </p>
              <p>
                <span>{{ $t('hardwareInfo.systemBootTime') }}</span>
                <text>{{FormatTimestampYH($store.originData.COMPUTER.ComputerSystemRunDate)}}</text>
              </p>
              <p>
                <span>{{ $t('hardwareInfo.systemRunTime') }}</span>
                <text>{{FormatSecondsToDHMS(Date.parse(new Date()) / 1000 - $store.originData.COMPUTER.ComputerSystemRunDate)}}</text>
              </p>
            </div>
          </div>
        </div>
        <!-- 系统信息 -->
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<style lang="scss" scoped>
.HardWareAll-container {
  width: 100%;
  background: rgba(45 ,46 ,57, 0.8);
  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  padding: 0 25px 0 20px;
  font-size: 12px;
    --mem-span-text-ml--: 5px;

  .iconfont.icon-Copy {
    // 复制iconfont样式
    color: #3579d5;
    cursor: pointer;
    &:hover {
      color: #ffffff;
    }
  }

  .hardware-box {
    width: 100%;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  .HardWareAll-CPU, .HardWareAll-memory, .HardWareAll-GPU, .HardWareAll-disk, .HardWareAll-mainboard,.HardWareAll-system,.HardWareAll-monitor{
    position: relative;
    .iconfont {
      color: #777777;
      font-size: 16px !important;
      height: 16px;
      line-height: 14px;
    }

    span {
      font-size: 12px;
    }

    .HardWareAll-info {
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      transition: all 0.3s;
      position: relative;

      .iconfont {
        display: none;
        margin: 0;
        line-height: 1;
      }

      &:hover {
        background: rgb(52 54 71 / 80%);
        border-radius: 2px;
        padding: 0 15px;

        .iconfont {
          display: block;
          margin-left: 5px;
        }

        .hw_hover {
          display: block;
        }
      }

      span {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }

    .hw_hover {
      position: absolute;
      top: 100%;
      left: 0;
      margin-top: 10px;
      display: none;
      background: #343647;
      //width: 100%;
      z-index: 999;
      padding: 15px 20px 20px 20px;
      border-radius: 4px;
      transition: all 0.3s;
      transition-delay: 150ms;

      &.cpu_hover {
       //width: 50%;
      }
      &.memory_hover,&.disk_hover{
        //width: 70%;
      }

      &.monitor_hover,&.system_hover {
        //width: 60%;
        top: calc(-100% - 90px) !important;
        &:before {
          content: '';
          position: absolute;
          top: 100%;
          left: 15px;
          width: 0;
          height: 0;
          border-top: 5px solid #343647;
          border-right: 5px solid transparent;
          border-left: 5px solid transparent;
          border-bottom: 5px solid transparent;
        }
      }
      &.monitor_hover {
        top: calc(-100% - 115px) !important;
      }
      &.system_hover {
        top: calc(-100% - 90px);
      }

      &:before {
        content: '';
        position: absolute;
        top: -10px;
        left: 15px;
        width: 0;
        height: 0;
        border-top: 5px solid transparent;
        border-right: 5px solid transparent;
        border-left: 5px solid transparent;
        border-bottom: 5px solid #343647;
      }

      p {
        display: flex;
        font-size: 12px;
        justify-content: flex-start;
        align-items: center;
        margin-top: 5px;
        span {
          color: #777777;
          width: 100px;
        }
        text {
          color: #ffffff;
          margin-left: 30px;
        }
      }
    }
  }

  .HardWareAll-memory {
    width: 100%;
    margin-top: 15px;

    .HardWareAll-info {
      margin-top: 0;
    }

      .flex-wrap {
          display: flex;
          flex-wrap: wrap;
          justify-content: flex-start;
      }

    .hardware-all-l2-title {
      padding-right: 0;

        .ml-5 {
            margin-left: 5px;
        }

        .ml-10 {
            margin-left: 10px;
        }

        span {
            display: inline-block;
            white-space: normal;
            text {
                margin-left: var(--mem-span-text-ml--);
            }
        }
    }
  }

  .HardWareAll-system,.HardWareAll-monitor {
    margin-top: 15px;
  }
  .HardWareAll-system {
    margin-bottom: 10px;
  }

  .HardWareAll-GPU {
    .HardWareAll-info {
      //background: #343647;
      border-radius: 4px;
      width: 100%;
      height: 50px;
      padding: 2px 0;
      transition: all 0.3s;

      &:hover {
        background: #343647;
        padding: 2px 15px;
      }
    }
  }

  .HardWareAll-disk {
    margin-top: 15px;

    .HardWareAll-info {
      margin-top: 0;
      span {
        margin-top: 1px;
        margin-bottom: 1px;
      }
    }
    .hardware-all-l2-title {
      padding-right: 0;
    }
  }

  .HardWareAll-info {
    span {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }

  .hardware-all-l2-title {
    width: 100%;
    color: #777777;
    padding-right: 25px;
    margin-bottom: 15px;
    margin-top: 15px;

      span {
          white-space: nowrap;
      }

    .iconfont {
      font-size: 12px;
    }

    .hideBtn {
      margin-left: 20px;
      cursor: pointer;
      transition: all .3s;

      &.icon_180deg {
        transform: rotate(180deg);
      }
    }
  }
}

.child-space-right-10 {
  & > * {
    margin-right: 10px;
  }
}

.child-space-right-5 {
  & > * {
    margin-right: 5px;
  }
}

.color777 {
  color: #777777;
}
</style>
<style lang="scss">

.hardware-iconfont-wrap {
    width: 30px;
    height: 30px;
    cursor: pointer;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;

    .iconfont {
      color: #3579d5 !important;
      font-size: 14px;
    }

    &:hover {
      background: #343647;

      .iconfont {
        color: #ffffff !important;
      }
    }
  }
</style>
