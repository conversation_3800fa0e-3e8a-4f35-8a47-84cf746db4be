//获取电脑配置
let HWInfoObj = [], COMPUTER = {}, COMPUTERValue = {}, CPUValue = {}, CPUPropertyValue = {}, CPUSubNodeValue = {}, MOBOValue = {},
    MOBOPropertyValue = {}, MOBOBIOSValue = {}, MOBOMainboardValue = {}, MOBOSystemValue = {};
let GPUPropertyValue = {}, GPUSubNodeValue = {};
let MEMORYPropertyValue = {}, MEMORYValue = {}, MEMORYSubNodeValue = {};
let MONITORPropertyValue = {}, MONITORValue = {};
let DRIVESPropertyValue = {}, DRIVESSubNodeValue = {};
let SOUNDPropertyValue = {}, SOUNDSubNodeValue = {};
let NETWORKPropertyValue = {}, NETWORKSubNodeValue = {};
let SMBPropertyValue = {}, SMBSubNodeValue = {};
let BUSPropertyValue = {}, BUSSubNodeValue = {};
let USBPropertyValue = {}, USBSubNodeValue = {};
let L1Cache = 0, L2Cache = 0, L3Cache = 0;
let HWinfoResolutionArr = [], DisplayCardInfoArr = [];
//CPU
const NeedCPUInfoArr = ["CPU ID", "Original Processor Frequency", "Original Processor Frequency [MHz]", "CPU Stepping", "CPU Code Name", "CPU Technology", "CPU Thermal Design Power (TDP)", "CPU Power Limit 1 - Long Duration", "CPU Power Limit 2 - Short Duration", "CPU Max. Junction Temperature (Tj,max)", "CPU Platform", "Microcode Update Revision", "Number of CPU Cores", "Number of Logical CPUs", "CPU LFM (Minimum)", "CPU HFM (Base)", "CPU Turbo Max", "Turbo Ratio Limits", "CPU Current", "LLC/Ring Maximum", "LLC/Ring Current", "System Agent Current", "L1 Cache","L1 Cache (P-cores)","L1 Cache (E-cores)","L2 Cache","L2 Cache (P-cores)","L2 Cache (E-cores)", "L3 Cache", "L1 Cache (P-Cores)", "L1 Cache (E-Cores)", "L2 Cache (P-Cores)", "L2 Cache (E-Cores)", "Instruction TLB", "Data TLB", "MMX Technology", "Streaming SIMD Extensions", "Streaming SIMD Extensions 2", "Streaming SIMD Extensions 3", "Streaming SIMD Extensions 4.1", "Streaming SIMD Extensions 4.2", "64-bit Extensions", "AVX Support", "Advanced Vector Extensions 2 (AVX2)", "Advanced Vector Extensions 512 (AVX-512)", "AES Cryptography Support", "Fused Multiply Add (FMA)", "Hardware Lock Elision (HLE)", "Restricted Transactional Memory (RTM)", "x86-64 Long Mode"];
//主板
const NeedMOBOInfoArr = ['Computer Brand Name', 'Motherboard Model', 'Motherboard Chipset', 'Motherboard Slots', 'PCI Express Version Supported', 'USB Version Supported', 'BIOS Vendor', 'BIOS Version', 'BIOS Release Date', 'BIOS Size', 'Mainboard Manufacturer', 'Mainboard Name', 'UUID', 'System Manufacturer', 'Case Type', 'Manufacturer', 'UEFI BIOS', 'Super-IO/LPC Chip', 'Trusted Platform Module (TPM) Chip'];
//显卡
const NeedGPUInfoArr = ['Video Chipset', 'Video Chipset Codename', 'Video Memory', 'Video Card', 'Video Bus', 'Graphics Processor Clock', 'Graphics Memory Clock', 'Graphics Memory Bus Width', 'Number Of ROPs', 'Number Of Unified Shaders','Number Of ALUs (cores)', 'Number Of TMUs (Texture Mapping Units)', 'ASIC Quality', 'Driver Manufacturer', 'Driver Version', 'Driver Date', 'NVIDIA SLI Status', 'Hardware ID','ASIC Manufacturer','ASIC Serial Number','ASIC Quality'];
//内存
const NeedMEMORYInfoArr = ['Total Memory Size', 'Total Memory Size [MB]', 'Current Memory Clock', 'Memory Channels Active', 'Current Timing (tCAS-tRCD-tRP-tRAS)', 'Command Rate','Command Rate (CR)', 'Read to Read Delay (tRD_RD) Same Rank', 'Read to Read Delay (tRD_RD) Different Rank', 'Read to Read Delay (tRD_RD) Different DIMM', 'Write to Write Delay (tWR_WR) Same Rank', 'Write to Write Delay (tWR_WR) Different Rank', 'Write to Write Delay (tWR_WR) Different DIMM', 'Read to Write Delay (tRD_WR) Same Rank', 'Read to Write Delay (tRD_WR) Different Rank', 'Read to Write Delay (tRD_WR) Different DIMM', 'Write to Read Delay (tWR_RD) Same Rank (tWTR)', 'Write to Read Delay (tWR_RD) Different Rank', 'Write to Read Delay (tWR_RD) Different DIMM', 'RAS# to RAS# Delay (tRRD)', 'Refresh Cycle Time (tRFC)', 'Four Activate Window (tFAW)', 'Module Number', 'Module Size', 'Memory Type', 'Memory Speed', 'Module Manufacturer', 'Module Part Number', 'Module Serial Number', 'Module Manufacturing Date', 'SDRAM Manufacturer','Module Nominal Voltage (VDD)'];
//显示器
const NeedMONITORInfoArr = ['Monitor Name', 'Monitor Name (Manuf)', 'Date Of Manufacture', 'Horizontal Frequency', 'Vertical Frequency', 'Maximum Pixel Clock', 'Serial Number', '1920 x 1080', '2560*1440', '4096×2160', 'Max. Vertical Size', 'Max. Horizontal Size', 'Monitor Hardware ID'];
//硬盘
const NeedDRIVESInfoArr = ['Drive Model', 'Drive Capacity', 'Drive Capacity [MB]', 'Media Rotation Rate', 'Drive Controller', '[09] Power-On Hours/Cycle Count', 'Drive Remaining Life', 'Drive Serial Number','Drive Firmware Revision', 'Host Controller','Device Health','Lifetime Power-On Resets','Power-on Hours','Power Cycles','Power On Hours','[09] Power-on Hours/Cycle Count','[0C] Power Cycle Count','Device Type'];
//声卡
const NeedSOUNDInfoArr = ['Audio Adapter', 'High Definition Audio Codec', 'Driver Manufacturer', 'Driver Version', 'Driver Date'];
//网卡
const NeedNETWORKInfoArr = ['Network Card', 'Vendor Description', 'MAC Address', 'Maximum Link Speed', 'Driver Manufacturer', 'Driver Version', 'Driver Date'];

const NeedSMBInfoArr = ['Device Name', 'Manufacturer Name','Serial Number','Chemistry', 'Designed Capacity', 'Full Charged Capacity', 'Wear Level', 'Cycle Count', 'Power Status','Current Capacity','Current Voltage'];

const NeedBUSInfoArr = ['Device Name', 'Original Device Name','Driver Manufacturer', 'Driver Description','Driver Provider','Driver Version', 'Driver Date', 'DeviceInstanceId', 'Hardware ID'];


//////获取JSON
function getJSONData(jsonObject, ItemName, defaultvalue) {
  if (jsonObject && jsonObject.hasOwnProperty(ItemName)) {
      return jsonObject[ItemName];
  }
  return defaultvalue;
}

function GetJSONData2(jsonObject, namearray, defaultvalue) {
  if (jsonObject && namearray && namearray.length) {
    let _object = jsonObject;
    try {
      for (let index = 0; index < namearray.length; index++) {
        const szName = namearray[index];
        if (_object && _object.hasOwnProperty(szName)) {
          _object = _object[szName];
        } else {
          _object = null;
          break;
        }
      }
      if (_object) {
        return _object;
      }
      } catch (error) {}
  }
  return defaultvalue;
}

function SetDefaultValue(jsonObject,ItemName,defaultvalue) {
  if (jsonObject && !jsonObject.hasOwnProperty(ItemName)) {
    jsonObject[ItemName] = defaultvalue;
  }
}

function ConvertToObject(jsonObj) {
  const retData = {};
    if (jsonObj) {
      $.each(jsonObj, function (Key, Value) {
        let szEntry = getJSONData(Value,"Entry","");
        let szDescription = getJSONData(Value,"Description","");
         szEntry = RemoveAllSpace(szEntry);
        retData[szEntry] = szDescription;
      });
    }
    return retData;
}

function ConvertToObjectWithFilter(jsonObj,filterArray) {
  const retData = {};
  if (jsonObj) {
    $.each(jsonObj, function (Key, Value) {
      let szEntry = getJSONData(Value,"Entry","");
      let szDescription = getJSONData(Value,"Description","");
      if ($.inArray(szEntry, filterArray) !== -1) {
        szEntry = RemoveAllSpace(szEntry);
        retData[szEntry] = szDescription;
      }
    });
  }
  return retData;
}



//XML解析
async function HWInfoXMLPageDisplayProcess (jsonObj) {
  window.localStorage.removeItem('CPU_PE_Status')
  COMPUTERValue['ScanTime'] = jsonObj.COMPUTER?.ScanTime || '';
  //获取显示器
  try {
    let DisplayCard = await getDisplay()
    if (DisplayCard) {
      $.each(DisplayCard['Element'], function (K, V) {
        if (JSON.stringify(V) !== '{}') {
          HWinfoResolutionArr.push(V);
          DisplayCardInfoArr.push(V);
        }
      });
    }
  } catch (error) {
    HWinfoResolutionStr = '[{"CurrentDisplayFrequency":-1,"MonitorModal":"","PelsHeight":1080,"PelsWidth":1920},{"CurrentDisplayFrequency":-1,"MonitorModal":"","PelsHeight":1080,"PelsWidth":1920}]';
  }

  //获取系统信息
  let GPPSystemInfo = { "Cards": [{ "IpAddress": "", "IpMask": "", "GatewayIpAddress": "", "CardMAC": "" }], "InstallDate": 0, "SystemRunDate": 0, "CurrentUserName": "" }
  let isCall = false
  try {
    try
    {
      GPPSystemInfo = await gamepp.hardware.getGPPSystemInfo.promise();
      isCall = true
    }
    catch{
      GPPSystemInfo = { "Cards": [{ "IpAddress": "", "IpMask": "", "GatewayIpAddress": "", "CardMAC": "" }], "InstallDate": 0, "SystemRunDate": 0, "CurrentUserName": "" };
    }

    try
    {
      if(!isCall)
      {
        GPPSystemInfo = await mythcool.hardware.getGPPSystemInfo.promise();
      }
    }
    catch{}
  } catch (error) {
    GPPSystemInfo = { "Cards": [{ "IpAddress": "", "IpMask": "", "GatewayIpAddress": "", "CardMAC": "" }], "InstallDate": 0, "SystemRunDate": 0, "CurrentUserName": "" };
  }
  let CardsArr = [];
  for (let i = 0; i < (GPPSystemInfo.Cards).length; i++) {if (GPPSystemInfo.Cards[i]['IpAddress'] !== "") {CardsArr.push(GPPSystemInfo.Cards[i])}}

  COMPUTERValue['ComputerUserName'] = GPPSystemInfo.CurrentUserName
  COMPUTERValue['ComputerInstallDate'] = GPPSystemInfo.InstallDate
  COMPUTERValue['ComputerSystemRunDate'] = GPPSystemInfo.SystemRunDate
  COMPUTERValue['ComputerCards'] = CardsArr

  if (HWinfoResolutionArr.length !== 0) {
    const w = HWinfoResolutionArr[0]['PelsWidth'] || '1920'
    const h = HWinfoResolutionArr[0]['PelsHeight'] || '1080'
    const RefreshFrequency = HWinfoResolutionArr[0]['CurrentDisplayFrequency'] || '-1'
    COMPUTER['Resolutions'] = w + '*' + h;
    COMPUTER['RefreshFrequency'] = RefreshFrequency;
  } else {
    COMPUTER['Resolutions'] = '1920' + '*' + '1080';
    COMPUTER['RefreshFrequency'] = '-1';
  }

  //获取主硬盘
  try {
    MainDiskSerialNumberArr = await getDisKInfo()
  } catch (e) {
    MainDiskSerialNumberArr = { "SerialNumber": "", "VendorId": "", "ProductId": "" };
  }

  if (MainDiskSerialNumberArr === null || MainDiskSerialNumberArr === 'null') {
    MainDiskSerialNumberArr = { "SerialNumber": "", "VendorId": "", "ProductId": "" };
  }
  if (!jsonObj.COMPUTER) jsonObj.COMPUTER = {};
  $.each(jsonObj.COMPUTER, function (PropertyK, PropertyV) {
    console.warn('硬件信息原型：',jsonObj);
    switch (PropertyK) {
      case 'Property':
        ProcessComputerBrandName(PropertyV);
        break;
      case 'SubNodes':
        $.each(PropertyV, function (PropertyChildK, PropertyChildV) {
          switch (PropertyChildK) {
            case 'CPU':
              ProcessCPU(PropertyChildV);
              break;
            case 'MOBO':
              ProcessMOBO(PropertyChildV);
              break;
            case 'VIDEO':
              ProcessVIDEO(PropertyChildV);
              break;
            case 'MEMORY':
              ProcessMEMORY(PropertyChildV, jsonObj);
              break;
            case 'MONITOR':
              ProcessMONITOR(PropertyChildV);
              break;
            case 'DRIVES':
              ProcessDRIVES(PropertyChildV);
              break;
            case 'SOUND':
              ProcessSOUND(PropertyChildV);
              break;
            case 'NETWORK':
              ProcessNETWORK(PropertyChildV);
              break;
            case 'SMB':
              ProcessSMB(PropertyChildV);
              break;
            case 'PORTS':
              // ProcessPORTS(PropertyChildV);
              break;
            case 'BUS':
                ProcessBUS(PropertyChildV);
                break;
          }
        });
        break;
    }
  });
  await WEB_SaveHardWareInfo();//上传硬件数据
}

function ProcessComputerBrandName(PropertyV) {
    if (PropertyV) {
        $.each(PropertyV, function (PropertyChildK, PropertyChildV) {
            if (PropertyChildV.Entry && PropertyChildV.Entry !== 'Computer Brand Name') {
                COMPUTERValue[RemoveAllSpace(PropertyChildV.Entry)] = PropertyChildV.Description;
            }
        });
    }
}

// obj = {
//   ['Number of CPU Cores','(\b\d+ x Performance\b)(?:\s+|\b)(\b\d+ x Efficient\b)']
//   ["",]
// }


 //处理器解析
 function ProcessCPU(PropertyChildV) {
  console.log('处理器解析解析新版');
  const Property = getJSONData(PropertyChildV, 'Property', [])
  CPUValue = ConvertToObject(Property)
  CPUPropertyValue['Property'] = CPUValue;
  const CPUSubNodeArr = [];
  let SubNode = getJSONData(PropertyChildV, 'SubNode', [])
   console.log(SubNode)
  if (SubNode && SubNode.length > 0) {
      $.each(SubNode, function (PropertyK, PropertyV) {
          const _Property = getJSONData(PropertyV, 'Property', null)
          let CPUKSubNodeObj = ConvertToObjectWithFilter(_Property, NeedCPUInfoArr)
          $.each(_Property, function (PropertyK1, PropertyV1) {
              const Entry = getJSONData(PropertyV, 'Entry', '')
              console.log(Entry)
              if (Entry === "Number of CPU Cores" || Entry === "Number of Logical CPUs") {
                  console.log(Entry)
                  console.log(PropertyV.Description)
                  if ((PropertyV.Description).includes('Performance') || (PropertyV.Description).includes('Efficient')) {
                      //大小核同时存在
                      if ((PropertyV.Description).includes(',')) {
                          let matches = (PropertyV.Description).match(/\d+/g);
                          if (matches !== null) {
                              CPUSubNodeValue[RemoveAllSpace(Entry)] = matches.reduce((accumulator, currentValue) => accumulator + parseInt(currentValue), 0);
                          }

                          let PropertyDesArr = (PropertyV.Description).split(',')
                          CPUSubNodeValue[RemoveAllSpace(Entry) + 'Performance'] = Number(RegExGetNum(PropertyDesArr[0]));
                          CPUSubNodeValue[RemoveAllSpace(Entry) + 'Efficient'] = Number(RegExGetNum((PropertyDesArr[1])));
                          if (PropertyDesArr[2]) {
                              CPUSubNodeValue[RemoveAllSpace(Entry) + 'LowPowerEfficient'] = Number(RegExGetNum(PropertyDesArr[2]));
                          } else {
                              CPUSubNodeValue[RemoveAllSpace(Entry) + 'LowPowerEfficient'] = 0
                          }
                          window.localStorage.setItem('CPU_PE_Status','PE')
                      } else {
                          CPUSubNodeValue[RemoveAllSpace(Entry)] = Number(removeNonDigits(PropertyV.Description));
                          if ((PropertyV.Description).includes('Performance')) {
                              CPUSubNodeValue[RemoveAllSpace(Entry) + 'Performance'] = Number(removeNonDigits(PropertyV.Description));
                              CPUSubNodeValue[RemoveAllSpace(Entry) + 'Efficient'] = 0;
                          } else if ((PropertyV.Description).includes('Efficient')) {
                              CPUSubNodeValue[RemoveAllSpace(Entry) + 'Performance'] = 0;
                              CPUSubNodeValue[RemoveAllSpace(Entry) + 'Efficient'] = Number(removeNonDigits(PropertyV.Description));
                          }
                      }
                  } else {
                      CPUSubNodeValue[RemoveAllSpace(Entry) + 'Performance'] = 0;
                      CPUSubNodeValue[RemoveAllSpace(Entry) + 'Efficient'] = 0;
                  }
              }
              if (PropertyV1.Entry === 'L1 Cache' || PropertyV1.Entry === 'L1 Cache (P-Cores)') {
                  if (PropertyV1.Description.includes('MBytes')) {
                      ByteNnitL1 = 1;
                  } else {
                      ByteNnitL1 = 1024;
                  }
                  if (PropertyV1.Description.includes('x')) {
                      L1CacheInstruction = RegExGetNum(PropertyV1.Description.split(',')[0].split('x')[0]) * RegExGetNum(PropertyV1.Description.split(',')[0].split('x')[1]);
                      L1CacheData = RegExGetNum(PropertyV1.Description.split(',')[1].split('x')[0]) * RegExGetNum(PropertyV1.Description.split(',')[1].split('x')[1]);
                  } else {
                      L1CacheInstruction = RegExGetNum(PropertyV1.Description.split(',')[0]);
                      L1CacheData = RegExGetNum(PropertyV1.Description.split(',')[1]);
                  }
                  L1Cache = Number((L1CacheInstruction + L1CacheData) / ByteNnitL1);
              }
              if (PropertyV1.Entry === 'L2 Cache'|| PropertyV1.Entry === 'L2 Cache (P-Cores)') {
                  if (PropertyV1.Description.includes('MBytes')) {
                      ByteNnitL2 = 1;
                  } else {
                      ByteNnitL2 = 1024;
                  }
                  if (PropertyV1.Description.includes('x')) {
                      L2Cache0 = RegExGetNum(PropertyV1.Description.split('x')[0]);
                      L2Cache1 = RegExGetNum(PropertyV1.Description.split('x')[1]);
                  } else {
                      L2Cache0 = RegExGetNum(PropertyV1.Description);
                      L2Cache1 = 1;
                  }
                  L2Cache = Number((L2Cache0 * L2Cache1) / ByteNnitL2);
              }
              if (PropertyV1.Entry === 'L3 Cache') {
                  if (PropertyV1.Description.includes('x')) {
                      const L3Cache0 = RegExGetNum(PropertyV1.Description.split('x')[0]);
                      const L3Cache1 = RegExGetNum(PropertyV1.Description.split('x')[1]);
                      L3Cache = Number(L3Cache0 * L3Cache1);
                  } else {
                      L3Cache = Number(RegExGetNum((PropertyV1.Description)));
                  }
              }
          });
          if (L3Cache === 0) {
              CPUKSubNodeObj['L3Cache'] = 'Integrated: 0 MBytes';
          }
          CPUKSubNodeObj['ProcessorName'] = PropertyV.NodeName;
          CPUKSubNodeObj['CACHE'] = L1Cache + L2Cache + L3Cache;
          CPUSubNodeArr.push(CPUKSubNodeObj);
      });
      CPUPropertyValue['SubNode'] = CPUSubNodeArr;
  } else {
      const _Property = getJSONData(SubNode, 'Property', null)
      CPUSubNodeValue = ConvertToObjectWithFilter(_Property, NeedCPUInfoArr)
      $.each(_Property, function (PropertyK, PropertyV) {
          const Entry = getJSONData(PropertyV, 'Entry', '')
          if (Entry === "Number of CPU Cores" || Entry === "Number of Logical CPUs") {
              if ((PropertyV.Description).includes('Performance') || (PropertyV.Description).includes('Efficient')) {
                  if ((PropertyV.Description).includes(',')) {
                      let matches = (PropertyV.Description).match(/\d+/g);
                      if (matches !== null) {
                          CPUSubNodeValue[RemoveAllSpace(Entry)] = matches.reduce((accumulator, currentValue) => accumulator + parseInt(currentValue), 0);
                      }

                      let PropertyDesArr = (PropertyV.Description).split(',')
                      CPUSubNodeValue[RemoveAllSpace(Entry) + 'Performance'] = Number(RegExGetNum(PropertyDesArr[0]));
                      CPUSubNodeValue[RemoveAllSpace(Entry) + 'Efficient'] = Number(RegExGetNum((PropertyDesArr[1])));
                      if (PropertyDesArr[2]) {
                          CPUSubNodeValue[RemoveAllSpace(Entry) + 'LowPowerEfficient'] = Number(RegExGetNum(PropertyDesArr[2]));
                      } else {
                          CPUSubNodeValue[RemoveAllSpace(Entry) + 'LowPowerEfficient'] = 0
                      }
                      window.localStorage.setItem('CPU_PE_Status','PE')
                  } else {
                      CPUSubNodeValue[RemoveAllSpace(Entry)] = Number(removeNonDigits(PropertyV.Description));
                      if (PropertyV.Description.includes('x')) {
                          CPUSubNodeValue[RemoveAllSpace(Entry)] = Number(removeNonDigits(PropertyV.Description));
                      }
                      if ((PropertyV.Description).includes('Performance')) {
                          CPUSubNodeValue[RemoveAllSpace(Entry) + 'Performance'] = Number(removeNonDigits(PropertyV.Description));
                          if (PropertyV.Description.includes('x')) {
                              CPUSubNodeValue[RemoveAllSpace(Entry) + 'Performance'] = Number(removeNonDigits(PropertyV.Description));
                          }
                          CPUSubNodeValue[RemoveAllSpace(Entry) + 'Efficient'] = 0;
                      } else if ((PropertyV.Description).includes('Efficient')) {
                          CPUSubNodeValue[RemoveAllSpace(Entry) + 'Performance'] = 0;
                          CPUSubNodeValue[RemoveAllSpace(Entry) + 'Efficient'] = Number(removeNonDigits(PropertyV.Description));
                          if (PropertyV.Description.includes('x')) {
                              CPUSubNodeValue[RemoveAllSpace(Entry) + 'Efficient'] = Number(removeNonDigits(PropertyV.Description));
                          }
                      }
                  }
              } else {
                  CPUSubNodeValue[RemoveAllSpace(Entry) + 'Performance'] = 0;
                  CPUSubNodeValue[RemoveAllSpace(Entry) + 'Efficient'] = 0;
              }
              if ((PropertyV.Description).includes('Classic') || (PropertyV.Description).includes('Compact')) {
                  //大小核同时存在
                  if ((PropertyV.Description).includes(',')) {
                      let matches = (PropertyV.Description).match(/\d+/g);
                      if (matches !== null) {
                          CPUSubNodeValue[RemoveAllSpace(Entry)] = matches.reduce((accumulator, currentValue) => accumulator + parseInt(currentValue), 0);
                      }
                      let PropertyDesArr = (PropertyV.Description).split(',')
                      CPUSubNodeValue[RemoveAllSpace(Entry) + 'Classic'] = Number(RegExGetNum(PropertyDesArr[0]))
                      CPUSubNodeValue[RemoveAllSpace(Entry) + 'Compact'] = Number(RegExGetNum((PropertyDesArr[1])))

                  } else {
                      CPUSubNodeValue[RemoveAllSpace(Entry)] = Number(removeNonDigits(PropertyV.Description));
                      if ((PropertyV.Description).includes('Classic')) {
                          CPUSubNodeValue[RemoveAllSpace(Entry) + 'Classic'] = Number(removeNonDigits(PropertyV.Description));
                          CPUSubNodeValue[RemoveAllSpace(Entry) + 'Compact'] = 0;
                      } else if ((PropertyV.Description).includes('Efficient')) {
                          CPUSubNodeValue[RemoveAllSpace(Entry) + 'Classic'] = 0;
                          CPUSubNodeValue[RemoveAllSpace(Entry) + 'Compact'] = Number(removeNonDigits(PropertyV.Description));
                      }
                  }
              }
          }
          if (Entry === 'L1 Cache' || Entry === "L1 Cache (P-Cores)") {
              if (PropertyV.Description.includes('MBytes')) {
                  ByteNnitL1 = 1;
              } else {
                  ByteNnitL1 = 1024;
              }
              if (PropertyV.Description.includes('x')) {
                  L1CacheInstruction = RegExGetNum(PropertyV.Description.split(',')[0].split('x')[0]) * RegExGetNum(PropertyV.Description.split(',')[0].split('x')[1]);
                  L1CacheData = RegExGetNum(PropertyV.Description.split(',')[1].split('x')[0]) * RegExGetNum(PropertyV.Description.split(',')[1].split('x')[1]);
              } else {
                  L1CacheInstruction = RegExGetNum(PropertyV.Description.split(',')[0]);
                  L1CacheData = RegExGetNum(PropertyV.Description.split(',')[1]);
              }
              L1Cache = Number((L1CacheInstruction + L1CacheData) / ByteNnitL1);
          }
          if (Entry === 'L2 Cache' || Entry === 'L2 Cache (P-Cores)') {
              if (PropertyV.Description.includes('MBytes')) {
                  ByteNnitL2 = 1;
              } else {
                  ByteNnitL2 = 1024;
              }
              if (PropertyV.Description.includes('x')) {
                  L2Cache0 = RegExGetNum(PropertyV.Description.split('x')[0]);
                  L2Cache1 = RegExGetNum(PropertyV.Description.split('x')[1]);
              } else {
                  L2Cache0 = RegExGetNum(PropertyV.Description);
                  L2Cache1 = 1;
              }
              L2Cache = Number((L2Cache0 * L2Cache1) / ByteNnitL2);
          }
          if (Entry === 'L3 Cache') {
              if (PropertyV.Description.includes('x')) {
                  const L3Cache0 = RegExGetNum(PropertyV.Description.split('x')[0]);
                  const L3Cache1 = RegExGetNum(PropertyV.Description.split('x')[1]);
                  L3Cache = Number(L3Cache0 * L3Cache1);
              } else {
                  L3Cache = Number(RegExGetNum((PropertyV.Description)));
              }
          }
      });

      if (L3Cache === 0) {
          CPUSubNodeValue['L3Cache'] = 'Integrated: 0 MBytes';
      }
      let ReleaseDate=''
      let name = (SubNode.NodeName).replace('Intel ', '').replace('AMD ', '').replace(' 64-Cores', '')
      for (let i = 0; i < release_cpu.length; i++) {if (release_cpu[i]['n'] === name) {ReleaseDate = FormatEnglishDate(release_cpu[i].d)}}
      CPUSubNodeValue['ProcessorName'] = SubNode.NodeName;
      CPUSubNodeValue['ReleaseDate'] = ReleaseDate;
      CPUSubNodeValue['CACHE'] = L1Cache + L2Cache + L3Cache;
      CPUSubNodeArr.push(CPUSubNodeValue);
      CPUPropertyValue['SubNode'] = CPUSubNodeArr;
  }
}
// function ProcessCPU(PropertyChildV) {
//   console.warn('ProcessCPU Begins！！！');
//   const Property = getJSONData(PropertyChildV, 'Property', [])
//   CPUValue = ConvertToObject(Property)
//   CPUPropertyValue['Property'] = CPUValue;
//   const CPUSubNodeArr = [];
//   const SubNode = getJSONData(PropertyChildV, 'SubNode', [])
//   if (SubNode && SubNode.length > 0) { //多处理器
//     for(let PropertyV of SubNode)
//     {
//       const _Property = getJSONData(PropertyV, 'Property', null)
//       let CPUKSubNodeObj = ConvertToObjectWithFilter(_Property, NeedCPUInfoArr)

//       for(let PropertyV1 in _Property)
//       {
//         const Entry = getJSONData(PropertyV, 'Entry', '')
//         if (Entry === "Number of CPU Cores" || Entry === "Number of Logical CPUs") {
//           ProcessPECore(Entry,_Property,PropertyV)
//         }

//         if (PropertyV1.Entry === 'L1 Cache' || PropertyV1.Entry === 'L1 Cache (P-Cores)') {
//           L1CacheProcess(_Property,PropertyV1)
//         }

//         if (PropertyV1.Entry === 'L2 Cache'|| PropertyV1.Entry === 'L2 Cache (P-Cores)') {
//           L2CacheProcess(_Property,PropertyV1)
//         }

//         if (PropertyV1.Entry === 'L3 Cache') {
//           L3CacheProcess(_Property,PropertyV1)
//         }
//       }

//         if (L3Cache === 0) {
//           CPUKSubNodeObj['L3Cache'] = 'Integrated: 0 MBytes';
//         }
//         CPUKSubNodeObj['ProcessorName'] = PropertyV.NodeName;
//         CPUKSubNodeObj['CACHE'] = L1Cache + L2Cache + L3Cache;
//         CPUSubNodeArr.push(CPUKSubNodeObj);
//     }
//     CPUPropertyValue['SubNode'] = CPUSubNodeArr;
//   } else {
//     const _Property = getJSONData(SubNode, 'Property', null)
//     CPUSubNodeValue = ConvertToObjectWithFilter(_Property, NeedCPUInfoArr)
//       for(let PropertyV of _Property)
//       {
//         if(PropertyV['Entry'] == 'Number of CPU Cores'){
//         }
//         const Entry = getJSONData(PropertyV, 'Entry', '')
//         if (Entry === "Number of CPU Cores" || Entry === "Number of Logical CPUs") {
//           ProcessPECore(Entry,_Property,PropertyV)
//         }
//         if (Entry === 'L1 Cache' || Entry === "L1 Cache (P-Cores)") {
//           L1CacheProcess(_Property,PropertyV)
//         }
//         if (Entry === 'L2 Cache' || Entry === 'L2 Cache (P-Cores)') {
//           L2CacheProcess(_Property,PropertyV)
//         }
//         if (Entry === 'L3 Cache') {
//           L3CacheProcess(_Property,PropertyV)
//         }
//       }
//     if (L3Cache === 0) {
//       CPUSubNodeValue['L3Cache'] = 'Integrated: 0 MBytes';
//     }
//     let ReleaseDate=''
//     let name = (SubNode.NodeName).replace('Intel ', '').replace('AMD ', '').replace(' 64-Cores', '')
//     for (let i = 0; i < release_cpu.length; i++) {if (release_cpu[i]['n'] === name) {ReleaseDate = FormatEnglishDate(release_cpu[i].d)}}
//     CPUSubNodeValue['ProcessorName'] = SubNode.NodeName;
//     CPUSubNodeValue['ReleaseDate'] = ReleaseDate;
//     CPUSubNodeValue['CACHE'] = L1Cache + L2Cache + L3Cache;
//     CPUSubNodeArr.push(CPUSubNodeValue);
//     CPUPropertyValue['SubNode'] = CPUSubNodeArr;
//     }
// }

 //主板解析
function ProcessMOBO(PropertyChildV) {
  const Property = getJSONData(PropertyChildV, 'Property', null)
  MOBOValue = ConvertToObjectWithFilter(Property, NeedMOBOInfoArr)
  MOBOPropertyValue['Property'] = MOBOValue;
  const SubNode = getJSONData(PropertyChildV, 'SubNode', null)
  Object.keys(SubNode).forEach( function (SubNodeK) {
    const SubNodeV = SubNode[SubNodeK]
    const NodeName = getJSONData(SubNodeV, 'NodeName', '')
    if (NodeName === 'SMBIOS DMI') {  //仅处理SMBIOS DMI的情况
      const _SubNode = getJSONData(SubNodeV, 'SubNode', null)
      if (_SubNode) {  //主板只处理下列4个字段
        Object.keys(_SubNode).forEach(function (SubNodeK1) {
          const SubNodeV1 = _SubNode[SubNodeK1]
          const _NodeName = getJSONData(SubNodeV1, 'NodeName', '')
          if (_NodeName === 'BIOS') {
            const _Property = getJSONData(SubNodeV1, 'Property', null)
            MOBOBIOSValue = ConvertToObjectWithFilter(_Property, NeedMOBOInfoArr)
          } else if (_NodeName === 'Mainboard') {
            const _Property = getJSONData(SubNodeV1, 'Property', null)
            MOBOMainboardValue = ConvertToObjectWithFilter(_Property, NeedMOBOInfoArr)
          } else if (_NodeName === 'System') {
            const _Property = getJSONData(SubNodeV1, 'Property', null)
            MOBOSystemValue = ConvertToObjectWithFilter(_Property, NeedMOBOInfoArr)
          } else if (_NodeName === 'System Enclosure') {
            const _Property = getJSONData(SubNodeV1, 'Property', null)
            MOBOMainboardValue1 = ConvertToObjectWithFilter(_Property, NeedMOBOInfoArr)
            MOBOMainboardValue = { ...MOBOMainboardValue, ...MOBOMainboardValue1 }
          }
        })
      }
    }
  });
  //设置默认值
  SetDefaultValue(MOBOBIOSValue, 'BIOSReleaseDate', '')
  SetDefaultValue(MOBOBIOSValue, 'BIOSSize', '')
  SetDefaultValue(MOBOBIOSValue, 'BIOSVendor', '')
  SetDefaultValue(MOBOBIOSValue, 'BIOSVersion', '')
  SetDefaultValue(MOBOMainboardValue, 'MainboardManufacturer', 'Unknown')
  SetDefaultValue(MOBOMainboardValue, 'MainboardName', 'Unknown')

  MOBOPropertyValue['BIOS'] = MOBOBIOSValue;
  MOBOPropertyValue['Mainboard'] = MOBOMainboardValue;
  if (Object.keys(MOBOMainboardValue).length === 0) {
      MOBOMainboardValue['MainboardManufacturer'] = 'Unknown Unknown';
      MOBOMainboardValue['MainboardName'] = 'Unknown Unknown';
      MOBOPropertyValue['Mainboard'] = MOBOMainboardValue;
  }
  MOBOPropertyValue['System'] = MOBOSystemValue;
  if (Object.keys(MOBOSystemValue).length === 0) {
      MOBOPropertyValue['UUID'] = '';
      MOBOPropertyValue['System'] = MOBOSystemValue;
  }
}

function ProcessVIDEO(PropertyChildV) {
  const VIDEOSubNodeArr = []
  const SubNode = getJSONData(PropertyChildV, 'SubNode', null)
  if (SubNode && SubNode.length > 0)  //多显卡
  {
    Object.values(SubNode).forEach(function (PropertyV) {
      const Property = getJSONData(PropertyV, 'Property', null)
      const NodeName = getJSONData(PropertyV, 'NodeName', null)
      //先筛选GPU可用字段
      let GPUSubNodeObj = ConvertToObjectWithFilter(Property, NeedGPUInfoArr)

      // const NeedGPUInfoArr = ['Video Chipset', 'Video Chipset Codename', 'Video Memory', 'Video Card', 'Video Bus', 'Graphics Processor Clock',
      //                         'Graphics Memory Clock', 'Graphics Memory Bus Width', 'Number Of ROPs', 'Number Of Unified Shaders','Number Of ALUs (cores)',
      //                         'Number Of TMUs (Texture Mapping Units)', 'ASIC Quality', 'Driver Manufacturer', 'Driver Version', 'Driver Date', 'NVIDIA SLI Status',
      //                         'Hardware ID','ASIC Manufacturer','ASIC Serial Number','ASIC Quality'];

      if (NodeName === 'Intel NPU') { //核显
        GPUSubNodeObj.VideoBus = 'Integrated'
        GPUSubNodeObj.VideoChipset = 'Intel NPU'
        GPUSubNodeObj.VideoMemory = ''
        GPUSubNodeObj.VideoCard = 'Intel NPU'
      } else if (GPUSubNodeObj.VideoBus.includes('PCIe v2.0') || GPUSubNodeObj.VideoBus === 'Integrated') {
        GPUSubNodeObj.VideoBusInterface = GPUSubNodeObj.VideoBus
        GPUSubNodeObj.VideoBus = 'Integrated'
      }
      // else if (GPUSubNodeObj.VideoBus === 'Integrated') {
      //   GPUSubNodeObj.VideoBusInterface = GPUSubNodeObj.VideoBus
      // }
      let ReleaseDate = ''
      let name = (GPUSubNodeObj.VideoChipset).replace('Intel ', '').replace('NVIDIA ', '').replace('AMD ', '')
      for (let i = 0; i < release_gpu.length; i++) {if (release_gpu[i]['n'] === name) {ReleaseDate = FormatEnglishDate(release_gpu[i].d)}}
      if (GPUSubNodeObj.VideoMemory) {
        let VideoMemory = (GPUSubNodeObj.VideoMemory).split('MBytes');
        if (!StrIsNumber(VideoMemory[0])) {VideoMemory[0] = 0}
        GPUSubNodeObj.VideoMemoryNumber = Number(VideoMemory[0])
      }
      GPUSubNodeObj.ReleaseDate = ReleaseDate
      VIDEOSubNodeArr.push(GPUSubNodeObj);
    });
    GPUPropertyValue['SubNode'] = VIDEOSubNodeArr;
  }
  else //单显卡
  {
    const Property = getJSONData(SubNode, 'Property', null)
    GPUSubNodeValue = ConvertToObjectWithFilter(Property, NeedGPUInfoArr)
    SetDefaultValue(GPUSubNodeValue, 'NumberOfUnifiedShaders', '')
    SetDefaultValue(GPUSubNodeValue, 'NumberOfROPs', '')
    SetDefaultValue(GPUSubNodeValue, 'GraphicsMemoryBusWidth', '')

    if (!GPUSubNodeValue['VideoMemory'] || GPUSubNodeValue['VideoMemory'] === 'Unknown') {
      GPUSubNodeValue['VideoMemory'] = '0 Unknown'
    }
    if (!GPUSubNodeValue['VideoChipset'] || GPUSubNodeValue['VideoChipset'] === '') {
      GPUSubNodeValue['VideoChipset'] = 'Unknown Unknown'
    }

    let ReleaseDate = ''
    let name = (GPUSubNodeValue?.VideoChipset).replace('Intel ', '').replace('NVIDIA ', '').replace('AMD ', '')
    //获取显卡发行日期
    for (let i = 0; i < release_gpu.length; i++) {if (release_gpu[i]['n'] === name) {ReleaseDate = FormatEnglishDate(release_gpu[i].d)}}
    SetDefaultValue(GPUSubNodeValue, 'ReleaseDate', ReleaseDate)

    //获取显存
    if (GPUSubNodeValue.VideoMemory)
    {
      let VideoMemory = (GPUSubNodeValue.VideoMemory).split('MBytes');
      if (!StrIsNumber(VideoMemory[0])) {VideoMemory[0] = 0}
      GPUSubNodeValue.VideoMemoryNumber = Number(VideoMemory[0])
    }
    VIDEOSubNodeArr.push(GPUSubNodeValue);
    GPUPropertyValue['SubNode'] = VIDEOSubNodeArr;
  }
}

///
function ProcessMEMORY(PropertyChildV, jsonObj) {
  //Get Property Value Info
  const MemoryProperty = getJSONData(PropertyChildV,"Property");
  const _CacheMemoryValue = ConvertToObjectWithFilter(MemoryProperty,NeedMEMORYInfoArr);
  SetDefaultValue(_CacheMemoryValue,"TotalMemorySize","0 GBytes");
  SetDefaultValue(_CacheMemoryValue,"CurrentMemoryClock","0 MHz");
  MEMORYValue = _CacheMemoryValue;
  MEMORYPropertyValue['Property'] = _CacheMemoryValue;
  const SubNode = getJSONData(PropertyChildV,"SubNode",null);
  if (SubNode) {
    if (SubNode.length > 0) {
      const MEMORYSubNodeArr = [];
      Object.keys(SubNode).forEach(function (PropertyK) {
        const PropertyV = SubNode[PropertyK];
        const NodeName = getJSONData(PropertyV,"NodeName",null);
        if (NodeName && !NodeName.includes('<Empty>')) {
          const _Property = getJSONData(PropertyV,"Property");
          const MEMORYSubNodeObj = ConvertToObjectWithFilter(_Property,NeedMEMORYInfoArr);
          SetDefaultValue(MEMORYSubNodeObj,"ModuleNumber",PropertyK);
          SetDefaultValue(MEMORYSubNodeObj,"ModuleSize",0);
          SetDefaultValue(MEMORYSubNodeObj,"MemoryType","null");
          SetDefaultValue(MEMORYSubNodeObj,"MemorySpeed","");
          SetDefaultValue(MEMORYSubNodeObj,"ModuleManufacturer","");
          SetDefaultValue(MEMORYSubNodeObj,"ModulePartNumber","");
          SetDefaultValue(MEMORYSubNodeObj,"ModuleSerialNumber","");
          SetDefaultValue(MEMORYSubNodeObj,"ModuleManufacturingDate","");
          SetDefaultValue(MEMORYSubNodeObj,"SDRAMManufacturer","");
          MEMORYSubNodeArr.push(MEMORYSubNodeObj);
        }
      });
      MEMORYPropertyValue['SubNode'] = MEMORYSubNodeArr;
    } else {
      const Property = getJSONData(SubNode,"Property",null);
      if (Property) {
        const MEMORYSubNodeArr = [];
        const _Property = getJSONData(SubNode,"Property");
        const _CacheMemoryValue = ConvertToObjectWithFilter(_Property,NeedMEMORYInfoArr);
        MEMORYSubNodeArr.push(_CacheMemoryValue);
        MEMORYPropertyValue['SubNode'] = MEMORYSubNodeArr;
      }
    }
  } else {
    let NotMemKIndex = 12;
    let Node = null;
    try {
      Node = jsonObj['COMPUTER']['SubNodes']['MOBO']['SubNode'][1]['SubNode']
    } catch (error) {}
    if (Node) {
      Object.keys(Node).forEach(function (NotMemK) {
        const NotMemV = Node[NotMemK]
        if (NotMemV['NodeName'] === 'Memory Devices') {
          NotMemKIndex = NotMemK;
        }
      })
    }

function isStringEqual(v1,v2)
{
  if(v1 && v2)
  {
    return v1.toLowerCase() === v2.toLowerCase()
  }
   return false
}

function getDescrptionByEntry(PropertyArr,szEntry)
{
  for (const v of PropertyArr) {
    if (isStringEqual(v.Entry,szEntry) ) {
      console.warn('v', v.Entry);
      return v.Description;
    }
  }
  return ''
}
    let NotMemoryArr = null;
    try {
        NotMemoryArr =  jsonObj['COMPUTER']['SubNodes']['MOBO']['SubNode'][1]['SubNode'][NotMemKIndex];
    } catch (error) {}
    if (NotMemoryArr) {
      const szNodeName = getJSONData(NotMemoryArr,"NodeName","");
      if (szNodeName === 'Memory Devices') {
        ModuleNumber = -1;
        const MEMORYSubNodeArr = []
        Object.keys(NotMemoryArr['SubNode']).forEach(function (NotMemoryK) {
          const NotMemoryV = NotMemoryArr['SubNode'][NotMemoryK]
          if (NotMemoryV['NodeName'] === 'Memory Device') {
          const PropertyArr = NotMemoryV['Property']
          const szDeviceSize =  getDescrptionByEntry(PropertyArr,"Device Size")

            ModuleNumber += 1;
            if (szDeviceSize !== '0 MBytes') {
              let MEMORYSubNodeObj = {};
              //Type
              const MemoryType = getDescrptionByEntry(PropertyArr,"Device Type")
              //MemorySpeed
              const MemorySpeed = RegExGetNum(getDescrptionByEntry(PropertyArr,"Memory Speed"));
              MEMORYSubNodeObj['MemorySpeed'] = (MemorySpeed / 2) + ' MHz (' + MemoryType.replace('LP', '') + '-' + MemorySpeed + ' / PC4-23900)';

              MEMORYSubNodeObj['ModuleNumber'] = ModuleNumber;
              MEMORYSubNodeObj['ModuleSize'] = (RegExGetNum(szDeviceSize) / 1024) + ' GBytes';
              MEMORYSubNodeObj['MemoryType'] = MemoryType + ' SDRAM';
              //Manufacturer
              MEMORYSubNodeObj['ModuleManufacturer'] = getDescrptionByEntry(PropertyArr,"Manufacturer")

              //Part Number
              MEMORYSubNodeObj['ModulePartNumber'] = getDescrptionByEntry(PropertyArr,"Part Number")

              //Serial Number
              MEMORYSubNodeObj['ModuleSerialNumber'] = getDescrptionByEntry(PropertyArr,"Serial Number")

              MEMORYSubNodeObj['ModuleManufacturingDate'] = 'N/A';
              MEMORYSubNodeObj['SDRAMManufacturer'] = '';
              //Asset Tag
              MEMORYSubNodeObj['AssetTag'] = getDescrptionByEntry(PropertyArr,"Asset Tag")

              MEMORYSubNodeArr.push(MEMORYSubNodeObj);
            }
          }
        });
        MEMORYPropertyValue['SubNode'] = MEMORYSubNodeArr;
      }
    }
  }
  //if no data
  const _SubNode = getJSONData(MEMORYPropertyValue,"SubNode",null);
  if (!(_SubNode && _SubNode.length>0)) {
    MEMORYPropertyValue['SubNode'] = [{ "ModuleNumber": "", "ModuleSize": "", "MemoryType": "", "MemorySpeed": "", "ModuleManufacturer": "", "ModulePartNumber": "", "ModuleSerialNumber": "", "ModuleManufacturingDate": "", "SDRAMManufacturer": "", "ModuleNominalVoltage(VDD)": "" }]
  }
}
function jqInArray (element, array) {
  return array.indexOf(element);
}
function ProcessMONITOR (PropertyChildV) {
  const SubNode = getJSONData(PropertyChildV, 'SubNode', null)
  const Property = getJSONData(SubNode, 'Property', [])
  if (SubNode) {
    const MONITORSubNodeArr = [];
    if (SubNode.length > 0) {
      //处理显示器数组顺序
      Object.keys(SubNode).forEach(function (PropertyK) {
        const PropertyV = SubNode[PropertyK]
        const _Property = getJSONData(PropertyV, 'Property', null)
        const NodeName = getJSONData(PropertyV, 'NodeName', '')
        const MONITORSubNodeObj = ConvertToObjectWithFilter(_Property, NeedMONITORInfoArr);
        MONITORSubNodeObj['Resolutions'] = '1920*1080'
        MONITORSubNodeObj['RefreshFrequency'] = '-1'
        if (_Property) {
          Object.keys(_Property).forEach(function (PropertyK1) {
            const PropertyV1 = _Property[PropertyK1]
            const NewHWinfoResolutionArr = [];
            const Entry = getJSONData(PropertyV1, 'Entry', '')
            if (Entry === 'Monitor Name (Manuf)') {
              Object.keys(HWinfoResolutionArr).forEach(function (MonitorK) {
                const MonitorV = HWinfoResolutionArr[MonitorK]
                const MonitorModal = getJSONData(MonitorV, 'MonitorModal', '')
                const Description = getJSONData(PropertyV1, 'Description', '')
                if (RemoveAllSpace(MonitorModal) === RemoveAllSpace(Description) && NewHWinfoResolutionArr.length === 0) {
                  if (MonitorK !== PropertyK) {
                    for (let i = HWinfoResolutionArr.length - 1; i >= 0; i--) {
                      NewHWinfoResolutionArr.push(HWinfoResolutionArr[i])
                    }
                  }
                }
              })
            }
            if (jqInArray(PropertyV1.Entry, NeedMONITORInfoArr) !== -1 && HWinfoResolutionArr[PropertyK]) {
              MONITORSubNodeObj['Resolutions'] = HWinfoResolutionArr[PropertyK]['PelsWidth'] + '*' + HWinfoResolutionArr[PropertyK]['PelsHeight'];
              MONITORSubNodeObj['RefreshFrequency'] = HWinfoResolutionArr[PropertyK]['CurrentDisplayFrequency'];
            }
            if (NewHWinfoResolutionArr.length !== 0) {
              HWinfoResolutionArr = NewHWinfoResolutionArr;
            }
          });
          if (!MONITORSubNodeObj['MonitorName']) {MONITORSubNodeObj['MonitorName'] = NodeName}
          MONITORSubNodeArr.push(MONITORSubNodeObj);
        }
      });
      MONITORPropertyValue['SubNode'] = MONITORSubNodeArr;
    } else {
      if (Property) {
        MONITORValue = ConvertToObjectWithFilter(Property, NeedMONITORInfoArr)
      } else {
        MONITORValue['MonitorName'] = ''
      }
      if (HWinfoResolutionArr.length !== 0) {
        MONITORValue['Resolutions'] = HWinfoResolutionArr[0]['PelsWidth'] + '*' + HWinfoResolutionArr[0]['PelsHeight'];
        MONITORValue['RefreshFrequency'] = HWinfoResolutionArr[0]['CurrentDisplayFrequency'];
        MONITORValue['MonitorName'] = ''
      } else {
        MONITORValue['Resolutions'] = '1920' + '*' + '1080';
        MONITORValue['RefreshFrequency'] = '-1';
        MONITORValue['MonitorName'] = ''
      }
      if (!MONITORValue['MonitorName']) {
        const NodeName = getJSONData(SubNode, 'NodeName', '')
        MONITORValue['MonitorName'] = NodeName
      }
      MONITORSubNodeArr.push(MONITORValue);
      MONITORPropertyValue['SubNode'] = MONITORSubNodeArr;
    }


    for (let i = 0; i < MONITORPropertyValue['SubNode'].length; i++) {
      let HwData = MONITORPropertyValue['SubNode'][i]
      for (let j = 0; j < DisplayCardInfoArr.length; j++) {
        let GPPData = DisplayCardInfoArr[j]
        if (HwData['MonitorName(Manuf)']?.toLowerCase() === GPPData['MonitorModal']?.toLowerCase()) {
          MONITORPropertyValue['SubNode'][i]['Resolutions'] = GPPData['PelsWidth'] + '*' + GPPData['PelsHeight']
          MONITORPropertyValue['SubNode'][i]['RefreshFrequency'] = GPPData['CurrentDisplayFrequency']
          MONITORPropertyValue['SubNode'][i]['LowestDisplayFrequency'] = GPPData['LowestDisplayFrequency']
          MONITORPropertyValue['SubNode'][i]['HighestDisplayFrequency'] = GPPData['HighestDisplayFrequency']
          break;
        } else if (HwData['MonitorHardwareID']?.toLowerCase() === GPPData['MonitorSerial']?.toLowerCase()) {
          MONITORPropertyValue['SubNode'][i]['Resolutions'] = GPPData['PelsWidth'] + '*' + GPPData['PelsHeight']
          MONITORPropertyValue['SubNode'][i]['RefreshFrequency'] = GPPData['CurrentDisplayFrequency']
          MONITORPropertyValue['SubNode'][i]['LowestDisplayFrequency'] = GPPData['LowestDisplayFrequency']
          MONITORPropertyValue['SubNode'][i]['HighestDisplayFrequency'] = GPPData['HighestDisplayFrequency']
        }
      }
    }

    //找出主屏幕 放最前面
    const primaryScreenData = DisplayCardInfoArr.find(item => item.IsPrimaryScreen === 1);
    MONITORPropertyValue.SubNode.forEach(item => {
      // 默认值为0，表示非主屏幕
      item.IsPrimaryScreen = item.IsPrimaryScreen || 0;
      if (item["MonitorName(Manuf)"] === primaryScreenData?.MonitorModal || item["MonitorHardwareID"] === primaryScreenData?.MonitorSerial) {
        item.IsPrimaryScreen = 1;
      }
    });
    MONITORPropertyValue.SubNode.sort((a, b) => {return b.IsPrimaryScreen - a.IsPrimaryScreen;});
  }
}

function ProcessDRIVES(PropertyChildV) {
    const DRIVESIsSubNodeArrBool = Array.isArray(PropertyChildV.SubNode);
    let DRIVESSubNodeArr = [];
    const SubNode = getJSONData(PropertyChildV, 'SubNode', null)
    if (DRIVESIsSubNodeArrBool) {
      if (SubNode) {
        Object.values(SubNode).forEach(function (Val) {
          const NodeName = getJSONData(Val, 'NodeName', '')
          if (NodeName === 'ATA Drives' || NodeName === 'NVMe Drives') {
            const DRIVESIsArrBool = Array.isArray(Val.SubNode);
            const _SubNode = getJSONData(Val, 'SubNode', null)
            const Property = getJSONData(_SubNode, 'Property', [])
            if (DRIVESIsArrBool) {
                if (_SubNode && _SubNode.length > 0) {
                  Object.values(_SubNode).forEach(function (PropertyV) {
                    const Property = getJSONData(PropertyV, 'Property', null)
                    if (Property) {
                      const _NodeName = getJSONData(PropertyV, 'NodeName', '')
                      if (_NodeName.includes('RAID Array')) {
                        const SubNodeV = getJSONData(PropertyV, 'SubNode', null)
                        const IsArrBool = Array.isArray(SubNodeV);
                        if (IsArrBool && SubNodeV) {
                          Object.values(SubNodeV).forEach(function (RAIDV1) {
                            const PropertyV1 = getJSONData(RAIDV1, 'Property', null)
                            const DRIVESSubNodeObj = ConvertToObjectWithFilter(PropertyV1, NeedDRIVESInfoArr)
                            Object.values(PropertyV1).forEach(function (PropertyK, PropertyV) {
                              if (PropertyV.Entry === 'Drive Model') {
                                DRIVESSubNodeObj['DriveModel'] = 'RAID ' + PropertyV.Description;
                              }
                            })
                            DRIVESSubNodeArr.push(DRIVESSubNodeObj);
                          });
                        } else {
                          if (SubNodeV) {
                            const _PropertyV = getJSONData(SubNodeV, 'Property', null)
                            const DRIVESSubNodeObj1 = ConvertToObjectWithFilter(_PropertyV, NeedDRIVESInfoArr)
                            Object.values(_PropertyV).forEach(function (PropertyV1) {
                              if (PropertyV1.Entry === 'Drive Model') {
                                DRIVESSubNodeObj1['DriveModel'] = 'RAID ' + PropertyV1.Description;
                              }
                            });
                            DRIVESSubNodeArr.push(DRIVESSubNodeObj1);
                          }
                        }
                      } else {
                        const DRIVESSubNodeObj3 = ConvertToObjectWithFilter(Property, NeedDRIVESInfoArr)
                        DRIVESSubNodeObj3['Interface'] = NodeName
                        DRIVESSubNodeArr.push(DRIVESSubNodeObj3);
                      }
                    }
                  });
                } else {
                  if (_SubNode && Property) {
                    DRIVESSubNodeValue = ConvertToObjectWithFilter(Property, NeedDRIVESInfoArr)
                    DRIVESSubNodeArr.push(DRIVESSubNodeValue);
                  }
                }
            } else {
              if (_SubNode && Property) {
                  const DRIVESSubNodeObj = ConvertToObjectWithFilter(Property, NeedDRIVESInfoArr);
                  DRIVESSubNodeArr.push(DRIVESSubNodeObj);
              }
            }
          }
        })
      }
    } else if (SubNode.SubNode) {
      const DRIVESIsArrBool = Array.isArray(SubNode.SubNode);
      if (DRIVESIsArrBool) {
          const _SubNode = getJSONData(SubNode, 'SubNode', null)
          if (_SubNode && _SubNode.length > 0) {
            Object.values(_SubNode).forEach(function (PropertyV) {
              const Property = getJSONData(PropertyV, 'Property', null)
              if (Property) {
                const DRIVESSubNodeObj = ConvertToObjectWithFilter(Property, NeedDRIVESInfoArr);
                DRIVESSubNodeArr.push(DRIVESSubNodeObj);
              }
            });
          } else {
            const Property = getJSONData(SubNode, 'Property', null)
            if (Property) {
              DRIVESSubNodeValue = ConvertToObjectWithFilter(Property, NeedDRIVESInfoArr)
              DRIVESSubNodeArr.push(DRIVESSubNodeValue);
            }
          }
      } else {
          const _SubNode = getJSONData(SubNode, 'SubNode', null)
          const Property = getJSONData(_SubNode, 'Property', null)
          if (Property) {
            DRIVESSubNodeValue = ConvertToObjectWithFilter(Property, NeedDRIVESInfoArr)
            DRIVESSubNodeArr.push(DRIVESSubNodeValue);
          }
      }

    }
    let MainDiskSerialNum = 0;
  Object.keys(DRIVESSubNodeArr).forEach(function (DRIVESSubNodeArrUpK) {
      const DRIVESSubNodeArrUpV = DRIVESSubNodeArr[DRIVESSubNodeArrUpK]
      const SerialNumber = getJSONData(MainDiskSerialNumberArr, 'SerialNumber', '')
      const ProductId = getJSONData(MainDiskSerialNumberArr, 'ProductId', '')
      const DriveSerialNumber = getJSONData(DRIVESSubNodeArrUpV, 'DriveSerialNumber', '')
      const DriveModel = getJSONData(DRIVESSubNodeArrUpV, 'DriveModel', '')
        if (SerialNumber && ProductId) {
            if (DriveSerialNumber === SerialNumber || DriveModel === ProductId) {
              MainDiskSerialNum = DRIVESSubNodeArrUpK;
            }
        }
    });
    if (MainDiskSerialNum !== 0) {
      ArrToFirst(DRIVESSubNodeArr, MainDiskSerialNum);
    }
    DRIVESPropertyValue['SubNode'] = DRIVESSubNodeArr;
}

function ProcessSOUND(PropertyChildV) {
  const SubNode = getJSONData(PropertyChildV, 'SubNode', null)
  const NodeName = getJSONData(SubNode, 'NodeName', '')
  if (NodeName !== 'Unknown or none') {
    const SOUNDSubNodeArr = [];
    const IsArrBool = Array.isArray(SubNode);
    if (IsArrBool) {
      Object.keys(SubNode).forEach((PropertyK)=>{
        const PropertyV = SubNode[PropertyK]
        const Property = getJSONData(PropertyV, 'Property', null)
        const SOUNDSubNodeObj = ConvertToObjectWithFilter(Property, NeedSOUNDInfoArr)
        SOUNDSubNodeArr.push(SOUNDSubNodeObj);
      })
      SOUNDPropertyValue['SubNode'] = SOUNDSubNodeArr;
    } else {
      const Property = getJSONData(SubNode, 'Property', null)
      SOUNDSubNodeValue = ConvertToObjectWithFilter(Property, NeedSOUNDInfoArr)
      SOUNDSubNodeArr.push(SOUNDSubNodeValue);
      SOUNDPropertyValue['SubNode'] = SOUNDSubNodeArr;
    }
  }
}

function ProcessNETWORK(PropertyChildV) {
  const NETWORKSubNodeArr = [];
  const SubNode = getJSONData(PropertyChildV, 'SubNode', null);
  const Property = getJSONData(SubNode, 'Property', null);
  if (SubNode && SubNode.length > 0) {
    for (let PropertyV in SubNode) {
      if (SubNode.hasOwnProperty(PropertyV)) {
        const _Property = getJSONData(SubNode[PropertyV], 'Property', null);
        const NETWORKSubNodeObj = ConvertToObjectWithFilter(_Property, NeedNETWORKInfoArr);
        NETWORKSubNodeArr.push(NETWORKSubNodeObj);
      }
    }
  }else {
    const NETWORKSubNodeValue = ConvertToObjectWithFilter(Property, NeedNETWORKInfoArr);
    NETWORKSubNodeArr.push(NETWORKSubNodeValue);
  }
  NETWORKPropertyValue['SubNode'] = NETWORKSubNodeArr;
}


function ProcessSMB(PropertyChildV) {
  const SMBSubNodeArr = [];
  const SubNode = getJSONData(PropertyChildV, 'SubNode', null);
  const Property = getJSONData(SubNode, 'Property', null);
  if (SubNode && SubNode.length > 0) {
    Object.keys(SubNode).forEach(function (PropertyV, PropertyK) {
      const _Property = getJSONData(PropertyV, 'Property', null);
      const SMBSubNodeObj = ConvertToObjectWithFilter(_Property, NeedSMBInfoArr);
      SMBSubNodeArr.push(SMBSubNodeObj);
    });
  } else {
    SMBSubNodeValue = ConvertToObjectWithFilter(Property, NeedSMBInfoArr);
    SMBSubNodeArr.push(SMBSubNodeValue);
  }
  SMBPropertyValue['SubNode'] = SMBSubNodeArr;
}

function ProcessBUS(PropertyChildV) {
  try {
    let BUSSubNodeArr = [];
    const SubNode = getJSONData(PropertyChildV, 'SubNode', null);
    if (SubNode.SubNode) {
      const DRIVESIsArrBool = Array.isArray(SubNode.SubNode);
      if (DRIVESIsArrBool) {
        SubNode.SubNode.forEach((PropertyV, PropertyK) => {
          const __SubNode = getJSONData(PropertyV, 'SubNode', null);
          if (__SubNode && __SubNode.SubNode) {
            for (let __PropertyK in __SubNode.SubNode) {
              if (__SubNode.SubNode.hasOwnProperty(__PropertyK)) {
                const __PropertyV = __SubNode.SubNode[__PropertyK];
                const ___Property = getJSONData(__PropertyV, 'Property', null);
                const ___NodeName = getJSONData(__PropertyV, 'NodeName', null);
                if (___NodeName && (___NodeName.includes("AMD") && (___NodeName.includes("NPU") || ___NodeName.includes("IPU")))) {
                  console.log('NPU位置:' + PropertyK + '--' + __PropertyK);
                  if (___Property) {
                    const DRIVESSubNodeObj = ConvertToObjectWithFilter(___Property, NeedBUSInfoArr);
                    BUSSubNodeArr.push(DRIVESSubNodeObj);
                  }
                }
              }
            }
          }
        });
      }
    }
    if (BUSSubNodeArr.length === 0) {
       BUSPropertyValue = {};
    } else {
       BUSPropertyValue = { 'SubNode': BUSSubNodeArr };
    }
  } catch (errors) {
    console.log(errors);
    BUSPropertyValue = {};
  }
}

function ProcessPORTS (PropertyChildV) {
  const PORTSSubNodeArr = [];
  const SubNode = getJSONData(PropertyChildV, 'SubNode', null)
  const NodeName = getJSONData(SubNode, 'NodeName', '')
  const Property = getJSONData(SubNode, 'Property', null)
  const SOUNDSubNodeArr = [];
  const IsArrBool = Array.isArray(SubNode);
  if (IsArrBool) {
    Object.keys(SubNode).forEach((PropertyK) => {
      const PropertyV = SubNode[PropertyK]
      const NodeName = getJSONData(PropertyV, 'NodeName', '')
      if (NodeName === 'USB') {
        Object.values(PropertyV.SubNode.SubNode.SubNode).forEach((PropertyV1) => {
          const NodeName1 = getJSONData(PropertyV1, 'NodeName', null)
          if (!NodeName1.includes('No Device Connected')) {
            PORTSSubNodeArr.push(PropertyV1);
          }
        })
      }
    })
    USBPropertyValue['SubNode'] = PORTSSubNodeArr;
  } else {
    USBSubNodeValue = ConvertToObjectWithFilter(Property, NeedNETWORKInfoArr);
    PORTSSubNodeArr.push(USBSubNodeValue);
    USBPropertyValue['SubNode'] = PORTSSubNodeArr;
  }
}

//XML解析保存JSON
async function WEB_SaveHardWareInfo () {
    COMPUTER['COMPUTER'] = COMPUTERValue;
    COMPUTER['CPU'] = CPUPropertyValue;
    COMPUTER['MOBO'] = MOBOPropertyValue;
    COMPUTER['GPU'] = GPUPropertyValue;
    COMPUTER['MEMORY'] = MEMORYPropertyValue;
    COMPUTER['MONITOR'] = MONITORPropertyValue;
    COMPUTER['DRIVES'] = DRIVESPropertyValue;
    COMPUTER['SOUND'] = SOUNDPropertyValue;
    COMPUTER['NETWORK'] = NETWORKPropertyValue;
    COMPUTER['SMB'] = SMBPropertyValue;
    COMPUTER['BUS'] = BUSPropertyValue;
    COMPUTER['NPU'] = BUSPropertyValue;
    COMPUTER['USB'] = USBPropertyValue;
    HWInfoObj.push(COMPUTER);
    console.log(HWInfoObj[0]);
    try {await gamepp.hardware.setBaseJsonInfo.promise(HWInfoObj[0]);} catch {await mythcool.hardware.setBaseJsonInfo.promise(HWInfoObj[0]);}
    let gpu_index = 0
    try {
        gpu_index = await FilterGPUIndexFromMemoryNumber(HWInfoObj[0])
    } catch (err) {
        console.error('FilterGPUIndexFromMemoryNumber', err)
    }

    let gpu_index_manual = window.localStorage.getItem('gpu_index_manual');
    if (gpu_index_manual === null) {
      window.localStorage.setItem('gpu_index', gpu_index)
    }
    //上传硬件信息
    let isUploadHD = window.localStorage.getItem('isUploadHD')
    if (!isUploadHD) {
      try {
        await upload_hd_info_startup(HWInfoObj[0], gpu_index)
      } catch(err) {
        console.error('启动上传硬件信息失败',err)
      }
    }

    //保存硬件信息列表(查询传感器信息使用)
  let disk_name = ''
  if (HWInfoObj[0] && HWInfoObj[0]['DRIVES'] && HWInfoObj[0]['DRIVES']['SubNode'] && Array.isArray(HWInfoObj[0]['DRIVES']['SubNode'])) {
    disk_name = HWInfoObj[0]['DRIVES']['SubNode'].map(node => node['DriveModel']).filter(Boolean);
  }

  let memory_name = '';
  if (Array.isArray(HWInfoObj[0].MEMORY?.SubNode) && HWInfoObj[0].MEMORY.SubNode[0].MemorySpeed) {
    const regex = /\((.+?)\)/g;
    const data = HWInfoObj[0].MEMORY.SubNode[0];
    const matches = data.MemorySpeed.match(regex);
    const memory_spd = matches ? matches[0].replace('(', '').split('/')[0] : '';
    const speed = Math.floor(data.MemorySpeed.split(' ')[0]);
    const moduleSize = data.ModuleSize.split(' ')[0];
    const manufacturer = data.ModuleManufacturer ? `${data.ModuleManufacturer} ` : '';
    memory_name = `${manufacturer}${memory_spd} (${speed} MHz) ${moduleSize}GB`;
  }

  let cpu_name = ''
  let gpu_name = ''
  let mainboard_name = ''
  if (HWInfoObj[0])
  {
    if (Array.isArray(HWInfoObj[0].CPU?.SubNode) && HWInfoObj[0].CPU.SubNode[0].ProcessorName)
    {
      cpu_name = HWInfoObj[0].CPU.SubNode[0].ProcessorName;
    }
    if (Array.isArray(HWInfoObj[0].GPU?.SubNode) && HWInfoObj[0].GPU.SubNode[0].VideoChipset)
    {
      gpu_name = HWInfoObj[0].GPU.SubNode[0].VideoChipset;
    }
    if (HWInfoObj[0].MOBO?.Mainboard)
    {
      mainboard_name = HWInfoObj[0].MOBO.Mainboard?.MainboardName;
    }
  }
  const data = {
    hw_list: {
      cpu_name      : [cpu_name],
      gpu_name      : [gpu_name],
      mainboard_name: [mainboard_name],
      dram_name     : [memory_name],
      disk_name     : disk_name
    }
  };
  window.localStorage.setItem('hw_list_data', JSON.stringify(data.hw_list));
}

async function upload_hd_info_startup (HdJsonInfoObj,main_gpu) {
  let cpu_base_clock = 0, cpu_turbo_clock = 0;
  if (HdJsonInfoObj['CPU']['SubNode'][0]['CPUHFM(Base)']) {
    cpu_base_clock = Number((HdJsonInfoObj['CPU']['SubNode'][0]['CPUHFM(Base)'].split('='))[0].replace(' MHz', ''));
    cpu_turbo_clock = Number((HdJsonInfoObj['CPU']['SubNode'][0]['CPUTurboMax'].split('='))[0].replace(' MHz', ''));
  } else {
    cpu_base_clock = Number(HdJsonInfoObj['CPU']['SubNode'][0]['OriginalProcessorFrequency[MHz]']);
    cpu_turbo_clock = Number(HdJsonInfoObj['CPU']['SubNode'][0]['OriginalProcessorFrequency[MHz]']);
  }

  let CPUSubNodeClockVol = HdJsonInfoObj['CPU']['SubNode'][0]['CPUCurrent'].split('@');
  let cpu_clock = Number((CPUSubNodeClockVol[0].split('='))[0].replace(' MHz', ''));


  //GPU
  let mem_name = '';
  for (let i = 0; i <HdJsonInfoObj['MEMORY']['SubNode'].length ; i++) {
    mem_name += (HdJsonInfoObj['MEMORY']['SubNode'][i]['ModuleManufacturer']) + ',';
  }

  let currenttiming = null;
  if (HdJsonInfoObj["MEMORY"]["Property"]["CurrentTiming(tCAS-tRCD-tRP-tRAS)"]) {
    currenttiming = HdJsonInfoObj["MEMORY"]['Property']['CurrentTiming(tCAS-tRCD-tRP-tRAS)'].split('-');
  } else {
    currenttiming = [15, 15, 15, 35];
  }

  let timing_cr = null;
  if (HdJsonInfoObj['MEMORY']['Property']['CommandRate']) {
    timing_cr = Number((HdJsonInfoObj['MEMORY']['Property']['CommandRate']).replace(/[^0-9]/ig, ""));
  } else if (HdJsonInfoObj['MEMORY']['Property']['CommandRate(CR)']) {
    timing_cr = Number((HdJsonInfoObj['MEMORY']['Property']['CommandRate(CR)']).replace(/[^0-9]/ig, ""));
  } else {
    timing_cr = 2;
  }

  const regex1 = /\[(.+?)\]/g;
  const VideoBrandArr = ((HdJsonInfoObj['GPU']['SubNode'][main_gpu].VideoCard).match(regex1));
  let GPUSubvendor = ((HdJsonInfoObj['GPU']['SubNode'][main_gpu].VideoCard)).split(' ');
  let VideoBrand = '';
  if (VideoBrandArr) {VideoBrand = (VideoBrandArr[VideoBrandArr.length - 1]).replace(/\[|]/g, '')}
  if (VideoBrand === '') {VideoBrand = GPUSubvendor[0];}


  let driver = [];
  for (let i = 0; i < HdJsonInfoObj['DRIVES']['SubNode'].length; i++) {
    let driver_info = {};
    if (HdJsonInfoObj['DRIVES']['SubNode'][i]['DriveCapacity[MB]']) {
      driver_info['DriveCapacity[MB]'] = HdJsonInfoObj['DRIVES']['SubNode'][i]['DriveCapacity[MB]'];
    }
    if (HdJsonInfoObj['DRIVES']['SubNode'][i]['DriveModel']){
      driver_info['DriveModel']        = HdJsonInfoObj['DRIVES']['SubNode'][i]['DriveModel'];
    }
    driver.push(driver_info);
  }

  let monitor_name = HdJsonInfoObj['MONITOR']['SubNode'][0]['MonitorName'].replace(/\[.*?\]/g, '');
  let monitor_manuf = HdJsonInfoObj['MONITOR']['SubNode'][0]['MonitorName(Manuf)'];

  let monitor_name_manuf = '';
  if (monitor_name !== 'Unknown' || monitor_manuf) {
    if (monitor_name !== 'Unknown') {
      monitor_name_manuf = monitor_name;
    } else {
      monitor_name_manuf = '';
    }
    if (monitor_name) {
      if (monitor_name?.toLowerCase() === monitor_manuf?.toLowerCase()) {
        monitor_name_manuf = monitor_name;
      } else {
        monitor_name_manuf = monitor_name + monitor_manuf;
      }
    } else {
      monitor_name_manuf = monitor_name;
    }
  }

  let upload_obj = {
    // mid                : await gamepp.getMID.promise(),
    // version            : await gamepp.getPlatformVersion.promise(),
    resolutions        : HdJsonInfoObj['Resolutions'],
    refreshfrequency   : HdJsonInfoObj['MONITOR']['SubNode'][0]['RefreshFrequency'],
    mainboardmodule    : HdJsonInfoObj['MOBO']['Property']['MotherboardModel'],
    mainboardvendor    : HdJsonInfoObj['MOBO']['Mainboard']['MainboardManufacturer'],
    system             : HdJsonInfoObj['COMPUTER']['OperatingSystem'],
    monitor            : monitor_name_manuf,
    cpu: {
      "base_clock" : cpu_base_clock,
      "cache"      : HdJsonInfoObj['CPU']['SubNode'][0]['CACHE'],
      "clock"      : cpu_clock,
      "cores"      : Number(HdJsonInfoObj['CPU']['Property']['NumberOfProcessorCores']),
      "name"       : HdJsonInfoObj['CPU']['SubNode'][0]['ProcessorName'],
      "tdp"        : Number((HdJsonInfoObj['CPU']['SubNode'][0]['CPUThermalDesignPower(TDP)']?.match(/\d+(.\d+)?/g))[0]),
      "technology" : Number(HdJsonInfoObj['CPU']['SubNode'][0]['CPUTechnology']?.replace(/[^0-9]/ig, "")),
      "threads"    : Number(HdJsonInfoObj['CPU']['Property']['NumberOfLogicalProcessors']),
      "turbo_clock": cpu_turbo_clock,
      "codename"   : HdJsonInfoObj['CPU']['SubNode'][0]['CPUCodeName']
    },
    gpu: {
      "codename": HdJsonInfoObj['GPU']['SubNode'][main_gpu]['VideoChipsetCodename'],
      "mem_bus" : Number(HdJsonInfoObj['GPU']['SubNode'][main_gpu]['GraphicsMemoryBusWidth']?.replace(/[^0-9]/ig, "")),
      "mem_size": Number($('#GPUAllSize').text() * 1024),
      "name"    : HdJsonInfoObj['GPU']['SubNode'][main_gpu]['VideoChipset'],
      "brand"   : VideoBrand,
      "rops"    : Number(HdJsonInfoObj['GPU']['SubNode'][main_gpu]['NumberOfROPs']),
      "shaders" : Number(HdJsonInfoObj['GPU']['SubNode'][main_gpu]['NumberOfUnifiedShaders']),
      "tmus"    : Number(HdJsonInfoObj['GPU']['SubNode'][main_gpu]['NumberOfTMUs(TextureMappingUnits)']),
      "videobus": (HdJsonInfoObj['GPU']['SubNode'][main_gpu]['VideoBus'])
    },
    driver: { "SubNode": driver },
    mem: {
      "channel"    : Number(HdJsonInfoObj['MEMORY']['Property']['MemoryChannelsActive']),
      "clock"      : Number((Math.ceil(HdJsonInfoObj['MEMORY']['Property']['CurrentMemoryClock']?.split('MHz')[0])) * 2),
      "name"       : mem_name,
      "size"       : Number(HdJsonInfoObj['MEMORY']['Property']['TotalMemorySize[MB]']),
      "timing_cr"  : timing_cr,
      "timing_tCL" : Number(currenttiming[0]),
      "timing_tRCD": Number(currenttiming[1]),
      "timing_tRP" : Number(currenttiming[2]),
      "timing_tRAS": Number(currenttiming[3])
    }
  }
  let isCall = false
  try
  {
    upload_obj['mid'] = await gamepp.getMID.promise(),
    upload_obj['version'] = await gamepp.getPlatformVersion.promise()
    isCall = true
    console.warn('isCal!!!!!!!!!!!!!!!!!!!!!!!!!!!!!');
  }
  catch{}
  try
  {
    if(!isCall)
    {
      upload_obj['mid'] = await mythcool.getMID.promise(),
      upload_obj['version'] = await mythcool.getPlatformVersion.promise()
    }
  }
  catch{}
  console.log(upload_obj);
  let aseKey = "gamepp@!123";
  let message = JSON.stringify(upload_obj);
  let des_upload_obj = encryptByDES(message, aseKey);
  $.ajax({
           type   : "GET",
           url    : 'http://t4.gamepp.com/log/hd.html?info=' + des_upload_obj,
           success: function () {
             console.log('软件启动上传硬件信息成功');
             window.localStorage.setItem('isUploadHD', 1)
           }
         });
}



function encryptByDES (message, key) {
  return CryptoJS.DES.encrypt(message, CryptoJS.enc.Utf8.parse(key), {
    mode   : CryptoJS.mode.ECB,
    padding: CryptoJS.pad.ZeroPadding
  }).toString();
}


async function FilterOptimalGPUIndex (JsonInfo) {
  return new Promise(async function (resolve) {
    let srcIndex = 0
    let Gpu_Count = JsonInfo.GPU.SubNode.length
    if (Gpu_Count === 1) {srcIndex = 0}
    if (Gpu_Count === 2) {
      if (JsonInfo.GPU.SubNode[0]['VideoMemory'] && JsonInfo.GPU.SubNode[0]['VideoMemory']) {
        let VideoMemory0 = (JsonInfo.GPU.SubNode[0]['VideoMemory']).split('MBytes')
        let VideoMemory1 = (JsonInfo.GPU.SubNode[1]['VideoMemory']).split('MBytes')
        if (!StrIsNumber(VideoMemory0[0])) {VideoMemory0[0] = 0}
        if (!StrIsNumber(VideoMemory1[0])) {VideoMemory1[0] = 0}
        if (Number(VideoMemory0[0]) < Number(VideoMemory1[0])) {
          srcIndex = 1
        } else {
          srcIndex = 0
        }
      } else {
        console.log('根据Shaders判断')
        const Pcie_Gpu = []
        for (let j = 0; j < Gpu_Count; j++) {
          if (JsonInfo.GPU.SubNode[j].VideoBus) {
            if (JsonInfo.GPU.SubNode[j].VideoCard.toLowerCase().indexOf('internal') === -1) {
              if (JsonInfo.GPU.SubNode[j].VideoBus.toLowerCase().indexOf('pcie') >= 0) {
                Pcie_Gpu.push(JsonInfo.GPU.SubNode[j])
                srcIndex = j
              }
            }
          }
        }
        if (Pcie_Gpu.length !== 0) {
          Pcie_Gpu.sort(sortGPUShaders)
          for (let j = 0; j < Gpu_Count; j++) {
            if (JsonInfo.GPU.SubNode[j].NumberOfUnifiedShaders) {
              if (JsonInfo.GPU.SubNode[j].NumberOfUnifiedShaders === Pcie_Gpu[0]['NumberOfUnifiedShaders']) {
                Pcie_Gpu.push(JsonInfo.GPU.SubNode[j])
                srcIndex = j
              }
            }
          }
        }
      }
    }
    resolve(srcIndex)
  })
}

async function FilterGPUIndexFromMemoryNumber (JsonInfo) {
  return new Promise(async function (resolve) {
    let srcIndex = 0
    if (JsonInfo.GPU && JsonInfo.GPU.SubNode) {
      let Gpu_Count = JsonInfo.GPU.SubNode.length
      if (Gpu_Count === 1) { resolve(0)}
      for (let i = 0; i < JsonInfo.GPU.SubNode.length; i++) {
        const currentNode = JsonInfo.GPU.SubNode[i];
        const currentVideoMemoryNumber = currentNode.VideoMemoryNumber;
        if (currentVideoMemoryNumber > (JsonInfo.GPU.SubNode[srcIndex].VideoMemoryNumber || 0)) {
          srcIndex = i;
        }
      }
    }else{
      resolve(0)
    }
    resolve(srcIndex)
  })
}

//////是否数字
function StrIsNumber (data) {
  return !isNaN(Number(data, 10));
}

function string2XML(xmlString) {
    const parser = new DOMParser();
    return parser.parseFromString(xmlString, "text/xml");
}

// 去除所有空格
function RemoveAllSpace(str) {
    return str.replace(/\s+/g, "").replace('-cores','-Cores');
}

//正则获取数字
function RegExGetNum(str) {
    return str.replace(/[^0-9]/ig, "");
}

function ArrToFirst(fieldData, index) {
    if (index != 0) {
        fieldData[index] = fieldData.splice(0, 1, fieldData[index])[0];
    }
}

/**
 * example:Apr 30th 2020
 */
function FormatEnglishDate (date) {
  if(!date.includes(','))return date
  if (date.includes(',')){date=date.replaceAll(',','')}
  let days = [
    "1st", "2nd", "3rd", "4th", "5th", "6th", "7th", "8th", "9th", "10th",
    "11th", "12th", "13th", "14th", "15th", "16th", "17th", "18th", "19th",
    "20th", "21st", "22nd", "23rd", "24th", "25th", "26th", "27th", "28th",
    "29th", "30th", "31st"
  ];
  let months = [
    "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
  ];
  let dates = date.split(" ");
  let year = dates[2];

  let month = months.indexOf(dates[0]) + 1;

  let day = days.indexOf(dates[1]) + 1;

  if (month.toString().length < 2) {
    month = "0" + month;
  }
  if (day.toString().length < 2) {
    day = "0" + day;
  }
  return year + "-" + month + "-" + day;
}


function ProcessPECore(Entry,_Property,PropertyV)
{
  if ((PropertyV.Description).includes('Performance') || (PropertyV.Description).includes('Efficient')) {
    //大小核同时存在
    if ((PropertyV.Description).includes(',')) {
      let matches = (PropertyV.Description).match(/\d+/g);
      if (matches !== null) {
        CPUSubNodeValue[RemoveAllSpace(Entry)] = matches.reduce((accumulator, currentValue) => accumulator + parseInt(currentValue), 0);
      }
      let PropertyDesArr = (PropertyV.Description).split(',')
      CPUSubNodeValue[RemoveAllSpace(Entry) + 'Performance'] = Number(RegExGetNum(PropertyDesArr[0]));
      CPUSubNodeValue[RemoveAllSpace(Entry) + 'Efficient'] = Number(RegExGetNum((PropertyDesArr[1])));
      if (PropertyDesArr[2]) {
        CPUSubNodeValue[RemoveAllSpace(Entry) + 'LowPowerEfficient'] = Number(RegExGetNum(PropertyDesArr[2]));
      } else {
        CPUSubNodeValue[RemoveAllSpace(Entry) + 'LowPowerEfficient'] = 0
      }
      window.localStorage.setItem('CPU_PE_Status','PE')
    } else {
      CPUSubNodeValue[RemoveAllSpace(Entry)] = Number((PropertyV.Description).replace('Performance','').replace('Efficient',''));
      if ((PropertyV.Description).includes('Performance')) {
        CPUSubNodeValue[RemoveAllSpace(Entry) + 'Performance'] = Number(((PropertyV.Description).replace('Performance', '')));
        CPUSubNodeValue[RemoveAllSpace(Entry) + 'Efficient'] = 0;
      } else if ((PropertyV.Description).includes('Efficient')) {
        CPUSubNodeValue[RemoveAllSpace(Entry) + 'Performance'] = 0;
        CPUSubNodeValue[RemoveAllSpace(Entry) + 'Efficient'] = Number(((PropertyV.Description).replace('Efficient', '')));
      }
    }
  } else {
    CPUSubNodeValue[RemoveAllSpace(Entry) + 'Performance'] = 0;
    CPUSubNodeValue[RemoveAllSpace(Entry) + 'Efficient'] = 0;
  }
}

function L1CacheProcess(_Property,PropertyV1,CPUSubNodeValue)
{
  if (PropertyV1.Description.includes('MBytes')) {
    ByteNnitL1 = 1;
  } else {
    ByteNnitL1 = 1024;
  }
  if (PropertyV1.Description.includes('x')) {
    L1CacheInstruction = RegExGetNum(PropertyV1.Description.split(',')[0].split('x')[0]) * RegExGetNum(PropertyV1.Description.split(',')[0].split('x')[1]);
    L1CacheData = RegExGetNum(PropertyV1.Description.split(',')[1].split('x')[0]) * RegExGetNum(PropertyV1.Description.split(',')[1].split('x')[1]);
  } else {
    L1CacheInstruction = RegExGetNum(PropertyV1.Description.split(',')[0]);
    L1CacheData = RegExGetNum(PropertyV1.Description.split(',')[1]);
  }
  L1Cache = Number((L1CacheInstruction + L1CacheData) / ByteNnitL1);
}

function L2CacheProcess(_Property,PropertyV1,CPUSubNodeValue)
{
  if (PropertyV1.Description.includes('MBytes')) {
    ByteNnitL2 = 1;
  } else {
    ByteNnitL2 = 1024;
  }
  if (PropertyV1.Description.includes('x')) {
    L2Cache0 = RegExGetNum(PropertyV1.Description.split('x')[0]);
    L2Cache1 = RegExGetNum(PropertyV1.Description.split('x')[1]);
  } else {
    L2Cache0 = RegExGetNum(PropertyV1.Description);
    L2Cache1 = 1;
  }
  L2Cache = Number((L2Cache0 * L2Cache1) / ByteNnitL2);
}

function L3CacheProcess(_Property,PropertyV1,CPUSubNodeValue)
{
  if (PropertyV1.Description.includes('x')) {
    const L3Cache0 = RegExGetNum(PropertyV1.Description.split('x')[0]);
    const L3Cache1 = RegExGetNum(PropertyV1.Description.split('x')[1]);
    L3Cache = Number(L3Cache0 * L3Cache1);
  } else {
    L3Cache = Number(RegExGetNum((PropertyV1.Description)));
  }
}

//Mythcool
async function getDisplay()
{
  let isCall = false
  let DisplayCard
  try
  {
    DisplayCard = await gamepp.hardware.getDisplayCardInfo.promise();
    isCall = true
  }
  catch
  {

  }
  if(!isCall)
  {
    try
    {
      DisplayCard = await mythcool.hardware.getDisplayCardInfo.promise();
    }
    catch
    {

    }
  }
  return DisplayCard
}

async function getDisKInfo()
{
  let isCall = false
  let current
  try
  {
    current = await gamepp.hardware.getMainDiskInfo.promise();
    isCall = true
  }
  catch
  {

  }
  if(!isCall)
  {
    try
    {
      current =  await mythcool.hardware.getMainDiskInfo.promise();
    }
    catch
    {

    }
  }
  return current
}
function removeNonDigits(str) {
  return str.replace(/\D/g, '');
}
