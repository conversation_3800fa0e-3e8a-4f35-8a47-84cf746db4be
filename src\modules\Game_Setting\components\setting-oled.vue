<template>
  <!--OLED防烧屏保护-->
  <div id="OLED"></div>
  <el-collapse-item :title="$t('Setting.OLEDscreen')" name="6">
    <div class="setting-item">
      <section>
        <div class="flex-row-center">
          <el-checkbox v-model="store.state.oled.enable" @change="setOled"></el-checkbox>
          <span style="margin: 0 5px">{{$t('Setting.MoveInterval_m1')}}</span>
          <el-input-number
              :controls="false"
              :step="1"
              style="width: 55px;"
              :min="1"
              :max="999"
              v-model="store.state.oled.time"
              @change="setOled"
          ></el-input-number>
          <span style="margin: 0 5px">{{$t('Setting.MoveInterval_m2')}}</span>
        </div>
      </section>
    </div>
  </el-collapse-item>
</template>

<script setup lang="ts">
import {gameppBaseSetting} from '@/modules/Game_Home/stores/index'
import {useScroll} from '../hooks/useScroll'
import {GPP_SendStatics} from "@/uitls/sendstatics"
useScroll('OLED');
const store = gameppBaseSetting();

function setOled() {
  if (store.state.oled.enable) {
    GPP_SendStatics(100760)
  }else{
    GPP_SendStatics(100761)
  }
  store.actions.setOledState(store.state.oled.enable, store.state.oled.time)
}
</script>

<style scoped lang="scss">
#OLED {
  margin-top: 20px;
}
</style>
