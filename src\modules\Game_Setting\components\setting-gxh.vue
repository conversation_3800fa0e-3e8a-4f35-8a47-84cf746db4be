<template>
  <!--个性化设置-->
  <div id="gxh"></div>
  <el-collapse-item :title="$t('Setting.personalizedSetting')" name="2">
    <template #title>
      <div class="gxh_title">
        <!--个性化设置-->
        <span style="font-size: .12rem;color:var(--font-color)">{{$t('Setting.personalizedSetting')}}</span>
        <span @click.stop="DefaultColor" class="restoreDefaultBtn"><span class="iconfont icon-switch"></span> <span>{{$t('Setting.restoreDefault')}}</span></span>
      </div>
    </template>
    <div class="setting-item">
      <section class="left-box">
        <p class="setting-item-title">{{$t('Setting.personalizedSetting')}}</p>
        <div>
          <el-radio-group v-model="store.colorOrImg" class="ml-4" style="flex-wrap: nowrap;">
            <el-radio :value="1" size="small"><span style="margin-top: 2px;display: block;">{{$t('Setting.picture')}}/{{$t('Setting.video')}}</span></el-radio>
            <el-radio :value="2" size="small"><span style="margin-top: 2px;display: block;">{{$t('Setting.color')}}</span></el-radio>
          </el-radio-group>
          <div class="dir" v-show="store.colorOrImg === 1">
            <div class="path">
              {{ store.gameppImg || store.gameppVideo }}
            </div>
            <el-button type="primary" size="small" @click="chooseFile">{{$t('Setting.browse')}}</el-button>
            <span class="clear-icon" v-show="store.gameppImg || store.gameppVideo" @click="clearImgOrVideo">{{$t('Setting.clear')}}</span>
          </div>
            <text v-show="store.colorOrImg === 1" style="white-space: nowrap;">{{$t('Setting.mp4VideoOrPNGImagesCanBeUploaded')}}</text>
            <div class="setImgObjectFit" v-show="store.colorOrImg === 1">
                <el-radio-group v-model="store.imgObjectFit" class="ml-4" style="flex-wrap: nowrap;">
                    <el-radio value="cover" size="small"><span style="margin-top: 2px;display: block;">{{$t('DesktopMonitoring.Fill')}}</span></el-radio>
                    <el-radio value="fill" size="small"><span style="margin-top: 2px;display: block;">{{$t('DesktopMonitoring.Stretch')}}</span></el-radio>
                    <el-radio value="contain" size="small"><span style="margin-top: 2px;display: block;">{{$t('DesktopMonitoring.Adapt')}}</span></el-radio>
                </el-radio-group>
            </div>
        </div>
      </section>
      <section class="right-box">
          <div class="bgOpacity" v-show="store.colorOrImg === 1">
              <p class="setting-item-title">{{$t('Setting.transparency')}}</p>
              <div style="width: 330px">
                  <el-slider  v-model="store.bgOpacity" :min="0" :max="1" :step="0.01" show-input :show-input-controls="false" />
                  <!--              <el-slider v-show="store.colorOrImg === 2" v-model="store.bgOpacity2" :min="0" :max="1" :step="0.01" show-input :show-input-controls="false" />-->
              </div>
          </div>
          <p class="setting-item-title" style="margin-top: 10px;">{{$t('Setting.backgroundColor')}}</p>
          <div class="flex-row-center">
              <el-input v-model="store.gameppBgColor" @change="(e)=>{verifyColor(e,'#22232E','gameppBgColor')}" style="width: 180px;"></el-input>
              <el-color-picker @change="store.colorOrImg = 2" v-model="store.gameppBgColor" popper-class="dark_color_picker"></el-color-picker>
          </div>
      </section>
    </div>
  </el-collapse-item>
</template>

<script setup lang="ts">
import {gameppBaseSetting} from '@/modules/Game_Home/stores/index'
import {ref, reactive, watch, computed, onBeforeMount, onUnmounted, onMounted,} from 'vue';
import {useScroll} from '../hooks/useScroll'
import { GPP_SendStatics } from '@/uitls/sendstatics';

useScroll('gxh');
const store = gameppBaseSetting();

const imgsrc = ref('')
const videosrc = ref('')
const bgInputShow = ref('')

let fontList = ref<Array<any>>([])

onMounted(async () => {
  let arr = new Set()
  try {
    const availableFonts = await window.queryLocalFonts();
    for (const fontData of availableFonts) {
      if (fontData.family.toLowerCase().startsWith('st')){
        // st开头字体不要
        continue
      }
      arr.add(fontData.family) // 去重
    }
    fontList.value = Array.from(arr)
  } catch (err) {
    console.error('获取字体失败', err);
  }

  setTimeout(()=>{
    console.log(store.bgOpacity)
    bgInputShow.value = bgOpacityFormatter(store.bgOpacity)
  },500)
})

let _timer: string | number | NodeJS.Timeout | null | undefined = null;
watch(() => store.colorOrImg, (newVal) => {
  _timer && clearTimeout(_timer)
  _timer = setTimeout(() => {
    if (newVal === 1 ){
      GPP_SendStatics(100722)
    }else if (newVal === 2 ){
      GPP_SendStatics(100723)
    }
  },120)
})

function bgOpacityFormatter(value: string | number): string {
  return String(Number(value) * 100) + '%'
}

function handleInputBgOpacity(v: string) {
  if (!isNaN(Number(v))) {
    const inputNum = Number(v)
    if (inputNum > 100) {
      bgInputShow.value = '100'
    }else if (inputNum < 0) {
      bgInputShow.value = '0'
    }else{
      bgInputShow.value = v
    }
  }else{
    bgInputShow.value = '100'
  }
}

const DefaultColor = () => {
  store.fontColor = {
    lv1_fontColor:'#777777', // 一级标题颜色
    lv1_fontFamily: 'Microsoft YaHei', // 一级标题(板块标题)字体
    lv2_fontColor:'#777777', // 二级标题颜色
    lv2_fontFamily: 'Microsoft YaHei',// 二级标题(条目标题)字体
    main_fontColor:'#FFFFFF', // 正文颜色
    main_fontFamily: 'Microsoft YaHei',// 正文字体
    des_fontColor:'#FFFFFF', // 描述文本颜色
    des_fontFamily: 'Microsoft YaHei',// 描述文本字体
    icon_fontColor:'#1193F8', // 图标颜色
    highlight_fontColor:'#FFFFFF', // 凸显文本颜色
    highlight_fontFamily: 'Microsoft YaHei',// 凸显文本字体
    degree_fontFamily: 'Microsoft YaHei',// 程度描述字体
    degree_fontColor_low: '#35d57d',
    degree_fontColor_mid: '#1193f8',
    degree_fontColor_high: '#ffa800',
    degree_fontColor_warn: '#bf4040',
  }
  store.gameppBgColor = '#22232e'
  store.bgOpacity = 1
  store.bgOpacity2 = 1
  store.gameppVideo = ''
  store.gameppImg = ''
}

// 校验颜色值是否正确
const verifyColor = (color: string,defaultColor:string,key:string) => {
  if (!isValidColor(color)) {
    warningMsg('无效的颜色值，请输入正确的颜色。');
    if (key === 'gameppBgColor') {
      store.gameppBgColor = defaultColor
    }else if (key.includes('fontColor'))  {
      store.fontColor[key] = defaultColor
    }
  }else{
    if (key === 'gameppBgColor') {
      store.colorOrImg = 2
    }
  }
}
function warningMsg(msg:string) {
  ElMessage({
    // customClass: 'sensor_msg',
    message: h('p', { style: 'line-height: 1; font-size: 14px' }, [
      h('span', null, msg),
    ]),
    duration: 3000,
    type: "warning"
  })
}
function isValidColor(color: string) {
  const regExpHex3 = /^#([0-9A-F]{3})$/i; // 3位16进制颜色 如#fff
  const regExpHex6 = /^#([0-9A-F]{6})$/i; // 6位16进制颜色 如#ffffff
  const regExpHex8 = /^#([0-9A-F]{8})$/i; // 带透明度的8位16进制颜色 如#999999ff
  const regExpRgb = /^rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)$/; // 对rgb颜色检验
  const regExpRgba = /^rgba\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*([\d\.]+)\s*\)$/; // 对rgba颜色检验

  return regExpHex3.test(color) || regExpHex6.test(color) || regExpHex8.test(color) || regExpRgb.test(color) || regExpRgba.test(color);
}
const clearImgOrVideo = async () => {
  store.gameppVideo = ''
  store.gameppImg = ''
}
const chooseFile = async () => {
  const dialogConfig: any =
    {
      title: '选择图片和视频',
      properties: ['openFile'],
      filters: [{
        name: 'Files',
        extensions: ['jpg', 'png', 'bmp', 'mkv', 'avi', 'mp4', 'rmvb', 'flv', 'gif']
      }]
    }
  const AddFileResult = await gamepp.dialog.showOpenDialog.promise(dialogConfig)
  console.warn('选择文件路径', AddFileResult)
  if (!AddFileResult.canceled) { //中途点了取消
    let choosefilePath = AddFileResult.filePaths[0]
    const fileTypeArr = choosefilePath.split('.')
    const fileType = fileTypeArr[fileTypeArr.length - 1]
    console.log('选择路径', choosefilePath)
    if (['jpg', 'png', 'bmp', 'JPG', 'PNG', 'BMP', 'gif'].includes(fileType)) {

      store.gameppImg = choosefilePath
      store.gameppVideo = ''

    } else if (['mkv', 'avi', 'mp4', 'rmvb', 'flv', 'MKV', 'AVI', 'MP4', 'RMVB', 'FLV'].includes(fileType)) {
      store.gameppVideo = choosefilePath
      store.gameppImg = ''
    }

  } else {
    store.gameppVideo = ''
    store.gameppImg = ''
  }
}

const changeLang = (value: string) => {
  try {
    gamepp.setting.setString.promise(242, value.toUpperCase());
  } catch (error) {
  }
}

const change266 = function (v: any) {
  doTry((v: any) => {
    if (v) {
      gamepp.setting.setInteger.promise(266, 1);
    } else {
      gamepp.setting.setInteger.promise(266, 0);
    }
  })
}

const setStartWithWindowState = function () {
  doTry(() => {
    gamepp.setStartWithWindowState.sync(store.state.startWithWindowState, store.state.startWithWindowDelay)
  })
}

function doTry(func: Function, ...args: any[]) {
  try {
    func(...args)
  } catch (e) {
    console.log('%c err: ', "background:red;font-size:16px", e)
  }
}
</script>

<style scoped lang="scss">
#gxh {
  margin-top: 20px;
}
.gxh_title {
  width: 100%;
  color: var(--font-color);
  font-size: .12rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 15px;

    .restoreDefaultBtn {
        margin-left: auto;
        color: var(--active-color);
        display:flex;
    }
}
.left-box {
  .bgOpacity {
    margin-top: 20px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    flex-flow: row nowrap;
    gap: 10px;
    width: 430px;
    p {
      color: var(--font-gray);
      font-size: .12rem;
      white-space: nowrap;
    }
    .bgOpacity-input {
      width: 60px;
      height: 30px;
      background: #22232E;
      border-radius: 4px;
      margin-left: 10px;
    }
  }
  .bgColorPicker {
    height: 30px;
    margin-top: 20px;
    margin-bottom: 13px;
  }
    .setImgObjectFit {
        margin-top: 10px;
    }
  .dir {
    width: 320px;
    height: 30px;
    background: #343647;
    border-radius: 2px;
    margin-top: 20px;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    padding: 0 2px 0 10px;

    .path {
      width: 100%;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    position: relative;

    .clear-icon {
      position: absolute;
      right: 0;
      color: var(--warn-color);
      cursor: pointer;
      transform: translateX(calc(100% + 10px));
    }
  }
}
.right-box {
  .degreeFont {
    display: flex;
    flex-flow: row wrap;
    justify-content: space-between;
  }
}
</style>

<style lang="scss">
.dark_color_picker {
  &.el-popper.is-light {
    background: #1C1C22;
    border-color: #1C1C22;

    .el-color-dropdown__link-btn {
      display: none;
    }
    .el-button.is-plain {
      --el-button-hover-bg-color: #d7d7d7
    }
  }
}
.setting-item {
  .el-color-picker__icon {
      display: none;
  }
}
.bgOpacity {
  .el-slider__input {
    width: 60px;
  }
}
</style>
