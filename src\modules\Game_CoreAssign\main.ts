import { createApp } from 'vue'
//pinia
import { createPinia } from 'pinia'
//element plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
import processCoreAssign from './processCoreAssign.vue'
//路由
import router from './router'
import  './Game_CoreAssign_RTL.scss'
//国际化
import i18n from '../../assets/lang'
import './assets/theme.scss';
import '../../assets/iconfont/iconfont.css'
import '../../assets/iconfont/iconfont.js'
const pinia = createPinia()
const app = createApp(processCoreAssign)

app.use(pinia)
app.use(router)
app.use(ElementPlus)
app.use(i18n)
app.mount('#app')
