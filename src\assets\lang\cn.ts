const cn = {
  "update": {
    "Game": "游戏",
    "PP": "加加",
    "upgradingInProgress": "正在升级",
    "theModuleIsBeingUpdated": "正在更新模块",
    "dataIsBeingUpdated": "正在更新数据",
    "checkingUpdate": "正在检查更新",
    "checkingUpgrade": "正在检查升级",
    "loadingProgramComponent": "正在加载程序组件",
    "loadingHotkeyModules": "正在加载热键组件",
    "loadingGPPModules": "正在加载游戏加加组件",
    "loadingBlackWhiteList": "正在加载黑白名单",
    "loadingGameSetting": "正在加载游戏设置参数",
    "loadingUserAbout": "正在加载用户认证相关",
    "loadingGameBenchmark": "正在加载游戏跑分",
    "loadingHardwareInfo": "正在加载硬件信息组件",
    "loadingDBModules": "正在加载数据库模块",
    "loadingIGCModules": "正在加载IGC模块",
    "loadingFTPModules": "正在加载FTP支持模块",
    "loadingDialogModules": "正在加载对话框模块",
    "loadingDataStatisticsModules": "正在加载数据统计模块",
    "loadingSysModules": "正在加载系统组件",
    "loadingGameOptimization": "正在加载游戏优化",
    "loadingGameAcceleration": "正在加载游戏加速",
    "loadingScreenshot": "正在加载录像截图",
    "loadingVideoComponent": "正在加载视频压缩组件",
    "loadingFileFix": "正在加载文件修复",
    "loadingGameAI": "正在加载游戏AI画质",
    "loadingNVAPIModules": "正在加载NVAPI模块",
    "loadingAMDADLModules": "正在加载AMDADL模块",
    "loadingModules": "正在加载模块"
  },
  "messages": {
    "append": "添加",
    "confirm": "确定",
    "cancel": "取消",
    "default": "默认",
    "quickSelect": "快速选择",
    "onoffingame": "开启/关闭游戏内监控:",
    "changeKey": "点击更改快捷键",
    "clear": "清空",
    "hotkeyOccupied": "热键已被占用,请重新设置!",
    "minimize": "最小化",
    "exit": "退出",
    "export": "导出",
    "import": "导入",
    "screenshot": "截屏",
    "showHideWindow": "显示/隐藏窗口",
    "ingameControlPanel": "游戏内控制面板",
    "openOrCloseGameInSettings": "打开/关闭游戏内设置面板",
    "openOrCloseGameInSettings2": "按下此快捷键启用",
    "openOrCloseGameInSettings3": "打开/关闭游戏内监控",
    "openOrCloseGameInSettings4": "打开/关闭游戏滤镜",
    "startManualRecord": "开始/停止手动记录统计",
    "performanceStatisticsMark": "性能统计标记",
    "EnableAIfilter": "AI滤镜需要按下此快捷键启用",
    "Start_stop": "开始/停止手动记录统计",
    "pressureTest": "压力测试",
    "moduleNotInstalled": "功能模块未安装",
    "installingPressureTest": "安装压力测试模块中...",
    "importFailed": "导入失败",
    "gamepp": "游戏加加",
    "copyToClipboard": "已复制到粘贴板"
  },
  "home": {
    "homeTitle": "首页",
    "hardwareInfo": "硬件信息",
    "functionIntroduction": "功能介绍",
    "fixedToNav": "固定至导航栏",
    "cancelFixedToNav": "取消固定至导航栏",
    "hardwareInfoLoading": "硬件信息加载中...",
    "performanceStatistics": "性能统计",
    "updateNow": "立即更新",
    "recentRun": "最近运行",
    "resolution": "分辨率：",
    "duration": "时长：",
    "gameFilter": "游戏滤镜",
    "gameFilterHasAccompany": "游戏滤镜已陪伴",
    "gameFilterHasAccompany2": "名玩家游玩赛博朋克、APEX、霍格沃兹之遗等游戏",
    "currentList": "当前列表的监控项",
    "moreFunction": "Benchmark、压力测试、桌面监控等更多功能正在开发中~",
    "newVersion": "该功能模块有新版本!",
    "discoverUpdate": "发现更新!",
    "downloading": "正在下载",
    "retry": "重试",
    "erhaAI": "2哈Ai",
    "recordingmodule": "此功能依赖录像模块",
    "superPower": "超能时刻",
    "autoRecord": "自动记录游戏中击杀瞬间，轻松保存游戏高光时刻",
    "externalDevice": "外设灵动光控",
    "linkage": "游戏中触发击杀场景，联动外设进行展示",
    "AI": "AI性能测试",
    "test": "用显卡测试AI模型，查看显卡的AI性能分数",
    "supportedGames": "已支持游戏",
    "games": "永劫无间、三角洲行动、界外狂潮、英雄联盟、PUBG、无畏契约、The Finals、APEX英雄、COD20等",
    "videoRecording": "视频录制",
    "videoRecording2": "基于OBS的视频录制功能，支持调节视频比特率（码率）和帧率（FPS），满足不同画质与流畅度需求；并且支持“即时回放”，按下快捷键即可随时保存精彩片段！",
    "addOne": "喜加一",
    "gamePlatform": "游戏平台",
    "goShop": "前往商店页面",
    "receiveDeadline": "后领取截止",
    "2Ai": "2哈Ai",
    "questionDesc": "问题描述",
    "inputYourQuestion": "在此处填写您需要反馈的建议或意见。",
    "uploadLimit": "以jpg / png / bmp格式上传本地图像最多3张图像",
    "email": "电子邮件",
    "contactWay": "联系方式",
    "qqNumber": "QQ号码(选填)",
    "submit": "提交"
  },
  "hardwareInfo": {
    "hardwareOverview": "硬件概览",
    "copyAllHardwareInfo": "复制所有硬件信息",
    "processor": "处理器",
    "coreCount": "核心数：",
    "threadCount": "线程数：",
    "currentFrequency": "当前频率：",
    "currentVoltage": "当前电压：",
    "copy": "复制",
    "releaseDate": "上市日期",
    "codeName": "代号",
    "thermalDesignPower": "热设计功耗",
    "maxTemperature": "最大温度",
    "graphicsCard": "显卡",
    "brand": "品牌：",
    "streamProcessors": "流处理器：",
    "Videomemory": "显存：",
    "busSpeed": "总线速率",
    "driverInfo": "驱动信息",
    "driverInstallDate": "驱动安装日期",
    "hardwareID": "硬件 ID",
    "motherboard": "主板",
    "chipGroup": "芯片组：",
    "BIOSDate": "BIOS日期",
    "BIOSVersion": "BIOS版本",
    "PCIESlots": "PCIE插槽",
    "PCIEVersion": "支持的PCIE版本",
    "memory": "内存",
    "memoryBarCount": "数量：",
    "totalSize": "大小：",
    "channelCount": "通道：",
    "Specificmodel": "具体型号",
    "Pellet": "颗粒制造商",
    "memoryBarEquivalentFrequency": "内存条等效频率：",
    "hardDisk": "硬盘",
    "hardDiskCount": "硬盘数量：",
    "actualCapacity": "实际容量",
    "type": "类型",
    "powerOnTime": "通电时间",
    "powerOnCount": "通电次数",
    "SSDRemainingLife": "SSD剩余寿命",
    "partitionInfo": "分区信息",
    "hardDiskController": "硬盘控制器",
    "driverNumber": "驱动器序号",
    "display": "显示器",
    "refreshRate": "刷新率：",
    "screenSize": "屏幕尺寸：",
    "inches": "英寸",
    "productionDate": "生产日期",
    "supportRefreshRate": "支持刷新率",
    "screenLongAndShort": "屏幕长宽尺寸",
    "systemInfo": "系统信息",
    "version": "版本号",
    "systemInstallDate": "系统安装日期",
    "systemBootTime": "本次开机时间",
    "systemRunTime": "运行时长",
    "Poccupied": "P占用",
    "Eoccupied": "E占用",
    "occupied": "占用",
    "temperature": "温度",
    "Pfrequency": "P频率",
    "Efrequency": "E频率",
    "thermalPower": "热功耗",
    "frequency": "频率",
    "current": "当前",
    "noData": "无数据",
    "loadHwinfo_SDK": "加载Hwinfo_SDK.dll失败，无法读取硬件、传感器数据",
    "loadHwinfo_SDK_reason": "可能导致此问题的原因：",
    "reason": "原因",
    "BlockIntercept": "被杀毒软件阻止拦截，例如:2345杀毒软件(2345主动防御进程,McAfee主动防御进程)",
    "solution": "解决方案：",
    "solution1": "关闭并卸载相关进程后，重启GamePP",
    "solution2": "拔掉相关设备后，重启GamePP",
    "RestartGamePP": "取下手柄、等待设备响应后，重启GamePP",
    "HWINFOcannotrun": "Hwinfo无法正常运行",
    "downloadHWINFO": "下载Hwinfo",
    "openHWINFO": "，打开Hwinfo，点击RUN是否能正常打开",
    "hardwareDriverProblem": "硬件驱动问题",
    "checkHardwareManager": "检查硬件管理器，确保主板、显卡驱动都正常安装",
    "systemProblem": "系统问题,例如:使用暴风,小马等激活工具会导致驱动无法加载,WIN7系统补丁无法自动安装",
    "reinstallSystem": "重新安装系统进行激活,WIN7下载安装:*********升级补丁",
    "Windows7": "Windows7请安装sha256补丁,Windows10请使用数字证书激活,Windows11预览版系统,请 关闭内存完整性",
    "ViolenceActivator": "如果已经使用小马等暴力激活工具请修复或重装系统 ",
    "MultipleGraphicsCardDrivers": "电脑中安装了不同品牌的显卡驱动，例如同时安装了AMD和Nvidia驱动",
    "UninstallUnused": "卸载无用的显卡驱动后重启电脑",
    "OfficialQgroup": "以上原因都不是，请加我们官方Q群:908287288(五群)处理",
    "ExportHardwareData": "导出硬件数据",
    "D3D": "D3D 占用",
    "Total": "Total 占用",
    "VRAM": "显存占用",
    "VRAMFrequency": "显存频率",
    "SensorData": "传感器数据",
    "CannotGetSensorData": "无法获取传感器数据",
    "LoadingHardwareInfo": "加载硬件信息中...",
    "ScanTime": "扫描时间",
    "Rescan": "重新扫描",
    "Screenshot": "截图",
    "configCopyed": "配置信息已复制到剪贴板",
    "LegalRisks": "显示存在潜在法律风险",
    "brandLegalRisks": "品牌显示存在潜在法律风险",
    "professionalVersion": "专业版",
    "professionalWorkstationVersion": "专业工作站版",
    "familyEdition": "家庭版",
    "educationEdition": "教育版",
    "enterpriseEdition": "企业版",
    "flagshipEdition": "旗舰版",
    "familyPremiumEdition": "家庭高级版",
    "familyStandardEdition": "家庭普通版",
    "primaryVersion": "初级版",
    "bit": "位",
    "tempWall": "温度墙",
    "error": "错误",
    "screenshotSuccess": "截图保存成功",
    "atLeastOneData": "最少保留1条数据",
    "atMostSixData": "最多添加6条数据",
    "screenNotActivated": "未激活"
  },
  "psc": {
    "processCoreAssign": "进程核心分配",
    "CoreAssign": "核心分配：",
    "groupName": "分组名称：",
    "notGameProcess": "非游戏进程",
    "unNamedProcess": "未命名分组",
    "Group2": "组",
    "selectTheCore": "选择核心",
    "controls": "操作",
    "tips": "提示",
    "search": "搜索",
    "shiftOut": "移出",
    "ppValue": "PP值",
    "ppDesc": "PP值反应了历史硬件资源消耗情况，此值越大越占用硬件资源",
    "littletips": "小提示：长按进程可拖动至左侧分组",
    "warning1": "跨组选择核心线程可能影响性能，建议使用同组核心",
    "warning2": "你确定将这个分组名设置成空的吗",
    "warning3": "删除后核心分配效果会失效，确定要删除分组吗？",
    "allprocess": "所有进程",
    "pleaseCheckProcess": "请勾选进程",
    "dataSaveDesktop": "数据已保存至桌面",
    "createAGroup": "创建分组",
    "delGroup": "删除分组",
    "Group": "分组",
    "editGroup": "修改分组",
    "groupinfo": "组信息",
    "moveOutGrouping": "移出分组",
    "createANewGroup": "创建新分组",
    "unallocatedCore": "未分配核心",
    "inactiveProcess": "未活动进程",
    "importGroupingScheme": "导入分组方案",
    "derivedPacketScheme": "导出分组方案",
    "addNowProcess": "添加当前运行进程",
    "displaySystemProcess": "显示系统进程",
    "max64": "最大只能选择64个线程",
    "processName": "进程名",
    "chooseCurProcess": "选择当前进程",
    "selectNoProcess": "还未选择任何进程",
    "coreCount": "核心数",
    "threadCount": "线程数",
    "process": "进程",
    "plzInputProcessName": "请输入进程名以手动添加",
    "has_allocation": "存在线程分配方案的进程",
    "not_made": "您还没有对任意进程进行核心分配",
    "startUse": "启用优化",
    "stopUse": "停用优化",
    "threadAllocation": "线程分配",
    "configProcess": "配置进程",
    "selectThread": "选择线程",
    "hyperthreadingState": "超线程状态",
    "open": "已开启",
    "notYetUnlocked": "未开启",
    "nonhyperthreading": "非超线程",
    "intervalSelection": "间隔选择",
    "invertSelection": "反选",
    "description": "将游戏进程锁定至指定CPU核心运行，智能隔离后台程序干扰。有效提升FPS上限，使游戏FPS更稳定！ 减少游戏突发卡顿与帧率骤降，让多核处理器性能完全释放，确保游戏全程高帧流畅运行！",
    "importSuccess": "导入成功",
    "importFailed": "导入失败"
  },
  "InGameMonitor": {
    "onoffingame": "开启/关闭游戏内监控:",
    "InGameMonitor": "游戏内监控",
    "CustomMode": "自定义模式",
    "Developing": "开发中...",
    "NewMonitor": "新增监控项",
    "Data": "参数",
    "Des": "备注",
    "Function": "功能",
    "Editor": "编辑",
    "Top": "置顶",
    "Delete": "删除",
    "Use": "使用",
    "DragToSet": "呼出面板后可以拖动设置",
    "MonitorItem": "监控项",
    "addMonitorItem": "新增监控项",
    "hide": "隐藏",
    "show": "显示",
    "generalstyle": "通用样式",
    "restoredefault": "恢复默认",
    "arrangement": "排列方式：",
    "horizontal": "横向",
    "vertical": "纵向",
    "monitorposition": "监控位置",
    "canquickselectposition": "可在左图快速选择位置",
    "curposition": "当前位置：",
    "background": "背景",
    "backgroundcolor": "背景颜色：",
    "font": "字体",
    "fontStyle": "字体样式",
    "fontsize": "字体大小：",
    "fontcolor": "字体颜色：",
    "style": "样式：",
    "style2": "样式",
    "performance": "性能",
    "refreshTime": "刷新时间：",
    "goGeneralSetting": "前往通用设置",
    "selectMonitorItem": "选择监控项",
    "selectedSensor": "选中的传感器：",
    "showTitle": "显示标题",
    "hideTitle": "隐藏标题",
    "showStyle": "展示方式：",
    "remarkSize": "备注大小：",
    "remarkColor": "备注颜色：",
    "parameterSize": "参数大小：",
    "parameterColor": "参数颜色：",
    "defaultValueColor2": "数值默认颜色",
    "lineChart": "折线图",
    "lineColor": "折线颜色：",
    "lineThickness": "折线粗细：",
    "areaHeight": "区域高度：",
    "sort": "排序",
    "displacement": "位移：",
    "up": "上移",
    "down": "下移",
    "bold": "加粗",
    "stroke": "描边",
    "text": "文本",
    "textLine": "文本+折线",
    "custom": "自定义",
    "upperLeft": "左上",
    "upper": "中上",
    "upperRight": "右上",
    "Left": "左中",
    "middle": "正中",
    "Right": "右中",
    "lowerLeft": "左下",
    "lower": "中下",
    "lowerRight": "右下",
    "notSupport": "外设不支持点击显示隐藏",
    "notSupportRate": "回报率不支持点击显示隐藏",
    "notFindSensor": "未找到传感器，点击修改",
    "monitoring": "监控",
    "condition": "条件",
    "bigger": "大于",
    "smaller": "小于",
    "biggerThan": "大于阈值",
    "biggerThanthreshold": "大于阈值百分比",
    "smallerThan": "小于阈值",
    "smallerThanthreshold": "小于阈值百分比",
    "biggerPercent": "当前值降幅百分比",
    "smallerPercent": "当前值涨幅百分比",
    "replay": "即时回放功能",
    "screenshot": "截图功能",
    "text1": "当传感器数值",
    "text2": "，并且在",
    "text3": "等待时间",
    "text4": "秒内没有出现更高数值时，立即触发",
    "text5": "每次触发回放后，将阈值更新为当时触发时的数值，以减少频繁触发",
    "text6": "显示当前用于触发回放的阈值",
    "text7": "显示传感器数值",
    "text8": "超过初始阈值",
    "text9": "低于初始阈值",
    "text10": "初始阈值的次数",
    "initThreshold": "初始阈值",
    "curThreshold": "当前阈值：",
    "curThreshold2": "当前阈值",
    "resetCurThreshold": "重置当前阈值",
    "action": "触发功能",
    "times": "次",
    "percentage": "百分比",
    "uninstallobs": "录像模块未下载",
    "install": "下载",
    "performanceAndAudioMode": "性能与音频兼容模式",
    "isSaving": "正在保存",
    "video_replay": "即时回放",
    "saved": "已保存",
    "loadQualitysScheme": "加载画质方案",
    "notSet": "未设置",
    "mirrorEnable": "滤镜已开启",
    "canBeTurnedOff": "可关闭",
    "mirrorClosed": "游戏滤镜已关闭",
    "closed": "已关闭",
    "openMirror": "开启滤镜",
    "wonderfulScenes": "精彩镜头",
    "VulkanModeHaveProblem": "Vulkan模式存在兼容性问题",
    "suggestDxMode": "建议切换为Dx模式",
    "functionNotSupported": "功能暂不支持",
    "NotSupported": "不支持",
    "gppManualRecording": "游戏加加,手动记录",
    "perfRecordsHaveBeenSaved": "性能记录已保存",
    "redoClickF8": "如需继续记录请再次按下F8",
    "startIngameMonitor": "正在启用游戏内监控功能",
    "inGameMarkSuccess": "游戏内标记成功",
    "recordingFailed": "录制失败",
    "recordingHasNotDownload": "录像功能未下载",
    "hotkeyDetected": "检测到功能热键冲突",
    "plzEditIt": "请到软件内修改后使用",
    "onePercentLowFrame": "1%Low帧",
    "pointOnePercentLowFrame": "0.1%Low帧",
    "frameGenerationTime": "帧生成时间",
    "curTime": "当前时间",
    "runTime": "运行时长",
    "cpuTemp": "CPU温度",
    "cpuUsage": "CPU占用",
    "cpuFreq": "CPU频率",
    "cpuPower": "CPU热功耗",
    "gpuTemp": "GPU温度",
    "gpuUsage": "GPU占用率",
    "gpuPower": "GPU热功耗",
    "gpuFreq": "GPU频率",
    "memUsage": "内存占用"
  },
  "LoginArea": {
    "login": "登录",
    "loginOut": "退出登录",
    "vipExpire": "到期",
    "remaining": "剩余",
    "day": "天",
    "openVip": "开通会员",
    "vipPrivileges": "会员特权",
    "rechargeRenewal": "充值续费",
    "Exclusivefilter": "滤镜",
    "configCloudSync": "配置云同步",
    "comingSoon": "敬请期待"
  },
  "GameMirror": {
    "filterStatus": "滤镜状态",
    "filterPlan": "滤镜方案",
    "filterShortcut": "滤镜快捷键",
    "openCloseFilter": "开启/关闭滤镜：",
    "effectDemo": "效果演示",
    "demoConfig": "演示配置",
    "AiFilter": "Ai滤镜以游戏内效果为准",
    "AiFilterFAQ": "Ai滤镜常见问题",
    "gamePPAiFilter": "游戏加加Ai滤镜",
    "gamePPAiFilterVip": "游戏加加VIP专用Ai滤镜，根据游戏场景实时调整滤镜参数，实现游戏画面效果优化，提升游戏体验。",
    "AiMingliangTips": "Ai明亮：建议在游戏画面过于昏暗时使用。",
    "AiBrightTips": "Ai鲜艳：建议在游戏画面过于暗淡时使用。",
    "AiDarkTips": "Ai暗淡：建议在游戏画面过于鲜艳时使用。",
    "AiBalanceTips": "Ai均衡：适合大多数游戏场景。",
    "AiTips": "Tips：Ai滤镜需要在游戏内按下快捷键使用。",
    "AiFilterUse": "在游戏中请使用",
    "AiFilterAdjust": "快捷键调整Ai滤镜",
    "Bright": "鲜艳",
    "Soft": "柔和",
    "Highlight": "高亮",
    "Film": "电影",
    "Benq": "明基",
    "AntiGlare": "防眩光",
    "HighSaturation": "高饱和度",
    "Brightness": "鲜明",
    "Day": "白天",
    "Night": "夜晚",
    "Nature": "自然",
    "smooth": "细腻",
    "elegant": "淡雅",
    "warm": "暖调",
    "clear": "清晰",
    "sharp": "锐利",
    "vivid": "生动",
    "beauty": "亮丽",
    "highDefinition": "高清",
    "AiMingliang": "AI 明亮",
    "AiBright": "AI 鲜艳",
    "AiDark": "AI 暗淡",
    "AiBalance": "AI 均衡",
    "BrightTips": "鲜艳滤镜适合休闲、动作或冒险类游戏，强化色彩饱和度，使游戏画面更加生动和吸引人。",
    "liangTips": "滤镜建议在游戏画面过于昏暗时使用。",
    "anTips": "滤镜建议在游戏画面过于暗淡时使用。",
    "jianyiTips": "滤镜建议在游戏画面过于鲜艳时使用。",
    "shiTips": "滤镜适合大多数游戏场景。",
    "shi2Tips": "滤镜适合休闲、动作或冒险类游戏，强化色彩饱和度，使游戏画面更加生动和吸引人。",
    "ruiTips": "滤镜色彩细腻，光影温和，适合表现梦幻、温馨或怀旧场景。",
    "qingTips": "色调明亮，对比度高，细节清晰锐利，适合展现鲜活生动、光线充足的场景。",
    "xianTips": "较高对比度和亮度条件，确保黑暗场景中细节不失真，明亮场景下不刺眼。",
    "dianTips": "适当提高画面亮度和色彩，让游戏画面尽可能达到电影的画面感,",
    "benTips": "降低白光的效果，让纯白的游戏场景不再刺眼,",
    "fangTips": "适合开放世界、冒险类游戏，提高亮度和对比度，使画面更加清晰,",
    "jiaoTips": "适合角色扮演、模拟类游戏，平衡色调，提升画面真实感,",
    "jieTips": "适合剧情丰富、情感细腻的游戏，增强细节和柔和度，使画面更加精致,",
    "jingTips": "适合动作、竞技类游戏，提高清晰度和对比度，使画面更加锐利,",
    "xiuTips": "适合治愈、休闲类游戏，增强暖色调和柔和度，使画面更加温馨,",
    "qihuanTips": "适合充满奇幻元素、色彩丰富的场景，加深色彩饱和度，营造视觉上的强烈冲击力,",
    "shengTips": "通过强化色彩和细节，突显场景的生动与真实感,",
    "sheTips": "适合第一人称射击、解谜或冒险类游戏，增强细节和对比度，提升游戏世界的逼真感,",
    "she2Tips": "适合射击、竞速或格斗类游戏，突出高清细节和动作表现，以增强游戏体验的紧张感和视觉效果,",
    "an2Tips": "让暗处的场景看起来更清晰，适合黑暗或夜晚下的场景,",
    "wenTips": "适合文艺、探险或休闲类游戏，营造柔和的色调和光影效果，增添场景的优雅和温馨感,",
    "jing2Tips": "适合竞技类、音乐节奏或夜间城市场景的游戏，突出明亮色彩和光影效果,",
    "jing3Tips": "适合竞技、动作或奇幻类游戏，强化色彩对比度，使画面更加饱满和充满活力,",
    "onlyVipCanUse": "该滤镜仅对VIP用户开放"
  },
  "GameRebound": {
    "noGame": "没有游戏记录",
    "noGameRecord": "还没有游戏记录哦！快去开一把吧!",
    "gameDuration": "当日游戏时长：",
    "gameElectricity": "当日用电：",
    "degree": "度",
    "gameCo2": "当日Co2排放：",
    "gram": "克",
    "manualRecord": "手动记录",
    "recordDuration": "记录时长：",
    "details": "详情",
    "average": "平均值",
    "minimum": "最小值",
    "maximum": "最大值",
    "occupancyRate": "占用率",
    "voltage": "电压",
    "powerConsumption": "功耗",
    "start": "开始：",
    "end": "结束：",
    "Gametime": "游戏时长：",
    "Compactdata": "精简数据",
    "FullData": "完整数据",
    "PerformanceAnalysis": "性能分析",
    "PerformanceAnalysis2": "事件报告",
    "HardwareStatus": "硬件状态",
    "totalPower": "总耗电",
    "TotalEmissions": "总排放",
    "PSS": "P.S.下方图示数据为平均值",
    "FrameGenerationTime": "帧生成时间",
    "GameResolution": "游戏分辨率",
    "FrameGenerationTimeTips": "此数据点异常高，因此未纳入统计",
    "FrameGenerationTimeTips2": "此数据点异常低，因此未纳入统计",
    "noData": "无",
    "ProcessorOccupancy": "处理器占用",
    "ProcessorFrequency": "处理器频率",
    "ProcessorTemperature": "处理器温度",
    "ProcessorHeatPower": "处理器热功耗",
    "GraphicsCardOccupancy": "显卡占用D3D",
    "GraphicsCardOccupancyTotal": "显卡占用Total",
    "GraphicsCardFrequency": "GPU频率",
    "GraphicsCardTemperature": "显卡温度",
    "GraphicsCardCoreTemperature": "显卡核心热点温度",
    "GraphicsCardHeatPower": "GPU 热功耗",
    "GraphicsCardMemoryTemperature": "显存温度",
    "MemoryOccupancy": "内存占用",
    "MemoryTemperature": "内存温度",
    "MemoryPageFaults": "内存分页中断",
    "Duration": "时长",
    "Time": "时间",
    "StartStatistics": "开始统计",
    "Mark": "标记",
    "EndStatistics": "结束统计",
    "LineChart": "折线图",
    "AddPointInGame_m1": "在游戏中按下",
    "AddPointInGame_m2": "可添加标记点",
    "LeftMouse": "鼠标左键切换显示/隐藏，鼠标右键更换颜色",
    "DeleteThisLine": "删除此折线",
    "AddCurve": "添加曲线",
    "AllCurvesAreHidden": "所有曲线均被隐藏",
    "ThereAreSamplingData": "共有采样数据：",
    "Items": "条",
    "StatisticsData": "统计数据",
    "electricity": "用电",
    "carbonEmission": "碳排放",
    "carbonEmissionTips": "二氧化碳排放量（千克） = 耗电度数 × 0.785",
    "D3D": "D3D占用：",
    "TOTAL": "TOTAL占用：",
    "Process": "工艺：",
    "L3Cache": "三级缓存：",
    "OriginalFrequency": "原始频率：",
    "MaximumBoostFrequency": "最大睿频：",
    "DriverVersion": "驱动版本号：",
    "GraphicsCardMemoryBrand": "显存品牌：",
    "Bitwidth": "位宽：",
    "System": "系统：",
    "Screen": "屏幕",
    "Interface": "接口：",
    "Channel": "通道：",
    "Timing": "时序：",
    "Capacity": "容量：",
    "Generation": "代数：",
    "AddPoint_m1": "在游戏中按下",
    "AddPoint_m2": "可添加标记点",
    "Hidden": "已隐藏",
    "Totalsampling": "共有采样数据：",
    "edition": "驱动版本：",
    "MainHardDisk": "主硬盘",
    "SetAsStartTime": "设置为开始时间",
    "SetAsEndTime": "设置为结束时间",
    "WindowWillBe": "性能统计窗口将在",
    "After": "后关闭",
    "NoLongerPopUpThisGame": "该游戏不再弹出",
    "HideTemperatureReason": "隐藏温度原因",
    "HideTemperatureReason2": "隐藏事件报告",
    "HideOtherReason": "隐藏其他原因",
    "CPUanalysis": "CPU性能分析",
    "TemperatureCause": "温度原因",
    "tempSensorEvent": "温度传感器事件",
    "NoTemperatureLimitation": "没有因温度导致的CPU性能受限，您的散热系统可以完美胜任这款游戏所需~",
    "NoTemperatureLimitation2": "没有温度传感器事件，您的散热系统可以完美胜任这款游戏所需~",
    "performanceis": "在所选的",
    "Inside": "内，",
    "TheStatisticsTimeOf": "的统计时间都满足由此原因触发的相关条件。其中，触发频次最高原因是",
    "limited": "占温度原因导致性能受限总时长的",
    "SpecificReasons": "具体原因及在温度原因中的占比：",
    "OptimizationSuggestion": "优化建议：",
    "CPUtemperature": "CPU自身温度过热，建议检查/改善CPU的散热环境。",
    "CPUoverheat": "CPU因主板供电过热，请检查主板相关设置或改善散热环境",
    "OtherReasons": "其他原因",
    "NoPowerSupplyLimitation": "没有因供电/功耗导致的CPU性能受限，您的BIOS功耗设置可以完美胜任这款游戏所需~",
    "PowerSupplyLimitation": "占供电/功耗原因导致性能受限总时长的",
    "SpecificReasonsInOtherReasons": "具体原因及在其他原因中的占比：",
    "PleaseCheckTheMainboard": "请检查主板供电情况或调整BIOS功耗设置以调节其他原因导致的CPU性能受限问题",
    "CPUcoretemperature": "核心温度达到Tj,Max而受到限制",
    "CPUCriticalTemperature": "CPU达到临界温度",
    "CPUCircuitTemperature": "CPU封装/环形总线达到Tj,Max而受到限制",
    "CPUCircuitCriticalTemperature": "CPU封装/环形总线达到临界温度",
    "CPUtemperatureoverheating": "CPU温度过热，触发后CPU会主动降频以降低温度，防止硬件故障",
    "CPUoverheatingtriggered": "因过热触发散热机制，CPU会相应调整电压和频率以降低功耗和温度",
    "CPUPowerSupplyOverheating": "CPU因主板供电严重过热而所受到限制",
    "CPUPowerSupplyLimitation": "CPU因主板供电过热而受到限制",
    "CPUMaximumPowerLimitation": "核心受到最大功耗限制",
    "CPUCircuitPowerLimitation": "CPU封装/环形总线达到功耗限制",
    "CPUElectricalDesignLimitation": "触发电气设计限制（含ICCmax电流墙、PL4峰值功耗墙、SVID电压限制等)",
    "CPULongTermPowerLimitation": "CPU的长时功耗达到限制",
    "CPULongTermPowerinstantaneous": "CPU的瞬时功耗达到限制",
    "CPUPowerLimitation": "CPU睿频衰减机制，一般由BIOS或特定软件限制",
    "CPUPowerWallLimitation": "CPU功耗墙限制",
    "CPUcurrentwalllimit": "CPU电流墙限制",
    "AiAgent": "加加特工(AI Agent)",
    "AgentDesc": "根据本次游戏的性能统计数据，从各方面进行数据分析",
    "fnBeta": "此功能处于邀请测试阶段，您的游戏加加账号暂未获得测试权限",
    "getAIReport": "获取AI报告",
    "waitingAi": "正在等待报告生成完毕",
    "no15mins": "游戏时长不足15分钟，无法获取有效AI报告",
    "timeout": "服务器请求超时",
    "agentId": "特工ID：",
    "reDo": "重新生成报告",
    "text2": "游戏加加特工：在线AI分析报告，以下内容由AI生成，仅作为参考。",
    "amdAiagentTitle": "游戏加加特工：AMD Ryzen AI分析报告，以下内容由AI生成，仅作为参考。",
    "noCurData": "暂无当前数据",
    "dataScreening": "数据筛选",
    "dataScreeningDescription": "描述本功能旨在排除数据统计中，类似地图加载、大厅挂机 等非有效游戏时间段的统计数据，0为不进行排除。",
    "excessivelyHighParameter": "过高参数",
    "tooLowParameter": "过低参数",
    "theMaximumValueIs": "最大值为",
    "theMinimumValueIs": "最小值为",
    "exclude": "排除",
    "dataStatisticsAtThatTime": "时的数据统计",
    "itHasBeenGenerated": "已生成完毕，",
    "clickToView": "点击查看",
    "onlineAnalysis": "在线分析",
    "amdRyzenAILocalAnalysis": "AMD Ryzen AI 本地分析",
    "useTerms": "使用条件：",
    "term1": "1.Ryzen AI Max 处理器 或 Ryzen Al 300系列处理器",
    "term2": "2.AMD NPU 驱动版本",
    "term3": "3.核显开启",
    "conformsTo": "符合",
    "notInLineWith": "不符合",
    "theVersionIsTooLow": "版本过低",
    "canNotUseAmdNpu": "您的配置不符合使用要求，无法使用AMD NPU本体分析",
    "unusable": "无法使用",
    "downloadTheFile": "下载文件",
    "downloadSource": "下载源：",
    "fileSize": "文件大小：约8.34GB",
    "cancelDownload": "取消下载",
    "filePath": "文件位置",
    "generateAReport": "生成报告",
    "fileMissing": "文件缺失，需重新下载",
    "downloading": "下载中...",
    "theModelConfigurationLoadingFailed": "模型配置加载失败",
    "theModelDirectoryDoesNotExist": "模型目录不存在",
    "thereIsAMistakeInReasoning": "推理出错",
    "theInputExceedsTheModelLimit": "输入超过模型限制",
    "selectModelNotSupport": "所选下载模型不支持",
    "delDirFail": "删除已存在的模型目录失败",
    "failedCreateModelDir": "创建模型目录失败",
    "modelNotBeenFullyDownload": "模型未全部下载",
    "agentIsThinking": "加加特工正在思考",
    "reasoningModelFile": "推理模型文件",
    "modelReasoningTool": "模型推理工具"
  },
  "SelectSensor": {
    "DefaultSensor": "默认传感器",
    "Change": "更改",
    "FanSpeed": "风扇转速",
    "MainGraphicsCard": "主显卡",
    "SetAsMainGraphicsCard": "设置为主显卡",
    "GPUTemperature": "GPU温度",
    "GPUHeatPower": "GPU热功耗",
    "GPUTemperatureD3D": "GPU D3D占用",
    "GPUTemperatureTOTAL": "GPU TOTAL占用",
    "GPUTemperatureCore": "GPU核心热点温度",
    "MotherboardTemperature": "主板温度",
    "MyAttention": "我的关注",
    "All": "全部",
    "Unit": "单位：",
    "NoAttention": "没有关注的传感器",
    "AttentionSensor": "关注的传感器(Beta)",
    "GoToAttention": "前往关注",
    "CancelAttention": "取消关注",
    "noThisSensor": "无传感器",
    "deviceAbout": "外设相关",
    "deviceBattery": "外设电量",
    "testFunction": "测试功能",
    "mouseEventRate": "鼠标回报率",
    "relatedWithinTheGame": "游戏相关",
    "winAbout": "系统相关",
    "trackDevicesBattery": "追踪外设电量",
    "ingameRealtimeMouseRate": "游戏实时采用的鼠标回报率",
    "notfoundDevice": "未发现支持设备",
    "deviceBatteryNeedMythcool": "支持显示电量设备列表：（需配合Myth.Cool使用）",
    "vkm1mouse": "Valkyrie M1鼠标",
    "vkm2mouse": "Valkyrie M2鼠标",
    "vk99keyboard": "Valkyrie 99磁轴键盘",
    "logitechProWireless": "罗技 PRO WIRELESS",
    "logitechProXSUPERLIGHT": "罗技 PRO X SUPERLIGHT",
    "logitechProXSUPERLIGHT2": "罗技 PRO ",
    "logitechPro2LIGHTSPEED": "罗技 PRO 2 LIGHTSPEED",
    "steelSeriesArctisGameBuds": "赛睿Arctis GameBuds",
    "steelSeriesArctis7PPlus": "赛睿Arctis 7P Plus",
    "razerV3": "雷蛇炼狱蝰蛇 V3 专业版",
    "razerV2": "雷蛇毒蝰 V2 专业版",
    "wireless": "无线",
    "logitechNeedGhub": "罗技无法获取设备型号时需下载GHUB",
    "chargingInProgress": "充电中",
    "inHibernation": "休眠中"
  },
  "video": {
    "videoRecord": "视频录制",
    "recordVideo": "录制的视频",
    "scheme": "方案",
    "suggestScheme": "推荐方案",
    "text1": "此方案下 1分钟视频大小约为",
    "text2": "该功能会占用额外系统资源",
    "low": "低",
    "mid": "中",
    "high": "高",
    "1080p": "原生",
    "RecordingFPS": "录制FPS",
    "bitRate": "视频比特率",
    "videoResolution": "视频分辨率",
    "startStopRecord": "开始/停止录像",
    "instantReplay": "即时回放",
    "instantReplayTime": "即时回放时长",
    "showIngame": "呼出游戏内控制面板",
    "CaptureMode": "捕获方式",
    "gameWindow": "游戏窗口",
    "desktopWindow": "桌面窗口",
    "fileSavePath": "文件存储路径",
    "selectVideoSavePath": "选择录像保存路径",
    "diskFreeSpace": "硬盘剩余空间：",
    "edit": "修改",
    "open": "打开",
    "displayMouse": "显示鼠标光标",
    "recordMicrophone": "录制麦克风",
    "gameGraphics": "游戏原始画面"
  },
  "Setting": {
    "common": "通用",
    "personal": "个性化设置",
    "messageNotification": "消息通知",
    "sensorReading": "传感器读数",
    "OLEDscreen": "OLED防烧屏",
    "performanceStatistics": "性能统计",
    "shortcut": "快捷键",
    "ingameSetting": "游戏设置保存",
    "other": "其他",
    "otherSettings": "其他设置",
    "GeneralSetting": "通用设置",
    "softwareVersion": "软件版本",
    "checkForUpdates": "检查更新",
    "updateNow": "立即更新",
    "currentVersion": "当前版本",
    "latestVersion": "最新版本",
    "isLatestVersion": "当前版本已经是最新版本",
    "functionModuleUpdate": "功能模块更新",
    "alwaysUpdateModules": "保持所有已经安装功能模块为最新版本",
    "lang": "语言",
    "bootstrap": "开机自启",
    "powerOn_m1": "开机",
    "powerOn_m2": "秒后自动启动",
    "defaultDelay": "默认为40秒",
    "followSystemScale": "跟随系统缩放",
    "privacySettings": "隐私设置",
    "JoinGamePPPlan": "加入GamePP用户体验改善计划",
    "personalizedSetting": "个性化设置",
    "restoreDefault": "恢复默认",
    "color": "颜色",
    "picture": "图片",
    "video": "视频",
    "browse": "浏览",
    "clear": "清除",
    "mp4VideoOrPNGImagesCanBeUploaded": "可上传MP4视频或PNG图片",
    "transparency": "透明度",
    "backgroundColor": "背景底色",
    "textFont": "正文字体",
    "message": "消息",
    "enableInGameNotifications": "开启游戏内通知",
    "messagePosition": "游戏内显示位置",
    "leftTop": "左上角",
    "leftCenter": "左中间",
    "leftBottom": "左下角",
    "rightTop": "右上角",
    "rightCenter": "右中间",
    "rightBottom": "右下角",
    "noticeContent": "通知内容",
    "gameInjection": "游戏注入",
    "ingameShow": "游戏内显示",
    "inGameMonitoring": "游戏内监控",
    "gameFilter": "游戏滤镜",
    "start": "开始",
    "endMarkStatistics": "结束标记统计",
    "readHwinfoFail": "HWINFO 硬件信息读取失败！",
    "dataSaveDesktop": "数据已保存至粘贴板和桌面文件",
    "TheSensorCacheCleared": "已清除传感器缓存数据",
    "defaultSensor": "默认传感器",
    "setSensor": "选择传感器",
    "refreshTime": "数据刷新时间",
    "recommend": "推荐",
    "sensorMsg": "时间间隔越短，性能消耗越大，请谨慎选择",
    "exportData": "数据导出",
    "exportHwData": "导出硬件信息数据",
    "sensorError": "传感器读数异常",
    "clearCache": "清除缓存",
    "littleTips": "小提示:性能报告会在游戏启动2分钟后生成",
    "disableAutoShow": "禁用性能统计窗口自动弹出",
    "AutoClosePopUpWindow_m1": "性能统计窗口在以下时间后自动关闭:",
    "AutoClosePopUpWindow_m2": "秒",
    "abnormalShutdownReport": "异常关机报告",
    "showWeaAndAddress": "显示天气、位置信息",
    "autoScreenShots": "标记时自动截图游戏画面",
    "keepRecent": "保留最近的数据显示条数:",
    "noLimit": "无限制",
    "enableInGameSettingsSaving": "启用游戏内设置保存",
    "debugMode": "调试模式",
    "enableDisableDebugMode": "打开/关闭调试模式",
    "audioCompatibilityMode": "音频兼容模式",
    "quickClose": "快速关闭",
    "closeTheGameQuickly": "快速关闭游戏进程",
    "cancel": "取消",
    "confirm": "确认",
    "MoveInterval_m1": "桌面监控和游戏内监控将在",
    "MoveInterval_m2": "分钟后略微移动",
    "text3": "退出游戏后,不弹出性能报告窗口,仅在保留历史记录",
    "text5": "将于异常关机后自动生成报告，开启本功能会占用额外的系统资源",
    "text6": "使用快捷键功能可能会与其他游戏快捷冲突,请谨慎设置,",
    "text7": "设置 快捷键 \"无”请使用键盘 Backspace 键",
    "text8": "根据进程名称保留游戏运行时的滤镜、游戏内监控等功能状态",
    "text9": "开启后会持续记录运行日志;关闭会清空日志文件（建议关闭）",
    "text10": "开启后将无法获取主板传感器，可解决游戏加加引起的音频问题",
    "text11": "连续2次使用Alt+F4,可快速退出当前游戏",
    "text12": "此模式,需要重启游戏加加是否继续？",
    "openMainUI": "打开主界面",
    "setting": "设置",
    "feedback": "问题反馈",
    "help": "帮助",
    "sensorReadingSetting": "传感器读数设置",
    "searchlanguage": "搜索语言"
  },
  "GamePlusOne": {
    "year": "年",
    "month": "月",
    "day": "日",
    "success": "成功",
    "fail": "失败",
    "will": "当前",
    "missedGame": "已错过的游戏",
    "text1": "款，约￥",
    "text2": "累计领取游戏数量",
    "text3": "款",
    "gamevalue": "游戏价值",
    "gamevalue1": "领取",
    "total": "共计领取",
    "text4": "款游戏，累计为您节省",
    "text6": "款，价值",
    "Platformaccountmanagement": "平台账号管理",
    "Missed1": "（错过领取）",
    "Received2": "（成功领取）",
    "Receivedsoon2": "（当前可领取）",
    "Receivedsoon": "当前可领取",
    "Missed": "错过领取",
    "Received": "成功领取",
    "Getaccount": "领取账号",
    "Worth": "价值",
    "Auto": "自动",
    "Manual": "手动",
    "Pleasechoose": "请选择游戏",
    "Receive": "立即领取",
    "Selected": "已选择",
    "text5": "款游戏",
    "Automatic": "自动领取中...",
    "Collecting": "领取中...",
    "ReceiveTimes": "本月领取次数",
    "Thefirst": "第",
    "Week": "周",
    "weekstotal": "共53周",
    "Return": "返回",
    "Solutionto": "绑定账号失败解决方案",
    "accounts": "绑定账号数量",
    "Addaccount": "添加账号",
    "Clearcache": "清除缓存",
    "Bindtime": "绑定时间",
    "Status": "状态",
    "Normal": "正常",
    "Invalid": "失效",
    "text7": "款游戏，累计为您节约",
    "Yuan": "元",
    "untie": "解绑",
    "disable": "停用",
    "enable": "启用",
    "gamePlatform": "游戏平台",
    "goStorePage": "前往商店页面",
    "receiveEnd": "后领取截止",
    "loginPlatformAccount": "登录的平台账号",
    "waitReceive": "等待领取",
    "receiveSuccess": "领取成功",
    "accountInvalid": "账号失效",
    "alreadyOwn": "已拥有",
    "networkError": "网络异常",
    "noGame": "无游戏本体",
    "manualReceiveInterrupt": "手动领取中断",
    "receiving": "领取中",
    "agree": "我同意加入《喜加一领取计划》",
    "again": "再次领取"
  },
  "shutdownTimer": {
    "timedShutdown": "定时关机",
    "currentTime": "当前时间：",
    "setCountdown": "设置倒计时",
    "shutdownInSeconds": "秒后关机",
    "shutdownIn": "后关机",
    "goingToBe": "将在",
    "executionPlan": "执行计划",
    "startTheClock": "开始计时",
    "stopTheClock": "取消计划",
    "isShuttingDown": "正在执行定时关机计划：",
    "noplan": "暂无关机计划",
    "hour": "时",
    "min": "分",
    "sec": "秒",
    "ms": "毫秒",
    "year": "年",
    "month": "月",
    "day": "日",
    "hours": "小时"
  },
  "screenshotpage": {
    "screenshot": "截图",
    "screenshotFormat": "专为游戏画面捕捉设计，支持JPG/PNG/BMP格式保存，可快速截取游戏画面，确保高清画质无损输出",
    "Turnon": "开启“自动截图”,每",
    "seconds": "秒",
    "takeScreenshot": "执行一次自动截图",
    "screenshotSettings": "此设置游戏中勾选无效",
    "saveGameFilterAndMonitoring": "在截图中保存“游戏滤镜”及“游戏内监控”效果",
    "disableScreenshotSound": "关闭截图声音提示",
    "imageFormat": "图片格式",
    "recommended": "推荐",
    "viewingdetails": "保留画质细节，体积适中，适合查看细节",
    "saveSpace": "画质可压缩，体积最小，省空间",
    "ultraQuality": "画质超清无压缩，文件极大，推荐画质追求玩家存档",
    "fileSavePath": "文件保存路径",
    "hardDiskSpace": "硬盘剩余空间：",
    "minutes": "分钟"
  },
  "DesktopMonitoring": {
    "desktopMonitoring": "桌面监控",
    "SomeSensors": "我们为您推荐了一些传感器进行监控，您可以删除或新增",
    "AddComponent": "新增组件",
    "Type": "类型",
    "Remarks": "备注",
    "AssociatedSensor": "关联传感器",
    "Operation": "操作",
    "Return": "返回",
    "TimeSelection": "时间选择：",
    "Format": "格式：",
    "Rule": "规则：",
    "Coordinate": "坐标：",
    "CustomTextContent": "自定义文本内容：",
    "SystemTime": "系统时间",
    "China": "中国",
    "Britain": "英国",
    "America": "美国",
    "Russia": "俄罗斯",
    "France": "法国",
    "DateAndTime": "日期+时间",
    "Time": "时间",
    "Date": "日期",
    "Week": "星期",
    "DateAndTimeAndWeek": "日期+时间+星期",
    "TimeAndWeek": "时间+星期",
    "Hour12": "12小时制",
    "Hour24": "24小时制",
    "SelectSensor": "选择传感器：",
    "AssociatedSensor1": "关联传感器：",
    "SensorUnit": "传感器单位：",
    "Second": "秒：",
    "Corner": "圆角：",
    "BackgroundColor": "背景颜色：",
    "ProgressColor": "进度颜色：",
    "Font": "字体：",
    "SelectFont": "选择字体",
    "FontSize": "字号：",
    "Color": "颜色：",
    "Style": "样式：",
    "Bold": "加粗",
    "Italic": "斜体",
    "Shadow": "阴影",
    "ShadowPosition": "阴影位置：",
    "ShadowEffect": "阴影效果：",
    "Blur": "模糊",
    "ShadowColor": "阴影颜色：",
    "SelectFromLocalFiles": "从本地文件中选取：",
    "UploadImageVideo": "上传图片/视频",
    "UploadSVGFile": "上传SVG文件",
    "Width": "宽：",
    "Height": "高：",
    "Effect": "效果：",
    "Rotation": "旋转：",
    "WhenTheSensorValue": "当传感器数值大于",
    "conditions": "不满足条件时（传感器值为0不旋转）",
    "Clockwise": "顺时针",
    "Counterclockwise": "逆时针",
    "QuickRotation": "快速旋转",
    "SlowRotation": "慢速旋转",
    "StopRotation": "停止旋转",
    "StrokeColor": "描边颜色：",
    "Path": "路径",
    "Color1": "颜色",
    "ChangeColor": "更换颜色",
    "When": "当",
    "SensorValue": "传感器数值大于等于",
    "SensorValue1": "传感器数值小于等于",
    "SensorValue2": "传感器数值等于",
    "MonitoringSettings": "监控设置",
    "RestoreDefault": "恢复默认",
    "Monitor": "显示器",
    "AreaSize": "区域大小",
    "Background": "背景",
    "ImageVideo": "图片/视频",
    "PureColor": "纯色",
    "Select": "选择",
    "ImageVideoDisplayMode": "图片/视频显示方式",
    "Transparency": "透明度",
    "DisplayPosition": "显示位置",
    "Stretch": "拉伸",
    "Fill": "填充",
    "Adapt": "适应",
    "SelectThePosition": "点击方格可快速选择位置",
    "CurrentPosition": "当前位置：",
    "DragLock": "拖动锁定",
    "LockMonitoringPosition": "锁定监控位置（锁定后监控不能拖动）",
    "Unlockinterior": "解锁内部元素拖动",
    "Font1": "字体",
    "GameSettings": "游戏设置",
    "CloseDesktopMonitor": "游戏运行时，自动关闭桌面监控",
    "OLED": "OLED防烧屏",
    "Display": "显示",
    "PleaseEnterContent": "请输入内容",
    "NextStep": "下一步",
    "Add": "添加",
    "StylesForYou": "我们为您推荐了一些监控样式，您可以选中后应用，未来将会有更多样式加入",
    "EditPlan": "编辑方案",
    "MonitoringStylePlan": "监控样式方案",
    "AddDesktopMonitoring": "添加桌面监控",
    "TextLabel": "文本标签",
    "ImageVideo1": "图片、视频",
    "SensorGraphics": "传感器图形",
    "SensorData": "传感器数据",
    "CustomText": "自定义文本",
    "DateTime": "日期时间",
    "Image": "图片",
    "Video": "视频",
    "SVG": "SVG",
    "ProgressBar": "进度条",
    "Graphics": "图形",
    "UploadImage": "上传图片",
    "UploadVideo": "上传视频",
    "RealTimeMonitoring": "实时监控CPU、GPU温度与占用等数据，自由拖拽布局，个性化风格定制，祝你掌控性能与桌面美学",
    "Chart": "图表",
    "Zigzagcolor": "折线颜色(起点)",
    "Zigzagcolor1": "折线颜色(终点)",
    "Zigzagcolor2": "折线图区域颜色(起点)",
    "Zigzagcolor3": "折线图区域颜色(终点)",
    "CustomMonitoring": "自定义监控"
  }
}
//messageEnd 
 export default cn 