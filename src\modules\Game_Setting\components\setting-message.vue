<template>
  <!--消息通知-->
  <div id="xx"></div>
  <el-collapse-item :title="$t('Setting.messageNotification')" name="3">
    <div class="setting-item">
      <section class="left-box">
        <p class="setting-item-title">{{$t('Setting.message')}}</p>
        <el-checkbox v-model="store.state.ingameMsg" @change="store.actions.setInGameMsg"><span style="font-size: .12rem;">{{$t('Setting.enableInGameNotifications')}}</span></el-checkbox>

        <p class="setting-item-title mt-25" style="color: #ffffff;">{{$t('Setting.messagePosition')}}</p>
        <div class="ingame">
          <div class="moniWindow">
            <div class="moniWindowHeader">
              <div class="point"></div>
              <div class="point"></div>
              <div class="point"></div>
            </div>
            <div class="click-box" @click="setIngamePosition('left top')" style="top: 10.5px;left: 0;"></div>
            <div class="click-box" @click="setIngamePosition('left center')" style="top: 77px;left: 0;"></div>
            <div class="click-box" @click="setIngamePosition('left bottom')" style="top: 140px;left: 0;"></div>
            <div class="click-box" @click="setIngamePosition('right top')" style="top: 10.5px;left: 280px;"></div>
            <div class="click-box" @click="setIngamePosition('right center')" style="top: 77px;left: 280px;"></div>
            <div class="click-box" @click="setIngamePosition('right bottom')" style="top: 140px;left: 280px;"></div>
            <div class="box" :style="{top: box.top + 'px',left: box.left + 'px'}"></div>
          </div>

          <!--<ul>-->
          <!--  &lt;!&ndash;左上角&ndash;&gt;-->
          <!--  <li @click="setIngamePosition('left top')" :class="{'active':store.state.ingameMsgPosition === 'left top'}" :title="$t('Setting.leftTop')">{{$t('Setting.leftTop')}}</li>-->
          <!--  &lt;!&ndash;左中间&ndash;&gt;-->
          <!--  <li @click="setIngamePosition('left center')" :class="{'active':store.state.ingameMsgPosition === 'left center'}" :title="$t('Setting.leftCenter')">{{$t('Setting.leftCenter')}}</li>-->
          <!--  &lt;!&ndash;左下角&ndash;&gt;-->
          <!--  <li @click="setIngamePosition('left bottom')" :class="{'active':store.state.ingameMsgPosition === 'left bottom'}" :title="$t('Setting.leftBottom')">{{$t('Setting.leftBottom')}}</li>-->
          <!--  &lt;!&ndash;右上角&ndash;&gt;-->
          <!--  <li @click="setIngamePosition('right top')" :class="{'active':store.state.ingameMsgPosition === 'right top'}" :title="$t('Setting.rightTop')" >{{$t('Setting.rightTop')}}</li>-->
          <!--  &lt;!&ndash;右中间&ndash;&gt;-->
          <!--  <li @click="setIngamePosition('right center')" :class="{'active':store.state.ingameMsgPosition === 'right center'}" :title="$t('Setting.rightCenter')">{{$t('Setting.rightCenter')}}</li>-->
          <!--  &lt;!&ndash;右下角&ndash;&gt;-->
          <!--  <li @click="setIngamePosition('right bottom')" :class="{'active':store.state.ingameMsgPosition === 'right bottom'}" :title="$t('Setting.rightBottom')">{{$t('Setting.rightBottom')}}</li>-->
          <!--</ul>-->
        </div>
      </section>
      <section class="right-box">
        <p class="setting-item-title">{{$t('Setting.noticeContent')}}</p>
        <el-checkbox @change="(e)=>handleIngameMsgChange(e,100739)" v-model="store.state.ingame_injection"><span style="font-size: .12rem;">{{$t('Setting.ingameShow')}}</span></el-checkbox>
        <br>
        <el-checkbox @change="(e)=>handleIngameMsgChange(e,100740)" v-model="store.state.ingame_monitor"><span style="font-size: .12rem;">{{$t('Setting.inGameMonitoring')}}</span></el-checkbox>
        <br>
        <el-checkbox @change="(e)=>handleIngameMsgChange(e,100741)" v-model="store.state.ingame_filter"><span style="font-size: .12rem;">{{$t('Setting.gameFilter')}}</span></el-checkbox>
        <br>
        <el-checkbox @change="(e)=>handleIngameMsgChange(e,100743)" v-model="store.state.ingame_statistics"><span style="font-size: .12rem;">{{$t('Setting.start')}}/{{$t('Setting.endMarkStatistics')}}</span></el-checkbox>
          <br>
        <el-checkbox v-model="store.state.ingame_record"><span style="font-size: .12rem;">{{$t('video.videoRecord')}}</span></el-checkbox>
          <br>
        <el-checkbox v-model="store.state.ingame_screenshot"><span style="font-size: .12rem;">{{$t('screenshotpage.screenshot')}}</span></el-checkbox>

      </section>
    </div>
  </el-collapse-item>
</template>

<script setup lang="ts">
import {computed, onMounted, ref} from 'vue'
import {gameppBaseSetting} from '@/modules/Game_Home/stores/index'
import {useScroll} from '../hooks/useScroll'
import {GPP_SendStatics} from "@/uitls/sendstatics"
useScroll('xx');
const store = gameppBaseSetting();

let box = ref({
  top: 9,
  left: 0
})

onMounted(() => {
   setTimeout(()=>{
     setIngamePosition(store.state.ingameMsgPosition)
   },200)
})

function handleIngameMsgChange (e:any,n:number) {
  if (!e) {
    GPP_SendStatics(n)
  }
}

function setIngamePosition(position:string) {
  store.state.ingameMsgPosition = position
  if (position === 'left top') {
    GPP_SendStatics(100732)
    box.value.top = 10.5
    box.value.left = 0
  }else if(position === 'left center') {
    GPP_SendStatics(100734)
    box.value.top = 77
    box.value.left = 0
  }else if(position === 'left bottom') {
    GPP_SendStatics(100736)
    box.value.top = 140
    box.value.left = 0
  }else if(position === 'right top') {
    GPP_SendStatics(100733)
    box.value.top = 10.5
    box.value.left = 280
  }else if(position === 'right center') {
    GPP_SendStatics(100735)
    box.value.top = 77
    box.value.left = 280
  }else if(position === 'right bottom') {
    GPP_SendStatics(100737)
    box.value.top = 140
    box.value.left = 280
  }
}
</script>

<style scoped lang="scss">
#xx {
  margin-top: 20px;
}

.ingame {
  display: flex;
  flex-flow: row nowrap;

  .moniWindow {
    width: 320px;
    height: 180px;
    background: #22232E;
    border-radius: 2px;
    position: relative;

    .moniWindowHeader {
      width: 320px;
      height: 10px;
      background: #71738C;
      border-radius: 2px;
      display: flex;
      align-items: center;
      flex-direction: row;
      justify-content: flex-end;

      .point {
        width: 6px;
        height: 6px;
        background: #FFFFFF;
        border-radius: 50%;
        margin-right: 3px;
      }
    }

    .box {
      width: 40px;
      height: 40px;
        background: #3578d5;
        border-radius: 2px;
        border: 1px solid #ffffff;
      position: absolute;
      transition: all .3s;
    }
    .click-box {
        width: 40px;
        height: 40px;
        background: #33343f;
        border-radius: 2px;
        position: absolute;
        transition: all .3s;
        cursor: pointer;

        &:hover {
            background: #525573;
        }
    }
  }

  ul {
    display: flex;
    flex-flow: column wrap;
    height: 120px;
  }

  ul li {
    width: 80px;
    height: 30px;
    background: #22232E;
    border-radius: 2px;
    text-align: center;
    line-height: 30px;
    margin-left: 10px;
    margin-bottom: 10px;
    color: var(--font-gray);
    cursor: pointer;
    border: 2px solid transparent;
    white-space: nowrap;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    &.active, &:hover {
      width: 80px;
      height: 30px;
      background: #22232E;
      border-radius: 2px;
      border: 2px solid var(--active-color);
    }
  }
}
</style>
