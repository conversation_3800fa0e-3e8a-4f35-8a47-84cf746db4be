<script setup lang="ts">
import * as echarts from 'echarts';
import {computed, onMounted, reactive, ref,watch,inject} from 'vue'
import {getHoursMinutesSeconds, MathArrayAvg2, totalSeconds,MathArrayMin2,MathArrayMax2,inputFormatter,FormatTime} from "./someScripts"
import Shortcut from "@/components/eleCom/shortcut.vue";
import RightClickMenu from "@/modules/Game_ReboundDetail/components/RightClickMenu.vue";
import ColorPickerCover from "@/modules/Game_ReboundDetail/components/ColorPickerCover.vue";
import {useReboundDetailStore,dropdown_menu as dm} from "@/modules/Game_ReboundDetail/stores/reboundDetail";
import {storeToRefs} from "pinia";
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const $store = useReboundDetailStore()
const {startTime,endTime,endTimeOriginal,gameTime} = storeToRefs($store)
const {handleInputTime,handlePointCommand} = $store
console.log($store)
const zoomValue = inject('zoomV')
const props = defineProps({
  hardwaerinfo: {
    type: Object,
    required: true
  },
  recentGameInfo: {
    type: Object,
    required: true
  },
  powerData: {
    type: Object,
    required: true
  },
})

const showData = ref({name: 'fps', value: 'FPS',value2:""}) // 现在显示的什么数据
const dropdown_menu = ref([...dm]);

const colors = reactive({
  min: '#0089E9',
  max: '#CE3030',
  avg: '#4AB54F',
  fps1: '#5DFFEE',
  fps01: '#FFFD55'
})

const echarts_Max = ref(0)
const echarts_Avg = ref(0)
const echarts_Min = ref(0)
let echartsInstance: any = null
const showFps1 = ref(true)
const showFps01 = ref(true)
let echartsShowIndexStart = 0;
let echartsShowIndexEnd = -1;
const dropdown_point_img = ref();
const dropdown_point_index = ref(-1);
const curDisplayDataCount = ref(0);
const elColorPickersFps1 = ref()
const elColorPickersFps01 = ref()
onMounted(() => {
  initColors();
  loadShowOrHideFps();
  getEndTime();
  initEcharts();
  const bc = new BroadcastChannel('fps_limit')
  bc.onmessage = ()=>{
    echartsSetOption()
  }
})

function FormatSeconds(value: any, second: any) {
    let theTime: number = parseInt(value);// 秒
    let theTime1: number = 0;// 分
    let theTime2: number = 0;// 小时
    if (theTime > 60) {
        theTime1 = parseInt(String(theTime / 60));
        theTime = parseInt(String(theTime % 60));
        if (theTime1 > 60) {
            theTime2 = parseInt(String(theTime1 / 60));
            theTime1 = parseInt(String(theTime1 % 60));
        }
    }
    let result = "";
    if (second) {
        result = "" + parseInt(String(theTime)) + t('shutdownTimer.sec');
    }
    if (theTime1 > 0) {
        result = "" + parseInt(String(theTime1)) + t('screenshotpage.minutes') + result;
    }
    if (theTime2 > 0) {
        result = "" + parseInt(String(theTime2)) + t('shutdownTimer.hours') + result;
    }
    return result;
}

const initEcharts = () => {
  echartsInstance = echarts.init(document.getElementById('simple-data-charts'))
  echartsSetOption()
}

let trytimes= 0
const echartsSetOption = () => {
  if (props.recentGameInfo.starttime && props.powerData.fps && props.powerData.full && props.powerData.full.fps) {
    let checkName = showData.value.name;
    let checkValue = showData.value.value + showData.value.value2;
    const PM_GetDetailsARR = props.powerData.full;
    let DetailArr;
    if (PM_GetDetailsARR[checkName]) {
      if (checkName === 'memory' || checkName === 'fps' || checkName === 'cpupower' || checkName === 'frametime' || checkName === 'frametime') {
        DetailArr = PM_GetDetailsARR[checkName]['detail'];
      } else if (checkName === 'gpuload' || checkName === 'gpuload1' || checkName === 'gputemperature' || checkName === 'gpumemoryload' || checkName === 'gpuclock' || checkName === 'gpupower') {
        if (PM_GetDetailsARR[checkName].length === 2) {
          if (PM_GetDetailsARR[checkName][0]['avg'] <= 0) {
            DetailArr = PM_GetDetailsARR[checkName][1]['detail'];
          } else {
            if (PM_GetDetailsARR.gpushadres !== undefined) {
              if (PM_GetDetailsARR.gpushadres[0] < PM_GetDetailsARR.gpushadres[1]) {
                DetailArr = PM_GetDetailsARR[checkName][1]['detail'];
              } else {
                DetailArr = PM_GetDetailsARR[checkName][0]['detail'];
              }
            } else {
              DetailArr = PM_GetDetailsARR[checkName][1]['detail'];
            }
          }
        } else {
          DetailArr = PM_GetDetailsARR[checkName][0]['detail'];
        }
      } else {
        DetailArr = PM_GetDetailsARR[checkName]['detail'];
      }
    }
    if (DetailArr !== null) {
      if (startTime.value.h != 0 || startTime.value.m != 0 || startTime.value.s != 0) {
        echartsShowIndexStart = Math.ceil(totalSeconds(startTime.value) / gameTime.value * DetailArr.length)
      }
      if (startTime.value.h == 0 && startTime.value.m == 0 && startTime.value.s == 0) {
        echartsShowIndexStart = 0
      }
      if (totalSeconds(endTime.value) < totalSeconds(endTimeOriginal.value)) {
        echartsShowIndexEnd = Math.ceil(totalSeconds(endTime.value) / gameTime.value * DetailArr.length)
      } else {
        echartsShowIndexEnd = -1
      }
      DELoadGraph(DetailArr, checkValue, checkName, PM_GetDetailsARR.fps1['detail'], PM_GetDetailsARR.fps01['detail']);
    } else {
      // @ts-ignore
      ElMessage(checkValue + '没有详细数据!');
    }
  } else {
    setTimeout(() => {
      trytimes++
      if (trytimes > 30) {
        return
      }
      echartsSetOption()
    }, 100)
  }
}

const isShowEmptyMsg = ref(false)
//加载折线图
function DELoadGraph(DetailArr: any, checkValue: string, CheckName: string, fps1 = [], fps01 = []) {
  if (DetailArr.length === 0) {
    // @ts-ignore
    ElMessage(t('GameRebound.noCurData'));
    isShowEmptyMsg.value = true;
  }else{
    isShowEmptyMsg.value = false;
  }

  let maxN = computedFullDataMax(DetailArr).toFixed(0);
  let minN = computedFullDataMin(DetailArr).toFixed(0);
  let avgN = computedFullDataAvg(DetailArr).toFixed(0);

  echarts_Max.value = Number(maxN)
  echarts_Min.value = Number(minN)
  echarts_Avg.value = Number(avgN)

  let color1 = '#0089E9';
  let color2 = 'rgba(0, 137, 233, 0.4)';
  let DataUnit = ''
  if (CheckName === 'cpuload' || CheckName === 'gpuload' || CheckName === 'memory') {
    DataUnit = ' %';
  } else if (CheckName === 'cputemperature' || CheckName === 'gputemperature') {
    DataUnit = ' ℃';
  } else if (CheckName === 'cpupower' || CheckName === 'gpupower') {
    DataUnit = ' W';
  } else {
    DataUnit = '';
  }
  const errsSet = new Set($store.powerData.errs)
  console.log(errsSet);

  let filterFn = (value: number, index: number)=>{
    if (errsSet.has(index)) {
      return false;
    }
    if (echartsShowIndexStart > 0 && index < echartsShowIndexStart) {
      return false
    }
    if (echartsShowIndexEnd != -1 && index > echartsShowIndexEnd) {
      return false
    }
    return true;
  }
  let DisplayArrData = DetailArr.filter(filterFn);
  let displayFormatterObj = JSON.parse(JSON.stringify($store.powerData.full))

  let _gpuarr = ['gpuload','gpuload1','gputemperature','gpumemoryload','gpuclock','gpupower','gpuvoltage']
  Object.keys(displayFormatterObj).forEach((k)=>{
    if (isObject(displayFormatterObj[k]) && Array.isArray(displayFormatterObj[k]['detail'])) {
      displayFormatterObj[k]['detail'] = displayFormatterObj[k]['detail'].filter(filterFn)
    }else if (Array.isArray(displayFormatterObj[k]) && k !== 'errs' && k !== 'errs_reason') {
      if (_gpuarr.includes(k)) {
        displayFormatterObj[k] = displayFormatterObj[k].map(item=>{
          item.detail = item.detail.filter(filterFn)
          return item;
        })
      }else{
        displayFormatterObj[k] = displayFormatterObj[k].filter(filterFn)
      }
    }
  });

  let OnePoint = '';
  for (let i = 0; i < (DisplayArrData.length) - 1; i++) {
    OnePoint += ',';
  }
  let FormartXAxisData = OnePoint.split(',');
  curDisplayDataCount.value = DisplayArrData.length
  let option:any = null;
  option = {
    title: {},
    tooltip: {
      trigger: 'axis',
      formatter: function (data: any) {
        if ((window as any).isScreenshot === true) return '';
        const PM_GetDetailsARR = displayFormatterObj;
        if (PM_GetDetailsARR.gpuload !== null && PM_GetDetailsARR.cpuload !== null) {
          let ArrSub = data[0].dataIndex;
          let TotalGameTiem: any = gameTime.value + '';
          let StartTime = props.recentGameInfo.starttime;
          let TotalPoint = DetailArr.length;
          if (!rightClickOptions.show) {
            rightClickOptions.ArrSub = ArrSub
            rightClickOptions.dataIndex = data[0].dataIndex
            rightClickOptions.duration = PM_GetDetailsARR.duration[ArrSub];
          }

          let AllDataHtml = '<div class="AllDataHtml">'
          if (showData.value.name === 'fps') {
            AllDataHtml += '<span style="color: #FFFFFF">FPS : </span>' + (PM_GetDetailsARR.fps['detail'][ArrSub]?PM_GetDetailsARR.fps['detail'][ArrSub]:t('GameRebound.noData')) + '<br/>';
          }else{
            AllDataHtml += '<span style="color: #777777">FPS : </span>' + (PM_GetDetailsARR.fps['detail'][ArrSub]?PM_GetDetailsARR.fps['detail'][ArrSub]:t('GameRebound.noData')) + '<br/>';
          }

          if (PM_GetDetailsARR.frametime['detail'] !== null) {
            if (showData.value.name === 'frametime') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.FrameGenerationTime')} : </span>` + (PM_GetDetailsARR.frametime['detail'][ArrSub]?(PM_GetDetailsARR.frametime['detail'][ArrSub]+'ms'):t('GameRebound.noData')) + '<br/>';
            }else{
              AllDataHtml += `<span style="color: #777777">${t('GameRebound.FrameGenerationTime')} : </span>` + (PM_GetDetailsARR.frametime['detail'][ArrSub]?(PM_GetDetailsARR.frametime['detail'][ArrSub]+'ms'):t('GameRebound.noData')) + '<br/>';
            }
          }

          if (PM_GetDetailsARR.fps1['detail'] !== null) {
            if (showData.value.name === 'fps1') {
              AllDataHtml += '<span style="color: #FFFFFF">FPS 1% Low : </span>' + (PM_GetDetailsARR.fps1['detail'][ArrSub]?PM_GetDetailsARR.fps1['detail'][ArrSub]:t('GameRebound.noData')) + '<br/>';
            }else{
              AllDataHtml += '<span style="color: #777777">FPS 1% Low : </span>' + (PM_GetDetailsARR.fps1['detail'][ArrSub]?PM_GetDetailsARR.fps1['detail'][ArrSub]:t('GameRebound.noData')) + '<br/>';
            }
          }

          if (PM_GetDetailsARR.fps01['detail'] !== null) {
            if (showData.value.name === 'fps01') {
              AllDataHtml += '<span style="color: #FFFFFF">FPS 0.1% Low : </span>' + (PM_GetDetailsARR.fps01['detail'][ArrSub]?PM_GetDetailsARR.fps01['detail'][ArrSub]:t('GameRebound.noData'))+ '<br/>';
            } else {
              AllDataHtml += '<span style="color: #777777">FPS 0.1% Low : </span>' + (PM_GetDetailsARR.fps01['detail'][ArrSub]?PM_GetDetailsARR.fps01['detail'][ArrSub]:t('GameRebound.noData'))+ '<br/>';
            }
          }

          if (showData.value.name === 'cpuload') {
            AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.ProcessorOccupancy')}` + ' : </span>' + PM_GetDetailsARR.cpuload['detail'][ArrSub] + '%' + '<br/>';
          } else {
            AllDataHtml += `<span style="color: #777777">${t('GameRebound.ProcessorOccupancy')}` + ' : </span>' + PM_GetDetailsARR.cpuload['detail'][ArrSub] + '%' + '<br/>';
          }

          if (PM_GetDetailsARR.cpuloadP && PM_GetDetailsARR.cpuloadP.avg !== 0) {
            if (showData.value.name === 'cpuloadP') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.ProcessorOccupancy')}-P` + ' : </span>' + PM_GetDetailsARR.cpuloadP['detail'][ArrSub] + '%' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777777">${t('GameRebound.ProcessorOccupancy')}-P` + ' : </span>' + PM_GetDetailsARR.cpuloadP['detail'][ArrSub] + '%' + '<br/>';
            }
          }
          if (PM_GetDetailsARR.cpuloadE && PM_GetDetailsARR.cpuloadE.avg !== 0) {
            if (showData.value.name === 'cpuloadE') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.ProcessorOccupancy')}-E` + ' : </span>' + PM_GetDetailsARR.cpuloadE['detail'][ArrSub] + '%' + '<br/>';
            }else{
              AllDataHtml += `<span style="color: #777777">${t('GameRebound.ProcessorOccupancy')}-E` + ' : </span>' + PM_GetDetailsARR.cpuloadE['detail'][ArrSub] + '%' + '<br/>';
            }
          }

          if (showData.value.name === 'cpuclock') {
            AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.ProcessorFrequency')}` + ' : </span>' + PM_GetDetailsARR.cpuclock['detail'][ArrSub] + 'MHz' + '<br/>';
          } else {
            AllDataHtml += `<span style="color: #777777">${t('GameRebound.ProcessorFrequency')}` + ' : </span>' + PM_GetDetailsARR.cpuclock['detail'][ArrSub] + 'MHz' + '<br/>';
          }
          if (PM_GetDetailsARR.cpuclockP && PM_GetDetailsARR.cpuclockP.avg !== 0) {
            if (showData.value.name === 'cpuclockP') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.ProcessorFrequency')}-P` + ' : </span>' + PM_GetDetailsARR.cpuclockP['detail'][ArrSub] + 'MHz' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777777">${t('GameRebound.ProcessorFrequency')}-P` + ' : </span>' + PM_GetDetailsARR.cpuclockP['detail'][ArrSub] + 'MHz' + '<br/>';
            }
          }
          if (PM_GetDetailsARR.cpuclockE && PM_GetDetailsARR.cpuclockE.avg !== 0) {
            if (showData.value.name === 'cpuclockE') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.ProcessorFrequency')}-E` + ' : </span>' + PM_GetDetailsARR.cpuclockE['detail'][ArrSub] + 'MHz' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777777">${t('GameRebound.ProcessorFrequency')}-E` + ' : </span>' + PM_GetDetailsARR.cpuclockE['detail'][ArrSub] + 'MHz' + '<br/>';
            }
          }
          if (showData.value.name === 'cputemperature') {
            AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.ProcessorTemperature')}` + ' : </span>' + PM_GetDetailsARR.cputemperature['detail'][ArrSub] + '℃' + '<br/>';
          } else {
            AllDataHtml += `<span style="color: #777777">${t('GameRebound.ProcessorTemperature')}` + ' : </span>' + PM_GetDetailsARR.cputemperature['detail'][ArrSub] + '℃' + '<br/>';
          }
          if (showData.value.name === 'cpupower') {
            AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.ProcessorHeatPower')}` + ' : </span>' + PM_GetDetailsARR.cpupower['detail'][ArrSub] + 'W' + '<br/>';
          } else {
            AllDataHtml += `<span style="color: #777777">${t('GameRebound.ProcessorHeatPower')}` + ' : </span>' + PM_GetDetailsARR.cpupower['detail'][ArrSub] + 'W' + '<br/>';
          }

          let GpuArrlocation = 0;
          if (PM_GetDetailsARR.gpuload.length > 1) {
            if (PM_GetDetailsARR.gpushadres && (PM_GetDetailsARR.gpushadres).length === 2) {
              if (PM_GetDetailsARR.gpushadres[0] < PM_GetDetailsARR.gpushadres[1]) {
                GpuArrlocation = 1;
              }
            }
          }

          if (PM_GetDetailsARR.gpuload[GpuArrlocation]['detail'] !== null) {
            if (showData.value.name === 'gpuload') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.GraphicsCardOccupancy')}` + ' : </span>' + PM_GetDetailsARR.gpuload[GpuArrlocation]['detail'][ArrSub] + '%' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777777">${t('GameRebound.GraphicsCardOccupancy')}` + ' : </span>' + PM_GetDetailsARR.gpuload[GpuArrlocation]['detail'][ArrSub] + '%' + '<br/>';
            }
          }

          if (PM_GetDetailsARR.gpuload1 && PM_GetDetailsARR.gpuload1[GpuArrlocation]['detail'] !== null) {
            if (showData.value.name === 'gpuload1') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.GraphicsCardOccupancyTotal')}` + ' : </span>' + PM_GetDetailsARR.gpuload1[GpuArrlocation]['detail'][ArrSub] + '%' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777777">${t('GameRebound.GraphicsCardOccupancyTotal')}` + ' : </span>' + PM_GetDetailsARR.gpuload1[GpuArrlocation]['detail'][ArrSub] + '%' + '<br/>';
            }
          }

          if (showData.value.name === 'gpuclock') {
            AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.GraphicsCardFrequency')}` + ' : </span>' + PM_GetDetailsARR.gpuclock[GpuArrlocation]['detail'][ArrSub] + 'MHz' + '<br/>';
          } else {
            AllDataHtml += `<span style="color: #777777">${t('GameRebound.GraphicsCardFrequency')}` + ' : </span>' + PM_GetDetailsARR.gpuclock[GpuArrlocation]['detail'][ArrSub] + 'MHz' + '<br/>';
          }

          if (PM_GetDetailsARR.gputemperature && PM_GetDetailsARR.gputemperature[GpuArrlocation]['detail'] !== null) {
            if (showData.value.name === 'gputemperature') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.GraphicsCardTemperature')}` + ' : </span>' + PM_GetDetailsARR.gputemperature[GpuArrlocation]['detail'][ArrSub] + '℃' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777777">${t('GameRebound.GraphicsCardTemperature')}` + ' : </span>' + PM_GetDetailsARR.gputemperature[GpuArrlocation]['detail'][ArrSub] + '℃' + '<br/>';
            }
          }

          if (PM_GetDetailsARR.gpuhotspottemp && PM_GetDetailsARR.gpuhotspottemp['detail'] !== null) {
            if (showData.value.name === 'gpuhotspottemp') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.GraphicsCardCoreTemperature')}` + ' : </span>' + PM_GetDetailsARR.gpuhotspottemp['detail'][ArrSub] + '℃' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777777">${t('GameRebound.GraphicsCardCoreTemperature')}` + ' : </span>' + PM_GetDetailsARR.gpuhotspottemp['detail'][ArrSub] + '℃' + '<br/>';
            }
          }

          if (showData.value.name === 'gpupower') {
            AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.GraphicsCardHeatPower')}` + ' : </span>' + PM_GetDetailsARR.gpupower[GpuArrlocation]['detail'][ArrSub] + 'W' + '<br/>';
          } else {
            AllDataHtml += `<span style="color: #777777">${t('GameRebound.GraphicsCardHeatPower')}` + ' : </span>' + PM_GetDetailsARR.gpupower[GpuArrlocation]['detail'][ArrSub] + 'W' + '<br/>';
          }

          if (PM_GetDetailsARR.gpumemoryload && PM_GetDetailsARR.gpumemoryload[GpuArrlocation]['detail'] !== null) {
            if (showData.value.name === 'gpumemoryload') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('hardwareInfo.VRAM')}` + ' : </span>' + PM_GetDetailsARR.gpumemoryload[GpuArrlocation]['detail'][ArrSub] + '%' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777777">${t('hardwareInfo.VRAM')}` + ' : </span>' + PM_GetDetailsARR.gpumemoryload[GpuArrlocation]['detail'][ArrSub] + '%' + '<br/>';
            }
          }

          if (PM_GetDetailsARR.gpumemorytemp && PM_GetDetailsARR.gpumemorytemp['detail'] !== null && PM_GetDetailsARR.gpumemorytemp['detail'][ArrSub]) {
            if (showData.value.name === 'gpumemorytemp') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.GraphicsCardMemoryTemperature')}` + ' : </span>' + PM_GetDetailsARR.gpumemorytemp['detail'][ArrSub] + '℃' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777777">${t('GameRebound.GraphicsCardMemoryTemperature')}` + ' : </span>' + PM_GetDetailsARR.gpumemorytemp['detail'][ArrSub] + '℃' + '<br/>';
            }
          }

          if (PM_GetDetailsARR.gpumemoryclock && PM_GetDetailsARR.gpumemoryclock['detail'] !== null) {
            if (showData.value.name === 'gpumemoryclock') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('hardwareInfo.VRAMFrequency')}` + ' : </span>' + PM_GetDetailsARR.gpumemoryclock['detail'][ArrSub] + 'MHz' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777777">${t('hardwareInfo.VRAMFrequency')}` + ' : </span>' + PM_GetDetailsARR.gpumemoryclock['detail'][ArrSub] + 'MHz' + '<br/>';
            }
          }

          if (showData.value.name === 'memory') {
            AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.MemoryOccupancy')}` + ' : </span>' + PM_GetDetailsARR.memory['detail'][ArrSub] + '%' + '<br/>';
          } else {
            AllDataHtml += `<span style="color: #777777">${t('GameRebound.MemoryOccupancy')}` + ' : </span>' + PM_GetDetailsARR.memory['detail'][ArrSub] + '%' + '<br/>';
          }

          if (PM_GetDetailsARR.memorytemperature && PM_GetDetailsARR.memorytemperature['detail'] !== null) {
            if (showData.value.name === 'memorytemperature') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.MemoryTemperature')}` + ' : </span>' + PM_GetDetailsARR.memorytemperature['detail'][ArrSub] + '℃' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777777">${t('GameRebound.MemoryTemperature')}` + ' : </span>' + PM_GetDetailsARR.memorytemperature['detail'][ArrSub] + '℃' + '<br/>';
            }
          }
          if (PM_GetDetailsARR.pageFaultDelta && PM_GetDetailsARR.pageFaultDelta['detail'] !== null) {
            if (showData.value.name === 'pageFaultDelta') {
              AllDataHtml += `<span style="color: #FFFFFF">${t('GameRebound.MemoryPageFaults')}` + ' : </span>' + PM_GetDetailsARR.pageFaultDelta['detail'][ArrSub]  + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777777">${t('GameRebound.MemoryPageFaults')}` + ' : </span>' + PM_GetDetailsARR.pageFaultDelta['detail'][ArrSub]  + '<br/>';
            }
          }

          let duration = PM_GetDetailsARR.duration[ArrSub]
          AllDataHtml += `<span style="color: #777777">${t('GameRebound.Duration')}` + ' : </span>' + FormatSeconds(duration, true) + '<br/>';
          AllDataHtml += `<span style="color: #777777">${t('GameRebound.Time')}` + ' : </span>' + FormatTime(Number((StartTime + duration))) + '<br/>' + '</div>';
          // 降频截图 打开既有
          // if(1>0){//判断加数组该点是否存在降频图片
          //     AllDataHtml += '降频截图：'+ '</br>'
          //     AllDataHtml +="<img style='width:140px;height:80px;margin-top:5px;margin-bottom:10px;cursor:pointer;' class='Frequencypic' onclick='Openthispic()'>";
          // }

          return AllDataHtml;
        } else {
          return null;
        }

      },
      confine: true,
      backgroundColor: 'rgb(44,44,51,0.8)',
      textStyle: {
        fontSize: '12',
        color: 'rgb(195,202,213)'
      },
      className: 'echarts-tooltip'
    },
    grid: {
      left: 0,
      right: 0,
      bottom: 0,
      top: '10%',
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: FormartXAxisData,
        axisLine: {
          lineStyle: {
            type: 'solid',
            color: '#333645',//左边线的颜色
            width: '1'//坐标线的宽度
          }
        },
        axisTick: { //x轴刻度线
          show: false
        },
        axisLabel: {
          textStyle: {
            color: '#333645'//坐标值得具体的颜色
          }
        },
        splitArea: {
          show: false,
          areaStyle: {
            color: [
              'rgba(37,39,47,.5)',
              'rgba(100,100,100,.5)'
            ]
          }
        },
        splitLine: {
          show: true,
          //  改变轴线颜色
          lineStyle: {
            // 使用深浅的间隔色
            color: ['#333645']
          }
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        // min: minN - 1,
        // max: maxN + 1,
        dataMax: "",
        axisLabel: {
          // formatter: '{value} °C',
          color: "#21222a"  //刻度线标签颜色
        },
        axisTick: { //y轴刻度线
          show: false
        },
        splitLine: {
          show: true,
          //  改变轴线颜色
          lineStyle: {
            // 使用深浅的间隔色
            color: ['#333645']
          }
        }
      }
    ],

    series: [
      {
        name: checkValue,
        type: 'line',
        symbol: 'circle',  //取消折点圆圈
        symbolSize: '2',
        stack: '总量',
        areaStyle: {normal: {}},
        data: DisplayArrData,
        cursor: 'default',
        itemStyle: {
          normal: {
//                        label : {show: true},
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                // 0% 处的颜色
                offset: 0, color: color1
              },
              {
                // 100% 处的颜色
                offset: 1, color: color2
              }
            ], false),
            borderColor: '#0089E9',
            opacity: 0,
            borderWidth: 1,
            lineStyle: {
              color: color1
            }

          },
          lineStyle: {
            // 使用深浅的间隔色
            color: ['#333645']
          },
          emphasis: {
            // borderColor:'red'
            color: '#39404d',
            borderColor: '#0089E9',
            opacity: 1,
          }
        },
        markLine: {
          // symbol: ['circle', 'none'],
          // label: {show: false},
          emphasis:{
            disabled: true,
          },
          data: [
            {
              type: 'average',
              name: '平均值',
              lineStyle: {
                color: colors.avg, // 设置颜色
                width: 1, // 设置线宽
                // type: 'dashed', // 设置虚线
                type: [4,4], // 设置虚线
                cap: 'round',
              },
            },
            [
              {
                symbol   : 'arrow',
                x        : '100%',
                yAxis    : 'max',
                lineStyle: {
                  color: colors.max,
                  width: 1, // 设置线宽
                  type: [4,4], // 设置虚线
                }
              },
              {
                symbol: 'circle',
                label : {
                  normal: {
                    position: 'start',
                    color: colors.max
                  }
                },
                type: 'max',
                name: '最大值'
              }
            ],
            [
              {
                symbol   : 'arrow',
                x        : '100%',
                yAxis    : 'min',
                lineStyle: {
                  color: colors.min,
                  width: 1, // 设置线宽
                  type: [4,4], // 设置虚线
                }
              },
              {
                symbol: 'circle',
                label : {
                  normal: {
                    position: 'start',
                    color: colors.min
                  }
                },
                type: 'min',
                name: '最小值'
              }
            ]
          ],
        }
      }
    ],
  };
  if (CheckName.includes('fps')) {
    if (showFps1.value && showData.value.name != 'fps1') { // 显示1% low fps
      let displayFps1 = fps1.filter(filterFn);
      option.series.push({
        name: '1% low',
        type: 'line',
        symbol: 'circle',  //取消折点圆圈
        symbolSize: '2',
        stack: 'fps1',
        data: displayFps1,
        cursor: 'default',
        lineStyle: {
          color: colors.fps1,
        },
        itemStyle: {
          normal: {
            color: colors.fps1,
            opacity: 0,
          },
        },
      } as any)
    }
    if (showFps01.value && showData.value.name != 'fps01') { // 显示0.1% low fps
      let displayFps01 = fps01.filter(filterFn);
      option.series.push({
        name: '1% low',
        type: 'line',
        symbol: 'circle',  //取消折点圆圈
        symbolSize: '2',
        stack: 'fps01',
        data: displayFps01,
        cursor: 'default',
        lineStyle: {
          color: colors.fps01,
        },
        itemStyle: {
          normal: {
            color: colors.fps01,
            opacity: 0,
          },
        },
      } as any)
    }
  }
  if (line_item.value && Object.keys(line_item.value).length > 0) { // point的竖线
    Object.keys(line_item.value).forEach(function (key: any) {
      if (Number(key) > echartsShowIndexStart && (Number(key) < echartsShowIndexEnd || echartsShowIndexEnd == -1)) {
          option.series[0].markLine.symbol = ['none', 'none']
         option.series[0].markLine.data.push({
          xAxis: Number(key) - echartsShowIndexStart,
          lineStyle: {
            type: 'solid',
            color: '#0089E9',
            lineWidth: 2
          },
          label: {
            show: false
          }
        })
      }
    })
  }
  if (option && typeof option === "object") {
    echartsInstance.setOption(option, true);
  }
}

function isObject (obj:any) {
  return obj && typeof obj === 'object' && !Array.isArray(obj)
}

function AVGNum(arr: Array<any>) {
  let sum = 0;
  let count = 0
  const errs = props.powerData.errs
  if (errs) {
    const errsSet = new Set(errs)
    for (let i = 0; i < arr.length; i++) {
      if (!errsSet.has(i)) {
        sum += arr[i]
        count++
      }
    }
  }

  if (count === 0) return 0;
  let avgValue = ~~(sum / count * 100) / 100;
  return parseInt(parseFloat(String(avgValue)).toFixed(0));
}

// (结束时间-开始时间差) / gameTime * 800
const line_width = computed(() => {
  const end = totalSeconds(endTime.value)
  const start = totalSeconds(startTime.value)
  return (end - start) / gameTime.value * 800 + 'px'
})
const line_left = computed(() => {
  const start = totalSeconds(startTime.value)
  return start / gameTime.value * 800 + 'px'
})
const line_item = computed(() => {
  if (props.powerData && props.powerData.points) {
    let map: any = {}
    const totalLen = props.powerData.points.length
    for (let i = 0; i < totalLen; i++) {
      const item = props.powerData.points[i]
      if (item !== null) {
        const dur = Math.ceil(Number((gameTime.value / totalLen) * i))
        const hms = getHoursMinutesSeconds(dur)
        map[i] = {
          imgurl: item,
          time: {
            ...hms
          },
          left: `${i / totalLen * 800}px`,
        }
      }
    }
    return map
  }
  return {}
})

let try_count = 0

function getEndTime() {
  if (props.recentGameInfo.starttime && !window.isGetEndTime) {
    gameTime.value = props.recentGameInfo.endtime - props.recentGameInfo.starttime
    const t = getHoursMinutesSeconds(gameTime.value)
    endTime.value.h = t.h
    endTime.value.m = t.m
    endTime.value.s = t.s
    endTimeOriginal.value.h = endTime.value.h
    endTimeOriginal.value.m = endTime.value.m
    endTimeOriginal.value.s = endTime.value.s
    window.isGetEndTime = true
  } else {
    try_count++
    if (try_count > 30) return
    setTimeout(getEndTime, 100)
  }
}

const initColors = () => {
  let local_colors: any = window.localStorage.getItem('reboundDetailColors')
  if (local_colors) {
    local_colors = JSON.parse(local_colors)
    for (let key in local_colors) {
      colors[key] = local_colors[key]
    }
  }
}

const handleColorChange = () => {
  window.localStorage.setItem('reboundDetailColors', JSON.stringify(colors))
  echartsSetOption()
}

const handleCommand = (command: any) => {
  //showData.value = command
  Object.assign(showData.value,command)
  echartsSetOption()
}

function isNumber(val: any) {
  return !isNaN(val) && !isNaN(parseFloat(val))
}

const displayHMSTime = (hms: { h: number, m: number, s: number }) => {
  return `${inputFormatter(hms.h)} : ${inputFormatter(hms.m)} : ${inputFormatter(hms.s)}`
}
const dropdownImgUrl = ref('')
const handleDropdownPointMouseIn = (event: MouseEvent, imgurl: string, type:string, index:number) => {
  setTimeout(() => {
    dropdown_point_index.value = index
    if (imgurl) {
      const {clientY} = event;
      dropdownImgUrl.value = imgurl
      if (type === 'start') {
        dropdown_point_img.value.style.left = 190 + 'px'
      } else {
        dropdown_point_img.value.style.left = 620 + 'px'
      }
      dropdown_point_img.value.style.top = clientY - 380 + 'px'
      dropdown_point_img.value.style.display = 'block'
    }
  })
}
const handleDropdownPointMouseLeave = () => {
  dropdown_point_img.value.style.display = 'none'
  dropdownImgUrl.value = ''
  dropdown_point_index.value = -1
}

function handlePointCommandWithSetOption(e: any, type: string) {
  const result = handlePointCommand(e,type)
  if (result) echartsSetOption()
}

function saveShowOrHideFps() {
  window.localStorage.setItem('reboundDetailShowFps1AndFps01', JSON.stringify({
    showFps1: showFps1.value,
    showFps01: showFps01.value
  }))
}

function loadShowOrHideFps() {
  const localData = window.localStorage.getItem('reboundDetailShowFps1AndFps01')
  if (localData) {
    const localDataObj = JSON.parse(localData)
    showFps1.value = localDataObj.showFps1
    showFps01.value = localDataObj.showFps01
  }
}
function handleColorPickerClick(type:string) {
  if (type === 'fps1') {
    if (showFps1.value) {
      hideFps1()
    }else{
      ShowFps1()
    }
  }
  if (type === 'fps01') {
    if (showFps01.value) {
      hideFps01()
    }else{
      ShowFps01()
    }
  }
}
function handleColorPickerContextmenu (type:string) {
  if (type === 'fps1') {
    elColorPickersFps1.value.show()
  }else if (type === 'fps01'){
    elColorPickersFps01.value.show()
  }
}
function hideFps1() {
  showFps1.value = false
  saveShowOrHideFps()
  echartsSetOption()
}

function ShowFps1() {
  showFps1.value = true
  saveShowOrHideFps()
  echartsSetOption()
}

function hideFps01() {
  showFps01.value = false
  saveShowOrHideFps()
  echartsSetOption()
}

function ShowFps01() {
  showFps01.value = true
  saveShowOrHideFps()
  echartsSetOption()
}

const rcm = ref()
const rightClickOptions = reactive({ // 一些开了右键菜单后需要的数据
  ArrSub: -1,
  dataIndex: -1,
  duration: 0,
  show: false,
  screenX: 0,
  screenY: 0,
  clientX: 0,
  clientY: 0,
})
function handleEchartsContextMenu(e:PointerEvent) {
  const {clientX, clientY, screenX, screenY} = e;
  const zoomLevel = zoomValue.value
  if (rcm.value.openMenu) {
    rightClickOptions.clientX = clientX/zoomLevel
    rightClickOptions.clientY = clientY/zoomLevel
    rightClickOptions.screenX = screenX/zoomLevel
    rightClickOptions.screenY = screenY/zoomLevel
    rightClickOptions.show = true
    rcm.value.openMenu(clientX, clientY, screenX, screenY)
  }
}

function handleRightClickMenuCommand(n:number) {
  const duration = Number(rightClickOptions.duration.toFixed(0))
  const hms = getHoursMinutesSeconds(duration)
  if (n === 1) { // 设置成开始时间
    startTime.value.h = hms.h
    startTime.value.m = hms.m
    startTime.value.s = hms.s
  } else { // 设置成结束时间
    endTime.value.h = hms.h
    endTime.value.m = hms.m
    endTime.value.s = hms.s
  }
  echartsSetOption()
}

function handleRightClickMenuClose() {
  rightClickOptions.show = false
}

function computedFullDataAvg(dataList:Array<number>) {
  if (!Array.isArray(dataList)) return 0
  if (dataList.length == 0) return 0
  let startIndex = 0
  let endIndex = 0
  if (startTime.value.h != 0 || startTime.value.m != 0 || startTime.value.s != 0) {
    startIndex = Math.ceil(totalSeconds(startTime.value) / gameTime.value * dataList.length)
  }
  if (startTime.value.h == 0 && startTime.value.m == 0 && startTime.value.s == 0) {
    startIndex = 0
  }
  if (totalSeconds(endTime.value) < totalSeconds(endTimeOriginal.value)) {
    endIndex = Math.ceil(totalSeconds(endTime.value) / gameTime.value * dataList.length)
  } else {
    endIndex = dataList.length
  }
  try {
    return MathArrayAvg2(dataList.slice(startIndex, endIndex),startIndex,props.powerData.errs)
  }catch (e) {
    return 0
  }
}
function computedFullDataMax(dataList:Array<number>) {
  let startIndex = 0
  let endIndex = 0
  if (startTime.value.h != 0 || startTime.value.m != 0 || startTime.value.s != 0) {
    startIndex = Math.ceil(totalSeconds(startTime.value) / gameTime.value * dataList.length)
  }
  if (startTime.value.h == 0 && startTime.value.m == 0 && startTime.value.s == 0) {
    startIndex = 0
  }
  if (totalSeconds(endTime.value) < totalSeconds(endTimeOriginal.value)) {
    endIndex = Math.ceil(totalSeconds(endTime.value) / gameTime.value * dataList.length)
  } else {
    endIndex = dataList.length
  }
  try {
    return MathArrayMax2(dataList.slice(startIndex, endIndex),startIndex,props.powerData.errs)
  }catch (e) {
    return 0
  }
}
function computedFullDataMin(dataList:Array<number>) {
  let startIndex = 0
  let endIndex = 0
  if (startTime.value.h != 0 || startTime.value.m != 0 || startTime.value.s != 0) {
    startIndex = Math.ceil(totalSeconds(startTime.value) / gameTime.value * dataList.length)
  }
  if (startTime.value.h == 0 && startTime.value.m == 0 && startTime.value.s == 0) {
    startIndex = 0
  }
  if (totalSeconds(endTime.value) < totalSeconds(endTimeOriginal.value)) {
    endIndex = Math.ceil(totalSeconds(endTime.value) / gameTime.value * dataList.length)
  } else {
    endIndex = dataList.length
  }
  try {
    return MathArrayMin2(dataList.slice(startIndex, endIndex),startIndex,props.powerData.errs)
  }catch (e) {
    return 0
  }
}
</script>

<template>
  <div class="simple-data-charts-container">
    <div class="charts-configs">
      <el-dropdown @command="handleCommand" trigger="click" max-height="200">
        <div class="drop-menu">
          <span :title="$t(showData.value)+showData.value2" style="display: inline-block;width: 75px;overflow: hidden;">{{ $t(showData.value)+showData.value2 }}</span>
          <div class="drop-menu-arrow-down">
            <span class="iconfont icon-hideshow"></span>
          </div>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item
                v-for="(item, index) in dropdown_menu"
                :key="item.name"
                :command="item"
            >{{ $t(item.value) + (item['value2']?item['value2']:"") }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <div class="shortcut-info">
        <span>{{ $t('GameRebound.AddPoint_m1') }}</span>
        <div>
          <shortcut :id="527" bg-color="#22232E" style="color: #ffffff;border: 1px solid #777;border-radius: 4px;"/>
        </div>
        <span>{{ $t('GameRebound.AddPoint_m2') }}</span>
      </div>

      <div class="avg-data">
        <div class="lowfps" v-if="showData.name.includes('fps') && showData.name!='fps1' && props.powerData.fps1"
             :style="{color:showFps1?colors.fps1:'#999999'}">
          <el-tooltip effect="light" :content="$t('GameRebound.LeftMouse')" placement="top">
            <div @contextmenu.prevent.stop="handleColorPickerContextmenu('fps1')" @click="handleColorPickerClick('fps1')">
              <ColorPickerCover :color="showFps1?colors.fps1:'#999999'"/>
              <el-color-picker v-model="colors.fps1" size="small" ref="elColorPickersFps1" popper-class="no-clear-icon" @change="ShowFps1();handleColorChange();"/>
            </div>
          </el-tooltip>
          <span class="lowval" v-show="showFps1">1% Low: {{ computedFullDataAvg(props.powerData.fps1.detail).toFixed(0) }}</span>
          <span class="lowval" v-show="!showFps1">{{ $t('GameRebound.Hidden') }}</span>
        </div>
        <div class="lowfps" v-if="showData.name.includes('fps') && showData.name!='fps01' && props.powerData.fps01"
             :style="{color:showFps01?colors.fps01:'#999999'}">
          <el-tooltip effect="light" :content="$t('GameRebound.LeftMouse')"  placement="top">
            <div @contextmenu.prevent.stop="handleColorPickerContextmenu('fps01')" @click="handleColorPickerClick('fps01')">
              <ColorPickerCover :color="showFps01?colors.fps01:'#999999'"/>
              <el-color-picker v-model="colors.fps01" size="small" ref="elColorPickersFps01" popper-class="no-clear-icon" @change="ShowFps01();handleColorChange();"/>
            </div>
          </el-tooltip>
          <span v-show="showFps01" class="lowval">0.1% Low: {{ computedFullDataAvg(props.powerData.fps01.detail).toFixed(0) }}</span>
          <span v-show="!showFps01" class="lowval">{{ $t('GameRebound.Hidden') }}</span>
        </div>
        <div class="min" :style="{color:colors.min}">
          <el-color-picker v-model="colors.min" size="small" popper-class="no-clear-icon" @change="handleColorChange"/>
          <span>Min : {{ echarts_Min }}</span>
        </div>
        <div class="max" :style="{color:colors.max}">
          <el-color-picker v-model="colors.max" size="small" popper-class="no-clear-icon" @change="handleColorChange"/>
          <span>Max : {{ echarts_Max }}</span>
        </div>
        <div class="avg" :style="{color:colors.avg}">
          <el-color-picker v-model="colors.avg" size="small" popper-class="no-clear-icon" @change="handleColorChange"/>
          <span>Average : {{ echarts_Avg }}</span>
        </div>
      </div>

      <div class="data-screening" style="margin-left: 30px;" @click="$store.openFpsLimitDialog">
          <span class="iconfont icon-parameter"></span>
          <span>{{$t('GameRebound.dataScreening')}}</span>
      </div>
    </div>
    <div class="charts-dur">
      <el-dropdown trigger="click" max-height="200" @command="(e)=>handlePointCommandWithSetOption(e,'start')">
        <div class="start-time">
          <el-input v-model="startTime.h" :formatter="inputFormatter" size="small"
                    @change="(e)=>{handleInputTime(e,'h','start');echartsSetOption();}"/>
          <span>:</span>
          <el-input v-model="startTime.m" :formatter="inputFormatter" size="small"
                    @change="(e)=>{handleInputTime(e,'m','start');echartsSetOption();}"
          />
          <span>:</span>
          <el-input v-model="startTime.s" :formatter="inputFormatter" size="small"
                    @change="(e)=>{handleInputTime(e,'s','start');echartsSetOption();}"
          />
          <span class="iconfont icon-hideshow"></span>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item :command="{time: {h:0,m:0,s:0}}">
              <div class="dropdown_point">
                <span>{{ $t('GameRebound.StartStatistics') }}</span>
                <span>00 : 00 : 00</span>
              </div>
            </el-dropdown-item>
            <el-dropdown-item
                v-for="(item, index) in Object.keys(line_item)"
                :key="item"
                :command="line_item[item]"
            >
              <div class="dropdown_point" @mouseenter="handleDropdownPointMouseIn($event, line_item[item].imgurl,'start',index)"
                   @mouseleave="handleDropdownPointMouseLeave">
                <span>{{ $t('GameRebound.Mark') }}{{ index + 1 }}</span>
                <span>{{ displayHMSTime(line_item[item].time) }}</span>
              </div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <div class="dur-process">
        <div class="line" :style="{width: line_width,left: line_left}">
        </div>
        <div
          v-for="(item,index) in Object.keys(line_item)"
          class="line-item"
          :class="{'is-active': (index === dropdown_point_index)}"
          :style="{left: line_item[item].left}"
        >
        </div>
      </div>
      <el-dropdown trigger="click" max-height="200" @command="(e)=>handlePointCommandWithSetOption(e,'end')">
        <div class="end-time">
          <el-input v-model="endTime.h" :formatter="inputFormatter" size="small"
                    @change="(e)=>{handleInputTime(e,'h','end');echartsSetOption()}"
          />
          <span>:</span>
          <el-input v-model="endTime.m" :formatter="inputFormatter" size="small"
                    @change="(e)=>{handleInputTime(e,'m','end');echartsSetOption()}"
          />
          <span>:</span>
          <el-input v-model="endTime.s" :formatter="inputFormatter" size="small"
                    @change="(e)=>{handleInputTime(e,'s','end');echartsSetOption()}"
          />
          <span class="iconfont icon-hideshow"></span>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item :command="{time: endTimeOriginal}">
              <div class="dropdown_point">
                <span>{{ $t('GameRebound.EndStatistics') }}</span>
                <span>{{ displayHMSTime(endTimeOriginal) }}</span>
              </div>
            </el-dropdown-item>
            <el-dropdown-item
                v-for="(item, index) in Object.keys(line_item)"
                :key="item"
                :command="line_item[item]"
            >
              <div class="dropdown_point" @mouseenter="handleDropdownPointMouseIn($event, line_item[item].imgurl,'end',index)"
                   @mouseleave="handleDropdownPointMouseLeave">
                <span>{{ $t('GameRebound.Mark') }}{{ index + 1 }}</span>
                <span>{{ displayHMSTime(line_item[item].time) }}</span>
              </div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <div class="charts-content">
      <div id="simple-data-charts" v-show="!isShowEmptyMsg" :style="{width: '1240px',height: '170px'}" @contextmenu="handleEchartsContextMenu"></div>
      <div v-show="isShowEmptyMsg" class="emptyBox">{{$t('GameRebound.noCurData')}}</div>
      <div class="charts-left-bottom">
        <div class="charts-left-bottom-box">
          <span>{{displayHMSTime(startTime)}}</span>
          <text>{{ $t('GameRebound.Totalsampling') }}</text>
          <span>{{curDisplayDataCount}}{{ $t('GameRebound.Items') }}</span>
        </div>
        <div class="charts-left-bottom-box" style="margin-left: auto;">
          <span >{{displayHMSTime(endTime)}}</span>
        </div>
      </div>
      <teleport to="body">
        <div class="right-click-markline" v-show="rightClickOptions.show" :style="{left: rightClickOptions.clientX+'px'}"></div>
      </teleport>
    </div>
    <div class="hardware-info">
      <h1>{{ $t('home.hardwareInfo') }}</h1>
      <section style="padding-top: 5px;">
        <div class="hardware-info-left">
          <!-- CPU -->
          <div class="hardware-item">
            <span class="iconfont icon-CPU hardware-item-label"></span>
            <span class="hardware-item-label wide" >CPU</span>
            <span class="hardware-item-name wide2" :title="props.hardwaerinfo.ProcessorName">{{ props.hardwaerinfo.ProcessorName }}</span>
            <div style="width: 135px;">
              <span class="hardware-item-label">{{ $t('hardwareInfo.coreCount') }}</span>
              <span
                  class="hardware-item-name">{{ props.hardwaerinfo.NumberofCPUCores }} {{
                  props.hardwaerinfo.CPUCores
                }}</span>
            </div>
            <span class="hardware-item-label">{{ $t('hardwareInfo.threadCount') }}</span>
            <span
                class="hardware-item-name">{{
                props.hardwaerinfo.NumberofLogicalCPUs
              }} {{ props.hardwaerinfo.LogicalCPUs }}</span>
          </div>

          <!-- GPU -->
          <div class="hardware-item">
            <span class="iconfont icon-GPU hardware-item-label"></span>
            <span class="hardware-item-label wide" >GPU</span>
            <span class="hardware-item-name wide2" :title="props.hardwaerinfo.GPU">{{ props.hardwaerinfo.GPU }}</span>
            <div style="width: 135px;">
              <span class="hardware-item-label">{{ $t('hardwareInfo.Videomemory') }}</span>
              <span class="hardware-item-name" :title="props.hardwaerinfo.GPU_VideoMemor">{{ props.hardwaerinfo.GPU_VideoMemor }}</span>
            </div>
            <span class="hardware-item-label">{{ $t('hardwareInfo.brand') }} </span>
            <span class="hardware-item-name" :title="props.hardwaerinfo.GPU_Brand">{{ props.hardwaerinfo.GPU_Brand }}</span>
          </div>
          <!-- GPU驱动版本 -->
          <div class="hardware-item">
            <span class="hardware-item-label" style="margin-left: 80px;">{{ $t('GameRebound.edition') }}</span>
            <span class="hardware-item-name">{{ props.hardwaerinfo.GPU_DriverVersion }}</span>
          </div>

          <!-- 内存 -->
          <div class="hardware-item DRAM">
            <div style="width: 79px;">
              <span class="iconfont icon-Dram hardware-item-label"></span>
              <span class="hardware-item-label wide" >DRAM</span>
            </div>
            <span class="hardware-item-label">{{ $t('hardwareInfo.totalSize') }}</span>
            <span class="hardware-item-name">{{ props.hardwaerinfo.Memory_size }}</span>
            <span class="hardware-item-label">{{ $t('hardwareInfo.memoryBarCount') }}</span>
            <span class="hardware-item-name">{{ props.hardwaerinfo.Memory_count }}</span>
            <span class="hardware-item-label">{{ $t('hardwareInfo.channelCount') }}</span>
            <span class="hardware-item-name">{{ props.hardwaerinfo.Memory_channels_active }}</span>
            <span class="hardware-item-label">{{ $t('hardwareInfo.frequency') }}： </span>
            <span class="hardware-item-name">{{ props.hardwaerinfo.Memory_Clock }}</span>
            <span class="hardware-item-label">{{ $t('GameRebound.Timing') }}</span>
            <span class="hardware-item-name">{{ props.hardwaerinfo.Memory_CurrentTiming }}</span>
          </div>
          <div class="dram-list scroll">
            <div v-for="(item,index) in props.hardwaerinfo.memory_list1" :key="'dd'+index">
              {{ item }} x{{ props.hardwaerinfo.memory_list2[index] ? props.hardwaerinfo.memory_list2[index] : '1' }}
            </div>
          </div>
        </div>
        <div class="hardware-info-right">
          <!-- 系统 -->
          <div class="hardware-item">
            <span class="iconfont icon-nav_toolbox hardware-item-label"></span>
            <span class="hardware-item-label wide" >{{ $t('GameRebound.System') }}</span>
            <span class="hardware-item-name">{{ props.hardwaerinfo.SystemName }}</span>
          </div>
          <!-- 主板 -->
          <div class="hardware-item">
            <span class="iconfont icon-board hardware-item-label"></span>
            <span class="hardware-item-label wide" >{{ $t('hardwareInfo.motherboard') }}</span>
            <span class="hardware-item-name wide3" >{{ props.hardwaerinfo.MainboardName }}</span>
            <span class="hardware-item-label">{{ $t('hardwareInfo.BIOSVersion') }}：</span>
            <span class="hardware-item-name">{{ props.hardwaerinfo.BIOSVersion }}</span>
          </div>
          <!-- 硬盘 -->
          <div class="hardware-item">
            <span class="iconfont icon-Harddisk hardware-item-label"></span>
            <span class="hardware-item-label wide" >{{ $t('GameRebound.MainHardDisk') }}</span>
            <span class="hardware-item-name wide2" :title="props.hardwaerinfo.gameDiskName">{{ props.hardwaerinfo.gameDiskName }}</span>
            <span class="hardware-item-label">{{ $t('GameRebound.Interface') }}</span>
            <span class="hardware-item-name">{{ props.hardwaerinfo.DriveController }}</span>
          </div>
          <!-- 屏幕 -->
          <div class="hardware-item">
            <span class="iconfont icon-Monitor hardware-item-label"></span>
            <span class="hardware-item-label wide" >{{ $t('GameRebound.Screen') }}</span>
            <span class="hardware-item-name wide2">{{ props.hardwaerinfo.MonitorNameStr }}</span>
            <span class="hardware-item-label">{{ $t('home.resolution') }}</span>
            <span class="hardware-item-name">{{ props.hardwaerinfo.resolutiop }}</span>
            <span class="hardware-item-label" style="margin-left: 10px;">{{ $t('hardwareInfo.refreshRate') }} </span>
            <span class="hardware-item-name">{{ props.hardwaerinfo.refresh_rate }}</span>
          </div>
        </div>
      </section>
    </div>
    <div class="dropdown_point_img" ref="dropdown_point_img">
      <span class="img-name">
        {{dropdownImgUrl.split('\\')[dropdownImgUrl.split('\\').length-1]}}
      </span>
      <img v-show="dropdownImgUrl" :src="dropdownImgUrl" alt="找不到该图片文件">
      <div class="bottom-info">
        <span></span>
        <span></span>
      </div>
    </div>
  </div>

  <RightClickMenu ref="rcm" @command="handleRightClickMenuCommand" @close="handleRightClickMenuClose"/>
</template>

<style scoped lang="scss">
#simple-data-charts {
    zoom: calc(1 / var(--zoomV--));
    transform: scale(var(--zoomV--));
    transform-origin: 0 0;
    &:hover {
        z-index: 99999;
    }
}
.simple-data-charts-container {
  margin-top: 25px;
  width: 100%;
  height: calc(100vh - 299px);
  padding-bottom: 5px;
  position: relative;
  display: flex;
  flex-flow: column nowrap;

  .charts-configs {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;

    .drop-menu {
      width: 109px;
      height: 24px;
      border-radius: 2px 0 0 2px;
      border: 1px solid #555555;
      font-size: 14px;
      color: #FFFFFF;
      line-height: 24px;
      padding-left: 6px;
      display: flex;
      flex-flow: row nowrap;
      justify-content: space-between;
      cursor: pointer;
      white-space: nowrap;

      .drop-menu-arrow-down {
        width: 22px;
        height: 22px;
        line-height: 22px;
        text-align: center;
        background: #0089E9;
        color: #ffffff;
        font-size: 14px;
      }
    }

    .shortcut-info {
      color: #777777;
      font-size: 12px;
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      gap: 10px;
      margin-left: 25px;
    }

    .avg-data {
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      gap: 5px;
      margin-left: auto;
      font-size: 12px;

      .lowfps {
        color: #FFFD55;
        margin-left: 5px;
        display: flex;
        align-items: center;

        :deep(.el-color-picker--small) {
          opacity: 0;
          pointer-events: none;
        }
        :deep(.el-tooltip__trigger) {
          height: 14px;
        }

        .lowval {
          width: 87px;
        }
      }

      .min {
        color: #0089E9;
        margin-left: 20px;
      }

      .max {
        color: #CE3030;
        margin-left: 20px;
      }

      .avg {
        color: #4AB54F;
        margin-left: 20px;
      }

      :deep(.el-color-picker__trigger) {
        border: 0;
      }

      :deep(.el-color-picker__color) {
        border: 0;
        border-radius: 0;
      }

      :deep(.el-color-picker--small .el-color-picker__trigger) {
        width: 18px;
        height: 18px;
      }

      :deep(.el-color-picker--small) {
        height: 18px;
      }

      :deep(.el-color-picker .el-color-picker__icon) {
        display: none;
      }
    }

  }

  .charts-dur {
    height: 30px;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: space-between;

    .start-time, .end-time {
      width: 190px;
      height: 30px;
      background: #22232E;
      border-radius: 4px;
      border: 1px solid #2D2E39;
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      justify-content: space-between;
      padding: 0 5px;
      gap: 3px;
      cursor: pointer;

      :deep(.el-input__wrapper) {
        padding: 0;
      }

      :deep(.el-input-number--small) {
        width: 40px;
        line-height: 20px;
        font-size: 12px;
        text-align: center;
      }

      :deep(.el-input__inner) {
        text-align: center;
      }
    }

    .dur-process {
      width: 800px;
      height: 10px;
      background: #2D2E39;
      border-radius: 5px;
      position: relative;
      overflow: hidden;

      .line {
        position: absolute;
        top: 0;
        left: 0;
        height: 10px;
        width: 800px;
        transition: all .3s;
        background-image: linear-gradient(
                        to right,
                        rgba(53, 121, 213, 0.6) 0%,
                        #2e3649 50%,
                        rgba(53, 121, 213, 0.6) 100%
        );
      }

      .line-item {
        width: 1px;
        height: 10px;
        position: absolute;
        background-color: #777777;
        transition: all .2s;

        &:hover, &.is-active {
          width: 3px;
          height: 10px;
          margin-left: -1.5px;
          background: #0089E9;
          box-shadow: 0 0 2px 2px #0089e961;
        }
      }
    }
  }

  .charts-content {
    width: 1240px;
    height: 170px;
    margin: 10px 0;
    position: relative;

      .emptyBox {
          color: #777777;
          width:1240px;
          height:170px;
          line-height: 170px;
          text-align: center;
          font-size: 16px;
          border: 1px solid #333333;
          border-radius: 4px;
      }

    .charts-left-bottom {
      position: absolute;
      left: 9px;
      bottom: 6px;
      display: flex;
      flex-flow: row nowrap;
      font-weight: 400;
      font-size: 12px;
      color: #777777;
      line-height: 20px;
      width: 1220px;
      gap: 5px;
      pointer-events: none;
      z-index: 900;
      span {
        color: #ffffff;
      }

      .charts-left-bottom-box {
        background: rgba(0, 0, 0, 0.6);
        border-radius: 4px;
        padding: 2px 9px;
        gap: 5px;
        display: flex;
      }
    }
  }

  .hardware-info {
    height: calc(100vh - 583px);
    display: flex;
    flex-flow:  column nowrap;
    h1 {
      font-weight: 400;
      font-size: 16px;
      color: #1193F8;
    }

    section {
      height: calc(100vh - 609px);
      display: flex;
      flex-flow: row nowrap;

      .hardware-info-left {
        display: flex;
        flex-direction: column;
        height: calc(100vh - 609px);
      }
      .hardware-info-left, .hardware-info-right {
        width: 50%;
        padding: 0 10px;

        .hardware-item {
          display: flex;
          flex-flow: row nowrap;
          align-items: center;
          font-size: 12px;
          margin-bottom: 10px;
          .wide{
            min-width: 60px;
          }
          .wide2{min-width: 200px;}
          .wide3{min-width: 270px;}
          & > div {
            display: flex;
            flex-flow: row nowrap;
            align-items: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .hardware-item-label {
            color: #777777;
            padding-right: 5px;
          }

          .hardware-item-name {
            color: #ffffff;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }

      .hardware-info-left {
        position: relative;
        &::before {
          content: '';
          width: 1px;
          height: 150px;
          position: absolute;
          background-color: #777777;
          right: 1px;
        }

        .dram-list {
          border-radius: 4px;
          border: 1px solid transparent;
          width: 530px;
          height: calc(100vh - 713px);
          max-height: 110px;
          overflow: hidden;
          padding: 5px 10px;
          display: flex;
          flex-flow: column nowrap;
          gap: 5px;
          margin-left: auto;

          &:hover {
            overflow: auto;
          }
        }
      }

      .DRAM {
        .hardware-item-name {
          margin-right: 10px;
        }
      }
    }
  }

  :deep(.AllDataHtml) {

  }
}
.right-click-markline {
    width: 1px;
    height: 153px;
    background-color: #fff;
    position: absolute;
    top: 403px;
    left: 0;
    z-index: 9999;
}
.dropdown_point_img {
  display: none;
  position: absolute;
  background-color: #373946;
  padding: 10px;
  width: 420px;
  height: 245px;
  .img-name {
    position: absolute;
    bottom: 13px;
    left: 50%;
    transform: translateX(-50%);
    display: inline-block;
    width: 361px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #ffffff;
    text-align: center;
  }
  .bottom-info {
    position: absolute;
    bottom: 14px;
    left: 19px;
    width: 365px;
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
    color: #ffffff;
  }
  img {
    width: 400px;
    height: 225px;
  }
}
</style>
<style lang="scss">
.no-clear-icon {
  .el-color-dropdown__link-btn {
    display: none;
  }
}

.scroll::-webkit-scrollbar {
  width: 5px;
  transition: 0.25s;
}

.scroll::-webkit-scrollbar-thumb {
  background: #71738C;
  border-radius: 3px;
}

.scroll::-webkit-scrollbar-thumb:hover {
  background-color: #71738C;
}

.scroll::-webkit-scrollbar-track {
  background-color: #2D2E39;
  width: 2px;
}

.dropdown_point {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  color: #777777;
  border-radius: 2px;
  padding: 5px;
  position: relative;
    gap: 10px;
    width: 100%;
    justify-content: space-between;

  span:first-child {
    min-width: 60px;
  }

  &:hover {
    color: #0089E9;
  }
}

.el-scrollbar {
  overflow: unset;
}

.el-dropdown__popper.el-popper {
  // 让弹出框向上偏移5px 避免鼠标进入下拉框时经过echarts的折线图导致弹出tooltip挡住下拉框
  margin-top: -5px;
}

.echarts-tooltip {
  top: -50% !important;
  margin-top: -45px;
  border: 0 !important;
  background: #373947 !important;
  opacity: 0.95!important;
}
</style>
