<script setup lang="ts">
import {useSensor} from "./hooks/useSensor";
import RightTopIcons from "@/components/mainCom/RightTopIcons.vue";
import { useLanguage } from '@/uitls/useLanguage';
useLanguage()
let {
  $store,
  step,
  PE_status,
  SensorInfoData,
  cpuName,
  gpuName0,
  gpuName1,
  gpuName2,
  motherboardName,
  gpu_index,
  setMasterGPUShow,
  collectedSensor,
  diskArr,
  data_arr,
  collectSensor,
  showSensorData,
  changeSensor,
  handleCloseWindow,
  setMasterGPU,
  RestoreDefault,
  goConcern,
} = useSensor()
</script>

<template>
  <div class="container">
    <header class="flex-items-center">
      <img src="../../assets/img/Public/logo_gpp.png" alt="">
      <span style="color: white">GamePP</span>
      <span class="headertipbox">{{ $t('SelectSensor.DefaultSensor') }}</span>
      <span class="ml-auto reset-default" @click="RestoreDefault">{{ $t('InGameMonitor.restoredefault') }}</span>
        <RightTopIcons close-icon @close="handleCloseWindow" :item-h="30" hover-color="#22232e"/>
      <!--<div class="close-btn" @click="handleCloseWindow">-->
      <!--  <span class="iconfont icon-Close"></span>-->
      <!--</div>-->
    </header>
    <div class="MonCenterAll Setup scroll">
      <div class="sensorData" v-if="cpuName">
        <p class="title">CPU <span id="cpuName">{{ cpuName }}</span></p>
        <div class="itemData" id="CpuTemp" v-if="data_arr['cpu_temp'] && PE_status != 'P' && PE_status != 'E'">
          <div class="editItem"><p class="editableText">{{ $t('hardwareInfo.temperature') }}</p></div>
          <div class="itemValue">
            <p>{{ showSensorData(data_arr['cpu_temp']) }}</p>
          </div>
          <el-button class="changeButton hover"
                     @click="changeSensor('cpuData', 0,{ type: 'CPU', type1: null },'℃')"
          >{{ $t('SelectSensor.Change') }}
          </el-button>
        </div>
<!--
        <div class="itemData" id="CpuTempP" v-if="data_arr['cpu_temp_p'] && PE_status.includes('P')">
          <div class="editItem"><p class="editableText">P 温度</p></div>
          <div class="itemValue">
            <p>{{ showSensorData(data_arr['cpu_temp_p']) }}</p>
          </div>
          <el-button class="changeButton hover"
                     @click="changeSensor('cpuData', 1,{ type: 'CPU', type1: null },'℃')"
          >更改
          </el-button>
        </div>

        <div class="itemData" id="CpuTempE" v-if="data_arr['cpu_temp_e'] && PE_status.includes('E')">
          <div class="editItem"><p class="editableText">E 温度</p></div>
          <div class="itemValue">
            <p>{{ showSensorData(data_arr['cpu_temp_e']) }}</p>
          </div>
          <el-button class="changeButton hover"
                     @click="changeSensor('cpuData', 2,{ type: 'CPU', type1: null },'℃')"
          >更改
          </el-button>
        </div> -->

        <div class="itemData" id="CpuClock" v-if="data_arr['cpu_clock'] && PE_status != 'P' && PE_status != 'E'">
          <div class="editItem"><p class="editableText">{{ $t('hardwareInfo.frequency') }}</p></div>
          <div class="itemValue">
            <p>{{ showSensorData(data_arr['cpu_clock']) }}</p>
          </div>
          <el-button class="changeButton hover"
                     @click="changeSensor('cpuData', 3,{ type: 'CPU', type1: null },'MHz')"
          >{{ $t('SelectSensor.Change') }}
          </el-button>
        </div>

        <div class="itemData" id="CpuClockP" v-if="data_arr['cpu_clock_p'] && PE_status.includes('P')">
          <div class="editItem"><p class="editableText">P {{ $t('hardwareInfo.frequency') }}</p></div>
          <div class="itemValue">
            <p>{{ showSensorData(data_arr['cpu_clock_p']) }}</p>
          </div>
          <el-button class="changeButton hover"
                     @click="changeSensor('cpuData', 4,{ type: 'CPU', type1: null },'MHz')"
          >{{ $t('SelectSensor.Change') }}
          </el-button>
        </div>

        <div class="itemData" id="CpuClockE" v-if="data_arr['cpu_clock_e'] && PE_status.includes('E')">
          <div class="editItem"><p class="editableText">E {{ $t('hardwareInfo.frequency') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['cpu_clock_e']) }}</p></div>
          <el-button class="changeButton hover"
                     @click="changeSensor('cpuData', 5,{ type: 'CPU', type1: null },'MHz')"
          >{{ $t('SelectSensor.Change') }}
          </el-button>
        </div>

        <div class="itemData" id="CpuUsage" v-if="data_arr['cpu_usage'] && PE_status != 'P' && PE_status != 'E'">
          <div class="editItem"><p class="editableText">{{ $t('hardwareInfo.occupied') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['cpu_usage']) }}</p></div>
          <el-button class="changeButton hover"
                     @click="changeSensor('cpuData', 6,{ type: 'CPU', type1: null },'%')"
          >{{ $t('SelectSensor.Change') }}
          </el-button>
        </div>

        <div class="itemData" id="CpuUsageP" v-if="data_arr['cpu_usage_p'] && PE_status.includes('P')">
          <div class="editItem"><p class="editableText">P {{ $t('hardwareInfo.occupied') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['cpu_usage_p']) }}</p></div>
          <el-button class="changeButton hover"
                     @click="changeSensor('cpuData',7,{ type: 'CPU', type1: null },'%')"
          >{{ $t('SelectSensor.Change') }}
          </el-button>
        </div>

        <div class="itemData" id="CpuUsageE" v-if="data_arr['cpu_usage_e'] && PE_status.includes('E')">
          <div class="editItem"><p class="editableText">E {{ $t('hardwareInfo.occupied') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['cpu_usage_e']) }}</p></div>
          <el-button class="changeButton hover"
                     @click="changeSensor('cpuData', 8,{ type: 'CPU', type1: null },'%')"
          >{{ $t('SelectSensor.Change') }}
          </el-button>
        </div>

        <div class="itemData" id="CpuVoltage" v-if="data_arr['cpu_voltage'] && PE_status != 'P' && PE_status != 'E'">
          <div class="editItem"><p class="editableText">{{ $t('GameRebound.voltage') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['cpu_voltage']) }}</p></div>
          <el-button class="changeButton hover"
                     @click="changeSensor('cpuData', 9,{ type: 'CPU', type1: null },'V')"
          >{{ $t('SelectSensor.Change') }}
          </el-button>
        </div>

        <div class="itemData" id="CpuVoltageP" v-if="data_arr['cpu_voltage_p'] && PE_status.includes('P')">
          <div class="editItem"><p class="editableText">P {{ $t('GameRebound.voltage') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['cpu_voltage_p']) }}</p></div>
          <el-button class="changeButton hover"
                     @click="changeSensor('cpuData', 10,{ type: 'CPU', type1: null },'V')"
          >{{ $t('SelectSensor.Change') }}
          </el-button>
        </div>

        <div class="itemData" id="CpuVoltageE" v-if="data_arr['cpu_voltage_e'] && PE_status.includes('E')">
          <div class="editItem"><p class="editableText">E {{ $t('GameRebound.voltage') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['cpu_voltage_e']) }}</p></div>
          <el-button class="changeButton hover"
                     @click="changeSensor('cpuData', 11,{ type: 'CPU', type1: null },'V')"
          >{{ $t('SelectSensor.Change') }}
          </el-button>
        </div>

        <div class="itemData" v-if="data_arr['cpu_power']">
          <div class="editItem"><p class="editableText">{{ $t('hardwareInfo.thermalPower') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['cpu_power']) }}</p></div>
          <el-button class="changeButton hover"
                     @click="changeSensor('cpuData', 12,{ type: 'CPU', type1: null },'W')"
          >{{ $t('SelectSensor.Change') }}
          </el-button>
        </div>

        <div class="itemData">
          <div class="editItem"><p class="editableText">{{ $t('SelectSensor.FanSpeed') }}</p></div>
          <div class="itemValue"><p id="cpu_fan">CPU : {{ $store.bg_sensor_data.cpu.fan }} RPM</p></div>
          <el-button class="changeButton hover"
                     @click="changeSensor('cpuData',13 ,{type: 'MainboardName', type1: 'System'},'RPM')"
          >{{ $t('SelectSensor.Change') }}
          </el-button>
        </div>
      </div>

      <div class="sensorData" v-if="gpuName0">
        <p class="title">GPU [#0] <span id="gpuName0">{{ gpuName0 }}</span>
          <span v-show="gpu_index === 0" class="master_gpu" style="margin-left: 10px">({{$t('SelectSensor.MainGraphicsCard')}})</span>
          <span v-show="setMasterGPUShow" class="setMasterGPU" id="setMasterGPU0"
                @click="setMasterGPU(0)">{{$t('SelectSensor.SetAsMainGraphicsCard')}}</span>
        </p>
        <div class="itemData" v-if="data_arr['gpu0_temp']">
          <div class="editItem"><p class="editableText">{{ $t('SelectSensor.GPUTemperature') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu0_temp']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu0Data',0, {type: 'GPU', type1: null},'℃')"><p>
            {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData" v-if="data_arr['gpu0_clock']">
          <div class="editItem"><p class="editableText">{{ $t('GameRebound.GraphicsCardFrequency') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu0_clock']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu0Data',1, {type: 'GPU', type1: null},'MHz')">
            <p>
              {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData" v-if="data_arr['gpu0_power']">
          <div class="editItem"><p class="editableText">{{ $t('SelectSensor.GPUHeatPower') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu0_power']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu0Data',2, {type: 'GPU', type1: null},'W')"><p>
            {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData" v-if="data_arr['gpu0_d3d_usage']">
          <div class="editItem"><p class="editableText">{{ $t('SelectSensor.GPUTemperatureD3D') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu0_d3d_usage']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu0Data',3, {type: 'GPU', type1: null},'%')"><p>
            {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData" v-if="data_arr['gpu0_total_usage']">
          <div class="editItem"><p class="editableText">{{ $t('SelectSensor.GPUTemperatureTOTAL') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu0_total_usage']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu0Data',4, {type: 'GPU', type1: null},'%')"><p>
            {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData" v-if="data_arr['gpu0_fan']">
          <div class="editItem"><p class="editableText">{{ $t('SelectSensor.FanSpeed') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu0_fan']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu0Data',5, {type: 'GPU', type1: null},'RPM')">
            <p>
              {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData" v-if="data_arr['gpu0_hot_spot_temp']">
          <div class="editItem"><p class="editableText">{{ $t('SelectSensor.GPUTemperatureCore') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu0_hot_spot_temp']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu0Data',6, {type: 'GPU', type1: null},'℃')"><p>
            {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData" v-if="data_arr['gpu0_mem_usage']">
          <div class="editItem"><p class="editableText">{{ $t('hardwareInfo.VRAM') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu0_mem_usage']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu0Data',7, {type: 'GPU', type1: null},'%')"><p>
            {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData" v-if="data_arr['gpu0_mem_clock']">
          <div class="editItem"><p class="editableText">{{ $t('hardwareInfo.VRAMFrequency') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu0_mem_clock']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu0Data',8, {type: 'GPU', type1: null},'MHz')">
            <p>
              {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData" v-if="data_arr['gpu0_mem_temp']">
          <div class="editItem"><p class="editableText">{{ $t('GameRebound.GraphicsCardMemoryTemperature') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu0_mem_temp']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu0Data',9, {type: 'GPU', type1: null},'℃')"><p>
            {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
      </div>

      <div class="sensorData" v-if="gpuName1">
        <p class="title">GPU [#1] <span id="gpuName1">{{ gpuName1 }}</span>
          <span v-show="gpu_index === 1" class="master_gpu" style="margin-left: 10px">({{$t('SelectSensor.MainGraphicsCard')}})</span>
          <span class="setMasterGPU" id="setMasterGPU1"
                v-show="setMasterGPUShow"
                @click="setMasterGPU(1)">{{$t('SelectSensor.SetAsMainGraphicsCard')}}</span>
        </p>
        <div class="itemData">
          <div class="editItem"><p class="editableText">{{ $t('SelectSensor.GPUTemperature') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu1_temp']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu1Data',0, {type: 'GPU', type1: null},'℃')"><p>
            {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData">
          <div class="editItem"><p class="editableText">{{ $t('GameRebound.GraphicsCardFrequency') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu1_clock']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu1Data',1, {type: 'GPU', type1: null},'MHz')">
            <p>
              {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData">
          <div class="editItem"><p class="editableText">{{ $t('SelectSensor.GPUHeatPower') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu1_power']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu1Data',2, {type: 'GPU', type1: null},'W')"><p>
            {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData">
          <div class="editItem"><p class="editableText">{{ $t('SelectSensor.GPUTemperatureD3D') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu1_d3d_usage']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu1Data',3, {type: 'GPU', type1: null},'%')"><p>
            {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData">
          <div class="editItem"><p class="editableText">{{ $t('SelectSensor.GPUTemperatureTOTAL') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu1_total_usage']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu1Data',4, {type: 'GPU', type1: null},'%')"><p>
            {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData">
          <div class="editItem"><p class="editableText">{{ $t('SelectSensor.FanSpeed') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu1_fan']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu1Data',5, {type: 'GPU', type1: null},'RPM')">
            <p>
              {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData">
          <div class="editItem"><p class="editableText">{{ $t('SelectSensor.GPUTemperatureCore') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu1_hot_spot_temp']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu1Data',6, {type: 'GPU', type1: null},'℃')"><p>
            {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData">
          <div class="editItem"><p class="editableText">{{ $t('hardwareInfo.VRAM') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu1_mem_usage']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu1Data',7, {type: 'GPU', type1: null},'%')"><p>
            {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData">
          <div class="editItem"><p class="editableText">{{ $t('hardwareInfo.VRAMFrequency') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu1_mem_clock']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu1Data',8, {type: 'GPU', type1: null},'MHz')">
            <p>
              {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData">
          <div class="editItem"><p class="editableText">{{ $t('GameRebound.GraphicsCardMemoryTemperature') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu1_mem_temp']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu1Data',9, {type: 'GPU', type1: null},'℃')"><p>
            {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
      </div>

      <div class="sensorData" v-if="gpuName2">
        <p class="title">GPU [#2] <span id="gpuName2">{{ gpuName2 }}</span>
          <span v-show="gpu_index === 2" class="master_gpu" style="margin-left: 10px">({{$t('SelectSensor.MainGraphicsCard')}})</span>
          <span class="setMasterGPU" id="setMasterGPU2"
                v-show="setMasterGPUShow"
                @click="setMasterGPU(2)">{{$t('SelectSensor.SetAsMainGraphicsCard')}}</span>
        </p>
        <div class="itemData">
          <div class="editItem"><p class="editableText">{{ $t('SelectSensor.GPUTemperature') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu2_temp']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu2Data',0, {type: 'GPU', type1: null},'℃')"><p>
            {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData">
          <div class="editItem"><p class="editableText">{{ $t('GameRebound.GraphicsCardFrequency') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu2_clock']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu2Data',1, {type: 'GPU', type1: null},'MHz')">
            <p>
              {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData">
          <div class="editItem"><p class="editableText">{{ $t('SelectSensor.GPUHeatPower') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu2_power']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu2Data',2, {type: 'GPU', type1: null},'W')"><p>
            {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData">
          <div class="editItem"><p class="editableText">{{ $t('SelectSensor.GPUTemperatureD3D') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu2_d3d_usage']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu2Data',3, {type: 'GPU', type1: null},'%')"><p>
            {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData">
          <div class="editItem"><p class="editableText">{{ $t('SelectSensor.GPUTemperatureTOTAL') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu2_total_usage']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu2Data',4, {type: 'GPU', type1: null},'%')"><p>
            {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData">
          <div class="editItem"><p class="editableText">{{ $t('SelectSensor.FanSpeed') }}</p></div>
          <div class="itemValue"><p id="gpu2_fan">Fan : {{ $store.bg_sensor_data.gpu_list[2].fan }}RPM</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu2Data',5, {type: 'GPU', type1: null},'RPM')">
            <p>
              {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData">
          <div class="editItem"><p class="editableText">{{ $t('SelectSensor.GPUTemperatureCore') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu2_hot_spot_temp']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu2Data',6, {type: 'GPU', type1: null},'℃')"><p>
            {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData">
          <div class="editItem"><p class="editableText">{{ $t('hardwareInfo.VRAM') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu2_mem_usage']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu2Data',7, {type: 'GPU', type1: null},'%')"><p>
            {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData">
          <div class="editItem"><p class="editableText">{{ $t('hardwareInfo.VRAMFrequency') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu2_mem_clock']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu2Data',8, {type: 'GPU', type1: null},'MHz')">
            <p>
              {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
        <div class="itemData">
          <div class="editItem"><p class="editableText">{{ $t('GameRebound.GraphicsCardMemoryTemperature') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['gpu2_mem_temp']) }}</p></div>
          <el-button class="changeButton hover" @click="changeSensor('gpu2Data',9, {type: 'GPU', type1: null},'℃')"><p>
            {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
      </div>

      <div class="sensorData">
        <p class="title">{{ $t('hardwareInfo.motherboard') }} <span id="motherboardName">{{ motherboardName }}</span></p>
        <div class="itemData">
          <div class="editItem"><p class="editableText">{{ $t('SelectSensor.MotherboardTemperature') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['mainboard_temp']) }}</p></div>
          <el-button class="changeButton hover"
                     @click="changeSensor('boardData',0, {type: 'MainboardName', type1: null},'℃')"><p> {{ $t('SelectSensor.Change') }}</p>
          </el-button>
        </div>
      </div>

      <div class="sensorData">
        <p class="title">{{ $t('hardwareInfo.memory') }}</p>
        <div class="itemData">
          <div class="editItem"><p class="editableText">{{ $t('hardwareInfo.occupied') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['memory_usage']) }}</p></div>
          <el-button class="changeButton hover"
                     @click="changeSensor('memoryData',0,{type: 'Memory', type1: 'DIMM',type2: 'System'},'%')"><p>
                      {{ $t('SelectSensor.Change') }}</p>
          </el-button>
        </div>
        <div class="itemData">
          <div class="editItem"><p class="editableText">{{ $t('hardwareInfo.temperature') }}</p></div>
          <div class="itemValue"><p>{{ showSensorData(data_arr['memory_temp']) }}</p></div>
          <el-button class="changeButton hover"
                     @click="changeSensor('memoryData',1,{type: 'Memory', type1: 'DIMM'},'℃')"><p>
                      {{ $t('SelectSensor.Change') }}</p></el-button>
        </div>
      </div>

      <div id="HDTempHtml">
        <div class="sensorData" v-for="(disk,index) in diskArr" :key="'disk'+index">
          <p class="title">{{ $t('hardwareInfo.hardDisk') }}{{ index + 1 }} {{ disk.describe }}</p>
          <div class="itemData">
            <div class="editItem">
              <p class="editableText">{{ $t('hardwareInfo.temperature') }}</p>
            </div>
            <div class="itemValue">
              <p id="disk${index}_temp">{{ disk.keyName }} : {{ disk.disk_value }}{{ disk.disk_unit }}</p>
            </div>
            <el-button class="changeButton hover"
                       @click="changeSensor('diskData',index,{type: 'Drive', type1: ''},'℃')">
              <p>{{ $t('SelectSensor.Change') }}</p>
            </el-button>
          </div>
        </div>
      </div>

      <div class="sensorData">
        <div class="Sensors">
          <p>{{ $t('SelectSensor.AttentionSensor') }}</p>
          <!-- <input class="hover" type="button" value="前往关注" onclick="goConcern()"> -->
          <div class="hover follow" @click="goConcern">{{ $t('SelectSensor.GoToAttention') }}</div>
        </div>
        <p class="tps">开发中,后期将会在其他功能处提供关注的传感器数据</p>
        <div class="Data_follow" id="MyAttentionHtml">
          <div class="rebound-empty" v-if="collectedSensor.length == 0"><p>{{ $t('SelectSensor.NoAttention') }}</p></div>
          <div
              v-for="(data,index) in collectedSensor"
              :key="'cd' + index"
              class="itme_data2"
          >
            <p>{{ data['tname'] }}</p>
            <el-tooltip :content="$t('SelectSensor.CancelAttention')">
              <div class="calloff"
                   @click="collectSensor(data.collected_data['OutIndex'], data.collected_data['InnerIndex'],data.outName,data.name)">
                <div class="collect_star Favorite"></div>
              </div>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.container {
  width: 680px;
  height: 712px;
  background: #22232e;
  border-radius: 2px;
  box-shadow: 0 1px 6px rgb(0 0 0 / 60%);
  position: relative;
  margin-left: 6px;
  margin-top: 6px;
  overflow: hidden;
}

header {
  height: 30px;
  background: #2b2c37;
  border-radius: 2px 2px 0px 0px;
  overflow: hidden;
  color: #fff;
  padding-left: 10px;
  -webkit-app-region:drag;
  font-size: 14px;

  .headertipbox {
    margin-left: 10px;
    font-size: 14px;
  }

  .reset-default {
    font-size: 14px;
    color: #3579d5;
    margin-right: 10px;
    cursor: pointer;
    -webkit-app-region:no-drag;

    &:hover {
      color: #4a87d9;
    }
  }

  img {
    width: 16px;
    height: 16px;
    margin-right: 10px;
  }

  .close-btn {
    width: 40px;
    height: 26px;
    font-size: 14px;
    border-radius: 2px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    -webkit-app-region:no-drag;

    &:hover {
      background: #22232e;
      .iconfont {
        color: #ffffff;
      }
    }

    .iconfont {
      font-size: 14px;
      color: #3579d5;
    }
  }
}

.MonCenterAll {
  height: 682px;
  border-radius: 2px 2px 0px 0px;
  padding: 35px 40px;
  overflow-y: auto;
}

.Setup .sensorData {
  padding-bottom: 28px;
  margin-bottom: 25px;
  border-bottom: 1px solid #5F5F5F
}

.sensorData {
  display: flex;
  flex-direction: column
}

.sensorData:first-child {
  padding-bottom: 28px;
  border-bottom: 1px solid #5F5F5F
}

.sensorData .title {
  font-size: 14px;
  color: #FFFFFF;
  font-weight: bold;
  margin-bottom: 8px
}

.itemData {
  width: 100%;
  height: 30px;
  display: flex;
  align-items: center;
  margin: 7px 0
}

.itemValue {
  width: 250px;
  font-size: 12px;
  color: #777
}

.editItem {
  width: 100px;
  height: 25px;
  font-size: 12px;
  color: #777;
  display: flex;
  align-items: center;
  margin-right: 23px;
  text-overflow: ellipsis;
  box-sizing: border-box
}

.setMasterGPU {
  visibility: visible;
  float: right;
  color: #3579d5;
  font-size: 13px;
  cursor: pointer;
}

.changeButton {
  margin-left: auto;
  background: #3579d5;
  border: transparent;
  color: white;
}

.hover:hover {
  background: #4a87d9;
}

.Sensors {
  display: flex;
  color: #FFFFFF;
  align-items: center;
  justify-content: space-between;
}

.Sensors p {
  font-size: 14px
}

.Sensors input {
  font-size: 12px;
  cursor: pointer;
  height: 30px;
  background: #3579d5;
  border-radius: 2px;
  color: #fff;
  padding: 0 15px
}

.tps {
  color: #777;
  font-size: 12px
}

.itme_data2 {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between
}

.itme_data2 p {
  color: #777;
  font-size: 12px;
}

.Data_follow {
  margin-top: 10px
}

.collect_star {
  background: url(./assets/ic_default_sensor_collect_a.png)no-repeat;
  width: 20px;
  height: 19px;
  background-size: 100% 100%;
  position: relative;
  cursor: pointer;
}

.itme_data2 .collect_star p {
  left: -35px
}

.calloff p {
  color: #fff
}

.DragBox {
  width: 770px;
  height: 40px;
  position: absolute;
  top: 0;
}

.follow {
  font-size: 12px;
  cursor: pointer;
  height: 30px;
  background: #3579d5;
  border-radius: 2px;
  color: #fff;
  padding: 0 15px;
  line-height: 30px;
}

.rebound-empty {
  color: white;
  font-size: 12px;
}
</style>

<style>
body {
  width: 100%;
}
.el-button:focus-visible{
  outline: transparent
}
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.flex-items-center {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
}

.ml-auto {
    margin-left: auto;
}

.scroll::-webkit-scrollbar {
    width: 5px;
    transition: 0.25s;
}

.scroll::-webkit-scrollbar-thumb {
    background: #71738C;
    border-radius: 3px;
}

.scroll::-webkit-scrollbar-thumb:hover {
    background-color: #71738C;
}

.scroll::-webkit-scrollbar-track {
    background-color: #2D2E39;
    width: 2px;
}

.scroll::-webkit-scrollbar {
    width: 5px;
    transition: 0.25s;
}

.scroll::-webkit-scrollbar-thumb {
    background: #71738C;
    border-radius: 3px;
}

.scroll::-webkit-scrollbar-thumb:hover {
    background-color: #71738C;
}

.scroll::-webkit-scrollbar-track {
    background-color: #2D2E39;
    width: 2px;
}
</style>
