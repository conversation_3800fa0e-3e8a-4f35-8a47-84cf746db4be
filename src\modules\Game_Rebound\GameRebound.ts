
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import '../../assets/iconfont/iconfont.css'
import '../../assets/iconfont/iconfont.js'
import './Game_Rebound_RTL.scss'
import App from './GameRebound.vue'
import '../Game_Home/reset.css'
import 'dayjs/locale/zh-cn'
import i18n from '../../assets/lang'

const app = createApp(App)
app.use(createPinia())
app.use(i18n)
app.mount('#app')