export const bg_list = {
  '--redbg': 'linear-gradient(90deg,  rgba(191,64,64,0.1) 0%, rgba(191,64,64,0.4)100%)',
  '--yellowbg': 'linear-gradient(90deg, rgba(255,126,0,0.1) 0%,  rgba(255,126,0,0.4)100%)',
  '--bluebg': 'linear-gradient(90deg, rgb(0,137,233,0.1) 0%,  rgba(0,137,233,0.4)100%)',
  '--greenbg': 'linear-gradient(90deg, rgba(47,210,116,0.1) 0%,  rgba(47,210,116,0.4)100%)'
}

export const totalSeconds = (time: { h: number; m: number; s: number }) => time.h * 3600 + time.m * 60 + time.s * 1;

export function getHoursMinutesSeconds(totalSeconds: number) {
  let hours = Math.floor(totalSeconds / 3600);
  let minutes = Math.floor((totalSeconds % 3600) / 60);
  let seconds = Math.floor(totalSeconds % 60);

  // 返回格式化后的时分秒对象
  return {
    h: hours,
    m: minutes,
    s: seconds
  };
}

export function MathArrayAvg2(arr: Array<any>,startIndex = 0,errs:Array<number>=[]) {
  let sum = 0;
  let count = 0;
  if (errs){
    const errsSet = new Set(errs);
    for (let i = 0; i < arr.length; i++) {
      if (!errsSet.has(i + startIndex)) {
        sum += arr[i]
        count++
      }
    }
  }
  if (count === 0) return 0;
  return sum / count;
}

export function MathArrayMax2(arr:any,startIndex = 0,errs:Array<number>=[]) {
  if (arr.length === 0) return 0;
  let maxNum = 0
  if (errs) {
    const errsSet = new Set(errs);
    for (let i = 0; i < arr.length; i++) {
      if (!errsSet.has(i + startIndex)) {
        maxNum = Math.max(maxNum, arr[i])
      }
    }
  }
  return maxNum
}

export function MathArrayMin2(arr:any,startIndex = 0,errs:Array<number>=[]) {
  if (arr.length === 0) return 0;
  let minNum:number|null = null
  if (errs) {
    const errsSet = new Set(errs);
    for (let i = 0; i < arr.length; i++) {
      if (!errsSet.has(i + startIndex)) {
        if (minNum === null) {minNum = arr[i]}
        else {minNum = Math.min(minNum, arr[i])}
      }
    }
  }
  if (minNum === null) return 0;
  return minNum
}

export function displayHMSTime(hms: { h: number, m: number, s: number })  {
  return `${inputFormatter(hms.h)} : ${inputFormatter(hms.m)} : ${inputFormatter(hms.s)}`
}

export function inputFormatter(value: string | number) {
  if (Number(value) < 10) {
    return '0' + Number(value)
  } else {
    return Number(value)
  }
}

export function FormatSeconds(value: any, second: any) {
  let theTime: number = parseInt(value);// 秒
  let theTime1: number = 0;// 分
  let theTime2: number = 0;// 小时
  if (theTime > 60) {
    theTime1 = parseInt(String(theTime / 60));
    theTime = parseInt(String(theTime % 60));
    if (theTime1 > 60) {
      theTime2 = parseInt(String(theTime1 / 60));
      theTime1 = parseInt(String(theTime1 % 60));
    }
  }
  let result = "";
  if (second) {
    result = "" + parseInt(String(theTime)) + '秒';
  }
  if (theTime1 > 0) {
    result = "" + parseInt(String(theTime1)) + '分钟' + result;
  }
  if (theTime2 > 0) {
    result = "" + parseInt(String(theTime2)) + '小时' + result;
  }
  return result;
}

export function FormatTime(time: any, type = null) {
  const date = new Date(time * 1000);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
  const Y = date.getFullYear() + '-';
  const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
  const D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate() + ' ';
  const h = ' ' + date.getHours() + ':';
  const m = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
  if (type) {
    return h + m;
  } else {
    return Y + M + D + h + m;
  }
}
