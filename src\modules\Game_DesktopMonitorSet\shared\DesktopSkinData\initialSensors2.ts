import { Sensor } from '../../../Game_DMComponent/sharedTypes';
import lightbox from '../../../../assets/img/monitoring/img2/img_lightbox_bg1.png';
import lightbox2 from '../../../../assets/img/monitoring/img2/img_lightbox_bg2.png';
import lightbox3 from '../../../../assets/img/monitoring/img2/img_lightbox_bg3.png';
import net_upload from '../../../../assets/img/monitoring/img2/icon_update.png';
export const initialSensors: Sensor[] = [
  {
    id:1,
    nums: 6,
    type: '时间',
    type2: 'time',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    timeFormat: 2,
    timeRule: 0,
    timeType: 1,
    style: {
      zIndex: 54,
      fontSize: 16,
      fontFamily: 'Hybriddd',
      top: 23,
      left: 18,
      color: 'rgba(73,231,231,1)',
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id:2,
    nums: 6,
    type: '时间',
    type2: 'time',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    timeFormat:3,
    timeRule: 0,
    timeType: 2,
    style: {
      zIndex: 53,
      fontSize: 16,
      fontFamily: 'Hybriddd',
      top: 42,
      left: 18,
      color: 'rgba(73,231,231,1)',
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id:3,
    nums: 6,
    type: '时间',
    type2: 'time',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    timeFormat: 1,
    timeRule: 1,
    timeType: 1,
    style: {
      zIndex: 52,
      fontSize: 36,
      fontFamily: 'Hybriddd',
      top: 23,
      left: 135,
      color: 'rgba(73,231,231,1)',
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id:4,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    parameters: '',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: lightbox,
    transformShow: false,
    style: {
      zIndex: 51,
      fontSize: 12,
      top: 10,
      left: 0,
      width: 300,
      height: 60,
      Interval: 60,
      animation: {
        names: 0,//顺时针  Clockwise
        names2: 1,//逆时针 Counterclockwise
        duration: 0, //快速旋转 动画时长
        duration2: 1, //慢速旋转
        // timingFunction: 'linear',
        // iterationCount: 'infinite',
      }
    },
  },
  {
    id: 5,
    nums: 9,
    type: '图形',
    type2: 'graphic',
    remark: '/',
    sensor: 'CPU Temp',
    parameters: 'temp',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    Switchcolorsshow: true,
    style: {
      zIndex: 50,
      fontSize: 12,
      width: 286,
      height: 20,
      backgroundColor: 'rgba(0, 255, 198, 0.6)',
      ProgressColor2:'#FF0000',
      Interval:80,
      Judgmentitem:0,
      top: 88,
      left: 7,
    },
  },
  {
    id: 6,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'CPU',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 49,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 24,
      color: 'rgba(73,231,231,1)',
      top: 110,
      left: 127,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 7,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'CPU Temp',
    parameters: 'temp',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: '°C',
    unitshow: true,
    style: {
      zIndex: 48,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 48,
      color: 'rgba(73,231,231,1)',
      top: 149,
      left: 101,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 8,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'Useage',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 47,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 216,
      left: 10,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 9,
    nums: 8,
    type: '进度条',
    type2: 'progress',
    remark: '/',
    sensor: 'CPU Usage',
    parameters: 'usage',
    belong: '',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: '%',
    Switchcolorsshow: true,
    style: {
      zIndex: 46,
      width: 150,
      height: 4,
      borderRadius: 4,
      backgroundColor: 'rgba(255,255,255,0)',
      ProgressColor:'rgba(0, 255, 198, 1)',
      ProgressColor2:'#FF0000',
      Interval:80,
      Judgmentitem:0,
      top: 221,
      left: 77,
    },
  },
  {
    id: 10,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'CPU Usage',
    parameters: 'usage',
    belong: '',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: '%',
    unitshow: true,
    style: {
      zIndex: 45,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 216,
      left: 242,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 11,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'Freq',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 44,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 236,
      left: 10,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 12,
    nums: 8,
    type: '进度条',
    type2: 'progress',
    remark: '/',
    sensor: 'CPU Clock',
    parameters: 'clock',
    belong: '',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'MHz',
    Switchcolorsshow: true,
    style: {
      zIndex: 43,
      width: 150,
      height: 4,
      borderRadius: 4,
      backgroundColor: 'rgba(255,255,255,0)',
      ProgressColor:'rgba(0, 255, 198, 1)',
      ProgressColor2:'#FF0000',
      Interval:4000,
      Judgmentitem:0,
      top: 242,
      left: 77,
    },
  },
  {
    id: 13,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'CPU Clock',
    parameters: 'clock',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'MHz',
    unitshow: true,
    style: {
      zIndex: 42,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 236,
      left: 242,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 14,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'Fan',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 41,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 257,
      left: 10,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 15,
    nums: 8,
    type: '进度条',
    type2: 'progress',
    remark: '/',
    sensor: 'CPU Fan',
    parameters: 'fan',
    belong: '',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'RPM',
    Switchcolorsshow: true,
    style: {
      zIndex: 40,
      width: 150,
      height: 4,
      borderRadius: 4,
      backgroundColor: 'rgba(255,255,255,0)',
      ProgressColor:'rgba(0, 255, 198, 1)',
      ProgressColor2:'#FF0000',
      Interval:2500,
      Judgmentitem:0,
      top: 262,
      left: 77,
    },
  },
  {
    id: 16,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'CPU Fan',
    parameters: 'fan',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'RPM',
    unitshow: true,
    style: {
      zIndex: 39,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 257,
      left: 242,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 17,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'Heat',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 38,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 277,
      left: 10,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 18,
    nums: 8,
    type: '进度条',
    type2: 'progress',
    remark: '/',
    sensor: 'CPU Power',
    parameters: 'power',
    belong: '',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'W',
    Switchcolorsshow: true,
    style: {
      zIndex: 37,
      width: 150,
      height: 4,
      borderRadius: 4,
      backgroundColor: 'rgba(255,255,255,0)',
      ProgressColor:'rgba(0, 255, 198, 1)',
      ProgressColor2:'#FF0000',
      Interval:90,
      Judgmentitem:0,
      top: 282,
      left: 77,
    },
  },
  {
    id: 19,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'CPU Power',
    parameters: 'power',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'W',
    unitshow: true,
    style: {
      zIndex: 36,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 277,
      left: 242,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id:20,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    parameters: '',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: lightbox2,
    transformShow: false,
    style: {
      zIndex: 35,
      fontSize: 12,
      top: 82,
      left: 0,
      width: 300,
      height: 219,
      Interval: 60,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },


  {
    id: 21,
    nums: 9,
    type: '图形',
    type2: 'graphic',
    remark: '/',
    sensor: 'GPU Temp',
    parameters: 'temp',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    Switchcolorsshow: true,
    style: {
      zIndex: 34,
      fontSize: 12,
      width: 286,
      height: 20,
      backgroundColor: 'rgba(0, 255, 198, 0.6)',
      ProgressColor2:'#FF0000',
      Interval:80,
      Judgmentitem:0,
      top: 317,
      left: 7,
    },
  },
  {
    id: 22,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'GPU',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 33,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 24,
      color: 'rgba(73,231,231,1)',
      top: 341,
      left: 127,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 23,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'GPU Temp',
    parameters: 'temp',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: '°C',
    unitshow: true,
    style: {
      zIndex: 32,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 48,
      color: 'rgba(73,231,231,1)',
      top: 378,
      left: 101,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 24,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'Useage',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 31,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 445,
      left: 10,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 25,
    nums: 8,
    type: '进度条',
    type2: 'progress',
    remark: '/',
    sensor: 'GPU Usage',
    parameters: 'total_usage',
    belong: '',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: '%',
    Switchcolorsshow: true,
    style: {
      zIndex: 30,
      width: 150,
      height: 4,
      borderRadius: 4,
      backgroundColor: 'rgba(255,255,255,0)',
      ProgressColor:'rgba(0, 255, 198, 1)',
      ProgressColor2:'#FF0000',
      Interval:80,
      Judgmentitem:0,
      top: 448,
      left: 77,
    },
  },
  {
    id: 26,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'GPU Usage',
    parameters: 'total_usage',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: '%',
    unitshow: true,
    style: {
      zIndex: 29,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 445,
      left: 242,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 27,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'Freq',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 28,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 466,
      left: 10,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 28,
    nums: 8,
    type: '进度条',
    type2: 'progress',
    remark: '/',
    sensor: 'GPU Clock',
    parameters: 'clock',
    belong: '',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'MHz',
    Switchcolorsshow: true,
    style: {
      zIndex: 27,
      width: 150,
      height: 4,
      borderRadius: 4,
      backgroundColor: 'rgba(255,255,255,0)',
      ProgressColor:'rgba(0, 255, 198, 1)',
      ProgressColor2:'#FF0000',
      Interval:2000,
      Judgmentitem:0,
      top: 470,
      left: 77,
    },
  },
  {
    id: 29,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'GPU Clock',
    parameters: 'clock',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'MHz',
    unitshow: true,
    style: {
      zIndex: 26,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 466,
      left: 242,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 30,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'Fan',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 25,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 489,
      left: 10,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 31,
    nums: 8,
    type: '进度条',
    type2: 'progress',
    remark: '/',
    sensor: 'GPU Fan',
    parameters: 'fan',
    belong: 'GPU',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'RPM',
    Switchcolorsshow: true,
    style: {
      zIndex: 24,
      width: 150,
      height: 4,
      borderRadius: 4,
      backgroundColor: 'rgba(255,255,255,0)',
      ProgressColor:'rgba(0, 255, 198, 1)',
      ProgressColor2:'#FF0000',
      Interval:1600,
      Judgmentitem:0,
      top: 493,
      left: 77,
    },
  },
  {
    id: 32,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'GPU Fan',
    parameters: 'fan',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'RPM',
    unitshow: true,
    style: {
      zIndex: 23,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 489,
      left: 242,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 33,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'Heat',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 22,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 508,
      left: 10,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id:34,
    nums: 8,
    type: '进度条',
    type2: 'progress',
    remark: '/',
    sensor: 'GPU Power',
    parameters: 'power',
    belong: 'GPU',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'W',
    Switchcolorsshow: true,
    style: {
      zIndex: 21,
      width: 150,
      height: 4,
      borderRadius: 4,
      backgroundColor: 'rgba(255,255,255,0)',
      ProgressColor:'rgba(0, 255, 198, 1)',
      ProgressColor2:'#FF0000',
      Interval:100,
      Judgmentitem:0,
      top: 512,
      left: 77,
    },
  },
  {
    id: 35,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'GPU Power',
    parameters: 'power',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'W',
    unitshow: true,
    style: {
      zIndex: 20,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 508,
      left: 242,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id:36,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: lightbox2,
    transformShow: false,
    style: {
      zIndex: 19,
      fontSize: 12,
      top: 310,
      left: 0,
      width: 300,
      height: 219,
      Interval: 60,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 37,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'VRAM',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 18,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 550,
      left: 10,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 38,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'VRAM Usage',
    parameters: 'mem_usage',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: '%',
    unitshow: true,
    style: {
      zIndex: 17,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 550,
      left: 155,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 39,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'VRAM Usage mb',
    parameters: 'mem_usage_mb',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'GB',
    unitshow: true,
    style: {
      zIndex: 16,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 550,
      left: 206,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 40,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 15,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 550,
      left: 240,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 41,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'VRAM Total',
    parameters: 'mem_size',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'GB',
    unitshow: true,
    style: {
      zIndex: 14,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 550,
      left: 256,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 42,
    nums: 8,
    type: '进度条',
    type2: 'progress',
    remark: '/',
    sensor: 'VRAM Usage',
    parameters: 'mem_usage',
    belong: '',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    class: 'ProgressShape1',
    unit: '%',
    Switchcolorsshow: true,
    style: {
      zIndex: 13,
      width: 300,
      height: 34,
      borderRadius: 0,
      backgroundColor: 'rgba(0,0,0,.6)',
      ProgressColor:'rgba(0, 255, 198, 0.6)',
      ProgressColor2:'#FF0000',
      Interval:80,
      Judgmentitem:0,
      top: 539,
      left: 0,
    },
  },
  {
    id: 43,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'DRAM',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 12,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 593,
      left: 10,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 44,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'DRAM Usage',
    parameters: 'd_usage',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: '%',
    unitshow: true,
    style: {
      zIndex: 11,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 593,
      left: 155,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 45,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'DRAM Usage mb',
    parameters: 'usage_mb',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'GB',
    unitshow: true,
    style: {
      zIndex: 10,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 593,
      left: 206,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 46,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 9,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 593,
      left: 240,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 47,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'DRAM Total',
    parameters: 'size',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'GB',
    unitshow: true,
    style: {
      zIndex: 8,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 593,
      left: 256,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 48,
    nums: 8,
    type: '进度条',
    type2: 'progress',
    remark: '/',
    sensor: 'DRAM Usage',
    parameters: 'd_usage',
    belong: '',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    class: 'ProgressShape1',
    unit: '%',
    Switchcolorsshow: true,
    style: {
      zIndex: 7,
      width: 300,
      height: 34,
      borderRadius: 0,
      backgroundColor: 'rgba(0,0,0,.6)',
      ProgressColor:'rgba(0, 255, 198, 0.6)',
      ProgressColor2:'#FF0000',
      Interval:80,
      Judgmentitem:0,
      top: 582,
      left: 0,
    },
  },
  {
    id: 49,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'NET',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 6,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 637,
      left: 10,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 50,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: net_upload,
    transformShow: false,
    style: {
      zIndex: 5,
      fontSize: 12,
      top: 636,
      left: 130,
      width: 8,
      height: 13,
      Interval: 60,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 51,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'network',
    parameters: 'upload',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'KB/s',
    unitshow: true,
    style: {
      zIndex: 4,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 637,
      left: 142,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 52,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    class: 'rotate180',
    mediaSrc: net_upload,
    transformShow: false,
    style: {
      zIndex: 3,
      fontSize: 12,
      top: 636,
      left: 205,
      width: 8,
      height: 13,
      Interval: 60,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 53,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'network',
    parameters: 'download',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'KB/s',
    unitshow: true,
    style: {
      zIndex: 2,
      fontFamily: 'Hybriddd',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(255,255,255,1)',
      top: 637,
      left: 217,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id:54,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: lightbox3,
    transformShow: false,
    style: {
      zIndex: 1,
      top: 626,
      left: 0,
      width: 300,
      height: 34,
      Interval: 60,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
]