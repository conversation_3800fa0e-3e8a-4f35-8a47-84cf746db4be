<script setup lang="ts">
import RightTopIcons from "@/components/mainCom/RightTopIcons.vue";

const gamepp = window.gamepp as any;
import {onMounted, ref} from 'vue'
let isFullScreen = ref(false) // 是否处于全屏模式

const props = defineProps({
    windowName: {
        type: String,
        required: true
    },
    closeIcon: {
        type: Boolean,
        default: true
    },
    minimizeIcon: {
        type: Boolean,
        default: true
    },
    maximizeIcon: {
        type: <PERSON>olean,
        default: true
    }
})
const methods = {
    async minimizeWindow() {
        await gamepp.webapp.windows.minimize.promise(props.windowName);
    },
    async closeWindows() {
        gamepp.webapp.windows.close.promise(props.windowName);
    },
    // 全屏
    async maximizeWindow() {
        isFullScreen.value = true;
        gamepp.webapp.windows.maximize.promise(props.windowName);
    },
    // 还原
    unmaximizeWindow() {
        isFullScreen.value = false;
        gamepp.webapp.windows.unmaximize.promise(props.windowName);
    }
}
onMounted(()=>{
    gamepp.webapp.windows.onWindowWillMaximize.addEventListener( () => {
        console.log('onWindowWillMaximize');
        isFullScreen.value = true
    })
    gamepp.webapp.windows.onWindowWillUnmaximize.addEventListener( () => {
        console.log('onWindowWillUnmaximize');
        isFullScreen.value = false
    })
})

</script>

<template>
    <header>
        <img class="logo" src="../../assets/img/Public/logo_gpp.png" alt="">
        <span>
            <slot name="default"></slot>
        </span>
        <RightTopIcons
            style="margin-left: auto;"
            :close-icon="props.closeIcon"
            :minimize-icon="props.minimizeIcon"
            :maximize-icon="props.maximizeIcon"
            :item-h="40"
            hover-color="#22232e"
            @close="methods.closeWindows"
            @minimize="methods.minimizeWindow"
            @maximize="methods.maximizeWindow"
            @unmaximize="methods.unmaximizeWindow"
            :is-full-screen="isFullScreen"
        />
    </header>
</template>

<style scoped lang="scss">
header {
    -webkit-app-region: drag;
    color: #ffffff;
    font-size: 12px;
    width: 100%;
    height: 40px;
    background: #2D2E39;
    box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
    padding-left: 5px;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    overflow: hidden;

    .logo {
        width: 20px;
        height: 20px;
        margin-left: 8px;
        margin-right: 10px;
    }
}
</style>

