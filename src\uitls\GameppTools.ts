export const AVGNum = (arr: any) => {
  var sum = eval(arr.join("+"));
  return ~~(sum / arr.length * 100) / 100;
}

export const MaxNum = (arr: any) => {
  if (arr.length === 0) return 0;
  return Math.max(...arr);
}

export const MinNum = (arr: any) => {
  if (arr.length === 0) return 0;
  return Math.min(...arr);
  // return Math.min.apply(Math, arr);
}

export const FormatTimestamp = (time: any) => {
  let date = new Date(time * 1000);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
  let Y = date.getFullYear() + '-';
  let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
  let D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate() + ' ';
  let h = ' ' + date.getHours() + ':';
  let m = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
  return Y + M + D + h + m;
}

export function findMostFrequent(arr:any) {
  const countMap:any = {}; // 用于统计每个元素的出现次数
  let maxCount = 0; // 最大出现次数
  let mostFrequentElement; // 出现次数最多的元素

  for (const item of arr) {
      if (!countMap[item]) {
          countMap[item] = 0; // 如果元素不存在于 countMap 中，初始化为 0
      }
      countMap[item] += 1; // 增加该元素的计数

      // 检查是否是当前出现次数最多的元素
      if (countMap[item] > maxCount) {
          maxCount = countMap[item];
          mostFrequentElement = item;
      }
  }

  return mostFrequentElement;
}

export const FormatSeconds = (value: any) => {
  var theTime: any = parseInt(value);// 秒
  var theTime1: any = 0;// 分
  var theTime2: any = 0;// 小时
  if (theTime > 60) {
    theTime1 = parseInt(String(theTime / 60));
    theTime = parseInt(String(theTime % 60));
    if (theTime1 > 60) {
      theTime2 = parseInt(String(theTime1 / 60));
      theTime1 = parseInt(String(theTime1 % 60));
    }
  }
  var result = "" + parseInt(theTime) + '秒';
  if (theTime1 > 0) {
    result = "" + parseInt(theTime1) + '分钟' + result;
  }
  if (theTime2 > 0) {
    result = "" + parseInt(theTime2) + '小时' + result;
  }
  return result;
}

export const FormatSeconds2 = (value: any) => {
  var theTime: any = parseInt(value);// 秒
  var theTime1: any = 0;// 分
  var theTime2: any = 0;// 小时
  if (theTime > 60) {
    theTime1 = parseInt(String(theTime / 60));
    theTime = parseInt(String(theTime % 60));
    if (theTime1 > 60) {
      theTime2 = parseInt(String(theTime1 / 60));
      theTime1 = parseInt(String(theTime1 % 60));
    }
  }
  var result = "" + parseInt(theTime) ;
  if (theTime1 > 0) {
    result = "" + parseInt(theTime1) + ':' + result;
  }
  if (theTime2 > 0) {
    result = "" + parseInt(theTime2) + ':' + result;
  }
  return result;
}

export const FormatTime = (time: any) => { //转换时间戳
  var date = new Date(time * 1000);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
  let Y = date.getFullYear() + '-';
  let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
  let D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate() + '';
  let h = ' ' + date.getHours() + ':';
  let m = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
  var DataArr: any = [];
  DataArr['ymd'] = Y + M + D;
  DataArr['hd'] = h + m;

  return DataArr;
  // return Y + M + D + h + m;
}
export const FormatTimePlus = (time: any) => {
  // 转换时间戳
  var date = new Date(time * 1000); // 时间戳为10位需*1000，时间戳为13位的话不需乘1000
  let Y = date.getFullYear() + '-';
  let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
  let D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' ';
  let h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
  let m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
  let s = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());

  var DataArr: any = [];
  DataArr['ymd'] = Y + M + D;
  DataArr['time'] = h + m + s; // 使用 'time' 作为包含小时、分钟和秒的键

  // return DataArr;
  // 如果你仍然想返回一个格式化的字符串而不是对象，可以取消注释下面这行
  return Y + M + D + ' ' + h + m + s;
};

export const FormatTimeCn = (time: number) => {
  // 定义时间单位的秒数
  const secondsInAMinute = 60;
  const minutesInAnHour = 60;

  // 计算总秒数
  const totalSeconds = Math.abs(time / 1000); // 将毫秒转换为秒

  // 计算小时、分钟和秒
  const hours = Math.floor(totalSeconds / minutesInAnHour);
  const minutes = Math.floor((totalSeconds % minutesInAnHour) / secondsInAMinute);
  const seconds = totalSeconds % secondsInAMinute;

  // 格式化时间字符串
  let result = "";
  if (hours > 0) result += hours + "小时";
  if (minutes > 0) result += minutes + "分";
  result += seconds + "秒";

  return result;
};

export const DiskCapacityConversion = (str: any, standard: any) => {
  if (!str) return ''
  const regex1 = /\((.+)\)/g;
  let Capacity: any = '';
  let point = 1;
  if (str.match(regex1)) { Capacity = Number(((str.match(regex1))[0]).replace(/[^0-9]/ig, "")); } else { Capacity = Number(str); }
  if (Capacity >= standard * 1000) { point = 100 }
  let k = standard;
  let sizes = [' MB', ' GB', ' TB', ' PB', ' EB', ' ZB', ' YB'];
  let i = Math.floor(Math.log(Capacity) / Math.log(k));
  return (Math.round((Capacity / Math.pow(k, i)) * point)) / point + sizes[i];
}

export const FormartMonthToNumber = (Month: any) => {
  var date = new Date(Month);
  if ((date.toDateString()).indexOf('Invalid') === -1) {
    date = new Date(Month);
  } else {
    date = new Date(Month.replace(/\\/g, ""));
  }
  var Y = date.getFullYear() + '-';
  var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
  var D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate() + ' ';
  return Y + M + D;
}

export const RemoveAllSpace = (str: string) => {
  return str.replace(/\s+/g, "");
}

export const RegExGetNum = (str: string) => {
  return Number(str.replace(/[^0-9]/ig, ""));
}
// let Gpp_tools = new Tools()
// export { Gpp_tools }

export const gpu_brand = (data: any) => {
  const CPUSubvendor = (data.VideoCard).split(' ')
  const regex = /\[(.+?)\]/g
  const VideoBrandArr = (data.VideoCard.match(regex))
  let VideoBrand = ''
  if (VideoBrandArr) {
    VideoBrand = (VideoBrandArr[VideoBrandArr.length - 1]).replace(/\[|]/g, '')
  }
  if (VideoBrand === '') {
    VideoBrand = CPUSubvendor[0]
  }
    const VideoBrand1Lower =VideoBrand.toLowerCase()
    if (VideoBrand1Lower.includes('清华同方') || VideoBrand1Lower.includes('tongfang') ||  VideoBrand1Lower.includes('thtf')) {
        return '品牌显示存在潜在法律风险'
    }
  return VideoBrand
}

export const  showNumberOfCPUCores  = (CPUSubNode: any) => {
  let CPUCoresPEHtml = '';
  // 大小核处理
  if (CPUSubNode.NumberofCPUCoresPerformance && CPUSubNode.NumberofCPUCoresEfficient) {
    if (CPUSubNode.NumberofCPUCoresPerformance !== 0 || CPUSubNode.NumberofCPUCoresEfficient !== 0) {
      if (CPUSubNode.NumberofCPUCoresLowPowerEfficient) {
        CPUCoresPEHtml = ' (' + CPUSubNode.NumberofCPUCoresPerformance + 'P+' + CPUSubNode.NumberofCPUCoresEfficient + 'E+' + CPUSubNode.NumberofCPUCoresLowPowerEfficient+')'
      } else {
        CPUCoresPEHtml = ' (' + CPUSubNode.NumberofCPUCoresPerformance + 'P+' + CPUSubNode.NumberofCPUCoresEfficient + 'E)'
      }
    }
  }
  // AMD AI 300系列
  if (CPUSubNode.NumberofCPUCoresClassic || CPUSubNode.NumberofCPUCoresCompact) {
    if (CPUSubNode.NumberofCPUCoresClassic !== 0 || CPUSubNode.NumberofCPUCoresCompact !== 0) {
      CPUCoresPEHtml += ' ('
      if (CPUSubNode.NumberofCPUCoresClassic && CPUSubNode.NumberofCPUCoresClassic !== 0) {
        CPUCoresPEHtml += CPUSubNode.NumberofCPUCoresClassic
      }
      if (CPUSubNode.NumberofCPUCoresCompact && CPUSubNode.NumberofCPUCoresCompact !== 0) {
        if (CPUSubNode.NumberofCPUCoresClassic && CPUSubNode.NumberofCPUCoresClassic !== 0) {
          CPUCoresPEHtml += '+'
        }
        CPUCoresPEHtml += CPUSubNode.NumberofCPUCoresCompact+'C'
      }
      CPUCoresPEHtml += ')'
    }
  }
  return CPUCoresPEHtml;
}

export  const  showNumberOfLogicalCpus =(CPUSubNode: any) =>{
  let CPULogicalPEHtml = '';
  if (CPUSubNode.NumberofLogicalCPUsPerformance && CPUSubNode.NumberofLogicalCPUsEfficient) {
    if (CPUSubNode.NumberofLogicalCPUsPerformance !== 0 || CPUSubNode.NumberofLogicalCPUsEfficient !== 0) {
      CPULogicalPEHtml = ' (' + CPUSubNode.NumberofLogicalCPUsPerformance + 'P+' + CPUSubNode.NumberofLogicalCPUsEfficient + 'E)'
    }
  }
  if (CPUSubNode.NumberofLogicalCPUsClassic || CPUSubNode.NumberofLogicalCPUsCompact) {
    if (CPUSubNode.NumberofLogicalCPUsClassic !== 0 || CPUSubNode.NumberofLogicalCPUsCompact !== 0) {
      CPULogicalPEHtml = ' ('
      let haveNumberofLogicalCPUsClassic = (CPUSubNode.NumberofLogicalCPUsClassic && CPUSubNode.NumberofLogicalCPUsClassic !== 0)
      if (haveNumberofLogicalCPUsClassic) {
        CPULogicalPEHtml += CPUSubNode.NumberofLogicalCPUsClassic
      }
      if (CPUSubNode.NumberofLogicalCPUsCompact && CPUSubNode.NumberofLogicalCPUsCompact !== 0) {
        if (haveNumberofLogicalCPUsClassic) {
          CPULogicalPEHtml += '+'
        }
        CPULogicalPEHtml += CPUSubNode.NumberofLogicalCPUsCompact+'C'
      }
      CPULogicalPEHtml += ')'
    }
  }

  return CPULogicalPEHtml
}

