<template>
    <div class="choose-monitor">
        <p class="describe">
            <span style="margin-left: 10px">{{ $t('DesktopMonitoring.StylesForYou') }}</span>
            <el-button color="#336AB5" :icon="Edit" @click="goToEdit">{{ $t('DesktopMonitoring.EditPlan') }}</el-button>
        </p>
        <div class="monitor-list">
            <p class="title">{{ $t('DesktopMonitoring.MonitoringStylePlan') }}</p>
             <div class="monitor-list-item_box scroll-y">
                <div class="monitor-list-item" :class="{'active': item.id === activeId}" v-for="item in monitorList" @click="handleClick(item.id)"
                    @mouseenter="showIcons = item.id"
                    @mouseleave="showIcons = null"
                    :key="item.id">
                    <div class="monitor-list-icons"  v-show="showIcons === item.id">
                        <el-tooltip class="item" effect="dark" :content="$t('InGameMonitor.Top')" placement="top" :delay="0" :duration="0"  :hide-after="0" :enterable="false" popper-class="custom_tooltip">
                            <el-icon class="el-icon-upload2 custom-icon"  @click.stop="pinToTop(item.id)"><Upload /></el-icon>
                        </el-tooltip>
                        <el-tooltip v-if="item.text" class="item" effect="dark" :content="$t('InGameMonitor.Delete')" placement="top" :delay="0" :duration="0"  :hide-after="0" :enterable="false" popper-class="custom_tooltip">
                            <el-icon class="el-icon-delete custom-icon"  @click.stop="deleteMonitor(item.id)"><Delete /></el-icon>
                        </el-tooltip>
                    </div>
                    <div v-if="item.text" class="monitor-text">
                    <p>{{ $t(item.text) }}</p><span>id：{{  item.code }}</span>
                    </div>
                    <img v-else :src="item.img" alt="">
                    <!-- <img :src="item.img" alt=""> -->
                    <div class="monitor-list-item_icon" :class="{'chosen': item.id === activeId}">
                        <el-icon v-show="item.id === activeId">
                            <Check/>
                        </el-icon>
                    </div>
                </div>
                <div class="add-monitor"  @click="addMonitor">
                    <span style="font-weight: 900;font-size: 26px;margin-bottom: 5px">+</span>
                    <span>{{ $t('DesktopMonitoring.AddDesktopMonitoring') }}</span>
                </div>
             </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import {Edit, Check} from "@element-plus/icons-vue";
import {onMounted, ref, defineEmits, computed} from "vue";
import img1 from "../assets/img/img_mapping.png"
import img2 from "../assets/img/img_lightbox.png"
import img3 from "../assets/img/img_starry_sky.png"
import useSensorData from '../shared/useSensorData'
import idb from '@/uitls/indexedDB'
import {Upload,Delete} from "@element-plus/icons-vue";
// import {gamepp} from 'gamepp'
const store = useSensorData()
const emit = defineEmits(["edit"])
// 当前选中的监控方案
const activeId = ref(-1)
const monitorList = ref([
    {id: 1, img: img1, text:'', code:''},
    {id: 2, img: img2, text:'', code:''},
    {id: 3, img: img3, text:'', code:''},
])
const showIcons = ref<number | null>(null);
onMounted(() => {
    checkLocal()
    // store.initializeSensors()
    const savedId = localStorage.getItem('activeMonitorId');
    console.log(savedId,'activeMonitorId')
    if (savedId) {
        activeId.value = parseInt(savedId);
        store.initializeSensors(activeId.value);
    }else{
        activeId.value = 1
        store.initializeSensors(1)
        localStorage.setItem('activeMonitorId', '1')
    }
})
    const nextCustomId = computed(() => {
        // 获取所有自定义方案的ID（有text属性的）
        const customIds = monitorList.value.filter(item => item.text).map(item => item.id);
        if (customIds.length === 0) return 9999;
        return Math.min(...customIds) - 1;
    });

    const generatedCode = ref('');
    function getRandomLetter() {
        const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
        return letters.charAt(Math.floor(Math.random() * letters.length));
    }

    function getRandomDigit() {
        return Math.floor(Math.random() * 10).toString();
    }

    function generateCode() {
        let result = '';
        // 按照字母-数字-字母-数字-字母的模式生成
        for (let i = 0; i < 5; i++) {
            if (i % 2 === 0) { 
                // 偶数位(0,2,4)字母
                result += getRandomLetter();
            } else { 
                // 奇数位(1,3)数字
                result += getRandomDigit();
            }
            if (result.length >= 5) break;
        }
        generatedCode.value = result;
    }

 const addMonitor = () => {
    generateCode()
    // const newId = monitorList.value.length + 1;
    const newId = nextCustomId.value;
    const newMonitor = {
        id: newId,
        img: '',
        text: 'DesktopMonitoring.CustomMonitoring',
        code: generatedCode.value
    };
    monitorList.value.push(newMonitor);
    localStorage.setItem('monitorList', JSON.stringify(monitorList.value));
    // localStorage.setItem('activeMonitorId', newId.toString());
    // store.initializeSensors(newId);
    saveDefaultSettings(newId);
 }

 const saveDefaultSettings = (id: number) => {
    const defaultSettings = {
        background: {
        type: "color",
        color: "#000000",
        img: "",
        video: "",
        img_video_display_type: "填充",
        opacity: 1,
        img_video_name: "",
        },
        size: {
        w: 600,
        h: 600,
        },
        font: {
        font_family: "Microsoft YaHei",
        },
        position: {
        screen: "",
        position: "upperRight",
        x: -1,
        y: -1,
        }
    };
    idb.setItem(`custom_monitor_window_setting_${id}`, defaultSettings);
  };

const handleClick = async(id: number) => {
    activeId.value = id
    window.localStorage.setItem("activeMonitorId", id.toString())
    //   window.localStorage.removeItem("sensorSettings")
    await store.initializeSensors(id);
}

const pinToTop = (id: number) => {
  const item = monitorList.value.find((item) => item.id === id);
  if (item) {
    monitorList.value = [item, ...monitorList.value.filter((item) => item.id !== id)];
    localStorage.setItem('monitorList', JSON.stringify(monitorList.value));
  }
};

const deleteMonitor = async (id: number) => {
  const index = monitorList.value.findIndex((item) => item.id === id);
  if (index !== -1) {
    monitorList.value.splice(index, 1);
    
    // 删除了选中的，重新更新选中状态，等设计确定效果后，再调整
    // if (!monitorList.value.some(item => item.id === activeId.value) && monitorList.value.length > 0) {
    //     activeId.value = monitorList.value[0].id;
    //     localStorage.setItem('activeMonitorId', activeId.value.toString());
    // }

    localStorage.setItem('monitorList', JSON.stringify(monitorList.value));
    localStorage.removeItem(`sensorSettings_${id}`);
    await idb.removeItem(`custom_monitor_window_setting_${id}`);
  }
};

function goToEdit() {
    // window.localStorage.setItem("activeMonitorId", activeId.value.toString())
    emit('edit')
}

function checkLocal() {
  const localMonitorId = window.localStorage.getItem("activeMonitorId");
  if (localMonitorId) {
    activeId.value = Number(localMonitorId);
  } else {
    activeId.value = 1;
    window.localStorage.setItem("activeMonitorId", "1");
  }
  const savedMonitorList = localStorage.getItem('monitorList');
  if (savedMonitorList) {
    monitorList.value = JSON.parse(savedMonitorList);
  }
}
</script>

<style scoped lang="scss">
.choose-monitor {
    width: 1260px;

    .describe {
        display: flex;
        flex-flow: row nowrap;
        justify-content: space-between;
        line-height: 32px;
        height: 32px;
        margin-bottom: 6px;
    }

    .monitor-list {
        width: 1260px;
        height: 552px;
        border-radius: 4px;
        padding: 15px 0 15px 15px;
        // display: flex;
        // flex-flow: row wrap;
        // gap: 5px;

        p.title {
            margin-bottom: 17px;
            width: 1240px;
        }
        .monitor-list-item_box {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            overflow-x: hidden;
            height: 530px;
        }
        .monitor-list-item {
            width: 197px;
            height: 330px;
            border-radius: 4px;
            position: relative;
            border: 1px dashed transparent;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            background: #383946;
            position: relative;
            &:hover{
                border: 1px dashed #409EFF;
                // background: #383946;
            }
            &.active {
                border: 1px solid #409EFF;
                // background: #383946;
            }

            img {
                max-width: 170px;
                max-height: 280px;
            }
            .monitor-text{
                font-size: 15px;
                font-weight: bold;
                display: flex;
                flex-direction: column;
                align-items: center;
                span{
                    color: #999;
                    font-size: 12px;
                    margin-top: 3px;
                }
            }
            .monitor-list-icons{
                position: absolute;
                right: 38px;
                top: 7px;
                gap: 10px;
                display: flex;
            }
        }

        .monitor-list-item_icon {
            position: absolute;
            right: 5px;
            top: 5px;
            width: 16px;
            height: 16px;
            border-radius: 8px;
            border: 1px solid #999999;
            text-align: center;

            &.chosen {
                background: #409EFF;
                border: 1px solid #409EFF;
            }
        }

        .add-monitor {
            width: 197px;
            height: 330px;
            border-radius: 4px;
            border: 1px dashed #666666;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #666666;
            font-size: 14px;
            &:hover{
                border: 1px dashed #409EFF;
            }
        }
    }
}
</style>
<style>
.monitor-list-icons .el-icon{
    font-size: 15px!important;
}
</style>
