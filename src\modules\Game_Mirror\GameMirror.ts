
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import '../../assets/iconfont/iconfont.css'
import '../../assets/iconfont/iconfont.js'
import App from './GameMirror.vue'
import './reset.css'
import './Game_Mirror_RTL.scss'
// import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import i18n from '../../assets/lang'
const app = createApp(App)

// app.use(ElementPlus)
app.use(createPinia())
app.use(i18n)
app.mount('#app')