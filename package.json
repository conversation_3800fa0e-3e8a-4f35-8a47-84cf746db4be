{"name": "gamepp", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev:Game_PressureTest": "npm run dev --page=Game_PressureTest", "build:Game_PressureTest": "npm run build --page=Game_PressureTest", "build:Game_PressureResult": "npm run build --page=Game_PressureResult", "dev:Game_PressureResult": "npm run dev --page=Game_PressureResult", "build:Game_FreeGame": "npm run build --page=Game_FreeGame", "build:Game_AutoShutDown": "npm run build --page=Game_AutoShutDown", "dev": "vite", "gamepp": "%LOCALAPPDATA%\\GamePPLite\\Temp\\electron.exe  /main b24d38f7f8ea72bdcf449d42e3764de3", "build": "vite build", "dev:Game_Home": "npm run dev --page=Game_Home", "dev:Game_FreeGame": "npm run dev --page=Game_FreeGame", "dev:Game_Update": "npm run dev --page=Game_Update", "Jenkins-Build-Front-WebUI-Webapp": "node scripts/buildAllPage.js", "Jenkins-Copy-Webapp-to-dist": "xcopy webapp\\windows\\pages dist /E /I /H", "build:All": "node scripts/buildAllPage.js && npm run watch", "build:Bg": "node scripts/buildBG.js", "build:Game_Home": "npm run build --page=Game_Home", "build:Game_CoreAssign ": "npm run build --page=Game_CoreAssign ", "build:Game_Mirror": "npm run build --page=Game_Mirror", "build:Game_ReboundDetail": "npm run build --page=Game_ReboundDetail", "build:Game_Rebound": "npm run build --page=Game_Rebound", "build:Game_ReboundLab": "npm run build --page=Game_ReboundLab", "build:Game_Setting": "npm run build --page=Game_Setting", "build:Game_Screenshot": "npm run build --page=Game_Screenshot", "build:Game_DMComponent": "npm run build --page=Game_DMComponent", "build:Game_Sensorchoose": "npm run build --page=Game_Sensorchoose", "build:Game_Sensorset": "npm run build --page=Game_Sensorset", "build:Game_Update": "npm run build --page=Game_Update", "build:Game_TimedShutdown": "npm run build --page=Game_TimedShutdown", "build:Game_Obs": "npm run build --page=Game_Obs", "build:Game_DesktopMonitor": "npm run build --page=Game_DesktopMonitorSet && npm run build --page=Game_DesktopMonitor", "build:inGame_monitor": "npm run build --page=inGame_monitor", "build:Lab": "npm run build --page=OnDesk_GameLab", "build:confilt": "npm run build --page=onDesk_Conflict", "syncBins": "BuildTools.exe --log --sync \\\\buildresult.gamepp.com\\buildsync\\GamePPLite\\Electron %LOCALAPPDATA%\\GamePPLite\\Temp /exclude win32,webapp.gpk,app.gpk", "syncDlls": "BuildTools.exe --log --sync \\\\buildresult.gamepp.com\\buildsync\\GamePPLite\\Apps\\8fb708e5972662062da26a2bb4753a23\\win32 %LOCALAPPDATA%\\GamePPLite\\Apps\\8fb708e5972662062da26a2bb4753a23\\1000000.0.0.0\\win32", "syncwebappPackage": "BuildTools.exe --log --sync \\\\buildresult.gamepp.com\\buildsync\\GamePPLite\\Apps\\8fb708e5972662062da26a2bb4753a23\\webapp.gpk %LOCALAPPDATA%\\GamePPLite\\Apps\\8fb708e5972662062da26a2bb4753a23\\1000000.0.0.0\\webapp.gpk", "syncNpmPkgs": "BuildTools.exe --log --sync \\\\buildresult.gamepp.com\\buildsync\\GamePPLite\\_SdkForWeb .\\node_modules", "watch": "BuildTools.exe --log --sync webapp %LOCALAPPDATA%\\GamePPLite\\Apps\\f647a66b00b8631745c1d63bb5c59e2b\\Debug", "watch3": "BuildTools.exe --watch --src=webapp  --dest=%LOCALAPPDATA%\\GamePPLite\\Apps\\f647a66b00b8631745c1d63bb5c59e2b\\Debug", "testsync": "BuildTools.exe --sync %LOCALAPPDATA%\\GamePPLite  --dest=\\192.168.112.8\\GamePP\\王胜灏\\轻量化测试", "syncwebapp": "BuildTools.exe --log --sync webapp \\\\192.168.112.8\\buildsync\\GamePPLite\\Apps\\f647a66b00b8631745c1d63bb5c59e2b\\Debug", "sync": "npm run syncBins && npm run syncDlls && npm run syncNpmPkgs && npm run syncwebappPackage && npm run watch", "deletecss": "del /Q /F \\\\buildresult.gamepp.com\\buildsync\\GamePPLiteLite\\Webapps\\f647a66b00b8631745c1d63bb5c59e2b\\Debug\\windows\\pages\\assets\\css\\*", "deletejs": "del /Q /F \\\\buildresult.gamepp.com\\buildsync\\GamePPLiteLite\\Webapps\\f647a66b00b8631745c1d63bb5c59e2b\\Debug\\windows\\pages\\assets\\js\\*", "watch2": "BuildTools.exe --watch --src=webapp  --dest=%LOCALAPPDATA%\\GamePPLite\\Apps\\f647a66b00b8631745c1d63bb5c59e2b\\Debug", "tsc": "tsc webapp\\windows\\background\\background.ts"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vitejs/plugin-vue": "^5.0.4", "axios": "^1.6.8", "crypto-js": "^4.2.0", "echarts": "^5.5.0", "element-plus": "^2.7.2", "glob": "^10.3.12", "gsap": "^3.12.7", "lodash-es": "^4.17.21", "md5": "^2.3.0", "mitt": "^3.0.1", "pinia": "^2.1.7", "sass": "^1.75.0", "vite-plugin-compression": "^0.5.1", "vue": "^3.4.21", "vue-echarts": "^6.7.2", "vue-i18n": "^9.13.1", "vue-router": "^4.3.0", "vue3-count-to": "^1.1.2", "xlsx": "^0.18.5"}, "devDependencies": {"@iconify-json/ep": "^1.1.14", "@types/md5": "^2.3.5", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "@vitejs/plugin-vue": "^5.0.4", "@vue/test-utils": "^2.4.5", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.20.1", "husky": "^8.0.3", "javascript-obfuscator": "^4.1.1", "jsdom": "^24.0.0", "lint-staged": "^15.2.0", "prettier": "^3.2.4", "sass": "^1.70.0", "unplugin-auto-import": "^0.17.8", "unplugin-icons": "^0.18.2", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.8", "vite-plugin-compression": "^0.5.1", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.0.25", "vitest": "^1.4.0"}}