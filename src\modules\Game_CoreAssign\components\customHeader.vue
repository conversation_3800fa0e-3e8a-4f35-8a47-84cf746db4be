<script setup lang="ts">
// @ts-ignore
import RightTopIcons from "@/components/mainCom/RightTopIcons.vue";

const gamepp = window.gamepp as any;
import { ref } from 'vue'

let isFullScreen = ref(false) // 是否处于全屏模式
const windowName = "processCoreAssign"
const methods = {
    async minimizeWindow() {
        await gamepp.webapp.windows.minimize.promise(windowName);
    },
    async closeWindows() {
        gamepp.webapp.windows.close.promise(windowName);
    },
    // 全屏
    async maximizeWindow() {
        isFullScreen.value = true;
        gamepp.webapp.windows.maximize.promise(windowName);
    },
    // 还原
    unmaximizeWindow() {
        isFullScreen.value = false;
        gamepp.webapp.windows.unmaximize.promise(windowName);
    }
}
</script>

<template>
  <header>
    <img class="logo" src="../../../assets/img/Public/logo_gpp.png" alt="">
    <span>
      <slot name="default"></slot>
    </span>

      <RightTopIcons
          style="margin-left: auto;"
          close-icon
          minimize-icon
          maximize-icon
          :item-h="40"
          hover-color="#22232e"
          @close="methods.closeWindows"
          @minimize="methods.minimizeWindow"
          @maximize="methods.maximizeWindow"
          @unmaximize="methods.unmaximizeWindow"
          :is-full-screen="isFullScreen"
      />
  </header>
</template>

<style scoped lang="scss">
header {
  -webkit-app-region: drag;
  color: #ffffff;
  font-size: 12px;
  width: 100%;
  height: 40px;
  background: #2D2E39;
  box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.3);
  padding-left: 5px;
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  overflow: hidden;

  .logo {
    width: 20px;
    height: 20px;
    margin-left: 8px;
    margin-right: 10px;
    // -webkit-app-region: no-drag;
  }

  span {
    // -webkit-app-region: no-drag;
  }

  .icon {
    // -webkit-app-region: no-drag;
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    &:hover {
      background: #414141;
    }
  }

  .warning {
    &:hover {
      background: #5b261f;
    }
  }
}
.ml-auto {
  margin-left: auto;
}
</style>
