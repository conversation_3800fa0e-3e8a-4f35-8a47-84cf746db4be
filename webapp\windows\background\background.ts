// "strict";

// @ts-ignore
const gamepp:any = window.gamepp

// import gameclient from './js/game_client'

let WindowName = {
    BACKGROUND                   : 'background',
    DESKTOP                      : 'desktop',
    UPDATE                       : 'update',
    TRAYMENU                     : 'traymenu',
    Desktop_Monitor              : 'desktop_monitor',
    IN_GAME_MAIN                 : "ingame_main",
    IN_GAME_MONITOR              : "ingame_monitor",
    IN_GAME_TipsPage             : "ingame_tipspage",
    IN_GAME_Quit_Game_Tips       : "ingame_quit_game_tips",
}

//模块安装状态
let moduleInstalled = {
    "rebound":false, //游戏退弹
    "mirror":false //游戏滤镜
}

const ProcessAcess = {
    GPP_MONITORY_ACCESS    : 1,  //游戏内监控位移
    GPP_RESHADE_ACCESS     : 2,   //游戏滤镜位移
    GPP_VIDEO_ACCESS       : 4,     //录像位移
    GPP_PHOTO_ACCESS       : 8,     //截图位移
    GPP_DIRECTOR_ACCESS    : 16,  //导演模式 位移
    GPP_NOTES_ACCESS       : 32,     //笔记位移
    GPP_BARRAGE_ACCESS     : 64,   //弹幕位移
    GPP_TARKOV_PRICE_ACCESS: 128, //塔科夫价格位移(已取消功能)
    GPP_TARKOV_DELAY_ACCESS: 256, //塔科夫延迟位移(已取消功能)
    GPP_TFT_ACCESS         : 512,     //云顶工具位移
}

let COMMANDID = {
    CM_ERROR                                                    : 0,
    CM_UNDEFINE                                                 : 1,
    CM_ENABLE_EFFECT                                            : 2,   //  画质开关(boolean)
    CM_ENABLE_HW_MONITING                                       : 3,   //  硬件信息显示开关(boolean)
    CM_SHOW_MAINUI                                              : 4,   //  以指定方式显示主程序(桌面上唤出窗体)
    CM_SET_SCREENPATH                                           : 5,   //  设置截图路径
    CM_SET_VIDEOPATH                                            : 6,   //  设置录像路径
    CM_SET_GENERALPATH                                          : 7,   //  设置通用存储路径
    CM_HOTKEY_SHOW_MAINUI                                       : 8,   //  设置游戏内显示主程序的快捷键
    CM_HOTKEY_SHOW_MAINUI_INFO                                  : 9,   //  设置游戏内显示主程序的快捷键文字描述(Ctrl+F1)
    CM_HOTKEY_SWITCH_EFFECT_RESOLUTION                          : 10,  //  设置游戏内切换画质优化方案的快捷键(Ctrl+F3)
    CM_HOTKEY_SWITCH_EFFECT_RESOLUTION_INFO                     : 11,  //  设置游戏内切换画质优化方案的快捷键值
    CM_HOTKEY_PRESENT_EFFECT_SWITCH                             : 12,  //  设置开关画质优化效果的快捷键(Ctrl+F5)
    CM_HOTKEY_PRESENT_EFFECT_SWITCH_INFO                        : 13,  //  设置开关画质优化效果的快捷键值
    CM_HOTKEY_HARDWARE_MONITING_SWITCH                          : 14,  //  设置开关游戏内数据显示的快捷键(F10)
    CM_HOTKEY_HARDWARE_MONITING_SWITCH_INFO                     : 15,  //  设置开关游戏内数据显示的快捷键值
    CM_HOTKEY_RANKS_QUERY_SWITCH                                : 16,  //  设置开关游戏内战绩查询的快捷键(Ctrl+L)
    CM_HOTKEY_RANKS_QUERY_SWITCH_INFO                           : 17,  //  设置开关游戏内战绩查询的快捷键值
    CM_HWM_RUNTIME_SWITCH                                       : 18,  //  运行时间
    CM_HWM_CURRENT_TIME_SWITCH                                  : 19,  //  当前时间
    CM_HWM_DOWNLOAD_SPEED_SWITCH                                : 20,  //  下载速度
    CM_HWM_UPLOAD_SPEED_SWITCH                                  : 21,  //  上传速度
    CM_HWM_NETWORK_PING_SWITCH                                  : 22,  //  网络延迟信息
    CM_HWM_GPU_USAGE_SWITCH                                     : 23,  //  显卡占用
    CM_HWM_GPU_TEMP_SWITCH                                      : 24,  //  显卡温度
    CM_HWM_GPU_FREQUENCY_SWITCH                                 : 25,  //  显卡频率
    CM_HWM_GPU_FAN_SPEED_SWITCH                                 : 26,  //  显卡风扇转速
    CM_HWM_GPU_TYPE_INFO                                        : 27,  //  显卡名称
    CM_HWM_GPU_DRAW_COUNT                                       : 28,  //  显卡渲染数目
    CM_HWM_GPU_VENDER_ID                                        : 29,  //  显卡VenderId
    CM_HWM_VM_USAGE_SWITCH                                      : 30,  //  显存占用
    CM_HWM_VM_DISPLAY_TYPE                                      : 31,  //  显存占用的显示方式（百分比/使用/剩余）
    CM_HWM_VM_FREQUENCY_SWITCH                                  : 32,  //  显存频率
    CM_HWM_SM_USAGE_SWITCH                                      : 33,  //  系统内存占用
    CM_HWM_SM_DISPLAY_TYPE                                      : 34,  //  系统内存占用的显示方式（百分比/使用/剩余）
    CM_HWM_SM_SIZE                                              : 35,  //  内存大小
    CM_HWM_CPU_USAGE_SWITCH                                     : 36,  //  CPU占用
    CM_HWM_CPU_TEMPRATURE_SWITCH                                : 37,  //  CPU温度
    CM_HWM_CPU_FREQUENCY_SWITCH                                 : 38,  //  CPU频率
    CM_HWM_CPU_FAN_SPEED_SWITCH                                 : 39,  //  CPU风扇转速
    CM_HWM_CPU_TYPE_INFO                                        : 40,  //  CPU名称
    CM_VC_VIDEO_SWITCH                                          : 41,  //  是否启用录像的大开关
    CM_VC_NORMAL_MODE_SWITCH                                    : 42,  //  是否使用正常录制的功能开关
    CM_VC_NORMAL_HOTKEY                                         : 43,  //  正常录制的快捷键开按键值
    CM_VC_NORMAL_HOTKEY_INFO                                    : 44,  //  正常录制的快捷键名称
    CM_VC_STREAMING_MODE_SWITCH                                 : 45,  //  回溯录制的功能开关， 如果该功能关闭， 那么回溯录制的任何操作将不可用， 包括快捷键
    CM_VC_STREAMING_HOTKEY                                      : 46,  //  回溯录制的快捷键开关按键值
    CM_VC_STREAMING_HOTKEY_INFO                                 : 47,  //  回溯录制的快捷键开关显示名称
    CM_VC_STREAMING_EX_MODE_SWITCH                              : 48,  //  增强版回溯录制---》 高能录制模式功能开关
    CM_VC_STREAMING_EX_MODE_STRONG_SWITCH                       : 49,  //  高能录制模式下（牛逼时刻）
    CM_VC_STREAMING_EX_MODE_LOSER_SWITCH                        : 50,  //  高能录制模式下（菜鸟时刻）
    CM_VR_VIDEO_RELIVE_SWITCH                                   : 51,  //  AMD relive
    CM_VR_VIDEO_NORMAL_SWITCH                                   : 52,  //  精彩一刻
    CM_VR_VIDEO_RESOLUTION                                      : 53,  //  视频分辨率
    CM_VR_VIDEO_FPS                                             : 54,  //  视频FPS
    CM_VR_VIDEO_BITRATE                                         : 55,  //  视频码率
    CM_VR_VIDEO_STREAMING_TIME                                  : 56,  //  回溯时间段
    CM_PC_PHOTO_SWITCH                                          : 57,  //  是否启用截图的功能开关
    CM_PC_NORMAL_MODE_SWITCH                                    : 58,  //  是否使用正常截图的功能开关
    CM_PC_NORMAL_MODE_HOTKEY                                    : 59,  //  正常截图快捷键开按键值
    CM_PC_NORMAL_MODE_HOTKEY_INFO                               : 60,  //  正常截图快捷键名称
    CM_PC_STREAMING_EX_MODE_SWITCH                              : 61,  //  高能截图模式功能开关呢
    CM_PC_STREAMING_EX_MODE_STRONG_SWITCH                       : 62,  //  高能截图模式下（牛逼时刻）
    CM_PC_STREAMING_EX_MODE_LOSER_SWITCH                        : 63,  //  高能截图模式下（菜鸟时刻）
    CM_FPS_LIMIT_CAPPED_SWITCH                                  : 64,  //  FPS 限制上限开关
    CM_FPS_LIMIT_CAPPED                                         : 65,  //  FPS 限制上限值
    CM_RI_RECORD_NAME                                           : 66,  //  战绩查询所查询的游戏名称
    CM_WS_GENERATE_SQUAD                                        : 67,  //  创建小队（包含创建和重建）
    CM_WS_JOIN_SQUAD_INITIATIVE                                 : 68,  //  加入小队(主动加入)
    CM_WS_LEAVE_SQUAD_INITIATIVE                                : 69,  //  离开小队(主动离开)
    CM_WS_JOIN_SQUAD_NOTIFY                                     : 70,  //  加入小队(通知有人加入)
    CM_WS_LEAVE_SQUAD_NOTIFY                                    : 71,  //  离开小队(通知有人离开)
    CM_HWM_FS_CURRENT_TYPE                                      : 72,  //  当前硬件显示信息的字体大小(0,1,2,,,,)
    CM_RESHADE_TYPE                                             : 73,  //  画质方案选项（0,1,2,,,）
    CM_KS_WIN                                                   : 74,  //  屏蔽win
    CM_KS_SHIFT                                                 : 75,  //  屏蔽shift
    CM_KS_CTRL_SPACE                                            : 76,  //  屏蔽ctrl+space
    CM_KS_CTRL_SHFIT                                            : 77,  //  屏蔽ctrl+shift
    CM_QUIT                                                     : 78,  //  退出命令
    CM_WS_SEND_PICTURE_HOTKEY                                   : 79,  //  发送图片热键值
    CM_WS_SEND_PICTURE_HOTKEY_INFO                              : 80,  //  发送图片热键文本
    CM_WS_FIRST_PLAYER_OPEN_PICTURE_HOTKEY                      : 81,  //  1号位置查看/关闭图片热键值
    CM_WS_FIRST_PLAYER_OPEN_PICTURE_HOTKEY_INFO                 : 82,  //  1号位置查看/关闭图片热键文本
    CM_WS_SECOND_PLAYER_OPEN_PICTURE_HOTKEY                     : 83,  //  2号位置查看/关闭图片热键值
    CM_WS_SECON_PLAYER_OPEN_PICTURE_HOTKEY_INFO                 : 84,  //  2号位置查看/关闭图片热键文本
    CM_WS_THIRD_PLAYER_OPEN_PICTURE_HOTKEY                      : 85,  //  3号位置查看/关闭图片热键值
    CM_WS_THIRD_PLAYER_OPEN_PICTURE_HOTKEY_INFO                 : 86,  //  3号位置查看/关闭图片热键文本
    CM_WS_FOURTH_PLAYER_OPEN_PICTURE_HOTKEY                     : 87,  //  4号位置查看/关闭图片热键值
    CM_WS_FOURTH_PLAYER_OPEN_PICTURE_HOTKEY_INFO                : 88,  //  4号位置查看/关闭图片热键文本
    CM_WS_SHOW_PICTURE_TIME                                     : 89,  //  图片展示时长
    CM_WS_AUTO_SHOW_PICTURE                                     : 90,  //  接收到图片自动展示
    CM_WS_PLAYER_NAME                                           : 91,  //  勇士系统里的用户名
    CM_WS_PLAYER_TOKEN                                          : 92,  //  勇士系统的唯一标识
    CM_WS_SCREEN_AZIMUTH                                        : 93,  //  勇士系统发送图片是否包含方位角
    CM_WS_SWITCH_USER_PICTURE_HOTKEY                            : 94,  //  切换显示下一张图片热键
    CM_WS_SWITCH_USER_PICTURE_HOTKEY_INFO                       : 95,  //  切换显示下一张图片热键文本
    CM_RESHADE_SELECTED                                         : 96,  //  是否选择了加载Reshade方案
    CM_RESHADE_SELECTED_INFO                                    : 97,  //  选择的reshade文件名
    CM_SET_SELF_RESHADE_FILEPATH                                : 98,  //  设置需要加载的reshade配置文件路径
    CM_SET_PUSHMESSAGE                                          : 99,  //  设置推送消息状态
    CM_HOTKEY_TIPS                                              : 100,  //  热键提示标志
    CM_USER_BIGJUMP                                             : 101,  //  大跳标志
    CM_SEND_PICTURE_HOTKEY_SPACTIME                             : 102,  //  发送图片热键按键间隔
    CM_MAPSFRAME_HOTKEY                                         : 103,  //  唤出地图显示热键值
    CM_MAPSFRAME_HOTKEY_INFO                                    : 104,  //  唤出地图显示热键文本
    CM_VIDEO_CAPTURE_MOUSE                                      : 105,  //  录像捕获鼠标
    CM_VIDEO_CAPTURE_MICROPHONE                                 : 106,  //  录像捕获麦克风
    CM_ISSET_LOBBY_LANGUGE_CHINESE                              : 107,  //  是否设置游戏大厅为中文
    CM_SHARE_MATCHEND_PICTURE                                   : 108,  //  是否开启分享游戏结束时的图像
    CM_CAPTURE_VOICE_SWITCH                                     : 109,  //  是否开启语音
    CM_CAPTURE_VOICE_HOTKEY                                     : 110,  //  语音热键
    CM_CAPTURE_VOICE_HOTKEY_INFO                                : 111,  //  语音热键文本
    CM_FLIGHT_PATH_SWITCH                                       : 112,  //  飞行航行路线（是否显示航线的开关）
    CM_FLIGHT_FRAME_SWITCH                                      : 113,  //  飞行航行图的开关(整个功能的服务器开关)
    CM_FLIGHT_PATH_HOTKEY                                       : 114,  //  飞行航行路线热键
    CM_FLIGHT_PATH_HOTKEY_INFO                                  : 115,  //  飞行航行路线热键文本
    CM_SHARE_MATCHEND_PICTURE_SWITCH                            : 116,  //  是否开启分享功能开关
    CM_SHARE_MATCHEND_PICTURE_URL                               : 117,  //  分享上传路径
    CM_FLIGHT_FRAME_CHECK                                       : 118,  //  飞行航行图 选中与否(整个功能的界面开关)
    CM_MAP_FRAME_SWITCH                                         : 119,  //  资源点地图功能开关
    CM_MODULE_RUN_SWITCH                                        : 120,  //  硬件显示开关(网络开关)
    CM_IMGAGE_OPTI_SWITCH                                       : 121,  //  画质优化开关(网络开关)
    CM_XUNFEI_APPID                                             : 122,  //  讯飞语翻ID
    CM_HWM_FS_CURRENT_COLOR                                     : 123,  //  硬件信息当前字体颜色
    CM_HWM_FS_CURRENT_CATEGORY                                  : 124,  //  硬件信息当前字体类别（宋体、 雅黑）
    CM_HWM_CPU_USAGE_DETAILED_SWITCH                            : 125,  //  CPU占用每个核心详细信息
    CM_HWM_CPU_FREQUENCY_DETAILED_SWITCH                        : 126,  //  CPU频率每个核心详细信息
    CM_HWM_CPU_TEMPRATURE_DETAILED_SWITCH                       : 127,  //  CPU温度每个核心详细信息
    CM_HWM_VER_POS_X                                            : 128,  //  纵向显示硬件信息窗体的X坐标
    CM_HWM_VER_POS_Y                                            : 129,  //  纵向显示硬件信息窗体的Y坐标
    CM_HWM_HOR_POS_X                                            : 130,  //  横向显示硬件信息窗体的X坐标
    CM_HWM_HOR_POS_Y                                            : 131,  //  横向显示硬件信息窗体的Y坐标
    CM_CUT_MOUSE_SPEED_SWITCH                                   : 132,  //  减小鼠标速度状态开关
    CM_CUT_MOUSE_SPEED_HOTKEY                                   : 133,  //  减小鼠标速度热键
    CM_CUT_MOUSE_SPEED_HOTKEY_INFO                              : 134,  //  减小鼠标速度热键文本
    CM_ADD_MOUSE_SPEED_SWITCH                                   : 135,  //  增加鼠标速度状态
    CM_ADD_MOUSE_SPEED_HOTKEY                                   : 136,  //  增加鼠标速度热键
    CM_ADD_MOUSE_SPEED_HOTKEY_INFO                              : 137,  //  增加鼠标速度热键文本
    CM_MUTE_SWITCH                                              : 138,  //  静音开关
    CM_MUTE_HOTKEY                                              : 139,  //  静音开关热键
    CM_MUTE_HTOKEY_INFO                                         : 140,  //  静音开关热键文本
    CM_CUT_VOLUME_SWITCH                                        : 141,  //  减小音量开关
    CM_CUT_VOLUME_HOTKEY                                        : 142,  //  减小音量开关热键
    CM_CUT_VOLUME_HOTKEY_INFO                                   : 143,  //  减小音量开关热键文本
    CM_ADD_VOLUME_SWITCH                                        : 144,  //  增加音量开关
    CM_ADD_VOLUME_HOTKEY                                        : 145,  //  增加音量开关热键
    CM_ADD_VOLUME_HOTKEY_INFO                                   : 146,  //  增加音量开关热键文本
    CM_HOTKEY_SHOW_RESHDE_OPTI                                  : 147,  //  设置游戏内显示画质优化的快捷键
    CM_HOTKEY_SHOW_RESHDE_OPTI_INFO                             : 148,  //  设置游戏内显示画质优化的快捷键文字描述(Ctrl+F1)
    CM_PC_INCLUDE_GAMEUI                                        : 149,  //  截图时保存产品的信息
    CM_SCREENHOT_AUTO_SWITCH                                    : 150,  //  自动保存截图开关
    CM_SCREENHOT_AUTO_INTERVAL_TIME                             : 151,  //  自动保存截图间隔时间
    CM_SAVE_IP_SWITCH                                           : 152,  //  保存IP 到本地文件中
    CM_SHOW_SOFT_VERSION_SWITCH                                 : 153,  //  显示本软件的版本号
    CM_SHOW_SOFT_VERSION_INFO                                   : 154,  //  显示软件的版本号文本
    CM_NOT_SHOW_SCREEN_SUCCESS_SWITCH                           : 155,  //  不显示截图保存路径信息
    CM_NOT_SHOW_RELEASE_MEMORY_SWITCH                           : 156,  //  不显示自动释放内存信息
    CM_AUTO_RELEASE_MEMORY_SWITCH                               : 157,  //  自动释放内存
    CM_AUTO_RELEASE_MEMORY_TIME                                 : 158,  //  自动释放内存间隔时间
    CM_TELL_HOUR_TIME_SWITCH                                    : 159,  //  整点报时
    CM_TELL_ZERO_TIME_SWITCH                                    : 160,  //  凌晨提醒
    CM_SET_BKIMAGE_FRAME_SWITCH                                 : 161,  //  带边框背景
    CM_USE_SHOW_HOR_INFO                                        : 162,  //  使用横向信息显示(0:竖向， 1：横向 ，2：竖向2)
    CM_VIDEO_INCLUDE_GAMEUI                                     : 163,  //  录像保留信息
    CM_VR_VIDEO_BITRATE_INDEX                                   : 164,  //  视频码率选择项
    CM_VR_VIDEO_STREAMING_TIME_INDEX                            : 165,  //  回溯时间段选择项
    CM_VR_VIDEO_PLAN_INDEX                                      : 166,  //  视频录制选择方案
    CM_RUN_WITH_WINDOWS_START                                   : 167,  //  随windows开机启动
    CM_CHECK_UPDATE_AUTO                                        : 168,  //  自动检查更新
    CM_TRAY_ICON_HIDE                                           : 169,  //  隐藏托盘图标
    CM_VIDEO_COMPATIBLE_MODEL                                   : 170,  //  录像使用兼容模式
    CM_PC_FILE_FORMAT_INDEX                                     : 171,  //  截图文件保存格式（1：bmp,2:png 3:jpg）
    CM_SOFT_RUN_TIME                                            : 172,  //  软件运行时长
    CM_USE_DESKTOP_FRAME                                        : 173,  //  使用 桌面显示
    CM_PLAY_IDENTITY_VOICE_SWITCH                               : 174,  //  播放语音开关
    CM_PLAY_IDENTITY_VOICE_HOTKEY                               : 175,  //  播放语音热键值
    CM_PLAY_IDENTITY_VOICE_HOTKEY_INFO                          : 176,  //  播放语音热键值文本
    CM_HWM_FPS_SWITCH                                           : 177,  //  FPS显示
    CM_USE_VISION_SWITCH                                        : 178,  //  使用勇士系统的标识
    CM_RECORD_DATA_SWITCH                                       : 179,  //  战报显示开关
    CM_RECORD_DATA_HOTKEY                                       : 180,  //  战报显示热键
    CM_RECORD_DATA_HOTKEY_INFO                                  : 181,  //  战报显示热键文本
    CM_LAN_SOCKET_QRCODE                                        : 182,  //  局域网socket链接二维码
    CM_HTML_RESOUCE_TEAM_MD5                                    : 183,  //  HTML Team本地资源包MD5
    CM_HTML_RESOUCE_RECORD_MD5                                  : 184,  //  HTML Record本地资源包MD5
    CM_AUDIO_SERVICE_SWITCH                                     : 185,  //  音频服务开关
    CM_PUBG_TEAM_SYSTEM_HOTKEY                                  : 186,  //  pubg++组队显示或关闭热键
    CM_PUBG_TEAM_SYSTEM_HOTKEY_INFO                             : 187,  //  pubg++组队显示或关闭热键文本
    CM_STOP_SPECIFY_VOICE_HOTKEY                                : 188,  //  停止正在播放的语音热键
    CM_STOP_SPECIFY_VOICE_HOTKEY_INFO                           : 189,  //  停止正在播放的语音热键文本
    CM_PC_COMPRESSION_RATIO                                     : 190,  //  可视化报点保存图片的压缩比例
    CM_SWITCH_INPUT_METHOD                                      : 191,  //  游戏时自动切换为英文输入法
    CM_HWM_VOLUME_VALUE                                         : 192,  //  扬声器音量值
    CM_HWM_MICROPHONE_VALUE                                     : 193,  //  麦克风音量值
    CM_HWM_MOUSE_SPEED_VALUE                                    : 194,  //  鼠标速度值
    CM_HTML_VOICELIST_REFRESH                                   : 195,  //  语音列表HTML刷新命令
    CM_IQ_ENHANCED_DARK_VALUE                                   : 196,  //  暗部细节增强 值
    CM_IQ_ENHANCED_DARK_SWITCH                                  : 197,  //  暗部细节增强开关（针对游戏生效）
    CM_IQ_DIGITAL_VIBRATION_VALUE                               : 198,  //  数字振动 数值
    CM_IQ_VERTREFRESH_SWITCH                                    : 199,  //  设置刷新率为144的开关
    CM_HWM_CPU_POWERS_SWITCH                                    : 200,  //  CPU功耗开关
    CM_HWM_INTERVAL_TIME                                        : 201,  //  获取硬件信息的间隔时间
    CM_HWM_MEMORY_FREQUENCY_SWITCH                              : 202,  //  内存频率开关
    CM_IQ_VERTREFRESH_VALUE                                     : 203,  //  当前刷新率值
    CM_HTML_SETTING_REFRESH                                     : 204,  //  设置HTML刷新
    CM_RECORD_DATA_REFRESH                                      : 205,  //  战报更新消息
    CM_IQ_ENHANCED_DARK_ALL_SWITCH                              : 206,  //  暗部细节增强开关
    CM_IQ_DIGITAL_VIBRATION_SWITCH                              : 207,  //  数字振动 开关
    CM_IQ_VERTREFRESH_ALL_SWITCH                                : 208,  //  设置刷新率 开关
    CM_IQ_DIGITAL_VIBRATION_GAME_SWITCH                         : 209,  //  数字振动针对游戏有效的开关
    CM_IQ_ENHANCED_DARK_DEFAULT_VALUE                           : 210,  //  暗部细节增强默认值
    CM_IQ_DIGITAL_VIBRATION_DEFAULT_VALUE                       : 211,  //  数字振动默认值
    CM_PUBG_USE_MAKE_TEAM_SYSTEM                                : 212,  //  是否使用组队系统标志
    CM_IQ_ENHANCED_DARK_SETTING_DONE                            : 213,  //  暗部细节增强 值更改完成标志
    CM_IQ_DIGITAL_VIBRATION_SETTTING_DONE                       : 214,  //  数字振动 数值 更改完成标志
    CM_IQ_HIGHEST_DISPLAY_FREQUENCY                             : 215,  //  当前显示器的最大刷新率
    CM_IQ_DISPLAY_FREQUENCY_DEFAULT                             : 216,  //  显示器的刷新率默认值
    CM_REFRESH_HARDWARE_GENERAL_SITUATION                       : 217,  //  更新所有硬件信息（主板、CPU、显卡）
    CM_REFRESH_MATCH_DATA_DONE                                  : 218,  //  当局对战数据已写入完成
    CM_MATCH_VAILED_TIME                                        : 219,  //  每局对战的有效时长（记录标志）
    CM_HWM_POST_DATA_MD5                                        : 220,  //  上次硬件信息的md5值
    CM_LOCAL_IP_PORT_APP_CONNECT                                : 221,  //  与APP相连接的本机 socket信息 ip 和 端口
    CM_DESK_CPU_SWITCH                                          : 222,  //  桌面显示CPU信息
    CM_DESK_GPU_SWITCH                                          : 223,  //  桌面显示GPU信息
    CM_DESK_MEMORY_SWITCH                                       : 224,  //  桌面显示内存信息
    CM_DESK_GPUMEMORY_SWITCH                                    : 225,  //  桌面显示显存信息
    CM_DESK_NETWORK_SWITCH                                      : 226,  //  桌面显示网络信息
    CM_DESK_TITLEBAR_SWITCH                                     : 227,  //  桌面显示标识
    CM_DESK_TRANSPARENT_VALUE                                   : 228,  //  桌面透明度值
    CM_SPEED_POWER_SOLUTION_SWITCH                              : 229,  //  Power Solution
    CM_SPEED_CPU_CORES_PLAN_SWITCH                              : 230,  //  CPU Cores Plan
    CM_SPEED_AUTOMATIC_SYSTEM_SWITCH                            : 231,  //  Automatic Closing of System in Game
    CM_SPEDD_RELEASE_MEMORY_SWITCH                              : 232,  //  Auto Release Memory
    CM_DESK_LOCATION_X                                          : 233,  //  桌面显示x坐标
    CM_DESK_LOCATION_Y                                          : 234,  //  桌面显示y坐标
    CM_KS_SHIFT_SPACE                                           : 235,  //  Shielding‘Shift+Space’
    CM_KS_SHIFT_ALT                                             : 236,  //  Shielding‘Shift + Alt’
    CM_KS_SHIFT_CAPSLOCK                                        : 237,  //  Shielding‘Shift + Capslock’
    CM_KS_CTRL_CAPSLOCK                                         : 238,  //  Shielding‘Ctrl+Capslock’
    CM_KS_ALT_HEN                                               : 239,  //  Shielding‘Alt + ~
    CM_KS_SWITCH_TOTAL                                          : 240,  //  热键屏蔽总开关
    CM_KS_WIN_SPACE                                             : 241,  //  Shielding‘win + space’
    CM_LANGUAGE_TYPE                                            : 242,  //  语言类别
    CM_TANKS_WOTDISABLEMULTICORESUPPORT_SWTICH                  : 243,  //  禁用坦克世界多核心支持---硬盘温度
    CM_TANKS_WOTREDUCEOBJECTDETAILLEVEL_SWITCH                  : 244,  //  减少对象细节级别
    CM_TANKS_WOTREDUCEDSOUNDQUALITY_SWITCH                      : 245,  //  降低音频质量
    CM_TANKS_WOTEXHAUSTSMOKE_SWITCH                             : 246,  //  坦克发动机排气烟雾
    CM_TANKS_WOTWRECKAGESMOKE_SWITCH                            : 247,  //  建筑残骸破坏烟雾
    CM_TANKS_WOTSHOTSMOKEANDFLAMES_SWITCH                       : 248,  //  开火烟雾及火焰
    CM_TANKS_WOTSHELLEXPLOSIONEFFECTS_SWITCH                    : 249,  //  炮弹爆炸效果
    CM_TANKS_WOTTANKHITEFFECTS_SWITCH                           : 250,  //  坦克击中效果
    CM_TANKS_WOTTANKDESTRUCTIONEFFECTS_SWITCH                   : 251,  //  坦克破坏效果
    CM_TANKS_WOTCLOUDS_SWITCH                                   : 252,  //  天上的云朵朵
    CM_TANKS_QUALITY_SELECTION_SWITCH                           : 253,  //  画质开关
    CM_TANKS_QUALITY_TYPE_VALUE                                 : 254,  //  方案值
    CM_TANKS_INSTALL_PATH                                       : 255,  //  坦克世界安装路径
    CM_TANKS_BACKGROUND_PATH                                    : 256,  //  坦克世界加载背景的路径
    CM_PUBG_CPU_CORES_SWITCH                                    : 257,  //  cpu cores plan
    CM_PUBG_TANK_ROLLING_SWITCH                                 : 258,  //  close tank rolling
    CM_PUBG_DEATH_PLAYBACK_SWITCH                               : 259,  //  death playback
    CM_PUBG_WHOLE_PLAYBACK_SWITCH                               : 260,  //  whole playback
    CM_PUBG_QUALITY_SWITCH                                      : 261,  //  画质开关
    CM_PUBG_QUALITY_PROGRAM_TYPE                                : 262,  //  方案值
    CM_SETTING_TRAY_MODEL_SWITCH                                : 263,  //  在托盘模式下运行
    CM_SETTING_ADVERTISING_SWITCH                               : 264,  //  不显示广告
    CM_SETTING_UPDATE_MODEL_TYPE                                : 265,  //  更新设置 0：启用自动更新 1：有新的更新时通知我
    CM_SETTING_EXPERIENCE_IMPROVE_SWITCH                        : 266,  //  加入GamePP用户体验改善计划
    CM_BARRGE_COMMENT_URL                                       : 267,  //  直播链接URL
    CM_BARRGE_SHOW_HOTKEY                                       : 268,  //  唤出 锁定热键
    CM_BARRGE_SHOW_HOTKEY_INFO                                  : 269,  //  唤出 锁定热键值
    CM_BARRGE_STATE                                             : 270,  //  当前状态（show 1 /unlock 2 /lock 3 /hide 4）
    CM_BARRGE_BACKGROUND_COLOR                                  : 271,  //  背景颜色值
    CM_BARRGE_BACKGROUND_TRANSPARENT                            : 272,  //  背景透明度
    CM_BARRGE_WINDOW_POS_X                                      : 273,  //  窗体相对显示的位置 x坐标
    CM_BARRGE_WINDOW_POS_Y                                      : 274,  //  窗体相对显示的位置 y坐标
    CM_BARRGE_WINDOW_SIZE_WIDTH                                 : 275,  //  窗体大小 宽度
    CM_BARRGE_WINDOW_SIZE_HEIGHT                                : 276,  //  窗体大小 高度
    CM_BARRGE_OPEN                                              : 277,  //  开启弹幕功能
    CM_POWER_PLAN_TAKE_EFFECT                                   : 278,  //  电源方法是否生效
    CM_VIDEO_SHOW_WATERMARK_SWITCH                              : 279,  //  录像显示水印
    CM_VIDEO_CAPTURE_MODEL_WINDOW                               : 280,  //  录制窗体选择（1：游戏窗体， 2：显示器桌面）
    CM_VIDEO_CAPTURE_SRCEEN_TYPE                                : 281,  //  录制显示器的选择（）
    CM_HWM_GPU_POWER_SWITCH                                     : 282,  //  显卡功耗开关
    CM_USER_LOGIN_CHANGE                                        : 283,  //  当前用户状态(1:普通登录 2: VIP登录，3:退出)
    CM_BARRGE_RE_CONNECT                                        : 284,  //  重新链接直播间
    CM_BARRGE_DISCONNECT                                        : 285,  //  直播间断开
    CM_VR_VIDEO_INVERT_MOUSE_CLICK                              : 286,  //  是否启用鼠标点击效果反转
    CM_CURRENT_GAME_PROCESS_ID                                  : 287,  //  当前游戏进程id
    CM_CPU_CORE_PROGRAM_EFFECT                                  : 288,  //  通知CPU core方案是否生效
    CM_CLIPBOARD_RELEASE_SWITCH                                 : 289,  //  剪切板清除开关
    CM_HWM_REFRESH_UI_EFFECT                                    : 290,  //  界面通知更新硬件信息
    CM_BARRGE_COMMENT_STATE                                     : 291,  //  直播间链接状态
    CM_BARRGE_HOTKYE_TIPS_SWITCH                                : 292,  //  直播间热键提示开关
    CM_HWM_FRAMETIME_SWITCH                                     : 293,  //  帧生成时间开关
    CM_APEX_QUALITY_SWITCH                                      : 294,  //  apex画质开关
    CM_APEX_QUALITY_PROGRAM_TYPE                                : 295,  //  apex方案值
    CM_PERFORMANCE_OPEN_SWITCH                                  : 296,  //  性能分析开关
    CM_PERFORMANCE_NOTIFY_SWITCH                                : 297,  //  性能分析通知开关
    CM_PERFORMANCE_RECORD_TIME                                  : 298,  //  性能分析记录间隔时间
    CM_PERFORMANCE_CLEAR_SWITCH                                 : 299,  //  性能分析清理开关
    CM_PERFORMANCE_CLEAR_TIME                                   : 300,  //  性能分析清理时间段
    CM_PERFORMANCE_PROCESS_QUIT                                 : 301,  //  性能分析有游戏退出
    CM_HTTP_SERVER_PORT                                         : 302,  //  http ip port信息
    CM_HTTP_PHONE_NAME                                          : 303,  //  链接到http server的设备名称
    CM_SHOW_NOTIFY_SWITH                                        : 304,  //  桌面通知显示开关
    CM_SHOW_NOTIFY_UI_SWITH                                     : 305,  //  游戏内通知显示开关
    CM_MEDIA_PATH                                               : 306,  //  媒体路径
    CM_DESK_MODEL                                               : 307,  //  桌面显示类型： 0：壁纸模式， 1：浮窗模式
    CM_DESK_MODEL_WALLPAPER                                     : 308,  //  壁纸模式下的背景图， 为空时表示使用桌面壁纸
    CM_DESK_MODEL_FLOATWINDOW                                   : 309,  //  DESK模型浮动窗口
    CM_DESK_OFF_WHEN_PLAYING                                    : 310,  //  进入游戏时，关闭桌面显示开关
    CM_DESK_FLOATWINDOW_LOCK                                    : 311,  //  浮窗锁定状态
    CM_HWM_IS_HAVE_SCAN                                         : 312,  //  是否扫描过的状态
    CM_ENABLED_DPI                                              : 313,  //  是否适配DPI
    CM_DESK_TYPE                                                : 314,  //  桌面显示格式  1：默认 2：简约 ，3:测评
    CM_TFT_OPEN                                                 : 315,  //  打开TFT
    CM_TFT_ITEM_CHEATSHEET                                      : 316,  //  TFT
    CM_TFT_ROLLING_CHANCE                                       : 317,  //  TFT
    CM_TFT_TEAM_TRACKER                                         : 318,  //  TFT
    CM_TFT_LANGAUGE                                             : 319,  //  TFT
    CM_TFT_TEAM_COMP                                            : 320,  //  TFT
    CM_TFT_HOTKEY_VALUE                                         : 321,  //  TFT
    CM_TFT_HOTKEY_STRIING                                       : 322,  //  TFT
    CM_TFT_HOTKEY_SETTING_VALUE                                 : 323,  //  唤出TFT 设置界面
    CM_TFT_HOTKEY_SETTING_STRIING                               : 324,  //  唤出TFT 设置界面 文本
    CM_TFT_EFFECTIVE                                            : 325,  //  TFT是否生效
    CM_TFT_ACTIVATION                                           : 326,  //  TFT激活
    CM_TFT_SKIN_VERSION                                         : 327,  //  TFT版本
    CM_TFT_SKIN_HASH                                            : 328,  //  TFT hash值
    CM_TERMINATION_SWITCH                                       : 329,  //  快速关闭进程热键（Alt+F4）
    CM_LOGOUT                                                   : 330,  //  注销
    CM_RESTART_PROGRAM                                          : 331,  //  重开软件
    CM_TFT_UPDATE_CHANGE                                        : 332,  //  TFT更新
    CM_USER_GPUPREFERENCES_SWITCH                               : 333,  //  AI游戏进程切换到高性能模式
    CM_VIDEO_DIRECTOR_SWITCH                                    : 334,  //  导演模式开关
    CM_VIDEO_DIRECTOR_CENTER_TAG_SWITCH                         : 335,  //  导演模式 中心标记开关
    CM_VIDEO_DIRECTOR_CENTER_COLOR                              : 336,  //  导演模式 中心标记颜色
    CM_VIDEO_DIRECTOR_CENTER_LINE_WIDTH                         : 337,  //  导演模式 中心标记线宽
    CM_VIDEO_DIRECTOR_CENTER_TRANSPARENCY                       : 338,  //  导演模式 中心标记透明度
    CM_VIDEO_DIRECTOR_CENTER_STYLE                              : 339,  //  导演模式 中心标记样式
    CM_VIDEO_DIRECTOR_BORDER_SWITCH                             : 340,  //  导演模式 四周标记开关
    CM_VIDEO_DIRECTOR_BORDER_TRANSPARENCY                       : 341,  //  导演模式 四周标记透明度
    CM_VIDEO_HIGHLIGHT_PUBG_SWITCH                              : 342,  //  HighLight PUBG 开关
    CM_VIDEO_HIGHLIGHT_TARKOV_SWITCH                            : 343,  //  HighLight Tarkov 开关
    CM_VIDEO_HIGHLIGHT_HOTKEY_SWITCH_VALUE                      : 344,  //  HighLight 热键开关值
    CM_VIDEO_HIGHLIGHT_HOTKEY_SWITCH_STRING                     : 345,  //  HighLight 热键开关文本
    CM_VIDEO_HIGHLIGHT_SWITCH                                   : 346,  //  HighLight 开关
    CM_TFT_PERFORMANCE_SWTICH                                   : 347,  //  TFT关闭对局分析开关
    CM_STRESS_CPU_SWITCH                                        : 348,  //  压力测试 CPU开关
    CM_STRESS_CPU_AVX2_SWITCH                                   : 349,  //  压力测试 CPU AVX 2开关
    CM_STRESS_CPU_LEVEL                                         : 350,  //  压力测试 CPU 压力等级
    CM_STRESS_MEMORY_SWITCH                                     : 351,  //  内存开关
    CM_STRESS_MEMORY_THREAD                                     : 352,  //  内存线程数
    CM_STRESS_MEMORY_SIZE                                       : 353,  //  内存容量
    CM_STRESS_MEMORY_LEVEL                                      : 354,  //  内存压力等级
    CM_STRESS_GPU_SWITCH                                        : 355,  //  GPU开关
    CM_STRESS_GPU_SAWTOOTH                                      : 356,  //  GPU抗锯齿
    CM_STRESS_GPU_DISPLAY                                       : 357,  //  GPU分辨率
    CM_STRESS_GPU_LEVEL                                         : 358,  //  GPU压力等级
    CM_STRESS_GPU_DISPLAY_WIDTH                                 : 359,  //  GPU分辨率 宽
    CM_STRESS_GPU_DISPLAY_HEIGHT                                : 360,  //  GPU分辨率 高
    CM_OPTIMIZE_AUTO_SWITCH                                     : 361,  //  进入游戏时自动优化开关
    CM_OPTIMIZE_HOTKEY_VALUE                                    : 362,  //  取消自动优化 热键值
    CM_OPTIMIZE_HOTKEY_TEXT                                     : 363,  //  取消自动优化 热键文本
    CM_PERFORMANCE_RECORD_HOTKEY_VALUE                          : 364,  //  手动记录 热键值
    CM_PERFORMANCE_RECORD_HOTKEY_TEXT                           : 365,  //  手动记录 热键文本
    CM_NOTE_SWITCH                                              : 366,  //  笔记游戏内显示开关
    CM_NOTE_CURRENT_SHOW_ID                                     : 367,  //  笔记游戏内当前显示ID
    CM_NOTE_REMVOE_ID                                           : 368,  //  删除某个笔记
    CM_NOTE_UPLOAD_SWITCH                                       : 369,  //  笔记上报开关
    CM_NOTE_SAVE_ID                                             : 370,  //  笔记保存通知
    CM_NOTE_SHOW_HOTKEY_VALUE                                   : 371,  //  笔记显示热键值
    CM_NOTE_SHOW_HOTKEY_TEXT                                    : 372,  //  笔记显示热键值文本
    CM_PC_VOICE_SWITCH                                          : 373,  //  主动截图声音开关
    CM_DESK_DEFAULT_POS                                         : 374,  //  桌面显示恢复到默认位置
    CM_OCR_DPI_VALUE                                            : 375,  //  DPI默认值
    CM_MACHENIKE_DESK_MONITOR_NOTIFY_SWITCH                     : 376,  //  桌面监控提示开关
    CM_MACHENIKE_DESK_SKIN_NOTIFY_SWITCH                        : 377,  //  桌面监控皮肤开关
    CM_MACHENIKE_OVERLAY_MONITOR_NOTIFY_SWITCH                  : 378,  //  游戏内监控提示开关
    CM_MACHENIKE_BACKGROUND_RUN_GAMEPP_SWITCH                   : 379,  //  打开游戏加加
    CM_MACHENIKE_WINDOW_START_WITH_GAMEPP_SWITCH                : 380,  //  游戏加加开启启动提示开关
    CM_MACHENIKE_GAMEPP_DESKTOP_LINK_SWITCH                     : 381,  //  游戏加加桌面快捷方式生成提示开关
    CM_MACHENIKE_FIRST_NOTIFY_SWITCH                            : 382,  //  第一次加载时，Ctrl+Tab提示开关
    CM_STRESS_ACTIVATION                                        : 383,  //  压力测试 激活
    CM_MACHENIKE_HIDE_OR_QUIT                                   : 384,  //  隐藏到托盘或直接退出 0: hide 1:quite
    CM_MACHENIKE_Quit_NOTIFY_SWITCH                             : 385,  //  隐藏到托盘或直接退出提示开关
    CM_OCR_SHOW_HOTKEY_VALUE                                    : 386,  //  OCR图像识别热键值
    CM_OCR_SHOW_HOTKEY_TEXT                                     : 387,  //  OCR图像识别热键值文本
    CM_AI_VIEW_SHOW_HOTKEY_VALUE                                : 388,  //  AI画质热键值
    CM_AI_VIEW_SHOW_HOTKEY_TEXT                                 : 389,  //  AI画质热键值文本
    CM_TRAKOV_SHOW_CURRENT_IP_ADDRESS                           : 390,  //  通知显示ip等信息
    CM_TRAKOV_CHANGE_IP_ADDRESS                                 : 391,  //  通知切换ip等信息
    CM_TRAKOV_CHANGE_HOTKEY_VALUE                               : 392,  //  通知切换ip热键值
    CM_TRAKOV_CHANGE_HOTKEY_TEXT                                : 393,  //  通知切换ip热键文本
    CM_TRAKOV_CHANGE_HIDE_HOTKEY_VALUE                          : 394,  //  通知隐藏ip界面热键值
    CM_TRAKOV_CHANGE_HIDE_HOTKEY_TEXT                           : 395,  //  通知隐藏ip界面热键文本
    CM_TRAKOV_NOTIFY_IP_CHANGE_FINISH                           : 396,  //  ip切换完成通知
    CM_TRAKOV_HIDE_CURRENT_IP_ADDRESS                           : 397,  //  通知隐藏ip界面
    CM_TRAKOV_PRICE_SHOW_SWITCH                                 : 398,  //  塔科夫 价格查询 显示开关
    CM_TRAKOV_IP_SHOW_SWITCH                                    : 399,  //  塔科夫 IP 显示开关
    CM_VIDEO_HIGHLIGHT_LOL_SWITCH                               : 400,  //  HighLight LOL 开关
    CM_VIDEO_HIGHLIGHT_VALORANT_SWITCH                          : 401,  //  HighLight valorant 开关
    CM_TRAKOV_ORIGIN_COLOR_INDEX                                : 402,  //  塔科夫 原生画质 选择
    CM_PS_SAVE_RESHADE_VIEW                                     : 403,  //  截图是否保存滤镜效果
    CM_TASKBAR_MONITOR_OPEN                                     : 404,  //  任务栏监控开关
    CM_VIDEO_CAPTURE_WITH_RESHADE_OPEN                          : 405,  //  录制游戏内显示滤镜开关
    CM_VIDEO_CAPTURE_QUERY_OPEN                                 : 406,  //  录制启用开关
    CM_MEMORY_TEMPERATURE_OPEN                                  : 407,  //  内存温度开关
    CM_GAME_AUTO_OR_MAN_RECEVICE                                : 408,  //  游戏自动还是手动领取
    CM_GAME_CLOUD_RECEVICE_OPEN                                 : 409,  //  游戏云端领取开关
    CM_NARAKA_GAMEPATH                                          : 410,  //  NARAKA 游戏路径
    CM_NARAKA_ALL_VOICE_ACTION                                  : 411,  //  全角色语音方案 (经典语音)
    CM_NARAKA_KURUMI_VOICE_ACTION                               : 412,  //  胡桃语音方案
    CM_NARAKA_MATARI_VOICE_ACTION                               : 413,  //  迦南语音方案
    CM_NARAKA_TARKA_VOICE_ACTION                                : 414,  //  季沧海语音方案
    CM_NARAKA_TEMULCH_VOICE_ACTION                              : 415,  //  特木尔语音方案
    CM_NARAKA_TIANHAI_VOICE_ACTION                              : 416,  //  天海语音方案
    CM_NARAKA_VIPER_VOICE_ACTION                                : 417,  //  宁红夜语音方案
    CM_NARAKA_YOTOHIME_VOICE_ACTION                             : 418,  //  妖刀姬语音方案
    CM_NARAKA_VALDA_VOICE_ACTION                                : 419,  //  崔三娘语音方案
    CM_NARAKA_VOICE_PLAN                                        : 420,  //  语言方案选择（经典，守望先锋，王者荣耀方案）
    CM_NARAKA_KURUMI_OVERWATCH_VOICE_ACTION                     : 421,  //  胡桃守望先锋语音方案
    CM_NARAKA_MATARI_OVERWATCH_VOICE_ACTION                     : 422,  //  迦南守望先锋语音方案
    CM_NARAKA_TARKA_OVERWATCH_VOICE_ACTION                      : 423,  //  季沧海守望先锋语音方案
    CM_NARAKA_TEMULCH_OVERWATCH_VOICE_ACTION                    : 424,  //  特木尔守望先锋语音方案
    CM_NARAKA_TIANHAI_OVERWATCH_VOICE_ACTION                    : 425,  //  天海守望先锋语音方案
    CM_NARAKA_VIPER_OVERWATCH_VOICE_ACTION                      : 426,  //  宁红夜守望先锋语音方案
    CM_NARAKA_YOTOHIME_OVERWATCH_VOICE_ACTION                   : 427,  //  妖刀姬守望先锋语音方案
    CM_NARAKA_VALDA_OVERWATCH_VOICE_ACTION                      : 428,  //  崔三娘守望先锋语音方案
    CM_NARAKA_ALL_OVERWATCH_VOICE_ACTION                        : 429,  //  全部英雄守望先锋语音方案
    CM_NARAKA_MATARI_KINGHONOUR_VOICE_ACTION                    : 430,  //  迦南王者荣耀语音方案
    CM_NARAKA_TARKA_KINGHONOUR_VOICE_ACTION                     : 431,  //  季沧海王者荣耀语音方案
    CM_NARAKA_TEMULCH_KINGHONOUR_VOICE_ACTION                   : 432,  //  特木尔王者荣耀语音方案
    CM_NARAKA_TIANHAI_KINGHONOUR_VOICE_ACTION                   : 433,  //  天海王者荣耀语音方案
    CM_NARAKA_VIPER_KINGHONOUR_VOICE_ACTION                     : 434,  //  宁红夜王者荣耀语音方案
    CM_NARAKA_YOTOHIME_KINGHONOUR_VOICE_ACTION                  : 435,  //  妖刀姬王者荣耀语音方案
    CM_NARAKA_VALDA_KINGHONOUR_VOICE_ACTION                     : 436,  //  崔三娘王者荣耀语音方案
    CM_NARAKA_KURUMI_KINGHONOUR_VOICE_ACTION                    : 437,  //  胡桃王者荣耀语音方案
    CM_NARAKA_ALL_KINGHONOUR_VOICE_ACTION                       : 438,  //  全部英雄王者荣耀语音方案
    CM_HWM_CPU_EFFICIENT_CORE_SWITCH                            : 439,  //  CPU节能核心占用
    CM_HWM_GPU_TOTAL_SWITCH                                     : 440,  //  GPU TOTAL 占用率显示
    CM_DESPTOP_MONITOR_ONOFF                                    : 441,  //  监控背景
    CM_DESPTOP_LOW_DISPLAY_ONOFF                                : 442,  //  low 显示
    CM_DESPTOP_MEMUSE_DISPLAY_ONOFF                             : 443,  //  内存使用量显示
    CM_DESPTOP_VOLTAGE_DISPLAY_ONOFF                            : 444,  //  电压显示
    CM_DESPTOP_MUTLECORE_DISPLAY_ONOFF                          : 445,  //  多核数据显示
    CM_DESPTOP_MONITOR_FAMILY_WINDOW                            : 446,  //  桌面监控是否有父窗口
    CM_NARAKA_YUESHAN_VOICE_ACTION                              : 447,  //  岳山经典语音方案
    CM_NARAKA_YUESHAN_OVERWATCH_VOICE_ACTION                    : 448,  //  岳山守望先锋语音方案
    CM_NARAKA_YUESHAN_KINGHONOUR_VOICE_ACTION                   : 449,  //  岳山王者荣耀语音方案
    CM_DATA_REFRESH_TIME_SETTING                                : 450,  //  数据刷新时间
    CM_FPS_ONOFF_HOTKEY                                         : 451,  //  启用关闭热键
    CM_FPS_FRAME_CAPTURE                                        : 452,  //  画面捕获模式
    CM_FPS_FPS_LIMIT                                            : 453,  //  帧数限制
    CM_FPS_RUNNING_DISPLAYCARD                                  : 454,  //  运行游戏使用的显卡
    CM_FPS_FRAME_DELAY                                          : 455,  //  提高画面延迟提升性能
    CM_FPS_WINDOWS_SCREEN_STOP                                  : 456,  //  禁用窗口大小设置
    CM_FPS_WINDOWS_UWP_HIDEN                                    : 457,  //  隐藏UWP窗口
    CM_FPS_CURSOR_HIDEN                                         : 458,  //  隐藏光标
    CM_FPS_CURSOR_SPEED                                         : 459,  //  缩放时调整光标速度
    CM_FPS_CURSOR_SANDGAME_LIMIT                                : 460,  //  在3D游戏中限制光标
    CM_FPS_CURSOR_SIZE                                          : 461,  //  光标大小
    CM_FPS_DIFF_ALGO                                            : 462,  //  差值算法
    CM_FPS_DIRECFILP_STOP                                       : 463,  //  禁用DirecFilp
    CM_FPS_SIMU_MONOPOLY_FULLSCR                                : 464,  //  模拟独占全屏
    CM_FPS_DX_COM_MODE                                          : 465,  //  0 FSR; 1 FSRDX10 显卡兼容模式
    CM_FRE_REDUCE_RANGE                                         : 466,  //  频率降低幅度
    CM_FRE_REDUCE_PRECENT                                       : 467,  //  百分比
    CM_MAN_RECORD_ONOFF_HOTKEY                                  : 468,  //  手动记录性能统计 启用关闭热键
    CM_NARAKA_JUSTINAGU_VOICE_ACTION                            : 469,  //  顾清寒经典语音方案
    CM_NARAKA_JUSTINAGU_OVERWATCH_VOICE_ACTION                  : 470,  //  顾清寒守望先锋语音方案
    CM_NARAKA_JUSTINAGU_KINGHONOUR_VOICE_ACTION                 : 471,  //  顾清寒王者荣耀语音方案
    CM_NARAKA_WUCHEN_VOICE_ACTION                               : 472,  //  无尘经典语音方案
    CM_NARAKA_WUCHEN_OVERWATCH_VOICE_ACTION                     : 473,  //  无尘守望先锋语音方案
    CM_NARAKA_WUCHEN_KINGHONOUR_VOICE_ACTION                    : 474,  //  无尘王者荣耀语音方案
    CM_GPPSHOW_CPU_VOLTAGE                                      : 475,  //  CPU电压
    CM_GPPSHOW_GPU_MEMTMP                                       : 476,  //  GPU显存温度
    CM_GPPSHOW_GPU_MEMFRE                                       : 477,  //  GPU显存频率
    CM_GPPSHOW_GPU_COREHOTTMP                                   : 478,  //  GPU核心热点温度
    CM_GPP_SETTING_ERROR_SHUTDOWN_REPORT                        : 479,  //	异常关机报告开关
    CM_LOW_0_DEL_1_PERCENT                                      : 480,  //	Low 0.1%   int 默认 0
    CM_GPUPERFORMANCESETTING_GPU_FAN_LOW_TMP                    : 481,	//	显卡风扇最低温度控制
    CM_GPUPERFORMANCESETTING_GPU_FAN_AUTO                       : 482,	//	显卡风扇自动调整开关
    CM_GPUPERFORMANCESETTING_GPU_FAN_RPM_PLAN                   : 483,	//	显卡风扇转速推荐方案
    CM_GPUPERFORMANCESETTING_GPU_FAN_RPM                        : 484,	//	显卡风扇转速
    CM_GPUPERFORMANCESETTING_GPU_TMP                            : 485,	//	显卡温度->改为  主板Vcore电压
    CM_GPUPERFORMANCESETTING_GPU_MAXIMUM_POWER_CONSUMPTION_LIMIT: 486,	//	最高功耗限制
    CM_GPUPERFORMANCESETTING_GPU_MAXIMUM_TMP_LIMIT              : 487,	//	最高温度限制
    CM_DESK_MONITOR_CPU_FAN                                     : 488,	//	桌面监控CPU转速
    CM_DESK_MONITOR_GPU_FAN                                     : 489,	//	桌面监控GPU转速
    CM_AUDIO_COMPATIBLE_MODE                                    : 490,	//	性能与音频兼容模式
    CM_DESK_MONITOR_AUTO_ADSORPTION                             : 491,	//	游戏监控边缘吸附(支持上下屏幕边缘吸)
    CM_GPPSETTING_GPU_COREFRE                                   : 492,	//	核心频率 int 默认0
    CM_GPPSETTING_GPU_MEMFRE                                    : 493,	//	显存频率 int 默认0
    CM_GPPSETTING_GPU_PERF_SWITCH                               : 494,	//	显卡性能开关  int类型  默认值0
    CM_GPPSETTING_OLEND_SCREEN_BURNIN_PREVENTION_SWITCH         : 495,	//	OLED防烧屏开关 默认值0
    CM_GPPSETTING_OLEND_SCREEN_BURNIN_PREVENTION_SWITCH_MINUTES : 496,	//	OLED防烧屏开关分钟数 默认值5
    CM_GPPSETTING_BATTERY_LEVEL_DISPLAY_SWITCH                  : 497,	//	电池电量开关 默认值0
    CM_GPPSETTING_AMD_ONLY_MOVE_GAME_TO_CCD0                    : 498,  //  AMD专属优化-转移游戏进程至缓存堆叠核心(CCD0)
    CM_GPPSETTING_NPU_CLOCK_VALUE                               : 499,  //  AMD NPU 频率
    CM_GPPSETTING_NPU_USAGE_VALUE                               : 500,  //  AMD NPU 使用率
    CM_HWM_GPU_USAGE_SWITCH_1                                   : 501,  //  显卡1 占用
    CM_HWM_GPU_TEMP_SWITCH_1                                    : 502,  //  显卡1 温度
    CM_HWM_GPU_FREQUENCY_SWITCH_1                               : 503,  //  显卡1 频率
    CM_HWM_GPU_FAN_SPEED_SWITCH_1                               : 504,  //  显卡1 风扇转速
    CM_HWM_VM_USAGE_SWITCH_1                                    : 505,  //  显卡1 显存占用
    CM_HWM_VM_DISPLAY_TYPE_1                                    : 506,  //  显卡1 显存占用的显示方式（百分比/使用/剩余）
    CM_HWM_VM_FREQUENCY_SWITCH_1                                : 507,  //  显卡1 显存频率
    CM_HWM_GPU_POWER_SWITCH_1                                   : 508,  //  显卡1 功耗开关
    CM_GPPSHOW_GPU_MEMTMP_1                                     : 509,  //  显卡1 显存温度
    CM_GPPSHOW_GPU_COREHOTTMP_1                                 : 510,  //  显卡1 核心热点温度
    CM_HWM_GPU_USAGE_SWITCH_2                                   : 511,  //  显卡2 占用
    CM_HWM_GPU_TEMP_SWITCH_2                                    : 512,  //  显卡2 温度
    CM_HWM_GPU_FREQUENCY_SWITCH_2                               : 513,  //  显卡2 频率
    CM_HWM_GPU_FAN_SPEED_SWITCH_2                               : 514,  //  显卡2 风扇转速
    CM_HWM_VM_USAGE_SWITCH_2                                    : 515,  //  显卡2 显存占用
    CM_HWM_VM_DISPLAY_TYPE_2                                    : 516,  //  显卡2 显存占用的显示方式（百分比/使用/剩余）
    CM_HWM_VM_FREQUENCY_SWITCH_2                                : 517,  //  显卡2 显存频率
    CM_HWM_GPU_POWER_SWITCH_2                                   : 518,  //  显卡2 功耗开关
    CM_GPPSHOW_GPU_MEMTMP_2                                     : 519,  //  显卡2 显存温度
    CM_GPPSHOW_GPU_COREHOTTMP_2                                 : 520,  //  显卡2 核心热点温度
    CM_GPPSETTING_WEATHER_LOCATION                              : 521,  //  天气和位置信息
    CM_GPPSETTING_2HAAI_AIMASTER                                : 522,  //  2哈AIAI教练开关
    CM_GPPSETTING_CENTER_MARK_POSITION_SWITCH                   : 523,  //  中心标记位置控制开关 默认0
    CM_GPPSETTING_MAGIC_GLASS_SRV_SWITCH                        : 524,  //  fps++开关 默认0  关闭
    CM_SCALE_ENABLESCALE                                        : 525,  //  getEnableScale 是否允许缩放
    CM_STATISTICS_SAVE_ADDRESS                                  : 526,  //  性能统计的保存路径
}

let DataBaseId = {
    GPP5DatabaseId: null,
    GPPDownDatabaseId: null
}

let keysEventsLog:any = []
let Kill_Time:any = null
let mouseEventsLog:any = [];
let setInterval_chooseSensor_list:any = null, localSensorListX:any = '', chooseSensor_list_xStr:any = '', sensorInfoModifying = false;
let setInterval_starSensor_list:any = null
let timerIds = new Set(); // 存储所有的定时器ID
let sparkMessageListener:any
let sparkMessageListenerId:any
let onHotkeyTriggeredId:any
const bc = new BroadcastChannel('bg_sensor_data');
let FirstTrayRightClick = true;
class Background {

    MonitoringArr:any = [];
    setInterval_data:any = null;
    setInterval_option:any = null;
    setInterval_manual:any = null;//手动记录定时器
    default_color = [255, 255, 255];//dark
    LoadingTime:any = 0;
    setTimeout_VACUUM_GPP5:any = null;
    GlobalBaseJsonInfo:any = null;
    GlobalShutdownStartTime:any = 0;
    setInterval_shutdown:any = 0;
    saveShutdownNumber:any = 30;//保留异常关机多少条数据

    isSaveGPUFan:any = false;
    setInterval_gpu_fan_control:any = null;
    setFanLevelValue:any = 0;


    async AppTrayOnItemClick(content) {
        console.log(content);
        if (!content) {
            return;
        }

        if (content["appId"] === gamepp.webapp.getAppId.sync()) {
            switch (content["id"]) {
                case 1004: {
                    gamepp.webapp.windows.show.sync(WindowName.DESKTOP);
                    gamepp.webapp.windows.focus.sync(WindowName.DESKTOP);
                    break;
                }
                case 1001: {
                    gamepp.exit.sync();
                    await gamepp.lansevice.stop.promise();
                    break;
                }
                default: {
                    console.log("Unknown menu index.");
                    break;
                }
            }
        } else if (content["gameId"]) {
            gamepp.launchExternalApp.sync(content["gameId"]);
        } else if (content["appId"]) {
            gamepp.webapp.windows.launchWebapp.promise(content["appId"], content);
        }

    }

    AppTrayOnTrayClick(value) {
        if (gamepp.webapp.windows.isVisible.sync(WindowName.DESKTOP)) {
            if (gamepp.webapp.windows.isMinimized.sync(WindowName.DESKTOP)) {
                gamepp.webapp.windows.show.sync(WindowName.DESKTOP);
            } else {
                gamepp.webapp.windows.hide.sync(WindowName.DESKTOP);
            }
        } else {
            if (this.GamePP_MainApp_Position !== null) {
                gamepp.webapp.windows.show.sync(WindowName.DESKTOP, true);
                gamepp.webapp.windows.setPosition.sync(WindowName.DESKTOP, this.GamePP_MainApp_Position[0], this.GamePP_MainApp_Position[1]);
                gamepp.webapp.windows.show.sync(WindowName.DESKTOP);
            } else {
                gamepp.webapp.windows.show.sync(WindowName.DESKTOP);
            }
        }
    }

    createTray() {
        console.log("background CreateTray");
        // create tray.
        if (gamepp.tray.createAppTray.sync()) {
            console.log("background CreateTray True ");
            // Listen on the click event of tray icon menu item.
            gamepp.tray.onMenuItemClick.addEventListener((value) => this.AppTrayOnItemClick(value));
            gamepp.tray.onClick.addEventListener((value) => this.AppTrayOnTrayClick(value));
            gamepp.tray.onRightClick.addEventListener((value) => {
                console.log(value)
                gamepp.webapp.windows.show.sync(WindowName.TRAYMENU);
                gamepp.webapp.windows.focus.sync(WindowName.TRAYMENU);
                const zoomWithSystem = gamepp.setting.getInteger.sync(313) // 是否点了跟随系统缩放开关
                let zoomValue = 1;
                if (zoomWithSystem === 1 && FirstTrayRightClick) {
                    zoomValue = gamepp.display.getScaleFromWindowInMonitor.sync();
                    FirstTrayRightClick = false
                }
                let TraymenuBounds = gamepp.webapp.windows.getBounds.sync('traymenu')
                let TaskbarPosition = gamepp.getTaskbarPosition.sync();
                //当前鼠标位置
                let CursorPoint = gamepp.tray.getCursorScreenPoint.sync();
                console.log(CursorPoint)
                let newX = CursorPoint['x'], newY = CursorPoint['y'];
                switch (TaskbarPosition) {
                    case 0:
                        break;
                    case 1:
                        newY -= Math.floor(TraymenuBounds['height']*zoomValue);
                        break;
                    case 2:
                        newY -= Math.floor(TraymenuBounds['height']*zoomValue);
                        break;
                    case 3:
                        newX -= Math.floor(TraymenuBounds['width']*zoomValue);
                        newY -= Math.floor(TraymenuBounds['height']*zoomValue);
                        break;
                }
                console.log(newX,newY)

                gamepp.webapp.windows.setPosition.sync(WindowName.TRAYMENU, newX, newY);
                console.log("Tray menu right clicked.");
            })
            console.log("background CreateTray Leave");
        } else {
            console.log("background CreateTray False");
        }
    }

    CloseGameProcessTipsCount = 0;
    InGameBarrageHotKeyStatus = 1;
    GamePP_MainApp_Position = null;

    /*
     快捷键
     */
    _hotkey_IngameHomeUi = 0;            //游戏内控制面板
    _hotkey_inGameMonitoring = 0;        //游戏内监控显示
    _hotkey_QuicklyCloseGameProcess = 0; //快速关闭游戏进程
    _hotkey_CancelQuicklyCloseGameProcess = 0; //快速关闭游戏进程
    _hotkey_OpenOrCloseInGameQuality = 0;//游戏内画质
    _hotkey_InGameManual = 0 //
    _hotkey_GlobalManualRecordRebound = null;
    GlobalManualRecordReboundCount = 0;
    GlobalManualRecordReboundStartTime = 0;

    /*
     热键事件
     */
    AppEvent_OnHotkeyTrigggered(info)
    {
        if (!gameclient.GAME_ProcessName) return
        if (info.State !== "Keydown") return
        console.log(info);
        switch (info.id) { //6778
            case this._hotkey_InGameManual:
                this.OnInGameManualRecording()
                break;
            case this._hotkey_IngameHomeUi:
                this.OnShowOrCloseIngameMainUi();
                break;
            case this._hotkey_inGameMonitoring:
                this.OnShowOrCloseInGameMonitoring();
                break;
            case this._hotkey_QuicklyCloseGameProcess:
                this.OnQuicklyCloseGameProcessTips();
                break;
            case this._hotkey_CancelQuicklyCloseGameProcess:
                this.OnCancelQuicklyCloseGameProcessTips();
                break;
            case this._hotkey_OpenOrCloseInGameQuality:
                this.OnOpenOrCloseInGameQuality();
                break;
            default:
                return;
        }
    }

    /**
     *全局热键事件
     */
     AppEvent_OnHotkeyGlobalTraggerInfo(info)
     {
        console.log(info)
        switch (info) {
            case this._hotkey_GlobalManualRecordRebound:
                let Rebound_exist  =  gamepp.package.isexists.sync('GameRebound')
                if(!Rebound_exist)return
                this.OnManualRecordReboundProcess()
                break;
            default:
                return;
        }
     }
    // GameMirror
    OnInGameManualRecording()
    {
        let Rebound_exist  =  gamepp.package.isexists.sync('GameRebound')
        if(!Rebound_exist) return
        localStorage.setItem('pointmark',JSON.stringify(1))//打点
        this.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 35, '游戏内标记成功', 3);//成功
        console.warn('游戏内：实验室打点标记');
    }

    /*
     显示隐藏游戏内控制窗口
     */
    OnShowOrCloseIngameMainUi()
    {
        if (gamepp.webapp.windows.isVisible.sync(WindowName.IN_GAME_MAIN)) {
            gamepp.webapp.windows.minimize.sync(WindowName.IN_GAME_MAIN);
        } else {
            gamepp.webapp.windows.show.sync(WindowName.IN_GAME_MAIN);
        }
    }
    /*
     开关游戏内监控
     */
    OnShowOrCloseInGameMonitoring()
    {
        if (gamepp.webapp.windows.isVisible.sync(WindowName.IN_GAME_MONITOR)) {
            gamepp.webapp.windows.close.sync(WindowName.IN_GAME_MONITOR);
            gamepp.setting.setInteger.sync(COMMANDID.CM_ENABLE_HW_MONITING, 0);
            this.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 12, '', 3);
            localStorage.setItem('ingameSwitch', JSON.stringify(false));
        } else {
            gamepp.webapp.windows.show.sync(WindowName.IN_GAME_MONITOR);
            gamepp.setting.setInteger.sync(COMMANDID.CM_ENABLE_HW_MONITING, 1);
            this.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 11, '', 3);
            localStorage.setItem('ingameSwitch', JSON.stringify(true));
        }
    }

    async OnQuicklyCloseGameProcessTips() {
        const TERMINATION_SWITCH = await gamepp.setting.getInteger.promise(COMMANDID.CM_TERMINATION_SWITCH);
        if (TERMINATION_SWITCH) {
            if (this.CloseGameProcessTipsCount === 0) {
                // await gamepp.setting.setBool2.promise('window', WindowName.IN_GAME_Quit_Game_Tips, false);
                // gamepp.webapp.windows.show.sync(WindowName.IN_GAME_Quit_Game_Tips);
                // gamepp.webapp.windows.focus.sync(WindowName.IN_GAME_Quit_Game_Tips);
                // await this.IsReadyShowSendPage('window', WindowName.IN_GAME_Quit_Game_Tips, false);
                // await gamepp.webapp.sendInternalAppEvent.promise(WindowName.IN_GAME_Quit_Game_Tips, this.CloseGameProcessTipsCount);
                this.CloseGameProcessTipsCount++;
            } else if (this.CloseGameProcessTipsCount === 1) {
                // gamepp.webapp.windows.close.sync(WindowName.IN_GAME_Quit_Game_Tips);
                var execPath = CurrentGameInfo['execPath'].split('\\');
                var ProcessName = execPath[execPath.length - 1];
                gamepp.killProcess.sync(ProcessName);
                this.CloseGameProcessTipsCount = 0;
            }
        }
    }

    async OnManualRecordReboundProcess() //手动记录开始
    {
        if (this.GlobalManualRecordReboundCount === 0)
        {
            console.log('%c性能统计实验室记录开始: ', 'color: orange;');
            this.GlobalManualRecordReboundCount++;
            // let startTime:any = Date.parse(new Date()) / 1000;
            let startTime: number = Date.parse(new Date().toISOString()) / 1000;
            this.GlobalManualRecordReboundStartTime = startTime;
            await gamepp.database.insert.promise(DataBaseId.GPP5DatabaseId, "GamePP_BaseInfo", ["starttime", "data_type"], [[startTime, "manual"]]);
            //创建详情数据表
            let DetailedStatus = await gamepp.database.exists.promise(DataBaseId.GPP5DatabaseId, startTime);
            if (!DetailedStatus)
            {
             await gamepp.database.create.promise(DataBaseId.GPP5DatabaseId, "'" + startTime + "'", '("id"INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,"memory"INTEGER,"memorytemperature"INTEGER,"cpuload"TEXT,"cputemperature"TEXT,"cpuclock"TEXT,"cpupower"TEXT,"cpuvoltage"INTEGER,"gpuload"TEXT,"gpuload1"TEXT,"gputemperature"TEXT,"gpuclock"TEXT,"gpupower"TEXT,"gpumemoryload"INTEGER,"gpuvoltage"INTEGER,"currenttime"INTEGER,"performance"INTEGER)');
            }
            this.GPP_InstallManualData(startTime);
            if (gamepp.webapp.windows.isVisible.sync(WindowName.DESKTOP))
            {
                //发送消息 打开游戏内计时面板
                let Obj = {};
                Obj['action'] = 'manual_recording_tips';
                Obj['msg'] = '开始手动记录,再次按下停止记录';
                await gamepp.webapp.sendInternalAppEvent.promise(WindowName.DESKTOP, Obj)
            }

            let Mode = gamepp.isDesktopMode.sync();
            if (Mode)
            {
              await gamepp.webapp.windows.show.promise('desktop_lab');
            }
            else
            {
              await gamepp.webapp.windows.show.promise('laboratory_tips_ingame');
            }
            window.localStorage.setItem('laboratoryStatus', JSON.stringify(1));
        }
        else if (this.GlobalManualRecordReboundCount === 1)
        {
            // let endTime:any = Date.parse(new Date()) / 1000;
            let endTime:any = Date.parse(new Date().toISOString()) / 1000;
            let GameTime = endTime - this.GlobalManualRecordReboundStartTime;
            if (GameTime <= 10)

            {
                let Obj = {};
                Obj['action'] = 'manual_recording_tips';
                Obj['msg'] = '记录间隔时间太短,请至少记录10秒!';
                await gamepp.webapp.sendInternalAppEvent.promise(WindowName.DESKTOP, Obj)
                return false
            }

            await this.GPP_EndManualData(endTime, GameTime); //结束手动记录 保存

            let is_show = await gamepp.webapp.windows.isVisible.promise('rebound_details_v2');
            if (is_show)
            {
                await gamepp.webapp.windows.close.promise('rebound_details_v2');
            }

            let SendObject = {};
            SendObject['table'] = Number(this.GlobalManualRecordReboundStartTime);
            SendObject['is_upload'] = true;
            console.log(SendObject)
            //
            let Rebound_exist  = await gamepp.package.isexists.promise('GameRebound')
            if(Rebound_exist)
            {
                await gamepp.setting.setBool2.promise('window', 'rebound_details_v2', false);
                gamepp.webapp.windows.show.sync('rebound_details_v2', false);
                let Bsend = 0
                await this.IsReadyShowSendPage('window', "rebound_details_v2", false);
                while(Bsend === 0)
                {
                    gamepp.webapp.windows.isValid.sync('rebound_details_v2')
                    {
                        await gamepp.webapp.sendInternalAppEvent.promise("rebound_details_v2", SendObject);
                        Bsend = 1
                        break;
                    }
                }
            }
            //刷新主程序退弹历史列表
            if (gamepp.webapp.windows.isVisible.sync(WindowName.DESKTOP)) {
                let Obj = {};
                Obj['action'] = 'manual_recording_tips';
                Obj['msg'] = '结束手动记录';
                await gamepp.webapp.sendInternalAppEvent.promise(WindowName.DESKTOP, Obj);

                let Object1 = {};
                Object1['action'] = 'RefreshLaboratoryList';
                Object1['table'] = this.GlobalManualRecordReboundStartTime;
                await gamepp.webapp.sendInternalAppEvent.promise(WindowName.DESKTOP, Object1);
            }


            await gamepp.webapp.windows.close.promise('desktop_lab');
            await gamepp.webapp.windows.close.promise('laboratory_tips_ingame');
            window.localStorage.setItem('laboratoryStatus', JSON.stringify(0));
        }
    }
    //保存手动记录性能数据
    async GPP_InstallManualData (startTime) {

        this.setInterval_manual = setInterval(async () => {

            let SensorDataStr = window.localStorage.getItem('bg_sensor_data');
            let SensorData = SensorDataStr ? JSON.parse(SensorDataStr) : null;
            if (SensorData) {
                let HWInfo = {
                    "memory"           : SensorData['memory']['usage'],
                    "memorytemperature": SensorData['memory']['temp'],
                    "cpuload"          : SensorData['cpu']['usage'],
                    "cputemperature"   : SensorData['cpu']['temp'],
                    "cpuclock"         : SensorData['cpu']['clock'],
                    "cpupower"         : SensorData['cpu']['power'],
                    "cpuvoltage"       : SensorData['cpu']['voltage'],
                    "gpuload"          : SensorData['gpu']['d3d_usage']+ '|',
                    "gpuload1"         : SensorData['gpu']['total_usage']+ '|',
                    "gputemperature"   : SensorData['gpu']['temp'] + '|',
                    "gpuclock"         : SensorData['gpu']['clock'] + '|',
                    "gpupower"         : SensorData['gpu']['power'] + '|',
                    "gpumemoryload"    : SensorData['gpu']['mem_usage'] + '|',
                    "gpuvoltage"       : SensorData['gpu']['voltage'] + '|',
                    "currenttime"      : Date.parse(new Date().toISOString()) / 1000,
                }

                // PERFORMANCE LIMIT REASONS
                let performance:any = [];
                let PlrArr = SensorData['cpu']['limit'];
                PlrArr.forEach((itemDetail) => {
                    let infoKey = Object.keys(itemDetail)[0];
                    let infoValue = itemDetail[infoKey];
                    if (infoValue !== 0) {
                        let performanceObj = {};
                        performanceObj[infoKey] = infoValue;
                        performance.push(performanceObj);
                    }
                })
                let performanceStr = performance.length !== 0 ? JSON.stringify(performance) : '';

                let Data_Field = ["memory", "memorytemperature", "cpuload", "cputemperature", "cpuclock", "cpupower", "cpuvoltage", "gpuload","gpuload1", "gputemperature", "gpuclock", "gpupower", "gpumemoryload", "gpuvoltage", "currenttime", "performance"];
                let Data_Content = [[HWInfo['memory'], HWInfo['memorytemperature'], HWInfo['cpuload'], HWInfo['cputemperature'], HWInfo['cpuclock'], HWInfo['cpupower'], HWInfo['cpuvoltage'], HWInfo['gpuload'],HWInfo['gpuload1'], HWInfo['gputemperature'], HWInfo['gpuclock'], HWInfo['gpupower'], HWInfo['gpumemoryload'], HWInfo['gpuvoltage'], HWInfo['currenttime'], performanceStr]]
                await gamepp.database.insert.promise(DataBaseId.GPP5DatabaseId, "'" + startTime + "'", Data_Field, Data_Content);
            }
        }, 1000)
    }

    //保存手动记录性能数据结束
    async GPP_EndManualData(endTime, GameTime) {
        let BaseJsonInfo = await gamepp.hardware.getBaseJsonInfo.promise();
        let BaseJsonInfoJson = JSON.parse(BaseJsonInfo);
        delete BaseJsonInfoJson["SOUND"];
        delete BaseJsonInfoJson["NETWORK"];
        let HD_Info = encodeURIComponent(JSON.stringify(BaseJsonInfoJson));

        await gamepp.database.update.promise(DataBaseId.GPP5DatabaseId, "GamePP_BaseInfo", ['endtime="' + endTime + '"', 'gametime="' + GameTime + '"', 'hd_info="' + HD_Info + '"'], 'starttime = "' + this.GlobalManualRecordReboundStartTime + '"');

        let WeatherInfo
        try
        {
            WeatherInfo = await gameclient.GetTianqiApiInfo();
            console.warn('WeatherInfo',WeatherInfo);
        }
        catch
        {
            let response_obj_new = {};
            response_obj_new['type'] = 'tencent';
            response_obj_new['city'] = '';
            response_obj_new['province'] = '';
            response_obj_new['cityEn'] = '';
            response_obj_new['wea'] = '';
            response_obj_new['wea_img'] = '';
            response_obj_new['tem'] = '';
            WeatherInfo = response_obj_new
        }
        try
        {
            if (WeatherInfo)
            {
                await gamepp.database.update.promise(DataBaseId.GPP5DatabaseId, "GamePP_BaseInfo", ['city="' + WeatherInfo['city'] + '"', 'province="' + WeatherInfo['province'] + '"', 'wea="' + WeatherInfo['wea'] + '"', 'wea_img="' + WeatherInfo['wea_img'] + '"', 'tem="' + WeatherInfo['tem'] + '"'], 'starttime = "' + this.GlobalManualRecordReboundStartTime + '"');
            }
        }
        catch{

        }
        /**
         * 显示退弹窗口
         */
        this.GlobalManualRecordReboundCount = 0;
        clearInterval(this.setInterval_manual)
    }

    async OnCancelQuicklyCloseGameProcessTips()
    {
        if (gamepp.webapp.windows.isVisible.sync(WindowName.IN_GAME_Quit_Game_Tips)) {
            await gamepp.webapp.sendInternalAppEvent.promise(WindowName.IN_GAME_Quit_Game_Tips, -1);
        }
    }

    //用于创建窗口后需要发送数据到新窗口 判断窗口是否初始化完成
    async IsReadyShowSendPage(sectionName, keyName, value = false)
    {
        return new Promise((resolve, reject) => {
            let nRet = false;
            let setInterval_getbool2 = setInterval(async () => {
                nRet = await gamepp.setting.getBool2.promise(sectionName, keyName, value);
                if (nRet) {
                    clearInterval(setInterval_getbool2);
                    resolve(1);
                }
            }, 100)
        })
    }

    //快捷键开关游戏滤镜
    async OnOpenOrCloseInGameQuality()
    {
        let GameMirror  =  gamepp.package.isexists.sync('GameMirror')
        if(!GameMirror) return
        await gamepp.utils.sendstatics.promise(Number(100628));
        const IsOpen = await gamepp.setting.getInteger.promise(COMMANDID.CM_ENABLE_EFFECT);
        console.log("IsOpen::",IsOpen);
        if (IsOpen === 1)
        {
            await gamepp.game.ingame.disableShader.promise();
            localStorage.setItem('mirrorSwitch', 'false');
            await gamepp.utils.sendstatics.promise(Number(100601));
        }
        else if (IsOpen === 0)
        {
            await gamepp.utils.sendstatics.promise(Number(100602));
            await gamepp.game.ingame.enableShader.promise();
            localStorage.setItem('mirrorSwitch', 'true');
            //当前画质方案
            let CurrentQuality = await gamepp.setting.getString.promise(COMMANDID.CM_RESHADE_TYPE);
            await gamepp.game.ingame.setShaderPreset.promise(CurrentQuality);
        }
        await gamepp.setting.setInteger.promise(COMMANDID.CM_ENABLE_EFFECT, Math.pow(0, IsOpen));//同时设置配置
    }


    async CheckHotkeyConflict()
    {
        window.localStorage.removeItem('conflict_hotkey');
        let KeyInfo =
        [
            {"def": "Ctrl+TAB",  "id": 9,  "text": "打开/关闭游戏内设置面板"},
            {"def": "Ctrl+F10",  "id": 15, "text": "开启/关闭游戏内数据显示" },
            {"def": "Ctrl+F5" ,  "id": 13, "text": "开启/关闭游戏滤镜"},
            {"def": "F9", "id": 60, "text": "截图热键"},
        ]
        let ConflictHotKeyArr = {};
        for (let i = 0; i < KeyInfo.length; i++)
        {
            let KeyDef = KeyInfo[i]['def'], KeyId = KeyInfo[i]['id'], KeyText = KeyInfo[i]['text'];
            let KeyValSet = await gamepp.setting.getString.promise(KeyId);
            if (!['None', 'None_cn', '无'].includes(KeyValSet)) //如果不为空 进入
            {
                let state = await gamepp.utils.registerGlobalHotKey.promise(KeyValSet);
                if (!state)
                {
                    //冲突
                    let obj = {};
                    obj['id'] = KeyId;
                    obj['default'] = KeyDef;
                    obj['text'] = KeyText;
                    ConflictHotKeyArr[KeyId] = obj;
                }
                await gamepp.utils.unregisterGlobalHotKey.promise(KeyValSet);
            }
        }

        let length = (Object.keys(ConflictHotKeyArr)).length;
        if (length === 0)
        {
            console.log('无冲突热键')
            window.localStorage.removeItem('conflict_hotkey');
        }
        else
        {
            console.log('冲突热键:',ConflictHotKeyArr)
            window.localStorage.setItem('conflict_hotkey', JSON.stringify(ConflictHotKeyArr));
        }
    }

    async registerIngameHotkey()
    {
        const _hotkey_ingameMainUiShotkey = await gamepp.setting.getString.promise(COMMANDID.CM_HOTKEY_SHOW_MAINUI_INFO);

        const _hotkey_inGameMonitoringKey = await gamepp.setting.getString.promise(COMMANDID.CM_HOTKEY_HARDWARE_MONITING_SWITCH_INFO);

        const _hotkey_OpenOrCloseInGameQualityKey = await gamepp.setting.getString.promise(COMMANDID.CM_HOTKEY_PRESENT_EFFECT_SWITCH_INFO);

        const _hotkey_inGamePointMark = await gamepp.setting.getString.promise(527);

        {
            this._hotkey_InGameManual = await gamepp.game.ingame.registerHotkey.promise(_hotkey_inGamePointMark);

            console.log("性能统计打点热键: ", 'Shift+F11', "Id: ", this._hotkey_InGameManual);

            this._hotkey_IngameHomeUi = await gamepp.game.ingame.registerHotkey.promise(_hotkey_ingameMainUiShotkey);//'Ctrl+Tab'
            console.log("游戏内控制面板热键: ", _hotkey_ingameMainUiShotkey, "Id: ", this._hotkey_IngameHomeUi);

            this._hotkey_inGameMonitoring = await gamepp.game.ingame.registerHotkey.promise(_hotkey_inGameMonitoringKey);
            console.log("游戏内监控开关热键: ", _hotkey_inGameMonitoringKey, "Id: ", this._hotkey_inGameMonitoring);

            this._hotkey_QuicklyCloseGameProcess = await gamepp.game.ingame.registerHotkey.promise('Alt+F4');
            console.log("快速关闭游戏进程热键: ", 'Alt+F4', "Id: ", this._hotkey_QuicklyCloseGameProcess);

            this._hotkey_CancelQuicklyCloseGameProcess = await gamepp.game.ingame.registerHotkey.promise('Escape');
            console.log("取消快速关闭游戏进程热键: ", 'Escape', "Id: ", this._hotkey_CancelQuicklyCloseGameProcess);

            this._hotkey_OpenOrCloseInGameQuality = await gamepp.game.ingame.registerHotkey.promise(_hotkey_OpenOrCloseInGameQualityKey);
            console.log("开关游戏内滤镜热键: ", _hotkey_OpenOrCloseInGameQualityKey, "Id: ", this._hotkey_OpenOrCloseInGameQuality);

            const _hotkey_manualRecordRebound = await gamepp.setting.getString.promise(468);

            this._hotkey_GlobalManualRecordRebound = _hotkey_manualRecordRebound;
            console.log("实验室热键: ", 'Shift+F10', "Id: ", this._hotkey_GlobalManualRecordRebound);

            if (!['无', 'None', 'None_cn'].includes(_hotkey_manualRecordRebound))
            {
                let manualRecordRebound = gamepp.utils.registerGlobalHotKey.sync(_hotkey_manualRecordRebound);
                console.log("试验室手动记录硬件数据: ", _hotkey_manualRecordRebound, manualRecordRebound);
            }
        }

        if (onHotkeyTriggeredId)
        {
            gamepp.game.ingame.onHotkeyTriggered.removeEventListener(onHotkeyTriggeredId)
            onHotkeyTriggeredId = false
        }

        onHotkeyTriggeredId = gamepp.game.ingame.onHotkeyTriggered.addEventListener((value) => this.AppEvent_OnHotkeyTrigggered(value));//注册游戏内热键 游戏内使用

        gamepp.utils.globalHotKeyTraggerInfo.addEventListener((value) => this.AppEvent_OnHotkeyGlobalTraggerInfo(value));//注册全局热键   游戏外使用
    }
    //喜加一自动领取 程序起来之后过10分钟执行 与HwInfo占用避开
    AutoRecevice()
    {
       let AutoFreeGame = JSON.parse(localStorage.getItem('autoReceive') as any)
       let autoseconds = JSON.parse(localStorage.getItem('autoSeconds') as any)
       if(!autoseconds)
       {
        autoseconds  = 60*60*3
       }
       if(AutoFreeGame)
       {
                if(!gamepp.webapp.windows.isValid.sync('FreeGame'))
                {
                    autoseconds--

                    if(autoseconds == 0)
                    {
                        localStorage.setItem('needclose',JSON.stringify(1))
                        gamepp.webapp.windows.show.sync('FreeGame',true) //执行完毕就关窗口
                        autoseconds = 60*60*3
                    }

                    localStorage.setItem('autoSeconds',JSON.stringify(autoseconds))
                }
       }
    }

    async AppEvent_OnInternalEvent(e)
    {
        console.warn('Backround receive message:',e);
        if (e['action'] === 'CancelExitGame')
        {
            this.CloseGameProcessTipsCount = 0;
        }
        else if (e['action'] === 'autoFree')
        {
            gamepp.webapp.windows.close.sync('FreeGame') //执行完毕就关窗口
        }
        else if (e['action'] === 'ImmediateExitGame')
        {
            var execPath = CurrentGameInfo['execPath'].split('\\');
            var ProcessName = execPath[execPath.length - 1];
            gamepp.killProcess.sync(ProcessName);
            this.CloseGameProcessTipsCount = 0;
        }
        else if (e['action'] === 'UpdateHotKey')
        {
            console.warn('更新热键');
            console.log(e);
            this.UpdateHotKeyRegistration();
        }
        else if (e['action'] === 'refreshUserToken')
        {
            console.log('refreshUserToken')
            for (let id of timerIds) {
                clearTimeout(id as string | number);
            }
            timerIds.clear(); // 清空Set
            for (let i = 0; i < 5; i++) {
                const delay = i === 0 ? 0 : 5000; // 第一次立即执行，后面4次间隔5秒一次
                if (i === 0) {
                    console.log('Loop iteration:', i + 1);
                } else {
                    await new Promise(resolve => {
                        let timerId = setTimeout(async () => {
                            console.log('Loop iteration:', i + 1);
                            await gamepp.user.refreshUserToken.promise(true);
                            resolve(true);
                        }, delay);
                        timerIds.add(timerId);
                    });
                }
            }
        }
        else if (e['action'] === 'ExitGamePP')
        {
            console.log('background收到APP退出通知');
            await gamepp.webapp.windows.hide.promise('desktop');
            //关闭异常关机报告
            if (await gamepp.setting.getInteger.promise(479) === 1)
            {
                await gamepp.webapp.sendInternalAppEvent.promise('background', {
                    "action": "sendShutdownReportState",
                    "value": 0
                });
                await gamepp.setting.setInteger.promise(479, 0);
            }
            //开启日志功能退出时清空日志目录
            let LogState = await gamepp.setting.getGamePPLogState.promise();
            if (LogState) await gamepp.setting.emptyGamePPLog.promise();

            if (gamepp.setting.getInteger.sync(311) === 0)
            {
                //锁定桌面监控
                await gamepp.setting.setInteger.promise(COMMANDID.CM_DESK_FLOATWINDOW_LOCK, 1);
                let PresentBounds = await gamepp.webapp.windows.getBounds.promise(WindowName.Desktop_Monitor); //保存监控位置
                await gamepp.setting.setInteger.promise(COMMANDID.CM_DESK_LOCATION_X, PresentBounds['x']);
                await gamepp.setting.setInteger.promise(COMMANDID.CM_DESK_LOCATION_Y, PresentBounds['y']);
                let handle = await gamepp.dialog.showdeskMonitor.promise();
                await gamepp.displayDestopWindow.promise(handle);
            }
            setTimeout(async () =>
            {
                await gamepp.lansevice.stop.promise()
                await gamepp.exit.promise();
            }, 250)
        }
        else if (e.action === 'reloadXMLInfo')
        {
            await gamepp.update.checkHWInfo.promise()
            //加载XML解析文件
            let result = await this.getScript('js/HWInfoDataAnalyzer/HwInfoXMLProcess.js')
            console.warn('load HwInfoXMLProcess result',result);
            let resultI = await this.getScript('js/HWInfoDataAnalyzer/HWInfoSensorProcess.js')
            console.warn('load HWInfoSensorProcess result',resultI);
            gamepp.update.updateHWInfo.promise()
            console.warn('gamepp.update.updateHWInfW');
            //XML解析
            if(result && resultI)
            {
                this.ConvertXMLData2JSON('reload')//转换XML数据2 JSON
            }
            else
            {
                console.warn('未进行XML解析');
            }
        }
        else if (e.action === 'moveDataBase')
        {
            await gamepp.database.close.promise(DataBaseId.GPPDownDatabaseId)
            await gamepp.database.close.promise(DataBaseId.GPP5DatabaseId)
            for (const item of ['\\GamePP5.dll', '\\GamePPStressNew.dll', '\\GamePPShutdown.dll']) {
                const r = await gamepp.utils.copyFile.promise(e.oldPath+item, e.newPath+item);
                console.log(r);
                if (r) {
                    gamepp.utils.unlinkFile.promise(e.oldPath+item)
                }
            }
            let AppDataDir = await gamepp.getAppDataDir.promise();
            let GPP5DatabaseDir = gamepp.setting.getString.sync(COMMANDID.CM_STATISTICS_SAVE_ADDRESS);
            if (GPP5DatabaseDir == '') GPP5DatabaseDir = AppDataDir + '\\common\\Data'
            let GPP5DatabaseId = await gamepp.database.open.promise(GPP5DatabaseDir+'\\GamePP5.dll');
            let GPPDownDatabaseId = await gamepp.database.open.promise(GPP5DatabaseDir + '\\GamePPShutdown.dll');
            DataBaseId.GPPDownDatabaseId = GPPDownDatabaseId;
            DataBaseId.GPP5DatabaseId = GPP5DatabaseId;
        }
        else if (e.action === 'ThresholdTriggered')
        {
            video_client.AppEvent_OnInGameThresholdTriggered(e.value)
        }
    }

    GPP_InstallShutdownData(startTime)
    {
        //setTimeout setInterval
        let insertIndex = 1;
        this.setInterval_shutdown = setInterval(async () => {
            let SensorDataStr = window.localStorage.getItem('bg_sensor_data');
            let SensorData = SensorDataStr ? JSON.parse(SensorDataStr) : null;
            if (SensorData) {
            let HWInfo = {
                "memory"    : SensorData['memory']['usage'],
                "memorytemp": SensorData['memory']['temp'],
                "cpuload"   : SensorData['cpu']['usage'],
                "cpuloadP"  : SensorData['cpu']['usageP'],
                "cpuloadE"  : SensorData['cpu']['usageE'],
                "cputemp"   : SensorData['cpu']['temp'],
                "cpuclock"  : SensorData['cpu']['clock'],
                "cpuclockP" : SensorData['cpu']['clockP'],
                "cpuclockE" : SensorData['cpu']['clockE'],
                "cputdp"    : SensorData['cpu']['power'],
                "cpuvoltage": SensorData['cpu']['voltage'],
                "cpufan"    : SensorData['cpu']['fan'],

                "gpuload"       : SensorData['gpu']['total_usage'],
                "gputemp"       : SensorData['gpu']['temp'],
                "gpuhotspottemp": SensorData['gpu']['hot_spot_temp'],
                "gpuclock"      : SensorData['gpu']['clock'],
                "gputdp"        : SensorData['gpu']['power'],
                "gpufan"        : SensorData['gpu']['fan'],
                "gpuvoltage"    : SensorData['gpu']['voltage'],

                "gpumemoryload" : SensorData['gpu']['mem_usage'],
                "gpumemoryclock": SensorData['gpu']['mem_clock'],
                "gpumemorytemp" : SensorData['gpu']['mem_temp'],

                "drivetemp": SensorData['drive']['temp'],
                "driveload": SensorData['drive']['usage'],

                "currenttime": Date.parse(new Date().toISOString()) / 1000,
            }

            //console.log(HWInfo)
            if (insertIndex > (this.saveShutdownNumber)) {
                let deleteId = Number(insertIndex - (this.saveShutdownNumber));
                await gamepp.database.delete.promise(DataBaseId.GPPDownDatabaseId, "'" + startTime + "'", "id = " + deleteId + "");
            }
            let Data_Field = ["memory", "memorytemp", "cpuload", "cpuloadP", "cpuloadE", "cputemp", "cpuclock", "cpuclockP", "cpuclockE", "cputdp", "cpuvoltage", "cpufan", "gpuload", "gputemp", "gpuhotspottemp", "gpuclock", "gputdp", "gpufan", "gpuvoltage", "gpumemoryload", "gpumemoryclock", "gpumemorytemp", "driveload", "drivetemp", "currenttime"];
            let Data_Content = [[HWInfo['memory'], HWInfo['memorytemp'], HWInfo['cpuload'], HWInfo['cpuloadP'], HWInfo['cpuloadE'], HWInfo['cputemp'], HWInfo['cpuclock'], HWInfo['cpuclockP'], HWInfo['cpuclockE'], HWInfo['cputdp'], HWInfo['cpuvoltage'], HWInfo['cpufan'], HWInfo['gpuload'], HWInfo['gputemp'], HWInfo['gpuhotspottemp'], HWInfo['gpuclock'], HWInfo['gputdp'], HWInfo['gpufan'], HWInfo['gpuvoltage'], HWInfo['gpumemoryload'], HWInfo['gpumemoryclock'], HWInfo['gpumemorytemp'], HWInfo['driveload'], HWInfo['drivetemp'], HWInfo['currenttime']]];
            await gamepp.database.insert.promise(DataBaseId.GPPDownDatabaseId, "'" + startTime + "'", Data_Field, Data_Content);
            insertIndex++
           }
        }, 1000)
    }

    ColorProcessDesc(num)
    {
        let DescColor:any = [];
        if (num <= 40) {
            DescColor = [96, 229, 77] //#60e54d
        } else if (num <= 80) {
            DescColor = [227, 197, 33] //#e3c521
        } else {
            DescColor = [224, 78, 58] //#e04e3a
        }
        return DescColor;
    }

    BytesToSize(bytes)
    {
        if (bytes === 0) return '0 KB';
        let k = 1024, sizes = ['KB', 'MB'];
        let i = Math.floor(Math.log(bytes) / Math.log(k));
        // return (bytes / Math.pow(k, i)).toPrecision(2) + sizes[i];
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + sizes[i];
    }


    /*
     更新热键注册
     */
    async UpdateHotKeyRegistration()
    {
        console.log('UpdateHotKeyRegistration');
        let unregisterHotkeyArr = [
            this._hotkey_InGameManual,
            this._hotkey_IngameHomeUi,
            this._hotkey_inGameMonitoring,
            this._hotkey_QuicklyCloseGameProcess,
            this._hotkey_OpenOrCloseInGameQuality,
            this._hotkey_CancelQuicklyCloseGameProcess,
            video_client._hotkey_NormalVideoToggle,
            video_client._hotkey_ReplaySave,
            video_client._hotkey_ShotcutTriggered,
            video_client._hotkey_AIViesSHOW,
            gameclient._hotkey_RecordHotkey
        ]

        for (let i = 0; i < unregisterHotkeyArr.length; i++) {
            this.unregisterHotkey(unregisterHotkeyArr[i])
        }
        //全局热键
        if (!['None_cn', 'None', '无'].includes(this._hotkey_GlobalManualRecordRebound as any)) {
            gamepp.utils.unregisterGlobalHotKey.sync(this._hotkey_GlobalManualRecordRebound)
        }
        await this.registerIngameHotkey()
        await video_client.registerMediaHotkey()
    }

    async unregisterHotkey(value) {
        if (!['None_cn', 'None', '无'].includes(value)) {
            await gamepp.game.ingame.unregisterHotkey.promise(value)
        }
    }

    isServiceStarted = false;

    async AppEvent_OnWindowReadyToShow() {
        console.log("App running now!");

        await gamepp.whenMainServicesStarted.promise();

        console.log("检查热键冲突");
        await this.CheckHotkeyConflict();

        console.log("Services started!");
        await this.registerIngameHotkey();

        await this.InitAppEvent();

        console.log("Initialize the system tray.");
        this.createTray();

        console.log("Initialize the ig component.");
        gameclient.Run();

        console.log("Initialize the video recorder.");

        video_client.Run();

        this.isServiceStarted = true;
        console.log("All things good. initialisation done.");


    }

    registerSecondInstanceNotifier()
    {
        gamepp.onSecondIntanceLaunched.addEventListener(argv => {
            console.log(argv);
            if (this.isServiceStarted) {
                gamepp.webapp.windows.show.sync(WindowName.DESKTOP);
                gamepp.webapp.windows.focus.sync(WindowName.DESKTOP);
            } else {
                //如果是第二个实例不应该再启动这个页面
                //gamepp.webapp.windows.show(WindowName.UPDATE);
                //gamepp.webapp.windows.focus(WindowName.UPDATE);
            }
        })
    }

    registerNotifyReceiver()
    {
        this.registerSecondInstanceNotifier();
        gamepp.webapp.onInternalAppEvent.addEventListener((value) => this.AppEvent_OnInternalEvent(value));
        try {
            gamepp.desktopmonitor.onScreenNumsChange.addEventListener((value) => {});//监听显示器数量变化
        } catch {
            console.log('onScreenNumsChange error')
        }
    }
    //转换XML数据为JSON并保存
    async ConvertXMLData2JSON(reload)
    {
        let HWinfoXmlState = 0;
        try {
            HWinfoXmlState = gamepp.hardware.getXMLState.sync();
        } catch {
            HWinfoXmlState = 1
        }
        if (HWinfoXmlState === 1) {
            let HWInfoXmlStr = await gamepp.hardware.getBaseXmlInfo.promise();
            // let HWInfoXmlStr = '';
            if (HWInfoXmlStr !== '' && HWInfoXmlStr !== null && HWInfoXmlStr !== undefined) {
                // let JsonObj = $.xml2json(string2XML(HWInfoXmlStr.replace(/&#?[0-9]+;/g,"")));
                let JsonObj = $.xml2json(string2XML(HWInfoXmlStr));
                console.log(JsonObj);
                window.localStorage.setItem('HWInfoXml',JSON.stringify(JsonObj))
                await HWInfoXMLPageDisplayProcess(JsonObj);//XML解析

                if (reload === 'init') {
                    //获取硬件监控设置参数
                    let HwInfoJsonStr = await gamepp.hardware.getBaseJsonInfo.promise();
                    let HwInfoJson = JSON.parse(HwInfoJsonStr);
                    try {
                        this.saveHardwareInformation(HwInfoJson);
                        //启动时判断开启任务栏监控
                        let taskbar_monitor_status = await gamepp.setting.getInteger.promise(COMMANDID.CM_TASKBAR_MONITOR_OPEN);
                        if (taskbar_monitor_status) {
                            setTimeout(async () => {
                                // await gamepp.deskband.startDeskBand.promise();
                                gamepp.webapp.windows.show.sync('taskbar_hardware')
                            }, 500)
                        }
                    } catch {}
                    this.GlobalBaseJsonInfo = HwInfoJson;
                    setTimeout(() => {
                        let refresh_time = 1000
                        try {
                            refresh_time = gamepp.setting?.getInteger.sync(COMMANDID.CM_DATA_REFRESH_TIME_SETTING)
                        } catch {
                        }
                        this.BgSensorDataProcess(refresh_time)
                        this.StarSensorDataProcess(refresh_time)
                        this.getSensorStatistics()
                    },2000)

                    //硬件信息获取成功开启桌面监控
                    if (gamepp.setting.getInteger.sync(COMMANDID.CM_USE_DESKTOP_FRAME) === 1) {
                        await gamepp.webapp.windows.show.promise(WindowName.Desktop_Monitor);
                    }
                }
            }
        } else {
            setTimeout(function () {
                app.ConvertXMLData2JSON(reload);
            }, 1000)
        }
    }

    SensorAddAverageData (SensorInfo:any, SensorInfoKeys:any)
    {
        let vidSum = 0, vidCount = 0, vidSumP = 0, vidCountP = 0, vidSumE = 0, vidCountE = 0;
        let vidMax = 0, vidMaxP = 0, vidMaxE = 0;

        let clockSum = 0, clockCount = 0, clockSumP = 0, clockCountP = 0, clockSumE = 0, clockCountE = 0;

        let tempSum = 0, tempCount = 0, tempSumP = 0, tempCountP = 0, tempSumE = 0, tempCountE = 0;

        let usageSum = 0, usageCount = 0, usageSumP = 0, usageCountP = 0, usageSumE = 0, usageCountE = 0;

        const vidRegex = /^Core[ \f\r\t\n][0-9]+[ \f\r\t\n]VID$/i;
        const vidRegexP = /^P-Core[ \f\r\t\n][0-9]+[ \f\r\t\n]VID$/i;
        const vidRegexE = /^E-Core[ \f\r\t\n][0-9]+[ \f\r\t\n]VID$/i;

        const clockRegex = /^Core[ \f\r\t\n][0-9]+[ \f\r\t\n]Clock[.\n]*$/i;
        const clockRegexP = /^P-Core[ \f\r\t\n][0-9]+[ \f\r\t\n]Clock[.\n]*$/i;
        const clockRegexE = /^E-Core[ \f\r\t\n][0-9]+[ \f\r\t\n]Clock[.\n]*$/i;

        const tempRegex = /^Core[ \f\r\t\n][0-9]{1,2}$/i;
        const tempRegexP = /^P-Core[ \f\r\t\n][0-9]{1,2}$/i;
        const tempRegexE = /^E-Core[ \f\r\t\n][0-9]{1,2}$/i;

        const usageRegex = /^CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]T[0-9][ \f\r\t\n]USAGE$/i;
        const usagePRegex = /^P-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]T[0-9][ \f\r\t\n]USAGE$/i;
        const usageERegex = /^E-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]T[0-9][ \f\r\t\n]USAGE$/i;

        SensorInfoKeys.forEach(key => {
            if (SensorInfo[key]) {
                SensorInfo[key].forEach(item => {
                    let itemKey = String(Object.keys(item)[0]);
                    const value = parseFloat(item[itemKey].value);
                    const type = item[itemKey]['type'];
                    itemKey = itemKey.toLowerCase();
                    if (type === "voltage" && itemKey.includes('core') && key.includes('CPU')) {
                        vidSum += value;
                        vidMax = Math.max(vidMax, value)
                        vidCount++;
                        if (itemKey.includes('p-core')) {
                            vidSumP += value;
                            vidMaxP = Math.max(vidMaxP, value)
                            vidCountP++;
                        } else if (itemKey.includes('e-core')) {
                            vidSumE += value;
                            vidMaxE = Math.max(vidMaxE, value)
                            vidCountE++;
                        }
                    } else if (type === "clock" && itemKey.includes('core') && itemKey.includes('clock') && key.includes('CPU') && !itemKey.includes('effective')) {
                        clockSum += value;
                        clockCount++;
                        if (itemKey.includes('p-core')) {
                            clockSumP += value;
                            clockCountP++;
                        } else if (itemKey.includes('e-core')) {
                            clockSumE += value;
                            clockCountE++;
                        }
                    } else if (tempRegex.test(itemKey) || tempRegexP.test(itemKey) || tempRegexE.test(itemKey)) {
                        tempSum += value;
                        tempCount++;
                        if (itemKey.includes('p-core')) {
                            tempSumP += value;
                            tempCountP++;
                        } else if (itemKey.includes('e-core')) {
                            tempSumE += value;
                            tempCountE++;
                        }
                    } else if (type === "usage" && itemKey.includes('core') && itemKey.includes('usage') && key.includes('CPU')) {
                        usageSum += value;
                        usageCount++;
                        if (itemKey.includes('p-core')) {
                            usageSumP += value;
                            usageCountP++;
                        } else if (itemKey.includes('e-core')) {
                            usageSumE += value;
                            usageCountE++;
                        }
                    }
                });
            }
        });

        const averageVidObj:any = { "Core Max VID": { "type": "voltage", "value": vidCount !== 0 ? vidMax : 0 } };
        const averageVidPObj = { "P Core Max VID": { "type": "voltage", "value": vidCountP !== 0 ? vidMaxP : 0 } };
        const averageVidEObj = { "E Core Max VID": { "type": "voltage", "value": vidCountE !== 0 ? vidMaxE : 0 } };

        const averageClockObj = { "Core Clocks": { "type": "clock", "value": clockCount !== 0 ? clockSum / clockCount : 0 } };
        const averageClockPObj = { "P Core Clocks": { "type": "clock", "value": clockCountP !== 0 ? clockSumP / clockCountP : 0 } };
        const averageClockEObj = { "E Core Clocks": { "type": "clock", "value": clockCountE !== 0 ? clockSumE / clockCountE : 0 } };

        // const averageTempObj = { "Core Temps": { "type": "temperature", "value": tempCount !== 0 ? tempSum / tempCount : 0 } };
        // const averageTempPObj = { "P Core Temps": { "type": "temperature", "value": tempCountP !== 0 ? tempSumP / tempCountP : 0 } };
        // const averageTempEObj = { "E Core Temps": { "type": "temperature", "value": tempCountE !== 0 ? tempSumE / tempCountE : 0 } };

        const averageUsageObj = { "Core Usages": { "type": "usage", "value": usageCount !== 0 ? usageSum / usageCount : 0 } };

        const averageUsagePObj = { "P Core Usages": { "type": "usage", "value": usageCountP !== 0 ? usageSumP / usageCountP : 0 } };
        const averageUsageEObj = { "E Core Usages": { "type": "usage", "value": usageCountE !== 0 ? usageSumE / usageCountE : 0 } };

        let CoreVidFound = false, CoreClockFound = false, CoreTempFound = false, CoreUsageFound = false;
        let pCoreVidFound = false, pCoreClockFound = false, pCoreTempFound = false, pCoreUsageFound = false;
        let eCoreVidFound = false, eCoreClockFound = false, eCoreTempFound = false, eCoreUsageFound = false;

        SensorInfoKeys.forEach(sensorKey => {
            let resultData:any = [];
            if (SensorInfo[sensorKey]) {
                SensorInfo[sensorKey].forEach(sensorItem => {
                    const sensorItemKey = Object.keys(sensorItem)[0];
                    const sensorItemKeyUpper = sensorItemKey.toUpperCase()
                    const type = sensorItem[sensorItemKey]['type'];
                    if (sensorKey.includes('CPU'))
                    {
                        if (vidCount !== 0 && !CoreVidFound) {
                            resultData.push(averageVidObj);
                            CoreVidFound = true
                        }
                        if (vidCountP !== 0 && !pCoreVidFound) {
                            resultData.push(averageVidPObj);
                            pCoreVidFound = true
                        }
                        if (vidCountE !== 0 && !eCoreVidFound) {
                            resultData.push(averageVidEObj);
                            eCoreVidFound = true
                        }
                        if (clockCount !== 0 && !CoreClockFound) {
                            resultData.push(averageClockObj);
                            CoreClockFound = true
                        }
                        if (clockCountP !== 0 && !pCoreClockFound) {
                            resultData.push(averageClockPObj);
                            pCoreClockFound = true
                        }
                        if (clockCountE !== 0 && !eCoreClockFound) {
                            resultData.push(averageClockEObj);
                            eCoreClockFound = true
                        }
                        if (usageCount !== 0 && !CoreUsageFound) {
                            resultData.push(averageUsageObj);
                            CoreUsageFound = true
                        }
                        if (usageCountP !== 0 && !pCoreUsageFound) {
                            resultData.push(averageUsagePObj);
                            pCoreUsageFound = true
                        }
                        if (usageCountE !== 0 && !eCoreUsageFound) {
                            resultData.push(averageUsageEObj);
                            eCoreUsageFound = true
                        }
                    }
                    resultData.push(sensorItem);
                });
                SensorInfo[sensorKey] = resultData;
            }

        });
        return SensorInfo;
    }
    //每秒获取传感器数据存bg,便于页面使用  setInterval setTimeout
    BgSensorDataProcess (refresh_time) {
        chooseSensor_list_xStr = window.localStorage.getItem('chooseSensor_list_v1')
        console.log('数据刷新时间: ' + refresh_time / 1000 + '秒')
        let cpuType = 'intel'
        let GPU0Data:any = null, GPU1Data:any = null, GPU2Data:any = null, isGPUData:boolean = false;
        let gpuName0:any = null, gpuName1:any = null, gpuName2:any = null;
        let MemoryData:any = null;
        let containsGPU:boolean = true;
        let mainDisk:any = { ProductId: "", SerialNumber: "", VendorId: "" };
        try {
            mainDisk = gamepp?.hardware?.getMainDiskInfo.sync() || mainDisk;
        } catch {
        }
        // setTimeout,setInterval
        setInterval_chooseSensor_list = setInterval(() => {
            let SensorInfoStr = null
            try {
                SensorInfoStr = gamepp.hardware.getSensorInfo.sync()
            } catch {
            }
            if (SensorInfoStr && !sensorInfoModifying) {
                const SensorInfoOriginal = JSON.parse(SensorInfoStr);
                const SensorInfoKeys = Object.keys(SensorInfoOriginal)
                if (!isGPUData) {
                    containsGPU = SensorInfoKeys.some(name => name.includes("GPU"));
                    SensorInfoKeys.forEach(function (item) {
                        if (item.startsWith('CPU')) {
                            if (String(item).toLowerCase().includes('intel')) {
                                cpuType = 'intel'
                            } else {
                                cpuType = 'amd'
                            }
                        }
                        if (item.startsWith("GPU [#0]")) {
                            gpuName0 = item.split(':')[1].trim();
                        } else if (item.startsWith("GPU [#1]")) {
                            gpuName1 = item.split(':')[1].trim();
                        } else if (item.startsWith("GPU [#2]")) {
                            gpuName2 = item.split(':')[1].trim();
                        }
                    });
                    GPU0Data = this.GetHardwareDetails('GPU', gpuName0)
                    GPU1Data = this.GetHardwareDetails('GPU', gpuName1)
                    GPU2Data = this.GetHardwareDetails('GPU', gpuName2)
                    isGPUData = true
                    MemoryData = this.GlobalBaseJsonInfo.MEMORY
                }

                let SensorInfo = this.SensorAddAverageData(SensorInfoOriginal, SensorInfoKeys)

                // console.log('%cSensorInfo','color:green',SensorInfo);   //
                let cpu_temp: Array<string|number> = ['','']
                if (cpuType === 'intel') { // Intel CPU 优先选择 CPU Package 或者 Core Max
                    cpu_temp = this.findValueByKeyAndType(SensorInfo, 'CPU', 'CPU Package', 'temperature')
                    if (cpu_temp[1] === '') {
                        cpu_temp = this.findValueByKeyAndType(SensorInfo, 'CPU', 'Core Max', 'temperature')
                    }
                }else{ // AMD CPU 优先选择 CPU Die (average) 或者 CPU (Tctl/Tdie)
                    cpu_temp = this.findValueByKeyAndType(SensorInfo, 'CPU', 'CPU Die (average)', 'temperature')
                    if (cpu_temp[1] === '') {
                        cpu_temp = this.findValueByKeyAndType(SensorInfo, 'CPU', 'CPU (Tctl/Tdie)', 'temperature')
                    }
                }
                if (cpu_temp[1] === '') { // 都没找到
                    cpu_temp = this.findValueByKeyAndType(SensorInfo, 'CPU', false, 'temperature')
                }
                const cpu_temp_p = this.findValueByKeyAndType(SensorInfo, 'CPU', false, 'temperature')
                const cpu_temp_e = this.findValueByKeyAndType(SensorInfo, 'CPU', 'E-core', 'temperature')

                const cpu_clock = this.findValueByKeyAndType(SensorInfo, 'CPU', false, 'clock')
                const cpu_clock_p = this.findValueByKeyAndType(SensorInfo, 'CPU', 'P Core Clocks', 'clock')
                const cpu_clock_e = this.findValueByKeyAndType(SensorInfo, 'CPU', 'E Core Clocks', 'clock')

                // 电压先从主板找Vcore
                let cpu_voltage = this.findValueByKeyAndType(SensorInfo, 'Mainboard', 'Vcore', 'voltage')
                if (cpu_voltage[1] === '') { // 找不到从CPU里找
                    cpu_voltage = this.findValueByKeyAndType(SensorInfo, 'CPU', false, 'voltage')
                }
                const cpu_voltage_p = this.findValueByKeyAndType(SensorInfo, 'CPU', 'P Core Max VID', 'voltage')
                const cpu_voltage_e = this.findValueByKeyAndType(SensorInfo, 'CPU', 'E Core Max VID', 'voltage')

                const cpu_usage = this.findValueByKeyAndType(SensorInfo, 'CPU', 'Total CPU Usage', 'usage')
                const cpu_usage_p = this.findValueByKeyAndType(SensorInfo, 'CPU', 'P Core Usages', 'usage')
                const cpu_usage_e = this.findValueByKeyAndType(SensorInfo, 'CPU', 'E Core Usages', 'usage')

                let cpu_fan = this.findValueByKeyAndType(SensorInfo, 'Mainboard', 'CPU|Chassis', 'fan')
                if (cpu_fan[0] === '') {
                    cpu_fan = this.findValueByKeyAndType(SensorInfo, 'Mainboard', false, 'fan')
                }
                const cpu_power = this.findValueByKeyAndType(SensorInfo, 'CPU', false, 'power')

                //AMD Enhanced Thermal Limit(AMD温度墙实际百分比)
                const cpu_amd_thermal_limit = this.findValueByKeyAndType(SensorInfo, 'Enhanced', 'Thermal Limit', 'usage')

                //NPU 数据
                const npu_clock = this.findValueByKeyAndType(SensorInfo, 'CPU', 'NPU Clock', 'clock')
                const npu_usage = this.findValueByKeyAndType(SensorInfo, 'CPU', 'NPU Usage', 'usage')


                //================================================================   GPU   ================================================================
                let gpu_temp = this.findValueByKeyAndType(SensorInfo, 'GPU [#0]', false, 'temperature')
                let gpu_clock = this.findValueByKeyAndType(SensorInfo, 'GPU [#0]', false, 'clock')
                const gpu_mem_clock = this.findValueByKeyAndType(SensorInfo, 'GPU [#0]', 'Memory', 'clock')
                let gpu_power = this.findValueByKeyAndType(SensorInfo, 'GPU [#0]', false, 'power')
                let gpu_fan = this.findValueByKeyAndType(SensorInfo, 'GPU [#0]', false, 'fan')
                let gpu_fan_amd = this.findValueByKeyAndType(SensorInfo, 'GPU [#0]', 'Fan PWM', 'other')
                let gpu_fan_unit = 'RPM'
                if (gpu_fan[0] === '' && gpu_fan_amd[0] !== '') {gpu_fan = gpu_fan_amd;gpu_fan_unit = '%'}

                let gpu_mem_usage = this.findValueByKeyAndType(SensorInfo, 'GPU [#0]', 'Memory Usage', 'usage')
                let gpu_mem_usage_amd = this.findValueByKeyAndType(SensorInfo, 'GPU [#0]', 'Memory Dedicated', 'other')
                if (gpu_mem_usage[0] === '' && gpu_mem_usage_amd[0] !== '' && GPU0Data) {gpu_mem_usage = [gpu_mem_usage_amd[0], gameclient.toPercent(gpu_mem_usage_amd[1], GPU0Data?.VideoMemoryNumber)]}


                const gpu_hot_spot_temp = this.findValueByKeyAndType(SensorInfo, 'GPU [#0]', 'Hot Spot Temperature', 'temperature')

                let   gpu_total_usage = this.findValueByKeyAndType(SensorInfo, 'GPU [#0]', false, 'usage')
                let   gpu_d3d_usage = this.findValueByKeyAndType(SensorInfo, 'GPU [#0]', 'D3D Usage', 'usage')
                const gpu_mem_temp = this.findValueByKeyAndType(SensorInfo, 'GPU [#0]', 'Memory Temperature|Memory Junction Temperature', 'temperature')
                const gpu_voltage = this.findValueByKeyAndType(SensorInfo, 'GPU [#0]', 'Voltage', 'voltage')

                if (!containsGPU) {
                    gpu_temp = this.findValueByKeyAndType(SensorInfo, 'CPU', false, 'temperature')
                    gpu_clock = this.findValueByKeyAndType(SensorInfo, 'CPU', 'GPU Clock', 'clock')
                    gpu_total_usage = this.findValueByKeyAndType(SensorInfo, 'CPU', 'GPU D3D Usage', 'usage')
                    gpu_d3d_usage = this.findValueByKeyAndType(SensorInfo, 'CPU', 'GPU D3D Usage', 'usage')
                    gpu_power = this.findValueByKeyAndType(SensorInfo, 'CPU', 'GT Cores Power', 'power')
                    GPU0Data = this.GlobalBaseJsonInfo.GPU.SubNode[0]
                    gpuName0 = GPU0Data.VideoChipset
                }

                const gpu_hotspot_thermal_limit = this.findValueByKeyAndType(SensorInfo, 'GPU [#0]', 'Hotspot Thermal Limit', 'usage')
                const gpu_memory_thermal_limit = this.findValueByKeyAndType(SensorInfo, 'GPU [#0]', 'Memory Thermal Limit', 'usage')

                let gpu_performance_limit = this.findValueByKeyAndType(SensorInfo, 'GPU [#0]', 'Performance Limit - Thermal', 'other')
                let gpu_performance_limit1 = this.findValueByKeyAndType(SensorInfo, 'GPU [#0]', 'Performance Limit - Reliability Voltage', 'other')
                let gpu_performance_limit2 = this.findValueByKeyAndType(SensorInfo, 'GPU [#0]', 'Performance Limit - Max Operating Voltage', 'other')

                //GPU1
                const gpu1_temp = this.findValueByKeyAndType(SensorInfo, 'GPU [#1]', false, 'temperature')
                const gpu1_clock = this.findValueByKeyAndType(SensorInfo, 'GPU [#1]', false, 'clock')
                const gpu1_mem_clock = this.findValueByKeyAndType(SensorInfo, 'GPU [#1]', 'Memory', 'clock')
                const gpu1_power = this.findValueByKeyAndType(SensorInfo, 'GPU [#1]', false, 'power')
                let gpu1_fan = this.findValueByKeyAndType(SensorInfo, 'GPU [#1]', false, 'fan')
                let gpu1_fan_amd = this.findValueByKeyAndType(SensorInfo, 'GPU [#1]', 'Fan PWM', 'other')

                let gpu1_fan_unit = 'RPM'
                if (gpu1_fan[0] === '' && gpu1_fan_amd[0] !== '') {gpu1_fan = gpu1_fan_amd;gpu1_fan_unit = '%'}


                let gpu1_mem_usage = this.findValueByKeyAndType(SensorInfo, 'GPU [#1]', 'Memory Usage', 'usage')
                let gpu1_mem_usage_amd = this.findValueByKeyAndType(SensorInfo, 'GPU [#1]', 'Memory Dedicated', 'other')
                if (gpu1_mem_usage[0] === '' && gpu1_mem_usage_amd[0] !== '' && GPU1Data) {gpu1_mem_usage = [gpu1_mem_usage_amd[0], gameclient.toPercent(gpu1_mem_usage_amd[1], GPU1Data?.VideoMemoryNumber)]}

                const gpu1_hot_spot_temp = this.findValueByKeyAndType(SensorInfo, 'GPU [#1]', 'Hot Spot Temperature', 'temperature')
                const gpu1_total_usage = this.findValueByKeyAndType(SensorInfo, 'GPU [#1]', false, 'usage')
                const gpu1_d3d_usage = this.findValueByKeyAndType(SensorInfo, 'GPU [#1]', 'D3D Usage', 'usage')

                const gpu1_mem_temp = this.findValueByKeyAndType(SensorInfo, 'GPU [#1]', 'Memory Temperature|Memory Junction Temperature', 'temperature')
                const gpu1_voltage = this.findValueByKeyAndType(SensorInfo, 'GPU [#1]', 'Voltage', 'voltage')

                const gpu1_hotspot_thermal_limit = this.findValueByKeyAndType(SensorInfo, 'GPU [#1]', 'Hotspot Thermal Limit', 'usage')
                const gpu1_memory_thermal_limit = this.findValueByKeyAndType(SensorInfo, 'GPU [#1]', 'Memory Thermal Limit', 'usage')

                let gpu1_performance_limit = this.findValueByKeyAndType(SensorInfo, 'GPU [#1]', 'Performance Limit - Thermal', 'other')
                let gpu1_performance_limit1 = this.findValueByKeyAndType(SensorInfo, 'GPU [#1]', 'Performance Limit - Reliability Voltage', 'other')
                let gpu1_performance_limit2 = this.findValueByKeyAndType(SensorInfo, 'GPU [#1]', 'Performance Limit - Max Operating Voltage', 'other')
                //GPU2
                const gpu2_temp = this.findValueByKeyAndType(SensorInfo, 'GPU [#2]', false, 'temperature')
                const gpu2_clock = this.findValueByKeyAndType(SensorInfo, 'GPU [#2]', false, 'clock')
                const gpu2_mem_clock = this.findValueByKeyAndType(SensorInfo, 'GPU [#2]', 'Memory', 'clock')
                const gpu2_power = this.findValueByKeyAndType(SensorInfo, 'GPU [#2]', false, 'power')
                let gpu2_fan = this.findValueByKeyAndType(SensorInfo, 'GPU [#2]', false, 'fan')
                let gpu2_fan_amd = this.findValueByKeyAndType(SensorInfo, 'GPU [#2]', 'Fan PWM', 'other')

                let gpu2_fan_unit = 'RPM'
                if (gpu1_fan[0] === '' && gpu2_fan_amd[0] !== '') {gpu1_fan = gpu2_fan_amd;gpu1_fan_unit = '%'}

                let gpu2_mem_usage = this.findValueByKeyAndType(SensorInfo, 'GPU [#2]', 'Memory Usage', 'usage')
                let gpu2_mem_usage_amd = this.findValueByKeyAndType(SensorInfo, 'GPU [#2]', 'Memory Dedicated', 'other')
                if (gpu2_mem_usage[0] === '' && gpu2_mem_usage_amd[0] !== '' && GPU2Data) {gpu2_mem_usage = [gpu1_mem_usage_amd[0], gameclient.toPercent(gpu1_mem_usage_amd[1], GPU2Data?.VideoMemoryNumber)]}



                const gpu2_hot_spot_temp = this.findValueByKeyAndType(SensorInfo, 'GPU [#2]', 'Hot Spot Temperature', 'temperature')
                const gpu2_total_usage = this.findValueByKeyAndType(SensorInfo, 'GPU [#2]', false, 'usage')
                const gpu2_d3d_usage = this.findValueByKeyAndType(SensorInfo, 'GPU [#2]', 'D3D Usage', 'usage')
                const gpu2_mem_temp = this.findValueByKeyAndType(SensorInfo, 'GPU [#2]', 'Memory Temperature|Memory Junction Temperature', 'temperature')
                const gpu2_voltage = this.findValueByKeyAndType(SensorInfo, 'GPU [#2]', 'Voltage', 'voltage')

                const gpu2_hotspot_thermal_limit = this.findValueByKeyAndType(SensorInfo, 'GPU [#2]', 'Hotspot Thermal Limit', 'usage')
                const gpu2_memory_thermal_limit = this.findValueByKeyAndType(SensorInfo, 'GPU [#2]', 'Memory Thermal Limit', 'usage')


                let gpu2_performance_limit = this.findValueByKeyAndType(SensorInfo, 'GPU [#2]', 'Performance Limit - Thermal', 'other')
                let gpu2_performance_limit1 = this.findValueByKeyAndType(SensorInfo, 'GPU [#2]', 'Performance Limit - Reliability Voltage', 'other')
                let gpu2_performance_limit2 = this.findValueByKeyAndType(SensorInfo, 'GPU [#2]', 'Performance Limit - Max Operating Voltage', 'other')
                //================================================================   GPUEnd   ================================================================



                const mainboard_temp = this.findValueByKeyAndType(SensorInfo, 'Mainboard', false, 'temperature')
                const mainboard_voltage = this.findValueByKeyAndType(SensorInfo, 'Mainboard', 'Vcore', 'voltage')

                // const drive_temp = this.findValueByKeyAndType(SensorInfo, 'S.M.A.R.T', false, 'temperature')
                const drive_temp = this.findValueByKeyAndType(SensorInfo, 'S.M.A.R.T.: ' + mainDisk?.ProductId, false, 'temperature')
                const drive_usage = this.findValueByKeyAndType(SensorInfo, 'Drive: ' + mainDisk?.ProductId, 'Total Activity', 'usage')

                const memory_usage = this.findValueByKeyAndType(SensorInfo, 'System', 'Physical Memory Load', 'other')
                const memory_used_mb = this.findValueByKeyAndType(SensorInfo, 'System', 'Physical Memory Used', 'other')
                const memory_temp = this.findValueByKeyAndType(SensorInfo, 'DIMM', false, 'temperature')
                const memory_clock = this.findValueByKeyAndType(SensorInfo, 'Memory Timings', 'Memory Clock', 'clock')
                const memory_tcas = this.findValueByKeyAndType(SensorInfo, 'Memory Timings', 'Tcas', 'other')
                const memory_trcd = this.findValueByKeyAndType(SensorInfo, 'Memory Timings', 'Trcd', 'other')
                const memory_trp = this.findValueByKeyAndType(SensorInfo, 'Memory Timings', 'Trp', 'other')
                const memory_tras = this.findValueByKeyAndType(SensorInfo, 'Memory Timings', 'Tras', 'other')

                const memory_voltage1 = this.findValueByKeyAndType(SensorInfo, 'Mainboard', 'DRAM', 'voltage')
                const memory_voltage2 = this.findValueByKeyAndType(SensorInfo, 'DIMM', 'VDD|VDDQ', 'voltage')
                const memory_voltage = (memory_voltage1 && memory_voltage1[1] !== '') ? memory_voltage1[1] : (memory_voltage2 && memory_voltage2[1] !== '') ? memory_voltage2[1] : 1.2;


                // const network_download = this.findValueByKeyAndType(SensorInfo, 'Network', 'Current DL', 'other')
                // const network_upload   = this.findValueByKeyAndType(SensorInfo, 'Network', 'Current UP', 'other')
                const network_info   = this.findValueByKeyAndType(SensorInfo, 'Network', false, 'other')

                const battery_chargelevel = this.findValueByKeyAndType(SensorInfo, 'Battery', 'Charge Level', 'other')
                const battery_voltage = this.findValueByKeyAndType(SensorInfo, 'Battery', 'Battery Voltage', 'other')

                const windows_hardware_errors = this.findValueByKeyAndType(SensorInfo, 'Windows Hardware Errors', 'Total Errors', 'other')


                const defaultConfiguration:any = {
                    cpuData   : [
                        { name: 'temp', describe: 'CPU温度', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' ℃' , outKey:'' , inKey:''},
                        { name: 'tempP', describe: 'CPU温度P', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' ℃' , outKey:'' , inKey:''},
                        { name: 'tempE', describe: 'CPU温度E', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' ℃' , outKey:'' , inKey:''},
                        { name: 'clock', describe: 'CPU频率', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'MHz' , outKey:'' , inKey:''},
                        { name: 'clockP', describe: 'CPU频率P', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'MHz' , outKey:'' , inKey:''},
                        { name: 'clockE', describe: 'CPU频率E', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'MHz', outKey:'' , inKey:'' },
                        { name: 'usage', describe: 'CPU占用', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' %' , outKey:'' , inKey:''},
                        { name: 'usageP', describe: 'CPU占用P', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' %' , outKey:'' , inKey:''},
                        { name: 'usageE', describe: 'CPU占用E', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' %' , outKey:'' , inKey:''},
                        { name: 'voltage', describe: 'CPU电压', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'V' , outKey:'' , inKey:''},
                        { name: 'voltageP', describe: 'CPU电压P', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'V' , outKey:'' , inKey:''},
                        { name: 'voltageE', describe: 'CPU电压E', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'V', outKey:'' , inKey:'' },
                        { name: 'power', describe: 'CPU热功耗', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'W' , outKey:'' , inKey:''},
                        { name: 'fan', describe: 'CPU风扇转速', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' RPM' , outKey:'' , inKey:''},
                    ],
                    gpu0Data  : [
                        { name: 'temp', describe: 'GPU温度', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' ℃' , outKey:'' , inKey:''},
                        { name: 'clock', describe: 'GPU频率', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'MHz' , outKey:'' , inKey:''},
                        { name: 'power', describe: 'GPU热功耗', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'W' , outKey:'' , inKey:''},
                        { name: 'd3d_usage', describe: 'GPU占用', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' %' , outKey:'' , inKey:''},
                        { name: 'total_usage', describe: 'GPU占用', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' %' , outKey:'' , inKey:''},
                        { name: 'fan', describe: 'GPU风扇转速', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' ' + gpu_fan_unit , outKey:'' , inKey:''},
                        { name: 'hot spot temp', describe: '核心热点温度', isDef: true, groupIndex: 0, itemIndex: 0, Unit: '℃' , outKey:'' , inKey:''},
                        { name: 'mem_usage', describe: '显存使用率', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' %' , outKey:'' , inKey:''},
                        { name: 'mem_clock', describe: '显存频率', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'MHz', outKey:'' , inKey:'' },
                        { name: 'mem_temp', describe: '显存温度', isDef: true, groupIndex: 0, itemIndex: 0, Unit: '℃' , outKey:'' , inKey:''},
                        { name: 'voltage', describe: 'GPU电压', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'V' , outKey:'' , inKey:''},
                    ],
                    gpu1Data  : [
                        { name: 'temp', describe: 'GPU温度', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' ℃' , outKey:'' , inKey:''},
                        { name: 'clock', describe: 'GPU频率', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'MHz' , outKey:'' , inKey:''},
                        { name: 'power', describe: 'GPU热功耗', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'W' , outKey:'' , inKey:''},
                        { name: 'd3d_usage', describe: 'GPU占用', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' %' , outKey:'' , inKey:''},
                        { name: 'total_usage', describe: 'GPU占用', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' %' , outKey:'' , inKey:''},
                        { name: 'fan', describe: 'GPU风扇转速', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' ' + gpu1_fan_unit , outKey:'' , inKey:''},
                        { name: 'hot spot temp', describe: '核心热点温度', isDef: true, groupIndex: 0, itemIndex: 0, Unit: '℃' , outKey:'' , inKey:''},
                        { name: 'mem_usage', describe: '显存使用率', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' %', outKey:'' , inKey:'' },
                        { name: 'mem_clock', describe: '显存频率', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'MHz' , outKey:'' , inKey:''},
                        { name: 'mem_temp', describe: '显存温度', isDef: true, groupIndex: 0, itemIndex: 0, Unit: '℃' , outKey:'' , inKey:''},
                        { name: 'voltage', describe: 'GPU电压', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'V' , outKey:'' , inKey:''},
                    ],
                    gpu2Data  : [
                        { name: 'temp', describe: 'GPU2温度', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' ℃', outKey:'' , inKey:'' },
                        { name: 'clock', describe: 'GPU频率', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'MHz' , outKey:'' , inKey:''},
                        { name: 'power', describe: 'GPU热功耗', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'W' , outKey:'' , inKey:''},
                        { name: 'd3d_usage', describe: 'GPU占用', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' %' , outKey:'' , inKey:''},
                        { name: 'total_usage', describe: 'GPU占用', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' %', outKey:'' , inKey:'' },
                        { name: 'fan', describe: 'GPU风扇转速', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' ' + gpu2_fan_unit , outKey:'' , inKey:''},
                        { name: 'hot spot temp', describe: '核心热点温度', isDef: true, groupIndex: 0, itemIndex: 0, Unit: '℃', outKey:'' , inKey:'' },
                        { name: 'mem_usage', describe: '显存使用率', isDef: true, groupIndex: 0, itemIndex: 0, Unit: '  %' , outKey:'' , inKey:''},
                        { name: 'mem_clock', describe: '显存频率', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'MHz' , outKey:'' , inKey:''},
                        { name: 'mem_temp', describe: '显存温度', isDef: true, groupIndex: 0, itemIndex: 0, Unit: '℃' , outKey:'' , inKey:''},
                        { name: 'voltage', describe: 'GPU电压', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'V' , outKey:'' , inKey:''},
                    ],
                    memoryData: [
                        { name: 'usage',       describe: '内存占用', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' %' , outKey:'' , inKey:''},
                        { name: 'temperature', describe: '内存温度', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' ℃' , outKey:'' , inKey:''},
                    ],
                    boardData : [
                        { name: 'temp', describe: '主板温度', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' ℃' , outKey:'' , inKey:''}
                    ],
                    networkData: [
                        { name: 'download', describe: '下载', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' KB/s' , outKey:'' , inKey:''},
                        { name: 'upload',   describe: '上传', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' KB/s' , outKey:'' , inKey:''}
                    ],
                    diskData  : [],
                    default   : true
                }

                let smartCount = 0;
                let originalSMART:any = []
                for (let key in SensorInfo) {
                    if (key.startsWith("S.M.A.R.T.:")) {
                        smartCount++; // 如果是，递增S.M.A.R.T.的计数器
                        let foundTemperature = false;
                        let key_name = (key.replace(/ \(.*\)$/, '').replace('S.M.A.R.T.: ', ''));
                        for (let item of SensorInfo[key]) {
                            if (foundTemperature) {break;}
                            for (let subKey in item) {
                                if (item[subKey].type === "temperature") {
                                    let disk_temp = parseFloat(item[subKey].value);
                                    let data:any = {
                                        name      : 'temperature',
                                        describe  : key_name,
                                        value     : disk_temp,
                                        isDef     : true,
                                        groupIndex: 0,
                                        itemIndex : 0,
                                        defVal    : disk_temp,
                                        Unit      : ' ℃',
                                        inGameMonitor: false,
                                        outKey:'' ,
                                        inKey:''
                                    }
                                    defaultConfiguration.diskData.push(data);
                                    originalSMART.push(data)
                                    foundTemperature = true;
                                    break;
                                }
                            }
                        }
                    }
                }

                //硬盘更换后 重新更新 local diskData
                if (localSensorListX && localSensorListX.diskData) {
                    let allMatched = true;
                    for (let i = 0; i < Math.min(originalSMART.length, localSensorListX.diskData.length); i++) {
                        if (originalSMART[i].describe !== localSensorListX.diskData[i].describe) {
                            allMatched = false;
                            break;
                        }
                    }
                    if (smartCount !== localSensorListX.diskData.length || !allMatched) {
                        localSensorListX.diskData = originalSMART
                        window.localStorage.setItem('chooseSensor_list_v1', JSON.stringify(localSensorListX))
                    }
                }

                let [cpu_temp_value,cpu_temp_value_p,cpu_temp_value_e,cpu_load_value,cpu_load_value_p,cpu_load_value_e,cpu_clock_value,cpu_clock_value_p,cpu_clock_value_e,cpu_power_value,cpu_voltage_value,cpu_voltage_value_p,cpu_voltage_value_e,cpu_fan_value]=[cpu_temp[1],cpu_temp_p[1],cpu_temp_e[1],cpu_usage[1],cpu_usage_p[1],cpu_usage_e[1],cpu_clock[1],cpu_clock_p[1],cpu_clock_e[1],cpu_power[1],cpu_voltage[1],cpu_voltage_p[1],cpu_voltage_e[1],cpu_fan[1]];

                let [gpu_temp_value, gpu_power_value, gpu_hot_spot_temp_value, gpu_clock_value, gpu_d3d_usage_value, gpu_fan_value, gpu_mem_usage_value, gpu_mem_clock_value, gpu_mem_temp_value, gpu_total_usage_value] = [gpu_temp[1], gpu_power[1], gpu_hot_spot_temp[1], gpu_clock[1], gpu_d3d_usage[1], gpu_fan[1], gpu_mem_usage[1], gpu_mem_clock[1], gpu_mem_temp[1], gpu_total_usage[1]];

                let [gpu1_temp_value, gpu1_power_value, gpu1_hot_spot_temp_value, gpu1_clock_value, gpu1_d3d_usage_value, gpu1_fan_value, gpu1_mem_usage_value, gpu1_mem_clock_value, gpu1_mem_temp_value, gpu1_total_usage_value] = [gpu1_temp[1], gpu1_power[1], gpu1_hot_spot_temp[1], gpu1_clock[1], gpu1_d3d_usage[1], gpu1_fan[1], gpu1_mem_usage[1], gpu1_mem_clock[1], gpu1_mem_temp[1], gpu1_total_usage[1]];

                let [gpu2_temp_value, gpu2_power_value, gpu2_hot_spot_temp_value, gpu2_clock_value, gpu2_d3d_usage_value, gpu2_fan_value, gpu2_mem_usage_value, gpu2_mem_clock_value, gpu2_mem_temp_value, gpu2_total_usage_value] = [gpu2_temp[1], gpu2_power[1], gpu2_hot_spot_temp[1], gpu2_clock[1], gpu2_d3d_usage[1], gpu2_fan[1], gpu2_mem_usage[1], gpu2_mem_clock[1], gpu1_mem_temp[1], gpu2_total_usage[1]];

                // let drive_temp_value = drive_temp[1]
                let mainboard_temp_value = mainboard_temp[1]
                let memory_temp_value = memory_temp[1]
                let memory_usage_value = memory_usage[1]

                // let network_dl_value = network_upload[1]
                // let network_up_value = network_download[1]

                let drive_list:any = [], drive_usage_list:any = [],drive_temp_list:any = []

                // console.warn('SensorInfo',SensorInfo);

                const obj = Object.keys(SensorInfo)
                // console.warn('obj',obj);

                if (chooseSensor_list_xStr) {
                    localSensorListX = JSON.parse(chooseSensor_list_xStr)

                    //================================================================   CPU   ================================================================
                    const [temp, temp_p, temp_e, clock, clock_p, clock_e, load, load_p, load_e, voltage, voltage_p, voltage_e, power, fan] = localSensorListX.cpuData;

                    cpu_temp_value = (!temp.isDef || temp.isDefServer) ? this.SensorInfoDataProcess(temp.isDef, temp.groupIndex, temp.itemIndex, true, SensorInfo,temp.outKey,temp.inKey) : cpu_temp[1];
                    cpu_temp_value_p = (!temp_p.isDef || temp_p.isDefServer) ? this.SensorInfoDataProcess(temp_p.isDef, temp_p.groupIndex, temp_p.itemIndex, true, SensorInfo,temp_p.outKey,temp_p.inKey) : cpu_temp_p[1];
                    cpu_temp_value_e = (!temp_e.isDef || temp_e.isDefServer) ? this.SensorInfoDataProcess(temp_e.isDef, temp_e.groupIndex, temp_e.itemIndex, true, SensorInfo,temp_e.outKey,temp_e.inKey) : cpu_temp_e[1];

                    cpu_load_value = (!load.isDef || load.isDefServer) ? this.SensorInfoDataProcess(load.isDef, load.groupIndex, load.itemIndex, true, SensorInfo,load.outKey,load.inKey) : cpu_usage[1];
                    cpu_load_value_p = (!load_p.isDef || load_p.isDefServer) ? this.SensorInfoDataProcess(load_p.isDef, load_p.groupIndex, load_p.itemIndex, true, SensorInfo,load_p.outKey,load_p.inKey) : cpu_usage_p[1];
                    cpu_load_value_e = (!load_e.isDef || load_e.isDefServer) ? this.SensorInfoDataProcess(load_e.isDef, load_e.groupIndex, load_e.itemIndex, true, SensorInfo,load_e.outKey,load_e.inKey) : cpu_usage_e[1];


                    cpu_clock_value = (!clock.isDef || clock.isDefServer) ? this.SensorInfoDataProcess(clock.isDef, clock.groupIndex, clock.itemIndex, true, SensorInfo,clock.outKey,clock.inKey) : cpu_clock[1];
                    cpu_clock_value_p = (!clock_p.isDef || clock_p.isDefServer) ? this.SensorInfoDataProcess(clock_p.isDef, clock_p.groupIndex, clock_p.itemIndex, true, SensorInfo,clock_p.outKey,clock_p.inKey) : cpu_clock_p[1];
                    cpu_clock_value_e = (!clock_e.isDef || clock_e.isDefServer) ? this.SensorInfoDataProcess(clock_e.isDef, clock_e.groupIndex, clock_e.itemIndex, true, SensorInfo,clock_e.outKey,clock_e.inKey) : cpu_clock_e[1];

                    cpu_voltage_value = (!voltage.isDef || voltage.isDefServer) ? this.SensorInfoDataProcess(voltage.isDef, voltage.groupIndex, voltage.itemIndex, true, SensorInfo,voltage.outKey,voltage.inKey) : cpu_voltage[1];
                    cpu_voltage_value_p = (!voltage_p.isDef || voltage_p.isDefServer) ? this.SensorInfoDataProcess(voltage_p.isDef, voltage_p.groupIndex, voltage_p.itemIndex, true, SensorInfo,voltage_p.outKey,voltage_p.inKey) : cpu_voltage_p[1];
                    cpu_voltage_value_e = (!voltage_e.isDef || voltage_e.isDefServer) ? this.SensorInfoDataProcess(voltage_e.isDef, voltage_e.groupIndex, voltage_e.itemIndex, true, SensorInfo,voltage_e.outKey,voltage_e.inKey) : cpu_voltage_e[1];

                    cpu_power_value = (!power.isDef || power.isDefServer) ? this.SensorInfoDataProcess(power.isDef, power.groupIndex, power.itemIndex, true, SensorInfo,power.outKey,power.inKey) : cpu_power[1];
                    cpu_fan_value = (!fan.isDef || fan.isDefServer) ? this.SensorInfoDataProcess(fan.isDef, fan.groupIndex, fan.itemIndex, true, SensorInfo,fan.outKey,fan.inKey,fan.Unit) : cpu_fan[1];

                    //================================================================   GPU   ================================================================
                    const [gpu0_temp_l, gpu0_clock_l, gpu0_power_l, gpu0_load_d3d_l, gpu0_total_usage_l, gpu0_fan_l, gpu0_hot_temp_l, gpu0_mem_usage_l, gpu0_mem_clock_l, gpu0_mem_temp_l] = localSensorListX.gpu0Data;
                    gpu_temp_value = !gpu0_temp_l.isDef ? this.SensorInfoDataProcess(gpu0_temp_l.isDef, gpu0_temp_l.groupIndex, gpu0_temp_l.itemIndex, true, SensorInfo,gpu0_temp_l.outKey,gpu0_temp_l.inKey) : gpu_temp[1];
                    gpu_power_value = !gpu0_power_l.isDef ? this.SensorInfoDataProcess(gpu0_power_l.isDef, gpu0_power_l.groupIndex, gpu0_power_l.itemIndex, true, SensorInfo,gpu0_power_l.outKey,gpu0_power_l.inKey) : gpu_power[1];
                    gpu_hot_spot_temp_value = !gpu0_hot_temp_l.isDef ? this.SensorInfoDataProcess(gpu0_hot_temp_l.isDef, gpu0_hot_temp_l.groupIndex, gpu0_hot_temp_l.itemIndex, true, SensorInfo,gpu0_hot_temp_l.outKey,gpu0_hot_temp_l.inKey) : gpu_hot_spot_temp[1];
                    gpu_clock_value = !gpu0_clock_l.isDef ? this.SensorInfoDataProcess(gpu0_clock_l.isDef, gpu0_clock_l.groupIndex, gpu0_clock_l.itemIndex, true, SensorInfo,gpu0_clock_l.outKey,gpu0_clock_l.inKey) : gpu_clock[1];
                    gpu_d3d_usage_value = !gpu0_load_d3d_l.isDef ? this.SensorInfoDataProcess(gpu0_load_d3d_l.isDef, gpu0_load_d3d_l.groupIndex, gpu0_load_d3d_l.itemIndex, true, SensorInfo,gpu0_load_d3d_l.outKey,gpu0_load_d3d_l.inKey) : gpu_d3d_usage[1];
                    gpu_total_usage_value = !gpu0_total_usage_l.isDef ? this.SensorInfoDataProcess(gpu0_total_usage_l.isDef, gpu0_total_usage_l.groupIndex, gpu0_total_usage_l.itemIndex, true, SensorInfo,gpu0_total_usage_l.outKey,gpu0_total_usage_l.inKey) : gpu_total_usage[1];
                    gpu_fan_value = !gpu0_fan_l.isDef ? this.SensorInfoDataProcess(gpu0_fan_l.isDef, gpu0_fan_l.groupIndex, gpu0_fan_l.itemIndex, true, SensorInfo,gpu0_fan_l.outKey,gpu0_fan_l.inKey) : gpu_fan[1];
                    gpu_mem_usage_value = !gpu0_mem_usage_l.isDef ? this.SensorInfoDataProcess(gpu0_mem_usage_l.isDef, gpu0_mem_usage_l.groupIndex, gpu0_mem_usage_l.itemIndex, true, SensorInfo,gpu0_mem_usage_l.outKey,gpu0_mem_usage_l.inKey) : gpu_mem_usage[1];
                    gpu_mem_clock_value = !gpu0_mem_clock_l.isDef ? this.SensorInfoDataProcess(gpu0_mem_clock_l.isDef, gpu0_mem_clock_l.groupIndex, gpu0_mem_clock_l.itemIndex, true, SensorInfo,gpu0_mem_clock_l.outKey,gpu0_mem_clock_l.inKey) : gpu_mem_clock[1];
                    gpu_mem_temp_value = !gpu0_mem_temp_l.isDef ? this.SensorInfoDataProcess(gpu0_mem_temp_l.isDef, gpu0_mem_temp_l.groupIndex, gpu0_mem_temp_l.itemIndex, true, SensorInfo,gpu0_mem_temp_l.outKey,gpu0_mem_temp_l.inKey) : gpu_mem_temp[1];


                    const [gpu1_temp_l, gpu1_clock_l, gpu1_power_l, gpu1_load_d3d_l, gpu1_total_usage_l, gpu1_fan_l, gpu1_hot_temp_l, gpu1_mem_usage_l, gpu1_mem_clock_l, gpu1_mem_temp_l] = localSensorListX.gpu1Data;
                    gpu1_temp_value = !gpu1_temp_l.isDef ? this.SensorInfoDataProcess(gpu1_temp_l.isDef, gpu1_temp_l.groupIndex, gpu1_temp_l.itemIndex, true, SensorInfo,gpu1_temp_l.outKey,gpu1_temp_l.inKey) : gpu1_temp[1];
                    gpu1_power_value = !gpu1_power_l.isDef ? this.SensorInfoDataProcess(gpu1_power_l.isDef, gpu1_power_l.groupIndex, gpu1_power_l.itemIndex, true, SensorInfo,gpu1_power_l.outKey,gpu1_power_l.inKey) : gpu1_power[1];
                    gpu1_hot_spot_temp_value = !gpu1_hot_temp_l.isDef ? this.SensorInfoDataProcess(gpu1_hot_temp_l.isDef, gpu1_hot_temp_l.groupIndex, gpu1_hot_temp_l.itemIndex, true, SensorInfo,gpu1_hot_temp_l.outKey,gpu1_hot_temp_l.inKey) : gpu1_hot_spot_temp[1];
                    gpu1_clock_value = !gpu1_clock_l.isDef ? this.SensorInfoDataProcess(gpu1_clock_l.isDef, gpu1_clock_l.groupIndex, gpu1_clock_l.itemIndex, true, SensorInfo,gpu1_clock_l.outKey,gpu1_clock_l.inKey) : gpu1_clock[1];
                    gpu1_d3d_usage_value = !gpu1_load_d3d_l.isDef ? this.SensorInfoDataProcess(gpu1_load_d3d_l.isDef, gpu1_load_d3d_l.groupIndex, gpu1_load_d3d_l.itemIndex, true, SensorInfo,gpu1_load_d3d_l.outKey,gpu1_load_d3d_l.inKey) : gpu1_d3d_usage[1];
                    gpu1_total_usage_value = !gpu1_total_usage_l.isDef ? this.SensorInfoDataProcess(gpu1_total_usage_l.isDef, gpu1_total_usage_l.groupIndex, gpu1_total_usage_l.itemIndex, true, SensorInfo,gpu1_total_usage_l.outKey,gpu1_total_usage_l.inKey) : gpu1_total_usage[1];
                    gpu1_fan_value = !gpu1_fan_l.isDef ? this.SensorInfoDataProcess(gpu1_fan_l.isDef, gpu1_fan_l.groupIndex, gpu1_fan_l.itemIndex, true, SensorInfo,gpu1_fan_l.outKey,gpu1_fan_l.inKey) : gpu1_fan[1];
                    gpu1_mem_usage_value = !gpu1_mem_usage_l.isDef ? this.SensorInfoDataProcess(gpu1_mem_usage_l.isDef, gpu1_mem_usage_l.groupIndex, gpu1_mem_usage_l.itemIndex, true, SensorInfo,gpu1_mem_usage_l.outKey,gpu1_mem_usage_l.inKey) : gpu1_mem_usage[1];
                    gpu1_mem_clock_value = !gpu1_mem_clock_l.isDef ? this.SensorInfoDataProcess(gpu1_mem_clock_l.isDef, gpu1_mem_clock_l.groupIndex, gpu1_mem_clock_l.itemIndex, true, SensorInfo,gpu1_mem_clock_l.outKey,gpu1_mem_clock_l.inKey) : gpu1_mem_clock[1];
                    gpu1_mem_temp_value = !gpu1_mem_temp_l.isDef ? this.SensorInfoDataProcess(gpu1_mem_temp_l.isDef, gpu1_mem_temp_l.groupIndex, gpu1_mem_temp_l.itemIndex, true, SensorInfo,gpu1_mem_temp_l.outKey,gpu1_mem_temp_l.inKey) : gpu1_mem_temp[1];


                    const [gpu2_temp_l, gpu2_clock_l, gpu2_power_l, gpu2_load_d3d_l, gpu2_total_usage_l, gpu2_fan_l, gpu2_hot_temp_l, gpu2_mem_usage_l, gpu2_mem_clock_l, gpu2_mem_temp_l] = localSensorListX.gpu2Data;
                    gpu2_temp_value = !gpu2_temp_l.isDef ? this.SensorInfoDataProcess(gpu2_temp_l.isDef, gpu2_temp_l.groupIndex, gpu2_temp_l.itemIndex, true, SensorInfo,gpu2_temp_l.outKey,gpu2_temp_l.inKey) :  gpu2_temp[1];
                    gpu2_power_value = !gpu2_power_l.isDef ? this.SensorInfoDataProcess(gpu2_power_l.isDef, gpu2_power_l.groupIndex, gpu2_power_l.itemIndex, true, SensorInfo,gpu2_power_l.outKey,gpu2_power_l.inKey) :  gpu2_power[1];
                    gpu2_hot_spot_temp_value = !gpu2_hot_temp_l.isDef ? this.SensorInfoDataProcess(gpu2_hot_temp_l.isDef, gpu2_hot_temp_l.groupIndex, gpu2_hot_temp_l.itemIndex, true, SensorInfo,gpu2_hot_temp_l.outKey,gpu2_hot_temp_l.inKey) :  gpu2_hot_spot_temp[1];
                    gpu2_clock_value = !gpu2_clock_l.isDef ? this.SensorInfoDataProcess(gpu2_clock_l.isDef, gpu2_clock_l.groupIndex, gpu2_clock_l.itemIndex, true, SensorInfo,gpu2_clock_l.outKey,gpu2_clock_l.inKey) :  gpu2_clock[1];
                    gpu2_d3d_usage_value = !gpu2_load_d3d_l.isDef ? this.SensorInfoDataProcess(gpu2_load_d3d_l.isDef, gpu2_load_d3d_l.groupIndex, gpu2_load_d3d_l.itemIndex, true, SensorInfo,gpu2_load_d3d_l.outKey,gpu2_load_d3d_l.inKey) :  gpu2_d3d_usage[1];
                    gpu2_total_usage_value = !gpu2_total_usage_l.isDef ? this.SensorInfoDataProcess(gpu2_total_usage_l.isDef, gpu2_total_usage_l.groupIndex, gpu2_total_usage_l.itemIndex, true, SensorInfo,gpu2_total_usage_l.outKey,gpu2_total_usage_l.inKey) :  gpu2_total_usage[1];
                    gpu2_fan_value = !gpu2_fan_l.isDef ? this.SensorInfoDataProcess(gpu2_fan_l.isDef, gpu2_fan_l.groupIndex, gpu2_fan_l.itemIndex, true, SensorInfo,gpu2_fan_l.outKey,gpu2_fan_l.inKey) :  gpu2_fan[1];
                    gpu2_mem_usage_value = !gpu2_mem_usage_l.isDef ? this.SensorInfoDataProcess(gpu2_mem_usage_l.isDef, gpu2_mem_usage_l.groupIndex, gpu2_mem_usage_l.itemIndex, true, SensorInfo,gpu2_mem_usage_l.outKey,gpu2_mem_usage_l.inKey) :  gpu2_mem_usage[1];
                    gpu2_mem_clock_value = !gpu2_mem_clock_l.isDef ? this.SensorInfoDataProcess(gpu2_mem_clock_l.isDef, gpu2_mem_clock_l.groupIndex, gpu2_mem_clock_l.itemIndex, true, SensorInfo,gpu2_mem_clock_l.outKey,gpu2_mem_clock_l.inKey) :  gpu2_mem_clock[1];
                    gpu2_mem_temp_value = !gpu2_mem_temp_l.isDef ? this.SensorInfoDataProcess(gpu2_mem_temp_l.isDef, gpu2_mem_temp_l.groupIndex, gpu2_mem_temp_l.itemIndex, true, SensorInfo,gpu2_mem_temp_l.outKey,gpu2_mem_temp_l.inKey) :  gpu2_mem_temp[1];

                    //================================================================   硬盘   ================================================================
                    // const temp2 = localSensorListX.diskData[0]
                    // if (temp2) {
                    //     drive_temp_value = !temp2.isDef ? this.SensorInfoDataProcess(temp2.isDef, temp2.groupIndex, temp2.itemIndex, true, SensorInfo) : drive_temp[1];
                    // }


                    //================================================================   主板   ================================================================
                    const board_temp_l = localSensorListX.boardData[0]
                    mainboard_temp_value = (!board_temp_l.isDef || board_temp_l.isDefServer) ? this.SensorInfoDataProcess(board_temp_l.isDef, board_temp_l.groupIndex, board_temp_l.itemIndex, true, SensorInfo,board_temp_l.outKey,board_temp_l.inKey) : mainboard_temp[1];

                    //================================================================   内存   ================================================================
                    const memory_temp_l = localSensorListX.memoryData[1]
                    memory_temp_value = !memory_temp_l.isDef ? this.SensorInfoDataProcess(memory_temp_l.isDef, memory_temp_l.groupIndex, memory_temp_l.itemIndex, true, SensorInfo,memory_temp_l.outKey,memory_temp_l.inKey) : memory_temp[1];



                    const memory_load_l = localSensorListX.memoryData[0]
                    memory_usage_value = !memory_load_l.isDef ? this.SensorInfoDataProcess(memory_load_l.isDef, memory_load_l.groupIndex, memory_load_l.itemIndex, true, SensorInfo,memory_load_l.outKey,memory_load_l.inKey) : memory_usage[1];


                    //================================================================   网络   ================================================================
                    // const [network_dl_l, network_up_l] = localSensorListX.networkData;
                    // network_dl_value = !network_dl_l.isDef ? this.SensorInfoDataProcess(network_dl_l.isDef, network_dl_l.groupIndex, network_dl_l.itemIndex, true, SensorInfo) : network_download[1];
                    // network_up_value = !network_up_l.isDef ? this.SensorInfoDataProcess(network_up_l.isDef, network_up_l.groupIndex, network_up_l.itemIndex, true, SensorInfo) : network_upload[1];


                    localSensorListX.diskData.forEach((disk, index) => {
                        if (disk.inGameMonitor) {
                            let disk_value:any = disk.value
                            if (!disk.isDef) {
                                disk_value = this.SensorInfoDataProcess(false, disk.groupIndex, disk.itemIndex, false, SensorInfo,disk.outKey,disk.inKey)
                            }
                            drive_list.push(disk_value)
                        }
                        let disk_usage_value:any = [0, 0]
                        if (disk.describe) {
                            disk_usage_value = this.findValueByKeyAndType(SensorInfo, 'Drive: ' + disk.describe, 'Total Activity', 'usage')
                        }
                        drive_usage_list.push(parseInt(disk_usage_value[1]))


                        let disk_temp_value:any = this.findValueByKeyAndType(SensorInfo, 'S.M.A.R.T.: ' + disk.describe, 'Temperature', 'temperature')
                        drive_temp_list.push(parseInt(disk_temp_value[1]))
                    })
                } else {
                    window.localStorage.setItem('chooseSensor_list_v1', JSON.stringify(defaultConfiguration))
                    chooseSensor_list_xStr = JSON.stringify(defaultConfiguration)
                }
                ///////////////////// 传感器全匹配查找/////////////////////////
                let res:any = localStorage.getItem('chooseSensor_list_v1')
                let list_v1 = JSON.parse(res)
                // console.warn('list_v1',list_v1);
                // console.warn('sadsaaaaaaaaaaaaaaaddddddddddddddddsa',SensorInfo);

                if(!list_v1['cpuData'][0].hasOwnProperty('outKey'))
                {
                    console.warn('更新脚本local');
                    window.localStorage.setItem('chooseSensor_list_v1', JSON.stringify(defaultConfiguration))
                    chooseSensor_list_xStr = JSON.stringify(defaultConfiguration)
                }

                let gpu_mem_usage_str = (GPU0Data?.VideoMemoryNumber * (gpu_mem_usage_value as any)) / 100;
                gpu_mem_usage_str = isNaN(gpu_mem_usage_str) ? 0 : gpu_mem_usage_str;

                let gpu1_mem_usage_str = (GPU1Data?.VideoMemoryNumber * (gpu1_mem_usage_value as any)) / 100;
                gpu1_mem_usage_str = isNaN(gpu1_mem_usage_str) ? 0 : gpu1_mem_usage_str;

                let gpu2_mem_usage_str = (GPU2Data?.VideoMemoryNumber * (gpu2_mem_usage_value as any)) / 100;
                gpu2_mem_usage_str = isNaN(gpu2_mem_usage_str) ? 0 : gpu2_mem_usage_str;


                const Drive_Read = this.findValueByKeyAndType(SensorInfo, 'Drive:', 'Read Rate', 'other') //null
                const Drive_Write = this.findValueByKeyAndType(SensorInfo, 'Drive:', 'Write Rate', 'other') //null


                //limit
                const PL1 = this.findValueByKeyAndType(SensorInfo, 'CPU', 'IA: Package-Level RAPL/PBM PL1', 'other')

                const PL23 = this.findValueByKeyAndType(SensorInfo, 'CPU', 'IA: Package-Level RAPL/PBM PL2,PL3', 'other')

                const elecTri  = this.findValueByKeyAndType(SensorInfo, 'CPU', 'IA: Electrical Design Point/Other (ICCmax,PL4,SVID,DDR RAPL)', 'other')

                const PPT = this.findValueByKeyAndType(SensorInfo, 'CPU', 'CPU PPT Limit', 'usage')

                const EDC = this.findValueByKeyAndType(SensorInfo, 'CPU', 'CPU EDC Limit', 'usage')

                console.log('%cPPT','color:red',PPT);
                console.log('%cEDC','color:red',EDC);

                let limitAll =
                {
                    PL1:PL1,
                    PL23:PL23,
                    elecTri:elecTri,
                    PPT:PPT,
                    EDC:EDC,
                }
                //DDR5
                const ddr5_temp_1 = this.findValueByKeyAndType(SensorInfo, 'DDR5 DIMM [#0]', false, 'temperature') //null
                const ddr5_temp_2 = this.findValueByKeyAndType(SensorInfo, 'DDR5 DIMM [#1]', false, 'temperature') //null
                const ddr5_temp_3 = this.findValueByKeyAndType(SensorInfo, 'DDR5 DIMM [#2]', false, 'temperature') //null
                const ddr5_temp_4 = this.findValueByKeyAndType(SensorInfo, 'DDR5 DIMM [#3]', false, 'temperature') //null
                const ddr5_temp_5 = this.findValueByKeyAndType(SensorInfo, 'DDR5 DIMM [#4]', false, 'temperature') //null

                const ddr5_voltage_1 = this.findValueByKeyAndType(SensorInfo, 'DDR5 DIMM [#0]', false, 'voltage') //null
                const ddr5_voltage_2 = this.findValueByKeyAndType(SensorInfo, 'DDR5 DIMM [#1]', false, 'voltage') //null
                const ddr5_voltage_3 = this.findValueByKeyAndType(SensorInfo, 'DDR5 DIMM [#2]', false, 'voltage') //null
                const ddr5_voltage_4 = this.findValueByKeyAndType(SensorInfo, 'DDR5 DIMM [#3]', false, 'voltage') //null
                const ddr5_voltage_5 = this.findValueByKeyAndType(SensorInfo, 'DDR5 DIMM [#4]', false, 'voltage') //null
                let ddr5_temperature =
                {
                    '#1':ddr5_temp_1[0] === ''?null:ddr5_temp_1[1],
                    '#2':ddr5_temp_2[0] === ''?null:ddr5_temp_2[1],
                    '#3':ddr5_temp_3[0] === ''?null:ddr5_temp_3[1],
                    '#4':ddr5_temp_4[0] === ''?null:ddr5_temp_4[1],
                    '#5':ddr5_temp_5[0] === ''?null:ddr5_temp_5[1],
                }

                let ddr5_voltage =
                {
                    '#1':ddr5_voltage_1[0] === ''?null:ddr5_voltage_1[1],
                    '#2':ddr5_voltage_2[0] === ''?null:ddr5_voltage_2[1],
                    '#3':ddr5_voltage_3[0] === ''?null:ddr5_voltage_3[1],
                    '#4':ddr5_voltage_4[0] === ''?null:ddr5_voltage_4[1],
                    '#5':ddr5_voltage_5[0] === ''?null:ddr5_voltage_5[1],
                }

                // 性能限制数据
                let organizeSensorInfoData = this.organizeSensorInfo(SensorInfo)

                let gpu0_mem_usage_mb = Math.floor(gpu_mem_usage_str) + 'M/'  +  (GPU0Data?.VideoMemoryNumber || 0) + 'M'
                let gpu1_mem_usage_mb = Math.floor(gpu1_mem_usage_str) + 'M/' + (GPU1Data?.VideoMemoryNumber || 0) + 'M'
                let gpu2_mem_usage_mb = Math.floor(gpu2_mem_usage_str) + 'M/' + (GPU2Data?.VideoMemoryNumber || 0) + 'M'
                let data_arr:any = {
                    cpu: {
                        name           : this.GlobalBaseJsonInfo.CPU && this.GlobalBaseJsonInfo.CPU.SubNode ? this.GlobalBaseJsonInfo.CPU.SubNode[0].ProcessorName : 'Null',
                        temp           : this.CheckValue(cpu_temp_value),
                        tempP          : this.CheckValue(cpu_temp_value_p),
                        tempE          : this.CheckValue(cpu_temp_value_e),
                        clock          : this.CheckValue(cpu_clock_value),
                        clockP         : this.CheckValue(cpu_clock_value_p),
                        clockE         : this.CheckValue(cpu_clock_value_e),
                        usage          : this.checkValueWithLimit(cpu_load_value),
                        usageP         : this.CheckValue(cpu_load_value_p),
                        usageE         : this.CheckValue(cpu_load_value_e),
                        voltage        : this.CheckValue(cpu_voltage_value),
                        voltageP       : this.CheckValue(cpu_voltage_value_p),
                        voltageE       : this.CheckValue(cpu_voltage_value_e),
                        power          : this.CheckValue(cpu_power_value),
                        fan            : this.CheckValue(cpu_fan_value),
                        limit          : organizeSensorInfoData['PLR'],
                        core_info      : organizeSensorInfoData['Core'],
                        effective_clock: organizeSensorInfoData['EffectiveClock'],
                        thread         : organizeSensorInfoData['Thread'],
                        amd_thermal    : this.CheckValue(cpu_amd_thermal_limit[1]),
                        npu_clock      : this.CheckValue(npu_clock[1]),
                        npu_usage      : this.CheckValue(npu_usage[1]),
                        limitAll       : limitAll
                    },
                    gpu_list : [
                        {
                            temp                    : this.CheckValue(gpu_temp_value),
                            hot_spot_temp           : this.CheckValue(gpu_hot_spot_temp_value),
                            clock                   : this.CheckValue(gpu_clock_value),
                            power                   : this.CheckValue(gpu_power_value),
                            fan                     : this.CheckValue(gpu_fan_value),
                            d3d_usage               : this.CheckValue(gpu_d3d_usage_value),
                            total_usage             : this.CheckValue(gpu_total_usage_value),
                            usage_str               : this.CheckValue(gpu_d3d_usage_value) + '%/' + this.CheckValue(gpu_total_usage_value) + '%',
                            mem_usage               : this.CheckValue(gpu_mem_usage_value),
                            mem_usage_mb            : gpu0_mem_usage_mb === "0M/0M" ? null : gpu0_mem_usage_mb,
                            mem_clock               : this.CheckValue(gpu_mem_clock_value),
                            mem_temp                : this.CheckValue(gpu_mem_temp_value),
                            mem_size                : GPU0Data?.VideoMemoryNumber,
                            name                    : gpuName0,
                            shadres                 : Number(GPU0Data?.NumberOfUnifiedShaders) || Number(GPU0Data?.['NumberOfALUs(cores)']),
                            videobus                : GPU0Data?.VideoBus,
                            voltage                 : this.CheckValue(gpu_voltage[1]),
                            thermal_hotspot         : this.CheckValue(gpu_hotspot_thermal_limit[1]),
                            thermal_memory          : this.CheckValue(gpu_memory_thermal_limit[1]),
                            pl_thermal              : this.CheckValue(gpu_performance_limit[1]),
                            pl_reliability_voltage  : this.CheckValue(gpu_performance_limit1[1]),
                            pl_max_operating_voltage: this.CheckValue(gpu_performance_limit2[1]),
                            power_list              : organizeSensorInfoData?.GPUTDP[0]?.TDP
                        }, {
                            temp                    : this.CheckValue(gpu1_temp_value),
                            hot_spot_temp           : this.CheckValue(gpu1_hot_spot_temp_value),
                            clock                   : this.CheckValue(gpu1_clock_value),
                            power                   : this.CheckValue(gpu1_power_value),
                            fan                     : this.CheckValue(gpu1_fan_value),
                            d3d_usage               : this.CheckValue(gpu1_d3d_usage_value),
                            total_usage             : this.CheckValue(gpu1_total_usage_value),
                            usage_str               : this.CheckValue(gpu1_d3d_usage_value) + '%/' + this.CheckValue(gpu1_total_usage_value) + '%',
                            mem_usage               : this.CheckValue(gpu1_mem_usage_value),
                            mem_usage_mb            : gpu1_mem_usage_mb === "0M/0M" ? null : gpu1_mem_usage_mb,
                            mem_clock               : this.CheckValue(gpu1_mem_clock_value),
                            mem_temp                : this.CheckValue(gpu1_mem_temp_value),
                            mem_size                : GPU1Data?.VideoMemoryNumber,
                            name                    : gpuName1,
                            shadres                 : Number(GPU1Data?.NumberOfUnifiedShaders) || Number(GPU1Data?.['NumberOfALUs(cores)']),
                            videobus                : GPU1Data?.VideoBus,
                            voltage                 : this.CheckValue(gpu1_voltage[1]),
                            thermal_hotspot         : this.CheckValue(gpu1_hotspot_thermal_limit[1]),
                            thermal_memory          : this.CheckValue(gpu1_memory_thermal_limit[1]),
                            pl_thermal              : this.CheckValue(gpu1_performance_limit[1]),
                            pl_reliability_voltage  : this.CheckValue(gpu1_performance_limit1[1]),
                            pl_max_operating_voltage: this.CheckValue(gpu1_performance_limit2[1]),
                            power_list              : organizeSensorInfoData?.GPUTDP[1]?.TDP
                        }, {
                            temp                    : this.CheckValue(gpu2_temp_value),
                            hot_spot_temp           : this.CheckValue(gpu2_hot_spot_temp_value),
                            clock                   : this.CheckValue(gpu2_clock_value),
                            power                   : this.CheckValue(gpu2_power_value),
                            fan                     : this.CheckValue(gpu2_fan_value),
                            d3d_usage               : this.CheckValue(gpu2_d3d_usage_value),
                            total_usage             : this.CheckValue(gpu2_total_usage_value),
                            usage_str               : this.CheckValue(gpu2_d3d_usage_value) + '%/' + this.CheckValue(gpu2_total_usage_value) + '%',
                            mem_usage               : this.CheckValue(gpu2_mem_usage_value),
                            mem_usage_mb            : gpu2_mem_usage_mb === "0M/0M" ? null : gpu2_mem_usage_mb,
                            mem_clock               : this.CheckValue(gpu2_mem_clock_value),
                            mem_temp                : this.CheckValue(gpu2_mem_temp_value),
                            mem_size                : GPU2Data?.VideoMemoryNumber,
                            name                    : gpuName2,
                            shadres                 : Number(GPU2Data?.NumberOfUnifiedShaders) || Number(GPU2Data?.['NumberOfALUs(cores)']),
                            videobus                : GPU2Data?.VideoBus,
                            voltage                 : this.CheckValue(gpu2_voltage[1]),
                            thermal_hotspot         : this.CheckValue(gpu2_hotspot_thermal_limit[1]),
                            thermal_memory          : this.CheckValue(gpu2_memory_thermal_limit[1]),
                            pl_thermal              : this.CheckValue(gpu2_performance_limit[1]),
                            pl_reliability_voltage  : this.CheckValue(gpu2_performance_limit1[1]),
                            pl_max_operating_voltage: this.CheckValue(gpu2_performance_limit2[1]),
                            power_list              : organizeSensorInfoData?.GPUTDP[2]?.TDP
                        }
                    ],
                    mainboard: {
                        temp   : this.checkValueWithLimit(mainboard_temp_value),
                        voltage: this.CheckValue(mainboard_voltage[1])
                    },
                    drive: {
                        temp         : drive_temp_list[drive_index],
                        sys_temp     : this.CheckValue(drive_temp[1]),
                        usage        : this.CheckValue(drive_usage[1]),
                        temp_list    : drive_list,
                        usage_list   : drive_usage_list,
                        temp_list_all: drive_temp_list
                    },
                    memory   : {
                        name    : MemoryData && MemoryData.SubNode ? MemoryData.SubNode[0].ModuleManufacturer + ' ' + (MemoryData.SubNode[0].MemoryType).split(' ')[0] : "null",
                        temp    : this.CheckValue(memory_temp_value),
                        usage   : this.CheckValue(memory_usage_value),
                        usage_mb: memory_used_mb[1],
                        size    : MemoryData && MemoryData.Property ? Number(MemoryData.Property['TotalMemorySize[MB]']) : 0,
                        clock   : Number((memory_clock[1] as  any) * 2),
                        channel : MemoryData && MemoryData?.Property ? Number(MemoryData?.Property.MemoryChannelsActive) : 0,
                        tcas    : memory_tcas[1],
                        trcd    : memory_trcd[1],
                        trp     : memory_trp[1],
                        tras    : memory_tras[1],
                        voltage : memory_voltage,
                        ddr5_temp: ddr5_temperature,
                        ddr5_voltage:ddr5_voltage
                    },
                    network: {
                        download: network_info[0],
                        upload  : network_info[1]
                    },
                    battery: {
                        chargelevel: this.CheckValue(battery_chargelevel[1]),
                        voltage    : this.CheckValue(battery_voltage[1])
                    },
                    whea: this.CheckValue(windows_hardware_errors[1])
                }
                let gpu_names = [gpuName0, gpuName1, gpuName2];
                const isAllNull = gpu_names.every(item => item === null);
                if (!isAllNull) {
                    gpu_names.forEach((name, index) => {
                        if (!name) {data_arr.gpu_list[index] = [];}
                    });
                    let nonEmptyObjects = data_arr.gpu_list.filter(item => item && typeof item === 'object' && Object.keys(item).length > 0);
                    if (nonEmptyObjects.length === 1) {
                        gpu_index = data_arr.gpu_list.findIndex(item => item && typeof item === 'object' && Object.keys(item).length > 0);
                    }
                    data_arr.gpu = data_arr.gpu_list[gpu_index]
                } else {
                    data_arr.gpu_list[1] = [];
                    data_arr.gpu_list[2] = [];
                    data_arr.gpu = data_arr.gpu_list[0]
                }
                // console.log(data_arr)
                window.localStorage.setItem('bg_sensor_data', JSON.stringify(data_arr))
                // Broadcast Channel API
                bc.postMessage(data_arr); // 发送数据
                /** 其他窗口接收
                 * const bc = new BroadcastChannel('bg_sensor_data');
                 * bc.onmessage = function(event) {
                 *     console.log(event.data);
                 * };
                 */
            }
        }, refresh_time)
    }
    //每秒获取收藏的传感器数据存bg,便于页面使用  setInterval setTimeout
    StarSensorDataProcess (refresh_time) {
        setInterval_starSensor_list = setInterval(() => {
            let SensorList:any = []
            let Star_Sensor_Data:any = []
            let sensor_collected:any = window.localStorage.getItem('collected_sensor_list')
            if (sensor_collected) {
                sensor_collected = JSON.parse(sensor_collected)
            }else{
                sensor_collected = []
            }
            let SensorInfoStr:string = ''
            try {
                SensorInfoStr = gamepp.hardware.getSensorInfo.sync()
            } catch {
                SensorInfoStr = JSON.stringify({})
            }
            const booleanArr = ['Critical Temperature', 'Thermal Throttling', 'Power Limit Exceeded', 'IA: ', 'GT: ', 'RING:', 'Drive Failure', 'Drive Warning', 'Chassis Intrusion', 'Performance Limit']
            const SensorInfo = JSON.parse(SensorInfoStr);
            // console.warn('传感器数据：：',SensorInfo);

            const SensorInfoKeys = Object.keys(SensorInfo)
            let SensorInfoTidyUp = this.SensorAddAverageData(SensorInfo, SensorInfoKeys)
            const regex = /^(.*?)(:|\s|$)/ // 匹配第一个冒号或空格之前的内容
            for (let i = 0; i < SensorInfoKeys.length; i++) {
                const SensorInfoKey = SensorInfoKeys[i]
                let Datas = SensorInfoTidyUp[SensorInfoKey]
                const SensorObj:any = {}
                let SensorListText = ''
                SensorObj.name = SensorInfoKey
                const match = SensorInfoKey.match(regex)
                SensorObj.type = match ? match[1].trim() : ''
                SensorObj.Sensoritem = []
                // if (!Datas) continue
                if (!Datas) { Datas = [{ Null: '', name: '' }] }
                for (let j = 0; j < Datas.length; j++) {
                    const Key = Object.keys(Datas[j])[0]
                    const Data = Datas[j][Key]
                    const SensoritemObj:any = {}
                    const ProcessKey:any = ProcessSensorUnit(Key, Data.type)
                    if (!SensorListText.includes(ProcessKey.UnitText)) {
                        SensorListText += ProcessKey.UnitText
                    }
                    if (booleanArr.find(item => Key.includes(item))) {
                        let booleanValue = 'No'
                    if (Number(Data.value)) { booleanValue = 'Yes' }
                        SensoritemObj.value = booleanValue
                    } else {
                    if (Data.value) { SensoritemObj.value = Number(Data.value).toFixed(ProcessKey.ToFixed) } else { SensoritemObj.value = 'Null' }
                    }
                    SensoritemObj.name = Key
                    SensoritemObj.unit = ProcessKey.DataUnit
                    SensorObj.Sensoritem.push(SensoritemObj)
                }
                SensorObj.UnitText = SensorListText
                SensorList.push(SensorObj)
            }
            if (sensor_collected.length > 0) {
                sensor_collected.forEach((item)=>{
                    try {
                        const obj = SensorList[item.OutIndex]['Sensoritem'][item.InnerIndex]
                        Star_Sensor_Data.push(obj);
                    }catch(e){}
                })
                window.localStorage.setItem('star_sensor_data',JSON.stringify(Star_Sensor_Data))
            }else{
                window.localStorage.setItem('star_sensor_data',JSON.stringify([]))
            }
        },refresh_time)
        function ProcessSensorUnit (Key, DataType) {
            const condition_MB = ['Memory Dynamic', 'Read Total', 'Write Total', 'Memory Allocated', 'Memory Dedicated', 'Total DL', 'Total UP', 'Memory Committed', 'Memory Available', 'Memory Used','GPU Memory Usage']
            const condition_GB = ['Total Host Writes', 'Total Host Reads']
            const condition_T = ['Tcas', 'Trcd', 'Trp', 'Tras', 'Trc', 'Trfc', 'Command Rate']
            const condition_KBs = ['Current DL rate', 'Current UP rate']
            let DataUnit = '';
            let ToFixed = 0;
            let UnitText = ''
            if (DataType === 'temperature') {
                DataUnit = ' ℃'
                UnitText = ' 温度'
            } else if (DataType === 'voltage') {
                DataUnit = ' V'
                ToFixed = 3
                UnitText = ' 电压'
            } else if (DataType === 'fan') {
                DataUnit = ' RPM'
                UnitText = ' 转速'
            } else if (DataType === 'power') {
                DataUnit = ' W'
                ToFixed = 3
                UnitText = ' 功耗'
            } else if (DataType === 'clock') {
                DataUnit = ' MHz'
                UnitText = ' 频率'
            } else if (DataType === 'usage') {
                DataUnit = ' %'
                ToFixed = 1
                UnitText = ' 占用'
            } else if (DataType === 'other') {
                if (Key.includes('Ratio')) {
                DataUnit = ' x'
                ToFixed = 1
                } else if (condition_MB.find(item => Key.includes(item))) {
                DataUnit = ' MB'
                } else if (['Read Rate', 'Write Rate'].includes(Key)) {
                DataUnit = ' MB/s'
                } else if (Key.match(new RegExp('^GPU Fan[0-9]$')) || Key === 'GPU Fan PWM') {
                DataUnit = ' %'
                } else if (Key.includes('[% of TDP]') || Key.includes('Memory Load') || Key.includes('Page File Usage')) {
                DataUnit = ' %'
                ToFixed = 1
                UnitText = ' 占用'
                } else if (Key === 'PCIe Link Speed') {
                DataUnit = ' GT/s'
                ToFixed = 1
                } else if (condition_T.find(item => Key.includes(item))) {
                DataUnit = ' T'
                } else if (condition_KBs.find(item => Key.includes(item))) {
                DataUnit = ' KB/s'
                ToFixed = 3
                } else if (condition_GB.find(item => Key.includes(item))) {
                DataUnit = ' GB'
                }
            }
            const TypeData:any = {}
            TypeData.DataUnit = DataUnit
            TypeData.ToFixed = ToFixed
            TypeData.UnitText = UnitText
            return TypeData
        }
    }
    CheckValue(value) {
        return value !== '' ? value : null;
    }

    checkValueWithLimit (value) {
        let result = this.CheckValue(value);

        // 如果结果是数字且大于100，则限制为100
        if (typeof result === 'number' && result > 100) {
            result = 100;
        }

        return result;
    }

    GetHardwareDetails(type, name) {
        if (!name || !this.GlobalBaseJsonInfo[type] || !this.GlobalBaseJsonInfo[type].SubNode) return null;
        const node = this.GlobalBaseJsonInfo[type].SubNode.find(node => node.VideoChipset === name || name.includes(node.VideoChipset));
        return node || null;
    }

    findValueByKeyAndType (data:Object, key:string, keySubstring:any = false, type) {
        let ToFixed = 0
        if (type.includes('voltage')) {
            ToFixed = 3
        }
        let key1:any = false, key2:any = false;
        if (key === 'Mainboard') {
            const sift = JSON.parse(JSON.stringify(data)) //深拷贝
            const keywords = ['CPU', 'GPU', 'Memory', 'S.M.A.R.T', 'System:', 'Windows Hardware', 'Drive', 'Network', 'DIMM']
            const filteredData = {}
            for (const key1 in sift) {
                let shouldKeep = true
                for (const keyword of keywords) {
                    if (data[key1] === null || key1.toLowerCase().includes(keyword.toLowerCase())) {
                        shouldKeep = false
                        break
                    }
                }
                if (shouldKeep) {
                    filteredData[key1] = sift[key1]
                }
            }
            data = filteredData
            if (type === 'temperature' && Object.keys(data).some(key => key.includes('Maxsun'))) {keySubstring = 'Temp5'}// 铭瑄主板温度异常,厂商要求取 Temp5
            if (type === 'temperature' && Object.keys(data).some(key => key.includes('BIOSTAR Group B850MT-E PRO'))) {keySubstring = 'Temperature 3'}// 映泰主板温度异常,厂商要求这个主板取 Temp3
            if (type === 'temperature' && Object.keys(data).some(key => key.includes('BIOSTAR Group B850MT2-E DJ'))) {keySubstring = 'Temperature 2'}// 映泰主板温度异常,厂商要求这个主板取 Temp2
            key = Object.keys(filteredData)[0]
            key1 = Object.keys(filteredData)[1]
            key2 = Object.keys(filteredData)[2]
        } else if (key === 'Network') {
            let totalDLRate = 0, totalUPRate = 0;
            for (const key1 in data) {
                if (key1.startsWith("Network")) {
                    const networkData = data[key1] || [];
                    networkData.forEach(item => {
                        if (item["Current DL rate"]) {
                            totalDLRate += parseFloat(item["Current DL rate"].value);
                        }
                        if (item["Current UP rate"]) {
                            totalUPRate += parseFloat(item["Current UP rate"].value);
                        }
                    });
                }
            }
            return [Number(totalDLRate.toFixed(0)), Number(totalUPRate.toFixed(0))];
        }
        //除开主板与NetWork都在此处进行查找
        for (const [parentKey, arr] of Object.entries(data)) {
            // console.warn('ArrArrArr',arr);
            if (data[parentKey] && (parentKey.includes(key) || (key1 && parentKey.includes(key1))|| (key2 && parentKey.includes(key2)))) {
                for (const obj of arr) {
                    const [itemKey, itemValue] = Object.entries(obj)[0] as [any,any]
                    // const [itemValue,itemKey] = Object.entries(obj)[0]
                    if (keySubstring) {
                        if (keySubstring.includes('|')) {
                            const substrings = keySubstring.split('|');
                            for (const substring of substrings) {
                                if (itemKey.includes(substring) && type === (itemValue.type)) {
                                    return [itemKey, Number(parseFloat(itemValue.value).toFixed(ToFixed))];
                                }
                            }
                        } else {
                            if (itemKey.includes(keySubstring) && type.includes(itemValue.type)) {
                                return [itemKey, Number(parseFloat((itemValue.value)).toFixed(ToFixed))]
                            }
                        }
                    } else {
                        if (type.includes(itemValue.type)) {
                            return [itemKey, Number(parseFloat((itemValue.value)).toFixed(ToFixed))]
                        }
                    }
                }
            }
        }
        return ['', '']
    }

    organizeSensorInfo (SensorInfos) {
        let CPUData:any = {}
        CPUData.Core = { Clock: [], Temp: [],Load:[] }
        CPUData.EffectiveClock = {}
        CPUData.Thread = {}
        CPUData.PLR = []
        CPUData.GPUTDP = null
        let GPU = {}
        let AllMainKeys = Object.keys(SensorInfos)
        let NeedPLR = ['IA: PROCHOT', 'IA: Thermal Event', 'IA: VR Thermal Alert', 'IA: VR TDC', 'IA: Electrical Design Point/Other (ICCmax,PL4,SVID,DDR RAPL)', 'IA: Package-Level RAPL/PBM PL1', 'IA: VmaxStress', 'Thermal Limit', 'CPU PPT Limit', 'CPU TDC Limit', 'CPU EDC Limit', 'Thermal Throttling (HTC)', 'Thermal Throttling (PROCHOT CPU)', 'Thermal Throttling (PROCHOT EXT)']
        for (let i = 0; i < AllMainKeys.length; i++) {
            let mainKey = AllMainKeys[i]
            let upperMainKey = mainKey.toUpperCase()
            let itemValue = SensorInfos[mainKey]
            if (upperMainKey.startsWith('CPU [#0]')) {
                if (upperMainKey.includes('PERFORMANCE LIMIT REASONS') || upperMainKey.includes('ENHANCED')) {
                    itemValue.filter(itemDetail => NeedPLR.includes(Object.keys(itemDetail)[0])).map(itemDetail => {
                        const infoKey = Object.keys(itemDetail)[0];
                        let value = parseInt(itemDetail[infoKey].value)
                        const UNIT = itemDetail[infoKey].type
                        if (value) {
                            if (UNIT !== 'usage') {
                                CPUData.PLR.push({ [infoKey]: value });
                            } else if (value >= 99) {
                                CPUData.PLR.push({ [infoKey]: value });
                            }
                        }
                    });
                } else if (upperMainKey.includes('DTS')) {
                    itemValue.forEach(function (itemDetail) {
                        let infoKey = Object.keys(itemDetail)[0]
                        if (infoKey.includes('Thermal Throttling') || infoKey === 'Package/Ring Critical Temperature' || infoKey === 'Package/Ring Thermal Throttling') {
                            let value = parseInt(itemDetail[infoKey].value)
                            if (value) {
                                CPUData.PLR.push({ [infoKey]: value });
                            }
                        }
                    })
                }
                itemValue.forEach(function (itemDetail) {
                    const infoKey = Object.keys(itemDetail)[0]
                    const itemInfo = itemDetail[infoKey]
                    const infoType = itemInfo.type.toUpperCase()
                    const infoValue = itemInfo.value
                    const upperInfoKey = infoKey.toUpperCase()
                    if (infoType === 'CLOCK') {
                        if (
                          upperInfoKey.match(new RegExp('^CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]CLOCK[.\n]*')) ||
                          upperInfoKey.match(new RegExp('^P-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]CLOCK [(]PERF #1[)][.\n]*')) ||
                          upperInfoKey.match(new RegExp('^E-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]CLOCK [(]PERF #1[)][.\n]*')) ||
                          upperInfoKey.match(new RegExp('^P-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]CLOCK[.\n]*')) ||
                          upperInfoKey.match(new RegExp('^E-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]CLOCK[.\n]*'))
                        ) {
                            CPUData.Core.Clock.push(Number(parseFloat(infoValue).toFixed(0)))
                        }
                        if (
                          upperInfoKey.match(new RegExp('^CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]T[0-9][ \f\r\t\n]EFFECTIVE CLOCK$'))
                        ) {
                            const clock = Number(parseFloat(infoValue).toFixed(0))
                            const infoKeyItems = upperInfoKey.split(' ')
                            const EffectiveClock = CPUData.EffectiveClock
                            if (EffectiveClock.hasOwnProperty(infoKeyItems[1])) {
                                EffectiveClock[infoKeyItems[1]][infoKeyItems[2]] = clock
                            } else {
                                let Detail = {}
                                Detail[infoKeyItems[2]] = clock
                                CPUData.EffectiveClock[infoKeyItems[1]] = Detail
                            }
                        }
                    } else if (infoType === 'USAGE') {
                        // 占用
                        if (
                          upperInfoKey.match(new RegExp('^CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]T[0-9][ \f\r\t\n]USAGE$')) ||
                          upperInfoKey.match(new RegExp('^P-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]T[0-9][ \f\r\t\n]USAGE$')) ||
                          upperInfoKey.match(new RegExp('^E-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]T[0-9][ \f\r\t\n]USAGE$'))
                        ) {
                            // 超线程技术
                            const usage = Number(parseFloat(infoValue).toFixed(0))
                            const infoKeyItems = upperInfoKey.split(' ')
                            const Thread = CPUData.Thread
                            if (Thread.hasOwnProperty(infoKeyItems[1])) {
                                Thread[infoKeyItems[1]][infoKeyItems[2]] = usage
                            } else {
                                var Detail = {}
                                Detail[infoKeyItems[2]] = usage
                                CPUData.Thread[infoKeyItems[1]] = Detail
                            }
                        } else if (
                          upperInfoKey.match(new RegExp('^CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]USAGE$'))||
                          upperInfoKey.match(new RegExp('^P-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]USAGE$')) ||
                          upperInfoKey.match(new RegExp('^E-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]USAGE$'))
                        ) {
                            // 没有超线程技术
                            const usage = Number(parseFloat(infoValue).toFixed(0))
                            const infoKeyItems = upperInfoKey.split(' ')
                            const Thread = CPUData.Thread
                            if (Thread.hasOwnProperty(infoKeyItems[1])) {
                                Thread[infoKeyItems[1]].T0 = usage
                            } else {
                                const Detail:any = {}
                                Detail.T0 = usage
                                CPUData.Thread[infoKeyItems[1]] = Detail
                            }
                        }
                    } else if (infoType === 'TEMPERATURE') {
                        if (upperInfoKey.match(new RegExp('^CORE[0-9]'))) {
                            CPUData.Core.Temp.push(Number(parseFloat(infoValue).toFixed(0)))
                        } else if (upperInfoKey.match(new RegExp('^CORE[ \f\r\t\n][0-9]')) || upperInfoKey.match(new RegExp('^P-CORE[ \f\r\t\n][0-9]')) || upperInfoKey.match(new RegExp('^E-CORE[ \f\r\t\n][0-9]'))) {
                            if (infoType === 'TEMPERATURE') {
                                if (upperInfoKey.indexOf('DISTANCE') === -1) {
                                    CPUData.Core.Temp.push(Number(parseFloat(infoValue).toFixed(0)))
                                }
                            }
                        }
                    }
                })
            } else if (upperMainKey.startsWith('GPU')) {
                let strIdx = upperMainKey.indexOf('#') + 1
                let GPUIDX = upperMainKey.substring(strIdx, strIdx + 1)
                let GPUData:any = {}
                GPUData.TDP = []
                let IsArrBool1 = Array.isArray(itemValue)
                if (itemValue && IsArrBool1) {
                    itemValue.forEach(function (itemDetail) {
                        const infoKey = Object.keys(itemDetail)[0]
                        const itemInfo = itemDetail[infoKey]
                        const infoType = itemInfo.type.toUpperCase()
                        const infoValue = itemInfo.value
                        if (infoType === 'POWER') {
                            if (infoKey !== 'GPU Power') {
                                const tdp = {}
                                tdp[infoKey] = Number(parseFloat(infoValue).toFixed(3))
                                GPUData.TDP.push(tdp)
                            }
                        }
                    })
                }
                GPU[GPUIDX] = GPUData
            }
        }
        CPUData.GPUTDP = GPU
        let Count = Object.keys(CPUData.Core.Clock)
        for (let i = 0; i < Count.length; i++) {
            const coreId = i.toString();
            const T0 = CPUData.Thread[coreId].T0;
            const T1 = CPUData.Thread[coreId].T1;
            if (T0 && T1 !== undefined) {
                CPUData.Core.Load.push(Math.round((T0 + T1) / 2))
            } else {
                CPUData.Core.Load.push(T0)
            }
        }
        return CPUData
    }

    SensorInfoDataProcess (isDef, groupIndex, itemIndex, isUnit, SensorInfo , outKey , inKey, Unit='') {
        try{
            if(!outKey || outKey === '')
            {
                let ToFixed = 0
                // console.warn('SensorInfo',SensorInfo);

                const obj:any = Object.keys(SensorInfo)
                // console.warn('obj',obj);

                const groupKey = obj[groupIndex]

                let results:any = ''
                if (SensorInfo[groupKey]) {
                    const ChoseData:any = SensorInfo[groupKey][itemIndex]
                    if (!ChoseData) {
                        return results
                    }
                    const [itemKey, itemValue] = Object.entries(ChoseData)[0] as [any,any]
                    if (itemValue.type === 'voltage') {
                        ToFixed = 3
                    }
                    results = Number(parseFloat(itemValue.value).toFixed(ToFixed))
                }

                return results
            }
            else
            {
              let SensorOut  = SensorInfo[outKey]
              let ToFixed = 0
              for(let i = 0 ; i < SensorOut.length  ; i++){
                let keyName = Object.keys(SensorOut[i])[0]
                if( keyName === inKey)
                {
                    if (Unit && this.getTypeFromUnit(Unit) !== SensorOut[i][keyName]['type']) continue
                    if (SensorOut[i][keyName]['type'] && SensorOut[i][keyName]['type'] === 'voltage') {
                        ToFixed = 3
                    }
                    return Number(parseFloat(SensorOut[i][keyName]['value']).toFixed(ToFixed))
                }
              }
            }
        }
        catch
        {
            return 0
        }
    }

    getTypeFromUnit (unit) {
        if (unit.includes('℃')) {
            return 'temperature'
        } else if (unit.includes('V')) {
            return 'voltage'
        } else if (unit.includes('RPM')) {
            return 'fan'
        } else if (unit.includes('W')) {
            return 'power'
        } else if (unit.includes('MHz')) {
            return 'clock'
        } else if (unit.includes('%')) {
            return 'usage'
        } else {
            return 'other'
        }
    }

    /**
     * getSensorIndexFromName
     */
    getSensorIndexFromName (data, key, keySubstring, type) {
        if (key === 'Mainboard') {
            const keywords = ['CPU', 'GPU', 'Memory', 'S.M.A.R.T', 'System:', 'Windows Hardware', 'Drive', 'Network', 'DIMM']
            const filteredData = {}
            for (const key1 in data) {
                let shouldKeep = true
                for (const keyword of keywords) {
                    if (data[key1] === null || key1.toLowerCase().includes(keyword.toLowerCase())) {
                        shouldKeep = false
                        break
                    }
                }
                if (!shouldKeep) {
                    filteredData[key1] = data[key1]
                } else {
                    filteredData['Mainboard ' + key1] = data[key1]
                }
            }
            data = filteredData
        }
        const keys = Object.keys(data);
        for (let i = 0; i < keys.length; i++) {
            const parentKey = keys[i];
            const arr = data[parentKey];
            if (data[parentKey] && (parentKey.includes(key))) {
                for (const [j, obj] of arr.entries()) {
                    const [itemKey, itemValue] = Object.entries(obj)[0] as [any,any];
                    if (itemKey.includes(keySubstring) && type.includes(itemValue.type)) {
                        return [i, j];
                    }
                }
            }
        }
        return null
    }

    //游戏内提示信息窗口
    async InGame_Tips_Information(WindowName, type, svalue, time) {
        //判断是否开启通知
        if (gamepp.setting.getInteger.sync(COMMANDID.CM_SHOW_NOTIFY_UI_SWITH) === 1) {
            if (await gamepp.isDesktopMode.promise()) return false;
            // await gamepp.setting.setBool2.promise('window', WindowName, false);
            let SendData = {};
            SendData['type'] = type;
            SendData['language'] = gamepp.setting.getString.sync(COMMANDID.CM_LANGUAGE_TYPE);
            SendData['svalue'] = svalue;
            SendData['time'] = time;
            // await this.IsReadyShowSendPage('window', WindowName, false);
            if (type === 30) {
                setTimeout(async () => {
                    // await gamepp.webapp.sendInternalAppEvent.promise(WindowName, SendData);
                    await window.localStorage.setItem('ingame_tips', JSON.stringify(SendData));
                    await gamepp.webapp.windows.show.promise(WindowName);
                }, this.LoadingTime);
                this.LoadingTime += 200;
            } else {
                // await gamepp.webapp.sendInternalAppEvent.promise(WindowName, SendData);
                await window.localStorage.setItem('ingame_tips', JSON.stringify(SendData));
                await gamepp.webapp.windows.show.promise(WindowName);
            }
        }
    }

    //监听配置信息改变
    BGonConfigChanged () {
        gamepp.setting.onConfigChanged.addEventListener((type, id, value) =>
            {
                if (id === COMMANDID.CM_ENABLE_EFFECT) {
                    const _hotkey_OpenOrCloseInGameQuality = gamepp.setting.getString.sync(COMMANDID.CM_HOTKEY_PRESENT_EFFECT_SWITCH_INFO);
                    if (value === 0) {
                        this.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 10, _hotkey_OpenOrCloseInGameQuality, 3);
                    } else if (value === 1) {
                        this.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 9, _hotkey_OpenOrCloseInGameQuality, 3);
                    }
                } else if (id === 450) {
                    //监听刷新时间修改
                    clearInterval(setInterval_chooseSensor_list)
                    gameclient.RefreshTime = value;
                    this.BgSensorDataProcess(value)
                    this.StarSensorDataProcess(value)
                }
            }
        )
    }

    /**
     *初始化事件
     */
    async InitAppEvent() {
        console.log('清理性能统计数据');
        let AppDataDir = await gamepp.getAppDataDir.promise();
        let GPP5DatabaseDir = gamepp.setting.getString.sync(COMMANDID.CM_STATISTICS_SAVE_ADDRESS);
        if (GPP5DatabaseDir == '') GPP5DatabaseDir = AppDataDir + '\\common\\Data'
        let GPP5DatabaseId = await gamepp.database.open.promise(GPP5DatabaseDir+'\\GamePP5.dll');


        let status = await gamepp.database.exists.promise(GPP5DatabaseId, 'GamePP_BaseInfo');
        if (!status) {
            await gamepp.database.create.promise(GPP5DatabaseId, "GamePP_BaseInfo", '("id"INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,"gamepath"TEXT,"starsensorInfo"TEXT,"processpath"TEXT,"exec_path"TEXT,"starttime"INTEGER,"endtime"INTEGER,"gametime"INTEGER,"icon"TEXT,"city"TEXT,"wea"TEXT,"wea_img"TEXT,"tem"TEXT,"hd_info"TEXT,"data_type"TEXT,"refresh_time"INTEGER)');
        }

        //新增endtime字段
        let queryField_endtime = await gamepp.database.queryField.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'endtime');
        if (!queryField_endtime) {
            await gamepp.database.alter.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'endtime INTEGER');
        }

        //新增硬件信息数据绑定字段
        let queryField_hd_info = await gamepp.database.queryField.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'hd_info');
        if (!queryField_hd_info) {
            await gamepp.database.alter.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'hd_info TEXT');
        }
        //新增城市字段
        let queryField_province = await gamepp.database.queryField.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'province');
        if (!queryField_province) {
            await gamepp.database.alter.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'province TEXT');
        }
        //新增数据类型绑定字段
        let queryField_data_type = await gamepp.database.queryField.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'data_type');
        if (!queryField_data_type) {
            await gamepp.database.alter.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'data_type TEXT');
        }
        //新增刷新时间绑定字段
        let queryField_refresh_time = await gamepp.database.queryField.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'refresh_time');
        if (!queryField_refresh_time) {
            await gamepp.database.alter.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'refresh_time INTEGER');
        }


        //新增页面硬件最大最小平均
        let queryField_hdLis = await gamepp.database.queryField.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'hd_list_data');
        if (!queryField_hdLis) {
            await gamepp.database.alter.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'hd_list_data TEXT');
        }

        //新增游戏分辨率字段
        let queryField_resolutions = await gamepp.database.queryField.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'resolutions');
        if (!queryField_resolutions) {
            await gamepp.database.alter.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'resolutions TEXT');
        }

        //新增游戏DX版本字段dx_version
        let queryField_dx_version = await gamepp.database.queryField.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'dx_version');
        if (!queryField_dx_version) {
            await gamepp.database.alter.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'dx_version TEXT');
        }

        //新增是否检查表字段
        let queryField_is_check = await gamepp.database.queryField.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'is_check');
        if (!queryField_is_check) {
            await gamepp.database.alter.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'is_check INTEGER');
        }

        //新增游戏路径字段(exec_path)
        let queryField_exec_path = await gamepp.database.queryField.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'exec_path');
        if (!queryField_exec_path) {
            await gamepp.database.alter.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'exec_path INTEGER');
        }

        DataBaseId.GPP5DatabaseId = GPP5DatabaseId;
        let GPPBase_table_exists = await gamepp.database.exists.promise(GPP5DatabaseId, 'GamePP_BaseInfo');
        if (GPPBase_table_exists) {
            const ListData = await gamepp.database.query.promise(GPP5DatabaseId, "GamePP_BaseInfo", "*", "order by id asc");
            const PERFORMANCE_CLEAR_TIME = await gamepp.setting.getInteger.promise(COMMANDID.CM_PERFORMANCE_CLEAR_TIME);
            //删除选择不保留的数据
            if (ListData.length > PERFORMANCE_CLEAR_TIME) {
                for (let i = 0; i < (ListData.length - PERFORMANCE_CLEAR_TIME); i++) {
                    let TableExists = await gamepp.database.exists.promise(GPP5DatabaseId, "" + ListData[i]['starttime'] + "");
                    if (TableExists) {
                        await gamepp.database.delete.promise(GPP5DatabaseId, "GamePP_BaseInfo", "id = " + ListData[i]['id'] + "");//列表数据
                        await gamepp.database.drop.promise(GPP5DatabaseId, "\'" + ListData[i]['starttime'] + "\'", "");//详细表
                    }
                }
            }

            //删除中途软件崩溃数据(EndTime为空)
            for (let j = 0; j < ListData.length; j++) {
                if (ListData[j]['is_check'] !== 1) {
                    if (ListData[j]['endtime'] === null) {
                        await gamepp.database.delete.promise(GPP5DatabaseId, "GamePP_BaseInfo", "id = " + ListData[j]['id'] + "");//列表数据
                        await gamepp.database.drop.promise(GPP5DatabaseId, "\'" + ListData[j]['starttime'] + "\'", "");//详细表
                    }

                    //删除详细信息为空数据
                    let dataLen = await gamepp.database.query.promise(GPP5DatabaseId, "'" + ListData[j]['starttime'] + "'", "COUNT(id)");
                    if (dataLen.length !== 0) {
                        let list_length = dataLen[0]['COUNT(id)'];
                        if (list_length === 0) {
                            await gamepp.database.delete.promise(GPP5DatabaseId, "GamePP_BaseInfo", "starttime = " + ListData[j]['starttime'] + "");//列表数据
                            await gamepp.database.drop.promise(GPP5DatabaseId, "\'" + ListData[j]['starttime'] + "\'", "");//详细表
                        }
                    }

                    //设置当前表已经检查
                    await gamepp.database.update.promise(GPP5DatabaseId, "GamePP_BaseInfo", ['is_check="' + 1 + '"'], 'id = "' + ListData[j]['id'] + '"');
                }
            }

        }

        //保留压力测试最近10条数据
        let StressNewDatabaseDir = gamepp.setting.getString.sync(COMMANDID.CM_STATISTICS_SAVE_ADDRESS);
        if (StressNewDatabaseDir == '') StressNewDatabaseDir = AppDataDir + '\\common\\Data'
        let StressNewDatabaseId = await gamepp.database.open.promise(StressNewDatabaseDir + '\\GamePPStressNew.dll');
        let list_table_exists = await gamepp.database.exists.promise(StressNewDatabaseId, 'list');
        if (list_table_exists) {
            //新增endtime字段
            let queryField_gpu_loop_data = await gamepp.database.queryField.promise(StressNewDatabaseId, 'list', 'gpu_loop_data');
            if (!queryField_gpu_loop_data) {
                await gamepp.database.alter.promise(StressNewDatabaseId, 'list', 'gpu_loop_data TEXT');
            }

            const ListData = await gamepp.database.query.promise(StressNewDatabaseId, "list", "*", "order by id asc");
            //删除选择不保留的数据
            if (ListData.length > 10) {
                for (let i = 0; i < (ListData.length - 10); i++) {
                    let TableExists = await gamepp.database.exists.promise(StressNewDatabaseId, "" + ListData[i]['starttime'] + "");
                    if (TableExists) {
                        await gamepp.database.drop.promise(StressNewDatabaseId, "\'" + ListData[i]['starttime'] + "\'", "");//详细表
                    }
                    try {
                        await gamepp.database.delete.promise(StressNewDatabaseId, "list", "id = " + ListData[i]['id'] + "");//列表数据
                    } catch {
                    }
                }
            }

            //新增综合测试区分CPU,GPU数据
            let queryField = await gamepp.database.queryField.promise(StressNewDatabaseId, 'list', 'comprehensive_data');
            if (!queryField) {
                await gamepp.database.alter.promise(StressNewDatabaseId, 'list', 'comprehensive_data TEXT');
            }
        }
        await gamepp.database.close.promise(StressNewDatabaseId)

        //传感器新增网络对象
        let chooseSensor_list_v1Str = window.localStorage.getItem('chooseSensor_list_v1')
        if (chooseSensor_list_v1Str) {
            let chooseSensor_list_temp = JSON.parse(chooseSensor_list_v1Str)
            if (!chooseSensor_list_temp.networkData) {
                chooseSensor_list_temp.networkData = [
                    { name: 'download', describe: '下载', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' KB/s' },
                    { name: 'upload', describe: '上传', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' KB/s' }
                ]
                window.localStorage.setItem('chooseSensor_list_v1', JSON.stringify(chooseSensor_list_temp))
            }
        }


        /*
         修复工具
         */
        await gamepp.repair.Initialize.promise();


        /*
         * 初始化加速模块SDK
         */
        if (gamepp.opti.isInitOptiSDK.sync() === false) {
            await gamepp.opti.initOptiSDK.promise();
        }


        /**
         * 打点
         */

            //(中文or英文)运行
        let LanStr = await gamepp.setting.getString.promise(COMMANDID.CM_LANGUAGE_TYPE);
        if (LanStr === 'CN') {
            await gamepp.utils.sendstatics.promise(110);
        } else {
            await gamepp.utils.sendstatics.promise(111);
        }

        //(启动or自启)程序
        let isStartedWithOs = await gamepp.isStartedWithOs.promise();
        let Id = isStartedWithOs === false ? 103 : 102;
        await gamepp.utils.sendstatics.promise(Id);

        //(升级or更新)程序
        let update_storage = window.localStorage.getItem('update_storage');
        if (update_storage !== null && update_storage !== 'null' && update_storage !== undefined) {
            let update_storage_object = JSON.parse(update_storage);
            if (update_storage_object['update'] === 1) {
                await gamepp.utils.sendstatics.promise(14);//更新打点
            }
            if (update_storage_object['upgrade'] === 1) {
                await gamepp.utils.sendstatics.promise(3);//升级打点
            }
        }

        //由于只保留OBS录像模式  其余模式启动主动切换为OBS
        let captureId = await gamepp.setting.getInteger.promise(COMMANDID.CM_VR_VIDEO_RELIVE_SWITCH);
        if (captureId !== 3) {
            await gamepp.setting.setInteger.promise(COMMANDID.CM_VR_VIDEO_RELIVE_SWITCH, 3);
        }

        //启动时修改localStorage 用于更新数据
        console.log('修改localStorage 用于更新 Benchmark Rank')
        let benchmark_str = window.localStorage.getItem('benchmark_data_v2');
        if (benchmark_str !== null && benchmark_str !== 'null' && benchmark_str !== undefined) {
            let benchmark_data = JSON.parse(benchmark_str);
            benchmark_data['need_update_rank'] = 1;
            window.localStorage.setItem('benchmark_data_v2', JSON.stringify(benchmark_data));
        }

        //删除部分localStorage
        //启动时删除硬件扫描标识(每次启动时候重新扫描硬件信息)
        window.localStorage.removeItem('HwIsScanned');
        window.localStorage.removeItem('gppToolboxlink');
        window.localStorage.removeItem('game_booster_begin_time');//加速状态
        await gamepp.opti.setValue.promise(73, 0)
        window.localStorage.removeItem('laboratoryStatus');
        window.localStorage.removeItem('danmu_ConnectionList');//弹幕多窗口连接列表
        window.localStorage.removeItem('GppConfig_url');//在线获取的链接
        window.localStorage.removeItem('chooseSensor_list_x');//使用新字段
        window.localStorage.removeItem('bg_sensor_data');
        window.localStorage.removeItem('star_sensor_data');
        window.localStorage.removeItem('game_run_disk')
        window.localStorage.removeItem('Not_Inject')
        window.localStorage.removeItem('GameMessageInGameShow')
        window.localStorage.removeItem('GameMessageExitShow')
        this.GetServerGppConfig();


        //获取主显示器信息
        let DisplayInfo = await gamepp.hardware.getDisplayCardInfo.promise();
        for (let i = 0; i < DisplayInfo.Count; i++) {
            if (DisplayInfo['Element'][i]['IsPrimaryScreen'] === 1) {
                window.localStorage.setItem('primary_screen', JSON.stringify(DisplayInfo['Element'][i]));
            }
        }

        // 获取主显卡信息
        let HighPerformanceGPUStr = await gamepp.getHighPerformanceGPU.promise();
        if (HighPerformanceGPUStr) {
            let HighPerformanceGPU = JSON.parse(HighPerformanceGPUStr);
            await window.localStorage.setItem('gpu_detect_list', JSON.stringify(HighPerformanceGPU));
        }

        let RefreshTime = await gamepp.setting.getInteger.promise(450);
        if (!([100, 300, 500, 1000, 2000, 3000, 5000].includes(RefreshTime))) {
            await gamepp.setting.setInteger.promise(450, 1000)
        }

        //创建异常关机数据库 GamePPShutdown
        let GPPDownDatabaseDir = gamepp.setting.getString.sync(COMMANDID.CM_STATISTICS_SAVE_ADDRESS);
        if (GPPDownDatabaseDir == '') GPPDownDatabaseDir = AppDataDir + '\\common\\Data'
        let GPPDownDatabaseId = await gamepp.database.open.promise(GPPDownDatabaseDir + '\\GamePPShutdown.dll');
        DataBaseId.GPPDownDatabaseId = GPPDownDatabaseId;
        let status_BaseInfo = await gamepp.database.exists.promise(GPPDownDatabaseId, 'BaseInfo');
        if (!status_BaseInfo) {
            await gamepp.database.create.promise(GPPDownDatabaseId, "BaseInfo", '("id"INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT ,"starttime"INTEGER,"hd_info"TEXT,"state"INTEGER)');
        } else {
            //const list = await gamepp.database.query.promise(StressNewDatabaseId, "BaseInfo", "*", "order by id desc");
            //console.log(list)
        }

        function checkIsSetScheduledShutdown() {
            // 检查用户是否取消关机
            let isUserSetScheduledShutdown = window.localStorage.getItem('isUserSetScheduledShutdown')
            if (!isUserSetScheduledShutdown) return
            if (isUserSetScheduledShutdown === '1') {
                // 用户设置关机时间戳 - 当前时间戳
                let scheduledShutdownTimestamp:any = window.localStorage.getItem('ScheduledShutdownTimestamp');
                if (!scheduledShutdownTimestamp) {
                    return
                }
                try {
                    gamepp.tray.setImage.promise('./assets/500.ico')
                } catch (e) {
                    console.log(e);
                }
                let timeleft = scheduledShutdownTimestamp - Date.now();
                if (timeleft <= 0 && timeleft >= (-5000)) { // 时间超过了5秒就不执行了
                    window.localStorage.removeItem('isUserSetScheduledShutdown');
                    window.localStorage.removeItem('ScheduledShutdownTimestamp');
                    setTimeout(()=>{
                        console.log('定时关机')
                        gamepp.sysShutdown.promise();
                    },500)
                }
            }else{
                try {
                    gamepp.tray.setImage.promise('./assets/logo.ico');
                } catch (e) {
                    console.log(e);
                }
            }
        }

        setInterval(()=>{
            checkIsSetScheduledShutdown()
        },1000)

        async function sendSetSparkMessage(server_config) {
            let msg = {
                server_config,
                'type': 'SetSparkServerInfo',
            }
            let ryzenOrDML = window.localStorage.getItem('lastSelectedType')
            console.log(ryzenOrDML)
            let needClose = false
            if (ryzenOrDML && !gamepp.sparkserver.isSparkServerRunning.sync()) {
                needClose = true // 服务没启动，开启后需要关闭
                if (ryzenOrDML === 'Ryzen') {
                    await gamepp.sparkserver.runRyzenAIClient.promise()//启动RyzenAI的版本调用
                } else if (ryzenOrDML === 'btn'){
                    await gamepp.sparkserver.runClient.promise() //启动通用模式的版本调用
                } else {
                    await gamepp.sparkserver.runUltraAIClient.promise()
                }
            }
            // 等sparkserver启动
            setTimeout(async ()=>{
                // 发消息
                await gamepp.sparkserver.sendMessage.promise(JSON.stringify(msg))
                if (needClose) {
                    await gamepp.sparkserver.stopClient.promise()
                }
            },200)
        }

        window.addEventListener("storage", function (event:any) {
            if (event['key'] === 'chooseSensor_list_v1') {
                sensorInfoModifying = true
                console.log('传感器信息修改')
                localSensorListX = JSON.parse(event['newValue'])
                chooseSensor_list_xStr = window.localStorage.getItem('chooseSensor_list_v1')
                sensorInfoModifying = false
            }else if (event['key'] === 'switchGamepad' || event['key'] === 'switchGamepad_aiMaster' || event['key'] === 'switchGamepad_aiSuperpower') {
                handleSparkServerSwitch()
            }else if (event.key === 'lastSelectedType') {
                const lastSelectedType = window.localStorage.getItem('lastSelectedType');
                try {
                    let server_config = JSON.parse(gamepp.sparkserver.getServerConfig.sync())
                    server_config['lastSelectedType'] = lastSelectedType
                    sendSetSparkMessage(server_config)
                } catch {}
            }else if (event.key === 'VKDevice') {
                const VKDevice = JSON.parse(localStorage.getItem('VKDevice'));
                try {
                    let server_config = JSON.parse(gamepp.sparkserver.getServerConfig.sync())
                    server_config['VKDevice'] = VKDevice
                    sendSetSparkMessage(server_config)
                } catch {}
            }
        });

        handleSparkServerSwitch();
    }

    /**
     * 获取服务器配置传感器默认数据
     */
    async getSensorStatistics () {
        console.log('获取服务器配置传感器默认数据')
        console.time('getSensorStatistics')
        let hw_list_dataStr = window.localStorage.getItem('hw_list_data')
        if (!hw_list_dataStr) return
        const data = {
            hw_list: JSON.parse(hw_list_dataStr),
            version: gamepp.getPlatformVersion.sync()
        }
        console.log(data);
        try {
            const encodeData = gamepp.user.getEncodeInfo.sync(JSON.stringify(data))
            const resultStr = await gamepp.http.httppost.promise('https://config.gamepp.com/hardware/v1/getSensorStatistics', '', encodeData);
            if (resultStr) {
                if (resultStr) {
                    let result = JSON.parse(resultStr)
                    if (result.code === 200 && result.data) {
                        const decodeData = JSON.parse(gamepp.user.getDecodeInfo.sync(result.data));
                        console.log(decodeData)
                        window.localStorage.setItem('sensorStatistics', JSON.stringify(decodeData));
                        if (decodeData.default) {
                            this.ModifyUserSensorToServerData(decodeData.default)
                        }
                    }
                }
            }
        } catch {
        }
        console.timeEnd('getSensorStatistics')
    }

    /**
     * 修改传感器为服务器默认数据
     */
    ModifyUserSensorToServerData (data) {
        let chooseSensor_list_xStr = window.localStorage.getItem('chooseSensor_list_v1')
        if (chooseSensor_list_xStr) {
            let chooseSensor_list = JSON.parse(chooseSensor_list_xStr)
            let SensorInfoStr = gamepp.hardware.getSensorInfo.sync();
            const SensorInfoOriginal = JSON.parse(SensorInfoStr);
            const SensorInfoKeys = Object.keys(SensorInfoOriginal)
            let SensorInfo = this.SensorAddAverageData(SensorInfoOriginal, SensorInfoKeys)
            let isSet = false
            for (let type in data) {
                for (let attribute in data[type]) {
                    let SenName = data[type][attribute]
                    let attribute_name = attribute
                    if (attribute === 'temperature') {attribute_name = 'temp'}
                    let type_name = type
                    if (type === 'board') {type_name = 'Mainboard'}
                    const Data = chooseSensor_list[type + 'Data']?.find(item => item.name === attribute_name);
                    if (Data && Data.isDef) {
                        if (type_name !== 'Mainboard') {
                            type_name = type_name.toUpperCase()
                        }
                        const sensor_index = this.getSensorIndexFromName(SensorInfo, type_name, SenName, attribute)
                        if (sensor_index) {
                            isSet = true
                            Data.isDefServer = true
                            Data.keyName = SenName
                            Data.groupIndex = sensor_index[0]
                            Data.itemIndex = sensor_index[1]
                            console.log(Data.describe + ': 已修改为: ' + SenName)
                        } else {
                            console.log(Data.describe + ': ' + SenName + ' 未匹配到')
                        }
                    }
                }
            }
            if (isSet) {
                window.localStorage.setItem('chooseSensor_list_v1', JSON.stringify(chooseSensor_list))
            }
        }
    }

    async IsReady() {
        return new Promise((resolve, reject) => {
            let nRet = 0;
            let isready_time = setInterval(async () => {
                try {
                    nRet = await gamepp.apiinfo.isready.promise();
                } catch (error) {
                    console.log(error);
                }
                if (nRet === 1) {
                    clearInterval(isready_time)
                    resolve(1)
                }
            }, 100)

        })
    }

    async Run() {
        console.log('check if BG update');

        console.log("Run 1");
        //等待接口准备就绪
        await this.IsReady();
        console.log("Run 2");
        this.registerNotifyReceiver()
        // this.ConvertXMLData2JSON('init')//转换XML数据2 JSON
        this.BGonConfigChanged()
        this.AppEvent_OnWindowReadyToShow();

        gamepp.webapp.onDesktopMonitorClosed.addEventListener(async(msg)=>
        {
            if(msg['action'] == 'appClosed')
            {
                if(gameclient.GAME_ProcessName)return
                console.warn('桌面监控关闭');
                const isDesktopMonitorOpen = await gamepp.setting.getInteger.promise(COMMANDID.CM_USE_DESKTOP_FRAME);
                if (isDesktopMonitorOpen === 1)
                    {
                            await gamepp.webapp.windows.close.promise(WindowName.Desktop_Monitor);
                            setTimeout(async () => {
                                await gamepp.webapp.windows.show.promise(WindowName.Desktop_Monitor);
                            }, 1000)
                    }

            }
        })
        //部分模块的注册放到了，升级模块注册函数中，所以这里得再调用一下
        console.log("Run 3");
        let isclash = gamepp.createMutexWithName.sync('GamePPTwins')//false

        // //HWInfo检查更新
        console.warn('32bit gamepp exist::',!isclash);
        if(!isclash)
        {
            gamepp.webapp.windows.show.sync('onDesk_Conflict');
        }
        else
        {
            //初始化GAMEPP模块
            await gamepp.initUpdateModules.promise();
            await gamepp.runSyncData.promise();
            await gamepp.startMainServices.promise();
            //HWInfo检查更新
            await gamepp.update.checkHWInfo.promise()
            //HWInfo检查更新
            try
            {
              let checkResult =  await gamepp.update.checkHWInfo.promise()
              console.warn('checkResult',checkResult);
            }
            catch(err)
            {
                console.warn('checkHWInfoERR：：',err);
            }
            //加载XML解析文件
            let result = await this.getScript('js/HWInfoDataAnalyzer/HwInfoXMLProcess.js')
            console.warn('load HwInfoXMLProcess result',result);
            let resultI = await this.getScript('js/HWInfoDataAnalyzer/HWInfoSensorProcess.js')
            console.warn('load HWInfoSensorProcess result',resultI);
            try
            {
                gamepp.update.updateHWInfo.sync()
            }
            catch(err)
            {
                console.warn('updateHWInfoERR：：',err);
            }
            console.warn('gamepp.update.updateHWInfo::');
            //XML解析
            if(result && resultI)
            {
                this.ConvertXMLData2JSON('init')//转换XML数据2 JSON
            }
            else
            {
                console.warn('未进行XML解析');
            }

            if (gamepp.isSkipUpdate.sync()) {
                if (!gamepp.isStartedWithOs.sync()) {
                    gamepp.webapp.windows.show.sync(WindowName.DESKTOP);
                    gamepp.webapp.windows.focus.sync(WindowName.DESKTOP);
                }
                gamepp.lansevice.start.promise()
            } else {
                if (gamepp.isStartedWithOs.sync()) {
                    gamepp.webapp.windows.show.sync(WindowName.UPDATE, true);
                } else {
                    gamepp.webapp.windows.show.sync(WindowName.UPDATE);
                    gamepp.webapp.windows.focus.sync(WindowName.UPDATE);
                }
                gamepp.lansevice.start.promise()
            }
            console.log("%c _______  _______  _______  _______    _______  _______ \n" +
                        "(  ____ \\(  ___  )(       )(  ____ \\  (  ____ )(  ____ )\n" +
                        "| (    \\/| (   ) || () () || (    \\/  | (    )|| (    )|\n" +
                        "| |      | (___) || || || || (__      | (____)|| (____)|\n" +
                        "| | ____ |  ___  || |(_)| ||  __)     |  _____)|  _____)\n" +
                        "| | \\_  )| (   ) || |   | || (        | (      | (      \n" +
                        "| (___) || )   ( || )   ( || (____/\\  | )      | )      \n" +
                        "(_______)|/     \\||/     \\|(_______/  |/       |/       \n" +
                        "                                                        ","color:#108FF0")
            setTimeout(() => {
                gamepp.game.ingame.RefreshActiveProcess.sync();
                console.log('RefreshActiveProcess')
            }, 8000)
        }
        let timerFree = setInterval(() => {
            // console.warn('bgInterval');
            this.AutoRecevice()
        }, 1000);
        this.initAutoCpuSet();
        this.checkNeedUpdate();
    }

    async checkNeedUpdate ()
    {
        const nUpdateStatus = await gamepp.update.checkUpdate.promise(2);
        console.log('nUpdateStatus',nUpdateStatus)
        if (nUpdateStatus === 1) {
            window.localStorage.setItem('needCheckUpdate', '1')
        }else{
            window.localStorage.removeItem('needCheckUpdate')
        }
    }

    initAutoCpuSet ()
    {
        const dur = 10000
        setInterval(()=>{
            try {
                const enableCpuSet = JSON.parse(window.localStorage.getItem("enableCpuSet") || "false") === true
                const localProcessListStr = window.localStorage.getItem("local_process")
                const processGroupsStr = window.localStorage.getItem("process_groups")
                if (enableCpuSet && localProcessListStr && processGroupsStr)
                {
                    const localProcessList = JSON.parse(localProcessListStr);
                    const groupList:any = JSON.parse(processGroupsStr)
                    const _obj:any = {}
                    groupList.forEach(({id,cpuSet})=>{
                        _obj[id] = cpuSet;
                    })
                    handleGetProcessList().then((systemProcessList)=>{
                        localProcessList.forEach((v) => {
                            v.process = -1;
                            v.cpuSet = []
                            try {
                                systemProcessList.forEach((item) => {
                                    if (v.name === item.name) {
                                        if (v.process !== item.process) {
                                            v.process = item.process
                                        }
                                        if (v.group) {
                                            v.cpuSet = _obj[v.group]
                                        }
                                        throw new Error('')
                                    }
                                })
                            } catch (e) {}
                        })
                        for (let i = 0; i < localProcessList.length; i++) {
                            const v = localProcessList[i]
                            if (v['cpuSet'] && v['process'] != -1) { // 设置了cpuSet并且有这个进程
                                const processCpuSet = gamepp.getProcessCpuSet.sync(v['process'])
                                const CPUSET = v['cpuSet']
                                if (!arraysAreEqual(processCpuSet,CPUSET)) {
                                    gamepp.setProcessCpuSet.promise(v['process'], CPUSET)
                                }
                            }
                        }
                    })
                }
            }catch (e) {}
        },dur)
        function arraysAreEqual(arr1, arr2) {
            if (arr1.length !== arr2.length) return false;
            const set1 = new Set(arr1);
            const set2 = new Set(arr2);
            return [...set1].every(val => set2.has(val));
        }
        async function handleGetProcessList() { // 获取进程列表，包含icon
            const processList = await gamepp.getprocessList.promise()
            return processList
        }
    }


    async getScript(url)
    {

        for (let index = 0; index < 3; index++)
        {
                const bRet = await this.getScriptex(url)
                if(bRet)
                {
                    return bRet
                }
        }

        return false
    }

    getScriptex(url) {
        return new Promise((resolve) => {
                var script = document.createElement('script');
                script.type = 'text/javascript';
                script.src = url;
                console.warn('script.src:', script.src);

                script.onload = function() {
                    console.log('Script loaded:', url);
                    resolve(true); // 成功返回true
                };

                script.onerror = function() {
                    resolve(false); // 失败返回false
                };

                document.head.appendChild(script);
        });
    }

    /**
     * 获取服务器配置文件信息
     */
    async GetServerGppConfig() {
        const response = await gamepp.http.httpget.promise('https://config.gamepp.com/api/v2/getGppConfig', '');
        if (response) {
            const response_obj = JSON.parse(response);
            if (response_obj['code'] === 200) {
                let decode = gamepp.user.getDecodeInfo.sync(response_obj['config']);
                let object = JSON.parse(decode);
                let regPos = /^[0-9]+.?[0-9]*/;
                let VideoCon:any = {}
                for (let i = 0; i < object.length; i++) {
                    if (object[i].state === 1) {
                        //1:config配置 2:链接配置
                        let type = object[i].type;
                        let id = object[i].id, value = object[i].value;
                        if (type === 1) {
                            if (regPos.test(value)) {
                                gamepp.setting.setInteger.sync(id, Number(value));
                            }
                        } else if (type === 2) {
                            VideoCon[id] = value
                        }
                    }
                    if (object[i].id === 522) { // ai教练允不允许开
                        window.localStorage.setItem('GppConfigAIMaster', object[i].value)
                    }
                }
                if (VideoCon.length !== 0) {
                    window.localStorage.setItem('GppConfig_url', JSON.stringify(VideoCon))
                }
            }
        }
    }

    /**
     * 保存CPU GPU 名字
     * 更换硬件后恢复默认传感器index
     */
    saveHardwareInformation(HwInfoJson) {
        let cpu_name = HwInfoJson.CPU.SubNode[0].ProcessorName;
        let gpu_name = HwInfoJson.GPU.SubNode[gpu_index].VideoChipset;
        let hdName_str = window.localStorage.getItem('cpu_gpu_Name');
        if (hdName_str !== null && hdName_str !== 'null' && hdName_str !== undefined) {
            let cpu_gpu_Name_data = JSON.parse(hdName_str);
            if (cpu_name !== cpu_gpu_Name_data.cpu || gpu_name !== cpu_gpu_Name_data.gpu) {
                window.localStorage.removeItem('monitor_setting');
                window.localStorage.removeItem('isUploadHD');
                window.localStorage.removeItem('chooseSensor_list_v1');
            }
        }
        let Object = {};
        Object['cpu'] = cpu_name;
        Object['gpu'] = gpu_name;
        window.localStorage.setItem('cpu_gpu_Name', JSON.stringify(Object));
    }

    async StartSpark () {
        const lastSelectedType = localStorage.getItem('lastSelectedType');
        console.log(lastSelectedType);
        // 打点
        // 100801 通用模式
        // 100802 AMD_NPU模式
        // 100803 Intel_NPU模式
        if (lastSelectedType === 'Ryzen') {
            try {await gamepp.utils.sendstatics.promise(100802);}catch (e) {}
            await gamepp.sparkserver.runRyzenAIClient.promise()//启动RyzenAI的版本调用
        } else if (lastSelectedType === 'btn'){
            try {await gamepp.utils.sendstatics.promise(100801);}catch (e) {}
            await gamepp.sparkserver.runClient.promise() //启动通用模式的版本调用
        } else {
            try {await gamepp.utils.sendstatics.promise(100803);}catch (e) {}
            await gamepp.sparkserver.runUltraAIClient.promise()
        }
        const newStartData = {
            type: 'GetSparkServerInfo',
        }
        const json = JSON.stringify(newStartData)
        try {await gamepp.sparkserver.sendMessage.promise(json) } catch {}
        sparkMessageListener = async (message) => {
            console.log(message)
            try {
                const parsedMessage = JSON.parse(message);
                const errorMap = {
                    '-1': '模型未找到',
                    '-2': '获取游戏窗口失败',
                    '-3': '设备不支持',
                    '-4': 'BLOB初始化失败',
                    '-5': '捕获画面失败',
                    '-6': 'YOLO推理初始化失败',
                    '-7': '识别进程未匹配到游戏所在显示器连接的显卡',
                    '-10': '当前已有推理程序运行',
                    '-11': '模式没有下载',
                    '-12': '服务未匹配到游戏所在显示器连接的显卡'
                };

                if (errorMap.hasOwnProperty(String(parsedMessage.code))) {
                    const errorMessage = errorMap[String(parsedMessage.code)];
                    console.log(errorMessage);
                    this.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 30, errorMessage, 2);
                    gamepp.webapp.sendInternalAppEvent.promise('ai_lab', parsedMessage.code);
                    // 收到sparkserver错误的消息关闭游戏内的窗口
                    if (gamepp.webapp.windows.isValid.sync('ingame_kdTips')) {
                        await gamepp.webapp.windows.close.promise('ingame_kdTips');
                    }
                    if (gamepp.webapp.windows.isValid.sync('ingame_highlightsavetips')){
                        await gamepp.webapp.windows.close.promise('ingame_highlightsavetips');
                    }
                    if (gamepp.webapp.windows.isValid.sync('ingame_broadsword')) {
                        await gamepp.webapp.windows.close.promise('ingame_broadsword');
                    }
                }
            } catch (error) {
                console.error(error);
            }
        }
        sparkMessageListenerId = gamepp.sparkserver.onSparkMessage.addEventListener(sparkMessageListener);
    }

    async StopSpark () {

        await gamepp.sparkserver.stopClient.promise()

        if (sparkMessageListenerId) {
            gamepp.sparkserver.onSparkMessage.removeEventListener(sparkMessageListenerId);
        }
    }
}
const app = new Background();

app.Run();//ConvertXMLData2JSON,Run

function handleSparkServerSwitch() {
    let switchGamepadData = window.localStorage.getItem('switchGamepad')
    let switchGamepad
    try {
        switchGamepad = switchGamepadData?JSON.parse(switchGamepadData):[];
    } catch (error) {
        switchGamepad = []
        localStorage.setItem('switchGamepad', JSON.stringify(switchGamepad));
    }
    // 取ai教练的开关状态
    let switchGamepad_aiMasterData = window.localStorage.getItem('switchGamepad_aiMaster')
    let switchGamepad_aiMaster
    try {
        switchGamepad_aiMaster = switchGamepad_aiMasterData?JSON.parse(switchGamepad_aiMasterData):[];
    } catch (error) {
        switchGamepad_aiMaster = []
        localStorage.setItem('switchGamepad_aiMaster', JSON.stringify(switchGamepad_aiMaster));
    }
    // 超能时刻开关状态
    let switchGamepad_HighLightData = window.localStorage.getItem('switchGamepad_aiSuperpower')
    let switchGamepad_HighLight
    try {
        switchGamepad_HighLight =switchGamepad_HighLightData?JSON.parse(switchGamepad_HighLightData):[]
    }catch(e){
        switchGamepad_HighLight = []
        localStorage.setItem('switchGamepad_HighLight', JSON.stringify(switchGamepad_HighLight));
    }

    const hasTrueValue = switchGamepad.some(item => item.value === true);
    const hasTrueValue_aiMaster = switchGamepad_aiMaster.some(item => item.value === true);
    const hasTrueValue_HighLight = switchGamepad_HighLight.some(item => item.value === true);

    if (hasTrueValue_HighLight) {
        try{gamepp.setting.setBool.sync(406, true);gamepp.setting.setBool.sync(41, true);}catch {}
    }

    if (hasTrueValue || hasTrueValue_aiMaster || hasTrueValue_HighLight) {
        console.log('switchGamepad', switchGamepad)
        console.log('switchGamepad_aiMaster', switchGamepad_aiMaster)
        if ((window as any).SparkTimer) clearTimeout(window.SparkTimer)
        (window as any).SparkTimer = setTimeout(()=>{
            app.StartSpark()
        },2000)
    }else{
        console.log('switchGamepad', switchGamepad)
        if ((window as any).SparkTimer) clearTimeout(window.SparkTimer)
        (window as any).SparkTimer = setTimeout(()=>{
            app.StopSpark()
        },2000)
    }
}
