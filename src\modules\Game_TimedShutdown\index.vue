<script setup lang="ts">
import {reactive, onMounted, onUnmounted, watch, ref} from "vue";
import {useI18n} from "vue-i18n";
import WindowHeader from "@/components/mainCom/windowHeader.vue";
import { useLanguage } from '@/uitls/useLanguage';
useLanguage()
const {t} = useI18n()
const data = reactive({
    nowTime: '', // 当前时间
    shutdownTime: {
        month: '00', // 关机时间
        day: '00', // 关机时间
        hour: '00', // 关机时间
        minute: '00', // 关机时间
        second: '00', // 关机时间
    }, // 关机时间
    leftTime: {
        hour: '00',
        minute: '00',
        second: '00',
        ms: '000'
    },
    countdown: {
        hour: 0, // 倒计时的小时
        minute: 0, // 倒计时的分钟
        second: 0, // 倒计时的秒数
        ms: 0 // 倒计时的毫秒
    },
    isSet: false
})
let animationFrameId: any = null; // 存储 requestAnimationFrame 的 ID
let setShutdownTimestamp = 0

const windowName = 'timedshutdown'
const zoomValue = ref<number>(1)
watch(zoomValue, (newValue) => {
    document.body.style.zoom = newValue+''
},{immediate:true})
onMounted(async () => {
    checkLocalStorage()
    getNowTime()
    try {
        const zoomWithSystem = gamepp.setting.getInteger.sync(313) // 是否点了跟随系统缩放开关
        if (zoomWithSystem === 1) {
            zoomValue.value = await gamepp.display.getScaleFromWindowInMonitor.promise();
            gamepp.webapp.windows.resize.sync(windowName, Math.floor(812 * zoomValue.value), Math.floor(512 * zoomValue.value))
        }
        gamepp.display.onDisplayMetricsChanged.addEventListener(async (scaleFactor: number) => {
            const zoomWithSystem = gamepp.setting.getInteger.sync(313)
            if (zoomWithSystem === 1) {
                zoomValue.value = scaleFactor
                gamepp.webapp.windows.resize.sync(windowName, Math.floor(812 * zoomValue.value), Math.floor(512 * zoomValue.value))
            }
        })
    } catch {

    }
})

onUnmounted(() => {
    if (animationFrameId) {
        cancelAnimationFrame(animationFrameId); // 组件卸载时取消动画帧
    }
})

function checkLocalStorage() {
    if (window.localStorage.getItem("isUserSetScheduledShutdown") === '1') {
        data.isSet = true
        const shutdownTimestamp = window.localStorage.getItem('ScheduledShutdownTimestamp')
        if (shutdownTimestamp) {
            setShutdownTimestamp = parseInt(shutdownTimestamp)
            const d = new Date(parseInt(shutdownTimestamp))
            const month = d.getMonth() + 1
            const day = d.getDate()
            const hour = d.getHours()
            const minute = d.getMinutes()
            const second = d.getSeconds()
            data.shutdownTime.month = month < 10 ? `0${month}` : month + ''
            data.shutdownTime.day = day < 10 ? `0${day}` : day + ''
            data.shutdownTime.hour = hour < 10 ? `0${hour}` : hour + ''
            data.shutdownTime.minute = minute < 10 ? `0${minute}` : minute + ''
            data.shutdownTime.second = second < 10 ? `0${second}` : second + ''
        }
    }
}

function getNowTime() {
    const now = new Date()
    const nowTimestamp = Date.now();
    const shutdownTimestamp = nowTimestamp + data.countdown.hour * 3600000 + data.countdown.minute * 60000 + data.countdown.second * 1000;
    const year = now.getFullYear()
    const month = (now.getMonth() + 1) < 10 ? `0${now.getMonth() + 1}` : now.getMonth() + 1 + ''
    const day = now.getDate() < 10 ? `0${now.getDate()}` : now.getDate() + ''
    const hour = now.getHours() < 10 ? `0${now.getHours()}` : now.getHours() + ''
    const minute = now.getMinutes() < 10 ? `0${now.getMinutes()}` : now.getMinutes() + ''
    const second = now.getSeconds() < 10 ? `0${now.getSeconds()}` : now.getSeconds() + ''
    data.nowTime = ` ${year}${t('shutdownTimer.year')}${month}${t('shutdownTimer.month')}${day}${t('shutdownTimer.day')} ${hour}:${minute}:${second}`

    if (!data.isSet) {
        const shutdownTime = new Date(shutdownTimestamp)
        const shutdownMonth = shutdownTime.getMonth() + 1
        const shutdownDay = shutdownTime.getDate()
        const shutdownHour = shutdownTime.getHours()
        const shutdownMinute = shutdownTime.getMinutes()
        const shutdownSecond = shutdownTime.getSeconds()
        data.shutdownTime.month = shutdownMonth < 10 ? `0${shutdownMonth}` : shutdownMonth + ''
        data.shutdownTime.day = shutdownDay < 10 ? `0${shutdownDay}` : shutdownDay + ''
        data.shutdownTime.hour = shutdownHour < 10 ? `0${shutdownHour}` : shutdownHour + ''
        data.shutdownTime.minute = shutdownMinute < 10 ? `0${shutdownMinute}` : shutdownMinute + ''
        data.shutdownTime.second = shutdownSecond < 10 ? `0${shutdownSecond}` : shutdownSecond + ''
    } else {
        const leftTime = setShutdownTimestamp - nowTimestamp
        const leftHour = Math.floor(leftTime / 3600000)
        const leftMinute = Math.floor((leftTime - leftHour * 3600000) / 60000)
        const leftSecond = Math.floor((leftTime - leftHour * 3600000 - leftMinute * 60000) / 1000)
        data.leftTime.hour = leftHour < 10 ? `0${leftHour}` : leftHour + ''
        data.leftTime.minute = leftMinute < 10 ? `0${leftMinute}` : leftMinute + ''
        data.leftTime.second = leftSecond < 10 ? `0${leftSecond}` : leftSecond + ''
        data.leftTime.ms = leftTime % 1000 < 10 ? `00${leftTime % 1000}` : leftTime % 1000 < 100 ? `0${leftTime % 1000}` : leftTime % 1000 + ''
        if (leftTime < 0) {
            data.isSet = false
        }
    }
    animationFrameId = requestAnimationFrame(getNowTime)
}

// 格式化显示，补零成两位数
const formatNumber = (value: number) => {
    return String(value).padStart(2, '0');
};

// 解析输入，确保返回的是数字（去掉前导零）
const parseNumber = (value: string) => {
    return parseInt(value, 10) || 0;
};

// 格式化显示，补零成三位数
const formatNumberMs = (value: number) => {
    return String(value).padStart(3, '0');
};

// 解析输入，确保返回的是数字（去掉前导零）
const parseNumberMs = (value: string) => {
    return parseInt(value, 10) || 0;
};

function setTimedShutdown() { // 设置定时关机
    if (data.countdown.hour === 0 && data.countdown.minute === 0 && data.countdown.second === 0) {
        return
    }
    data.isSet = true;
    const nowTimestamp = Date.now();
    const shutdownTimestamp = nowTimestamp + data.countdown.hour * 3600000 + data.countdown.minute * 60000 + data.countdown.second * 1000;
    setShutdownTimestamp = shutdownTimestamp
    window.localStorage.setItem('ScheduledShutdownTimestamp', `${shutdownTimestamp}`)
    window.localStorage.setItem("isUserSetScheduledShutdown", '1');
}

function cancelTimedShutdown() { // 取消定时关机
    data.isSet = false;
    window.localStorage.setItem("isUserSetScheduledShutdown", '0');
    window.localStorage.removeItem('ScheduledShutdownTimestamp')
}

function changeCountdownHour(v: string) {
    const n = parseInt(v)
    if (n > 23) {
        data.countdown.hour = 23
    } else if (n < 0) {
        data.countdown.hour = 0
    }
}

function changeCountdownMinute(v: string) {
    const n = parseInt(v)
    if (n > 59) {
        data.countdown.minute = 59
    } else if (n < 0) {
        data.countdown.minute = 0
    }
}

function changeCountdownSecond(v: string) {
    const n = parseInt(v)
    if (n > 59) {
        data.countdown.second = 59
    } else if (n < 0) {
        data.countdown.second = 0
    }
}

function changeCountdownMs(v: string) {
    const n = parseInt(v)
    if (n > 999) {
        data.countdown.ms = 999
    } else if (n < 0) {
        data.countdown.ms = 0
    }
}

function closeWindow() {
    gamepp.webapp.windows.close.sync(windowName)
}
</script>

<template>
    <div class="shutdown-timer">
        <window-header :window-name="windowName" :maximizeIcon="false" :minimize-icon="false" @close="closeWindow">
            <span class="title">{{ $t('shutdownTimer.timedShutdown') }}</span>
        </window-header>

        <div class="shutdown-timer-content">
            <section>
                <div class="currentTime">
                    {{ $t('shutdownTimer.currentTime') }}
                    {{ data.nowTime }}
                </div>
                <div class="countDown" v-show="!data.isSet">
                    {{ $t('shutdownTimer.setCountdown') }}
                    <div class="num">
                        <el-input
                            style="width: 60px;"
                            :formatter="formatNumber"
                            :parser="parseNumber"
                            v-model="data.countdown.hour"
                            v-show="!data.isSet"
                            @change="changeCountdownHour"
                        />
                    </div>
                    {{ $t('shutdownTimer.hour') }}
                    <div class="num">
                        <el-input
                            style="width: 60px;"
                            :formatter="formatNumber"
                            :parser="parseNumber"
                            v-model="data.countdown.minute"
                            @change="changeCountdownMinute"
                        />
                        <span v-show="data.isSet">{{ data.leftTime.minute }}</span>
                    </div>
                    {{ $t('shutdownTimer.min') }}
                    <div class="num" >
                        <el-input
                            style="width: 60px;"
                            :formatter="formatNumber"
                            :parser="parseNumber"
                            v-model="data.countdown.second"
                            @change="changeCountdownSecond"
                        />
                    </div>
                    {{ $t('shutdownTimer.sec') }}
                    <div class="num">
                        <el-input
                            disabled
                            style="width: 60px;"
                            :formatter="formatNumberMs"
                            :parser="parseNumber"
                            v-model="data.countdown.ms"
                            @change="changeCountdownMs"
                        />
                    </div>
                    {{ $t('shutdownTimer.ms')+$t('shutdownTimer.shutdownIn') }}
                </div>
                <div class="countDown" v-show="data.isSet">
                    {{ $t('shutdownTimer.setCountdown') }}
                    <div class="countDownBox">
                        {{`${data.leftTime.hour}:${data.leftTime.minute}:${data.leftTime.second}:${data.leftTime.ms}`}}
                    </div>
                    {{$t('shutdownTimer.shutdownIn')}}
                </div>

                <div class="willBe">
                    <span>{{ $t('shutdownTimer.goingToBe') }}</span>
                    <span class="qzFont">{{ data.shutdownTime.month }}</span>
                    <span>{{ $t('shutdownTimer.month') }}</span>
                    <span class="qzFont">{{ data.shutdownTime.day }}</span>
                    <span>{{ $t('shutdownTimer.day') }}</span>
                    <span class="qzFont">{{ data.shutdownTime.hour }}</span>
                    <span>{{ $t('shutdownTimer.hour') }}</span>
                    <span class="qzFont">{{ data.shutdownTime.minute }}</span>
                    <span>{{ $t('shutdownTimer.min') }}</span>
                    <span class="qzFont">{{ data.shutdownTime.second }}</span>
                    <span>{{ $t('shutdownTimer.sec') }}</span>
                    <span style="margin-left: 10px">{{ $t('shutdownTimer.executionPlan') }}</span>
                </div>

                <div class="bottom-btn" v-if="!data.isSet" @click="setTimedShutdown">
                    <span>{{ $t('shutdownTimer.startTheClock') }}</span>
                </div>
                <div class="bottom-btn redbg" v-if="data.isSet" @click="cancelTimedShutdown">
                    <span>{{ $t('shutdownTimer.stopTheClock') }}</span>
                </div>
            </section>
        </div>
    </div>
</template>

<style scoped lang="scss">
.shutdown-timer {
    margin-left: 6px;
    margin-top: 6px;
    box-shadow:0 1px 6px rgba(0,0,0,.6);
    width: 800px;
    height: 500px;
    overflow: hidden;
    border-radius: 6px;

    .shutdown-timer-header {
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        align-items: center;
        justify-content: flex-start;
        height: 40px;
        background: #2B2C37;
        color: #ffffff;
        padding-left: 10px;

        .logo {
            width: 14px;
            height: 14px;
            margin-right: 13px;
        }

        .close {
            color: #999999;
            margin-left: auto;
            height: 40px;
            width: 40px;
            text-align: center;
            line-height: 40px;

            &:hover {
                background-color: #22232e;
            }
        }
    }

    .shutdown-timer-content {
        width: 800px;
        height: 460px;
        background-color: #22232d;
        padding: 10px;

        section {
            width: 780px;
            height: 440px;
            background: #2B2C37;
            box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
            position: relative;

            .currentTime {
                position: absolute;
                top: 11px;
                right: 10px;
                font-size: 12px;
                color: #999999;
            }

            .countDown {
                position: absolute;
                top: 130px;
                left: 50%;
                transform: translate(-50%);
                display: flex;
                flex-flow: row nowrap;
                gap: 9px;
                color: #fff;
                // white-space: normal; 
                align-items: center;
                line-height: 32px;
                word-break: keep-all;

                .num {
                    width: 60px;
                    height: 32px;
                    border-radius: 4px;
                    background: #202129;
                    font-family: Quartz, serif;
                    font-size: 18px;
                    text-align: center;

                    :deep(.el-input) {
                        --el-input-bg-color: #202129;
                        --el-input-border-color: #202129;
                        --el-input-text-color: #F93030;

                    }

                    :deep(.el-input__wrapper) {
                        box-shadow: none;
                    }

                    :deep(.el-input__wrapper:hover) {
                        box-shadow: none;
                    }

                    :deep(.el-input__wrapper.is-focus) {
                        box-shadow: none;
                    }

                    :deep(.el-input__inner) {
                        font-family: Quartz, serif;
                        font-size: 18px;
                        text-align: center;
                    }

                    :deep(.el-input.is-disabled .el-input__wrapper) {
                        background-color: #202129;
                        color: #999999;
                    }
                }

                .countDownBox {
                    height: 32px;
                    border-radius: 4px;
                    background: #202129;
                    padding: 0 18px;
                    line-height: 32px;
                    font-family: Quartz,sans-serif;
                    font-size: 18px;
                    letter-spacing: 3px;
                    color: #F93030;
                }
            }

            .willBe {
                position: absolute;
                top: 245px;
                left: 50%;
                transform: translate(-50%);
                display: flex;
                flex-flow: row nowrap;
                white-space: nowrap;
                color: #999999;
                align-items: center;

                .qzFont {
                    margin: 0 15px;
                }
            }

            .bottom-btn {
                position: absolute;
                bottom: 30px;
                left: 50%;
                transform: translate(-50%);
                min-width: 120px;
                height: 30px;
                background: #336AB5;
                border-radius: 4px;
                text-align: center;
                line-height: 30px;
                color: #ffffff;
                cursor: pointer;
                padding: 0 12px;

                &:hover {
                    background: #295899;
                }

                &.redbg {
                    background-color: #A64343;

                    &:hover {
                        background-color: #b75050;
                    }
                }
            }

            .qzFont {
                font-family: Quartz, sans-serif;
                font-size: 18px;
            }
        }
    }
}
</style>
