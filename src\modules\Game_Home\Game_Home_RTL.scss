body[dir=rtl] {
    .ingamenav .ingamenav_box .nav_left .gpp-img {
        margin-right: 0;
        margin-left: 5px;
    }
    .gpp-img {
        margin-right: 0;
        margin-left: 5px;
    }
    .container .right .nav .setting {
        margin-left: -6px;
        margin-right: auto;
    }

    .outside .message span {
        margin-right: 20px;
    }

    .Main .title_des {
        margin-right: 20px;
    }

    .setting-dropdown .el-dropdown-menu .iconfont {
        margin-left: 12px;
        margin-right: 0;
    }

    .Main .title span {
        margin-right: 0;
        margin-left: 10px;
    }

    .Main .monitor .top {
        margin-right: 20px;
        margin-left: 0;
    }

    .Main .monitor .hardwareItem .chunk {
        margin-right: 0;
        margin-left: 19px;
    }

    .Main .hardware .hardwareItem .left {
        margin-left: 0;
        margin-right: 20px;
    }

    .Main .hardware .hardwareItem .right {
        margin-left: 28px;
        margin-right: 0;
    }

    .Main .monitor .hardwareItem {
        padding: 5px 22px 5px 0;
    }

    .top .outervalue span {
        margin-left: 5px !important;
        margin-right: 0;
    }

    .Main .gamemirror .bottom {
        margin-left: 0;
        margin-right: 20px;
    }

    .Main .gamemirror .bottom .mirror .mode {
        margin-left: 0;
        margin-right: 20px;
    }

    .Main .gamemirror .bottom .mirror .current {
        margin-left: 20px;
        margin-right: 0;
    }

    .Main .gamemirror .bottom .intro {
        margin-left: 0;
        margin-right: 20px;
    }

    .Main .pcocessCore .top, .Main .FreeGame .top, .Main .pcocessCore .top {
        margin-left: 0;
        margin-right: 20px;
    }

    .Main .regameing .bottom .gameBox {
        margin-left: 0;
        margin-right: 20px;
    }

    .Main .regameing .bottom .baseInfo .baseItem {
        margin-left: 0;
        margin-right: 20px;
    }

    .Main .pcocessCore .bottom .content {
        margin: 10px 20px 0 0;
    }

    .Main .FreeGame .content2 {
        margin-right: 20px;
    }

    .Main .title img {
        margin-left: 5px;
        margin-right: 0;
    }

    .outervalue, .toolBar {
        margin-left: 0;
        margin-right: 6px;
    }

    .toolBar .content {
        right: unset;
        left: 0;
    }

    section.login_status {
        right: 158px;
        left: unset;
    }

    .hardware-outside .hardware-message h1 {
        margin-left: 0;
        margin-right: 15px;
    }

    .hardware-outside .hardware-message .icon-hwinfo {
        margin-left: 30px;
        margin-right: auto;
    }

    .el-button + .el-button {
        margin-left: 0;
        margin-right: 12px;
    }

    .hardware-outside .hardware-content .iconfont {
        margin-right: 0 !important;
        margin-left: 5px !important;
    }

    .HardwareCPU .HardwareCPUTitle, .HardWareAll-container .hardware-all-l2-title, .HardwareGPU .HardwareGPUTitle, .HardwareMemory .HardwareMemoryTitle, .HardwareDisk .HardwareDiskTitle {
        padding-left: 25px;
        padding-right: 0;
    }

    .hardware-iconfont-wrap.el-tooltip__trigger.el-tooltip__trigger {
        margin-left: 0 !important;
        margin-right: 20px;
    }

    .hardware-outside .ml-auto {
        margin-left: 0;
        margin-right: auto;
    }

    .HardWareAll-container .HardWareAll-memory .hardware-all-l2-title .ml-5 {
        margin-left: 0;
        margin-right: 5px;
    }

    .HardWareAll-container .HardWareAll-memory .hardware-all-l2-title text {
        margin-left: 5px;
        margin-right: 3px;
    }

    .top_setting .top_switch .el-switch {
        margin-right: 10px;
    }

    .monitor-container .content-all .content-left .left-title p, .monitor-container .content-all .content-right .right-title p {
        padding-left: 0;
        padding-right: 10px;
    }

    .import-button:last-child {
        margin-left: 0;
        margin-right: 15px;
    }

    .monitor-container .content-all .content-right .arranged p {
        margin-right: 0;
        margin-left: 78px;
    }

    .monitor-container .content-all .content-right .arranged .el-radio {
        margin-right: 0;
        margin-left: 32px;
    }

    .monitor-container .content-all .content-right .arranged .el-radio__label {
        padding-left: 0;
        padding-right: 8px;
    }

    .monitor-container .content-all .content-right .monitor-position .set-position .position-text {
        margin-left: 0;
        margin-right: 72px;
    }

    .monitor-container .content-all .content-right .monitor-bg .change-bg .bg-color .font-family {
        margin-right: 0;
        margin-left: 25px;
    }

    .el-checkbox.el-checkbox--large .el-checkbox__label {
        padding-left: 0;
        padding-right: 8px;
    }

    .monitor-container .content-all .content-right .font-style {
        margin-left: 0;
        margin-right: 25px;
    }

    .monitor-container .content-all .content-right {
        margin-left: 0;
        margin-right: 20px;
    }

    .single_setting .el-collapse-item__header {
        padding-left: 0;
        padding-right: 10px;

        .el-collapse-item__arrow {
            margin: 0 auto 0 8px;
        }
    }

    .single_setting {
        .mr-10 {
            margin-right: 0;
            margin-left: 10px;
        }
        .el-collapse-item__content {
            padding-left: 0;
            padding-right: 10px;
        }
        .chooseSensor {
            margin-left: 25px;
            margin-right: 0;
        }
        .el-checkbox__label {
            padding-left: 0;
            padding-right: 8px;
        }
    }
    .feedback-container {
        .feedback-header{
            p{
                margin-left: 0;
                margin-right: 10px;
            }
            .close-btn{
                left: 0;
                right: auto;
            }
        }
        .uploadImgList{
            span{
                margin-left:0;
                margin-right: 10px;
            }
        }
    }
    .feedback-container .feedback-content .feedback-item .confirm-btn{
        margin-left:inherit;
        margin-right: auto;
    }
    .feedback-container .feedback-content .feedback-item .btn_qq{
        margin-left:0!important;
        margin-right: 25px;
   }
}
