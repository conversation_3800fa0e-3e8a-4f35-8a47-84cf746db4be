body[dir=rtl] {
    .new_component{
        .left{
            .title{
                margin-left:0;
                margin-right: 20px;
            }
            .expand-trigger{
                padding-left:0;
                padding-right: 20px;
                span{
                    margin-left:0;
                    margin-right: 10px;
                }
            }
            .expand-content{
                .list-item{
                    padding-left:0;
                    padding-right: 45px;
                }
            }
        }
        .header {
            .close{
                margin-left: inherit!important;
                margin-right: auto;
            }
        }
        .dynamic-content{
            .el-checkbox__label{
                padding-left:0;
                padding-right: 8px;
            }
            .displacement{
                strong{
                    margin-left:0;
                    margin-right: 5px;
                }
                .Colorbox{
                    margin-right:0;
                    margin-left: 10px;
                }
                .el-checkbox{
                    margin-right: 0;
                    margin-left: 30px;
                }
                .el-checkbox:last-of-type{
                    margin-right: 0;
                    margin-left: 0;
                }
                span{
                    margin-right: 0;
                    margin-left: 10px;
                }
            }
        }
        .el-button+.el-button{
            margin-left: 0px;
            margin-right: 12px;
        }
    }
}