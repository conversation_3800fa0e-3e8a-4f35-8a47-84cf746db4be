export interface effective_clock {
  [key: number]: {
    T0?: number;
    T1?: number;
  };
}
export interface cpu_thread extends effective_clock {}
export interface bg_sensor_data_cpu {
  amd_thermal: number|null
  clock: number|null 
  clockE: number|null
  clockP: number|null
  core_info: {
    Clock: Array<number>
    Load: Array<number>
    Temp: Array<number>
  }
  effective_clock: effective_clock
  fan: number|null
  limit: Array<number>
  name: string|null
  npu_clock: number|null
  npu_usage: number|null
  power: number|null
  temp: number|null
  tempE: number|null
  tempP: number|null
  thread: cpu_thread
  usage: number|null
  usageE: number|null
  usageP: number|null
  voltage: number|null
  voltageE: number|null
  voltageP: number|null
}
export interface bg_sensor_data_drive {
  sys_temp: number|null
  temp: number|null
  temp_list: Array<number>
  temp_list_all: Array<number>
  usage: number|null
  usage_list: Array<number|null>
}
export interface bg_sensor_data_gpu {
  clock: number|null
  d3d_usage: number|null
  fan: number|null
  hot_spot_temp: number|null
  mem_clock: number|null
  mem_size: number|null
  mem_temp: number|null
  mem_usage: number|null
  mem_usage_mb: string|null
  name: string|null
  pl_max_operating_voltage: number|null
  pl_reliability_voltage: number|null
  pl_thermal: number|null
  power: number|null
  power_list: Array<any>
  shadres: number|null
  temp: number|null
  thermal_hotspot: number|null
  thermal_memory: number|null
  total_usage: number|null
  usage_str: string|null
  videobus: string|null
  voltage: number|null
}
export interface bg_sensor_data_memory {
  channel: number|null
  clock: number|null
  name: string|null
  size: number|null
  tcas: number|null
  temp: number|null
  tras: number|null
  trcd: number|null
  trp: number|null
  usage: number|null
  usage_mb: number|null
  voltage: number|null
}

export interface bg_sensor_data {
  cpu:bg_sensor_data_cpu
  drive:bg_sensor_data_drive
  gpu:bg_sensor_data_gpu
  gpu_list: [bg_sensor_data_gpu,bg_sensor_data_gpu,bg_sensor_data_gpu]
  mainboard: {
    temp: number|null
    voltage: number|null
  }
  memory: bg_sensor_data_memory
  network: {
    download: number|null
    upload: number|null
  }
  whea: number|null
}
