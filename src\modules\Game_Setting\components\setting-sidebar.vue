<template>
  <div class="setting-sidebar">
    <h1>{{ $t('Setting.setting') }}</h1>
    <ul class="scroll">
      <li
          v-for="(nav,index) in NavList"
          :key="nav.target"
          :class="{'active': nav.active}"
          @click="goToAnchorPoint(nav.target,index)"
      >
        {{ $t(nav.name) }}
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import {inject, nextTick, onBeforeMount, onMounted} from "vue";

let NavList:any = inject('NavList');

onBeforeMount(()=>{
  onBeforeunload()
})

onMounted(()=>{
  checkLocal()
})

function goToAnchorPoint(target: string, index: number) {
  window.ToAnchorPoint = true
  window.location.hash = target
  for (let i = 0; i < NavList.value.length; i++) {
    NavList.value[i].active = false
  }
  NavList.value[index].active = true
  setTimeout(()=>{
    window.ToAnchorPoint = false
  },100)
}

function checkLocal() {
  let target = window.localStorage.getItem('setting_anchorPoint')
  console.log(target)
  if (target === '#cgq') {
    setTimeout(()=>{
      goToAnchorPoint('#cgq', 3)
    },100)
  } else if(target === '#kjj') {
    goToAnchorPoint('#kjj', 6)
  }else if(target === '#OLED') {
    goToAnchorPoint('#OLED', 4)
  }
}

function onBeforeunload() {
  window.addEventListener('beforeunload', function (event) {
    window.localStorage.removeItem('setting_anchorPoint')
  })
}
</script>

<style scoped lang="scss">
.setting-sidebar {
  color: var(--font-color);
  width: 160px;
  height: 100%;
  background: var(--sidebar-bg);
  border-radius: 4px;
  display: flex;
  flex-flow: column nowrap;
  align-items: center;
  font-size: .12rem;

  h1 {
    font-size: .14rem;
    margin: 40px 0 39px 0;
  }

  ul {
    overflow-y: auto;
  }

  ul li {
      width: 150px;
      height: 60px;
      border-radius: 4px;
      text-align: center;
      cursor: pointer;
      white-space: normal;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0 5px;

    a {
      color: var(--font-color);
    }

    &.active {
      background-color: var(--active-sidebar-bg);
      color: var(--active-color);
    }
  }
}
</style>
