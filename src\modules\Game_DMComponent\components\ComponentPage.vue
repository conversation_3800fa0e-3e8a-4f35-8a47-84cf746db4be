<template>
  <div class="dynamic-content">
    <div class="dynamic-box scroll" v-show="!htmlshow">
      <!-- <div class="Set_item" style="margin-top: 5px;">
        <p>设备页面:</p>
        <div class="itemdata">
          <el-radio-group v-model="radio">
            <el-radio :label="1">1</el-radio>
            <el-radio :label="2">2</el-radio>
          </el-radio-group>
        </div>
      </div> -->
      <div class="Set_item" v-if="type2 === 'text'">
        <p>{{ $t('DesktopMonitoring.CustomTextContent') }}</p>
        <div class="itemdata">
          <div class="displacement color_box">
            <el-input class="single-des" @input="changeText" :placeholder="$t('DesktopMonitoring.PleaseEnterContent')" v-model="singleDes" />
          </div>
        </div>
      </div>
      <div class="Set_item"  v-if="['sensor', 'progress','graphic','img','chart'].includes(type2)">
        <p>{{ type2 === 'sensor' ? $t('DesktopMonitoring.SelectSensor') : $t('DesktopMonitoring.AssociatedSensor1') }}</p>
        <div class="itemdata">
          <div class="displacement">
            <el-button style="width: auto;padding: 0 30px;" @click="ChangeSensor" class="Sensor-button">
              {{ isDefaultSensor ? $t('Setting.setSensor') : singleName }}
            </el-button>
          </div>
        </div>
      </div>
      <div class="Set_item" v-if="type2 === 'sensor'">
        <p>{{ $t('DesktopMonitoring.SensorUnit') }}</p>
        <div class="itemdata">
          <div class="displacement">
            <el-checkbox label="bold" v-model="selectedUnit" @change="unitChange">{{ $t('DesktopMonitoring.Display') }}</el-checkbox>
          </div>
        </div>
      </div>

      <div v-if="type2 === 'time'">
        <div class="datatime format">
          <p>{{ $t('DesktopMonitoring.TimeSelection') }}</p>
          <div class="itemdata">
            <el-button
              v-for="(item, index) in buttons"
              :key="index"
              :class="{ active: activeIndex === index }"
              @click="activeIndex = index"
            >
              {{ $t(item) }}
            </el-button>
          </div>
        </div>
        <div class="datatime">
          <p>{{ $t('DesktopMonitoring.Format') }}</p>
          <div class="itemdata format">
            <el-button
              v-for="(item, index) in buttons2"
              :key="index"
              :class="{ active: activeIndex2 === index }"
              @click="activeIndex2 = index"
            >
              {{ $t(item) }}
            </el-button>
          </div>
        </div>
        <div class="datatime">
          <p>{{ $t('DesktopMonitoring.Rule') }}</p>
          <div class="itemdata">
            <el-button
              v-for="(item, index) in buttons3"
              :key="index"
              :class="{ active: activeIndex3 === index }"
              @click="activeIndex3 = index"
            >
              {{ $t(item) }}
            </el-button>
          </div>
        </div>
      </div>

      <div v-if="!['img', 'video', 'svg','progress','graphic','chart'].includes(type2)">
        <div class="Set_item"v-if="!['animation', 'graphic','chart'].includes(type2)">
          <p>{{ $t('DesktopMonitoring.Font') }}</p>
          <div class="itemdata">
            <div class="displacement font_box">
              <el-select v-model="selectedFont" :placeholder="$t('DesktopMonitoring.SelectFont')" class="font-select"  @change="updateStyle('fontFamily', selectedFont)" :popper-append-to-body="false">
                <el-option
                  v-for="item in fontValues"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </div>
          </div>
        </div>
        <div class="Set_item" v-if="!['animation', 'graphic','chart'].includes(type2)">
          <p>{{ $t('DesktopMonitoring.FontSize') }}</p>
          <div class="itemdata">
            <div class="displacement color_box">
              <el-input class="Inputsize" @input="updateStyle('fontSize', singleSize)" v-model="singleSize" />
              <strong>px</strong>
            </div>
          </div>
        </div>
        <div class="Set_item" v-if="!['chart'].includes(type2)">
          <p>{{ $t('DesktopMonitoring.Color') }}</p>
          <div class="itemdata">
            <div class="displacement color_box">
              <el-input v-model="Fontcolor" class="Colorbox"></el-input>
              <el-color-picker v-model="Fontcolor" show-alpha :predefine="predefineColors" @change="updateStyle('color', Fontcolor)"></el-color-picker>
            </div>
          </div>
        </div>
        <div class="Set_item" v-if="!['animation', 'graphic','chart'].includes(type2)">
          <p>{{ $t('DesktopMonitoring.Style') }}</p>
          <div class="itemdata">
            <div class="displacement">
              <el-checkbox-group v-model="selectedStyles">
                <el-checkbox label="bold" @change="toggleStyle('fontWeight', 'bold')">{{ $t('DesktopMonitoring.Bold') }}</el-checkbox>
                <el-checkbox label="italic" @change="toggleStyle('fontStyle', 'normal')">{{ $t('DesktopMonitoring.Italic') }}</el-checkbox>
                <el-checkbox label="shadow" @change="toggleShadow">{{ $t('DesktopMonitoring.Shadow') }}</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </div>
      </div>

      <div class="Set_item" v-if="selectedStyles.includes('shadow')">
        <p>{{ $t('DesktopMonitoring.ShadowPosition') }}</p>
        <div class="itemdata">
          <div class="displacement color_box">
            <span>X</span>
            <el-input v-model="shadowX" class="Inputcontent" @input="updateShadow('x', shadowX)"></el-input>
          </div>
          <div class="displacement color_box">
            <span>Y</span>
            <el-input v-model="shadowY" class="Inputcontent" @input="updateShadow('y', shadowY)"></el-input>
          </div>
        </div>
      </div>
      <div class="Set_item" v-if="selectedStyles.includes('shadow')">
        <p>{{ $t('DesktopMonitoring.ShadowEffect') }}</p>
        <div class="itemdata">
          <div class="displacement color_box">
            <span>{{ $t('DesktopMonitoring.Blur') }}</span>
            <el-input v-model="shadowBlur" class="Inputcontent" @input="updateShadow('blur', shadowBlur)"></el-input>
          </div>
        </div>
      </div>
      <div class="Set_item" v-if="selectedStyles.includes('shadow')">
        <p>{{ $t('DesktopMonitoring.ShadowColor') }}</p>
        <div class="itemdata">
          <div class="displacement color_box">
            <el-input v-model="shadowColor" class="Colorbox"></el-input>
            <el-color-picker v-model="shadowColor" show-alpha @change="updateShadow('color', shadowColor)"></el-color-picker>
          </div>
        </div>
      </div>

      <div class="picturevideo" v-if="['img', 'video', 'svg', 'graphic', 'progress','chart'].includes(type2)">
        <div class="Set_item" v-if="type2 === 'img' || type2 === 'video'">
          <p>{{ $t('DesktopMonitoring.SelectFromLocalFiles') }}</p>
          <div class="itemdata">
            <input type="file" ref="fileInput" :accept="type2 === 'img' ? 'image/png , image/gif' : 'video/mp4'" hidden @change="handleFileUpload" />
            <el-button  @click="fileInput?.click()"> 
              {{ fileName || $t(type2 === 'img' ? 'DesktopMonitoring.UploadImage' : 'DesktopMonitoring.UploadVideo') }}
            </el-button>
           
          </div>
        </div>
        <div class="Set_item" v-if="type2 === 'svg'">
          <p>{{ $t('DesktopMonitoring.SelectFromLocalFiles') }}</p>
          <input type="file" ref="svgUpload" accept=".svg" hidden @change="handleSvgUpload" />
          <el-button type="primary"  @click="svgUpload?.click()">  {{ $t('DesktopMonitoring.UploadSVGFile') }}</el-button>
        </div>

        <div class="Set_item">
          <p>{{ $t('DesktopMonitoring.Width') }}</p>
          <div class="itemdata">
            <div class="displacement color_box">
              <el-input class="Inputsize" @input="updateStyle('width', singleSizewide)" v-model="singleSizewide" />
              <strong>px</strong>
            </div>
          </div>
        </div>
        <div class="Set_item">
          <p>{{ $t('DesktopMonitoring.Height') }}</p>
          <div class="itemdata">
            <div class="displacement color_box">
              <el-input class="Inputsize" @input="updateStyle('height', singleSizehigh)" v-model="singleSizehigh" />
              <strong>px</strong>
            </div>
          </div>
        </div>
        <div class="Set_item" v-if="['img',].includes(type2)">
          <p>{{ $t('DesktopMonitoring.Effect') }}</p>
            <div class="itemdata">
                <div class="displacement">
                <!-- <el-checkbox-group v-model="selectedStyles"> -->
                    <el-checkbox label="RotationChange" v-model="selectedRotation"  @change='ChangeRotation'>{{ $t('DesktopMonitoring.Rotation') }}</el-checkbox>
                <!-- </el-checkbox-group> -->
                </div>
            </div>
        </div>

        <div class="Set_item2" v-show="currentSensor.transformShow && type2 === 'img'">
            <span>{{ $t('DesktopMonitoring.WhenTheSensorValue') }}</span>
            <el-input type="number"  @input="updateStyle('Interval', RotationData)" v-model.number="RotationData" class="Inputcontent2"></el-input>
            <el-select v-model="Animationname" :placeholder="$t('DesktopMonitoring.Select')" @change="updateAnimation('names', Animationname)" style="width: 120px">
                <el-option
                v-for="(item, index) in rotationOptions"
                :key="index"
                :label="$t(item.name)"
                :value="index"
                />
            </el-select>
            <el-select v-model="AnimationSeppd"  :placeholder="$t('DesktopMonitoring.Select')" @change="updateAnimation('duration', AnimationSeppd)" style="width: 130px">
                <el-option
                v-for="(item, index)  in SpeedOptions"
                :key="index"
                :label="$t(item.value)"
                :value="index"
                />
            </el-select>

        </div>

        <div class="Set_item2" v-show="currentSensor.transformShow && type2 === 'img'">
            <span>{{ $t('DesktopMonitoring.conditions') }}</span>
            <el-select v-model="Animationname2"  :placeholder="$t('DesktopMonitoring.Select')" @change="updateAnimation('names', Animationname2)" style="width: 120px">
                <el-option
                v-for="(item, index)  in rotationOptions2"
                :key="index"
                :label="$t(item.name)"
                :value="index"
                />
            </el-select>
            <el-select v-model="AnimationSeppd2"  :placeholder="$t('DesktopMonitoring.Select')" @change="updateAnimation('duration', AnimationSeppd2)" style="width: 130px">
                <el-option
                v-for="(item, index)  in SpeedOptions2"
                :key="index"
                :label="$t(item.value)"
                :value="index"
                />
            </el-select>

        </div>


        <div class="Set_item" v-if="type2 === 'progress' ">
            <p>{{ $t('DesktopMonitoring.Corner') }}</p>
            <div class="itemdata">
            <div class="displacement">
                <el-input type="number" v-model.number="borderRadius" class="Inputcontent2"  @input="updateStyle('borderRadius', borderRadius)"></el-input>
            </div>
            </div>
        </div>
        <div class="Set_item"  v-if="['progress', 'graphic'].includes(type2)">
            <p>{{ $t('DesktopMonitoring.BackgroundColor') }}</p>
            <div class="itemdata">
                <div class="displacement color_box ">
                <el-input v-model="backgcolor" class="Colorbox"></el-input>
                <el-color-picker v-model="backgcolor" show-alpha :predefine="predefineColors"  @change="updateStyle('backgroundColor', backgcolor)" ></el-color-picker>
                </div>
            </div>
        </div>
        <div class="Set_item" v-if="type2 === 'progress' ">
            <p>{{ $t('DesktopMonitoring.ProgressColor') }}</p>
            <div class="itemdata">
                <div class="displacement color_box ">
                <el-input v-model="ProgressColor" class="Colorbox"></el-input>
                <el-color-picker v-model="ProgressColor" show-alpha :predefine="predefineColors"  @change="updateStyle('ProgressColor', ProgressColor)" ></el-color-picker>
                </div>
            </div>
        </div>

        <div class="Set_item" v-if="['progress','graphic'].includes(type2)">
            <p>{{ $t('DesktopMonitoring.Effect') }}</p>
            <div class="itemdata">
                <div class="displacement">
                <!-- <el-checkbox-group v-model="selectedStyles"> -->
                    <el-checkbox label="colorChange" v-model="selectedcolor"  @change='Changecolor'>{{ $t('DesktopMonitoring.ChangeColor') }}</el-checkbox>
                <!-- </el-checkbox-group> -->
                </div>
            </div>
        </div>

        <div class="Set_item2" v-show=" currentSensor.Switchcolorsshow && ['progress','graphic'].includes(type2)">
            <span>{{ $t('DesktopMonitoring.When') }}</span>
            <el-select v-model="Numerical"  :placeholder="$t('DesktopMonitoring.Select')" @change="updateStyle('Judgmentitem', Numerical)" style="width: 200px">
                <el-option
                v-for="(item, index) in options"
                :key="index"
                :label="$t(item.value)"
                :value="index"
                />
            </el-select>
            <el-input type="number"  @input="updateStyle('Interval', IntervalData)" v-model.number="IntervalData" class="Inputcontent2"></el-input>
            <el-input v-model="ProgressColor2" class="Colorbox Colorbox_2"></el-input>
            <el-color-picker v-model="ProgressColor2" show-alpha :predefine="predefineColors"  @change="updateStyle('ProgressColor2', ProgressColor2)" ></el-color-picker>
        </div>

        
        <div v-if="type2 === 'chart' ">
          <div class="Set_item">
              <p>{{ $t('DesktopMonitoring.Zigzagcolor') }}</p>
              <div class="itemdata">
                  <div class="displacement">
                  <el-input v-model="gradientColors1" class="Colorbox"></el-input>
                  <el-color-picker v-model="gradientColors1" show-alpha :predefine="predefineColors"  @change="updateStyle('gradientColors1', gradientColors1)" ></el-color-picker>
                  </div>
              </div>
          </div>
          <div class="Set_item">
              <p>{{ $t('DesktopMonitoring.Zigzagcolor1') }}</p>
              <div class="itemdata">
                  <div class="displacement">
                  <el-input v-model="gradientColors2" class="Colorbox"></el-input>
                  <el-color-picker v-model="gradientColors2" show-alpha :predefine="predefineColors"  @change="updateStyle('gradientColors2', gradientColors2)" ></el-color-picker>
                  </div>
              </div>
          </div>
          <div class="Set_item">
            <p>{{ $t('DesktopMonitoring.Zigzagcolor2') }}</p>
              <div class="itemdata">
                  <div class="displacement">
                  <el-input v-model="areaColors1" class="Colorbox"></el-input>
                  <el-color-picker v-model="areaColors1" show-alpha :predefine="predefineColors"  @change="updateStyle('areaColors1', areaColors1)" ></el-color-picker>
                  </div>
              </div>
          </div>
          <div class="Set_item">
            <p>{{ $t('DesktopMonitoring.Zigzagcolor3') }}</p>
              <div class="itemdata">
                  <div class="displacement">
                  <el-input v-model="areaColors2" class="Colorbox"></el-input>
                  <el-color-picker v-model="areaColors2" show-alpha :predefine="predefineColors"  @change="updateStyle('areaColors2', areaColors2)" ></el-color-picker>
                  </div>
              </div>
          </div>
        </div>

      </div>
    </div>

    <div  class="dynamic-box scroll" v-show="htmlshow">
      <div class="progressBox" v-if="['progress'].includes(type2)">
        <el-radio-group  v-model="selectedRadio">
          <div class="GraphicBox"   @click="handleRadioSelect(n)"
              v-for="n in  [1, 2, 3]" :key="n"
              :style="{  border: radioBox === n ? '1px solid #409EFF' : '' }"
              >
              <el-radio  :value="n" ></el-radio>
              <div class=""   v-for="(sensor, index) in Fixeddata.filter(i => i.id === n)"   :key="index"  
                    :style="store.getWrapperStyle(sensor, index)">
                <MonitoringModule
                  :nums="sensor.nums"
                  :textShadow="store.getTextShadow(sensor)"
                  :value="'50'"
                  :processvalue= 50
                  :text="sensor.remark"
                  :unit='sensor.unit'
                  :mediaSrc="sensor.mediaSrc"
                  :custStyle="store.getModuleStyle(sensor)"
                  :unitshow="sensor.unitshow"
                  :show="sensor.showDetails"
                  :sensor="sensor"
                  :switchcolorsshow="sensor.Switchcolorsshow"
                  :transformShow ="sensor.transformShow"
                  :customClass="sensor.class"
                  :id="sensor.id"
              >
              </MonitoringModule>
              </div>
          </div>
        </el-radio-group>
      </div>

      <div class="progressBox" v-if="['graphic'].includes(type2)">
        <el-radio-group  v-model="selectedRadio">
          <div class="GraphicBox"   @click="handleRadioSelect(n)"
              v-for="n in [1]" :key="n"
              :style="{   border: radioBox2 === n ? '1px solid #409EFF' : '' }"
              >
              <el-radio  :value="n" ></el-radio>
              <div class=""   v-for="(sensor, index) in Fixeddata2.filter(i => i.id === n)"   :key="index"  
                    :style="store.getWrapperStyle(sensor, index)">
                <MonitoringModule
                  :nums="sensor.nums"
                  :textShadow="store.getTextShadow(sensor)"
                  :text="sensor.remark"
                  :custStyle="store.getModuleStyle(sensor)"
                  :show="sensor.showDetails"
                  :sensor="sensor"
                  :switchcolorsshow="sensor.Switchcolorsshow"
                  :transformShow ="sensor.transformShow"
                  :customClass="sensor.class"
                  :id="sensor.id"
              >
              </MonitoringModule>
              </div>
          </div>
        </el-radio-group>
      </div>

      <div class="progressBox" v-if="['chart'].includes(type2)">
        <el-radio-group  v-model="selectedRadio">
          <div class="GraphicBox"   @click="handleRadioSelect(n)"
              v-for="n in [1]" :key="n"
              :style="{   border: radioBox3 === n ? '1px solid #409EFF' : '' }"
              >
              <el-radio  :value="n" ></el-radio>
              <div class=""   v-for="(sensor, index) in Fixeddata3.filter(i => i.id === n)"   :key="index"  
                    :style="store.getWrapperStyle(sensor, index)">
                <MonitoringModule
                  :nums="sensor.nums"
                  :textShadow="store.getTextShadow(sensor)"
                  :text="sensor.remark"
                  :custStyle="store.getModuleStyle(sensor)"
                  :show="sensor.showDetails"
                  :sensor="sensor"
                  :switchcolorsshow="sensor.Switchcolorsshow"
                  :transformShow ="sensor.transformShow"
                  :customClass="sensor.class"
                  :id="sensor.id"
                  :parameters="seriesData"
              >
              </MonitoringModule>
              </div>
          </div>
        </el-radio-group>
      </div>
    </div>  

    <div class="btn_Button">
      <div class="btn">
        <el-button type="primary" @click="Nextstep"  v-show="htmlshow">{{ $t('DesktopMonitoring.NextStep') }}</el-button>
        <el-button type="primary" @click="addSensor"  v-show="!htmlshow">{{ $t('DesktopMonitoring.Add') }}</el-button>
        <el-button type="primary" class="cancelback" @click="CancelSensor">{{ $t('messages.cancel') }}</el-button>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, onMounted ,toRaw,computed} from 'vue';
import { ElMessage } from 'element-plus';
import {gamepp} from 'gamepp'
import { Sensor } from '../sharedTypes';
import MonitoringModule from '@/components/MonitoringModule/Newmonitoring.vue'
import useSensorData from '../../Game_DesktopMonitorSet/shared/useSensorData'
// import { storeToRefs } from 'pinia'
const store = useSensorData()
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
// const {sensorRawValues} = storeToRefs(store)
    const props = defineProps({
      type2: {
        type: String,
        default: ''
      },
      htmlshow :{
        type: Boolean,
        default: false
      },
    });
    const radioBox = ref(1)
    const Fixeddata = ref<Sensor[]>([
      {
        id: 1,
        nums: 8,
        type: '进度条',
        type2: 'progress',
        remark: '/',
        sensor: '',
        belong: '',
        page: 1,
        group: false,
        showDetails: true,
        hideDetails: false,
        settop: false,
        showdelete: false,
        enter: true,
        unit: '%',
        style: {
          zIndex: 0,
          width: 150,
          height: 4,
          borderRadius: 4,
          backgroundColor: 'rgba(255,255,255,.2)',
          ProgressColor:'#00FFC6',
          top: 46,
          left: 15,
        },
      },
      {
        id: 2,
        nums: 8,
        type: '进度条',
        type2: 'progress',
        remark: '/',
        sensor: '',
        belong: '',
        page: 1,
        group: false,
        showDetails: true,
        hideDetails: false,
        settop: false,
        showdelete: false,
        enter: true,
        class: 'ProgressShape1',
        unit: '%',
        style: {
          zIndex: 0,
          width: 150,
          height: 24,
          borderRadius: 0,
          backgroundColor: 'rgba(0,0,0,.6)',
          ProgressColor:'rgba(0, 255, 198, 0.6)',
          top: 36,
          left: 15,
        }
      },
      {
        id: 3,
        nums: 8,
        type: '进度条',
        type2: 'progress',
        remark: '/',
        sensor: '',
        belong: '',
        page: 1,
        group: false,
        showDetails: true,
        hideDetails: false,
        settop: false,
        showdelete: false,
        enter: true,
        unit: '%',
        Switchcolorsshow: true,
        style: {
          zIndex: 51,
          width: 150,
          height: 5,
          borderRadius: 0,
          backgroundColor:'rgba(67,107,148,1)',
          ProgressColor:'rgba(255, 255, 255, 1)',
          ProgressColor2:'#FF0000',
          Interval:80,
          Judgmentitem:0,
          top: 45,
          left: 15,
        },
      },
    ]);
    const radioBox2 = ref(1)
    const Fixeddata2 = ref<Sensor[]>([
      {
        id: 1,
        nums: 9,
        type: '图形',
        type2: 'graphic',
        remark: '/',
        sensor: '',
        page: 1,
        group: false,
        showDetails: true,
        hideDetails: false,
        settop: false,
        showdelete: false,
        enter: true,
        style: {
          zIndex: 50,
          fontSize: 12,
          width: 150,
          height: 20,
          backgroundColor: 'rgba(0, 255, 198, 0.6)',
          ProgressColor2:'#FF0000',
          Interval:80,
          Judgmentitem:0,
          top: 40,
          left: 15,
        },
      },
    ]);

    const radioBox3 = ref(1)
    const Fixeddata3 = ref<Sensor[]>([
      {
        id: 1,
        nums: 10,
        type: '折线图',
        type2: 'chart',
        remark: '/',
        sensor: '',
        page: 1,
        group: false,
        showDetails: true,
        hideDetails: false,
        settop: false,
        showdelete: false,
        enter: true,
        style: {
          zIndex: 28,
          fontFamily: 'AeeAndCui',
          fontStyle: 'normal',
          fontWeight: 'normal',
          fontSize: 18,
          // color: 'rgba(0,0,0,1)',
          gradientColors1:'rgba(77,158, 103,1)',
          gradientColors2: 'rgba(77, 158, 103,1)',
          areaColors1: 'rgba(186, 0, 255, 0)',
          areaColors2: 'rgba(186, 0, 255, 0)',
          top: 36,
          left: 15,
          width:150,
          height: 30,
        },
      },
    ]);
    const seriesData = [40,30,40,55,32,45,60,10,70,60]

    const singleName = ref('');
    const sensors = ref<Sensor[]>([]);
    const singleDes = ref('');
    const selectedUnit = ref(true);
    const selectedFont = ref('Microsoft YaHei');
    let fontValues = ref<Array<any>>([])
    const singleSize = ref('12');
    const Fontcolor = ref('rgba(255, 255, 255, 1)');
    const selectedStyles = ref<('bold' | 'italic' | 'shadow')[]>([]);
    const predefineColors = ref([
      '#ff4500', '#ff8c00', '#ffd700', '#90ee90', '#00ced1', '#1e90ff', '#c71585', 'rgba(255, 69, 0, 0.68)',
      'rgb(255, 120, 0)', 'hsv(51, 100, 98)', 'hsva(120, 40, 94, 0.5)', 'hsl(181, 100%, 37%)', 'hsla(209, 100%, 56%, 0.73)', '#c7158577'
    ]);
    const singleSizewide = ref(300);
    const singleSizehigh = ref(300);
    const mediaSrc = ref('');
    const processedSvg = ref('');
    const shadowX = ref(0);
    const shadowY = ref(1);
    const shadowBlur = ref(6);
    const shadowColor = ref('rgba(255, 255, 255, 1)');

    const selectedRotation = ref<boolean>(false)
    const RotationData = ref<number>(0)
    const Animationname = ref<number>(0)
    const Animationname2 = ref<number>(1)
    const AnimationSeppd = ref<number>(0)
    const AnimationSeppd2 = ref<number>(1)
    const rotationOptions = ref([
      { name: 'DesktopMonitoring.Clockwise',},
      {name: 'DesktopMonitoring.Counterclockwise', }
    ])
    const SpeedOptions = ref([
      {value: 'DesktopMonitoring.QuickRotation',},
      { value: 'DesktopMonitoring.SlowRotation',},
      { value: 'DesktopMonitoring.StopRotation',},
    ])
    const rotationOptions2 = ref([
      { name: 'DesktopMonitoring.Clockwise',},
      {name: 'DesktopMonitoring.Counterclockwise', }
    ])
    const SpeedOptions2 = ref([
      {value: 'DesktopMonitoring.QuickRotation',},
      { value: 'DesktopMonitoring.SlowRotation',},
      { value: 'DesktopMonitoring.StopRotation',},
    ])
    const borderRadius = ref<number>(0)
    const backgcolor = ref<string>('rgba(255, 255, 255, 0)')
    const ProgressColor = ref<string>('#00FFC6')
    const ProgressColor2 = ref<string>('#FF0000')
    const selectedcolor = ref<boolean>(false)
    const Numerical = ref<number>(0)
    const IntervalData = ref<number>(50)
    const gradientColors1 = ref<string>('rgba(77,158, 103,1)')
    const gradientColors2 = ref<string>('rgba(77, 158, 103,1)')
    const areaColors1 = ref<string>('rgba(186, 0, 255, 0)')
    const areaColors2 = ref<string>('rgba(186, 0, 255, 0)')
    const options = ref([
      {
        value: 'DesktopMonitoring.SensorValue',
      },
      {
        value: 'DesktopMonitoring.SensorValue1',
      },
      {
        value: 'DesktopMonitoring.SensorValue2',
      },
    ])
    const createDefaultSensor = (): Sensor => ({
      id: 0,
      nums: 1,
      page: 1,
      type: '',
      type2: '',
      remark: '',
      sensor: '',
      parameters: '',
      unit: '',
      unitshow: true,
      mediaSrc: '',
      processedSvg: '',
      group: false,
      showDetails: true,
      hideDetails: false,
      settop: false,
      showdelete: false,
      enter: false,
      belong: '',
      transformShow: false,
      Switchcolorsshow: false,
      class: '',
      style: {
        zIndex: 1,
        fontSize: 12,
        color: '#ffffff',
        top: 0,
        left: 0,
        width: 0,
        height: 0,
        fontFamily: 'Microsoft YaHei',
        fontWeight: 'normal',
        fontStyle: 'normal',
        strokeColor: 'rgba(255, 255, 255, 1)',
        pathFills: [],
        borderRadius: 0,
        backgroundColor: 'rgba(0,0,0,.6)',
        ProgressColor:'rgba(0, 255, 198, 0.6)',
        ProgressColor2:'#FF0000',
        Judgmentitem:0,
        Interval: 0,
        gradientColors1:'rgba(77,158, 103,1)',
        gradientColors2: 'rgba(77, 158, 103,1)',
        areaColors1: 'rgba(186, 0, 255, 0)',
        areaColors2: 'rgba(186, 0, 255, 0)',
        animation: {
          names: 0,
          names2: 1,
          duration: 0,
          duration2: 1,
        },
        shadow: {
          enabled: false,
          x: 0,
          y: 1,
          blur: 6,
          color: 'rgba(0,0,0,0.1)',
        },
      },
      timeType: 0,
      timeFormat: 0,
      timeRule: 0,
    });
    const currentSensor = ref<Sensor>(createDefaultSensor());
    const buttons = ref<string[]>(['DesktopMonitoring.SystemTime', 'DesktopMonitoring.China', 'DesktopMonitoring.America', 'DesktopMonitoring.Russia', 'DesktopMonitoring.Britain','DesktopMonitoring.France'])
    const activeIndex = ref<number>(0)
    const buttons2 = ref<string[]>(['DesktopMonitoring.DateAndTime', 'DesktopMonitoring.Time', 'DesktopMonitoring.Date', 'DesktopMonitoring.Week', 'DesktopMonitoring.DateAndTimeAndWeek','DesktopMonitoring.TimeAndWeek'])
    const activeIndex2 = ref<number>(0)
    const buttons3 = ref<string[]>(['DesktopMonitoring.Hour12', 'DesktopMonitoring.Hour24'])
    const activeIndex3 = ref<number>(0)
    const radio = ref(1);
    const fileInput = ref<HTMLInputElement | null>(null)
    const svgUpload = ref<HTMLInputElement | null>(null)
    interface TypeMapping {
      name: string;
      num: number;
    }
    onMounted(() => {
      const activeId = Number(localStorage.getItem('activeMonitorId'));
      const savedSensors = JSON.parse(localStorage.getItem(`sensorSettings_${activeId}`) || '[]');
      if(savedSensors){
        sensors.value = savedSensors;
      }
      loadFontFamilies();
      console.log('数据',sensors.value)
      gamepp.webapp.onInternalAppEvent.addEventListener(async (value:any) => {
        console.log(value,'传感器')
        if(value.action === 'Change'){
            console.log(value,value.action,'传感器111')
            currentSensor.value.sensor = value.value.name
            singleName.value = value.value.name
            currentSensor.value.enter = false
            currentSensor.value.belong = value.value.all.name
        }
      })
    });
    const isDefaultSensor = computed(() => !singleName.value);
    const selectedRadio = computed({
      get() {
        return props.type2 === 'progress' ? radioBox.value : props.type2 === 'chart'? radioBox3.value : radioBox2.value;
      },
      set(newValue) {
        if (props.type2 === 'progress') {
          radioBox.value = newValue;
        } else if (props.type2 === 'graphic') {
          radioBox2.value = newValue;
        }else{
          radioBox3.value = newValue;
        }
      }
    });

    const handleRadioSelect = (value: number) => {
      if (props.type2 === 'progress') {
        radioBox.value = value;
      } if (props.type2 === 'graphic') {
          radioBox2.value = value;
        }else{
          radioBox3.value = value;
        }
    };


    const emit = defineEmits(['toggleHtmlShow']);
    const Nextstep = () => {
       emit('toggleHtmlShow');
       if (['progress','graphic', 'chart'].includes(props.type2)) {
        // const selectedItem =(props.type2 === 'progress' ? Fixeddata :props.type2 === 'chart'? Fixeddata3 : Fixeddata2).value.find(item => item.id === (props.type2 === 'progress' ? radioBox : props.type2 === 'chart'? radioBox3 : radioBox2).value);
        let dataSource, selectedId;
        if (props.type2 === 'progress') {
          dataSource = Fixeddata.value;
          selectedId = radioBox.value;
        } else if (props.type2 === 'graphic') {
          dataSource = Fixeddata2.value;
          selectedId = radioBox2.value;
        } else {
          dataSource = Fixeddata3.value;
          selectedId = radioBox3.value;
        }
        const selectedItem = dataSource.find(item => item.id === selectedId);
        console.log(selectedItem,'selectedItem')
        if (selectedItem) {
          singleSizewide.value = selectedItem.style.width ?? 0;
          singleSizehigh.value = selectedItem.style.height ?? 0;
          borderRadius.value = selectedItem.style.borderRadius ?? 0;
          backgcolor.value =  selectedItem.style.backgroundColor ?? 'rgba(255, 255, 255, 0)';
          ProgressColor2.value = selectedItem.style.ProgressColor2 ?? '#FF0000';
          if (props.type2 === 'progress') {
            ProgressColor.value =  selectedItem.style.ProgressColor ?? '#00FFC6';
          }
          if (props.type2 === 'chart') {
            gradientColors1.value = selectedItem.style.gradientColors1 ?? 'rgba(77, 158, 103,1)';
            gradientColors2.value = selectedItem.style.gradientColors2 ?? 'rgba(77, 158, 103,1)';
            areaColors1.value = selectedItem.style.areaColors1 ?? 'rgba(186, 0, 255, 0)';
            areaColors2.value = selectedItem.style.areaColors2 ?? 'rgba(186, 0, 255, 0)';
            
          }
        }
      }
    }

    const ChangeSensor = () => {
      window.localStorage.setItem('SensorType', 'Change');
      if(gamepp.webapp.windows.isValid.sync("Sensorchoose")){
          gamepp.webapp.windows.close.sync('Sensorchoose')
          setTimeout(() => {
              gamepp.webapp.windows.show.sync('Sensorchoose',false)
          }, 300);
      }
        gamepp.webapp.windows.show.sync('Sensorchoose',false)
        window.localStorage.removeItem('SensorManageType') // {{}}此时传感器操作的种类
        window.localStorage.removeItem('SensorManageData') // 更改此时传感器操作的种类
        window.localStorage.removeItem('SensorManageFilter') // 更改此时传感器操作的种类
        gamepp.webapp.windows.close.sync('hardware_setupsensor')

    };

    const loadFontFamilies = async () => {
      try {
        // const fontData = gamepp.getFontFamilys();
        // fontValues.value = fontData || fontValues.value;
        // let arr = new Set()
        let arr = new Set(["QuartzRegular","Hybriddd","AeeAndCui"]); 
        try {
          const availableFonts = await window.queryLocalFonts();
          for (const fontData of availableFonts) {
            if (fontData.family.toLowerCase().startsWith('st')){
              continue
            }
            arr.add(fontData.family) // 去重
          }
          fontValues.value = Array.from(arr)
        } catch (err) {
          console.error('获取字体失败', err);
        }
      } catch (error) {
        
      }
    };

    const updateStyle = (property: string, value: any) => {
      if (currentSensor.value.style) {
        currentSensor.value.style[property] = value;
      }
    };

    const toggleStyle = (styleProperty: string, styleValue: string) => {
      const currentValue = currentSensor.value.style[styleProperty] || (styleProperty === 'fontStyle' ? 'normal' : '400');
      let newValue: string;
      if (styleProperty === 'fontStyle') {
        newValue = currentValue === 'italic' ? 'normal' : 'italic';
      } else {
        newValue = currentValue === styleValue ? 'normal' : styleValue;
      }
      updateStyle(styleProperty, newValue);
    };

    const updateShadow = (property: string, value: any) => {
      if (!currentSensor.value.style.shadow) {
        currentSensor.value.style.shadow = {};
      }
      currentSensor.value.style.shadow[property] = value;
    };

    const updateAnimation = (property: string, value: any) => {
      if (!currentSensor.value.style.animation) {
        currentSensor.value.style.animation = {}
      }
      currentSensor.value.style.animation[property as keyof typeof currentSensor.value.style.animation] = value
    }

    const ChangeRotation = () => {
        currentSensor.value.transformShow = selectedRotation.value
    }

    const Changecolor = () => {
      currentSensor.value.Switchcolorsshow = selectedcolor.value
    }

    const toggleShadow = () => {
      currentSensor.value.style.shadow.enabled = !currentSensor.value.style.shadow.enabled;
    };

    const changeText = () => {
      currentSensor.value.remark = singleDes.value;
    };

    const unitChange = () => {
      currentSensor.value.unitshow = selectedUnit.value;
    };
    const fileName = ref('');

    const handleFileUpload = (e: Event) => {
      const target = e.target as HTMLInputElement;
      const file = target.files?.[0];

      if (!file) return;
      const allowedTypes = ['image/png', 'image/gif', 'video/mp4'];
      if (!allowedTypes.includes(file.type)) {
        ElMessage.error('只支持 PNG、GIF 和 MP4 格式');
        return;
      }
      fileName.value = file.name;
      const reader = new FileReader();
      reader.onload = (event) => {
        const base64 = event.target?.result as string;
        mediaSrc.value = base64; // 更新 mediaSrc
        currentSensor.value.mediaSrc = base64; 
      };

      reader.onerror = () => {
        ElMessage.error('文件读取失败');
      };

      reader.readAsDataURL(file); 
    };
    const handleSvgUpload = (e: Event) => {
      const target = e.target as HTMLInputElement;
      const file = target.files?.[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = (e) => {
        const svgString = e.target?.result as string;
        const parser = new DOMParser();
        const svgDoc = parser.parseFromString(svgString, "image/svg+xml");

        const paths = Array.from(svgDoc.querySelectorAll('path'));
        currentSensor.value.style.pathFills = paths.map(p => p.getAttribute('fill') || '#000');

        const strokePath = paths.find(p => p.hasAttribute('stroke'));
        if (strokePath) {
          currentSensor.value.style.strokeColor = strokePath.getAttribute('stroke') || 'rgba(255, 255, 255, 1)';
        }

        currentSensor.value.processedSvg = svgDoc.documentElement.outerHTML;
        processedSvg.value = currentSensor.value.processedSvg;
      };
      reader.readAsText(file);
    };

    const getNewId = () => {
      return sensors.value.length ? Math.max(...sensors.value.map(s => s.id)) + 1 : 1;
    };

    const addSensor = () => {
      if (!singleDes.value && props.type2 === 'text') {
        ElMessage.error('请输入文本内容');
        return;
      }
      if (!mediaSrc.value && (props.type2 === 'img' || props.type2 === 'video')) {
        ElMessage.error('请上传媒体文件');
        return;
      }
      if (!processedSvg.value && props.type2 === 'svg') {
        ElMessage.error('请上传SVG文件');
        return;
      }
      if ((['progress','sensor','chart'].includes(props.type2)) && !currentSensor.value.sensor) {
        ElMessage.error('请选择传感器');
        return;
      }
      if (props.type2 === 'text' || props.type2 === 'sensor') {
        if (selectedStyles.value.includes('shadow')) {
        if (
          shadowX.value === 0 && 
          shadowY.value === 1 && 
          shadowBlur.value === 6 && 
          shadowColor.value === 'rgba(255, 255, 255, 1)'
        ) {
          ElMessage.error('阴影设置不能全为默认值');
          return;
        }
        currentSensor.value.style.shadow = {
          enabled: true,
          x: shadowX.value,
          y: shadowY.value,
          blur: shadowBlur.value,
          color: shadowColor.value
        };
      } else {
        currentSensor.value.style.shadow = { 
          enabled: false,
          x: shadowX.value,
          y: shadowY.value,
          blur: shadowBlur.value,
          color: shadowColor.value
        };
      }
    }
      const newSensor = { ...currentSensor.value };
      // const newSensor = { ...toRaw(currentSensor.value) };
      console.log('newSensor', newSensor);
      newSensor.id = getNewId();
      const typeMappings: Record<string, TypeMapping> = {
        text: { name: '自定义文本', num: 1 },
        sensor: { name: '传感器', num: 2 },
        img: { name: '图片', num: 3 },
        video: { name: '视频', num: 4 },
        svg: { name: 'SVG', num: 5 },
        time: { name: '时间', num: 6 },
        progress: { name: '进度条', num: 8 },
        graphic: { name: '图形', num: 9 },
        chart: { name: '折线图', num: 10 },
      };
      newSensor.type = typeMappings[props.type2]?.name;
      newSensor.nums = typeMappings[props.type2]?.num;
      // newSensor.type = props.type2 === 'text' ? '自定义文本' : props.type2 === 'sensor' ? '传感器' : props.type2 === 'img' ? '图片' : props.type2 === 'video' ? '视频' : props.type2 === 'svg' ? 'SVG' : '时间';
      // newSensor.nums = props.type2 === 'text' ? 1 : props.type2 === 'sensor' ? 2 : props.type2 === 'img' ? 3 : props.type2 === 'video' ? 4 : props.type2 === 'svg' ? 5 : 6;
      newSensor.type2 = props.type2;
      newSensor.remark = props.type2 === 'text' ? singleDes.value : '/';
      newSensor.sensor = ['sensor', 'img', 'progress', 'graphic','chart'].includes(props.type2) ? 
      (isDefaultSensor.value && (['img', 'graphic'].includes(props.type2)) ? '/' : singleName.value): '/';
      newSensor.page = radio.value;
      newSensor.unit = currentSensor.value.unit;
      newSensor.unitshow = selectedUnit.value;
      newSensor.mediaSrc = mediaSrc.value;
      newSensor.group = false;
      newSensor.showDetails = true;
      newSensor.hideDetails = false;
      newSensor.settop = false;
      newSensor.showdelete = false;
      newSensor.enter = false;
      newSensor.style = { ...currentSensor.value.style };
      // newSensor.style = { ...toRaw(currentSensor.value.style) }; 
      newSensor.style.fontSize = parseInt(singleSize.value);
      newSensor.style.fontFamily = selectedFont.value;
      newSensor.style.color = Fontcolor.value;
      newSensor.style.top = 0;
      newSensor.style.left = 0;
      newSensor.style.zIndex = getNewId();

      //图片旋转效果属性
      newSensor.transformShow = props.type2 === 'img' ? selectedRotation.value : undefined;
      newSensor.style.Interval = RotationData.value;
      newSensor.style.animation = {
        names: Animationname.value,
        duration: AnimationSeppd.value,
        names2: Animationname2.value,
        duration2: AnimationSeppd2.value,
        Interval: RotationData.value,
      };

      //进度条/图形效果属性
      newSensor.style.borderRadius = borderRadius.value;
      newSensor.style.backgroundColor = backgcolor.value;
      newSensor.style.ProgressColor = ProgressColor.value;
      newSensor.style.ProgressColor2 = ProgressColor2.value;
      newSensor.style.Numerical = Numerical.value;
      newSensor.style.Interval = IntervalData.value;
      newSensor.Switchcolorsshow = selectedcolor.value;
      if (['progress','graphic','chart'].includes(props.type2)) {
        // const selectedItem =(props.type2 === 'progress' ? Fixeddata :props.type2 === 'chart' ? Fixeddata3 : Fixeddata2).value.find(item => item.id === (props.type2 === 'progress' ? radioBox : props.type2 === 'chart'? radioBox3 : radioBox2).value);
        let dataSource, selectedId;
        if (props.type2 === 'progress') {
          dataSource = Fixeddata.value;
          selectedId = radioBox.value;
        } else if (props.type2 === 'graphic') {
          dataSource = Fixeddata2.value;
          selectedId = radioBox2.value;
        } else {
          dataSource = Fixeddata3.value;
          selectedId = radioBox3.value;
        }
        const selectedItem = dataSource.find(item => item.id === selectedId);
        if (selectedItem) {
          newSensor.style.width = singleSizewide.value;
          newSensor.style.height = singleSizehigh.value;
          newSensor.style.borderRadius = borderRadius.value;
          newSensor.style.backgroundColor = backgcolor.value;
          newSensor.style.ProgressColor2 = ProgressColor2.value;
          if (props.type2 === 'progress') {
            newSensor.style.ProgressColor =  ProgressColor.value;
          }
          if (selectedItem.class !== '') {
            newSensor.class = selectedItem.class;
          }
          if (props.type2 === 'chart') {
            gradientColors1.value = selectedItem.style.gradientColors1 ?? 'rgba(77, 158, 103,1)';
            gradientColors2.value = selectedItem.style.gradientColors2 ?? 'rgba(77, 158, 103,1)';
            areaColors1.value = selectedItem.style.areaColors1 ?? 'rgba(186, 0, 255, 0)';
            areaColors2.value = selectedItem.style.areaColors2 ?? 'rgba(186, 0, 255, 0)';
          }
        }
      }

      if (['img', 'svg', 'video'].includes(props.type2)) {
        newSensor.style.width = singleSizewide.value;
        newSensor.style.height = singleSizehigh.value;
      }

      newSensor.timeType = activeIndex.value
      newSensor.timeFormat = activeIndex2.value
      newSensor.timeRule = activeIndex3.value

      sensors.value.unshift(newSensor);

      const data = toRaw(sensors.value)
      console.log(data);
      
      const obj = {
        type: 'sensors',
        data: data,
      };
      console.log('obj', obj);

      gamepp.webapp.sendInternalAppEvent.promise('desktop_monitor_setting', JSON.parse(JSON.stringify(obj)));
      // localStorage.setItem('sensorSettings', JSON.stringify(sensors.value));

      resetForm();
      CancelSensor();
    };

    const resetForm = () => {
      radio.value = 1;
      singleDes.value = '';
      singleName.value = '';
      selectedUnit.value = true;
      selectedFont.value = 'Microsoft YaHei';
      singleSize.value = '12';
      Fontcolor.value = 'rgba(255, 255, 255, 1)';
      selectedStyles.value = [];
      singleSizewide.value = 300;
      singleSizehigh.value = 300;
      mediaSrc.value = '';
      processedSvg.value = '';
      shadowX.value = 0;
      shadowY.value = 1;
      shadowBlur.value = 6;
      shadowColor.value = 'rgba(255, 255, 255, 1)';
      selectedRotation.value = false;
      RotationData.value = 0
      Animationname.value = 0
      Animationname2.value = 1
      AnimationSeppd.value = 0
      AnimationSeppd2.value = 1
      backgcolor.value = 'rgba(255, 255, 255, 0)';
      ProgressColor.value = '#00FFC6';
      ProgressColor2.value = '#FF0000';
      Numerical.value = 0;
      IntervalData.value = 0;
      selectedcolor.value = false;
      radioBox.value = 1;
      borderRadius.value = 0;
      gradientColors1.value = 'rgba(77, 158, 103,1)';
      gradientColors2.value = 'rgba(77, 158, 103,1)';
      areaColors1.value = 'rgba(186, 0, 255, 0)';
      areaColors2.value = 'rgba(186, 0, 255, 0)';
    };

    const CancelSensor = () => {
      gamepp.webapp.windows.close.sync('new_component');
    };
</script>
  
<style scoped  lang="scss">
    .dynamic-content{
        width: 100%;
        .dynamic-box{
          padding: 5px 20px 20px 20px;
          box-sizing: border-box;
          height: 422px;
          overflow: auto;
        }
        .scroll::-webkit-scrollbar {
          width: 5px;
          transition: 0.25s;
        }
        .scroll::-webkit-scrollbar-thumb {
            background: #71738C;
            border-radius: 3px;
        }
        .scroll::-webkit-scrollbar-thumb:hover {
            background-color: #71738C;
        }
        .scroll::-webkit-scrollbar-track {
            background-color: #2D2E39;
            width: 2px;
        }
        .Set_item{
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 20px;
        p{
          font-size: 12px;
          color: #999999;
        }
        .itemdata{
          display: flex;
          align-items: center;
          gap: 20px;
          .displacement{
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #fff;
            .Colorbox{
              margin-right: 10px;
            }
             span{
              margin-right: 10px;
             }
             strong{
              margin-left: 5px;
              font-weight: normal;
             }
            .Inputcontent{
              width: 70px;
              height: 28px;
              border-radius: 4px;
              background: #171B20;
              border: none;
            }
            .single-des{
              width: 220px;
              height: 28px;
              border-radius: 4px;
            }
          }
        }
      }
      .btn_Button{
        display: flex;
        padding-right: 20px;
        .btn{
          margin-left: auto;
          .cancelback{
            background: transparent;
            border: 1px solid #566B92;
            color: rgba(64, 158, 255,.8);
          }
        }
      }
      .datatime{
        margin-bottom: 15px;
        p{
          font-size: 12px;
          color: #999999;
        }
        .itemdata{
          margin-top: 10px;
          .el-button{
            font-size: 12px;
            background: #354054;
            border: none;
            color: #fff;
            &:hover{
              background: #566B92!important;
            }
          }
          .active {
              background-color: #566B92; 
              color: #fff;
            }
        }
      }
      .picturevideo{
        .el-button{
          background: #354054;
          border: none;
          color: #fff;
          &:hover{
            background: #566B92!important;;
          }
        }
      }
      .GraphicBox{
        border: 1px dashed #999999;
        box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
        width: 180px;
        height: 90px;
        display: flex;
        flex-direction: column;
        cursor: pointer;
        box-sizing: border-box;
        position: relative;
      }
    }
   .format{
    .el-button{
      margin: 0 12px 10px 0;
    }
   }
   .Set_item2{
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 20px;
        color: #fff;
        .Colorbox_2{
          width: 180px;
        }
    }
    .Inputcontent2{
        width: 70px;
        height: 28px;
        border-radius: 4px;
        background: #171B20;
        border: none;
    }
</style>
<style lang="scss">
.dynamic-content{
    .el-input__inner {
        background: #354054;
    }
    .el-button{
      padding: 9px 30px;
      font-weight: 400;
      background: #354054;
      border: none;
    }
    .color_box{
      .el-input__inner{
        background: #171b20!important;
      }
    }
    .Colorbox_2{
      .el-input__inner{
        background: #3e4050!important;
      }
    }
    .Colorbox{
      .el-input__inner{
        background: #3e4050!important;
      }
    }
} 
.dynamic-content{
  .el-select-dropdown{
      background: #354054;
      border: none;
    }
    .el-select-dropdown__item{
      color: #fff;
    }
    .el-select-dropdown__item.selected {
        color: #409eff!important;
    }
    .el-select-dropdown__item.hover, .el-select-dropdown__item:hover {
      background-color: #566b92;
    }
    .el-select-dropdown__wrap {
        max-height: 234px;
    }
    .popper__arrow{
      border-top-color:transparent!important;
      border-bottom-color:transparent!important;
    }
    .popper__arrow:after{
      border-top-color: #566b92!important;
      border-bottom-color:#566b92!important;
    }
    .el-input__wrapper{
      padding: 0!important;
      box-shadow: none!important;
      background-color: transparent!important;
    }
    .el-button{
      &:hover{
        background-color: #566b92!important;
        color:  #fff!important;
      }
    }
}
.font-select-dropdown{
  background: #354054;
  border: none;
  .el-select-dropdown__item{
    color: #fff;
  }
}

// .font-select-dropdown .el-select-dropdown__item.is-selected {
   
// }
.font-select-dropdown .el-select-dropdown__item.is-hovering {
    background-color: #566b92;
}
.font-select-dropdown .el-popper.is-light .el-popper__arrow:before{
  background: #354054!important;
  border: 1px solid #354054!important;
}
.font-select-dropdown .el-select-dropdown__wrap{
  max-height: 260px;
}
.font-select-dropdown {
  .el-select__popper.el-popper{
      border:none!important
    }
    .el-popper.is-light{
      border: none !important;
    }
}

.Colorbox .el-input__wrapper{
  padding: 0;
  background: transparent;
  box-shadow:none
}

.font_box{
  .el-select__wrapper{
    box-shadow: none!important;
    background-color: #354054;
  }
  .el-select__selection{
      width: 150px;
      background: #354054;
  }
  .el-select__placeholder{
    color: #fff!important;
  }  
}
.Inputcontent,.Inputcontent2 {
    .el-input__inner {
        width: 70px;
        height: 28px;
        border-radius: 4px;
        background: #171b20;
        border: none;
        color: #fff;
        text-align: center;
    }
}
.Colorbox2{
    width: 190px;
    .el-input__inner{
        background: #3e4050;
        padding: 0 10px;
        border-radius: 4px;
        color: #fff;
        text-align: center;    
    }
  
}
.Set_item2{
  .el-select__wrapper{
    box-shadow: none !important;
    background-color: #354054 !important;
  }
  .el-select__placeholder{
    color: #fff!important;
  }
}
.dynamic-content{
  --el-fill-color-blank: #3E4050;
  --el-border-color: #3E4050;
  --el-border-color-hover: #3E4050;
  --el-color-primary: #3E4050;

  :deep(.el-input__wrapper) {
      background: #22232E;
      box-shadow: 0 0 0 1px #22232E inset;
  }

  :deep(.el-radio) {
      --el-radio-input-bg-color: #fff;
  }

  :deep(.el-radio__input.is-checked .el-radio__inner) {
      background: #409EFF;
  }

  :deep(.el-radio__input.is-checked+.el-radio__label) {
      color: #fff;
      font-size: 12px;
  }

  :deep(.el-slider__bar) {
      background-color: #409EFF;
      z-index: 1;
  }

  :deep(.el-radio__label) {
      font-size: 12px;
  }

  :deep(.el-checkbox__label) {
      color: #fff;
      font-size: 12px;
  }

  :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
      background-color: #409EFF;
      border-color: #409EFF;
  }

}
.el-checkbox__input.is-checked+.el-checkbox__label{
  color:  #fff!important;
}
.new_component .is-checked .el-checkbox__inner{
  border: 1px solid #409eff!important;
  background-color: #409EFF!important;
}
.GraphicBox{ 
  .el-radio{
    margin-left: auto;
    margin-right: 5px;
  }
  .el-radio__input.is-checked .el-radio__inner {
    background: #409eff;
  }
}
.progressBox .el-radio-group{
  display: flex;
  gap: 7px;
}
</style>
  