let interval = null
let isReceiving = false
let nextTimeReceive = 43200
let isGetEmailStatus = true
let sendEmailSwitch = false
let sleepTime = JSON.parse(localStorage.getItem('sleepTime'))||86400
console.warn('间隔时间',sleepTime);


localStorage.setItem('lineupList', '[]')
nextTimeReceive = sleepTime
localStorage.setItem('nextTimeReceive', sleepTime)

if (!localStorage.getItem('allGameList')) {
  localStorage.setItem('allGameList', '[]')
}

if (localStorage.getItem('account') === null || JSON.parse(localStorage.getItem('account')).length !== 6) {
  localStorage.setItem('account', "[{\"persist\":\"persist:B6976E9166D9F496497780A0540833B5\",\"platform\":\"epic\",\"bindDate\":\"\",\"platform_name\":\"\",\"active\":false,\"loading\":false,\"loginStatus\":false,\"idle\":true,\"localIndex\":0},{\"persist\":\"persist:750E5870F1ED4F21574CFC35DC6CAF05\",\"platform\":\"epic\",\"bindDate\":\"\",\"platform_name\":\"\",\"active\":false,\"loading\":false,\"loginStatus\":false,\"idle\":true,\"localIndex\":1},{\"persist\":\"persist:8F0F22FCFD202C017FA9E4F3244C074B\",\"platform\":\"epic\",\"bindDate\":\"\",\"platform_name\":\"\",\"active\":false,\"loading\":false,\"loginStatus\":false,\"idle\":true,\"localIndex\":2},{\"persist\":\"persist:6C18E7EED1BC08121CA763FD41A1FE5D\",\"platform\":\"steam\",\"bindDate\":\"\",\"platform_name\":\"\",\"active\":false,\"loading\":false,\"loginStatus\":false,\"idle\":true,\"localIndex\":3},{\"persist\":\"persist:1875FC35367415A2A6E81A1E66270668\",\"platform\":\"steam\",\"bindDate\":\"\",\"platform_name\":\"\",\"active\":false,\"loading\":false,\"loginStatus\":false,\"idle\":true,\"localIndex\":4},{\"persist\":\"persist:BF40CD8E6CB17A3D90FCAC0E8075B74F\",\"platform\":\"steam\",\"bindDate\":\"\",\"platform_name\":\"\",\"active\":false,\"loading\":false,\"loginStatus\":false,\"idle\":true,\"localIndex\":5}]")
}

refreshHisttoryList()

intervalFunc(sleepTime*1000)

autoReceiveTimer()

console.warn('执行喜加一脚本');
// setTimeout(() => {
//     processGameList()
// }, 2000);
    //自动刷新游戏脚本
    let timerGame = setInterval(() => {
      const isManual = gamepp.setting.getInteger(408) === 0
      if(!isManual){
        console.warn('刷新喜加一游戏列表：自动');
        processGameList()
      }
      }, sleepTime*1000);

setTimeout(() => {
  startReceive()
}, 10000);

async function startReceive(){
  const isManual = gamepp.setting.getInteger(408) === 0
  if (!isManual) {
    console.warn('软件启动自动领取一次');
    gamepp.utils.sendstatics.promise(50004)
    const lineupList = await createLineup(true)
    
    startReceiveGame(lineupList, false)
  
  }
}

gamepp.freegame.onStartGetGame.addEventListener(async (value) => {
  console.log('后台接收', value)
  if (value.page === 'receiveGame' && value.type === 'processGameList') {
    processGameList()
  } else if (value.page === 'receiveGame' && value.type === 'manualStart') {
    gamepp.utils.sendstatics.promise(50003)
    const lineupList = await createLineup()
    if (lineupList.length === 0) {
      return
    }
    startReceiveGame(lineupList, true)
    setTimeout(() => {
      refreshHisttoryList()
      processGameList()
    }, 60000);
  } else if (value.page === 'receiveGame' && value.type === 'autoStart') {
    if (isReceiving) {
      const interval = setInterval(async () => {
        if (isReceiving) {
          console.log('跳过本次执行')
        } else {
          console.log('开始执行自动领取')
          gamepp.utils.sendstatics.promise(50004)
          const lineupList = await createLineup()
          if (lineupList.length === 0) {
            refreshPersist()
          }
          startReceiveGame(lineupList, false)
          setTimeout(() => {
            processGameList()
          }, 60000);
          clearInterval(interval)
        }
      }, 60000);
    } else {
      gamepp.utils.sendstatics.promise(50004)
      const lineupList = await createLineup()
      if (lineupList.length === 0) {
        refreshPersist()
      }
      startReceiveGame(lineupList, false)
      setTimeout(() => {
        processGameList()
      }, 60000);
    }
  } else if (value.page === 'receiveGame' && value.type === 'sendEmailSwitch') {
    sendEmailSwitch = value.value
  }
})


// -------------------------主要功能-------------------------- //
// 处理游戏列表数据
async function processGameList () {
  console.warn('开始生成领取游戏列表')
  let gameList = await getGameList()
  let GameidArr = []
  for(let j=0;j<gameList.length;j++){
      GameidArr.push(gameList[j].game_name)
  }
  const localGameList = JSON.parse(localStorage.getItem('allGameList'))
  const now = new Date();
  // 筛选重复游戏
  for(let i = 0;i < localGameList.length ;i++)
    {
      const expirationDate = new Date(localGameList[i].expired_date)
      if(GameidArr.indexOf(localGameList[i].game_name) == -1 || now > expirationDate){
          localGameList.splice(i,1)
          i--;
      }
    }
  for(let i = 0;i < gameList.length ;i++)
    {
      const expirationDate = new Date(gameList[i].expired_date)
      if(GameidArr.indexOf(gameList[i].game_name) == -1 || now > expirationDate){
          gameList.splice(i,1)
          i--;
    }
}
  const historyList = JSON.parse(localStorage.getItem('freeGameHistoryList'))
  const accountList = getUnidleAccountList()
  gameList.forEach((v, i) => {
    const isNew = !localGameList.some((someV, someI) => {
      return someV.game_name === v.game_name
    })
    if (isNew) {
      v.owned = false  // 游戏已拥有
      v.success = false // 领取成功
      v.received = false // 曾经领取
      v.noBasicGame = false // 无游戏本体
      v.noAccount = true // 未登陆账号
      v.choosen = true // 选中
      v.accountList = []
      accountList.forEach((account) => { 
        if (account.platform === v.platform) {
          const owned = historyList.some(
            (history) => {
              if (
                history.game_name === v.game_name &&
                history.platform === account.platform &&
                history.platform_name === account.platform_name
              ) {
                return +history.status === 3
              } else {
                return false
              }
            }
          )
          const success = historyList.some(
            (history) => {
              if (
                history.game_name === v.game_name &&
                history.platform === account.platform &&
                history.platform_name === account.platform_name
              ) {
                return +history.status === 200
              } else {
                return false
              }
            }
          )
          const received = historyList.some(
            (history) => {
               if (
                history.game_name === v.game_name &&
                history.platform === account.platform &&
                history.platform_name === account.platform_name
               ) {
                return true
               } else {
                return false
               }
            }
          )
          const noBasicGame = historyList.some(
            (history) => {
               if (
                history.game_name === v.game_name &&
                history.platform === account.platform &&
                history.platform_name === account.platform_name
               ) {
                return +history.status === 9
               } else {
                return false
               }
            }
          )

          v.accountList.push({
            platform_name: account.platform_name,
            owned: owned,
            success: success,
            received: received,
            noBasicGame: noBasicGame,
            persist: account.persist,
            localIndex:account.localIndex,
            //第一次循环
            loginStatus:account.loginStatus
          })

        }
      })

      if (v.accountList.length !== 0) {
        v.noAccount = false
        v.received = v.accountList.every((account) => { return account.received })
        v.success = v.accountList.every((account) => { return ((account.success||account.owned)&&account.loginStatus) })
        v.noBasicGame = v.accountList.some((account) => { return account.noBasicGame }) && !v.success
        // v.owned = v.accountList.some((account) => { return account.owned }) && !v.success && !v.noBasicGame
        v.owned = v.accountList.every((account) => { return account.owned  && !v.success && !v.noBasicGame})
        v.Allowned = v.accountList.every((account) => { return account.owned }) && !v.success && !v.noBasicGame
        v.NologIn = v.accountList.every((account) => { return account.loginStatus })
        //满足一个就返回true
        // v.confirmsuccess = v.accountList.some((account)=>{return !account.owned && !account.received && !account.success })
        // v.getfial = false
        let failnum = 0
        let unreceivednum = 0
        let noLognum = 0
        v.accountList.forEach((item,index)=>{
             if(!item.loginStatus){
              noLognum ++
             }
             if(item.received&&!item.success&&!item.owned)//没有的时候 领了 领失败了 +1
             {
              failnum ++
             }
             if(!item.owned&&!item.received){//没有 而且没有领取
              unreceivednum++
             }
             if(!item.owned && item.received && !item.success){
              v.getfial =  true
            }else{
              v.getfial =  false
            }
        })
        v.failnum = failnum
        v.unreceivednum = unreceivednum
        v.noLognum = noLognum
      }

      localGameList.unshift(v)
      //不是新游戏的状况
    } else {
      v.owned = false  // 游戏已拥有
      v.success = false // 领取成功
      v.received = false // 曾经领取
      v.noBasicGame = false // 无游戏本体
      v.noAccount = true // 未登陆账号
      v.accountList = []
      accountList.forEach((account) => {
        if (account.platform === v.platform) {

          const owned = historyList.some(
            (history) => {
              if (
                history.game_name === v.game_name &&
                history.platform === account.platform &&
                history.platform_name === account.platform_name
              ) {
                return +history.status === 3
              } else {
                return false
              }
            }
          )
          const success = historyList.some(
            (history) => {
              if (
                history.game_name === v.game_name &&
                history.platform === account.platform &&
                history.platform_name === account.platform_name
              ) {
                return +history.status === 200
              } else {
                return false
              }
            }
          )
          const received = historyList.some(
            (history) => {
               if (
                history.game_name === v.game_name &&
                history.platform === account.platform &&
                history.platform_name === account.platform_name
               ) {
                return true
               } else {
                return false
               }
            }
          )
          const noBasicGame = historyList.some(
            (history) => {
               if (
                history.game_name === v.game_name &&
                history.platform === account.platform &&
                history.platform_name === account.platform_name
               ) {
                return +history.status === 9
               } else {
                return false
               }
            }
          )

          v.accountList.push({
            platform_name: account.platform_name,
            owned: owned,
            success: success,
            received: received,
            noBasicGame: noBasicGame,
            persist: account.persist,
            localIndex:account.localIndex,
            loginStatus:account.loginStatus
          })

        }
      })
      if (v.accountList.length !== 0) {
        v.noAccount = false
        v.received = v.accountList.every((account) => { return account.received })
        v.success = v.accountList.every((account) => { return (account.success||account.owned)&&account.loginStatus})
        v.noBasicGame = v.accountList.some((account) => { return account.noBasicGame }) && !v.success
        // v.owned = v.accountList.some((account) => { return account.owned }) && !v.success && !v.noBasicGame
        v.owned = v.accountList.some((account) => { return account.owned  && !v.success && !v.noBasicGame})
        v.NologIn = v.accountList.every((account) => { return account.loginStatus })


        // v.getfial = false
        let failnum = 0
        let unreceivednum = 0
        let noLognum = 0
        //领取成功和没领取 已拥有的情况

        v.accountList.forEach((item)=>{
           //领取成功和没领取 已拥有的情况
            // if((item.received && item.success) || item.owned){
              if(!item.loginStatus){
                noLognum ++
               }
            // }
             if(item.received&&!item.success&&!item.owned)//没有的时候 领了 领失败了 +1
             {
              failnum += 1
             }
             if(!item.owned&&!item.received){//没有 而且没有领取
              unreceivednum += 1
             }
             //全部领取成功的情况
             //领了之后发现是拥有的
             if(!item.owned && item.received && !item.success){
               v.getfial =  true
             }else{
              v.getfial =  false
             }
        })
        v.failnum = failnum
        v.unreceivednum = unreceivednum
        v.noLognum = noLognum
      }

      localGameList.map((localV, localI) => {
        const date = new Date()
        const year = date.getFullYear().toString()
        const month = (date.getMonth() + 1).toString().padStart(2, '0')
        const day = date.getDate().toString().padStart(2, '0')
        const hour = date.getHours().toString().padStart(2, '0')
        const minutes = date.getMinutes().toString().padStart(2, '0')
        const secend = date.getSeconds().toString().padStart(2, '0')
        const nowDate = `${year}-${month}-${day} ${hour}:${minutes}:${secend}`

        if (nowDate > localV.expired_date) {
          localGameList.splice(localI, 1)
        } else if (localV.game_name === v.game_name) {
          localV.accountList = v.accountList
          localV.noBasicGame = v.noBasicGame
          localV.received = v.received
          localV.success = v.success
          localV.owned = v.owned
          localV.noAccount = v.noAccount
          // localV.needreceived = v.needreceived
          localV.failnum = v.failnum
          localV.unreceivednum = v.unreceivednum
          localV.getfial = v.getfial
          localV.noLognum = v.noLognum
          localV.NologIn =  v.NologIn
        }
      })
    }
  })
  try{
    let sameGameArr = []
    gameList.forEach((v,i)=>{
      for(j=0;j<localGameList.length;j++){
        if(gameList[i].game_name == localGameList[j].game_name){
          sameGameArr.push(gameList[i].game_name)
          if(gameList[i].get_url !== localGameList[j].get_url){
            console.log('二者连接不一样',gameList[i].get_url);
          }
          if(gameList[i].cover !== localGameList[j].cover){
            console.log('二者图片不一样',gameList[i].get_url);
            localGameList[j].cover = gameList[i].cover
          }
          if(gameList[i].tags !== localGameList[j].tags){
            console.log('二者tags不一样',gameList[i].tags);
            localGameList[j].tags = gameList[i].tags
          }
          if(gameList[i].description !== localGameList[j].description){
            console.log('二者description不一样',gameList[i].description);
            localGameList[j].description = gameList[i].description
          }
          if(gameList[i].original_price !== localGameList[j].original_price){
            console.log('二者original_price不一样',gameList[i].original_price);
            localGameList[j].original_price = gameList[i].original_price
          }
          if(gameList[i].id !== localGameList[j].id){
            console.log('二者id不一样',gameList[i].id);
            localGameList[j].id = gameList[i].id
          }
        }
      }
  })
  console.log('服务器列表与客户端列表相同游戏：',sameGameArr);
  }catch{
       console.log('服务器数据对比错误');
  }
  

  localStorage.setItem('allGameList', JSON.stringify(localGameList))
  console.log('localGameList处理后',localGameList);
  gamepp.freegame.startgetgame({ page: 'background', type: 'refreshGameList', reason: 'processGameListEnd' })
}

// 创建领取队列
async function createLineup (auto) {
  await gamepp.freegame.updatePreload.promise()
  console.warn('开始生成领取队列')
  isReceiving = true
  let lineupList = []

  const localAccountList = JSON.parse(localStorage.getItem('account'))
  const localGameList = JSON.parse(localStorage.getItem('allGameList'))
  for (let gameI = 0; gameI < localGameList.length; gameI++) {
    const gameV = localGameList[gameI]
    
    // console.log('gameV', gameV)

    if (gameV.choosen && !gameV.isDLC) {
      const param1 = gameV.platform === 'steam' ? gameV.sub_id : gameV.get_url
      const param2 = gameV.platform === 'steam' ? gameV.app_id : ''
      for (const account of gameV.accountList) {
        if (!account.success && !account.owned && localAccountList[account.localIndex].active && localAccountList[account.localIndex].loginStatus) {
          lineupList.push({
            gameIndex: gameI,
            platform: gameV.platform,
            param1: param1,
            param2: param2,
            platform_name: account.platform_name,
            persist: account.persist,
            id: gameV.id,
            game_name: gameV.game_name,
            receiving: false, // 当前任务领取状态 true 正在领取，false 尚未领取 || 领取完成
            received: false, // 当前任务领取状态 true 领取完成， false 尚未领取
            resCode: -1, // 领取结果返回码
            original_price:gameV.original_price,
            localIndex: account.localIndex,
            cover: gameV.cover,
            CancelStatus:false
          })
        }
      }
    }
    if(auto){
      const param1 = gameV.platform === 'steam' ? gameV.sub_id : gameV.get_url
      const param2 = gameV.platform === 'steam' ? gameV.app_id : ''
      for (const account of gameV.accountList) {
        if (!account.success && !account.owned && localAccountList[account.localIndex].active && localAccountList[account.localIndex].loginStatus) {
          lineupList.push({
            gameIndex: gameI,
            platform: gameV.platform,
            param1: param1,
            param2: param2,
            platform_name: account.platform_name,
            persist: account.persist,
            id: gameV.id,
            game_name: gameV.game_name,
            receiving: false, // 当前任务领取状态 true 正在领取，false 尚未领取 || 领取完成
            received: false, // 当前任务领取状态 true 领取完成， false 尚未领取
            resCode: -1, // 领取结果返回码
            original_price:gameV.original_price,
            localIndex: account.localIndex,
            cover: gameV.cover,
            CancelStatus:false
          })
        }
      }
    }
  }
  console.warn('领取队列',lineupList);
  if (lineupList.length === 0) {
    gamepp.freegame.startgetgame({ page: 'background', type: 'receiveEnd', reason: 'noLineup' }) // 领取队列为空
    isReceiving = false
  } else {
    localStorage.setItem('lineupList', JSON.stringify(lineupList))
    gamepp.freegame.startgetgame({ page: 'background', type: 'createLineupEnd' }) //生成领取队列完成
  }

  return lineupList
}

// 开始执行领取队列
async function startReceiveGame (lineupList, isManual) {
  // console.log('本次领取游戏队列', lineupList)
  console.log('开始领取游戏')
  for (let index = 0; index < lineupList.length; index++) {
    const lineupItem = lineupList[index]
    if(!lineupItem['CancelStatus'])
    {

    }
    lineupItem.receiving = true
    localStorage.setItem('lineupList', JSON.stringify(lineupList))

    let res = await gamepp.freegame.getGame.promise(lineupItem.platform, lineupItem.param1, lineupItem.param2, lineupItem.persist, isManual, lineupItem.id)
    
    if (res.code !== 200 && res.code !== 1 && res.code !== 3 && res.code !== 7 && res.code !== 9 && res.code !== 10) 
    //领取失败重试
    {
      res = await tryAgain(lineupItem.platform, lineupItem.param1, lineupItem.param2, lineupItem.persist, 3, isManual, lineupItem.id)
    }
    //账号失效
    if (res.code === 1) 
    {
      const tempAccountList = JSON.parse(localStorage.getItem('account'))
      tempAccountList[lineupItem.localIndex].loginStatus = false
      localStorage.setItem('account', JSON.stringify(tempAccountList))
    }
    console.log('游戏:', lineupItem.game_name, '账户:', lineupItem.platform_name, '领取结果', res)
    lineupItem.received = true
    lineupItem.receiving = false
    lineupItem.resCode = res.code

    let HistoryList = JSON.parse(localStorage.getItem('freeGameHistoryList'))
    console.log('领取历史：',HistoryList);

    const date = new Date()
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2,0)
    const day = (date.getDate()).toString().padStart(2,0)
    const hour = date.getHours().toString().padStart(2,0)
    const min = date.getMinutes().toString().padStart(2,0)
    const sec = date.getSeconds().toString().padStart(2,0)

    if(HistoryList === null)
    {
          HistoryList = [{
            status:res.code,
            game_name:lineupItem.game_name,
            platform:lineupItem.platform,
            platform_name:lineupItem.platform_name,
            receive_time: `${year}-${month}-${day} ${hour}:${min}:${sec}`,
            original_price:lineupItem.original_price
          }]
    }
    else
    {
        HistoryList.unshift({
          status:res.code,
          game_name:lineupItem.game_name,
          platform:lineupItem.platform,
          platform_name:lineupItem.platform_name,
          receive_time: `${year}-${month}-${day} ${hour}:${min}:${sec}`,
          original_price:lineupItem.original_price

        })
    }

    localStorage.setItem('freeGameHistoryList', JSON.stringify(HistoryList))


    const localGameList = JSON.parse(localStorage.getItem('allGameList'))
    // console.log('领取时的locallist',localGameList);

    localGameList.forEach((localV, localI) => {
      if (localI === lineupItem.gameIndex) {
        localV.accountList.forEach((accountV) => {
          if (accountV.platform_name === lineupItem.platform_name) {
            accountV.owned = res.code === 3
            accountV.success = res.code === 200
            accountV.received = true
            accountV.noBasicGame = res.code === 8
          }
        })

        localV.received = localV.accountList.every((account) => { return account.received })
        localV.success = localV.accountList.every((account) => { return account.success || account.owned })
        localV.noBasicGame = localV.accountList.some((account) => { return account.noBasicGame }) && !localV.success
        localV.owned = localV.accountList.some((account) => { return account.owned }) && !localV.success && !localV.noBasicGame
        localV.AllaccountList = JSON.parse(localStorage.getItem('account'))
        // localV.getfial = false
        let failnum = 0
        let unreceivednum = 0
        localV.accountList.forEach((item,index)=>{
             if(item.received&&!item.success&&!item.owned)//没有的时候 领了 领失败了 +1
             {
              failnum += 1
             }
             if(!item.owned&&!item.received){//没有 而且没有领取
              unreceivednum += 1
             }
             if(!item.owned && item.received && !item.success){
              localV.getfial =  true
            }else{
              localV.getfial =  false
            }
        })
        localV.failnum = failnum
        localV.unreceivednum = unreceivednum
        // //需要领取的情况 1.未拥有  2.未领取（领取了领取失败)
        // localV.needreceived = localV.accountList.some((account)=>{return (!account.owned )||(account.received && !account.success) })
        // 全部领取的情况
        // 未领取的情况 （1.未拥有 2.领取失败)
      }
    })

    // console.log('本地游戏列表',localGameList);
    console.warn('res.code////////////////////////',res.code);
    postReceiveGameInfo(lineupItem.id, lineupItem.platform_name, res.code, res.msg, true)

    localStorage.setItem('lineupList', JSON.stringify(lineupList))
    localStorage.setItem('allGameList', JSON.stringify(localGameList))
    setTimeout(() => {
      gamepp.freegame.startgetgame({ page: 'background', type: 'refreshGameList' })
    }, 1000);
  }

  isReceiving = false
  gamepp.freegame.startgetgame({ page: 'background', type: 'receiveEnd', reason: 'normal' })


  if (isGetEmailStatus) {
    const EmailStatusObj = await getEmailStatus()
    sendEmailSwitch = EmailStatusObj.receive_type === 1
    isGetEmailStatus = false
  }
  if (sendEmailSwitch) {
    sendEmail(lineupList)
  }
}

// 重试
async function tryAgain (platform, param1, param2, persist, tryTime, isManual, gid) {
  console.log('开始重试领取')
  let res
  for (let index = 0; index < tryTime; index++) {
    res = await gamepp.freegame.getGame.promise(platform, param1, param2, persist, isManual, gid)
    console.log('重试第' + (index + 1) + '次结果:', res)
    if (res.code === 200 || res.code === 3 || res.code === 1 || res.code === 7 || res.code === 9 || res.code == 10) {
      break
    }
  }
  return res
}

// 自动领取
function intervalFunc (time) {
  interval = setInterval(async () => {
    const isManual = gamepp.setting.getInteger(408) === 0
    if (!isManual && !isReceiving) {
      console.log('自动定时领取')

      gamepp.utils.sendstatics.promise(50004)
      const lineupList = await createLineup(true)
      
      startReceiveGame(lineupList, false)

    }
    clearInterval(interval)
    interval = null
    intervalFunc(3600000)
    nextTimeReceive = sleepTime
    refreshPersist()
    processGameList()
  }, time);
}



// -------------------------主要功能-------------------------- //




// ------------------------- 获取数据-------------------------- //

async function getGameList () {
  return new Promise ((resolve, reject) => {
    $.ajax({
      type: "GET",
      url: `https://free-game.gamepp.com/game/getFreeGameList?${new Date().getTime()}`,
      dataType: "json",
      success: function (res) {
        const EncodeData = res.data
        console.log('freeGameList',EncodeData);
        console.log('freeGameList',gamepp.user.getDecodeInfo(EncodeData));
        if (EncodeData !== '') {
          const decodeObj = gamepp.user.getDecodeInfo(EncodeData)
          const DecodeData = JSON.parse(decodeObj)
          console.log('freeGameList',EncodeData);
          if(DecodeData.length > 0){
              localStorage.setItem('sleepTime',JSON.stringify(DecodeData[0].sleep_time))
          }
          console.warn('res',DecodeData);
          resolve(DecodeData)
        } else {
          resolve([])
        }
      },
      failed: function () {
        resolve([])
      }
    })
  })
}

async function getHistoryList () {
  const jsonObj = {}
  jsonObj.uid = gamepp.user.getUserID()
  const encodeData = gamepp.user.getEncodeInfo(JSON.stringify(jsonObj))
  // console.log('encodeData', encodeData)
  return new Promise ((resolve, reject) => {
    $.ajax({
      type: "POST",
      url: 'https://free-game.gamepp.com/game/getReceiveGameInfo',
      dataType: "json",
      data: encodeData,
      success: function (res) {
        console.log('getHistoryList', res)
        const EncodeData = res.data
        if (res.code === 200) {
          const DecodeData = gamepp.user.getDecodeInfo(EncodeData)
          const decodeObj = JSON.parse(DecodeData)
          resolve(decodeObj)
        } else {
          resolve([])
        }
      },
      failed: function () {
        resolve([])
      }
    })
  })
}

function getUnidleAccountList () {
  const accountList = JSON.parse(localStorage.getItem('account'))
  if (accountList.every((v, i) => { return v.idle })) {
    return []
  } else {
    const list = accountList.filter((v, i) => { return !v.idle })
    return list
  }
}

async function getEmailStatus () {
  const jsonObj = {}
  jsonObj.uid = gamepp.user.getUserID()
  const encodeData = gamepp.user.getEncodeInfo(JSON.stringify(jsonObj))
  return new Promise((resolve, reject) => {
    $.ajax({
      type: "POST",
      url: 'https://free-game.gamepp.com/subscribe/getEmailInfo',
      dataType: "json",
      data: encodeData,
      success: function (res) {
        if (res.code === 200) {
          const DecodeData = gamepp.user.getDecodeInfo(res.data)
          const decodeObj = JSON.parse(DecodeData)
          resolve(decodeObj)
        } else {
          resolve({
            new_game_type: 0,
            receive_type: 0
          })
        }
      }
    })
  })
}


// ------------------------- 获取数据-------------------------- //




// -------------------------附加功能-------------------------- //

// 领取完成发送邮件



function sendEmail (lineupList) {
  const obj = {}
  obj.uid = gamepp.user.getUserID()
  obj.uname = decodeURI(gamepp.user.getUserName())
  obj.email = gamepp.user.getEmail()
  // obj.email = '<EMAIL>' // 刘思君测试邮箱
  obj.all_num = lineupList.length
  obj.success_num = 0
  obj.error_num = lineupList.length
  obj.lineupList = []
  obj.gid = lineupList.id
  let game = {
    game_name: '',
    platform: '',
    info: []
  }
  obj.is_cloud = 0
  lineupList.forEach((v ,i) => {
    if (v.resCode === 200) {
      obj.success_num += 1
      obj.error_num -= 1
    }
    if (game.game_name === v.game_name && game.platform === v.platform) {
      if (!game.info.some(infoV => { return infoV.platform_name === v.platform_name })) {
        game.info.push({
          platform_name: v.platform_name,
          status: v.resCode === 200 ? 1: 0,
          code: v.resCode,
          msg: v.resCode === 200 ?
          '领取成功' :
          v.resCode === 1 ?
          '账号已失效' :
          v.resCode === 2 ?
          '页面加载失败' :
          v.resCode === 3 ?
          '游戏已拥有' :
          v.resCode === 4 ?
          '网络异常（-1）' :
          v.resCode === 5 ?
          '网络异常（-2）' :
          v.resCode === 7 ?
          '网络异常（-3）' :
          v.resCode === 8 ?
          '网络异常（-4）' :
          v.resCode === 9 ?
          '无游戏本体' :
          v.resCode === 10 ?
          '手动领取中断' :
          v.resCode === 11 ?
          '用户需要手动填写生日':
          '领取游戏超时'
        })
      }
    } else {
      game.cover = v.cover
      game.game_name = v.game_name
      game.original_price = v.original_price
      game.platform = v.platform
      if (!game.info.some(infoV => { return infoV.platform_name === v.platform_name })) {
        game.info.push({
          platform_name: v.platform_name,
          status: v.resCode === 200 ? 1: 0,
          code: v.resCode,
          msg: v.resCode === 200 ?
          '领取成功' :
          v.resCode === 1 ?
          '账号已失效' :
          v.resCode === 2 ?
          '页面加载失败' :
          v.resCode === 3 ?
          '游戏已拥有' :
          v.resCode === 4 ?
          '网络异常（-1）' :
          v.resCode === 5 ?
          '网络异常（-2）' :
          v.resCode === 7 ?
          '网络异常（-3）' :
          v.resCode === 8 ?
          '网络异常（-4）' :
          v.resCode === 9 ?
          '无游戏本体' :
          v.resCode === 10 ?
          '手动领取中断' :
          v.resCode === 11 ?
          '用户需要手动填写生日':
          '领取游戏超时'
        })
      }
      obj.lineupList.push(game)
      if (i < lineupList.length - 1) {
        if (lineupList[i + 1].game_name !== v.game_name || lineupList[i + 1].platform !== v.platform) {
          game = {
            game_name: '',
            platform: '',
            info: []
          }
        }
      }
    }
  })
  console.log('发送邮件数据', obj)
  const encodeData = gamepp.user.getEncodeInfo(JSON.stringify(obj))
  console.log('发送邮件加密数据', encodeData)
  if(obj.success_num == 0 && obj.error_num == 0){

  }else{
    console.log('发送领取结果邮件');
    $.ajax({
      type: "POST",
      url: 'https://free-game.gamepp.com/subscribe/sendReceiveEmail',
      dataType: "json",
      data: encodeData,
      success: function (res) {
        console.log('发送邮件结果', res)
      }
    })
  }

}

// 更新账号登陆信息
function refreshPersist () {
  // return
  if (!gamepp.user.getToken()) {
    // 未登录游戏加加，返回不做处理
    return
  }
  console.log('刷新账号persist')
  let steam = ''
  let epic = ''
  const accountList = JSON.parse(localStorage.getItem('account'))
  accountList.forEach(async (v, i) => {
    if (v.idle) {
      // 空闲cookie
      return
    }
    const loginStatus = await gamepp.freegame.getLoginStatus.promise(v.platform, v.persist)
    if (loginStatus === -1) {
      // 超时不做处理并返回
      return
    }
    // console.log('获取账号登陆状态', '获取的登陆状态:', loginStatus, '本地保存的登陆状态', v.loginStatus, i)
    if (!v.idle && v.loginStatus && !loginStatus) {
      if (v.platform === 'steam') {
        if (steam === '') {
          steam += v.platform_name
        } else {
          steam += ',' + v.platform_name
        }
      } else {
        if (epic === '') {
          epic += v.platform_name
        } else {
          epic += ',' + v.platform_name
        }
      }
    }
    if (v.loginStatus !== loginStatus) {
      v.active = loginStatus
      v.loginStatus = loginStatus
    }
    if (loginStatus) {
      uploadAccount(v.persist.replace('persist:', ''), v)
    }
    localStorage.setItem('account', JSON.stringify(accountList))
  })
  // checkAllGameAccount()
  setTimeout(() => {
    sendAccountInvalidEmail(steam, epic)
  }, 60000);
}

// 上传账号信息
async function uploadAccount (cookie, data) {
  console.log('上传账号信息数据', cookie, data)
  if (!gamepp.user.isVIP()) {
    return false
  } else {
    setTimeout(async () => {
      const res = await gamepp.freegame.upload.promise(cookie, JSON.stringify(data))
      console.log('上传账号信息返回结果', res)
    }, 5000)
  }
}

// 发送账号失效邮件
function sendAccountInvalidEmail (steam, epic) {
  console.log('steam失效账号', steam, 'epic失效账号', epic)
  if (steam !== '' || epic !== '') {
    const data = {
      uid: gamepp.user.getUserID(),
      uname: decodeURI(gamepp.user.getUserName()),
      email: gamepp.user.getEmail(),
      steam: steam,
      epic: epic,
      is_cloud: 0
    }
    const encodeData = gamepp.user.getEncodeInfo(JSON.stringify(data))
    $.ajax({
      type: "POST",
      url: 'https://free-game.gamepp.com/subscribe/sendAccountExpireEmail',
      dataType: "json",
      data: encodeData,
      success: function (res) {
        if (res.code === 200) {
          console.log('sendAccountExpireEmail数据发送成功', res)
        }
      }
    })
    // console.log('发送账号失效邮件数据', JSON.stringify(data))
  }
}

// 领取动作完成后发送结果到服务器
async function postReceiveGameInfo (gid, platformName, code, errMessage, tryAgain) {
  const uid = gamepp.user.getUserID()
  const jsonObj = {
    is_cloud: 0,
    receiver_uid: -1,
    uid: uid,
    gid: gid,
    platform_name: platformName,
    status: code,
    errMessage: errMessage
  }
  console.warn('uploadData:',jsonObj);
  const encodeData = gamepp.user.getEncodeInfo(JSON.stringify(jsonObj))
  $.ajax({
    type: "POST",
    url: 'https://free-game.gamepp.com/game/addReceiveGameInfo',
    dataType: "json",
    data: encodeData,
    success: function (res) {
      console.log('服务器返回:',res);
    },
    failed: function (res) {
      console.log('服务器返回fail',res);
      if (tryAgain) {
        setTimeout(() => {
          postReceiveGameInfo(gid, platformName, code, errMessage, false)
        }, 5000)
      }
    }
  })
}

// 刷新领取历史记录
async function refreshHisttoryList () {
  await gamepp.whenMainServicesStarted.promise()
  const historyList = await getHistoryList()
  // console.warn('刷新领取历史记录：///////////////////////////////',historyList);
  localStorage.setItem('freeGameHistoryList', JSON.stringify(historyList))
}
//领取游戏初始化数据处理
function HandleReciveOriginData(){}
// -------------------------附加功能-------------------------- //


// -------------------------其他-------------------------- //


// 自动领取定时器时间
function autoReceiveTimer () {
  setInterval(() => {
    nextTimeReceive -= 1
    localStorage.setItem('nextTimeReceive', nextTimeReceive)
  }, 1000);
}

// -------------------------其他-------------------------- //
