<script setup lang="ts">
import { ref,reactive,watch,computed,onBeforeMount,onUnmounted,onMounted } from 'vue';
import { gamepp } from 'gamepp'
import { keyCodeTransform } from './keyborad'
import { ElMessage } from "element-plus";
import shortcut from '../../components/eleCom/shortcut.vue'
import RightTopIcons from '@/components/mainCom/RightTopIcons.vue'
import { useLanguage } from '@/uitls/useLanguage';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
useLanguage()

const 鲜艳  =  'https://dl-common.gamepp.com/common/webp/xianyan.webp';
const 柔和 = 'https://dl-common.gamepp.com/common/webp/rouhe.webp';
const 高亮 = 'https://dl-common.gamepp.com/common/webp/gaoliang.webp';
const 电影 = 'https://dl-common.gamepp.com/common/webp/film.webp';
const 明基 = 'https://dl-common.gamepp.com/common/webp/minji.webp';
const 防雪盲 = 'https://dl-common.gamepp.com/common/webp/snow.webp';
const 雾天 = 'https://dl-common.gamepp.com/common/webp/fog.webp';
const 卡拉金 = 'https://dl-common.gamepp.com/common/webp/kalajin.webp';
const atday = 'https://dl-common.gamepp.com/common/webp/day.webp';
const atnight = 'https://dl-common.gamepp.com/common/webp/night.webp';
const APEX = 'https://dl-common.gamepp.com/common/webp/APEX.webp';
const volrant = 'https://dl-common.gamepp.com/common/webp/valorant.webp';
const cod = 'https://dl-common.gamepp.com/common/webp/cod.webp';
const 彩虹六号 = 'https://dl-common.gamepp.com/common/webp/r6.webp';
const 激战2 = 'https://dl-common.gamepp.com/common/webp/jizhan.webp';
const 永劫无间 = 'https://dl-common.gamepp.com/common/webp/nakara.webp';
const 永劫无间Ti = 'https://dl-common.gamepp.com/common/webp/nakarati.webp';
const 帝国神话 = 'https://dl-common.gamepp.com/common/webp/diguo.webp';
const 原神 = 'https://dl-common.gamepp.com/common/webp/yuanshen.webp';


// import 鲜艳  from  './assets/img/webp/xianyan.webp';
// import 柔和 from './assets/img/webp/rouhe.webp';
// import 高亮 from './assets/img/webp/gaoliang.webp';
// import 电影 from './assets/img/webp/film.webp';
// import 明基 from './assets/img/webp/minji.webp';
// import 防雪盲 from './assets/img/webp/snow.webp';
// import 雾天 from './assets/img/webp/fog.webp';
// import 卡拉金 from './assets/img/webp/kalajin.webp';
// import atday from './assets/img/webp/day.webp';
// import atnight from './assets/img/webp/night.webp';
// import APEX from './assets/img/webp/APEX.webp';
// import volrant from './assets/img/webp/valorant.webp';
// import cod from './assets/img/webp/cod.webp';
// import 彩虹六号 from './assets/img/webp/r6.webp';
// import 激战2 from './assets/img/webp/jizhan.webp';
// import 永劫无间 from './assets/img/webp/nakara.webp';
// import 永劫无间Ti from './assets/img/webp/nakarati.webp';
// import 帝国神话 from './assets/img/webp/diguo.webp';
// import 原神 from './assets/img/webp/yuanshen.webp';
import { Value } from 'sass';

let packageversion = ref('3.2.192.0000')
let mirrorWidth = ref(1280)
let mirrorHeight = ref(720)
let fullScreen = ref(false)
let mirrorSwitch = ref(false)
let mirrorArr:any = ref([])
let curindex:any = ref(0)
let hotkeyObj:any = ref
({
  GameMirror_hotkey : ref('Ctrl + F5'),
  hotkeyArr : ref([]),
  Ai_hotkey : ref('Shift + F6'),
  Ai_hotkeyArr : ref([]),
})

const gameMirror = ref
([
  { "value": "gpp_bright.ini", "text": "GameMirror.Bright" ,choosen:false,isVip:false,src:鲜艳},
  { "value": "gpp_gentle.ini", "text": "GameMirror.Soft"  ,choosen:false,isVip:false,src:柔和},
  { "value": "gpp_highlight.ini", "text": "GameMirror.Highlight"  ,choosen:false,isVip:false,src:高亮},
  { "value": "gpp_movie.ini", "text": "GameMirror.Film"  ,choosen:false,isVip:false,src:电影},
  { "value": "gpp_mingji.ini", "text": "GameMirror.Benq"  ,choosen:false,isVip:false,src:明基},
  { "value": "gpp_snow.ini", "text": "GameMirror.AntiGlare"  ,choosen:false,isVip:false,src:防雪盲},
  { "value": "gpp_wutian.ini", "text": "GameMirror.HighSaturation"  ,choosen:false,isVip:false,src:雾天},
  { "value": "gpp_karakin.ini", "text": "GameMirror.Brightness"  ,choosen:false,isVip:false,src:卡拉金},
  { "value": "gpp_tarkovcustoms.ini", "text": "GameMirror.Day"  ,choosen:false,isVip:false,src:atday},
  { "value": "gpp_tarkovfactory.ini", "text": "GameMirror.Night"  ,choosen:false,isVip:false,src:atnight},
  { "value": "gpp_apex.ini", "text": "GameMirror.Nature"  ,choosen:false,isVip:true,src:APEX},
  { "value": "gpp_valorant.ini", "text": "GameMirror.beauty"  ,choosen:false,isVip:true,src:volrant},
  { "value": "gpp_cod16.ini", "text": "GameMirror.smooth"  ,choosen:false,isVip:true,src:cod},
  { "value": "gpp_rsix.ini", "text": "GameMirror.elegant"  ,choosen:false,isVip:true,src:彩虹六号},
  { "value": "gpp_gw2.ini", "text": "GameMirror.warm"  ,choosen:false,isVip:true,src:激战2},
  { "value": "gpp_naraka.ini", "text": "GameMirror.clear"  ,choosen:false,isVip:true,src:永劫无间},
  { "value": "gpp_naraka_sp.ini", "text": "GameMirror.sharp"  ,choosen:false,isVip:true,src:永劫无间Ti},
  { "value": "gpp_mythofempire.ini", "text": "GameMirror.vivid"  ,choosen:false,isVip:true,src:帝国神话},
  { "value": "gpp_genshinimpact.ini", "text": "GameMirror.highDefinition" ,choosen:false,isVip:true,src:原神},
  {"value": "gpp_ai_bright.ini", "text": "GameMirror.AiMingliang"  ,choosen:false,isVip:true,src:鲜艳},//19
  { "value": "gpp_ai_colorful.ini", "text": "GameMirror.AiBright"  ,choosen:false,isVip:true,src:鲜艳},
  { "value": "ggpp_ai_dim.ini", "text": "GameMirror.AiDark"  ,choosen:false,isVip:true,src:鲜艳},
  { "value": "gpp_ai_balance.ini", "text": "GameMirror.AiBalance" ,choosen:false,isVip:true,src:鲜艳},
])
const settings = ref([{name:'最小化',icon: "icon-minimize"},{name:'退出',icon:"icon-Close"},])
const ingame_now = ref(false);
const zoomValue = ref(1)
onBeforeMount(async() =>
{

  try
  {
    // zoomValue.value = await gamepp.display.getScaleFromWindowInMonitor.promise();
    // gamepp.webapp.windows.resize.sync('game_mirror',Math.floor(1280 * zoomValue.value),Math.floor(720 * zoomValue.value))
    gamepp.display.onDisplayMetricsChanged.addEventListener( async (scaleFactor:number)=>{
      // console.log('display',scaleFactor)
      // zoomValue.value = scaleFactor
      // gamepp.webapp.windows.resize.sync('game_mirror',Math.floor(1280 * zoomValue.value),Math.floor(720 * zoomValue.value))
    })
  }
  catch{

  }
  // getWindowSize()
  initZoom()

})

onMounted(async () =>
{
  let rick = await gamepp.package.getversion.promise('GameMirror')
  packageversion.value = rick.version
  await init()
  getWindowSize()
  initMirrorOptions()
  console.log('update');
})
async function initZoom() {
  try {
    zoomValue.value = await gamepp.display.getScaleFromWindowInMonitor.promise();
    const zoomWithSystem = gamepp.setting.getInteger.sync(313)
    if (zoomWithSystem === 1) {
      // 设置body zoom
      document.body.style.zoom = zoomValue.value
      gamepp.webapp.windows.resize.sync('game_mirror',Math.floor(1300 * zoomValue.value),Math.floor(740 * zoomValue.value))
    }
  }catch (e) {
    zoomValue.value = 1
  }

  try {
    gamepp.display.onDisplayMetricsChanged.addEventListener( async (scaleFactor:number)=>{
      const zoomWithSystem = gamepp.setting.getInteger.sync(313)
      if (zoomWithSystem === 1) {
        console.log('display',scaleFactor)
        zoomValue.value = scaleFactor
        document.body.style.zoom = zoomValue.value
        try{
          gamepp.webapp.windows.resize.sync('game_mirror',Math.floor(1300 * zoomValue.value),Math.floor(740 * zoomValue.value))
        }catch (e) {
          console.log(e)
        }
      }
    })
  }catch (e) {
    console.log(e)
  }
}

const openUrl = () =>
{
  gamepp.shell.openExternal('https://gamepp.com/news/20221201173029-2.html');
}
//----------------------------------热键检查类函数

const openGameMirror = async() =>
{
  if(mirrorSwitch.value)
  {
    let QualityText = await gamepp.setting.getString.promise(73);
    if(QualityText == '')
    {
      QualityText = gameMirror.value[5].value
      await gamepp.setting.setString.promise(73,String(QualityText))
    }
    const index = gameMirror.value.findIndex((v,i)=>
    {
      return v.value == QualityText
    })
    await gamepp.game.ingame.setShaderPreset.promise(gameMirror.value[index].value); //设置滤镜
    await gamepp.setting.setInteger.promise(2,1)
    await gamepp.game.ingame.enableShader.promise();
    await gamepp.utils.sendstatics.promise(Number(100601));
  }
  else
  {
    await gamepp.setting.setInteger.promise(2,0)
    gamepp.game.ingame.disableShader.sync();
    await gamepp.utils.sendstatics.promise(Number(100602));
  }
  localStorage.setItem('mirrorSwitch', JSON.stringify(mirrorSwitch.value));
}

const translatedText = ref('')
// 监听游戏滤镜页面变化
const handleStorageChange = (event:any) => {
  if (event.key === 'mirrorSwitch' && event.newValue !== null) {
    try {
      mirrorSwitch.value = JSON.parse(event.newValue);
    } catch (error) {}
  }
  if (event.key === 'mirrorText_mutilang') {
    try {
      const text = JSON.parse(event.newValue || '""');
      const index = gameMirror.value.findIndex(item => item.text === text);
      if (index !== -1) {
        curindex.value = index;
        gameMirror.value.forEach(v => { v.choosen = false; });
        gameMirror.value[index].choosen = true;
      }
    } catch (error) {}
  }
  if(event.key === 'language') {
    initMirrorOptions()
  }
};

window.addEventListener('storage', handleStorageChange)

onUnmounted(() => {
  window.removeEventListener('storage', handleStorageChange);
});

const initMirrorOptions = async() =>
{

  const Switch = await gamepp.setting.getInteger.promise(2);
  console.warn('滤镜开关:1为开',Switch);
  if ( Switch === 1)
  {
    mirrorSwitch.value = true
  }
  else
  {
    mirrorSwitch.value = false
  }
  hotkeyObj.value['GameMirror_hotkey'] = await gamepp.setting.getString.promise(13);
  hotkeyObj.value['Ai_hotkey'] = await gamepp.setting.getString.promise(389);
  let QualityText = await gamepp.setting.getString.promise(73);
  if(QualityText == '')
  {
    QualityText = gameMirror.value[5].value
    await gamepp.setting.setString.promise(73,String(QualityText))
  }

  console.warn('滤镜热键:',hotkeyObj.value['GameMirror_hotkey']);
  console.warn('Ai滤镜热键:',hotkeyObj.value['Ai_hotkey'] );
  console.warn('滤镜方案：',QualityText);

  const index = gameMirror.value.findIndex((v,i)=>{
    return v.value == QualityText
  })

  translatedText.value = gameMirror.value[index].text;
  localStorage.setItem('mirrorText_mutilang',JSON.stringify(translatedText.value))
  curindex.value = index
  gameMirror.value.forEach(v => {v.choosen = false});
  gameMirror.value[index].choosen = true
}

const setMirror = async(index:number) =>
{
  const isVIP = await gamepp.user.isVIP.promise();
  if(index > 9 && !isVIP)
  {
    ElMessage({message: t('GameMirror.onlyVipCanUse'),type: 'warning',grouping:true})
  }
  else
  {
    curindex.value = index
    console.warn(curindex.value,gameMirror.value[curindex.value].src);
    gameMirror.value.forEach(v => {v.choosen = false});
    gameMirror.value[index].choosen = true
    //打点
    try
    {
      let originIndex = 100603
      let mirrorIndex =  gameMirror.value.findIndex((item:any)=>
      {
        return item.value == gameMirror.value[curindex.value].value
      })
      if(mirrorIndex !== -1)
      {
        if(mirrorSwitch.value)
        {
          console.warn('已上传,',Number(originIndex+mirrorIndex));
          await gamepp.utils.sendstatics.promise(Number(originIndex+mirrorIndex));
        }
      }
    }
    catch
    {
      console.warn('上传错误');
    }
    await gamepp.setting.setString.promise(73,String(gameMirror.value[index].value)) //保存设置
    await gamepp.game.ingame.setShaderPreset.promise(gameMirror.value[index].value); //设置滤镜

    const translatedText = gameMirror.value[index].text;
    localStorage.setItem('mirrorText_mutilang',JSON.stringify(translatedText))

    const obj =
        {
          action:'changeMirror'
        }
    await gamepp.webapp.sendInternalAppEvent.promise('desktop', obj)
    console.warn('setLocal',gameMirror.value[index].text);

  }

}

const init = async() =>
{
  window.addEventListener('resize', getWindowSize);
  if(gamepp.webapp.windows.getTitle.sync() === '游戏加加游戏内主窗口')
  {
    ingame_now.value = false
  }else
  {
    ingame_now.value = true

  }
}

const getWindowSize = () =>
{
  mirrorWidth.value = window.innerWidth;
  mirrorHeight.value = window.innerHeight;
};

async function setting(index:number)
{
  if(index == 1)
  {
    await gamepp.webapp.windows.close.promise('game_mirror');
  }
  else if(index == 0)
  { // 最小化
    await gamepp.webapp.windows.minimize.promise('game_mirror');
  }

  console.warn(index);
}

function settingShow(index:number)
{
  if (index === 2)
  {
    return fullScreen.value
  }else if (index === 1)
  {
    return !fullScreen.value
  }else
  {
    return true
  }
}

// const GameMirrorStyle = computed(() => {
//   return {
//     width: `${mirrorWidth.value / zoomValue.value}px`,
//     height: `${mirrorHeight.value / zoomValue.value}px`,
//     zoom:`${zoomValue.value}`
//   }
// })
</script>

<template>
  <!-- <div class="GameMirror" :style="GameMirrorStyle"> -->
  <div class="GameMirror" :class="{ 'ingameGameMirror': !ingame_now }">

    <header class="nav" v-show="ingame_now">
      <div class="left"><img src="../../assets/img/Public/logo_gpp.png" alt=""><p>{{ $t('home.gameFilter') }}</p><p style="margin-left: 13px;"><span>V</span>{{ packageversion }}</p></div>
      <div class="drag-bar"></div>
      <div class="setting">
          <RightTopIcons close-icon minimize-icon @close="setting(1)" @minimize="setting(0)" hover-color="#22232e" />
        <!--<div class="item" v-for="(item,index) in settings" @click="setting(index)"-->
        <!--     v-show="settingShow(index)">-->
        <!--  <span :class="['iconfont',item.icon]"></span>-->
        <!--</div>-->
      </div>
    </header>
    <div class="container">
      <div class="left" :class="{ 'ingameleftright': !ingame_now }">
        <div class="line" style="display: flex;justify-content: space-between;align-items: center;"><span>{{ $t('GameMirror.filterStatus') }}</span>
          <el-switch  tabindex="-1"  style="--el-switch-on-color: #3579D5" v-model="mirrorSwitch" @change="openGameMirror()" /></div>
        <div class="line"><span>{{ $t('GameMirror.filterPlan') }}</span> </div>
        <div class="mirrorBox scroll">
          <div>
            <div class="mirroritem" v-for="(item,index) in gameMirror" :class="[item.choosen?'choosen':'']" @click="setMirror(index)">
              <p>{{ $t(item.text) }}</p>
              <img  v-show="item.isVip" src="./assets//img/bq_VIP.png" alt="">
            </div>
          </div>
        </div>
        <div class="title" style="color:#FFFFFF;font-size: 12px;width: 200px;margin: 20px 0 0 20px;">{{ $t('GameMirror.filterShortcut') }}</div>
        <div class="title" style="color:#777777;font-size: 12px;width: 200px;margin: 10px 0 0 20px;">{{ $t('GameMirror.openCloseFilter') }}</div>
        <div class="sethotkey" style="width: 200px;margin: 10px 0 0 20px;">
          <!-- <el-input  tabindex="-1" v-model="hotkeyObj['GameMirror_hotkey']" style="width: 140px" @focus="startchangeHotkey('GameMirror_hotkey','hotkeyArr')" @input="checkHotkeyValue('GameMirror_hotkey','hotkeyArr')" @blur="onblurCheckHotkey('GameMirror_hotkey','hotkeyArr','Ctrl + F5',13)"  placeholder="按下设置热键" /> -->
          <!-- <el-input v-model="hotkeyObj['Ai_hotkey']"         style="width: 140px" @focus="startchangeHotkey('Ai_hotkey','Ai_hotkeyArr')" @input="checkHotkeyValue('Ai_hotkey','Ai_hotkeyArr')" @blur="onblurCheckHotkey('Ai_hotkey','Ai_hotkeyArr','Shift + F6',389)"  placeholder="按下Ai设置热键" /> -->
          <shortcut :id="13"></shortcut>
        </div>
      </div>
      <div class="right" :class="{ 'ingameleftright': !ingame_now }">
        <div class="Aihotkeyline" >
          <div class="itemline" v-show="curindex <= 18" >
            <div>
              <span>{{ $t('GameMirror.effectDemo') }}</span>
            </div>
            <!-- <div  class="htkey" style="display: flex;align-items: center;" v-show="curindex > 18"><span style="margin-right: 5px;">在游戏场是光线、明暗变化的情况下，请使用</span>
             <el-input v-model="hotkeyObj['Ai_hotkey']"         style="width: 140px" @focus="startchangeHotkey('Ai_hotkey','Ai_hotkeyArr')" @input="checkHotkeyValue('Ai_hotkey','Ai_hotkeyArr')" @blur="onblurCheckHotkey('Ai_hotkey','Ai_hotkeyArr','Shift + F6',389)"  placeholder="按下Ai设置热键" />
             <shortcut :id="389"></shortcut>
             <span style="margin: 0 30px 0 5px">快捷键调整滤镜</span> -->
            <!-- </div> -->
          </div>
          <div class="itemline" v-show="curindex <=18 ">
            <div class="example"><span>{{ $t('GameMirror.demoConfig') }}</span><div><span style="color:#777777;margin:0 10px;">{{ $t('hardwareInfo.processor') }}：</span>AMD Ryzen 5 5600X <span></span></div></div>
            <div class="example"><span>{{ $t('GameMirror.demoConfig') }}</span><div><span style="color:#777777;margin:0 10px;">{{ $t('hardwareInfo.graphicsCard') }}：</span>AMD Radeon RX 6600 XT ASRock <span></span></div></div>

          </div>
          <div class="itemline rtl" v-show="curindex > 18" style="margin:20px 0 0 20px">
            <div style="width: 210px">
              <span style="color: #777777;">{{ $t('GameMirror.AiFilter') }}</span>
            </div>
            <div @click="openUrl()" style="margin-left: 630px;width: 120px;height: 30px;background-color:#3579D5;border-radius: 4px;display: flex;align-items: center;justify-content: center;cursor: pointer;">{{ $t('GameMirror.AiFilterFAQ') }}</div>
          </div>
          <div class="Aibox" v-show="curindex >18 ">
            <div class="top">
              <div class="Ailines">
                <span style="font-size: 20px;margin-left: 20px">{{ $t('GameMirror.gamePPAiFilter') }}</span><img style="width: 60px;height: 20px;" src="./assets//img/vip.png" alt="">
              </div>
              <div class="Ailines">
                <span style="color: #3579D5;margin-left: 20px">{{ $t('GameMirror.gamePPAiFilterVip') }}</span>
              </div>
            </div>
            <div class="bottom">
              <div class="Ailines">
                <span style="color: #777777;margin-left: 20px" :class="[curindex == 19?'choosen':'']">{{ $t('GameMirror.AiMingliangTips') }}</span>
              </div>
              <div class="Ailines">
                <span style="color: #777777;margin-left: 20px" :class="[curindex == 20?'choosen':'']">{{ $t('GameMirror.AiBrightTips') }}</span>
              </div>
              <div class="Ailines">
                <span style="color: #777777;margin-left: 20px" :class="[curindex == 21?'choosen':'']">{{ $t('GameMirror.AiDarkTips') }}</span>
              </div>
              <div class="Ailines">
                <span style="color: #777777;margin-left: 20px" :class="[curindex == 22?'choosen':'']">{{ $t('GameMirror.AiBalanceTips') }}</span>
              </div>
              <div class="Ailines">
                <span style="color: rgb(251, 172, 20);margin-left: 20px;font-size: 14px">{{ $t('GameMirror.AiTips') }}</span>
              </div>
            </div>
            <div  class="htkey" style="margin-top: 20px;display: flex; margin-top: 20px;align-items: center;">
              <span style="margin-right: 10px;">{{ $t('GameMirror.AiFilterUse') }}</span>
              <!-- <el-input tabindex="-1"v-model="hotkeyObj['Ai_hotkey']"         style="width: 140px" @focus="startchangeHotkey('Ai_hotkey','Ai_hotkeyArr')" @input="checkHotkeyValue('Ai_hotkey','Ai_hotkeyArr')" @blur="onblurCheckHotkey('Ai_hotkey','Ai_hotkeyArr','Shift + F6',389)"  placeholder="按下Ai设置热键" /> -->
              <shortcut :id="389"></shortcut>
              <span style="margin: 0 30px 0 10px">{{ $t('GameMirror.AiFilterAdjust') }}</span>
            </div>
          </div>
        </div>
        <img v-show="curindex <= 18" :src="gameMirror[curindex].src" alt="">
      </div>
    </div>
    <!-- <img :src="icon" alt=""> -->
  </div>
</template>

<style lang="scss" scoped>
.ingameGameMirror{
  background: transparent!important;
}
.ingameleftright{
  background: rgba(45, 46, 57, .8)!important;
}
.GameMirror{
  border-radius: 6px;
  overflow: hidden;
  background-color:#22232E;
  user-select: none;
  margin: 10px 0 0 10px;
  box-shadow: 0px 1px 6px rgba(0, 0, 0, .4);
  width: 1280px;
  height: 720px;
  .nav{
    .left{
      display: flex;
      font-size: 12px;
      display: flex;
      align-items: center;
      color: #FFFFFF;
      img{
        width: 14px;
        height: 14px;
        margin:0 13px
      }
      p{
        //  width: 120px;
        white-space: nowrap;
      }
    }
    .drag-bar{
      width: 90%;
      height: 20px;
      -webkit-app-region: drag;
      cursor: pointer;
    }
    width: 100%;
    height: 40px;
    // border: 1px solid #FFFFFF;
    background-color:#2D2E39;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .setting{
      display: flex;
      .item{
        width: 30px;
        height: 30px;
        text-align: center;
        line-height: 30px;
        margin-right: 10px;
        cursor: pointer;
        color: #3579D5;
        border-radius: 2px;

        &:hover{
          background: #22232e;
        }
        // &:last-child:hover {
        //   background: #BF4040;
        // }
      }
    }
  }
  .container{
    display: flex;
    .left{
      width: 220px;
      height: 640px;
      // border:1px solid green;
      margin:20px;
      border-radius: 4px;
      background-color:#2D2E39;
      display: flex;
      flex-direction: column;
      align-items: center;
      // justify-content: center;
      .line{
        width: 180px;
        margin:10px 0;
        font-size: 14px;
        color: #FFFFFF;
      }
      .mirrorBox{
        overflow: auto;
        width: 180px;
        height: 420px;
        background-color:#22232E;
        border-radius: 4px;
        .mirroritem{
          width: 100%;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          color:#777777;
          font-size: 12px;
          cursor: pointer;
          p{
            margin-left: 10px;
          }
          img{
            margin-right: 10px
          }
        }
        .mirroritem:hover{
          background-color:#343647;
          color: #FFFFFF;
        }
        .choosen{
          background-color:#343647;
          color: #FFFFFF;
        }
      }

    }
    .right{
      width: 1000px;
      height: 640px;
      // border:1px solid green;
      margin: 20px 0;
      background-color:#2D2E39;
      border-radius: 4px;
      font-size: 12px;
      .Aihotkeyline{
        display:flex;
        flex-direction: column;
        color: #FFFFFF;
        .itemline{
          margin: 10px 0 0 20px;
          width: 100%;
          height: 30px;
          display:flex;
          align-items: center;
          flex-wrap: nowrap;
          // justify-content:space-between;
          .htkey{
            width: 650px;
            margin-left: 355px
          }
          .example{
            width:491px;
            display: flex;
            align-items: center;
            span{
              max-width: 120px;
            }
            div{
              margin-left: 10px;
              display: flex;
              align-items: center;
              background-color:#22232E;
              border-radius: 4px;
              height: 30px;
              width: 350px;
            }
          }
        }
      }
      img{
        width: 960px;
        height: 520px;
        margin-left:20px;
        margin-bottom: 20px;
        margin-top: 20px
      }
      .Aibox{
        width: 960px;
        height: 500px;
        margin: 20px 0 0 20px;
        display: flex;
        flex-direction: column;
        .top{
          background-color:#343647;
          height: 90px
        }
        .bottom{
          background-color:#22232E;
          height: 300px;
          .Ailines{
            span{
              margin-top: 30px;
              font-size: 16px
            }
          }
          .choosen{
            color: #FFFFFF !important;
          }
        }
        .Ailines{
          display: flex;
          align-items: center;
        }
      }
    }
  }
}
.scroll::-webkit-scrollbar {
  width: 5px;
  transition: 0.25s;
}

.scroll::-webkit-scrollbar-thumb {
  background: #71738C;
  border-radius: 3px;
}

.scroll::-webkit-scrollbar-thumb:hover {
  background-color: #71738C;
}
.scroll::-webkit-scrollbar-track {
  background-color: #22232E;
  width: 2px;
}


</style>
<style>
.GameMirror{
  .el-input{
    width: 180px !important;
  }
  .el-input__wrapper{
    background-color: #343647 !important;
    /* border: 1px solid #343647; */
    color: #FFFFFF !important;
    box-shadow: none;
  }
  .el-input__inner{
    color: #FFFFFF !important;
    text-align: center;
  }
  .el-switch__core{
    background:#4c4d4f!important;
    border:1px solid #4c4d4f!important;
  }
  .el-switch.is-checked .el-switch__core{
    background-color: #3579D5!important;
    border:1px solid #3579D5!important;
  }
}

</style>
