<template>
    <header>
        <p>V{{ version }}</p>
        <RightTopIcons
            close-icon
            minimize-icon
            @minimize="minimizeWindow"
            @close="closeWindow"
        />
    </header>
</template>

<script setup lang="ts">
import {gamepp} from 'gamepp'
import RightTopIcons from "@/components/mainCom/RightTopIcons.vue";


let version = gamepp.getPlatformVersion.sync()

async function closeWindow() {
  gamepp.webapp.windows.close.promise('gamepp_config')
}

async function minimizeWindow() {
  gamepp.webapp.windows.minimize.promise('gamepp_config')
}
</script>

<style scoped lang="scss">
header {
  height: 45px;
  color: var(--font-color);
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  width: 100%;
  -webkit-app-region: drag;
  padding-bottom: 5px;

  p {
    font-size: .12rem;
    margin-left: auto;
    margin-right: 10px;
  }

  .iconfont-box {
    display: flex;
    width: 40px;
    height: 40px;
    justify-content: center;
    align-items: center;
    border-radius: 2px;
    cursor: pointer;
    -webkit-app-region: no-drag;

    &:hover {
      background-color: #2d2e39;

      .iconfont {
        color: #ffffff;
      }
    }
  }

  .iconfont {
    color: var(--active-color);
    cursor: pointer;
    display: block;
    font-size: 14px;
    text-align: center;
    -webkit-app-region: no-drag;
  }
}
</style>
