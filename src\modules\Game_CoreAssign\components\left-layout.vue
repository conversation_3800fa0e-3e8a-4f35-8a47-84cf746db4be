<script setup lang="ts">
import {onMounted, ref} from "vue";
import {processCoreAssignStore} from "../stores";
import {useI18n} from "vue-i18n";

const $store = processCoreAssignStore()
const { t } = useI18n();
onMounted(() => {
    checkLocalProcessGroup()
})

function checkLocalProcessGroup() {
    let localData = window.localStorage.getItem('process_groups')
    if (localData) {
        localData = JSON.parse(localData) as any[]
        for (let i = 0; i < localData.length; i++) {
            if (localData[i].name === '非游戏进程') {
                localData[i].name = 'psc.notGameProcess'
            }
            if (localData[i].name === '未命名分组') {
                localData[i].name = 'psc.unNamedProcess'
            }
        }
        $store.display.process_groups = localData
    }
}

// 查看这些个进程有多少是运行中的
function computedProcessActive (groupId:string) {
    const arr = $store.display.local_process.filter((v)=>{
        return v.group === groupId
    })
    let activeCount = 0
    arr.forEach((item: any) => {
        const findItem = $store.process_list.find(el => el.name === item.name)
        if (findItem) {
            activeCount++
        }
    })
    return `<span style="color: #35D57D;">${activeCount}</span><span> / ${arr.length}</span>`
}

// 显示这个组用到的CPU核心
function showChosenCpuCore (cpuSets: Array<number>) {
    if (!cpuSets || cpuSets.length === 0) return ''
    const cpuComplexInfo = $store.cpuComplexInfo;
    let _set = new Set();
    cpuSets.forEach((cpuSet: number) => {
        cpuComplexInfo.forEach(({Cores})=>{
            Cores.forEach((core) => {
                core.LogicalCores.forEach((logicalCore) => {
                    if (logicalCore.CPUSetId === cpuSet) {
                        _set.add(core.Core)
                    }
                })
            })
        })
    })

    return Array.from(_set).sort().join(' ')
}

function successMsg(msg:string,duration=3000) {
    // @ts-ignore
    ElMessage({
        // @ts-ignore
        message: h('p', { style: 'line-height: 1; font-size: 14px; color: #fff;' }, [
            // @ts-ignore
            h('span', null, msg),
        ]),
        duration: duration,
        type: "success"
    })
}
async function exportScheme() {
    let obj:any = {
        cpuName: $store.display.cpuName,
    }
    let process_groups:any = window.localStorage.getItem('process_groups')
    if (process_groups) {
        process_groups = JSON.parse(process_groups) as any[]
        for (let i = 0; i < process_groups.length; i++) {
            if (process_groups[i].name === '非游戏进程') {
                process_groups[i].name = 'psc.notGameProcess'
            }
            if (process_groups[i].name === '未命名分组') {
                process_groups[i].name = 'psc.unNamedProcess'
            }
        }
        obj.process_groups = process_groups
    }
    let local_process:any = window.localStorage.getItem('local_process')
    if (local_process) {
        local_process = JSON.parse(local_process)
        local_process.forEach((item:any)=>{
            item.icon = ''
        })
        obj.local_process = local_process
    }
    try {
        //@ts-ignore
        let AppDataDir = await gamepp.getAppDataDir.promise()
        let save_path = AppDataDir.replace('AppData', 'Desktop').split('\\').slice(0, 4).join('\\') + '\\' + t('psc.processCoreAssign');
        //@ts-ignore 保存到桌面
        await gamepp.saveFileToDisk.promise(save_path + '.txt', JSON.stringify(obj))

        successMsg(t('psc.dataSaveDesktop'))
    }catch (e) {

    }
}
async function importScheme() {
    try {
        // 创建一个文件选择器
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.txt'; // 限制文件类型为 .txt
        input.style.display = 'none'; // 隐藏文件选择器

        // 监听文件选择事件
        const file:Blob = await new Promise((resolve, reject) => {
            input.addEventListener('change', (event:any) => {
                const file = event.target.files[0];
                if (file) {
                    resolve(file);
                } else {
                    reject(new Error('未选择文件'));
                }
            });
            input.click(); // 触发文件选择器
        });

        // 读取文件内容
        const fileContent:string = await new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result as any);
            reader.onerror = () => reject(new Error('文件读取失败'));
            reader.readAsText(file); // 以文本形式读取文件
        });

        // 解析文件内容为 JSON 对象
        const data = JSON.parse(fileContent);

        // 恢复数据到 localStorage
        if (data.process_groups) {
            if (data.cpuName && data.cpuName === $store.display.cpuName) {

            }else{
                data.process_groups.forEach((item:any)=>{
                    item.cpuSet = []
                })
            }
            window.localStorage.setItem('process_groups', JSON.stringify(data.process_groups));
        }
        if (data.local_process) {
            window.localStorage.setItem('local_process', JSON.stringify(data.local_process));
        }

        // 提示用户导入成功
        successMsg(t('psc.importSuccess'));
        $store.display.local_process = data.local_process
        $store.display.process_groups = data.process_groups
        $store.display.activeGroup = ''
    } catch (error) {
        console.error('导入失败:', error);
        // @ts-ignore
        ElMessage.error(t('psc.importFailed')) // 提示用户导入失败
    }
}
</script>

<template>
    <div class="left-layout">
        <el-button
            type="primary"
            class="all-process"
            :class="$store.display.activeGroup === '' ? 'all-process-active' : ''"
            color="#343647"
            tabindex="-1"
            @click="$store.display.activeGroup = ''"
        >
            {{$t('psc.allprocess')}}
        </el-button>

        <div class="parting-line"></div>

        <div class="add-process-group" @click="$store.addProcessGroup()">
            <span class="add-icon">+</span>
            <span style="line-height: 1;">{{$t('psc.createAGroup')}}</span>
        </div>

        <ul class="process-groups scroll-y">
            <li
                v-for="(item, index) in $store.display.process_groups"
                :key="item.name + index"
                @click="$store.display.activeGroup = item.id"
                :class="$store.display.activeGroup === item.id ? 'active' : ''"
                @mouseenter="$store.mouseEnterGroupId = item.id"
                @mouseleave="$store.mouseEnterGroupId = ''"
            >
                <p>
                    <span class="group-name">{{$t(item.name)}}</span>
                    <span class="group-pinfo" v-html="computedProcessActive(item.id)"></span>
                </p>
                <p class="cpus" v-if="item.cpuSet.length>0">CPU ( <span>{{showChosenCpuCore(item.cpuSet)}}</span> )</p>
                <p class="cpus" v-else>{{$t('psc.unallocatedCore')}}</p>
            </li>
        </ul>

        <div class="footer">
            <div class="scheme">
                <span @click="importScheme">{{$t('psc.importGroupingScheme')}}</span>
                <span @click="exportScheme">{{$t('psc.derivedPacketScheme')}}</span>
            </div>
            <div class="parting-line"></div>
            <div class="btns">
                <el-button class="text-ellipsis" v-show="!$store.enableCpuSet" type="primary" color="#48B383" @click="$store.handleEnableCpuSet">{{ $t('psc.startUse') }}</el-button>
                <el-button class="text-ellipsis" v-show="$store.enableCpuSet" type="primary" color="#BF4040" @click="$store.disableCpuSet">{{ $t('psc.stopUse') }}</el-button>
            </div>
        </div>
    </div>
</template>

<style scoped lang="scss">
.text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.left-layout {
  width: 180px;
  border-radius: 4px;
  height: 100%;
  padding: 15px 10px 7px 10px;
  background: #2B2C37;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);

  .all-process {
    width: 157px;
    height: 60px;
    border-radius: 4px;
  }

  :deep(.all-process.el-button:hover) {
    background-color: #494C66;
    border-color: #494C66;
  }

  :deep(.all-process-active.el-button) {
    background: #336AB5;
    border: 2px solid #579EFF;
  }

  .parting-line {
    margin: 9px 0;
    width: 157px;
    height: 2px;
    background: linear-gradient(90deg, #336ab500, #336ab5 50%, #336ab500);
  }

  .add-process-group {
    width: 157px;
    height: 40px;
    margin-bottom: 10px;
    border-radius: 4px;
    box-sizing: border-box;
    border: 1px dashed #70738A;
    cursor: pointer;
    color: #70738A;
    font-size: 12px;
    display: flex;
    flex-flow: row nowrap;
    justify-content: center;
    align-items: center;
    gap: 5px;

    .add-icon {
      font-weight: 700;
      font-size: 20px;
    }

    &:hover {
      border-color: #9598B3;
      color: #9598B3;
    }
  }

  .process-groups {
    display: flex;
    flex-flow: column nowrap;
    gap: 10px;
    height: calc(100% - 232px);
    overflow-x: hidden;
    width: 160px;
    &::-webkit-scrollbar {
      width: 2px;
    }

    li {
      width: 155px;
      height: 60px;
      border-radius: 4px;
      background-color: #22232E;
      padding: 9px 11px;
      font-size: 12px;
      color: #999999;
      flex-flow: column nowrap;
      justify-content: space-between;
      cursor: pointer;
      border: 1px solid transparent;

      &:hover,&.active {
        border: 1px solid #508DE2;
        color: #ffffff;
      }

      p {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: flex;
        flex-flow: row nowrap;

        .group-name {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 107px;
          display: inline-block;
        }
        .group-pinfo {
          margin-left: auto;
        }
      }

      .green-color {
        color: #35D57D;
      }

      .cpus {
        color: #555555;
      }
    }
  }

  .footer {
    font-size: 12px;

    .scheme {
      // display: flex;
      // flex-flow: row nowrap;
      // justify-content: space-between;
    }
    span {
      color: #3579D5;
      cursor: pointer;
      width: 100%;
      display: block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    :deep(.el-button) {
      width: 155px;
      height: 40px;
      color: #ffffff;
    }
    :deep(.el-button+.el-button) {
      margin-left: 0;
    }

    .btns {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
