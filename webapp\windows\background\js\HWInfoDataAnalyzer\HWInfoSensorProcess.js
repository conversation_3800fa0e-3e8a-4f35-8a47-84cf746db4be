if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
    // 在 Node.js 环境下，使用 module.exports 导出
    module.exports = {
        SensorAddAverageData
    };
}
//提交测试 提交测试
//整理 cpu 电压 频率 占用平均值
function SensorAddAverageData (SensorInfo, SensorInfoKeys) {
    let vidSum = 0, vidCount = 0, vidSumP = 0, vidCountP = 0, vidSumE = 0, vidCountE = 0;
    let vidMax = 0, vidMaxP = 0, vidMaxE = 0;

    let clockSum = 0, clockCount = 0, clockSumP = 0, clockCountP = 0, clockSumE = 0, clockCountE = 0;

    let tempSum = 0, tempCount = 0, tempSumP = 0, tempCountP = 0, tempSumE = 0, tempCountE = 0;

    let usageSum = 0, usageCount = 0, usageSumP = 0, usageCountP = 0, usageSumE = 0, usageCountE = 0;

    const vidRegex = /^Core[ \f\r\t\n][0-9]+[ \f\r\t\n]VID$/i;
    const vidRegexP = /^P-Core[ \f\r\t\n][0-9]+[ \f\r\t\n]VID$/i;
    const vidRegexE = /^E-Core[ \f\r\t\n][0-9]+[ \f\r\t\n]VID$/i;

    const clockRegex = /^Core[ \f\r\t\n][0-9]+[ \f\r\t\n]Clock[.\n]*$/i;
    const clockRegexP = /^(P-Core|P-core)\s*\d+\s*Clock\b.*$/i;
    const clockRegexE = /^(E-Core|E-core)\s*\d+\s*Clock\b.*$/i;

    const tempRegex = /^Core[ \f\r\t\n][0-9]{1,2}$/i;
    const tempRegexP = /^P-Core[ \f\r\t\n][0-9]{1,2}$/i;
    const tempRegexE = /^E-Core[ \f\r\t\n][0-9]{1,2}$/i;

    const usageRegex  = /^CORE\s+\d+(?:\s+T\d+)?\s*USAGE$/i;
    const usagePRegex = /^P-CORE\s+\d+(?:\s+T\d+)?\s+USAGE$/i;
    const usageERegex = /^E-CORE\s+\d+(?:\s+T\d+)?\s+USAGE$/i;


    SensorInfoKeys.forEach(key => {
        if (SensorInfo[key]) {
            SensorInfo[key].forEach(item => {
                let itemKey = String(Object.keys(item)[0]);
                const value = parseFloat(item[itemKey].value);
                const type = item[itemKey]['type'];
                itemKey = itemKey.toLowerCase();
                if (type === "voltage" && itemKey.includes('core') && key.includes('CPU')) {
                    vidSum += value;
                    vidMax = Math.max(vidMax, value)
                    vidCount++;
                    if (itemKey.includes('p-core')) {
                        vidSumP += value;
                        vidMaxP = Math.max(vidMaxP, value)
                        vidCountP++;
                    } else if (itemKey.includes('e-core')) {
                        vidSumE += value;
                        vidMaxE = Math.max(vidMaxE, value)
                        vidCountE++;
                    }
                } else if (type === "clock" && itemKey.includes('core') && itemKey.includes('clock') && key.includes('CPU') && !itemKey.includes('effective')) {
                    clockSum += value;
                    clockCount++;
                    if (itemKey.includes('p-core')) {
                        clockSumP += value;
                        clockCountP++;
                    } else if (itemKey.includes('e-core')) {
                        clockSumE += value;
                        clockCountE++;
                    }
                } else if (tempRegex.test(itemKey) || tempRegexP.test(itemKey) || tempRegexE.test(itemKey)) {
                    tempSum += value;
                    tempCount++;
                    if (itemKey.includes('p-core')) {
                        tempSumP += value;
                        tempCountP++;
                    } else if (itemKey.includes('e-core')) {
                        tempSumE += value;
                        tempCountE++;
                    }
                } else if (type === "usage" && itemKey.includes('core') && itemKey.includes('usage') && key.includes('CPU')) {
                    usageSum += value;
                    usageCount++;
                    if (itemKey.includes('p-core')) {
                        usageSumP += value;
                        usageCountP++;
                    } else if (itemKey.includes('e-core')) {
                        usageSumE += value;
                        usageCountE++;
                    }
                }
            });
        }
    });

    const averageVidObj = { "Core VIDs": { "type": "voltage", "value": vidCount !== 0 ? vidSum / vidCount : 0 } };
    const averageVidPObj = { "P Core VIDs": { "type": "voltage", "value": vidCountP !== 0 ? vidSumP / vidCountP : 0 } };
    const averageVidEObj = { "E Core VIDs": { "type": "voltage", "value": vidCountE !== 0 ? vidSumE / vidCountE : 0 } };

    const averageClockObj = { "Core Clocks": { "type": "clock", "value": clockCount !== 0 ? clockSum / clockCount : 0 } };
    const averageClockPObj = { "P Core Clocks": { "type": "clock", "value": clockCountP !== 0 ? clockSumP / clockCountP : 0 } };
    const averageClockEObj = { "E Core Clocks": { "type": "clock", "value": clockCountE !== 0 ? clockSumE / clockCountE : 0 } };

    const averageTempObj = { "Core Temps": { "type": "temperature", "value": tempCount !== 0 ? tempSum / tempCount : 0 } };
    const averageTempPObj = { "P Core Temps": { "type": "temperature", "value": tempCountP !== 0 ? tempSumP / tempCountP : 0 } };
    const averageTempEObj = { "E Core Temps": { "type": "temperature", "value": tempCountE !== 0 ? tempSumE / tempCountE : 0 } };

    const averageUsageObj = { "Core Usages": { "type": "usage", "value": usageCount !== 0 ? usageSum / usageCount : 0 } };

    const averageUsagePObj = { "P Core Usages": { "type": "usage", "value": usageCountP !== 0 ? usageSumP / usageCountP : 0 } };
    const averageUsageEObj = { "E Core Usages": { "type": "usage", "value": usageCountE !== 0 ? usageSumE / usageCountE : 0 } };

    let CoreVidFound = false, CoreClockFound = false, CoreTempFound = false, CoreUsageFound = false;
    let pCoreVidFound = false, pCoreClockFound = false, pCoreTempFound = false, pCoreUsageFound = false;
    let eCoreVidFound = false, eCoreClockFound = false, eCoreTempFound = false, eCoreUsageFound = false;

    SensorInfoKeys.forEach(sensorKey => {
        let resultData = [];
        if (SensorInfo[sensorKey]) {
            SensorInfo[sensorKey].forEach(sensorItem => {
                const sensorItemKey = Object.keys(sensorItem)[0];
                const sensorItemKeyUpper = sensorItemKey.toUpperCase()
                const type = sensorItem[sensorItemKey]['type'];
                if (sensorKey.includes('CPU'))
                {
                    if (vidCount !== 0 && !CoreVidFound) {
                        resultData.push(averageVidObj);
                        CoreVidFound = true
                    }
                    if (vidCountP !== 0 && !pCoreVidFound) {
                        resultData.push(averageVidPObj);
                        pCoreVidFound = true
                    }
                    if (vidCountE !== 0 && !eCoreVidFound) {
                        resultData.push(averageVidEObj);
                        eCoreVidFound = true
                    }
                    if (clockCount !== 0 && !CoreClockFound) {
                        resultData.push(averageClockObj);
                        CoreClockFound = true
                    }
                    if (clockCountP !== 0 && !pCoreClockFound) {
                        resultData.push(averageClockPObj);
                        pCoreClockFound = true
                    }
                    if (clockCountE !== 0 && !eCoreClockFound) {
                        resultData.push(averageClockEObj);
                        eCoreClockFound = true
                    }
                    if (usageCount !== 0 && !CoreUsageFound) {
                        resultData.push(averageUsageObj);
                        CoreUsageFound = true
                    }
                    if (usageCountP !== 0 && !pCoreUsageFound) {
                        resultData.push(averageUsagePObj);
                        pCoreUsageFound = true
                    }
                    if (usageCountE !== 0 && !eCoreUsageFound) {
                        resultData.push(averageUsageEObj);
                        eCoreUsageFound = true
                    }
                }
                resultData.push(sensorItem);
            });
            SensorInfo[sensorKey] = resultData;
        }
    });
    return SensorInfo;
}
