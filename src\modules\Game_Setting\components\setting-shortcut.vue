<template>
  <!--快捷键-->
  <div id="kjj"></div>
  <el-collapse-item :title="$t('Setting.shortcut')" name="8">
    <template #title>
      <div class="gxh_title">
        <span style="font-size: .12rem;color:var(--font-color)">{{$t('Setting.shortcut')}}</span>
        <span @click.stop="DefaultShotcut" style="margin-left: auto;color: var(--active-color);display:flex;"><span class="iconfont icon-switch"></span> <span>{{$t('Setting.restoreDefault')}}</span></span>
      </div>
    </template>
    <div class="setting-item">
      <section class="left-box">
        <ul>
          <li v-for="item in shortcutList" :key="item.id">
            <span>{{$t(item.title).replace(':','').replace('：','')}}</span>
            <shortcut :id="item.id" />
          </li>
        </ul>
      </section>
      <section class="right-box">
        <ul>
          <li v-for="item in shortcutList2" :key="item.id">
            <span>{{$t(item.title)}}</span>
            <shortcut :id="item.id" />
          </li>
          <li style="display: block">
            <text>{{$t('Setting.text6')}}</text>
            <br>
            <text>{{$t('Setting.text7')}}</text>
          </li>
        </ul>
      </section>
    </div>
  </el-collapse-item>
</template>

<script setup lang="ts">
import {ref} from "vue";
import {gameppBaseSetting} from '@/modules/Game_Home/stores/index'
import {useScroll} from '../hooks/useScroll'
import shortcut from '@/components/eleCom/shortcut.vue'
useScroll('kjj');
// @ts-ignore
const gamepp = window.gamepp;

const store = gameppBaseSetting();

const shortcutList = ref([
  {
    title: 'messages.ingameControlPanel',
    id: 9,
  },
  {
    title: 'messages.openOrCloseGameInSettings3',
    id: 15,
  },
  {
    title: 'messages.openOrCloseGameInSettings4',
    id: 13,
  },
  {
      title: 'video.startStopRecord',
      id: 44,
  },
    {
        title: 'video.instantReplay',
        id: 47,
    }
])
const shortcutList2 = ref([
  {
    title: 'messages.EnableAIfilter',
    id: 389,
  },
  {
    title: 'messages.Start_stop',
    id: 468,
  },
  {
    title: 'messages.performanceStatisticsMark',
    id: 527,
  },
  {
    title: 'hardwareInfo.Screenshot',
    id: 60,
  },
])
const sc_broadcast = new BroadcastChannel('shortcut')
const DefaultShotcut = ()=>{
  GPP_WriteString(9, 'Ctrl+TAB')
  store.state.shortcutKey[9] = 'Ctrl+TAB'
  GPP_WriteString(15, 'Ctrl+F10')
  store.state.shortcutKey[15] = 'Ctrl+F10'
  GPP_WriteString(13, 'Ctrl+F5')
  store.state.shortcutKey[13] = 'Ctrl+F5'
  GPP_WriteString(389, 'Shift+F6')
  store.state.shortcutKey[389] = 'Shift+F6'
  GPP_WriteString(468, 'Shift+F10')
  store.state.shortcutKey[468] = 'Shift+F10'
  GPP_WriteString(527, 'Shift+F11')
  store.state.shortcutKey[527] = 'Shift+F11'
  GPP_WriteString(60, 'F9')
  store.state.shortcutKey[60] = 'F9'
  GPP_WriteString(44, 'Shift+F12')
  store.state.shortcutKey[44] = 'Shift+F12'
  GPP_WriteString(47, 'Shift+F8')
  store.state.shortcutKey[47] = 'Shift+F8'
  sc_broadcast.postMessage({action: 'set_default'})
}
async function GPP_WriteString(id: number, value: string) {
  console.log('setString,' + '' + id + ',' + value);
  try {
    await gamepp.setting.setString.promise(id, value);
  } catch (error) {
  }
}
</script>

<style scoped lang="scss">
#kjj {
  margin-top: 20px;
}
.gxh_title {
  width: 100%;
  color: var(--font-color);
  font-size: .12rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 15px;
}
.left-box,.right-box {
  ul {
    li {
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      margin-bottom: 20px;

      &:last-child{
        margin-bottom: 0;
      }

      span {
        width: 270px;

        &:last-child {
          margin-left: auto;
          color: #BF4040;
        }
      }
    }
  }
}
.clear {
  margin: 0 !important;
  color: #BF4040;
  cursor: pointer;
  height: 30px;
  width: 28px !important;
  font-size: .12rem;
  line-height: 30px;
}
</style>
