<script setup lang="ts">
   import { ref,reactive,computed,onBeforeMount,onMounted,onBeforeUnmount,inject} from 'vue';
   import { useRouter } from "vue-router";
   import { ElMessage } from "element-plus";
   import { gameppBaseSetting,hardware,moduleInfo,} from  '../stores';
   import { firstPageInfo } from '../stores';
   import { useWaterFall }  from '@/modules/Game_Home/hooks/waterFall'
   import { gamepp } from 'gamepp'
   import { useZoomStore } from '../stores/zoomStore';
   import { getGameList , getHistoryList  ,getTime ,getAllFreeGameList} from "../../Game_FreeGame/utils/receiveGame";
   import { useI18n } from "vue-i18n";

   let timer3:any = null
   const {t} = useI18n()
   const benching = ref(false)
   const countdown = ref('00:00:00');
   const gameppsetting = gameppBaseSetting()
   const moduleInfosetting:any = moduleInfo()
   const zoomStore = useZoomStore();
   const router = useRouter()
   const firstPage = firstPageInfo()
   const reboundProgress =  ref(0)
   const mirrorProgress = ref(0)
   const processProgress = ref(0)
   const FreeGameProgress = ref(0)
   const GameObsProgress = ref(0)
   const AiLabProgress = ref(0)
   const MoProgress  = ref(0)
   const TestProgress  = ref(0)
   const allGameList:any = ref([{
      cover:'',
      game_name:'',
      platform:'',
      get_url:'',
      original_price:'',
      expired_date:''
   }])
   const modules:any = [
         { name: 'GameRebound', tabIndex: 3, textIndex: 0 },
         { name: 'GameMirror', tabIndex: 4, textIndex: 1 },
         { name: 'GameCoreAssign', tabIndex: 5, textIndex: 2 },
         { name: 'GameFreeGame', tabIndex: 6, textIndex: 3 },
         { name: 'GameObs', tabIndex: 7, textIndex: 4 },
         { name: 'AiLab', tabIndex: 8, textIndex: 5 },
         { name: 'DesktopMonitor', tabIndex: 11, textIndex: 6 },
         { name: 'PressureTest', tabIndex: 12, textIndex: 7 },

      ];
   const moduleMap:any = {
         3: { name: 'GameRebound', textIndex: 0 },
         4: { name: 'GameMirror', textIndex: 1 },
         5: { name: 'GameCoreAssign', textIndex: 2 },
         6: { name: 'GameFreeGame', textIndex: 3 },
         7: { name: 'GameObs', textIndex: 4 },
         8: { name: 'AiLab', textIndex: 5 },
         11: { name: 'DesktopMonitor', textIndex: 6 },
         12: { name: 'PressureTest', textIndex: 7 },
   };
   const textArr:any = ref(['home.newVersion','home.newVersion','home.newVersion','home.newVersion','home.newVersion','home.newVersion','home.newVersion','home.newVersion'])
   let timer1:any
   let mirrorInfo = reactive({
      name:'GameMirror.Bright',
      text:'GameMirror.BrightTips'
   })

   let playerNum = ref(0)

   let recentGameInfo:any = reactive({
      gameName:'???',
      resolution:'???',
      gametime:'???',
      iconsrc:'',
      starttime:0,
      cputemperature:
      {
         avg:'???',
         min:'???',
         max:'???',
      },
      fps:{
         avg:'???',
         min:'???',
         max:'???',
         fps01:'???'
      },
      gputemperature:
      {
         avg:'???',
         min:'???',
         max:'???',
      },
})

   const { menuInfo } = useWaterFall([
      {height:260,top:0,left:0},//0
      {height:270,top:0,left:0},//1
      {height:280,top:0,left:0},//2
      {height:190,top:0,left:0},//3
      {height:170,top:0,left:0},//4
      {height:275,top:0,left:0},//5
      {height:170,top:0,left:0},//6
      {height:275,top:0,left:0},
      {height:135,top:0,left:0},
      {height:135,top:0,left:0},
      {height:170,top:0,left:0},//
      {height:170,top:0,left:0},//
      // {height:80, top:0,left:0},
   ])

   let  monitorItem =  ref<any>([])

   const mirrorText:any = ref(
      {
         "GameMirror.AiMingliang":'GameMirror.liangTips',
         "GameMirror.AiBright":'GameMirror.anTips',
         "GameMirror.AiDark":'GameMirror.jianyiTips',
         "GameMirror.AiBalance":'GameMirror.shiTips',
         'GameMirror.Bright':'GameMirror.BrightTips',
         'GameMirror.Soft':'GameMirror.ruiTips',
         'GameMirror.Highlight':'GameMirror.qingTips',
         'GameMirror.Benq':'GameMirror.xianTips',
         'GameMirror.Film':'GameMirror.dianTips',
         'GameMirror.AntiGlare':'GameMirror.benTips',
         'GameMirror.Day':'GameMirror.fangTips',
         'GameMirror.Nature':'GameMirror.jiaoTips',
         'GameMirror.smooth':'GameMirror.jieTips',
         'GameMirror.highDefinition':'GameMirror.jingTips',
         'GameMirror.warm':'GameMirror.xiuTips',
         'GameMirror.Brightness':'GameMirror.qihuanTips',
         'GameMirror.vivid':'GameMirror.shengTips',
         'GameMirror.clear':'GameMirror.sheTips',
         'GameMirror.sharp':'GameMirror.she2Tips',
         'GameMirror.Night':'GameMirror.an2Tips',
         'GameMirror.elegant':'GameMirror.wenTips',
         'GameMirror.beauty':'GameMirror.jing2Tips',
         'GameMirror.HighSaturation':'GameMirror.jing3Tips'
      }
   )
   // const currentLanguage = ref('CN');
   const isSetTimedShutdown = ref(false)
   const shutdownCountDown = ref('00:00:00:000')
   let shutdownCountDownTimer:any = null
   const currentLanguage = inject('currentLanguage');
//////////////////////////不要在周期函数里面写代码 写方法！！！！/////////////////////////////////

   onBeforeMount(async () =>
   {
      window.addEventListener('storage',(e) => { // 监听indexedDB变化
        if (e.key === 'InGameList1')
        {
          getmonitorItem()
        }else if (e.key === 'isUserSetScheduledShutdown') {
            checkIsUserSetScheduledShutdown()
        }
      //   if(e.key === 'language') {
      //       currentLanguage.value = e.newValue || 'CN';
      //    }
      })
      initModuleSet()


   })
//////////////////////////不要在周期函数里面写代码 写方法！！！！/////////////////////////////////

   onMounted( async() =>
   {



      try
      {
         allGameList.value= await getGameList()
         console.log('%callGameList.value3','color:red',allGameList.value);
      }
      catch
      {
         allGameList.value= await getGameList()
         console.log('%callGameList.value4','color:red',allGameList.value);
      }
       getMainInfo() //获取首页基本信息
       await getSensorInfo() //获取传感器
       createListener() //创建监听器
       await checkModule()
       ListenerOfModules()
      //  loadLanguage()
      //  timer3 = setInterval(updateCountdown, 1000);
       timer3 = setInterval(() => {
         updateCountdown()
       }, 1000);
       checkIsUserSetScheduledShutdown()
       try
      {
         allGameList.value= await getGameList()
         console.log('%callGameList.value1','color:red',allGameList.value);

      }
      catch
      {
         allGameList.value= await getGameList()
         console.log('%callGameList.value2','color:red',allGameList.value);
      }
   })

   onBeforeUnmount(()=> //毁灭
   {
      clearInterval(timer1)
      clearInterval(timer3)
      timer1 = null

   })

   const checkIsUserSetScheduledShutdown = () => {
       const isUserSetScheduledShutdown = window.localStorage.getItem('isUserSetScheduledShutdown')
       if (isUserSetScheduledShutdown === '1') {
           isSetTimedShutdown.value = true;
           let scheduledShutdownTimestamp:any = window.localStorage.getItem('ScheduledShutdownTimestamp');
           if (!scheduledShutdownTimestamp) {
               return
           }
           if (shutdownCountDownTimer) clearInterval(shutdownCountDownTimer)
           shutdownCountDownTimer = setInterval(()=>{
               const leftTime = scheduledShutdownTimestamp - Date.now();
               if (leftTime > 0) {
                   const leftHour = Math.floor(leftTime / 3600000)
                   const leftMinute = Math.floor((leftTime - leftHour * 3600000) / 60000)
                   const leftSecond = Math.floor((leftTime - leftHour * 3600000 - leftMinute * 60000) / 1000)
                   const leftMillisecond = leftTime - leftHour * 3600000 - leftMinute * 60000 - leftSecond * 1000
                   const Hour = leftHour < 10 ? `0${leftHour}` : leftHour + ''
                   const Minute = leftMinute < 10 ? `0${leftMinute}` : leftMinute + ''
                   const Second = leftSecond < 10 ? `0${leftSecond}` : leftSecond + ''
                   const Millisecond = leftMillisecond < 10 ? `00${leftMillisecond}` : leftMillisecond < 100 ? `0${leftMillisecond}` : leftMillisecond + ''
                   shutdownCountDown.value = `00:${Hour}:${Minute}:${Second}:${Millisecond}`
               }else{
                   shutdownCountDown.value = '00:00:00:000'
                   isSetTimedShutdown.value = false;
                   clearInterval(shutdownCountDownTimer)
               }
           },16)
       }else{
           if (shutdownCountDownTimer) clearInterval(shutdownCountDownTimer)
           shutdownCountDown.value = '00:00:00:000'
           isSetTimedShutdown.value = false;
       }
   }

   const GPP_OpenPage = (url:string)=>
   {
      try
      {
         gamepp.shell.openExternal(url);
      }
      catch (error)
      {
         window.open(url)
      }
   }

   const GPP_OpenURL = (num:number) =>
   {
      const WebList:any = [
         'https://bb.gamepp.com/video/hw_info',
         'https://bb.gamepp.com/video/select_sensor',
         'https://bb.gamepp.com/video/in_game_monitor',
         'https://bb.gamepp.com/video/report',
         'https://bb.gamepp.com/video/reshade',
        'https://bb.gamepp.com/video/hw_info',
        'https://bb.gamepp.com/video/hw_info',
      ]
      let url = WebList[num]
      try
      {
         gamepp.shell.openExternal(url);
      }
      catch (error)
      {
         window.open(url)
      }
   }

   // const loadLanguage = () => {
   //     const savedLanguage = localStorage.getItem('language');
   //    if (savedLanguage) {
   //       currentLanguage.value = savedLanguage;
   //    }
   // };
   const updateCountdown = async() =>
      {
         if(allGameList.value[0].expired_date == '' || !allGameList.value[0].expired_date)
         {
            console.warn(allGameList.value);

            allGameList.value= getGameList()
            return
         }

         const now:any = new Date();
         let time:any = new Date(allGameList.value[0].expired_date)
         const timeDifference:any = time - now;

         if (timeDifference > 0) {
            const days = Math.floor(timeDifference / (1000 * 60 * 60 * 24));
            const hours = Math.floor((timeDifference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((timeDifference % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((timeDifference % (1000 * 60)) / 1000);

            countdown.value = `${days}:${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
         } else {
            countdown.value = '00:00:00';
         }
      }

   const createListener = () =>
   {
      gamepp.webapp.onInternalAppEvent.addEventListener((e) =>
      {
         console.warn('Get Message：',e);
         let msg
         try
         {
            msg = e['action']
            console.warn('msg:',msg);
            switch(msg)
            {
               case 'changeMirror':
               getMirrorText();//更新滤镜信息 ·
               break;

               case 'changeRebound':
               console.log('主页收到更新消息');
               setTimeout(() => {
                  getRecent()//更新性能统计
               }, 1000);
               break;
               case 'clearRebound':
                  console.warn('清除性能统计数据');
                  clearRebound()
            }
         }
         catch(err){}

      });
   }

   const clearRebound = () =>
   {
      recentGameInfo.value = {
            gameName:'???',
            resolution:'???',
            gametime:'???',
            iconsrc:'',
            starttime:0,
            fps:{
               avg:'???',
               min:'???',
               max:'???',
               fps01:'???'
            }
         }
   }

   const getNum = (startDate:any, currentDate:any) =>
   {
         const diffTime = currentDate - startDate;
         const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
         const num = diffDays * 250;
         console.warn('diffDays',diffDays);
         return num;
   }

   const getmonitorItem = () =>
   {
      let Info
      const nameList:any = []
      try
      {
         Info = localStorage.getItem('InGameList1')
      }
         catch(err){console.warn(err);
      }

      if(Info == null){return}
      JSON.parse(Info).flat(1).forEach((v:any)=>{
         let obj =
         {
            name:v.name,
            des:v.des
         }
         nameList.push(obj)
      })
       monitorItem.value = [...new Set(nameList)]
   }

   const updataLocal = () =>
   {
      localStorage.setItem('moduleInfosetting',JSON.stringify(moduleInfosetting.tabInfo))
   }

   const initModuleSet = async() =>  //初始化模块信息
   {
      moduleInfosetting.tabInfo[0]['download']['installed'] = true
      let bret = localStorage.getItem('moduleInfosetting')
      if(bret != null)
      {
         let Info = JSON.parse(bret)
         if (Info.length !== moduleInfosetting.tabInfo.length)
         {
            console.warn('Info:', Info);
            console.warn('长度不相等');
            moduleInfosetting.tabInfo.forEach((itemB: any) =>
            {
               const existsInA = Info.some((itemA: any) => itemA.name === itemB.name);
               if (!existsInA)
               {
                  moduleInfosetting.tabInfo.push(itemB);
               }
            });

            // 同步更新localStorage
            localStorage.setItem('moduleInfosetting', JSON.stringify(moduleInfosetting.tabInfo));
            console.warn('moduleInfosetting.tabInfo', moduleInfosetting.tabInfo);
         }
         else
         {
            moduleInfosetting.tabInfo = JSON.parse(bret)
            //初始化安装中
            moduleInfosetting.tabInfo.forEach((v:any)=>
            {
               v['download']['installing'] = false
            })
            console.warn('长度相等');
         }
      }
      else
      {
         console.warn('初始化Local!!!!');
         localStorage.setItem('moduleInfosetting',JSON.stringify(moduleInfosetting.tabInfo))
      }

      let index:number = moduleInfosetting.tabInfo.findIndex((item:any,index:number) => {
         return item.choosen
      })

      router.push({
         path: moduleInfosetting.tabInfo[index].route
      })
      console.warn('被激活的index:',index);
   }

   const getMainInfo = () =>
   {
      getMirrorText()
      getRecent()
      getmonitorItem()
      const startDate = new Date(2018, 3, 1); // 注意月份是从0开始的，所以4月是3
      // 获取当前日期的时间戳
      const currentDate = Date.now();

      // 计算num的值
      playerNum.value = getNum(startDate, currentDate)
      console.warn('游玩人数：',playerNum.value);
   }

   //获取滤镜使用
   const getMirrorText = () =>
   {
      let text = localStorage.getItem('mirrorText_mutilang')
      if(text)
      {
         mirrorInfo['name'] = JSON.parse(text)
         mirrorInfo['text'] = mirrorText.value[JSON.parse(text)] ?? ''
      }
   }

   //获取近期游玩数据
   const getRecent = () =>
   {
      console.warn('Module Info::',moduleInfosetting.tabInfo);

      if(!moduleInfosetting.tabInfo[3]['download']['installed']){
         return
      }
      let local:any = localStorage.getItem('recentGame') //更新最新游戏近况
      if(local)
      {
         console.log('Recent Game::',local);
         let Info  = JSON.parse(local)
         console.warn("Recent info::",Info);
         recentGameInfo.gameName = Info['gameName']
         recentGameInfo.resolution = Info['resolution']
         recentGameInfo.gametime = Info['gametime'] ? (Info['gametime'].replace('小时',":").replace('分钟',":").replace('秒',"")) : '';
         recentGameInfo.iconsrc = Info['iconsrc']
         recentGameInfo.starttime = Info['starttime']
         recentGameInfo.fps = Info['fps']
         if(Info['cputemperature'] && Info['cputemperature'] != '')
         {
            recentGameInfo.cputemperature = Info['cputemperature']
            recentGameInfo.gputemperature = Info['gputemperature']
         }

      }
   }

   //固定至左侧导航栏
   const setTabInleft = (index:number,bool:boolean) =>
   {
         moduleInfosetting.tabInfo[index].Inleft = bool

         console.warn('setTabLeft // Module Info::',moduleInfosetting.tabInfo);
         updataLocal()
   }

   //计算属性 存储下载状态
   const Rebound_Uninstall = computed(() => {
         console.warn('Rebound_Uninstall changed');
         const downloadInfo = !moduleInfosetting.tabInfo[3]['download']['installed'] && !moduleInfosetting.tabInfo[3]['download']['installing']
         return downloadInfo
   });

   const Rebound_Installing = computed(() => {
         console.warn('Rebound_Installing changed');
         const downloadInfo = moduleInfosetting.tabInfo[3]['download']['installing']
         return downloadInfo
   });

   const Mirror_Uninstall = computed(() => {
         console.warn('Mirror_Uninstall changed');
         const downloadInfo = !moduleInfosetting.tabInfo[4]['download']['installed'] && !moduleInfosetting.tabInfo[4]['download']['installing']
         return downloadInfo
   });

   const Mirror_Installing = computed(() => {
         console.warn('Mirror_Installing changed');
         const downloadInfo = moduleInfosetting.tabInfo[4]['download']['installing']
         return downloadInfo
   });

   const Process_Uninstall = computed(() => {
         console.warn('v changed');
         const downloadInfo = !moduleInfosetting.tabInfo[5]['download']['installed'] && !moduleInfosetting.tabInfo[5]['download']['installing']
         return downloadInfo
   });

   const Process_Installing = computed(() => {
         console.warn('Process_Installing changed');
         const downloadInfo = moduleInfosetting.tabInfo[5]['download']['installing']
         return downloadInfo
   });

   const FreeGame_Uninstall = computed(() => {
         console.warn('v changed');
         const downloadInfo = !moduleInfosetting.tabInfo[6]['download']['installed'] && !moduleInfosetting.tabInfo[6]['download']['installing']
         return downloadInfo
   });

   const FreeGame_Installing = computed(() => {
         console.warn('Process_Installing changed');
         const downloadInfo = moduleInfosetting.tabInfo[6]['download']['installing']
         return downloadInfo
   });

   const OBS_Uninstall = computed(() => {
         console.warn('v changed');
         const downloadInfo = !moduleInfosetting.tabInfo[7]['download']['installed'] && !moduleInfosetting.tabInfo[7]['download']['installing']
         return downloadInfo
   });

   const OBS_Installing = computed(() => {
         console.warn('Process_Installing changed');
         const downloadInfo = moduleInfosetting.tabInfo[7]['download']['installing']
         return downloadInfo
   });

   const haha_Uninstall = computed(() => {
         console.warn('v changed');
         const downloadInfo = !moduleInfosetting.tabInfo[8]['download']['installed'] && !moduleInfosetting.tabInfo[8]['download']['installing']
         return downloadInfo
   });

   const haha_Installing = computed(() => {
         console.warn('Process_Installing changed');
         const downloadInfo = moduleInfosetting.tabInfo[8]['download']['installing']
         return downloadInfo
   });

   const mo_Installing = computed(() => {
         console.warn('Process_Installing changed');
         const downloadInfo = moduleInfosetting.tabInfo[11]['download']['installing']
         return downloadInfo
   });

   const mo_Uninstall = computed(() => {
         console.warn('v changed');
         const downloadInfo = !moduleInfosetting.tabInfo[11]['download']['installed'] && !moduleInfosetting.tabInfo[11]['download']['installing']
         return downloadInfo
   });

   const test_Installing = computed(() => {
         console.warn('Process_Installing changed');
         const downloadInfo = moduleInfosetting.tabInfo[12]['download']['installing']
         return downloadInfo
   });

   const test_Uninstall = computed(() => {
         console.warn('v changed');
         const downloadInfo = !moduleInfosetting.tabInfo[12]['download']['installed'] && !moduleInfosetting.tabInfo[12]['download']['installing']
         return downloadInfo
   });

   const checkModule = async () =>
   {
         // 自动更新设置
         const Auto_update = JSON.parse(localStorage.getItem('alwaysUpdateModules') as any);
         for (const module of modules)
         {
            const { name, tabIndex, textIndex } = module;
            // 检查模块是否存在
            const moduleExists = await gamepp.package.isexists.promise(name);
            console.warn(`need_update:${name}_exist`, moduleExists);
            moduleInfosetting.tabInfo[tabIndex]['download']['installed'] = moduleExists;
            if (moduleExists)
            {
               const need_update = await gamepp.package.checkupdate.promise(name);
               console.warn(`need_update:${name}`, need_update);
               if (need_update.result)
               {
                  moduleInfosetting.tabInfo[tabIndex]['download']['isLatest'] = false;
                  textArr.value[textIndex] = 'home.discoverUpdate';
                  // 自动更新
                  if (Auto_update)
                  {
                     downloadModule(tabIndex);
                  }
               }
               else
               {
                  moduleInfosetting.tabInfo[tabIndex]['download']['isLatest'] = true;
               }
            }
         }
   };

   const DownLoadShutdown = () =>
   {
      moduleInfosetting.tabInfo[9]['download']['installed'] = true;
   }

   const DownLoadscreenshot = () =>
   {
      moduleInfosetting.tabInfo[10]['download']['installed'] = true;
   }

   const OpenShutdown = (index:number) =>
   {
      if(moduleInfosetting.tabInfo[index].download['installed'])
      {
         gamepp.webapp.windows.show.sync(moduleInfosetting.tabInfo[index].windowName,'desktop')
      }
      else
      {
         ElMessage({
            message: t('messages.moduleNotInstalled'),
            type: 'warning',
            grouping:true
         })

         console.warn('功能模块未安装',moduleInfosetting.tabInfo);
      }
   }

    const downloadObs = async () => {
        const OBSexist = await gamepp.package.isexists.promise('obs')
        console.warn('exist',OBSexist);
        if(!OBSexist) {
            let bUpdate = await gamepp.package.checkupdate.promise('obs')
            console.warn('开始下载',bUpdate);
            if(bUpdate.result)
            {
                startDownload()
                let result = await gamepp.package.startdownload.promise('obs',bUpdate.url,bUpdate.md5,bUpdate.version)
                if(result) //下载完成
                {
                    await gamepp.obs.runOBSClient.promise()
                    downloadModule(7)
                }
                else
                {
                    ElMessage.error('下载失败，请检查网络环境')
                }
            }else{
                ElMessage.error('下载失败，请检查网络环境')
            }
        }else{
            // 存在obs只下载页面
            downloadModule(7)
        }
    }
   function startDownload()
   {
       gamepp.package.onPackageDownloadStart.addEventListener((value,value2) => {
           console.warn('onPackageDownloadStart:',value,value2);
       });

       gamepp.package.onPackageDownloadSpeed.addEventListener((value,value2) => {
           console.warn('onPackageDownloadSpeed:',value,value2);
       });

       gamepp.package.onPackageDownloadComplete.addEventListener((value,value2) => {
           console.warn('onPackageDownloadComplete:',value,value2);
       });

       gamepp.package.onPackageDownloadExtractComplete.addEventListener((value,value2) => {
           console.warn('onPackageDownloadExtractComplete:',value,value2);
       });
   }
   const downloadModule = async (index: number) =>
   {
         const moduleInfo = moduleMap[index];
         if (!moduleInfo)
         {
            console.error('Invalid module index:', index);
            return;
         }
            const { name, textIndex } = moduleInfo;

            moduleInfosetting.tabInfo[index]['download']['installing'] = true;
            textArr.value[textIndex] = 'home.downloading';
         try
         {
            const checkUpdate = await gamepp.package.checkupdate.promise(name);
            console.warn('checkUpdate', checkUpdate);
            if (checkUpdate.result)
            {
               const result = await gamepp.package.startdownload.promise(name, checkUpdate.url, checkUpdate.md5, checkUpdate.version);
               console.warn('result', result);
               if(index == 12)
               {
                  if(result)
                  {
                     benching.value = true
                  }
                  let bench_ = await gamepp.package.checkupdate.promise('PCBenchmark')
                  let status = false
                  if(bench_.result)
                  {
                     status = await gamepp.package.startdownload.promise('PCBenchmark',bench_.url,bench_.md5,bench_.version)
                  }
                  else
                  {
                     status = true
                  }

                  if (status && result)
                  {
                     benching.value = false
                     moduleInfosetting.tabInfo[index]['download']['installed'] = true;
                     moduleInfosetting.tabInfo[index]['download']['installing'] = false;
                     moduleInfosetting.tabInfo[index]['download']['isLatest'] = true;
                     console.warn('download already！',moduleInfosetting.tabInfo[index]);
                  }
                  else
                  {
                     throw new Error('Download failed');
                  }
               }
               else
               {
                  if (result)
                  { //下载完成
                     moduleInfosetting.tabInfo[index]['download']['installed'] = true;
                     moduleInfosetting.tabInfo[index]['download']['installing'] = false;
                     moduleInfosetting.tabInfo[index]['download']['isLatest'] = true;
                     console.warn('download already！',moduleInfosetting.tabInfo[index]);

                  }
                  else
                  {
                     throw new Error('Download failed');
                  }
               }

            }
            else
            {
               throw new Error('No update available');
            }
         }
         catch (error)
         {
            ElMessage({message:'下载或更新失败：请检查网络状况',type:'warning',grouping:true});
            moduleInfosetting.tabInfo[index]['download']['installing'] = false;
            textArr.value[textIndex] = 'home.retry';
         }
   };

   const ListenerOfModules = () =>
   {
      gamepp.package.onPackageDownloadSpeed.addEventListener((moduleName, downloadDetails) => {
         console.warn(`模块名称:: ${moduleName}`, ';下载详情',downloadDetails);
         if(!moduleName)return
         const progressMap:any =
         {
               GameRebound: reboundProgress,
               GameMirror: mirrorProgress,
               GameCoreAssign: processProgress,
               GameFreeGame: FreeGameProgress,
               GameObs: GameObsProgress,
               AiLab: AiLabProgress,
               DesktopMonitor: MoProgress,
               PressureTest: TestProgress,
         };
         const progress = progressMap[moduleName];
         if (progress)
         {
               progress.value = Number(downloadDetails.percent).toFixed(0);
               if (progress.value === 100)
               {
                  setTimeout(() => {
                     progress.value = 0;
                  }, 1000);
               }
         }
      });
   };

   //卸载模块
   const uninstallModule = async (index:number) =>
   {

   }

   //获取首页信息
   const getSensorInfo = async () =>
   {
      timer1 = setInterval(async () => {
            try
            {
               let bg_sensor_data = JSON.parse(window.localStorage.getItem('bg_sensor_data')!)
               firstPage.info.hardware[0].name = bg_sensor_data['cpu']['name']
               firstPage.info.hardware[1].name = bg_sensor_data['gpu']['name']
               firstPage.info.hardware[0].value = bg_sensor_data['cpu']['temp']
               firstPage.info.hardware[1].value = bg_sensor_data['gpu']['temp']
               // console.log('bg_sensor_data',bg_sensor_data);
            }
            catch
            {
               // console.log('错误！！获取传感器失败');
            }

            }, 2000);
   }
   //路由跳转
   const routepage = async(pageIndex:number) =>
   {
        if(moduleInfosetting.tabInfo[pageIndex].module)
        { //外部模块
            if(moduleInfosetting.tabInfo[pageIndex].download['installed'])
            {
               if(gamepp.package.islibversionmatch.sync(moduleMap[pageIndex].name))
               {
                  gamepp.webapp.windows.show.sync(moduleInfosetting.tabInfo[pageIndex].windowName,'desktop')
               }
               else
               {
                  ElMessage({ message: '模块需更新',type: 'warning',grouping:true})
               }
            }
            else
            {
               ElMessage({
                  message: t('messages.moduleNotInstalled'),
                  type: 'warning',
                  grouping:true
               })

               console.warn('功能模块未安装',moduleInfosetting.tabInfo);
            }
        }
        else
        {
            moduleInfosetting.tabInfo.forEach((v:any,i:any)=>{v.choosen = false})
            moduleInfosetting.tabInfo[pageIndex].choosen = true
            router.push({path: moduleInfosetting.tabInfo[pageIndex].route})
        }

      const InleftTab = moduleInfosetting.tabInfo.filter((v:any)=>{
         return v.Inleft == true
      })

      const index = InleftTab.findIndex((v:any)=>{
            return v.choosen == true
         })
      if(index != -1){
        moduleInfosetting.activeTab = index
      }
      updataLocal()
   }

const messageStyle = computed(() => {
   return {
      // width:  `${(gameppsetting.gameppWidth-(160*zoomStore.zoomLevel)) / zoomStore.zoomLevel}px`
      width:  `80%`
   }
})

const getColor = (value:any) =>
{
   if (value > 80)
   {
        return '#BF4040'
   }
   else if (value > 50)
   {
        return '#3579D5';
   }
   else
   {
        return '#35D5B1';
   }
}

const getRecentTestInfo = async() =>
{
   let recentInfo = JSON.parse(localStorage.getItem('benchmark') as any)
}
</script>

<template>
   <div class="outside"  :style="{height:`${(gameppsetting.gameppHeight - (50*zoomStore.zoomLevel)) / zoomStore.zoomLevel}px`}" style="display: flex;flex-direction:column;margin-top: 10px;">
      <div class="message" :style="messageStyle"><span style="margin-left: 20px;font-size: 16px;font-weight: bold">{{$t('home.homeTitle')}}</span></div>
      <div class="Main scroll" :style="{height:  `${(gameppsetting.gameppHeight  - (100*zoomStore.zoomLevel) )/ zoomStore.zoomLevel}px`}">
         <!-- 硬件信息 -->
         <div class="item hardware border" :style = "{ height: '260px',top: '20px',left: '20px'}"  @click.stop="routepage(1)">
            <div class="top">
                  <div class="title"><span class="iconfont icon-hardwaredetail" style="color:#3579D5;font-size:20px;margin-right: 10px"></span>{{$t('home.hardwareInfo')}}</div>
                   <div class="toolBar">
                     ...
                     <div class="content" v-show="firstPage.info.hardware[0].name != ''">
                          <div class="settingitem"  @click.stop="GPP_OpenURL(0)">{{$t('home.functionIntroduction')}}</div>
                          <div class="settingitem" v-show="!moduleInfosetting.tabInfo[1].Inleft" @click.stop="setTabInleft(1,true)">{{$t('home.fixedToNav')}}</div>
                          <div class="settingitem" v-show="moduleInfosetting.tabInfo[1].Inleft" @click.stop="setTabInleft(1,false)">{{$t('home.cancelFixedToNav')}}</div>
                     </div>
                  </div>
               </div>
             <div
               v-loading = "firstPage.info.hardware[0].name == ''"
               element-loading-background="#2d2e39"
               :element-loading-text="$t('home.hardwareInfoLoading')"
             >
               <div class="hardwareItem" v-for="(item,index) in firstPage.info.hardware">
                  <div class="left">
                  <span  :style="{fontSize: 20+'px',width: 30+'px',height: 30+'px',color:getColor(item.value)}"  :class="item.icon"></span>
                  <!-- <img src="../../assets/01.jpg" alt=""> -->
                  <div class="left_info">
                     <div style="color:#777777;">{{ item.type }}</div>
                     <div><span>{{ item.name }}</span></div>
                  </div>
                  </div>
                  <div class="right">
                     <div :style="{color:getColor(item.value),fontWeight:'bold'}"><span>{{ item.value }}℃</span></div>
                     <!-- <div :style="{color:item.value > 80 ? '#BF4040' : item.value > 50 ? '#3579D5' : '#35D5B1',fontWeight:'bold'}"><span>{{ item.value }}℃</span></div> -->
                  </div>
            </div>
             </div>
         </div>
         <!-- 游戏内监控 -->
         <div class="item monitor  monitorAuto border scroll" :style="{height:menuInfo[1].height+'px',top:20+'px',left:640+'px'}"  @click.stop="routepage(2)">
            <div class="top">
                  <div class="title"><span class="iconfont icon-fpsyc"></span>{{ $t('InGameMonitor.InGameMonitor') }}</div>
                   <div class="toolBar">
                     ...
                     <div class="content">
                          <div class="settingitem"  @click.stop="GPP_OpenURL(2)">{{$t('home.functionIntroduction')}}</div>
                          <div class="settingitem" v-show="!moduleInfosetting.tabInfo[2].Inleft" @click.stop="setTabInleft(2,true)">{{$t('home.fixedToNav')}}</div>
                          <div class="settingitem" v-show="moduleInfosetting.tabInfo[2].Inleft" @click.stop="setTabInleft(2,false)">{{$t('home.cancelFixedToNav')}}</div>
                     </div>
                  </div>
               </div>
            <p class="title_des">{{$t('home.currentList')}}</p>
            <div class="bottom">
               <div class="hardwareItem"  v-for="(item,index) in monitorItem" >
                   <div class="chunk"></div>
                   {{ $t(item.des || item.name) }}
                   <!-- <span v-show="item.des == ''">{{ item.name}}</span>
                   <span v-show="item.des !== ''">{{ item.des}}</span> -->
       </div>
</div>
</div>
         <!-- 性能统计 -->
         <div class="item monitor border regameing" :style="{height:menuInfo[2].height+'px',top:menuInfo[2].top+'px',left:menuInfo[2].left+'px'}"  @click.stop="routepage(3)">
            <div class="top">
                  <div class="title"><span class="iconfont icon-a-Performancestatistics"></span>{{$t('home.performanceStatistics')}}</div>
                  <div class="outervalue">
                     <span style="color: #35D5B1;margin: 5px 5px 0 0 ;" v-show="moduleInfosetting.tabInfo[3]['download']['installed']&&!moduleInfosetting.tabInfo[3]['download']['isLatest']">{{$t(textArr[0])}}</span>
                     <div class="settingitem introduce"  v-show="Rebound_Uninstall" @click.stop="GPP_OpenURL(3)" ><span class="iconfont icon-a-FunctionIntroduction"></span><span>{{$t('home.functionIntroduction')}}</span></div>
                     <div class="settingitem downloadbuttom" v-show="Rebound_Uninstall" @click.stop="downloadModule(3)" ><span class="iconfont icon-download"></span></div>

                     <div class="settingitem downloadbuttom updateNow" v-show="moduleInfosetting.tabInfo[3]['download']['installed']&&!moduleInfosetting.tabInfo[3]['download']['isLatest']&&!moduleInfosetting.tabInfo[3]['download']['installing']" @click.stop="downloadModule(3)" ><span style="color: #FFFFFF;font-size: 12px;">{{$t('home.updateNow')}}</span></div>
                     <div v-show="Rebound_Installing" style="display: flex;align-items: center;padding: 0 10px;"><div class="progress" style="margin-right: 10px;" ><div class="progressValue" :style="{'width':reboundProgress+'%'}"></div></div><span style="width: 10px;">{{reboundProgress+'%' }}</span></div>
                     <div class="toolBar" v-show="moduleInfosetting.tabInfo[3]['download']['installed']">
                        ...
                        <div class="content" >
                           <div class="settingitem"  @click.stop="GPP_OpenURL(3)">{{$t('home.functionIntroduction')}}</div>
                           <div class="settingitem" v-show="!moduleInfosetting.tabInfo[3]['Inleft'] && moduleInfosetting.tabInfo[3]['download']['installed']" @click.stop="setTabInleft(3,true)">{{$t('home.fixedToNav')}}</div>
                           <div class="settingitem" v-show="moduleInfosetting.tabInfo[3]['Inleft'] && moduleInfosetting.tabInfo[3]['download']['installed']" @click.stop="setTabInleft(3,false)">{{$t('home.cancelFixedToNav')}}</div>
                     </div>
                  </div>
                  </div>
               </div>
            <p class="title_des">{{$t('home.recentRun')}}</p>
            <div class="bottom">
               <div class="recently_game" >
                  <div class="gameBox">
                     <div >
                        <img :src=recentGameInfo.iconsrc alt="" v-show="recentGameInfo.iconsrc != ''">
                        <p style="margin-left: 10px;">{{recentGameInfo.gameName}}</p>
                     </div>
                     <div style="margin-right: 20px;">
                        <p class=""><span>{{$t('home.resolution')}}</span><span>{{ recentGameInfo.resolution }}</span></p>
                        <p style="margin-left: 10px;"><span>{{$t('home.duration')}}</span><span>{{recentGameInfo.gametime}}</span></p>
                     </div>
                  </div>
                  <div class="baseInfo">
                     <div class="baseItem">
                        <div>
                           <p style="margin-top: 10px;">Avg FPS</p>
                           <div class="line" style="margin-top: 10px;"></div>
                        </div>
                         <p style="margin-top: 20px;color:#FFFFFF;font-weight: bold;font-size: 14px;">{{ recentGameInfo.fps.avg }}</p>
                     </div>
                     <div class="baseItem">
                        <div>
                           <p style="margin-top: 10px;">1% Low FPS</p>
                           <div class="line" style="margin-top: 10px;"></div>
                        </div>
                         <p style="margin-top: 20px;color:#FFFFFF;font-weight: bold;font-size: 14px">{{ recentGameInfo.fps.fps01 }}</p>
                     </div>
                     <div class="baseItem">
                        <div>
                           <p style="margin-top: 10px;">CPU TEMP</p>
                           <div class="line" style="margin-top: 10px;"></div>
                        </div>
                         <p style="margin-top: 20px;color:#FFFFFF;font-weight: bold;font-size: 14px">{{ recentGameInfo.cputemperature.avg }}℃</p>
                     </div>
                     <div class="baseItem">
                        <div>
                           <p style="margin-top: 10px;">GPU TEMP</p>
                           <div class="line" style="margin-top: 10px;"></div>
                        </div>
                         <p style="margin-top: 20px;color:#FFFFFF;font-weight: bold;font-size: 14px">{{ recentGameInfo.gputemperature.avg }}℃</p>
                     </div>
                  </div>
               </div>
            </div>
         </div>
         <!-- 游戏滤镜 -->
         <div class="item  border monitor gamemirror scroll" :style="{overflow:'hidden',height:menuInfo[3].height+'px',top:menuInfo[3].top+'px',left:menuInfo[3].left+'px'}" @click.stop="routepage(4)">
            <div class="top">
                  <div class="title"><span class="iconfont icon-VIPfilter"></span>{{$t('home.gameFilter')}}</div>

                  <div class="outervalue">
                     <span style="color: #35D5B1;margin: 5px 5px 0 0;" v-show="moduleInfosetting.tabInfo[4]['download']['installed']&&!moduleInfosetting.tabInfo[4]['download']['isLatest']">{{$t(textArr[1])}}</span>
                     <div class="settingitem introduce"  v-show="Mirror_Uninstall" @click.stop="GPP_OpenURL(4)" ><span class="iconfont icon-a-FunctionIntroduction"></span><span>{{$t('home.functionIntroduction')}}</span></div>
                     <div class="settingitem downloadbuttom" v-show="Mirror_Uninstall" @click.stop="downloadModule(4)" ><span class="iconfont icon-download"></span></div>
                     <div class="settingitem downloadbuttom updateNow" v-show="moduleInfosetting.tabInfo[4]['download']['installed']&&!moduleInfosetting.tabInfo[4]['download']['isLatest']&&!moduleInfosetting.tabInfo[4]['download']['installing']"  @click.stop="downloadModule(4)" ><span style="color: #FFFFFF;font-size: 12px;">{{$t('home.updateNow')}}</span></div>
                     <div v-show="Mirror_Installing" style="display: flex;align-items: center;padding: 0 10px;"><div class="progress" style="margin-right: 10px;" ><div class="progressValue" :style="{'width':mirrorProgress+'%'}"></div></div><span style="width: 10px;">{{mirrorProgress+'%' }}</span></div>
                     <div class="toolBar" v-show="moduleInfosetting.tabInfo[4]['download']['installed']">
                        ...
                        <div class="content" >
                           <div class="settingitem"  @click.stop="GPP_OpenURL(4)">{{$t('home.functionIntroduction')}}</div>
                           <div class="settingitem" v-show="!moduleInfosetting.tabInfo[4]['Inleft'] && moduleInfosetting.tabInfo[4]['download']['installed']" @click.stop="setTabInleft(4,true)">{{$t('home.fixedToNav')}}</div>
                           <div class="settingitem" v-show="moduleInfosetting.tabInfo[4]['Inleft'] && moduleInfosetting.tabInfo[4]['download']['installed']" @click.stop="setTabInleft(4,false)">{{$t('home.cancelFixedToNav')}}</div>
                        </div>
                     </div>
                  </div>

            </div>
            <div class="bottom" style="margin-top: 10px;display: block">
                <div class="mirror">
                  <p class="mode">{{$t(mirrorInfo['name'])}}</p>
                  <p class="current">{{$t('home.gameFilterHasAccompany')}}<span style="margin: 0 2px;"> {{playerNum}} </span>{{$t('home.gameFilterHasAccompany2')}}</p>
                </div>
                <br />
                <div class="intro">
                  {{$t(mirrorInfo['text'])}}
                </div>
            </div>
         </div>
   <!-- 桌面监控-->
   <div class="item"  :style="{height:menuInfo[4].height+'px',top:menuInfo[4].top+'px',left:menuInfo[4].left+'px',backgroundColor:'transparent'}"  @click.stop="routepage(11)">
            <div class="inside pcocessCore border">
               <div class="top">
                  <div class="title"><span class="iconfont icon-icon_deskmonitor"></span>{{ $t('DesktopMonitoring.desktopMonitoring') }}</div>
                  <div class="outervalue">
                     <span style="color: #35D5B1;margin: 5px 5px 0 0;" v-show="moduleInfosetting.tabInfo[11]['download']['installed']&&!moduleInfosetting.tabInfo[11]['download']['isLatest']">{{$t(textArr[4])}}</span>
                     <!-- <div class="settingitem introduce"  v-show="mo_Uninstall" @click.stop="GPP_OpenURL(7)" ><span class="iconfont icon-a-FunctionIntroduction"></span><span>{{$t('home.functionIntroduction')}}</span></div>  -->
                     <div class="settingitem downloadbuttom" v-show="mo_Uninstall" @click.stop="downloadModule(11)" ><span class="iconfont icon-download"></span></div>

                     <div class="settingitem downloadbuttom updateNow" v-show="moduleInfosetting.tabInfo[11]['download']['installed']&&!moduleInfosetting.tabInfo[11]['download']['isLatest']&&!moduleInfosetting.tabInfo[11]['download']['installing']"  @click.stop="downloadModule(11)" ><span style="color: #FFFFFF;font-size: 12px;">{{$t('home.updateNow')}}</span></div>
                     <div v-show="mo_Installing" style="display: flex;align-items: center;padding: 0 10px;"><div class="progress" style="margin-right: 10px;" ><div class="progressValue" :style="{'width':MoProgress+'%'}"></div></div><span style="width: 10px;">{{MoProgress+'%' }}</span></div>
                     <div class="toolBar" v-show="moduleInfosetting.tabInfo[11]['download']['installed']">
                        ...
                        <div class="content" >
                            <!-- <div class="settingitem"  @click.stop="GPP_OpenURL(7)">{{$t('home.functionIntroduction')}}</div> -->
                           <div class="settingitem" v-show="!moduleInfosetting.tabInfo[11]['Inleft'] && moduleInfosetting.tabInfo[11]['download']['installed']" @click.stop="setTabInleft(11,true)">{{$t('home.fixedToNav')}}</div>
                           <div class="settingitem" v-show="moduleInfosetting.tabInfo[11]['Inleft'] && moduleInfosetting.tabInfo[11]['download']['installed']" @click.stop="setTabInleft(11,false)">{{$t('home.cancelFixedToNav')}}</div>
                        </div>
                     </div>
                     </div>
                     </div>
                     <div class="bottom">
                 <div class="content">

                  <p style="color: #777777;font-size: 12px;">{{ $t('DesktopMonitoring.RealTimeMonitoring') }}</p>
                 </div>
            </div>
         </div>
   </div>
   <!-- 压力测试 -->
   <div class="item"  :style="{height:menuInfo[6].height+'px',top:menuInfo[6].top+'px',left:menuInfo[6].left+'px',backgroundColor:'transparent'}"  @click.stop="routepage(12)">
           <div class="inside pcocessCore border">
              <div class="top">
                 <div class="title"><span class="iconfont icon-icon_deskmonitor"></span>{{ $t('messages.pressureTest') }}</div>
                 <div class="outervalue">
                    <span style="color: #35D5B1;margin: 5px 5px 0 0;" v-show="moduleInfosetting.tabInfo[12]['download']['installed']&&!moduleInfosetting.tabInfo[12]['download']['isLatest']">{{$t(textArr[4])}}</span>
                    <div class="settingitem downloadbuttom" v-show="test_Uninstall" @click.stop="downloadModule(12)" ><span class="iconfont icon-download"></span></div>

                    <div class="settingitem downloadbuttom updateNow" v-show="moduleInfosetting.tabInfo[12]['download']['installed']&&!moduleInfosetting.tabInfo[12]['download']['isLatest']&&!moduleInfosetting.tabInfo[12]['download']['installing']"  @click.stop="downloadModule(12)" ><span style="color: #FFFFFF;font-size: 12px;">{{$t('home.updateNow')}}</span></div>
                    <span v-show="benching">{{ $t('messages.installingPressureTest') }}</span>
                    <div v-show="test_Installing && !benching" style="display: flex;align-items: center;padding: 0 10px;"><div class="progress" style="margin-right: 10px;" ><div class="progressValue" :style="{'width':TestProgress+'%'}"></div></div><span style="width: 10px;">{{TestProgress+'%' }}</span></div>
                    <div class="toolBar" v-show="moduleInfosetting.tabInfo[12]['download']['installed']">
                       ...
                       <div class="content" >
                           &lt;!&ndash; <div class="settingitem"  @click.stop="GPP_OpenURL(7)">{{$t('home.functionIntroduction')}}</div> &ndash;&gt;
                          <div class="settingitem" v-show="!moduleInfosetting.tabInfo[12]['Inleft'] && moduleInfosetting.tabInfo[12]['download']['installed']" @click.stop="setTabInleft(12,true)">{{$t('home.fixedToNav')}}</div>
                          <div class="settingitem" v-show="moduleInfosetting.tabInfo[12]['Inleft'] && moduleInfosetting.tabInfo[12]['download']['installed']" @click.stop="setTabInleft(12,false)">{{$t('home.cancelFixedToNav')}}</div>
                       </div>
                    </div>
                    </div>
                    </div>
                    <div class="bottom">
                <div class="content">
                 <p style="color: #777777;font-size: 12px;">{{ $t('messages.pressureTest') }}</p>
                </div>
           </div>
        </div>
   </div>
         <!-- 进程核心分配 -->
         <div class="item" :style="{height:menuInfo[10].height+'px',top:menuInfo[10].top+'px',left:menuInfo[10].left+'px',backgroundColor:'transparent'}"  @click.stop="routepage(5)">
            <div class="inside pcocessCore border">
               <div class="top">
                  <div class="title"><span class="iconfont icon-jinchengfenpei-1-copy"></span>{{$t('psc.processCoreAssign')}}</div>

                  <div class="outervalue">
                     <span style="color: #35D5B1;margin: 5px 5px 0 0;" v-show="moduleInfosetting.tabInfo[5]['download']['installed']&&!moduleInfosetting.tabInfo[5]['download']['isLatest']">{{$t(textArr[2])}}</span>
                     <div class="settingitem introduce"  v-show="Process_Uninstall" @click.stop="GPP_OpenURL(5)" ><span class="iconfont icon-a-FunctionIntroduction"></span><span>{{$t('home.functionIntroduction')}}</span></div>
                     <div class="settingitem downloadbuttom" v-show="Process_Uninstall" @click.stop="downloadModule(5)" ><span class="iconfont icon-download"></span></div>

                     <div class="settingitem downloadbuttom updateNow" v-show="moduleInfosetting.tabInfo[5]['download']['installed']&&!moduleInfosetting.tabInfo[5]['download']['isLatest']&&!moduleInfosetting.tabInfo[5]['download']['installing']"  @click.stop="downloadModule(5)" ><span style="color: #FFFFFF;font-size: 12px;">{{$t('home.updateNow')}}</span></div>
                     <div v-show="Process_Installing" style="display: flex;align-items: center;padding: 0 10px;"><div class="progress" style="margin-right: 10px;" ><div class="progressValue" :style="{'width':mirrorProgress+'%'}"></div></div><span style="width: 10px;">{{mirrorProgress+'%' }}</span></div>
                     <div class="toolBar" v-show="moduleInfosetting.tabInfo[5]['download']['installed']">
                        ...
                        <div class="content" >
                           <!-- <div class="settingitem"  @click.stop="GPP_OpenURL(5)">{{$t('home.functionIntroduction')}}</div> -->
                           <div class="settingitem" v-show="!moduleInfosetting.tabInfo[5]['Inleft'] && moduleInfosetting.tabInfo[5]['download']['installed']" @click.stop="setTabInleft(5,true)">{{$t('home.fixedToNav')}}</div>
                           <div class="settingitem" v-show="moduleInfosetting.tabInfo[5]['Inleft'] && moduleInfosetting.tabInfo[5]['download']['installed']" @click.stop="setTabInleft(5,false)">{{$t('home.cancelFixedToNav')}}</div>
                        </div>
                     </div>
                  </div>

            </div>
            <div class="bottom">
                 <div class="content">
                  <p style="color: #777777;font-size: 12px;">{{ $t('psc.description') }}</p>
                 </div>
            </div>
          </div>
         </div>

           <!-- 喜加一 -->
           <div class="item" :style="{height:menuInfo[5].height+'px',top:menuInfo[5].top+'px',left:menuInfo[5].left+'px',backgroundColor:'transparent'}"  @click.stop="routepage(6)">
            <div class="inside FreeGame border">
              <div class="top">
                  <div class="title"><span class="iconfont icon-icon_gift"></span>{{$t('home.addOne')}}</div>
                  <div class="outervalue">
                     <span style="color: #35D5B1;margin: 5px 5px 0 0;" v-show="moduleInfosetting.tabInfo[6]['download']['installed']&&!moduleInfosetting.tabInfo[6]['download']['isLatest']">{{$t(textArr[3])}}</span>
                     <!-- <div class="settingitem introduce"  v-show="FreeGame_Uninstall" @click.stop="GPP_OpenURL(6)" ><span class="iconfont icon-a-FunctionIntroduction"></span><span>{{$t('home.functionIntroduction')}}</span></div> -->
                     <div class="settingitem downloadbuttom" v-show="FreeGame_Uninstall" @click.stop="downloadModule(6)" ><span class="iconfont icon-download"></span></div>
                     <div class="settingitem downloadbuttom updateNow" v-show="moduleInfosetting.tabInfo[6]['download']['installed']&&!moduleInfosetting.tabInfo[6]['download']['isLatest']&&!moduleInfosetting.tabInfo[6]['download']['installing']"  @click.stop="downloadModule(6)" ><span style="color: #FFFFFF;font-size: 12px;">{{$t('home.updateNow')}}</span></div>
                     <div v-show="FreeGame_Installing" style="display: flex;align-items: center;padding: 0 10px;"><div class="progress" style="margin-right: 10px;" ><div class="progressValue" :style="{'width':FreeGameProgress+'%'}"></div></div><span style="width: 10px;">{{mirrorProgress+'%' }}</span></div>
                     <div class="toolBar" v-show="moduleInfosetting.tabInfo[6]['download']['installed']">
                        ...
                        <div class="content">
                           <div class="settingitem"  @click.stop="GPP_OpenURL(6)">{{$t('home.functionIntroduction')}}</div>
                            <div class="settingitem" v-show="!moduleInfosetting.tabInfo[6]['Inleft'] && moduleInfosetting.tabInfo[6]['download']['installed']" @click.stop="setTabInleft(6,true)">{{$t('home.fixedToNav')}}</div>
                           <div class="settingitem" v-show="moduleInfosetting.tabInfo[6]['Inleft'] && moduleInfosetting.tabInfo[6]['download']['installed']" @click.stop="setTabInleft(6,false)">{{$t('home.cancelFixedToNav')}}</div>
                        </div>
                     </div>
                     </div>
            </div>
            <div class="bottom">
                 <!-- <div class="content2" v-show="countdown != '00:00:00'"> -->
                  <div class="content2">
                  <img :src="allGameList[0].cover">
                  <div class="GameInformation">
                  <p class="Game_title" style="font-size: 18px;"> {{ allGameList[0].game_name }}</p>
                  <p style="display: flex;align-items: center;width: 100%;"><span>{{$t('home.gamePlatform')}}：</span><span>{{ (allGameList[0].platform).toUpperCase()}}</span></p>
                  <p style="color:#4791F6;cursor: pointer;width: 100px;" @click.stop="GPP_OpenPage(allGameList[0].get_url)">{{$t('home.goShop')}}</p>
                  <div style="display: flex;align-items: center;"><div class="countDown"><span>{{countdown}}</span></div>{{$t('home.receiveDeadline')}}</div>
                  <div class="saveMoney"><span>{{allGameList[0].original_price}}</span><div class="oneHundredpercent">-100%</div></div>
                  </div>
                 </div>
                 <!-- <div class="content2" v-show="countdown == '00:00:00'">
                     <span style="color: #FFFFFF;margin-left: 20px;">暂无可领取游戏</span>
                 </div> -->
            </div>
          </div>
         </div>
          <!-- 二哈AI -->
          <div class="item" :style="{height:menuInfo[7].height+'px',top:menuInfo[7].top+'px',left:menuInfo[7].left+'px',backgroundColor:'transparent'}"  @click.stop="routepage(8)">
            <div class="inside FreeGame thaAi border">
              <div class="top">
                  <div class="title"><img style="width: 24px;height: 24px;margin-right: 5px;" src="../assets/icon_erha.png" alt="">{{$t('home.erhaAI')}} <span style="color:#999999;font-size: 12px;margin-left: 5px;">{{ $t('home.recordingmodule') }}</span></div>
                  <div class="outervalue">
                     <span style="color: #35D5B1;margin: 5px 5px 0 0;" v-show="moduleInfosetting.tabInfo[8]['download']['installed']&&!moduleInfosetting.tabInfo[8]['download']['isLatest']">{{$t(textArr[5])}}</span>
                     <!-- <div class="settingitem introduce"  v-show="haha_Uninstall" @click.stop="GPP_OpenURL(8)" ><span class="iconfont icon-a-FunctionIntroduction"></span><span>{{$t('home.functionIntroduction')}}</span></div> -->
                     <div class="settingitem downloadbuttom" v-show="haha_Uninstall" @click.stop="downloadModule(8)" ><span class="iconfont icon-download"></span></div>
                  <!-- </div> -->
                      <div class="settingitem downloadbuttom updateNow" v-show="moduleInfosetting.tabInfo[8]['download']['installed']&&!moduleInfosetting.tabInfo[8]['download']['isLatest']&&!moduleInfosetting.tabInfo[8]['download']['installing']"  @click.stop="downloadModule(8)" ><span style="color: #FFFFFF;font-size: 12px;">{{$t('home.updateNow')}}</span></div>
                     <div v-show="haha_Installing" style="display: flex;align-items: center;padding: 0 10px;"><div class="progress" style="margin-right: 10px;" ><div class="progressValue" :style="{'width':AiLabProgress+'%'}"></div></div><span style="width: 10px;">{{mirrorProgress+'%' }}</span></div>
                     <div class="toolBar" v-show="moduleInfosetting.tabInfo[8]['download']['installed']">
                        ...
                        <div class="content" >
                           <div class="settingitem"  @click.stop="GPP_OpenURL(8)">{{$t('home.functionIntroduction')}}</div>
                            <div class="settingitem" v-show="!moduleInfosetting.tabInfo[8]['Inleft'] && moduleInfosetting.tabInfo[8]['download']['installed']" @click.stop="setTabInleft(8,true)">{{$t('home.fixedToNav')}}</div>
                           <div class="settingitem" v-show="moduleInfosetting.tabInfo[8]['Inleft'] && moduleInfosetting.tabInfo[8]['download']['installed']" @click.stop="setTabInleft(8,false)">{{$t('home.cancelFixedToNav')}}</div>
                        </div>
                     </div>
                   </div>
            </div>
            <div class="bottom scroll">
                  <p><span class="pan1">{{ $t('home.superPower') }}</span><span>：{{ $t('home.autoRecord') }}</span></p>
                  <p><span class="pan1">{{$t('home.externalDevice') }}：</span><span>{{$t('home.linkage') }}</span></p>
                  <p><span class="pan1">{{$t('home.AI') }}：</span><span>{{$t('home.test') }}</span></p>
                  <p>{{$t('home.supportedGames') }}：</p>
                  <p>{{$t('home.games') }}</p>
            </div>
          </div>
         </div>

            <!-- OBS-->
            <div class="item" :style="{height:menuInfo[11].height+'px',top:menuInfo[11].top+'px',left:menuInfo[11].left+'px',backgroundColor:'transparent'}"  @click.stop="routepage(7)">
            <div class="inside pcocessCore border">
               <div class="top">
                  <div class="title"><span class="iconfont icon-icon_video"></span>{{$t('home.videoRecording') }}</div>
                  <div class="outervalue">
                     <span style="color: #35D5B1;margin: 5px 5px 0 0;" v-show="moduleInfosetting.tabInfo[7]['download']['installed']&&!moduleInfosetting.tabInfo[7]['download']['isLatest']">{{$t(textArr[4])}}</span>
                     <!-- <div class="settingitem introduce"  v-show="OBS_Uninstall" @click.stop="GPP_OpenURL(7)" ><span class="iconfont icon-a-FunctionIntroduction"></span><span>{{$t('home.functionIntroduction')}}</span></div> -->
                     <div class="settingitem downloadbuttom" v-show="OBS_Uninstall" @click.stop="downloadObs" ><span class="iconfont icon-download"></span></div>

                     <div class="settingitem downloadbuttom updateNow" v-show="moduleInfosetting.tabInfo[7]['download']['installed']&&!moduleInfosetting.tabInfo[7]['download']['isLatest']&&!moduleInfosetting.tabInfo[7]['download']['installing']"  @click.stop="downloadModule(7)" ><span style="color: #FFFFFF;font-size: 12px;">{{$t('home.updateNow')}}</span></div>
                     <div v-show="OBS_Installing" style="display: flex;align-items: center;padding: 0 10px;"><div class="progress" style="margin-right: 10px;" ><div class="progressValue" :style="{'width':GameObsProgress+'%'}"></div></div><span style="width: 10px;">{{mirrorProgress+'%' }}</span></div>
                     <div class="toolBar" v-show="moduleInfosetting.tabInfo[7]['download']['installed']">
                        ...
                        <div class="content" >
                           <!-- <div class="settingitem"  @click.stop="GPP_OpenURL(7)">{{$t('home.functionIntroduction')}}</div> -->
                           <div class="settingitem" v-show="!moduleInfosetting.tabInfo[7]['Inleft'] && moduleInfosetting.tabInfo[7]['download']['installed']" @click.stop="setTabInleft(7,true)">{{$t('home.fixedToNav')}}</div>
                           <div class="settingitem" v-show="moduleInfosetting.tabInfo[7]['Inleft'] && moduleInfosetting.tabInfo[7]['download']['installed']" @click.stop="setTabInleft(7,false)">{{$t('home.cancelFixedToNav')}}</div>
                        </div>
                     </div>
                  </div>

            </div>
            <div class="bottom">
                 <div class="content">
                  <p style="color: #777777;font-size: 12px;">{{$t('home.videoRecording2') }}</p>
                 </div>
            </div>
          </div>
           </div>

          <!-- 定时关机-->
          <div class="item" :style="{height:menuInfo[8].height+'px',top:menuInfo[8].top+'px',left:menuInfo[8].left+'px',backgroundColor:'transparent'}"  @click.stop="OpenShutdown(9)">
              <div class="inside pcocessCore border">
                  <div class="top">
                      <div class="title"><span class="iconfont icon-icon_timed1"></span>{{$t('shutdownTimer.timedShutdown') }}</div>
                      <div class="outervalue">
                          <div class="settingitem downloadbuttom" v-show="!moduleInfosetting.tabInfo[9]['download']['installed']" @click.stop="DownLoadShutdown()"><span class="iconfont icon-download"></span></div>
                          <div class="toolBar" v-show="moduleInfosetting.tabInfo[9]['download']['installed']">
                              ...
                              <div class="content" >
                                  <!-- <div class="settingitem"  @click.stop="GPP_OpenURL(9)">{{$t('home.functionIntroduction')}}</div> -->
                                  <div class="settingitem" v-show="!moduleInfosetting.tabInfo[9]['Inleft'] && moduleInfosetting.tabInfo[9]['download']['installed']" @click.stop="setTabInleft(9,true)">{{$t('home.fixedToNav')}}</div>
                                  <div class="settingitem" v-show="moduleInfosetting.tabInfo[9]['Inleft'] && moduleInfosetting.tabInfo[9]['download']['installed']" @click.stop="setTabInleft(9,false)">{{$t('home.cancelFixedToNav')}}</div>
                              </div>
                          </div>
                      </div>

                  </div>
                  <div class="bottom">
                      <div class="content" v-if="isSetTimedShutdown">
                          <div class="shutdown">
                              <p style="color: #fff;font-size: 12px;margin-bottom: 10px;">{{$t('shutdownTimer.isShuttingDown') }}</p>
                              <div style="display: flex;flex-flow: row nowrap;align-items: center;">
                                  <span>{{ $t('shutdownTimer.goingToBe') }}</span>
                                  <div class="countDown">
                                      {{shutdownCountDown}}
                                  </div>
                                  <span>{{ $t('shutdownTimer.shutdownIn') }}</span>
                              </div>
                          </div>
                      </div>
                      <div class="content" v-if="!isSetTimedShutdown">
                          <p style="color: #fff;font-size: 12px;">{{$t('shutdownTimer.noplan') }}</p>
                      </div>
                  </div>
              </div>
          </div>

          <!-- 截图-->
          <div class="item" :style="{height:menuInfo[9].height+'px',top:menuInfo[9].top+'px',left:menuInfo[9].left+'px',backgroundColor:'transparent'}"  @click.stop="OpenShutdown(10)">
              <div class="inside pcocessCore border">
                  <div class="top">
                      <div class="title"><span class="iconfont icon-icon_screenshot" style="font-size: 18px;"></span>{{$t('screenshotpage.screenshot') }}</div>
                      <div class="outervalue">
                          <div class="settingitem downloadbuttom" v-show="!moduleInfosetting.tabInfo[10]['download']['installed']" @click.stop="DownLoadscreenshot()"><span class="iconfont icon-download"></span></div>
                          <div class="toolBar" v-show="moduleInfosetting.tabInfo[10]['download']['installed']">
                              ...
                              <div class="content" >
                                  <div class="settingitem" v-show="!moduleInfosetting.tabInfo[10]['Inleft'] && moduleInfosetting.tabInfo[10]['download']['installed']" @click.stop="setTabInleft(10,true)">{{$t('home.fixedToNav')}}</div>
                                  <div class="settingitem" v-show="moduleInfosetting.tabInfo[10]['Inleft'] && moduleInfosetting.tabInfo[10]['download']['installed']" @click.stop="setTabInleft(10,false)">{{$t('home.cancelFixedToNav')}}</div>
                              </div>
                          </div>
                      </div>

                  </div>
                  <div class="bottom">
                      <div class="content">
                          <p style="color: #777777;font-size: 12px;">{{$t('screenshotpage.screenshotFormat') }}</p>
                      </div>
                  </div>
              </div>
          </div>

         </div>
      </div>


</template>

<style lang="scss" scoped>
.outside{
   overflow-y: auto;
   overflow-x: hidden;
}
.message{
   /* width: 1200px; */
   // -webkit-app-region:drag;
   height: 40px;
   border-radius: 4px;
   // margin-bottom: 20px;
   display: flex;
   align-items: center;
   color:rgba(53, 121, 213, 1)
}
.toolBar{
   display: flex;
   align-items: center;
   justify-content: center;
   width:30px;
   height: 30px;
   font-size: 18px;
   color:rgba(53, 102, 213, 1);
   font-weight: bold;
   position: relative;
   margin-left: 6px;
   .content{
      font-size: 12px;
      font-weight: 400;
      position: absolute;
      display: none;
      // width: 120px;
      // height: 120px;
      border-radius: 4px;
      background:rgba(52, 54, 71, 1);
      top: 30px;
      right: 0px;
      flex-direction: column;
      .settingitem{
         height: 40px;
         border-radius: 4px;
         // width: 120px;
         min-width: 120px;
         display: flex;
         align-items: center;
         justify-content: center;
          text-align: center;
      }
      .settingitem:hover{
         border-radius: 4px;
         background: rgba(62, 64, 80, 1);
         color: #FFFFFF
      }
   };
}
.introduce
         {
         //   width: 100px;
           height: 30px;
           //   background-color: transparent;
           display: flex;
           align-items: center;
           justify-content: center;
           color: #3579D5;
           padding: 0 10px;
           span
           {
            margin-right: 5px
           };
           margin-right: 5px;
           border-radius: 6px;
         }
         .introduce:hover{
            background-color: #393943;
         }

.outervalue{
   display: flex;
   align-items: center
}

.progress{
   width: 120px;
   height: 5px;
   background-color:#22232E;
   border-radius: 3px;
   position: relative;

   .progressValue{
      transition: 0.5s;
      position: absolute;
      height: 5px;
      background-color:#3579D5;
      border-radius: 2px;
   }
}

.toolBar:hover{
   .content{
      display: flex;
   }
}
.toolBar:hover{
   background:rgba(34, 35, 46, 1);
   border-radius: 4px;
}
.Main{
   width: 100%;
   position: relative;
   color: #FFFFFF;
   overflow-y: auto;
   overflow-x: hidden;
   margin-bottom: 20px;
   .top{
         font-size:12px;
         width: 560px;
         height: 20px;
         margin: 20px 0 29px 0;
         display: flex;
         justify-content: space-between;
         align-items: center;
      }
   /* 硬件信息开始 */
   .hardware{
      display: flex;
      flex-direction: column;
      align-items: center;
      .hardwareItem{
         background-color:rgba(34, 35, 46, 0.8);
         width: 560px;
         height: 80px;
         margin-bottom:10px;
         border-radius: 5px;
         align-items: center;
         display: flex;
         justify-content: space-between;

         .left{
            font-size: 12px;
            display: flex;
            align-items: center;
            margin-left: 20px;
            .left_info{
               margin-left: 10px
            }
         }
         img{
            width:14px;
            height: 14px;
         }
         .right{
            margin-right: 28px;
            font-size: 14px
         }
      }
   }
   .title{
        font-size: 12px;
        color: #ffffff;
        display: flex;
        align-items: center;
        span{
         color:#3579D5;font-size:20px;margin-right: 10px
        }
      }
   .title_des{
       font-size: 12px;
       color: #777777;
       margin-left: 20px;
   }
   /* 硬件结束开始 */
   .pcocessCore{
      display: flex;
      flex-direction: column;
      // overflow: auto;
      .top{
         margin-left: 20px;
         margin-bottom: 15px;
      }
      .bottom{
         width: 560px;
         height: 100px;
         display: flex;
         .content{
            width: 535px;
            height: 80px;
            margin: 10px 0 0 20px;
            flex-wrap: wrap;
            display: flex;
         }
      }

   }
   .monitorAuto{
      overflow: auto;
   }
   /* 游戏内监控开始 */
   .monitor{
      display: flex;
      flex-direction: column;
      .top{
         margin-left: 20px;
         margin-bottom: 15px;
      }
      .bottom{
         display: flex;
         flex-wrap: wrap;
      }
      .hardwareItem{
         display: flex;
         align-items: center;
         font-size: 12px;
         width: 160px;
         height: 20px;
         // margin: 10px 0 10px 20px;
         padding: 5px 0px 5px 22px;
         // overflow: hidden;
         // white-space: nowrap;
         // text-overflow: ellipsis;
         line-height: 13px;
         .chunk{
            width:10px;
            height: 10px;
            background-color: #3579D5;
            margin-right: 19px
         }
         span{
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            width: 135px;
            line-height: 20px;
         }
      }
   }
   /* 游戏内监控结束 */
   /* 游戏内监控开始 */
   .regameing{
      .bottom{
         display: flex;
         flex-direction: column;
         .gameBox
         {
         font-size: 12px;
         color: #777777;
          display: flex;
          justify-content: space-between;
          margin-left:20px;
          margin-top: 20px;
          img{
            width: 14px;
            height: 14px;
          }
          div{
            display: flex
          }
         }
         .baseInfo
         {
            font-size: 12px;
            color: #777777;
            display: flex;
            .baseItem{
              width: 122px;
              height: 110px;
              background-color:#22232E;
              border-radius: 4px;
              margin-left: 20px;
              margin-top: 20px;
              display: flex;
              flex-direction: column;
              align-items: center;
              p{
               text-align: center;
              }
              .line{
               width:107px;
               height: 3px;
               background-color:#42434D;
              }
            }
         }
      }
   }
   .FreeGame{
      display: flex;
      flex-direction: column;
      overflow: hidden;
      .top{
         margin-left: 20px;
         margin-bottom: 15px;
      }
      .content2{
         width: 560px;
         height: 195px;
         display: flex;
         margin-top: 15px;
        img{
            width: 290px;
            height: 162px;
            margin-left: 10px;
            border-radius: 4px;
        }
        .GameInformation{
          width: 240px;
          margin:0 0 0 12px;
          display: flex;
          flex-direction: column;
          color: white;
          p{
            margin-bottom: 10px;
          }
        }
        .countDown{
          padding:0 10px;
          height: 30px;
          background-color:#1A1B24;
          color: #FFFFFF;
          font-size: 14px;
          border-radius: 6px;
          margin-right:9px;
          span{
            color: #F14343;
            font-size: 15px;
            font-family: 'Quartz',sans-serif;
            line-height: 30px;
            letter-spacing: 3px;
          }
        }
        .saveMoney{
          display: flex;
          align-items: center;
          margin-top: 10px;
          span{
            font-size: 20px;
            font-weight: 700;
            color: #999999;
            text-decoration: line-through;
          }
          .oneHundredpercent
          {
            width:45px;
            height: 20px;
            background-color:#35D57D;
            color: white;
            border-radius: 2px;
            line-height: 20px;
            text-align: center;
            margin-left: 10px;
          }
        }
      }
   }

   .thaAi
   {
      width: 600px;
      height: 260px;
      .bottom
      {
          padding: 0 20px;
          display: flex;
          color: #FFFFFF;
          flex-direction: column;
          max-height: 200px;
          overflow: auto;
          p{
            margin: 10px 0;
          }
          .pan1
          {
            color:#999999;
          }
      }
   }

   .gamemirror{
       .bottom{
         width: 560px;
         height: 105px;
         background-color: #22232E;
         border-radius: 4px;
         margin-left: 20px;
         display: flex;
         flex-direction: column;
         .mirror{
            width: 100%;
            display: flex;
            font-size: 14px;
            margin:20px 0 0 0;
            justify-content: space-between;
            .mode{
               margin-left: 20px;
               min-width: 60px;
            }
            .current{
               font-size: 12px;
               margin-right: 20px;
               color: #777777;
                padding-left: 10px;
               span{
                  color:#3579D5;
                  font-weight: bold
               }
            }
         }
         .intro{
            font-size: 12px;
            margin-left: 20px;
         }
       }
   }
   .downloadbuttom{
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 6px;
      // width: 30px;
      padding: 0 12px;
      height: 26px;
      cursor: pointer;
      width: auto;
      //min-width: 90px;
      span{
         color:#3579D5;font-size:20px;
      }
   }

   .downloadbuttom:hover{
      background-color: #393943;
   }
   .predict{
      display: flex;
      align-items: center;
      font-size: 12px;
      div{
         width: 100%;
         margin: 0 20px 0 20px;
         display: flex;
         justify-content: space-between;
         align-items: center;
      }
   }
   .videoBox{
      overflow: hidden;;
      .video{
         display: flex;
         video{
           width:160px;
           height: 90px;
           border:1px solid #FFFFFF;
           margin-left:20px;
         }
         .video_Info{
            font-size: 12px;
            p{
               margin-left: 20px
            }
         }
      }
   }
}
.column{
   width: 600px;
   height: auto;
   border: 1px solid #05e74980;
   margin-left: 20px;
}
  .opcity{
   background-color: transparent;
  }
  .item{
       border-radius: 4px;
      .inside{
         background-color: rgba(45, 46, 57, 0.8);
         height: 98%;
         border-radius: 4px;
         box-sizing: border-box;
         transition: 0.4s linear;
         overflow: hidden;
      }
      width: 600px;
      margin-bottom: 10px;
      position: absolute;
      // background-color:#2D2E39;
      box-sizing: border-box;
      background-color: rgba(45, 46, 57, 0.8);
      transition: 0.4s linear;
      cursor: pointer;
  }
  .border{
   border: 2px solid transparent;
   // transition: none;
  }
  .border:hover{
   box-sizing: border-box;
   border: 2px solid #3E66D6;
   transition: none;
  }
//   .border:


  .scroll::-webkit-scrollbar {
    width: 5px;
    transition: 0.25s;
}

.scroll::-webkit-scrollbar-thumb {
    background: #71738C;
    border-radius: 3px;
}

.scroll::-webkit-scrollbar-thumb:hover {
    background-color: #71738C;
}
.scroll::-webkit-scrollbar-track {
    background-color: #2D2E39;
    width: 2px;
}

.downloading{
   margin-top: 5px;
   display: flex;
   align-items: center;
   justify-content: center;
   animation: rotate 2s infinite linear
}

.updateNow{
   font-size: 12px;
   width: 80px;
   height: 30px;
   border-radius: 4px;
   background-color:#3579D5;
   display: flex;
   justify-content: center;
   align-items: center;
   color:#ffffff !important;
    span {
        white-space: nowrap;
    }
}

.updateNow:hover{
   background-color:#4a87d9 !important;
}

@keyframes rotate {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}

.shutdown {
    .countDown{
        padding:0 10px;
        height: 30px;
        background-color: #1a1b24;
        border-radius: 6px;
        margin-right:9px;
        color: #F14343;
        font-size: 15px;
        font-family: 'Quartz',sans-serif;
        line-height: 30px;
        letter-spacing: 3px;
        margin: 0 10px;
    }
}

// .cell {
//   height: 30px;
//   padding: 3px 0;
//   box-sizing: border-box;
// }
// .cell .text {
//   width: 24px;
//   height: 24px;
//   display: block;
//   margin: 0 auto;
//   line-height: 24px;
//   position: absolute;
//   left: 50%;
//   transform: translateX(-50%);
//   border-radius: 50%;
// }
// .cell.current .text {
//   background: #626aef;
//   color: #fff;
// }
// .cell .holiday {
//   position: absolute;
//   width: 6px;
//   height: 6px;
//   background: var(--el-color-danger);
//   border-radius: 50%;
//   bottom: 0px;
//   left: 50%;
//   transform: translateX(-50%);
// }
</style>


