<script setup lang="ts">
import {ref, onBeforeMount, onMounted, computed} from 'vue';
import gpp_tips_cn from './assets/gpp_tips_cn.png';
import gpp_tips_en from './assets/gpp_tips_en.png';
import ic_replace_plan from './assets/ic_replace_plan.png'
import ic_saved_screenshots from './assets/ic_saved_screenshots.png'
import ic_start_playback from './assets/ic_start_playback.png'
import ic_saved_recording from './assets/ic_saved_recording.png'
import ic_open_plan from './assets/ic_open_plan.png'
import ic_open_data from './assets/ic_open_data.png'
import ic_close_plan from './assets/ic_close_plan.png'
import ic_close_data from './assets/ic_close_data.png'
import ic_saved_recording_error from './assets/ic_saved_recording_error.png'
import ic_saved_recording_problem from './assets/ic_saved_recording_problem.png'
import audioSrc from './assets/screen.wav'
import { gameppBaseSetting } from "@/modules/Game_Home/stores";
import QuickQuitTips from './components/QuickQuitTips.vue'
import AiHighLightTips from "@/inGame/inGame_tips/components/AiHighLightTips.vue";
import {useI18n} from "vue-i18n";

const i18n = useI18n()
// @ts-ignore
const gamepp = window.gamepp
const $store = gameppBaseSetting()
let vedioadreess = ref('')
let vedioadreess2 = ref('')
let TextPromptArr={19:"游戏内监控",20:"游戏滤镜",21:"录像功能",22:"截图功能",23:"导演模式",24:"游戏内游戏笔记",25:"游戏内弹幕工具",26:"塔科夫价格查询",27:"塔科夫延迟",28:"云顶工具"};
let t = 3;
let timer:any = '';
let timer2:any = '';
let ShowSta = ref('0')
let lan = ''
let boxLgWidthAni = ref(false)
let boxWidthAni = ref(false)
let boxLgWidthAniHide = ref(false)
let boxWidthAniHide = ref(false)
let tip_twoClassName = ref('')
let box_titleMarginTop = ref(23)
let tip_img = ref<any>('')
let tip_one = ref('')
let tip_two = ref('')
let tip_address = ref('')
const BgMusic = ref(null)
const quickCloseWindow = ref(false)
const aiHighLightTip = ref(false)
const isShow = ref(false)
onBeforeMount(() =>
{
  console.log(gamepp.webapp.windows.getTitle.sync());

  try {
    if (gamepp.webapp.windows.getTitle.sync().includes('快速关闭游戏进程提示窗口')) {
      quickCloseWindow.value = true;
    }else if (gamepp.webapp.windows.getTitle.sync().includes('击杀识别游戏内显示')) {
      aiHighLightTip.value = true;
    }
  }catch(e){}
  tip_img.value = ic_replace_plan
  vedioadreess.value = gamepp.setting.getString.sync(6)
  vedioadreess2.value = gamepp.setting.getString.sync(5)
  console.log($store.state.ingameMsgPosition)
})

onMounted(() =>
{
  console.log(Position)
  console.log($store.state)

  if (!aiHighLightTip.value) InitPage()
})


async function InitPage () {
  let w =  Math.round(await gamepp.game.getWidth.promise())
  let h = Math.round(await gamepp.game.getHeight.promise())
  await gamepp.webapp.windows.resize.promise('ingame_tipspage',w,h)
  let value = JSON.parse(window.localStorage.getItem('ingame_tips')!);
  setTimeout(()=>{
    TP_ShowMessage(value);
    isShow.value = true;
  },100)
}
function TP_ShowMessage(data_arr:any) {
  //显示快捷键(1)、截图保存(2)、视频开始(3)、视频开始保存(5)、回溯视频开始保存(6)、视频保存成功(7)、画质加载(8)、画质开启(9)、画质关闭(10)、游戏内监控开启(11)、游戏内监控关闭(12)、VULKAN提示(16)
  t = data_arr.time;

  if (ShowSta.value !== (data_arr.type) || data_arr.type === 2 || data_arr.type === 3 || data_arr.type === 5 || data_arr.type === 6 || data_arr.type === 7 || data_arr.type === 29 || data_arr.type === 30) {
    clearTimeout(timer);
    clearTimeout(timer2);
    ShowSta.value = data_arr.type;
    lan = (data_arr.language).toLowerCase();
    switch (data_arr.type) {
      case 1:
        ShowHotkey(data_arr);
        break;
      case 2:
        SaveImg(data_arr);
        break;
      case 3:
        VideoOn(data_arr);
        break;
      case 5:
        VideoSavedIng(data_arr);
        break;
      case 6:
        BackVideoSavedIng(data_arr);
        break;
      case 7:
        VideoSavedSucc(data_arr);
        break;
      case 8:
        QualityIng(data_arr);
        break;
      case 9:
        QualityOn(data_arr);
        break;
      case 10:
        QualityOff(data_arr);
        break;
      case 11:
        HardwareOn();
        break;
      case 12:
        HardwareOff();
        break;
      case 14:
        HighlightOpen();
        break;
      case 15:
        HighlightClose();
        break;
      case 16:
        VULKANPoint();
        break;
      case 17:
        ManualRecording();
        break;
      case 18:
        ManualRecordingStop();
        break;
      case  19:
      case  20:
      case  21:
      case  22:
      case  23:
      case  24:
      case  25:
      case  26:
      case  27:
      case  28:
        TextPrompt(data_arr);
        break;
      case 29:
        TarkovPrice();
        break;
      case 30:
        LoadingFunctionTips(data_arr);
        break;
      case 31:
        FunctionFailed(data_arr);
        break;
      case 32:
        FunctionSuccess(data_arr);
        break;
      case 33:
        HotkeyConflict();
        break;
      case 34:
        fileNotExist(data_arr);
      case 35:
        pointMark()
        //游戏内打点
        break;
      case 36:
        obsTriggerError(data_arr);
        break;
    }
    boxLgWidthAni.value = true
    boxWidthAni.value = true
    var ShowTime = t;
    if (data_arr.time > 30) {
      ShowTime = 3;
    } else if (data_arr.time === '') {
      ShowTime = 3;
    }
    if (t <= 0) {
      clearTimeout(timer);
      clearTimeout(timer2);
      // return;
    }
    t--;
    timer = setTimeout(HideTips, (ShowTime) * 1000);
    timer2 = setTimeout(HideTips2, ((ShowTime) * 1000 + 750));
    if (data_arr.type === 16) {
      tip_twoClassName.value = 'describe1'
      box_titleMarginTop.value = 15
    } else if (data_arr.type === 17) {
      box_titleMarginTop.value = 10
    } else if (data_arr.type === 18) {
      box_titleMarginTop.value = 17
    } else if (data_arr.type >= 19 && data_arr.type <= 28 || data_arr.type === 31) {
      tip_twoClassName.value += 'describe2'
    } else if (data_arr.type === 30){
      tip_twoClassName.value = ''
      box_titleMarginTop.value = 23
    } else {
      tip_twoClassName.value = ''
      box_titleMarginTop.value = 13
    }
  }
}

//截图文件不存在
function fileNotExist (data:any) {
  tip_img.value = ic_saved_screenshots
  tip_one.value = i18n.t('screenshotpage.screenshot')
  tip_address.value = vedioadreess2.value
  tip_two.value = data['svalue']
}

function HideTips() {
  boxWidthAniHide.value = true
//        $('.box_lg').animate({width: "0px", opacity: '0.5'}, 1200);
}

async function HideTips2 () {
  boxLgWidthAniHide.value = true
  // await gamepp.setting.setBool2.promise('window', 'ingame_tipspage', false);
  await gamepp.webapp.windows.close.promise('ingame_tipspage');
}

function ShowHotkey(data:any) {
  if (!$store.state.ingame_injection) {
    gamepp.webapp.windows.close.sync('ingame_tipspage');
    return
  }
  let KeyValue = data.svalue;
  if (data.svalue === '无' || data.svalue === 'None' || data.svalue === 'None_cn') {
    // KeyValue = eval(lan).NotSet;
    KeyValue = i18n.t('GameRebound.noData')
  }
  tip_img.value = gpp_tips_en
  tip_one.value = KeyValue
  tip_two.value = i18n.t('messages.ingameControlPanel')
}

//截图
function SaveImg() {
    if (!$store.state.ingame_screenshot) {
        gamepp.webapp.windows.close.sync('ingame_tipspage');
        return
    }
  tip_img.value = ic_saved_screenshots
  tip_one.value = i18n.t('screenshotpage.screenshot')
  tip_address.value = vedioadreess2.value
  tip_two.value = "已保存"
  if (gamepp.setting.getInteger.sync(373) === 0) {
    BgMusic.value.play();
  }
}
//视频录制
async function VideoOn() {
  if (!$store.state.ingame_record) {
    gamepp.webapp.windows.close.sync('ingame_tipspage');
    return
  }
  let Mode = await gamepp.setting.getInteger.promise(51);
  // let Mode = 1;
  let ModeText = ''
  if (Mode === 1) {
    //兼容
    ModeText = i18n.t('InGameMonitor.performanceAndAudioMode');
  } else if (Mode === 2) {
    //NVD
    ModeText = 'NVIDIA';
  } else if (Mode === 0) {
    //AMD Relive
    ModeText = 'AMDReLive'
  } else if (Mode === 3) {
    //OBS模式
    ModeText = 'OBS';
  }
  tip_img.value = ic_saved_screenshots
  tip_one.value = i18n.t('video.videoRecord') + ' (' + ModeText + ')';
  tip_two.value = i18n.t('Setting.start')
}

function VideoSavedIng() {
  if (!$store.state.ingame_record) {
    gamepp.webapp.windows.close.sync('ingame_tipspage');
    return
  }
  tip_img.value = ic_start_playback
  tip_one.value = i18n.t('video.videoRecord')
  tip_address.value = vedioadreess.value
  tip_two.value = i18n.t('InGameMonitor.isSaving')
}

function BackVideoSavedIng() {
  if (!$store.state.ingame_record) {
    gamepp.webapp.windows.close.sync('ingame_tipspage');
    return
  }
  tip_img.value = ic_start_playback
  tip_one.value = i18n.t('InGameMonitor.video_replay')
  tip_two.value = i18n.t('InGameMonitor.isSaving')
}

function VideoSavedSucc() {
  if (!$store.state.ingame_record) {
    gamepp.webapp.windows.close.sync('ingame_tipspage');
    return
  }
  tip_img.value = ic_saved_recording
  tip_one.value = i18n.t('video.videoRecord')
  tip_address.value = vedioadreess.value
  tip_two.value = i18n.t('InGameMonitor.saved')
}

//画质加载
function QualityIng(data_arr:any) {
  if (!$store.state.ingame_filter || !gamepp.package.isexists.sync('GameMirror')) {
    gamepp.webapp.windows.close.sync('ingame_tipspage');
    return
  }
  tip_img.value = ic_replace_plan
  tip_one.value = i18n.t('Setting.gameFilter')
  tip_two.value = i18n.t('InGameMonitor.loadQualitysScheme') + ' '+data_arr['svalue']
}


//画质开启
function QualityOn(data:any) {
  if (!$store.state.ingame_filter || !gamepp.package.isexists.sync('GameMirror')) {
    gamepp.webapp.windows.close.sync('ingame_tipspage');
    return
  }
  let KeyValue = data.svalue;
  if (data.svalue === '无' || data.svalue === 'None' || data.svalue === 'None_cn') {
    KeyValue = i18n.t('InGameMonitor.notSet');
  }
  tip_img.value = ic_open_plan;
  tip_one.value = i18n.t('InGameMonitor.mirrorEnable')
  tip_two.value = KeyValue + ' ' + i18n.t('InGameMonitor.canBeTurnedOff')
}


//画质关闭
function QualityOff(data:any) {
  if (!$store.state.ingame_filter || !gamepp.package.isexists.sync('GameMirror')) {
    gamepp.webapp.windows.close.sync('ingame_tipspage');
    return
  }
  let KeyValue = data.svalue;
  if (data.svalue === '无' || data.svalue === 'None' || data.svalue === 'None_cn') {
    KeyValue = i18n.t('InGameMonitor.notSet');
  }
  tip_img.value = ic_close_plan
  tip_one.value = i18n.t('InGameMonitor.mirrorClosed')
  tip_two.value = KeyValue + ' ' + i18n.t('InGameMonitor.openMirror')
}

//游戏内显示
function HardwareOn() {
  if (!$store.state.ingame_monitor) {
    gamepp.webapp.windows.close.sync('ingame_tipspage');
    return
  }
  tip_img.value = ic_open_data
  tip_one.value = i18n.t('InGameMonitor.InGameMonitor')
  tip_two.value = i18n.t('tsc.open')
}

function HardwareOff() {
  if (!$store.state.ingame_monitor) {
    gamepp.webapp.windows.close.sync('ingame_tipspage');
    return
  }
  tip_img.value = ic_close_data
  tip_one.value = i18n.t('InGameMonitor.InGameMonitor')
  tip_two.value = i18n.t('InGameMonitor.closed')
}

//Highlight 开启
function HighlightOpen() {
  tip_img.value = ic_start_playback
  tip_one.value = i18n.t('InGameMonitor.wonderfulScenes')
  tip_two.value = i18n.t('tsc.open')
}

function HighlightClose() {
  tip_img.value = ic_start_playback
  tip_one.value = i18n.t('InGameMonitor.wonderfulScenes')
  tip_two.value = i18n.t('InGameMonitor.closed')
}

function VULKANPoint() {
  tip_img.value = gpp_tips_en
  tip_one.value = i18n.t('InGameMonitor.VulkanModeHaveProblem')
  tip_two.value = i18n.t('InGameMonitor.suggestDxMode')
}

function ManualRecording() {
  if (!$store.state.ingame_statistics) {
    gamepp.webapp.windows.close.sync('ingame_tipspage');
    return
  }
  tip_one.value = i18n.t('InGameMonitor.gppManualRecording')
  tip_two.value = i18n.t('InGameMonitor.functionNotSupported')
}

function ManualRecordingStop() {
  if (!$store.state.ingame_statistics) {
    gamepp.webapp.windows.close.sync('ingame_tipspage');
    return
  }
  tip_one.value = i18n.t('InGameMonitor.perfRecordsHaveBeenSaved')
  tip_two.value = i18n.t('InGameMonitor.redoClickF8')
}


//功能不支持文字提示
function TextPrompt(data:any) {
  tip_img.value = gpp_tips_en
  tip_one.value = TextPromptArr[data['type']]
  tip_two.value = i18n.t('InGameMonitor.NotSupported')
}


//塔科夫价格查询 不支持其他分辨率
function TarkovPrice() {
  tip_img.value = gpp_tips_en
  tip_one.value = '塔科夫价格查询';
  tip_two.value = '仅支持1920*1080分辨率'
}

function LoadingFunctionTips (data:any) {
  if (data.svalue === '正在启用游戏内监控功能' && !$store.state.ingame_injection) {
    gamepp.webapp.windows.close.sync('ingame_tipspage');
    return
  }
  tip_img.value = gpp_tips_en
  tip_one.value = i18n.t('messages.gamepp');
  tip_two.value = i18n.t('InGameMonitor.startIngameMonitor');
}

function pointMark () {
  if (!gamepp.package.isexists.sync('GameRebound')) {
    gamepp.webapp.windows.close.sync('ingame_tipspage');
    return
  }
  tip_img.value = gpp_tips_en
  tip_one.value = i18n.t('home.performanceStatistics');
  tip_two.value = i18n.t('InGameMonitor.inGameMarkSuccess')
}

function obsTriggerError(data_arr) {
    let svalue = data_arr.svalue;
    if (svalue.includes('录像功能未下载')) {
        tip_img.value = ic_saved_recording_error
    }else{
        tip_img.value = ic_saved_recording_problem
    }
    tip_one.value = i18n.t('InGameMonitor.recordingFailed');
    tip_two.value = i18n.t('InGameMonitor.recordingHasNotDownload');
}

/**
 *功能开启失败
 */
function FunctionFailed (data:any) {
  tip_img.value = gpp_tips_en
  tip_one.value = data.svalue;
  tip_two.value = i18n.t('GamePlusOne.fail');
}

/**
 * 功能开启成功
 */
function FunctionSuccess (data:any) {
  tip_img.value = gpp_tips_en
  tip_one.value = data.svalue;
  tip_two.value = i18n.t('GamePlusOne.success');
}

function HotkeyConflict () {
  tip_img.value = gpp_tips_en
  tip_one.value = i18n.t('InGameMonitor.hotkeyDetected');
  tip_two.value = i18n.t('InGameMonitor.plzEditIt');
}

const Position = computed(()=>{
  let obj = {
    left: '',
    right: '',
    top: '',
    bottom: ''
  }
  if ($store.state.ingameMsgPosition.includes('left')) {
    obj.left = '0px'
    obj.right = ''
  }else{
    obj.right = '0px'
    obj.left = ''
  }
  if ($store.state.ingameMsgPosition.includes('top')) {
    obj.top = '50px'
    obj.bottom = ''
  }else if ($store.state.ingameMsgPosition.includes('center')) {
    obj.top = (gamepp.game.getHeight.sync() / 2 - 130) + 'px'
    obj.bottom = ''
  }else{
    obj.top = ''
    obj.bottom = '50px'
  }
  return obj
})
</script>
<template>
  <template v-if="!quickCloseWindow && !aiHighLightTip">
    <audio ref="BgMusic" id="BgMusic" :src="audioSrc" v-show="isShow"></audio>
    <!--<audio id="BgMusic" src="dist/media/camera.wav"></audio>-->
    <div v-show="isShow" class="box" :style="{top:Position.top,left:Position.left,right:Position.right,bottom:Position.bottom}" :class="{'boxWidthAni':boxWidthAni,'boxWidthAniHide':boxWidthAniHide}">
      <div class="box_words">
        <img id="tip_img" :src="tip_img">
        <div class="box_title" :style="{marginTop:box_titleMarginTop+'px'}">
          <p id="tip_one" class="title_name">{{tip_one}}</p>
          <!-- 路径地址 -->
          <p id="tip_address" class="tip_address">{{tip_address}}</p>
          <p class="describe_al">
            <span id="tip_two" class="describe" :class="tip_twoClassName">{{tip_two}}</span>
            <!--<span class="describe">显示时间<span id="debug" class=""></span></span>-->
          </p>
        </div>
      </div>
      <div class="box_lg" :style="{left:Position.left,right:Position.right}" :class="{'boxLgWidthAni':boxLgWidthAni,'boxLgWidthAniHide':boxLgWidthAniHide}"></div>
    </div>
    <input v-show="isShow" id="ShowSta" type="hidden" name="" v-model="ShowSta">
  </template>
  <template v-if="quickCloseWindow">
    <QuickQuitTips />
  </template>
  <template v-if="aiHighLightTip">
    <AiHighLightTips />
  </template>
</template>

<style>
#app {
    width: 100vw;
    height: 100vh;
}
</style>

<style lang="scss" scoped>
.boxLgWidthAni {
  animation: boxLgWidth 100ms ease-out forwards;
  animation-delay: 120ms;
}
.boxWidthAni {
  animation: boxWidth 1000ms ease-out forwards;
  animation-delay: 120ms;
}
.boxLgWidthAniHide {
  animation: boxLgWidthHide 600ms ease-out forwards;
  animation-delay: 120ms;
}
.boxWidthAniHide {
  animation: boxWidthHide 1000ms ease-out forwards;
  animation-delay: 120ms;
}
@keyframes boxLgWidth {
  from { width: 0; opacity: 0.5; }
  to { width: 6px; opacity: 1; }
}
@keyframes boxLgWidthHide {
  from { width: 6px; opacity: 1; }
  to { width: 0; opacity: 0.5; }
}
@keyframes boxWidth {
  from { width: 0; opacity: 0.5; }
  to { width: 260px; opacity: 1; }
}
@keyframes boxWidthHide {
  from { width: 260px; opacity: 1; }
  to { width: 0; opacity: 0.5; }
}
.Container{width: auto;height:auto;padding: 0 1px;position: fixed;}

.game-fps{margin-bottom: 8px;text-shadow: 0 1px rgba(16, 16, 16, 0.65), 1px 0 rgba(16, 16, 16, 0.65), -1px 0 rgba(16, 16, 16, 0.65), 0 -1px rgba(16, 16, 16, 0.65);font-weight: bold;}


.game-fps .title_name{color: #45EE71;font-size: 16px;font-weight: bold;}
.game-cpu{margin-bottom: 8px;text-shadow: 0 1px rgba(16, 16, 16, 0.65), 1px 0 rgba(16, 16, 16, 0.65), -1px 0 rgba(16, 16, 16, 0.65), 0 -1px rgba(16, 16, 16, 0.65);}
.game-cpu P{color: #FBC214;font-size: 14px;font-weight:bold}
.game-cpu .data{color: #FBC214;font-size: 14px;display: block;font-weight: bold}


.game-npu{margin-bottom: 8px;text-shadow: 0 1px rgba(16, 16, 16, 0.65), 1px 0 rgba(16, 16, 16, 0.65), -1px 0 rgba(16, 16, 16, 0.65), 0 -1px rgba(16, 16, 16, 0.65);}
.game-npu P{color: #FBC214;font-size: 14px;font-weight:bold}
.game-npu .data{color: #FBC214;font-size: 14px;display: block;font-weight: bold}

.game-gpu,.game-gpuram{margin-bottom: 8px;text-shadow: 0 1px rgba(16, 16, 16, 0.65), 1px 0 rgba(16, 16, 16, 0.65), -1px 0 rgba(16, 16, 16, 0.65), 0 -1px rgba(16, 16, 16, 0.65);}
.game-gpu,.game-gpuram p{color: #3DD4FF;font-size: 14px;font-weight: bold}
.game-gpu,.game-gpuram .data{color: #3DD4FF;font-size: 14px;display: block;font-weight: bold}
.game-Memory{margin-bottom: 8px;text-shadow: 0 1px rgba(16, 16, 16, 0.65), 1px 0 rgba(16, 16, 16, 0.65), -1px 0 rgba(16, 16, 16, 0.65), 0 -1px rgba(16, 16, 16, 0.65);}
.game-Memory p{color: #F3E587;font-size: 14px;font-weight: bold}
.game-Memory .data{color: #F3E587;font-size: 14px;display: block;font-weight: bold}
.game-Other{text-shadow: 0 1px rgba(16, 16, 16, 0.65), 1px 0 rgba(16, 16, 16, 0.65), -1px 0 rgba(16, 16, 16, 0.65), 0 -1px rgba(16, 16, 16, 0.65);}
.game-Other p{color: #FFFFFF;font-size: 14px;font-weight: bold}
.game-Other .data{color: #FFFFFF;font-size: 14px;display: block;font-weight: bold}
.game-Other img{margin-left: 2px;}
.Container span{font-weight: bold;}
.tip_address {color: white;}

/*//横条*/
#Container_normal {
  width:auto;
  height: auto;
  /*background-color:rgba(0,0,0,0.6);*/
  position: fixed;
  /*padding: 0 1px;*/
}
/*.sort_outside{color: #989a9e;float: left; padding-left: 5px;padding-right: 10px;}*/
.sort_outside{color: #A3A3A3;display: inline;
  /*margin-left: 4px;*/
  padding-right: 0;
  background-color:rgba(0,0,0,0.55);
}

.deep_color{background-color: rgba(0,0,0,0.75);}

#Container_normal #outside_41{
  /*display: inline-flex;*/
  vertical-align: bottom;
  align-items: center;
}

.gray{color: #808080}
.green{color: #60e54d;}
.red{color:#e04e3a;}
.yellow{color: #e3c521;}
.blue{color:#48b0ec;}
.time_white{color: #FFFFFF;}
/*.circular{width: 10px;height: 10px;border-radius: 50%;background: #f13b3b;position: absolute;top:50%;left: 8px;margin-top: -5px;margin-left: -5px;}*/
/*.spot{float: left;position: relative;}*/
.sort_outside .data{display: inline;margin-left: 0.3%;margin-right:0.3%}


/*/测评模式/*/
.data-map{width:512px; }
.videoCard{display: inline-block;width: 512px;}
.videoCard .title{width: 113px;font-size: 18px;color: #FBC214;font-weight: bold;float: left;text-shadow: 0 1px rgba(16, 16, 16, 0.65), 1px 0 rgba(16, 16, 16, 0.65), -1px 0 rgba(16, 16, 16, 0.65), 0 -1px rgba(16, 16, 16, 0.65);}
.videoCard .attribute{float: left;color: #FBC214;font-size: 18px;font-weight: bold; text-shadow: 0 1px rgba(16, 16, 16, 0.65), 1px 0 rgba(16, 16, 16, 0.65), -1px 0 rgba(16, 16, 16, 0.65), 0 -1px rgba(16, 16, 16, 0.65);}
.attribute p{float: left;height: 24px;}
.numeric{padding-right: 5px;float: right;width: 57px!important;text-align: right;font-size: 18px!important;}
.attribute span{float: left;font-size: 14px;height: 24px;}
.Occupy{width: 35px;}
.attribute p:first-child{padding-left:0;}
.span{display: block;line-height: 28px;width: 34px;}
.one{width: 57px!important;}
.lg .title{color: #3DD4FF;}
.lg .attribute{color: #3DD4FF;}
.ram .title{color: #F3E587;}
.ram .attribute{color: #F3E587;}
.D3D .title{color: #45EE71;}
.D3D .attribute{ color:#45EE71;}
.Frametime .title{color: #ff9138;font-size: 14px;}
.Frametime .attribute{ color:#ff9138;}
.time-right{font-size: 14px!important;float: right;}
.ms{font-size: 12px!important;line-height: 21px;}
.Frametime-lg p{float: right;margin-right: 14px;position: relative;top:10px;}
.net .title{color: #ffffff;}
.net .attribute{ color:#ffffff;}
.attribute img{padding-left: 20px;}
#time {font-size: 18px;color: #ffffff;}
.red-r{width: 15px;height: 15px!important;background: #C73E30;border-radius:50%;margin-left: 27px;margin-top: 5px;}
.diagram{width: 100%;border: 0 solid red; height:auto;padding: 8px 0 5px;}
#container{max-width:450px;height:70px}



/*//提示框*/

.box {
  width:0;height:90px; background:rgb(25, 41, 46);position: fixed;right: 0;
}
.box_lg{width:0;height: 90px;background: #ECB025;position: absolute;right: 0;z-index: 99;}


#tip_img{margin: 24px 15px;float: left;}
.box_words{width: 254px;height: 90px;float: left;}
.box_title{float: left;width: 180px;height: 40px;margin-top: 23px;}
.box_top{font-size: 14px;color:#FEFEFE;margin-top: 45px;}
.title_name{font-size: 14px;color:#FEFEFE;}
.describe{font-size: 14px;color: #ECB025;font-weight: bold;padding-right: 20px;}
.describe1{font-size: 12px;color: #F23939;font-weight: bold;padding-right: 20px;}
.describe2{font-size: 14px;color: #F23939;font-weight: bold;padding-right: 20px;}
.describe_al{margin-top: 2px;}
.state{font-size: 14px;color:#FEFEFE;font-weight: bold;}

.hints{width:0;height:213px;position: fixed;right: 0;bottom: 130px;}
.hints-head{height: 60px;border-bottom: 1px solid #8F8F8F;}
.tips-left{float: left;background: rgb(25, 41, 46);width: 254px}
.tips-right{float: left;width: 0;background: #ECB025;height: 213px;position: absolute;right: 0;z-index: 99;}
.hints-main{height: 152px;padding-top: 9px;box-sizing: border-box;}
.hints-head span{font-size: 14px;color: #EEEEEE;font-weight: bold;line-height: 60px;}
.hints-head img{margin-left: 28px;margin-right: 15px;margin-top: -5px;}
.hints-main p{font-size: 14px;color: #ECB025;font-weight: bold;margin-bottom: 12px;margin-left: 20px;}
.countdown{margin-top: 30px;}
.countdown span{font-size: 14px;color: #ECB025;font-weight: bold;margin-left: 20px;display: block;margin-bottom: 12px;}
.countdown span em{font-style: normal;color: #39E041;}
.hints-process{height: 152px;padding-top: 9px;box-sizing: border-box;display: none;}
.hints-process p{font-size: 14px;color: #ECB025;font-weight: bold;margin-bottom: 5px;margin-left: 20px;}
.course li{width: 100%;height: 25px;line-height: 25px;}
.course li span{font-size: 12px;color: #FFFFFF;}
#bar {background: #4AB54F;float: left;text-align: center;line-height: 150%;height:100%;}
.container-bar{width: 254px;height: 3px;margin-top: -3px;}

.loader-min {width:15px;height:15px;border-radius:100%;border:4px solid;border-top-color:rgba(254, 168, 23, 0.65);border-bottom-color:rgba(57, 154, 219, 0.65);border-left-color:rgba(188, 84, 93, 0.95);border-right-color:rgba(137, 188, 79, 0.95);-webkit-animation: loader2 1s linear infinite; position: relative; top: -24px; left: 220px;}
@-webkit-keyframes loader2 { from {-webkit-transform: rotate(0deg);} to {-webkit-transform: rotate(360deg);} }
.process-name1{font-size: 11px;color: #B9BFCA;display: block;float: left;line-height: 40px!important;margin-left: 10px;height: 40px;overflow: hidden;}
.mark_completed{display: block; position: relative; top: -20px; left: 223px;}
.hints-process li{height: 28px;}
.hints-process .word{font-size: 12px; font-family:Microsoft YaHei;color:rgba(255,255,255,1);font-weight:400;margin-left: 21px; line-height: 25px;}
.hints-process .dots{font-size: 12px; font-family:Microsoft YaHei;color:rgba(255,255,255,1);font-weight:400;position: relative;top: -22px;left: 227px;}
.hints-process .optimize{font-size: 12px; font-family:Microsoft YaHei;color:rgba(255,255,255,1);font-weight:400;display: inline-block;}

.DelayPrompt{width: auto;font-size: 13px;background: rgba(31, 31, 31, 0.7);text-align: right;height:22px;float: right;margin-right: 50px;padding-left: 10px;display: none;    text-shadow: 0 1px rgba(16, 16, 16, 0.65), 1px 0 rgba(16, 16, 16, 0.65), -1px 0 rgba(16, 16, 16, 0.65), 0 -1px rgba(16, 16, 16, 0.65);}
/*.DelayPrompt-center{padding: 10px 0 10px 25px;}*/
.DelayPrompt-center p{color: #B6B6C6;margin-top: 10px;}
.DelayPrompt-center span{color: #c7c7c7;}
.DelayPrompt-center p:first-child{margin-top: 0;}
.Moving-point{color: #B6B6C6!important;}
.dot {
  display: inline-block;
  /*width: 1.5em;*/
  vertical-align: bottom;
  overflow: hidden;
}

@-webkit-keyframes dot {
  0% { width: 0; margin-right: .8em; }
  33% { width: .2em; margin-right: .5em; }
  66% { width: .5em; margin-right: .2em; }
  100% { width: .8em; margin-right: 0;}
}
.dot {
  -webkit-animation: dot 3s infinite step-start;
  animation: dot 3s infinite step-start;
}
.continuity-Alt{font-weight: bold;color: #0089E9!important;}
.region{color: #B6B6C6!important;}
.testing em{font-style: normal;color: #E3C521;padding-left: 5px;padding-right: 5px;}
.continuity-Alt em{color:#0089E9;font-style: normal;padding-left: 5px;padding-right: 5px;font-weight: bold;}
.optimal{color: #B6B6C6;font-weight: normal;}
.probably{color: #DE1F0B;font-weight: bold;}
.Cancel-switch{color: #B6B6C6;margin-top: 10px;}
.Cancel-switch span{padding-right: 5px;}
.Switch-succeeded{color: #B6B6C6;margin-bottom: 10px;}
.Recon{margin-top: 10px;}

/*自检模式*/
.Checking{max-width: 550px;}

.Checking .game-fps{line-height: 21px;}
.Checking .game-fps .title_name{}
.Low{float: left;width: 110px;}
.fps-all{display: table;}
.game-cpu-int{display: table;margin-top: 3px;}
.game-cpu-int em{font-style: normal;margin-left: 10px;}
.game-cpu-int ul{float: left;width: 100%;line-height: 20px;}
.game-cpu-int ul li{float: left;padding-right: 22px;}
.game-cpu-int ul li:last-child{padding-right: 0;}
.core-all{margin-left: 22px;}
.core-all{display: table;}
.core-all em{font-style: normal;margin-left: 10px;}
.core-all ul{float: left;width: 100%;line-height: 21px;}
.core-all ul li{float: left;padding-right: 22px;}
.core-all ul li:last-child{padding-right: 0;}
.gpu-display{margin:3px 0 5px 0;}
.gpu-display span{margin-right: 5px;}
.display-data{color: #3DD4FF;font-size: 14px;font-weight: bold;line-height: 22px;margin-left: 27px;}
.display-data em{font-style: normal;}
.name-data{width: 105px;display: inline-block;}
.VRAMUsage-d{margin-left: 20px;}
.gpu-display p{margin-bottom: 3px;}
.game-RAM{margin-bottom: 8px;text-shadow: 0 1px rgba(16, 16, 16, 0.65), 1px 0 rgba(16, 16, 16, 0.65), -1px 0 rgba(16, 16, 16, 0.65), 0 -1px rgba(16, 16, 16, 0.65);}
.game-RAM p{color: #F3E587;font-size: 14px;font-weight: bold}
.game-RAM .display-data{color: #F3E587;font-size: 14px;font-weight: bold;line-height: 21px;margin-left: 27px;}
.videoCard-new .title{font-size: 14px!important;}
.attribute-new img{width: 15px;height: 15px;padding-left: 10px!important;margin-top: -3px;}
.attribute-new .net_p{margin-right: 40px;}
.attribute-new p{font-size: 14px!important;}
.videoCard-new .title{width: 105px!important;}
.red-r-new{margin-left: 12px;margin-top: 2px;}

/* 小飞机模式 */
.SmallPlane{max-width: 625px;height: auto;padding: 15px 0 15px 15px;font-family:'unispacebd'!important;text-shadow: 0 1px rgb(00 00 00 / 100%), 1px 0 rgb(00 00 00 / 100%), -1px 0 rgb(00 00 00 / 100%), 0 -1px rgb(00 00 00 / 100%);}
.filter{border: 1px solid rgba(133, 133, 133, 0.3);backdrop-filter: blur(3px) brightness(100%);border-radius: 4px;}
.typeData{display: flex;align-items: baseline;line-height: 28px;}
.type{display: flex;align-items: baseline;}
.type div{display: flex;text-align: right;}
.type div:first-child{margin-right: 25px;}
.symbol{font-size: 12px!important;position: relative;top: -3px;margin-left: 8px;transform: scale(.8);}
.symbol_b{font-size: 12px!important;position: relative;bottom: -3px;margin-left: 0!important;transform: scale(.8);}
.posit{top:-6px}

.gap span:nth-child(2){margin-left: 10px;}
.type  p{font-size: 18px;margin-right: 10px;}
.PlaneColor1{color: #C08080;}
.PlaneColor2{color: #FFFFFF;}
.PlaneColor3{color: #0080C0;}
.PlaneColor4{color: #FF8000;}
.PlaneColor5{color: #008040;}
.Width1{width: 44px;font-size: 18px;}
.Width2{width: 33px;font-size: 18px!important;}
.Width3{width: 55px;font-size: 18px;}
/* .Width4{width: 74px;font-size: 18px} */
.fontSize{font-size: 24px;}
.sign_name{font-size: 18px;margin-right: 10px!important;}
.sign_name span:last-child{margin-left: 10px;}
.gpu_model{margin-left: 25px;}
.fontSize2{font-size: 18px;}
.redMode{width: 12px;height: 12px;background: green;border-radius: 50%;margin: 6px 0 0 5px;box-shadow: 0px 0px 6px #000;}
.type span:nth-child(4){margin-left: 10px;}
.type img{width: 8px;height: 9px;margin-top: 5px;}
.Slash{margin: 0 8px;}


#Container_normal .data{text-align: center;}
.FontSize1 #cpu_temperature{width: 35px;}
.FontSize1 #cpu_totalusage{width: 33px;}
.FontSize1 #cpu_usageP{width: 48px;}
.FontSize1 #cpu_usageE{width: 47px;}
.FontSize1 #cpu_totalclock{width: 55px;}
.FontSize1 #cpu_clockP{width: 71px;}
.FontSize1 #cpu_clockE{width: 70px;}
.FontSize1 #cpu_power{width: 34px;}
.FontSize1 #cpu_fans{width: 55px;}
.FontSize1 #gpu_temperature{width: 34px;}
.FontSize1 #gpu_HotSpotTemp{width: 34px;}
.FontSize1 #gpu_clock{width: 55px;}
.FontSize1 #gpu_power{width: 39px;}
.FontSize1 #gpu_fans{width: 55px;}
.FontSize1 #cpu_vid{width: 55px;}
.FontSize1 #motherboard_vcore{width: 43px;}
.FontSize1 #gpu_usage{width: 55px;}


.FontSize2 #cpu_temperature{width: 38px;}
.FontSize2 #cpu_totalusage{width: 36px;}
.FontSize2 #cpu_usageP{width: 52px;}
.FontSize2 #cpu_usageE{width: 51px;}
.FontSize2 #cpu_totalclock{width: 59px;}
.FontSize2 #cpu_clockP{width: 75px;}
.FontSize2 #cpu_clockE{width: 74px;}
.FontSize2 #cpu_power{width: 38px;}
.FontSize2 #cpu_fans{width: 59px;}
.FontSize2 #gpu_temperature{width: 37px;}
.FontSize2 #gpu_HotSpotTemp{width: 37px;}
.FontSize2 #gpu_clock{width: 59px;}
.FontSize2 #gpu_power{width: 37px;}
.FontSize2 #gpu_fans{width: 59px;}
.FontSize2 #cpu_vid{width: 59px;}
.FontSize2 #motherboard_vcore{width: 47px;}
.FontSize2 #gpu_usage{width: 59px;}

.FontSize3 #cpu_temperature{width: 44px;}
.FontSize3 #cpu_totalusage{width: 42px;}
.FontSize3 #cpu_usageP{width: 60.1px;}
.FontSize3 #cpu_usageE{width: 60px;}
.FontSize3 #cpu_totalclock{width: 69px;}
.FontSize3 #cpu_clockP{width: 88px;}
.FontSize3 #cpu_clockE{width: 87px;}
.FontSize3 #cpu_power{width: 43.1px;}
.FontSize3 #cpu_fans{width: 69px;}
.FontSize3 #gpu_temperature{width: 43px;}
.FontSize3 #gpu_HotSpotTemp{width: 43px;}
.FontSize3 #gpu_clock{width: 69px;}
.FontSize3 #gpu_power{width: 44px;}
.FontSize3 #gpu_fans{width: 69px;}
.FontSize3 #cpu_vid{width: 69px;}
.FontSize3 #motherboard_vcore{width: 52px;}
.FontSize3 #gpu_usage{width: 69px;}

.FontSize4 #cpu_temperature{width: 50.2px;}
.FontSize4 #cpu_totalusage{width: 47.5px;}
.FontSize4 #cpu_usageP{width: 69px;}
.FontSize4 #cpu_usageE{width: 68px;}
.FontSize4 #cpu_totalclock{width: 78.2px;}
.FontSize4 #cpu_clockP{width: 100px;}
.FontSize4 #cpu_clockE{width: 99px;}
.FontSize4 #cpu_power{width: 49.2px;}
.FontSize4 #cpu_fans{width: 78.2px;}
.FontSize4 #gpu_temperature{width: 49px;}
.FontSize4 #gpu_HotSpotTemp{width: 49px;}
.FontSize4 #gpu_clock{width: 78.2px;}
.FontSize4 #gpu_power{width: 49.2px;}
.FontSize4 #gpu_fans{width: 78.2px;}
.FontSize4 #cpu_vid{width:  78.2px;}
.FontSize4 #motherboard_vcore{width: 60px;}
.FontSize4 #gpu_usage{width: 78px;}


.FontSize5 #cpu_temperature{width: 100.3px;}
.FontSize5 #cpu_totalusage{width: 94.5px;}
.FontSize5 #cpu_usageP{width: 137.2px;}
.FontSize5 #cpu_usageE{width: 135.2px;}
.FontSize5 #cpu_totalclock{width: 156.5px;}
.FontSize5 #cpu_clockP{width: 199.2px;}
.FontSize5 #cpu_clockE{width: 197.2px;}
.FontSize5 #cpu_power{width: 98.5px;}
.FontSize5 #cpu_fans{width: 156.5px;}
.FontSize5 #gpu_temperature{width: 98px;}
.FontSize5 #gpu_HotSpotTemp{width: 98px;}
.FontSize5 #gpu_clock{width: 156.3px;}
.FontSize5 #gpu_power{width: 98.5px;}
.FontSize5 #gpu_fans{width: 156.5px;}
.FontSize5 #cpu_vid{width:  156.5px;}
.FontSize5 #motherboard_vcore{width: 118px;}
.FontSize5 #gpu_usage{width: 156px;}


/* 永劫无间 */
#Container_Forever{-webkit-app-region: drag;
  display: none;
}
.Forever{ width: 8.6rem;height: 1.87rem;padding-top: 6px;background: url('../../../static/img/Public/nalakainsidebg.webp')no-repeat;background-size: 100% 100%;}
.lie{text-align: center;}
.rail{display: flex;align-items: center;margin-top: 0.24rem;}
/* .lie2{width: 1rem;} */
.titleAll{font-size: .14rem;margin-right: .1rem;color: #fff;}
.lieFlex{display: flex;align-items: center;}
.memoryAll{margin-left: .1rem;}
.lie4{display: flex;align-items: center;height: .2rem;}
.outside_cg{display: flex; justify-content: space-between;margin-top: 0.06rem;margin-bottom: 0.04rem;}
.Forever_cpu{margin-left: .32rem;margin-top: 0.12rem;}
.mag{margin-right: .02rem;width: .85rem;}
.Forever_cpu .titleAll{margin-bottom: .02rem;width: 1.45rem;}
.Forever_cpu .lie4:nth-child(2){margin-left: .1rem;}
.Forever_cpu .lie4:nth-child(3){margin-left: .05rem;}
.lie5{display: flex;align-items: center;height: .22rem;}
.Forever_gpu{margin-top: .12rem;}
.Forever_gpu .lie5:nth-child(3){margin-left: .05rem;}
.Forever_gpu .lie5:nth-child(4){margin-left: .1rem;}
.Forever_gpu .titleAll{margin-bottom: .02rem;width: 1.45rem;}
.Forever_name{text-align: center;display: block;}
.lie2{margin-left: 1.9rem;}
.lie3{margin-left: 1.8rem;}
.PEall .lie4:first-child{margin-left: .15rem;}
.titleAll span{font-weight: 600;}
</style>
