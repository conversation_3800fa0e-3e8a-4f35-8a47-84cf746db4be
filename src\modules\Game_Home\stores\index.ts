import {computed, reactive, ref, toRaw, watch} from 'vue'
import {defineStore} from 'pinia'
import {bg_sensor_data, bg_sensor_data_gpu} from "@/modules/Game_Home/hooks/types";
import idb from '@/uitls/indexedDB'
import { GPP_SendStatics } from '@/uitls/sendstatics';
import erhaImg from '../../Game_Home/assets/icon_erha.png'
// @ts-ignore
const gamepp = window.gamepp

// export const useGameStore = defineStore('game', {
//   state: () => ({
//     ingameNow: false,
//   }),
//   actions: {
//     setIngameNow(value: boolean) {
//       this.ingameNow = value;
//     },
//   },
// });

export const gameppUser = defineStore('gameppUser',()=>{//游戏加加用户基本信息
  const isVip = ref('false')
  const email = ref(null)
  const uid = ref(0)


  return { isVip,email,uid }
})

export const firstPageInfo =  defineStore('firstPageInfo', () => {//首页信息 数据从其他模块获取
  const info = ref(
  {
      hardware:
      [
        {type:'CPU',name:'',value:0,unit:'C',icon:'iconfont icon-CPU'},
        {type:'GPU',name:'',value:0,unit:'C',icon:'iconfont icon-GPU'}
      ],

      monitor:
      [
        '测试','0.1% Low Fps','CPU Temp','CPU Useage','CPU Freq','CPU风扇转速',
        'FPS','0.1% Low Fps','CPU Temp','CPU Useage','CPU Freq','CPU风扇转速',
        'FPS','0.1% Low Fps','CPU Temp','CPU Useage','CPU Freq','CPU风扇转速',
        'FPS','0.1% Low Fps','CPU Temp','CPU Useage','CPU Freq','CPU风扇转速',
      ],

      recentlyGaming:
      {
          name:'英雄联盟',
          resulition:'2560x1440',
          playtime:'24小时59分钟',
          data:[
            { name:'Avg FPS',value:'???',},
            { name:'Max Fps',value:'???',},
            { name:'Min Fps',value:'???',},
            { name:'1% Low FPS',value:'???',},
            ]
      },
      gamemirror:
      {
          name:'GameMirror.Bright',
          usercount:10000,
          intro:'GameMirror.BrightTips'
      },
      videoBox:
      {
          vediosrc:'',
          title:'跑分报错/游戏崩溃！13/14代i9为什么连“默认“都不稳定！',
          date:'2024/5/09',
          up:'GamePP游戏加加'
      }
  }
  )
  return { info }

},
{persist:false}
)

export const gameppBaseSetting = defineStore('gameppBaseSetting', () => { //游戏加加基本样式设置
  let enableSave = true
  const theme = ref('light') //主题
  let fontColor = reactive({
    lv1_fontColor:'#777777', // 一级标题颜色
    lv1_fontFamily: 'Microsoft YaHei', // 一级标题(板块标题)字体
    lv2_fontColor:'#777777', // 二级标题颜色
    lv2_fontFamily: 'Microsoft YaHei',// 二级标题(条目标题)字体
    main_fontColor:'#FFFFFF', // 正文颜色
    main_fontFamily: 'Microsoft YaHei',// 正文字体
    des_fontColor:'#FFFFFF', // 描述文本颜色
    des_fontFamily: 'Microsoft YaHei',// 描述文本字体
    icon_fontColor:'#1193F8', // 图标颜色
    highlight_fontColor:'#FFFFFF', // 凸显文本颜色
    highlight_fontFamily: 'Microsoft YaHei',// 凸显文本字体
    degree_fontFamily: 'Microsoft YaHei',// 程度描述字体
    degree_fontColor_low: '#35d57d',
    degree_fontColor_mid: '#1193f8',
    degree_fontColor_high: '#ffa800',
    degree_fontColor_warn: '#bf4040',
  }) //软件字体颜色

  const gameppVideo = ref('')
  const gameppImg = ref('')
  const gameppBgColor = ref('#22232e')
  const colorOrImg = ref(1) // 1:图片/视频 2:颜色 背景用 颜色还是图片/视频
  const imgObjectFit = ref('cover') // 图片3个方式， 填充 拉伸 适应
  const bgOpacity = ref(1)
  const bgOpacity2 = ref(1)
  const gameppWidth = ref(1440) //软件宽
  const gameppHeight = ref(810) //软件高
  const state = reactive({
    lang: 'CN', // 语言 CN中文 EN英文
    CM_SETTING_EXPERIENCE_IMPROVE_SWITCH: true, // 是否用户体验改善计划
    startWithWindowDelay: 40, // 开机自启延迟
    startWithWindowState: false,
    scaleWithSystem: false, // 是否跟随系统缩放
    ingameMsgPosition: 'right bottom', // 游戏内消息位置
    ingameMsg: true, // 是否开启游戏内消息
    ingame_injection: true, // 是否开启游戏注入消息
    ingame_monitor: true, // 是否开启游戏内监控消息
    ingame_filter: true, // 是否开启游戏滤镜消息
    ingame_record: true, // 是否开启录像消息
    ingame_statistics: true, // 是否开启开始/结束标记统计消息
    ingame_screenshot: true, // 是否开启截图消息
    refreshTime: 1000, // 数据刷新时间
    CM_STATISTICS_SAVE_ADDRESS: '', // 性能统计,异常关机保存路径
    CM_STATISTICS_SAVE_SIZE: '', // 性能统计,异常关机保存的大小
    shortcutKey: { // 快捷键
      9: '', // 打开/关闭游戏内设置面板
      15:'', // 打开/关闭游戏内数据显示
      13:'', // 打开/关闭游戏内滤镜
      389:'', // AI滤镜需要按下此快捷键启用
      468:'', // 开始/停止 手动记录统计
      527:'', // 游戏内添加标记点
      44:'',// 开始/停止录像
      47:'',// 即时回放
      60:'',// 截图
    } as any ,
    oled:{ // oled防烧屏幕
      enable: false, // oled防烧屏开没开
      time: 5, // 每几分钟动一次
    },
    performanceStatistics: { //性能统计
      296: true,//性能统计
      521: true,//天气
      297: false,//禁用性能统计窗口自动弹出
      300: 90, // 保留几条数据 无限制是10000
      479: false, // 异常关机报告
    },
    performanceStatisticsAutoClose: false, // 性能统计是否自动关闭
    performanceStatisticsAutoCloseTime: 30, // 性能统计自动关闭时间
    ingameSettingSave: false, // 游戏内设置保存
  })

  watch(theme, () => {saveStoreData()});
  watch(state, () => {saveStoreData()},{deep:true});
  watch(fontColor, () => {saveStoreData()},{deep:true});
  watch(gameppVideo, () => {saveStoreData()});
  watch(gameppImg, () => {saveStoreData()});
  watch(gameppBgColor, () => {saveStoreData()});
  watch(colorOrImg, () => {saveStoreData()});
  watch(imgObjectFit, () => {saveStoreData()});
  watch(gameppWidth, () => {saveStoreData()});
  watch(gameppHeight, () => {saveStoreData()});
  watch(bgOpacity, () => {saveStoreData()});
  watch(bgOpacity2, () => {saveStoreData()});

  // 保存整个store的内容
  function saveStoreData() {
    if (!enableSave) return
    const obj = {
      theme:theme.value,
      fontColor:toRaw(fontColor),
      gameppVideo:gameppVideo.value,
      gameppImg:gameppImg.value,
      gameppWidth:gameppWidth.value,
      gameppBgColor:gameppBgColor.value,
      gameppHeight:gameppHeight.value,
      bgOpacity: bgOpacity.value,
      bgOpacity2: bgOpacity2.value,
      colorOrImg:colorOrImg.value,
      imgObjectFit:imgObjectFit.value,
      state:toRaw(state),
    }
    // console.warn(obj)
    idb.setItem('gameppBaseSetting',obj,(err:Error,res:any)=>{
      if (err) {
        console.log(err,'gameppBaseSetting保存失败')
      }
    })
  }

  async function getStoreData () {
    const data = await idb.getItem('gameppBaseSetting')
    if (data) {
      console.log(data)
      enableSave = false // 同步其他窗口数据时，避免watch到修改导致又存一遍indexedDB
      try {
        if (data.theme) theme.value = data.theme
        if (data.fontColor) Object.assign(fontColor,data.fontColor)
        if (data.gameppVideo !== undefined) gameppVideo.value = data.gameppVideo
        if (data.gameppImg !== undefined) gameppImg.value = data.gameppImg
        if (data.gameppWidth) gameppWidth.value = data.gameppWidth
        if (data.gameppBgColor) gameppBgColor.value = data.gameppBgColor
        if (data.gameppHeight) gameppHeight.value = data.gameppHeight
        if (data.bgOpacity) bgOpacity.value = data.bgOpacity
        if (data.bgOpacity2) bgOpacity2.value = data.bgOpacity2
        if (data.colorOrImg) colorOrImg.value = data.colorOrImg
        if (data.imgObjectFit) imgObjectFit.value = data.imgObjectFit
        if (data.state) Object.assign(state, data.state)
      }catch (e) {
        console.log(e)
      }
      setTimeout(()=>{enableSave=true}) // 全部执行完了，重新设置为true
    }
  }

  function doTry(func: Function, ...args: any[]) {
    try {
      func(...args)
    } catch (e) {
      console.log('%c err: ', "background:red;font-size:16px", e)
    }
  }

  const actions = {
    init() {
      window.addEventListener('storage',(e) => { // 监听indexedDB变化
        if (e.key === 'indexedDBChange') {
          // @ts-ignore
          window.settingChange && clearTimeout(window.settingChange)
          // @ts-ignore
          window.settingChange = setTimeout(()=>{
            getStoreData()
          },150)
        }
      })
      // 先获取持久化存储的数据
      getStoreData().then(()=>{
        // 获取接口的给的数据
        doTry(() => {
          state.scaleWithSystem = (gamepp.setting.getInteger.sync(313) === 1)
        })
        doTry(() => {
          gamepp.getLanguage.promise().then((res: string) => {
            if (res === '') return
            state.lang = res.toUpperCase();
          })
        })
        doTry(() => {
          gamepp.setting.getInteger.promise(266).then((res: number) => {
            state.CM_SETTING_EXPERIENCE_IMPROVE_SWITCH = (res === 1)
          })
        })
        doTry(() => {
          state.startWithWindowState = gamepp.getStartWithWindowState.sync()
        })
        doTry(() => {
          state.refreshTime = gamepp.setting.getInteger.sync(450)
        })
        doTry(() => {
          state.CM_STATISTICS_SAVE_ADDRESS = gamepp.setting.getString.sync(526)
          if (state.CM_STATISTICS_SAVE_ADDRESS == '') {
            gamepp.getAppDataDir.promise().then((p:string)=>{
              state.CM_STATISTICS_SAVE_ADDRESS = p + '\\common\\Data';
            })
          }
        })
        doTry(() => {
          Object.keys(state.shortcutKey).forEach((k)=>{
            state.shortcutKey[k] = gamepp.setting.getString.sync(Number(k));
          })
        })
        doTry(()=>{
          state.oled.enable = (gamepp.setting.getInteger.sync(495) === 1)
          state.oled.time = gamepp.setting.getInteger.sync(496)
        })
        doTry(()=>{
          Object.keys(state.performanceStatistics).forEach( (k)=>{
            if (Number(k) != 300) {
              // @ts-ignore
              state.performanceStatistics[k] = (gamepp.setting.getInteger.sync(Number(k)) === 1) as boolean
            }else{
              // @ts-ignore
              state.performanceStatistics[k] = gamepp.setting.getInteger.sync(Number(k))
            }
          })
        })
        doTry(()=>{
          state.ingameSettingSave = (gamepp.setting.getInteger.sync(475) === 1)
        })
        doTry(()=>{
          state.ingameMsg = (gamepp.setting.getInteger.sync(305) === 1)
        })
      })
    },
    changeLang(value: string) {
      try {
        // if (value !== 'CN' && value !== 'EN') return
        gamepp.setLanguage.promise(value)
        gamepp.setting.setString.promise(242, value);
        window.localStorage.setItem('language',value)
      } catch (error) {
      }
    },
    setInGameMsg() {
      doTry(() => {
        let i = state.ingameMsg?1:0
        if (!i) {
          GPP_SendStatics(100738)
        }
        gamepp.setting.setInteger.sync(305,i)
      })
    },
    setCM_SETTING_EXPERIENCE_IMPROVE_SWITCH (v: boolean) {
      doTry(() => {
        if (v) {
          gamepp.setting.setInteger.promise(266, 1);
        } else {
          gamepp.setting.setInteger.promise(266, 0);
        }
      })
    },
    setStartWithWindowState (v:boolean,delay:number) {
       doTry(() => {
         state.startWithWindowState = v
         state.startWithWindowDelay = delay
         gamepp.setStartWithWindowState.sync(v,delay)
       })
    },
    setRefreshTime(t:number) {
      doTry(() => {
        state.refreshTime = t
        gamepp.setting.setInteger.promise(450, state.refreshTime)
      })
    },
    setOledState(v:boolean, t:number)  {
      // t单位是分钟
      doTry(() => {
        gamepp.setting.setInteger.promise(496, t)
        gamepp.setting.setInteger.promise(495, v ? 1 : 0)
        state.oled.enable = v
        state.oled.time = t
      })
    },
    setIngameSettingSave(v:boolean) {
      doTry(() => {
        gamepp.setting.setInteger.promise(475, v ? 1 : 0)
        state.ingameSettingSave = v
        if (v) {
          GPP_SendStatics(100778)
        }else{
          GPP_SendStatics(100779)
        }
      })
    },
    /**
     * await gamepp.display.getScaleFromWindowInMonitor.promise();这个接口获取系统缩放是多少 值为1,1.25,1.5以此类推
     *
     * 下面这个接口监听系统缩放变化
     * gamepp.display.onDisplayMetricsChanged.addEventListener( async (scaleFactor:number)=>{
     *   监听到变化后执行下面两个接口
     *   设置窗口的最小宽高
     *   gamepp.webapp.windows.setMinimumSize.sync('窗口名', Math.floor(窗口默认宽 * zoomValue.value),Math.floor(窗口默认高 * zoomValue.value))
     *   gamepp.webapp.windows.resize.sync('窗口名',Math.floor(窗口默认宽 * zoomValue.value),Math.floor(窗口默认高 * zoomValue.value))
     *
     *   执行完后修改body的zoom
     *   // document.body.style.zoom = scaleFactor
     *   特别注意，修改body的zoom这一步需要放进微任务执行或者修改一个响应式的值通过在外面watch来执行
     *   例子：
     *   zoomValue.value = scaleFactor
     *   watch(zoomValue,(newValue)=>{
     *      document.body.style.zoom = newValue
     *   })
     *
     *   如果是需要全屏的窗口，需要手动算窗口，窗口宽高需要除以缩放值
     * })
     */
    setScaleWithSystem(v:boolean) {
      if (!v) {
        GPP_SendStatics(100721) // 取消勾选跟随系统缩放打点
      }
      doTry(() => {
        gamepp.setting.setInteger.promise(313, v ? 1 : 0)
        state.scaleWithSystem = v
      })
    },
  }

  setTimeout(actions.init,50)

  return { theme,fontColor,gameppVideo,gameppImg,bgOpacity,bgOpacity2,gameppBgColor,gameppWidth,gameppHeight,colorOrImg,imgObjectFit,state,actions,saveStoreData }
},)

export const moduleInfo = defineStore('moduleInfo', () => {//游戏加加导航栏以及模块设置
  const activeTab = ref(0)
  const tabInfo = ref([
    {
      name:'home.homeTitle',
      enname:'Home',
      choosen:true,
      route:'/',
      download:{installed:true,installing:false,progress:0},//是否安装 是否安装中 安装进度
      star:true,
      icon:'iconfont icon-homepage',
      Inleft:true,
      module:false,
      windowName:'',
      order:0,
    },
    {
      name:'home.hardwareInfo',
      enname:'HardWare',
      choosen:false,
      route:'/HardWare',
      download:{installed:true,installing:false,progress:0},
      star:true,
      icon:'iconfont icon-hardwaredetail',
      Inleft:true,
      module:false,
      windowName:'',
      order:1,
    },
    {
      name:'InGameMonitor.InGameMonitor',
      enname:'InGame Monitor',
      choosen:false,
      route:'/InGameMonitor',
      download:{installed:true,installing:false,progress:0},
      star:true,
      icon:'iconfont icon-fpsyc',
      Inleft:true,
      module:false,
      windowName:'',
      order:2,
    },
    {
      name:'Setting.performanceStatistics',
      enname:'Performance Metrics',
      choosen:false,
      route:'/GameRebound',
      download:{installed:false,installing:false,progress:0,isLatest:true},
      star:true,
      icon:'iconfont icon-a-Performancestatistics',
      Inleft:false,
      module:true,
      windowName:'game_rebound',
      order:3,
    },
    {
      name:'home.gameFilter',
      enname:'GameMirror',
      choosen:false,
      route:'/GameMirror',
      download:{installed:false,installing:false,progress:0,isLatest:true},
      star:true,
      icon:'iconfont icon-VIPfilter',
      Inleft:false,
      module:true,
      windowName:'game_mirror',
      order:4,
    },
    {
      name:'psc.processCoreAssign',
      enname:'Core Affinity',
      choosen:false,
      route:'/GameMirror',
      download:{installed:false,installing:false,progress:0,isLatest:true},
      star:true,
      icon:'iconfont icon-jinchengfenpei-1-copy',
      Inleft:false,
      module:true,
      windowName:'processCoreAssign',
      order:5,
    },
    {
      name:'home.addOne',
      enname:'Benefits',
      choosen:false,
      route:'/GameMirror',
      download:{installed:false,installing:false,progress:0,isLatest:true},
      star:true,
      icon:'iconfont icon-icon_gift',
      Inleft:false,
      module:true,
      windowName:'FreeGame',
      order:6,
    },
    {
      name:'home.videoRecording',
      enname:'Video recording',
      choosen:false,
      route:'/GameMirror',
      download:{installed:false,installing:false,progress:0,isLatest:true},
      star:true,
      icon:'iconfont icon-icon_video',
      Inleft:false,
      module:true,
      windowName:'videoRecord',
      order:7,
    },
    {
      name:'home.2Ai',
      enname:'2ha.ai',
      choosen:false,
      route:'/GameMirror',
      download:{installed:false,installing:false,progress:0,isLatest:true},
      star:true,
      icon:'',
      img: erhaImg,
      Inleft:false,
      module:true,
      windowName:'ai_lab',
      order:8,
    },
    {
      name:'shutdownTimer.timedShutdown',
      enname:'Timed shutdown',
      choosen:false,
      route:'/GameMirror',
      download:{installed:false,installing:false,progress:0,isLatest:true},
      star:true,
      icon:'iconfont icon-icon_timed1',
      Inleft:false,
      module:true,
      windowName:'timedshutdown',
      order:9,
    },
    {
      name:'screenshotpage.screenshot',
      enname:'Stowage plan',
      choosen:false,
      route:'/GameMirror',
      download:{installed:false,installing:false,progress:0,isLatest:true},
      star:true,
      icon:'iconfont icon-icon_screenshot',
      Inleft:false,
      module:true,
      windowName:'screenshot',
      order:10,
    },
    {
      name:'DesktopMonitoring.desktopMonitoring',
      enname:'Stowage plan',
      choosen:false,
      route:'/GameMirror',
      download:{installed:false,installing:false,progress:0,isLatest:true},
      star:true,
      icon:'iconfont icon-icon_deskmonitor',
      Inleft:false,
      module:true,
      windowName:'desktop_monitor_setting',
      order:11,
    },
    {
      name:'压力测试',
      enname:'Stowage plan',
      choosen:false,
      route:'/GameMirror',
      download:{installed:false,installing:false,progress:0,isLatest:true},
      star:true,
      icon:'iconfont icon-icon_deskmonitor',
      Inleft:false,
      module:true,
      windowName:'pressureTest',
      order:12,
    },
  ])
  return { tabInfo,activeTab }
},{persist:false}
)

export const hardware = defineStore('hardware', () =>{
  let setTimeout1Count = 0
  // 用户硬件信息
  // ------------------------------------------------------------- state-------------------------------------------------

  // 硬件数据 gamepp.hardware.getBaseJsonInfo()获取
  let HwInfo = reactive<any>({});

  /**
   * 传感器的原始数据 gamepp.hardware.getSensorInfo()
   * 使用原始传感器数据需要获取手动更新
   * 使用原始数据示例
   * let store = hardware()
   * setInterval(async ()=>{
   *   await store.getSensorInfo()
   * },store.refreshTime)
   */
  let HwSensorData = reactive<any>({});

  let gpuDefault:bg_sensor_data_gpu = {
    clock: null,
    d3d_usage: null,
    fan: null,
    hot_spot_temp:null,
    mem_clock: null,
    mem_size: null,
    mem_temp: null,
    mem_usage: null,
    mem_usage_mb: null,
    name: null,
    pl_max_operating_voltage: null,
    pl_reliability_voltage: null,
    pl_thermal: null,
    power: null,
    power_list: [],
    shadres: null,
    temp: null,
    thermal_hotspot: null,
    thermal_memory: null,
    total_usage: null,
    usage_str: null,
    videobus: null,
    voltage: null,
  }
  let defaultObj:bg_sensor_data = {
    cpu:{
      amd_thermal: null,
      clock: null,
      clockE: null,
      clockP: null,
      core_info: {
        Clock: [],
        Load: [],
        Temp: [],
      },
      effective_clock: {},
      fan: null,
      limit: [],
      name: null,
      npu_clock: null,
      npu_usage: null,
      power: null,
      temp: null,
      tempE: null,
      tempP: null,
      thread: [],
      usage: null,
      usageE: null,
      usageP: null,
      voltage: null,
      voltageE: null,
      voltageP: null,
    },
    drive:{
      sys_temp: null,
      temp: null,
      temp_list: [],
      temp_list_all: [],
      usage: null,
      usage_list: [],
    },
    gpu: {...gpuDefault},
    gpu_list: [{...gpuDefault},{...gpuDefault},{...gpuDefault}],
    mainboard: {
      temp: null,
      voltage: null,
    },
    memory: {
      channel: null,
      clock: null,
      name: null,
      size: null,
      tcas: null,
      temp: null,
      tras: null,
      trcd: null,
      trp: null,
      usage: null,
      usage_mb: null,
      voltage: null,
    },
    network: {
      download: null,
      upload: null,
    },
    whea: null,
  }
  // background处理后放在localStorage的传感器数据，不需要手动刷新，自动读local更新
  let bg_sensor_data =  reactive<bg_sensor_data>({...defaultObj});
  let refreshTime = ref(1000);
  // -----------------------------------------------------------end state-------------------------------------------------

  // ------------------------------------------------------------- getter-------------------------------------------------

  // 扫描时间
  let scanTime = computed(()=> {
    try {
      const ScanTime = HwInfo.COMPUTER.ScanTime.split(' ');
      const Years = ScanTime[0].split('.');
      const ScanTimeData = Years[2] + '-' + Years[1] + '-' + Years[0];
      return ScanTimeData + '\xa0\xa0' + ScanTime[1]
    } catch {
      return ''
    }
  })


  // -----------------------------------------------------------end getter-------------------------------------------------

  // 引入store时获取一遍传感器数据和硬件数据、并监听bg_sensor_data的改变
  setTimeout(()=>{
    getBaseHardwareInfo();
    getBgSensorData();
    listenBgSensorDataChange();
  },50)
  gamepp.setting.onConfigChanged.addEventListener((type, id:number, value) => {
    if (id == 450) {
      refreshTime.value = value;
    }
  })
  try {
    refreshTime.value = gamepp.setting.getInteger.sync(450)
  }catch (e) {
    refreshTime.value = 1000
  }

  // ------------------------------------------------------------ action --------------------------------------------------
  async function getBaseHardwareInfo() {
    try {
      let LocalHwInfoJsonStr = window.localStorage.getItem('LocalHwInfoJsonStr');
      if (LocalHwInfoJsonStr && LocalHwInfoJsonStr !== 'null') {
        let InitHwInfoJson = JSON.parse(LocalHwInfoJsonStr)
        Object.assign(HwInfo,InitHwInfoJson) // 先显示本地存的硬件信息
        let HWInfoJSONState = 1
        try {HWInfoJSONState = await gamepp.hardware.getJSONState.promise();} catch {}
        if (HWInfoJSONState === 1) {
          try {
            gamepp.hardware.getBaseJsonInfo.promise().then(async (RealTime_HwInfoJsonStr:any)=>{
              if (RealTime_HwInfoJsonStr) {
                let RealTime_HwInfoJson = JSON.parse(RealTime_HwInfoJsonStr)
                let LocalInfo = JSON.stringify(InitHwInfoJson.COMPUTER.ComputerName) + JSON.stringify(InitHwInfoJson.COMPUTER.OperatingSystem) + JSON.stringify(InitHwInfoJson.CPU.SubNode[0]['ProcessorName']) + JSON.stringify(InitHwInfoJson.MOBO) + JSON.stringify(InitHwInfoJson.GPU.SubNode[0]['VideoCard']) + JSON.stringify(InitHwInfoJson.MEMORY.SubNode) + JSON.stringify(InitHwInfoJson.MONITOR.SubNode);
                let NewInfo = JSON.stringify(RealTime_HwInfoJson.COMPUTER.ComputerName) + JSON.stringify(RealTime_HwInfoJson.COMPUTER.OperatingSystem) + JSON.stringify(RealTime_HwInfoJson.CPU.SubNode[0]['ProcessorName']) + JSON.stringify(RealTime_HwInfoJson.MOBO) + JSON.stringify(RealTime_HwInfoJson.GPU.SubNode[0]['VideoCard']) + JSON.stringify(RealTime_HwInfoJson.MEMORY.SubNode) + JSON.stringify(RealTime_HwInfoJson.MONITOR.SubNode);
                let LocalHash = await gamepp.getMd5.promise(LocalInfo);
                let NewHash = await gamepp.getMd5.promise(NewInfo);
                console.log('本地Hash', LocalHash, '新Hash', NewHash)

                let GPULength = InitHwInfoJson.GPU.SubNode.length === RealTime_HwInfoJson.GPU.SubNode.length;
                let DrivesLength = InitHwInfoJson.DRIVES.SubNode.length === RealTime_HwInfoJson.DRIVES.SubNode.length;
                let NetworkLength = InitHwInfoJson.NETWORK.SubNode.length === RealTime_HwInfoJson.NETWORK.SubNode.length;
                let MonitorLength = InitHwInfoJson.MONITOR.SubNode.length === RealTime_HwInfoJson.MONITOR.SubNode.length;

                console.log(GPULength, DrivesLength, NetworkLength, MonitorLength)

                if (LocalHash !== NewHash || !GPULength || !DrivesLength || !NetworkLength || !MonitorLength) {
                  // console.log(LocalInfo)
                  // console.log(NewInfo)
                  console.log('%c硬件信息发生变化', 'color: red;');
                  // 硬件信息发生变化
                  Object.assign(HwInfo,RealTime_HwInfoJson)
                  const hw = new BroadcastChannel('hw')
                  hw.postMessage({action:'change'})
                } else {
                  Object.assign(HwInfo,InitHwInfoJson)
                  console.log('%c硬件信息未发生变化', 'color: green;');
                  const hw = new BroadcastChannel('hw')
                  hw.postMessage({action:'change'})
                }

                window.localStorage.setItem('LocalHwInfoJsonStr', RealTime_HwInfoJsonStr);
              }
            })
          }catch(e){

          }
        }else{
          setTimeout1Count++;
          if (setTimeout1Count <= 50) {
            setTimeout(() => {
              setTimeout1Count++
              getBaseHardwareInfo()
            }, 1000)
          }
        }
      } else {
        let HWInfoJSONState = 1
        try {HWInfoJSONState = await gamepp.hardware.getJSONState.promise();} catch {}
        if (HWInfoJSONState === 1) {
          try {
            gamepp.hardware.getBaseJsonInfo.promise().then((jsonInfo:any)=>{
              if (jsonInfo) {
                Object.assign(HwInfo,JSON.parse(jsonInfo))
                window.localStorage.setItem('LocalHwInfoJsonStr', jsonInfo);
              }
            })
          }catch(e){

          }
        }else{
          setTimeout1Count++;
          if (setTimeout1Count <= 50) {
            setTimeout(() => {
              setTimeout1Count++
              getBaseHardwareInfo()
            }, 1000)
          }
        }
      }
    }catch (e) {
      console.warn(e)
      setTimeout1Count++;
      if (setTimeout1Count <= 50) {
        setTimeout(() => {
          setTimeout1Count++
          getBaseHardwareInfo()
        }, 1000)
      }
    }
  }
  function getBgSensorData() {
    let data:any = window.localStorage.getItem('bg_sensor_data')
    if (data) {
      data = JSON.parse(data)
      Object.assign(bg_sensor_data,data)
    }else{
      Object.assign(bg_sensor_data,defaultObj)
    }
  }
  function listenBgSensorDataChange() {
    // @ts-ignore
    if (window.bgStorageListener) return
    // @ts-ignore
    window.bgStorageListener = true
    const bc = new BroadcastChannel('bg_sensor_data');
    bc.onmessage = (event) => {
      const bgData = event.data
      Object.assign(bg_sensor_data, bgData)
    }
  }
  async function getSensorInfo(): Promise<any> {
    try {
      let tempData = JSON.parse(await gamepp.hardware.getSensorInfo.promise())
      if (Object.keys(tempData).length > 1){
        Object.assign(HwSensorData, tempData)
      }
      return HwSensorData
    }catch (e) {
      return HwSensorData
    }
  }
  // -----------------------------------------------------------end action-------------------------------------------------

  return {
    originData: HwInfo,
    HwInfo: HwInfo,
    sensorInfo: HwSensorData,
    HwSensorData: HwSensorData,
    bg_sensor_data,
    scanTime,
    refreshTime,
    getBaseHardwareInfo,
    getBgSensorData,
    listenBgSensorDataChange,
    getSensorInfo
  }
})

export const HotKey = defineStore('HotKey', () => {
  const HotKey = ref(
    {

    }
  )
})

export const getInGameList = ()=>{
  const storedList = localStorage.getItem('InGameList1');
  if(!storedList  || storedList === 'null' || storedList === ''  || storedList === '[]' ){
    const defaultList = JSON.parse(JSON.stringify(defaultInGameList()));
    localStorage.setItem('InGameList1', JSON.stringify(defaultList));
    return defaultList;
  }
  return JSON.parse(storedList);
}

export function saveInGameList(list: any) {
  localStorage.setItem('InGameList1', JSON.stringify(list));
  gamepp.webapp.sendInternalAppEvent.promise('desktop', 'vkDeviceUpdate');
}

export const defaultInGameList = ()=> {
  return [
      [
        {name: 'FPS',des: 'FPS', show: true, color:'rgba(56, 255, 152, 1)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: false,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: false, title: 'FPS', unit:'',textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgba(56, 255, 152, 1)',notFound: false},
        {name: 'FPS 1% Low',des: 'InGameMonitor.onePercentLowFrame',bg: '', show: true, color:'rgba(56, 255, 152, 1)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: false,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: false,title: 'FPS 1% Low', unit:'',textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)',notFound: false},
        {name: 'FPS 0.1% Low',des: 'InGameMonitor.pointOnePercentLowFrame',bg: 'FPS 0.1% Low', show: false, color:'rgba(56, 255, 152, 1)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: false,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: true,title: 'FPS 0.1% Low', unit:'',textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)',notFound: false},
        {name: 'Frame Time',des: 'InGameMonitor.frameGenerationTime',bg: 'Frame Time', show: true, color:'rgba(56, 255, 152, 1)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: false,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: false,title: '', unit:'',textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)',notFound: false},
        {name: 'Current time',des: 'InGameMonitor.curTime',bg: 'Current time', show: false, color:'rgb(255,255,255)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: false,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: true,title: '', unit:'',textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)',notFound: false},
        {name: 'Run time',des: 'InGameMonitor.runTime',bg: 'Run time', show: false, color:'rgb(255,255,255)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: false,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: true,title: '', unit:'',textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)',notFound: false},
        {name: 'CPU Temp',des: 'InGameMonitor.cpuTemp', bg: 'temp', show: true, color:'rgba(87, 172, 255, 1)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: false,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: false, title: 'CPU', unit:'℃' , key: 0,textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)',notFound: false},
        {name: 'CPU Usage',des: 'InGameMonitor.cpuUsage',bg: 'usage', show: true, color:'rgba(87, 172, 255, 1)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: false,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: false, title: 'CPU', unit:'%',key: 6,textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)',notFound: false},
        {name: 'CPU Clock',des: 'InGameMonitor.cpuFreq',bg: 'clock', show: true, color:'rgba(87, 172, 255, 1)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: false,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: false, title: 'CPU', unit:'MHz',key: 3,textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)',notFound: false},
        {name: 'CPU Power',des: 'InGameMonitor.cpuPower',bg: 'power', show: false, color:'rgba(87, 172, 255, 1)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: false,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: true, title: 'CPU', unit:'W',key: 12,textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)',notFound: false},
        {name: 'GPU Temp',des: 'InGameMonitor.gpuTemp',bg: 'temp', show: true, color:'rgba(255, 177, 60, 1)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: false,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: false, title: 'GPU',unit:'℃', key: 0,textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)',notFound: false},
        {name: 'GPU Usage',des: 'InGameMonitor.gpuUsage',bg: 'total_usage', show: true, color:'rgba(255, 177, 60, 1)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: false,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: false, title: 'GPU', unit:'%', key: 4,textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)',notFound: false},
        {name: 'GPU Power',des: 'InGameMonitor.gpuPower',bg: 'power', show: false, color:'rgba(255, 177, 60, 1)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: false,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: true, title: 'GPU', unit:'MHz', key: 2,textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)',notFound: false},
        {name: 'GPU Clock',des: 'InGameMonitor.gpuFreq',bg: 'clock', show: false, color:'rgba(255, 177, 60, 1)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: false,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: true, title: 'GPU', unit:'W', key: 1,textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)',notFound: false},
        {name: 'Physical Memory Load',des: 'InGameMonitor.memUsage',bg: 'usage', show: true, color:'rgba(255, 177, 60, 1)', hovering: false, group: false,active: false,methodWay: 0,fontSize: 'Microsoft YaHei',fontStyle: 12, fontBold: false,outIndex:0,innerIndex:0, origin: true, top:false, lock: false,lineColor: 'rgb(255,255,255)',lineHeight:30,lineSize:2, showOnlyHide: false, title: 'memory', unit:'%', key: 1,textShadow:false,desshow: true,TitlefontStyle:12,titlecolor:'rgb(255,255,255)',notFound: false}
      ]
  ];
}
