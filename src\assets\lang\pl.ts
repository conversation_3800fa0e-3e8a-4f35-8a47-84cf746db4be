const pl = {
  "update": {
    "Game": "Game",
    "PP": "PP",
    "upgradingInProgress": "Trwa aktualizacja",
    "theModuleIsBeingUpdated": "Aktualizowanie modułu",
    "dataIsBeingUpdated": "Aktualizowanie danych...",
    "checkingUpdate": "Sprawdzanie aktualizacji",
    "checkingUpgrade": "Sprawdzanie aktualizacji",
    "loadingProgramComponent": "Wczytywanie komponentów programu...",
    "loadingHotkeyModules": "Ładowanie komponentu skrótów klawiaturowych",
    "loadingGPPModules": "Ładuje się komponenty GamePP",
    "loadingBlackWhiteList": "Lista czarna/biała jest ładowana",
    "loadingGameSetting": "Ładowanie parametrów ustawień gry...",
    "loadingUserAbout": "Wczytywanie danych uwierzytelniania użytkownika",
    "loadingGameBenchmark": "Ładowanie wyniku gry",
    "loadingHardwareInfo": "Ładowanie komponentu informacji o sprzęcie",
    "loadingDBModules": "Wczytywanie modułu bazy danych...",
    "loadingIGCModules": "Ładowanie modułu IGC",
    "loadingFTPModules": "Ładowanie modułu wsparcia FTP w toku",
    "loadingDialogModules": "Ładowanie modułu okna dialogowego",
    "loadingDataStatisticsModules": "Trwa ładowanie modułu statystyk",
    "loadingSysModules": "Wczytywanie komponentów systemowych w toku",
    "loadingGameOptimization": "Trwa ładowanie optymalizacji gry",
    "loadingGameAcceleration": "Ładowanie przyspieszenia gry",
    "loadingScreenshot": "Ładowanie zrzutu ekranu z nagrania",
    "loadingVideoComponent": "Ładowanie komponentu kompresji wideo",
    "loadingFileFix": "Ładowanie naprawy pliku",
    "loadingGameAI": "Ładowanie jakości AI gry",
    "loadingNVAPIModules": "Ładowanie modułu NVAPI",
    "loadingAMDADLModules": "Ładowanie modułu AMDADL w toku",
    "loadingModules": "Trwa ładowanie modułu"
  },
  "messages": {
    "append": "Dodaj",
    "confirm": "Potwierdź",
    "cancel": "Anuluj",
    "default": "Domyślne",
    "quickSelect": "Szybkie Wybory",
    "onoffingame": "Włącz/Wyłącz monitorowanie w grze:",
    "changeKey": "Kliknij, aby zmienić skrót klawiaturowy",
    "clear": "Wyczyść",
    "hotkeyOccupied": "Skrót klawiaturowy jest już w użyciu. Proszę ustawić nowy！",
    "minimize": "Minimalizuj",
    "exit": "Zamknij",
    "export": "Eksportuj",
    "import": "Import",
    "screenshot": "Zrzut ekranu",
    "showHideWindow": "Pokaż/Ukryj okno",
    "ingameControlPanel": "Pasek sterowania w grze",
    "openOrCloseGameInSettings": "Przełącz panel ustawień w grze",
    "openOrCloseGameInSettings2": "Naciśnij ten skrót klawiaturowy, aby włączyć",
    "openOrCloseGameInSettings3": "Włącz/Wyłącz monitorowanie w grze",
    "openOrCloseGameInSettings4": "Włącz/Wyłącz filtr gry",
    "startManualRecord": "Rozpocznij/Zakończ ręczne rejestrowanie statystyk",
    "performanceStatisticsMark": "Marker statystyk wydajności",
    "EnableAIfilter": "Filtr AI wymaga naciśnięcia tej skrótu klawiaturowego, aby go aktywować",
    "Start_stop": "Uruchom/Zatrzymaj ręczne rejestrowanie statystyk",
    "pressureTest": "Test stresu",
    "moduleNotInstalled": "Moduł funkcjonalny nie jest zainstalowany",
    "installingPressureTest": "Instalacja modułu testu obciążeniowego...",
    "importFailed": "Import nie powiódł się",
    "gamepp": "GamePP",
    "copyToClipboard": "Skopiowano do schowka"
  },
  "home": {
    "homeTitle": "Powrót",
    "hardwareInfo": "Informacje o sprzęcie",
    "functionIntroduction": "Funkcje",
    "fixedToNav": "Przypnij do paska nawigacyjnego",
    "cancelFixedToNav": "Odłącz z paska nawigacyjnego",
    "hardwareInfoLoading": "Wczytywanie informacji o sprzęcie...",
    "performanceStatistics": "Statystyki wydajności",
    "updateNow": "Zaktualizuj teraz",
    "recentRun": "Ostatnia aktywność",
    "resolution": "Rozdzielczość:",
    "duration": "Czas trwania:",
    "gameFilter": "Filtr gier",
    "gameFilterHasAccompany": "Filtr gier jest aktywny",
    "gameFilterHasAccompany2": "Użytkownicy grają w gry takie jak Cyberpunk, APEX i Hogwarts Legacy.",
    "currentList": "Elementy monitorowane na bieżącej liście",
    "moreFunction": "Benchmark, test stresowy, monitorowanie pulpitu i inne funkcje są w trakcie rozwoju.",
    "newVersion": "Nowa wersja dostępna !",
    "discoverUpdate": "Znaleziono aktualizację！",
    "downloading": "Pobieranie",
    "retry": "Spróbuj ponownie",
    "erhaAI": "2HaAI",
    "recordingmodule": "Ta funkcja zależy od modułu nagrywania",
    "superPower": "Tryb Ultra",
    "autoRecord": "Automatycznie nagrywaj momenty zabijania w grze i łatwo zapisuj kliki najważniejszych momentów",
    "externalDevice": "Dynamiczne oświetlenie periferyjne",
    "linkage": "Wyzwalaj sceny zabójstw w grach i wyświetlaj je za pomocą podłączonych urządzeń peryferyjnych",
    "AI": "Test wydajności AI",
    "test": "Testuj modele AI przy użyciu GPU i wyświetl wynik wydajności AI GPU",
    "supportedGames": "Obsługiwane gry",
    "games": "Naraka: Bladepoint, Delta Force, FragPunk, League of Legends, PUBG, Valorant, The Finals, APEX Legends, COD20",
    "videoRecording": "Nagrywanie wideo",
    "videoRecording2": "Funkcja nagrywania wideo oparta na OBS, umożliwia dostosowanie bitrate i częstotliwości klatek (FPS), aby spełnić różne wymagania jakościowe i płynności; obsługuje również \"Bezpośredni Odtwórz\", naciśnij klawisz skrótu, aby w dowolnym momencie zapisywać najważniejsze momenty!",
    "addOne": "Pobierz za darmo",
    "gamePlatform": "Platforma gry",
    "goShop": "Przejdź do strony sklepu",
    "receiveDeadline": "Końcowy termin zgłoszenia po akcji",
    "2Ai": "2 Śmiech AI",
    "questionDesc": "Opis problemu",
    "inputYourQuestion": "Proszę wpisać tutaj swoje sugestie lub komentarze。",
    "uploadLimit": "Prześlij maksymalnie 3 lokalne obrazy w formacie JPG/PNG/BMP",
    "email": "E-mail",
    "contactWay": "Dane kontaktowe",
    "qqNumber": "Numer QQ (opcjonalnie)",
    "submit": "Zatwierdź"
  },
  "hardwareInfo": {
    "hardwareOverview": "Przegląd sprzętu",
    "copyAllHardwareInfo": "Skopiuj wszystkie informacje o sprzęcie",
    "processor": "Procesor",
    "coreCount": "Jądra :",
    "threadCount": "Liczba wątków:",
    "currentFrequency": "Obecna częstotliwość:",
    "currentVoltage": "Obecne napięcie:",
    "copy": "Kopiuj",
    "releaseDate": "Data wydania",
    "codeName": "Kod imienia",
    "thermalDesignPower": "Moc cieplna projektowana",
    "maxTemperature": "Maksymalna temperatura",
    "graphicsCard": "Karta graficzna",
    "brand": "Marka:",
    "streamProcessors": "Procesor strumieni:",
    "Videomemory": "Pamięć wideo：",
    "busSpeed": "Prędkość magistrali",
    "driverInfo": "Informacje o sterowniku",
    "driverInstallDate": "Data instalacji sterownika",
    "hardwareID": "ID sprzętu",
    "motherboard": "Płyta główna",
    "chipGroup": "Chipset:",
    "BIOSDate": "Data BIOS-u",
    "BIOSVersion": "Wersja BIOS",
    "PCIESlots": "slot PCIe",
    "PCIEVersion": "Wspierana wersja PCIe",
    "memory": "Pamięć",
    "memoryBarCount": "Ilość:",
    "totalSize": "Rozmiar:",
    "channelCount": "Kanał:",
    "Specificmodel": "Specyficzny model",
    "Pellet": "Generator cząstek",
    "memoryBarEquivalentFrequency": "Efektywna częstotliwość pamięci:",
    "hardDisk": "Dysk twardy",
    "hardDiskCount": "Liczba dysków twardych:",
    "actualCapacity": "Pojemność rzeczywista",
    "type": "Typ",
    "powerOnTime": "Czas włączania",
    "powerOnCount": "Cykle mocy",
    "SSDRemainingLife": "Pozostały czas życia SSD",
    "partitionInfo": "Informacje o partycji",
    "hardDiskController": "Kontroler dysku twardego",
    "driverNumber": "Numer dysku",
    "display": "Dyskryminacja",
    "refreshRate": "Szybkość odświeżania:",
    "screenSize": "Rozmiar ekranu:",
    "inches": "calka",
    "productionDate": "Data produkcji",
    "supportRefreshRate": "Obsługa częstotliwości odświeżania",
    "screenLongAndShort": "Wymiary ekranu",
    "systemInfo": "Informacje systemowe",
    "version": "Wersja",
    "systemInstallDate": "Data instalacji systemu",
    "systemBootTime": "Obecny czas uruchomienia",
    "systemRunTime": "Czas wykonywania",
    "Poccupied": "Użycie P",
    "Eoccupied": "E jest zajęte",
    "occupied": "Zajęte",
    "temperature": "Temperatura",
    "Pfrequency": "Częstotliwość procesora",
    "Efrequency": "Częstotliwość E",
    "thermalPower": "Energia cieplna",
    "frequency": "Częstotliwość",
    "current": "Obecny",
    "noData": "Brak danych",
    "loadHwinfo_SDK": "Nie można załadować Hwinfo_SDK.dll, nie można odczytać danych sprzętu/z czujników.",
    "loadHwinfo_SDK_reason": "Możliwe przyczyny tego problemu:",
    "reason": "Powód",
    "BlockIntercept": "Zablokowane przez oprogramowanie antywirusowe, np.: 2345 Antivirus Software (Proces 2345 Active Defense, Proces Active Defense McAfee)",
    "solution": "Rozwiązanie:",
    "solution1": "Po zamknięciu i odinstalowaniu powiązanych procesów, uruchom ponownie GamePP",
    "solution2": "Po odłączeniu skojarzonych urządzeń uruchom ponownie GamePP",
    "RestartGamePP": "Odłącz kontroler, poczekaj na odpowiedź urządzenia, a następnie uruchom ponownie GamePP",
    "HWINFOcannotrun": "Hwinfo nie może działać poprawnie",
    "downloadHWINFO": "Pobierz Hwinfo",
    "openHWINFO": "Po uruchomieniu Hwinfo, czy kliknięcie w RUN uruchomi program poprawnie?",
    "hardwareDriverProblem": "Problemy z sterownikami sprzętu",
    "checkHardwareManager": "Sprawdź menedżera sprzętu, aby upewnić się, że sterowniki płyty głównej i karty graficznej są poprawnie zainstalowane",
    "systemProblem": "Problemy systemowe, np.: Użycie narzędzi aktywacyjnych takich jak Baofeng lub Xiaoma może powodować niepowodzenie ładowania sterowników, a poprawki systemowe Windows 7 nie mogą być automatycznie zainstalowane",
    "reinstallSystem": "Aby aktywować, ponownie zainstaluj system. Pobierz i zainstaluj Windowsa 7: poprawka *********",
    "Windows7": "Windows 7: Zainstaluj poprawkę SHA-256, Windows 10: Aktywuj za pomocą certyfikatu cyfrowego, Windows 11 Wersja wersji zapoznawczej: Deaktywuj Integrity Pamięci",
    "ViolenceActivator": "Jeśli użyłeś narzędzi aktywacji siłą (np. Xiaoma), napraw lub zainstaluj ponownie system",
    "MultipleGraphicsCardDrivers": "Na komputerze zainstalowane są sterowniki kart graficznych różnych marek, np. sterowniki AMD i Nvidia zainstalowane jednocześnie.",
    "UninstallUnused": "Uruchom ponownie komputer po odinstalowaniu niepotrzebnych sterowników graficznych",
    "OfficialQgroup": "Żaden z powyższych powodów nie dotyczy. Dołącz do naszej oficjalnej grupy QQ: 908287288 (Grupa 5), aby uzyskać rozwiązanie.",
    "ExportHardwareData": "Eksportuj dane sprzętu",
    "D3D": "Użycie D3D",
    "Total": "Całkowite użycie",
    "VRAM": "Użycie VRAM",
    "VRAMFrequency": "Częstotliwość VRAM",
    "SensorData": "Dane z czujnika",
    "CannotGetSensorData": "Nie można pobrać danych z czujnika",
    "LoadingHardwareInfo": "Ładowanie informacji o sprzęcie…",
    "ScanTime": "Ostatni skan:",
    "Rescan": "Przeskanuj ponownie",
    "Screenshot": "Zrzut ekranu",
    "configCopyed": "Informacje konfiguracyjne zostały skopiowane do schowka.",
    "LegalRisks": "Wykryto potencjalne ryzyko prawne",
    "brandLegalRisks": "Wyświetlanie marki może wiązać się z potencjalnymi ryzykami prawno-legalnymi",
    "professionalVersion": "Wersja Profesjonalna",
    "professionalWorkstationVersion": "Wersja Workstation Profesjonalna",
    "familyEdition": "Wersja Home",
    "educationEdition": "Wersja Edukacyjna",
    "enterpriseEdition": "Wersja Enterprise",
    "flagshipEdition": "Edycja Premium",
    "familyPremiumEdition": "Wersja Premium Rodzinna",
    "familyStandardEdition": "Wersja Standardowa Rodzinna",
    "primaryVersion": "Podstawowa wersja",
    "bit": "bit",
    "tempWall": "Ściana temperatury",
    "error": "Błąd",
    "screenshotSuccess": "Zrzut ekranu został pomyślnie zapisany",
    "atLeastOneData": "Trzeba zachować przynajmniej 1 wpis danych",
    "atMostSixData": "Dodaj maksymalnie 6 wpisów danych",
    "screenNotActivated": "Nie aktywowany"
  },
  "psc": {
    "processCoreAssign": "Przydzielanie rdzenia procesu",
    "CoreAssign": "Alokacja rdzenia：",
    "groupName": "Nazwa grupy:",
    "notGameProcess": "Procesy niegier",
    "unNamedProcess": "Grupa bez nazwy",
    "Group2": "Grupa",
    "selectTheCore": "Wybierz jądro",
    "controls": "Operacja",
    "tips": "Wskazówka",
    "search": "Wyszukaj",
    "shiftOut": "Powrót",
    "ppValue": "Wartość PP",
    "ppDesc": "Wartość PP odzwierciedla historyczne zużycie zasobów sprzętowych. Im wyższa wartość, tym większe zużycie zasobów.",
    "littletips": "Wskazówka: Trzymaj i przeciągnij proces do grupy po lewej stronie。",
    "warning1": "Wybór wątków jądra między grupami może wpłynąć na wydajność. Zaleca się użycie jąder z tej samej grupy.",
    "warning2": "Czy na pewno chcesz pozostawić nazwę grupy pustą?",
    "warning3": "Efekt przydzielenia jądra zostanie wycofany po usunięciu. Czy na pewno chcesz usunąć tę grupę?",
    "allprocess": "Wszystkie procesy",
    "pleaseCheckProcess": "Proszę zaznaczyć proces",
    "dataSaveDesktop": "Dane zostały zapisane na pulpicie.",
    "createAGroup": "Utwórz grupę",
    "delGroup": "Usuń grupę",
    "Group": "Grupa",
    "editGroup": "Edytuj grupę",
    "groupinfo": "Informacje o grupie",
    "moveOutGrouping": "Usuń z grupy",
    "createANewGroup": "Utwórz nową grupę",
    "unallocatedCore": "Nierozdzielony rdzeń",
    "inactiveProcess": "Nieaktywny proces",
    "importGroupingScheme": "Importuj profil grupowania",
    "derivedPacketScheme": "Eksportuj konfigurację grupy",
    "addNowProcess": "Dodaj aktualnie uruchomiony proces",
    "displaySystemProcess": "Pokaż procesy systemowe",
    "max64": "Maksymalna liczba wyborów to 64 wątki",
    "processName": "Nazwa procesu",
    "chooseCurProcess": "Wybierz bieżący proces",
    "selectNoProcess": "Nie wybrano żadnego procesu",
    "coreCount": "Jądra",
    "threadCount": "Wątki",
    "process": "Proces",
    "plzInputProcessName": "Wprowadź nazwę procesu, aby dodać ją ręcznie",
    "has_allocation": "Procesy z schematami alokacji wątków",
    "not_made": "Nie przypisano żadnych procesów do jąder",
    "startUse": "Włącz optymalizację",
    "stopUse": "Wyłącz optymalizację",
    "threadAllocation": "Przydzielenie wątku",
    "configProcess": "Konfiguracja procesu",
    "selectThread": "Wybierz wątek",
    "hyperthreadingState": "Stan Hyper-Threadingu",
    "open": "Włączony",
    "notYetUnlocked": "Wyłączony",
    "nonhyperthreading": "Brak Hyper-Threading",
    "intervalSelection": "Wybór interwału",
    "invertSelection": "Odwróć zaznaczenie",
    "description": "Zablokuj procesy gry na wyznaczonych rdzeniach CPU, inteligentnie izolując zakłócenia z programów w tle. Efektywnie zwiększa sufit FPS i stabilizuje wydajność gry! Zmniejsza nagłe opóźnienia i spadki FPS, w pełni wykorzystując potencjał wielordzeniowych procesorów, aby zapewnić wysoką i stabilną ilość klatek podczas całej rozgrywki!",
    "importSuccess": "Pomyślnie zaimportowano",
    "importFailed": "Niepowodzenie importu"
  },
  "InGameMonitor": {
    "onoffingame": "Włącz/Wyłącz monitorowanie w grze:",
    "InGameMonitor": "Monitorowanie w grze",
    "CustomMode": "Tryb niestandardowy",
    "Developing": "W trakcie rozwoju...",
    "NewMonitor": "Dodaj element monitorowania",
    "Data": "Parametry",
    "Des": "Notatka",
    "Function": "Funkcja",
    "Editor": "Edytuj",
    "Top": "Przypnij do góry",
    "Delete": "Usuń",
    "Use": "Używać",
    "DragToSet": "Po wywołaniu panelu można przeciągnąć, aby ustawić",
    "MonitorItem": "Element monitorowania",
    "addMonitorItem": "Dodaj element monitorowania",
    "hide": "Ukryj",
    "show": "Wyświetl",
    "generalstyle": "Ustawienia ogólne",
    "restoredefault": "Przywróć ustawienia domyślne",
    "arrangement": "Układ",
    "horizontal": "Poziomo",
    "vertical": "Pionowy",
    "monitorposition": "Lokalizacja monitorowania",
    "canquickselectposition": "Szybko wybierz lokalizację na lewej mapie。",
    "curposition": "Bieżące położenie:",
    "background": "Tło",
    "backgroundcolor": "Kolor tła:",
    "font": "Czcionka",
    "fontStyle": "Styl czcionki",
    "fontsize": "Rozmiar czcionki：",
    "fontcolor": "Kolor czcionki:",
    "style": "Styl:",
    "style2": "Styl",
    "performance": "Wydajność",
    "refreshTime": "Czas odświeżania:",
    "goGeneralSetting": "Przejdź do ustawień ogólnych",
    "selectMonitorItem": "Wybierz element monitorowania",
    "selectedSensor": "Wybrany czujnik:",
    "showTitle": "Pokaż tytuł",
    "hideTitle": "Ukryj tytuł",
    "showStyle": "Tryb wyświetlania:",
    "remarkSize": "Rozmiar notatki:",
    "remarkColor": "Kolor notatki:",
    "parameterSize": "Rozmiar parametru :",
    "parameterColor": "Kolor parametru :",
    "lineChart": "Wykres liniowy",
    "lineColor": "Kolor linii:",
    "lineThickness": "Grubość linii：",
    "areaHeight": "Wysokość regionu:",
    "sort": "Sortuj",
    "displacement": "Przesunięcie:",
    "up": "Przesuń w górę",
    "down": "Przesuń w dół",
    "bold": "Pogrubienie",
    "stroke": "Kontur",
    "text": "Tekst",
    "textLine": "Tekst + Wykres liniowy",
    "custom": "Niestandardowy",
    "upperLeft": "Góra lewo",
    "upper": "Środkowy-Górny",
    "upperRight": "Góra, prawo",
    "Left": "Lewe Centrum",
    "middle": "Centrum",
    "Right": "Prawy środek",
    "lowerLeft": "W lewym dolnym rogu",
    "lower": "Średni-Niski",
    "lowerRight": "Dolny prawy",
    "notSupport": "Urządzenia zewnętrzne nie obsługują wyświetlania/ukrywania za pomocą kliknięcia myszy",
    "notSupportRate": "Nie można przełączać stopy zwrotu kliknięciem",
    "notFindSensor": "Nie znaleziono czujnika. Kliknij, aby zmodyfikować。",
    "monitoring": "Monitorowanie",
    "condition": "Warunek",
    "bigger": "Większy niż",
    "smaller": "Mniejszy niż",
    "biggerThan": "Przekroczono próg",
    "biggerThanthreshold": "Większy niż procent progu",
    "smallerThan": "Poniżej progu",
    "smallerThanthreshold": "Mniej niż procent progowy",
    "biggerPercent": "Procentowe zmniejszenie bieżącej wartości",
    "smallerPercent": "Procentowy wzrost aktualnej wartości",
    "replay": "Funkcja Natychmiastowego Odtwarzania",
    "screenshot": "Funkcja zrzutu ekranu",
    "text1": "Gdy wartość czujnika",
    "text2": " i w",
    "text3": "Czas oczekiwania",
    "text4": "Jeśli w ciągu ustawionych sekund nie pojawi się wyższa wartość, natychmiast włącz",
    "text5": "Po każdym wyzwalaczu powtórzeń zaktualizuj próg do wartości w momencie wyzwolenia, aby zmniejszyć częste aktywacje",
    "text6": "Wyświetla obecną wartość progową używaną do uruchomienia odtwarzania",
    "text7": "Wyświetl wartości czujników",
    "text8": "Przekroczono początkowy próg",
    "text9": "Poniżej początkowego progu",
    "text10": "Początkowy licznik progowy",
    "initThreshold": "Początkowy próg",
    "curThreshold": "Bieżąca wartość progowa:",
    "curThreshold2": "Aktualny próg",
    "resetCurThreshold": "Zresetuj obecny próg",
    "action": "Aktywuj funkcję",
    "times": "Razy",
    "percentage": "Procent",
    "uninstallobs": "Moduł nagrywania nie został pobrany",
    "install": "Pobierz",
    "performanceAndAudioMode": "Tryb kompatybilności wydajności i audio",
    "isSaving": "Zapisywanie w toku",
    "video_replay": "Natychmiastowe odtwarzanie",
    "saved": "Zapisano",
    "loadQualitysScheme": "Załaduj preset graficzny",
    "notSet": "Nie skonfigurowano",
    "mirrorEnable": "Filtr został włączony",
    "canBeTurnedOff": "Powrót",
    "mirrorClosed": "Filtr gry został wyłączony",
    "closed": "Zamknięto",
    "openMirror": "Włącz filtr",
    "wonderfulScenes": "Wyróżnienia",
    "VulkanModeHaveProblem": "Tryb Vulkan ma problemy z kompatybilnością",
    "suggestDxMode": "Zalecamy przełączenie na tryb Dx",
    "functionNotSupported": "Funkcja nie jest obsługiwana",
    "NotSupported": "Nieobsługiwany",
    "gppManualRecording": "GamePP, ręczne rejestrowanie",
    "perfRecordsHaveBeenSaved": "Dane wydajności zostały zapisane",
    "redoClickF8": "Aby kontynuować nagrywanie, naciśnij ponownie klawisz F8.",
    "startIngameMonitor": "Włączanie funkcji monitorowania w grze",
    "inGameMarkSuccess": "Zaznaczenie w grze zakończyło się sukcesem",
    "recordingFailed": "Nagrywanie nie powiodło się",
    "recordingHasNotDownload": "Funkcja nagrywania nie została pobrana",
    "hotkeyDetected": "Wykryto konflikt klawiszy funkcji",
    "plzEditIt": "Proszę dokonać zmian w programie, a następnie go użyć",
    "onePercentLowFrame": "1% Niski klatka",
    "pointOnePercentLowFrame": "0.1% Niskie FPS",
    "frameGenerationTime": "Czas generowania klatki",
    "curTime": "Obecna godzina",
    "runTime": "Czas trwania uruchomienia",
    "cpuTemp": "Temperatura CPU",
    "cpuUsage": "Zużycie CPU",
    "cpuFreq": "Częstotliwość CPU",
    "cpuPower": "Moc cieplna CPU",
    "gpuTemp": "Temperatura GPU",
    "gpuUsage": "Zajętość GPU",
    "gpuPower": "Moc cieplna GPU",
    "gpuFreq": "Częstotliwość GPU",
    "memUsage": "Użycie pamięci"
  },
  "LoginArea": {
    "login": "Zaloguj",
    "loginOut": "Wyloguj",
    "vipExpire": "Wygasł",
    "remaining": "Pozostałe",
    "day": "Niebo",
    "openVip": "Aktywuj członkostwo",
    "vipPrivileges": "Prawa członka",
    "rechargeRenewal": "Napłata & Odnowienie",
    "Exclusivefilter": "Filtr",
    "configCloudSync": "Konfiguruj synchronizację w chmurze",
    "comingSoon": "Wkrótce dostępne"
  },
  "GameMirror": {
    "filterStatus": "Stan filtra",
    "filterPlan": "Preset filtra",
    "filterShortcut": "Filtr skrótów",
    "openCloseFilter": "Włącz/Wyłącz filtr:",
    "effectDemo": "Demonstracja efektu",
    "demoConfig": "Konfiguracja demo",
    "AiFilter": "Efekty filtru AI podlegają efektom w grze",
    "AiFilterFAQ": "Typowe problemy z filtrami AI",
    "gamePPAiFilter": "Filtr AI GamePP",
    "gamePPAiFilterVip": "Właściwy tylko dla VIP GamePP filtr AI, dynamicznie dostosowuje parametry filtra do scenariuszy gry, optymalizując efekty wizualne i poprawiając doświadczenie gry.",
    "AiMingliangTips": "Jasność AI: Zalecane do użycia, gdy ekran gry jest zbyt ciemny.",
    "AiBrightTips": "AI Vibrant: Zalecane do użycia, gdy grafika gry wygląda zbyt ciemno.",
    "AiDarkTips": "AI przyciemnianie: Zalecane do użycia, gdy grafika gry jest zbyt żywa",
    "AiBalanceTips": "Równowaga AI: Odpowiada większości scenariuszy gier.",
    "AiTips": "Wskazówka: Filtr AI aktywuje się poprzez naciśnięcie skrótu klawiaturowego w grze.",
    "AiFilterUse": "Proszę używać w grze",
    "AiFilterAdjust": "Dostosowanie skrótów klawiaturowych do filtra AI",
    "Bright": "Wyrazny",
    "Soft": "Oprogramowanie",
    "Highlight": "Wyróżnienie",
    "Film": "Wideo",
    "Benq": "BenQ",
    "AntiGlare": "Antyblaskowe",
    "HighSaturation": "Wysoka nasycalność",
    "Brightness": "Vivid",
    "Day": "Dzień",
    "Night": "Noc",
    "Nature": "Naturalny",
    "smooth": "Subtelnie",
    "elegant": "Surowy",
    "warm": "Ciepły ton",
    "clear": "Wyczyść",
    "sharp": "Ostrość",
    "vivid": "Dynamiczny",
    "beauty": "Vivid",
    "highDefinition": "HD",
    "AiMingliang": "AI Jasność",
    "AiBright": "AI Wyrazisty",
    "AiDark": "AI Przygaszenie",
    "AiBalance": "Równowaga AI",
    "BrightTips": "Filtr Vivid nadaje się do gier casual, akcji lub przygody, poprawiając nasycenie kolorów, aby uczynić wizualizacje gry bardziej dynamicznymi i angażującymi。",
    "liangTips": "Warto rozważyć użycie filtrów, gdy obraz gry jest zbyt ciemny。",
    "anTips": "Filtr ten jest zalecany do użycia, gdy ekran gry jest zbyt ciemny。",
    "jianyiTips": "Filtry zaleca się stosować, gdy wizualizacje gry są zbyt żywe。",
    "shiTips": "Filtr jest odpowiedni dla większości scenariuszy gry。",
    "shi2Tips": "Filtr jest odpowiedni do gier kawalerskich, akcji lub przygody i wzmaga nasycenie kolorów, aby wizualizacje gry były bardziej żywe i angażujące。",
    "ruiTips": "Płynne kolory filtrów i delikatne efekty oświetleniowe są idealne do przedstawienia scen marzycielskich, ciepłych lub niesamowitych。",
    "qingTips": "Jasny ton, wysoki kontrast, ostre detale. Odpowiada scenom żywo wyrazistym z wystarczającym oświetleniem.",
    "xianTips": "Wyższe ustawienia kontrastu i jasności zapewniają wyraźne szczegóły w ciemnych scenach bez zniekształceń i komfortowe oglądanie w jasnych scenach.",
    "dianTips": "Umocnij umiarkowanie jasność i kolor ekranu, aby osiągnąć możliwie najwyższą jakość wizualną filmową",
    "benTips": "Zmniejsza efekty światła białego, aby zapobiec męce oczu w czystych białych środowiskach gier",
    "fangTips": "Optymalizowane dla gier open-world i przygodowych, poprawia jasność i kontrast, aby uzyskać ostrzejsze wizualizacje.",
    "jiaoTips": "Dopasowane do gier RPG i symulacyjnych, zrównoważone tony, zwiększona wizualna realistyczność",
    "jieTips": "Optymalizowane pod kątem gier o bogatych fabułach i subtelnych emocjach, wzbogacając szczegóły i miękkość w celu osiągnięcia bardziej wyrafinowanych wizualizacji。",
    "jingTips": "Optymalizowane do gier akcji i rywalizacyjnych, poprawia ostrość i kontrast, by uzyskać bardziej ostre wizualizacje.",
    "xiuTips": "Optymalizowane pod kątem leczenia i gier casual, wzmacnia ciepłe odcienie i miękkość, tworząc bardziej przytulną atmosferę.",
    "qihuanTips": "Odpowiednie dla scen bogatych w elementy fantastyki i intensywne kolory, wzmacnia nasycenie kolorów, aby stworzyć silny wizualny wpływ",
    "shengTips": "Wzmacniaj kolory i szczegóły, aby podkreślić żywość i realistyczną jakość obrazu。",
    "sheTips": "Dostosowany do FPS, gier logicznych i przygodowych, wzmacnia szczegóły i kontrast, zwiększając realistyczność świata gry.",
    "she2Tips": "Odpowiednie do gier strzelankowych, wyścigowych lub walki, podkreślające szczegóły w wysokiej rozdzielczości i dynamiczną wydajność, aby wzmocnić intensywność i efekty wizualne doświadczenia gry.",
    "an2Tips": "Udoskonala wyrazistość scen w ciemnych środowiskach, idealne do ciemnych lub nocnych scenariuszy。",
    "wenTips": "Dostosowane do gier artystycznych, przygodowych lub relaksujących, tworzy subtelne odcienie kolorów oraz efekty świetlne i cieniowe, dodając elegancji i ciepła scenie。",
    "jing2Tips": "Dostosowane do gier konkurencyjnych, rytmicznych muzyki lub scenariuszy miasta nocnego, podkreślając żywe kolory i efekty oświetleniowe,",
    "jing3Tips": "Optymalizowane dla gier konkurencyjnych, akcji lub fantasy, wzmaga kontrast kolorów, aby wizualizacje były bardziej dynamiczne i żywe。",
    "onlyVipCanUse": "Ten filtr jest dostępny tylko dla użytkowników VIP"
  },
  "GameRebound": {
    "noGame": "Brak rekordów gry",
    "noGameRecord": "Nie ma jeszcze żadnych rekordów gry! Uruchom teraz sesję!",
    "gameDuration": "Dzisiejszy czas gry:",
    "gameElectricity": "Codzienna zużycie energii elektrycznej",
    "degree": "Stopień",
    "gameCo2": "Emisja CO₂ dzisiaj :",
    "gram": "Klawisz",
    "manualRecord": "Ręczna rejestracja",
    "recordDuration": "Czas trwania nagrywania:",
    "details": "Szczegóły",
    "average": "Średnia",
    "minimum": "Minimum",
    "maximum": "Maksymalny",
    "occupancyRate": "Użycie",
    "voltage": "Napięcie",
    "powerConsumption": "Zużycie energii",
    "start": "Start：",
    "end": "Koniec",
    "Gametime": "Czas trwania gry :",
    "Compactdata": "Optymalizacja danych",
    "FullData": "Pełne dane",
    "PerformanceAnalysis": "Analiza wydajności",
    "PerformanceAnalysis2": "Raport zdarzenia",
    "HardwareStatus": "Stan sprzętu",
    "totalPower": "Całkowite zużycie mocy",
    "TotalEmissions": "Całkowite emisje",
    "PSS": "Uwaga: Dane poniżej wykresu przedstawiają wartości średnie.",
    "FrameGenerationTime": "Czas generowania klatki",
    "GameResolution": "Rozdzielczość gry",
    "FrameGenerationTimeTips": "Ten punkt danych jest wyjątkowo wysoki i został wykluczony ze statystyk",
    "FrameGenerationTimeTips2": "Ten punkt danych jest niezwykle niski, dlatego nie został uwzględniony w statystykach",
    "noData": "Brak aktualnych danych do wyświetlenia",
    "ProcessorOccupancy": "Użycie CPU",
    "ProcessorFrequency": "Częstotliwość procesora",
    "ProcessorTemperature": "Temperatura procesora",
    "ProcessorHeatPower": "Moc cieplna procesora",
    "GraphicsCardOccupancy": "Użycie GPU D3D",
    "GraphicsCardOccupancyTotal": "Całkowite użycie GPU",
    "GraphicsCardFrequency": "Częstotliwość GPU",
    "GraphicsCardTemperature": "Temperatura GPU",
    "GraphicsCardCoreTemperature": "Temperatura punktu gorącego rdzenia GPU",
    "GraphicsCardHeatPower": "Moc cieplna GPU",
    "GraphicsCardMemoryTemperature": "Temperatura pamięci GPU",
    "MemoryOccupancy": "Użycie pamięci",
    "MemoryTemperature": "Temperatura pamięci",
    "MemoryPageFaults": "Przerwanie stronicowania pamięci",
    "Duration": "Czas trwania",
    "Time": "Czas",
    "StartStatistics": "Rozpocznij statystyki",
    "Mark": "Tag",
    "EndStatistics": "Zakończ statystyki",
    "LineChart": "Wykres liniowy",
    "AddPointInGame_m1": "Naciśnij w grze",
    "AddPointInGame_m2": "Można dodać punkt znacznika",
    "LeftMouse": "Kliknij lewym przyciskiem myszy, aby przełączyć widoczność, kliknij prawym przyciskiem myszy, aby zmienić kolor",
    "DeleteThisLine": "Usuń tę linię poliliniową",
    "AddCurve": "Dodaj krzywą",
    "AllCurvesAreHidden": "Wszystkie krzywe są ukryte",
    "ThereAreSamplingData": "Całkowite dane próbne:",
    "Items": "Wejście",
    "StatisticsData": "Statystyki",
    "electricity": "Zużycie energii",
    "carbonEmission": "Emisje węgla",
    "carbonEmissionTips": "Emisja dwutlenku węgla (kg) = zużycie energii elektrycznej (kWh) × 0,785",
    "D3D": "Użycie D3D:",
    "TOTAL": "Całkowity czas użycia:",
    "Process": "Proces:",
    "L3Cache": "Cache L3:",
    "OriginalFrequency": "Oryginalna częstotliwość:",
    "MaximumBoostFrequency": "Maksymalny Turbo Boost:",
    "DriverVersion": "Wersja sterownika :",
    "GraphicsCardMemoryBrand": "Marka VRAM:",
    "Bitwidth": "Szerokość magistrali",
    "System": "System :",
    "Screen": "Ekran",
    "Interface": "Interfejs:",
    "Channel": "Kanał:",
    "Timing": "Sekwencja: ",
    "Capacity": "Pojemność:",
    "Generation": "algebra",
    "AddPoint_m1": "Naciśnij w grze",
    "AddPoint_m2": "Dodaj punkt znacznika",
    "Hidden": "Ukryty",
    "Totalsampling": "Całkowite dane próbkowania:",
    "edition": "Wersja sterownika:",
    "MainHardDisk": "Główny dysk twardy",
    "SetAsStartTime": "Ustaw jako czas rozpoczęcia",
    "SetAsEndTime": "Ustaw jako czas zakończenia",
    "WindowWillBe": "Okno statystyk wydajności zostanie umieszczone w",
    "After": "Po zamknięciu",
    "NoLongerPopUpThisGame": "Ta gra nie będzie już wyświetlać okienek dialogowych",
    "HideTemperatureReason": "Ukryj powód temperatury",
    "HideTemperatureReason2": "Ukryj raport zdarzenia",
    "HideOtherReason": "Ukryj inne powody",
    "CPUanalysis": "Analiza wydajności CPU",
    "TemperatureCause": "Przyczyna temperatury",
    "tempSensorEvent": "Wydarzenie czujnika temperatury",
    "NoTemperatureLimitation": "Nie wykryto ograniczenia wydajności CPU spowodowanego temperaturą. Twój system chłodzenia całkowicie spełnia wymagania tej gry.",
    "NoTemperatureLimitation2": "Brak zdarzeń czujników temperatury, Twój system chłodzenia może idealnie obsłużyć wymagania gry.",
    "performanceis": "Wybrany",
    "Inside": "Wewnętrzny,",
    "TheStatisticsTimeOf": "Okres statystyczny spełnia odpowiednie warunki wyzwalania. Najczęstszy powód wyzwolenia to",
    "limited": "Procent całkowitego czasu spowodowany ograniczeniami wydajnościowymi z powodu temperatury",
    "SpecificReasons": "Konkretne przyczyny i ich proporcje w przypadku przyczyn związanych z temperaturą:",
    "OptimizationSuggestion": "Sugestie optymalizacji :",
    "CPUtemperature": "Temperatura CPU jest zbyt wysoka. Sprawdź/ulepsz środowisko chłodzenia CPU。",
    "CPUoverheat": "Nadmiarowe przegrzewanie CPU spowodowane jest zasilaniem z płyty głównej. Sprawdź ustawienia związane z płytą główną lub popraw środowisko chłodzenia。",
    "OtherReasons": "Inne powody",
    "NoPowerSupplyLimitation": "Wydajność procesora nie jest ograniczona przez zasilanie/przeciążenie. Ustawienia zużycia energii w BIOS-ie są wystarczające do spełnienia wymagań tego gry。",
    "PowerSupplyLimitation": "Z powodu ograniczeń zasilania elektrycznego/zużycia energii",
    "SpecificReasonsInOtherReasons": "Konkretny powód i jego proporcja wśród innych powodów:",
    "PleaseCheckTheMainboard": "Sprawdź stan dostarczania mocy płyty głównej lub dostosuj ustawienia zasilania BIOS, aby rozwiązać ograniczenia wydajności CPU spowodowane innymi czynnikami",
    "CPUcoretemperature": "Osiągnięto temperaturę rdzenia Tj,Max i została ona ograniczona",
    "CPUCriticalTemperature": "Temperatura CPU krytyczna",
    "CPUCircuitTemperature": "CPU Package/Ring Bus ograniczony osiągnięciem Tj,Max",
    "CPUCircuitCriticalTemperature": "Pakiet CPU / magistrala pierścieniowa osiągnął krytyczną temperaturę",
    "CPUtemperatureoverheating": "Wykryto przegrzanie CPU, co wyzwalając automatyczną redukcję częstotliwości, aby obniżyć temperaturę i zapobiec awariom sprzętu.",
    "CPUoverheatingtriggered": "Procesor aktywuje mechanizm chłodzenia i dostosuje napięcie/częstotliwość, aby zmniejszyć zużycie energii i temperaturę.",
    "CPUPowerSupplyOverheating": "Jednostka CPU jest ograniczona ze względu na poważne przegrzanie zasilania płyty głównej",
    "CPUPowerSupplyLimitation": "Moc CPU jest ograniczona z powodu przegrzania zasilacza płyty głównej",
    "CPUMaximumPowerLimitation": "Jądro jest ograniczone maksymalnym limitem zużycia energii",
    "CPUCircuitPowerLimitation": "Pakiet CPU/Bus Ring osiągnął limit mocy",
    "CPUElectricalDesignLimitation": "Wyzwalanie ograniczeń projektowych elektrycznych (w tym ICCmax muru prądowego, PL4 muru mocy szczytowej, ograniczenia napięcia SVID itd.)",
    "CPULongTermPowerLimitation": "Osiągnięto długoterminowy limit mocy CPU",
    "CPULongTermPowerinstantaneous": "Natychmiastowe zużycie energii przez procesor osiągnął limit",
    "CPUPowerLimitation": "Mechanizm degradacji częstotliwości Turbo CPU, zazwyczaj ograniczany przez BIOS lub specyficzne oprogramowanie",
    "CPUPowerWallLimitation": "Ograniczenie mocy CPU",
    "CPUcurrentwalllimit": "Obecna granica ściany CPU",
    "AiAgent": "Agent GamePP (Agent AI)",
    "AgentDesc": "Powróć do strony głównej",
    "fnBeta": "Ta funkcja jest obecnie w fazie testowania na zaproszenie. Twoje konto GamePP nie otrzymało jeszcze dostępu do testu。",
    "getAIReport": "Uzyskaj raport AI",
    "waitingAi": "Czekanie na ukończenie generowania raportu",
    "no15mins": "Czas gry jest krótszy niż 15 minut, nie można wygenerować ważnego raportu AI。",
    "timeout": "Przekroczono czas oczekiwania na żądanie serwera",
    "agentId": "ID agenta:",
    "reDo": "Ponownie wygeneruj raport",
    "text2": "Agent GamePP: Raport analizy AI online, poniższe treści zostały wygenerowane przez AI i mają charakter informacyjny.",
    "amdAiagentTitle": "Agnent GamePP: Raport analizy AMD Ryzen AI, poniższy treść został wygenerowany przez AI i ma charakter wyłącznie referencyjny.",
    "noCurData": "Brak aktualnych danych",
    "dataScreening": "Filtrowanie danych",
    "dataScreeningDescription": "Ta funkcja ma na celu wykluczenie statystyk danych z nieefektywnych okresów gry, takich jak wczytywanie mapy lub oczekiwanie w lobby. 0 oznacza brak wykluczenia.",
    "excessivelyHighParameter": "Zbyt wysokie parametry",
    "tooLowParameter": "Zbyt niskie parametry",
    "theMaximumValueIs": "Maksymalna wartość to",
    "theMinimumValueIs": "Wartość minimalna to",
    "exclude": "Wyklucz",
    "dataStatisticsAtThatTime": "Statystyki danych w tym momencie",
    "itHasBeenGenerated": "Generacja ukończona,",
    "clickToView": "Kliknij, aby wyświetlić",
    "onlineAnalysis": "Analiza online",
    "amdRyzenAILocalAnalysis": "AMD Ryzen AI Lokalna analiza",
    "useTerms": "Warunki użytkowania:",
    "term1": "1. Procesor Ryzen AI Max lub Procesor Ryzen Al 300 Serii",
    "term2": "2.Wersja sterownika AMD NPU",
    "term3": "3. Włącz grafikę wbudowaną",
    "conformsTo": "Zgoda",
    "notInLineWith": "Niekompatybilny",
    "theVersionIsTooLow": "Za stara wersja",
    "canNotUseAmdNpu": "Twoja konfiguracja nie spełnia wymagań. Analiza jednostki AMD NPU nie jest dostępna。",
    "unusable": "Nie dostępne",
    "downloadTheFile": "Pobierz plik",
    "downloadSource": "Źródło pobierania:",
    "fileSize": "Rozmiar pliku: ok. 8,34 GB",
    "cancelDownload": "Anuluj pobieranie",
    "filePath": "Lokalizacja pliku",
    "generateAReport": "Wygeneruj raport",
    "fileMissing": "Plik brakuje. Należy ponownie pobrać.",
    "downloading": "Pobieranie w toku...",
    "theModelConfigurationLoadingFailed": "Nie można załadować konfiguracji modelu",
    "theModelDirectoryDoesNotExist": "Katalog modelu nie istnieje",
    "thereIsAMistakeInReasoning": "Błąd wnioskowania",
    "theInputExceedsTheModelLimit": "Wprowadzony tekst przekracza limit modelu",
    "selectModelNotSupport": "Wybrany model pobierania nie jest obsługiwany",
    "delDirFail": "Nie można usunąć istniejącego katalogu modelu",
    "failedCreateModelDir": "Nie udało się utworzyć katalogu modelu",
    "modelNotBeenFullyDownload": "Model nie został całkowicie pobrany",
    "agentIsThinking": "Agent Jiajia myśli",
    "reasoningModelFile": "Plik modelu do wnioskowania",
    "modelReasoningTool": "Narzędzie wnioskowania modelu"
  },
  "SelectSensor": {
    "DefaultSensor": "Domyślny czujnik",
    "Change": "Zmień",
    "FanSpeed": "Prędkość wentylatora",
    "MainGraphicsCard": "Główny GPU",
    "SetAsMainGraphicsCard": "Ustaw jako główną kartę graficzną",
    "GPUTemperature": "Temperatura GPU",
    "GPUHeatPower": "Moc termiczna GPU",
    "GPUTemperatureD3D": "Użycie GPU D3D",
    "GPUTemperatureTOTAL": "Całkowite wykorzystanie GPU",
    "GPUTemperatureCore": "Temperatura punktu gorącego rdzenia GPU",
    "MotherboardTemperature": "Temperatura płyty głównej",
    "MyAttention": "Moje ulubione",
    "All": "Wszystkie",
    "Unit": "Jednostka:",
    "NoAttention": "Niesledzonych czujników",
    "AttentionSensor": "Monitorowane czujniki (Beta)",
    "GoToAttention": "Przejdź do trybu Fokus",
    "CancelAttention": "Anuluj subskrypcję",
    "noThisSensor": "Brak czujnika",
    "deviceAbout": "Periferie powiązane",
    "deviceBattery": "Bateria obwodowa",
    "testFunction": "Funkcja testowa",
    "mouseEventRate": "Częstotliwość zapytań",
    "relatedWithinTheGame": "Dotyczący gry",
    "winAbout": "System",
    "trackDevicesBattery": "Sledź poziom baterii urządzeń peryferyjnych",
    "ingameRealtimeMouseRate": "Obecnie stosowana częstotliwość kwerendy myszy w czasie rzeczywistym w grach",
    "notfoundDevice": "Nie znaleziono urządzenia obsługującego",
    "deviceBatteryNeedMythcool": "Lista urządzeń obsługujących wyświetlanie poziomu baterii: (wymaga użycia Myth.Cool)",
    "vkm1mouse": "Mysz Valkyrie M1",
    "vkm2mouse": "Valkyrie M2 Mysz",
    "vk99keyboard": "Valkyrie 99 Klawiatura Magnetyczno-Osowa",
    "logitechProWireless": "Logitech Pro Wireless",
    "logitechProXSUPERLIGHT": "Logitech PRO X SUPERLIGHT",
    "logitechProXSUPERLIGHT2": "Logitech PRO",
    "logitechPro2LIGHTSPEED": "Logitech PRO 2 LIGHTSPEED",
    "steelSeriesArctisGameBuds": "SteelSeries Arctis GameBuds",
    "steelSeriesArctis7PPlus": "SteelSeries Arctis 7P Plus",
    "razerV3": "Razer Valkyrie V3 Wersja Profesjonalna",
    "razerV2": "Razer Viper V2 Wersja Profesjonalna",
    "wireless": "Bezprzewodowy",
    "logitechNeedGhub": "Gdy Logitech nie może pobrać modelu urządzenia, należy pobrać oprogramowanie GHUB",
    "chargingInProgress": "Ładowanie",
    "inHibernation": "W trybie uśpienia"
  },
  "video": {
    "videoRecord": "Nagrywanie wideo",
    "recordVideo": "Nagranie wideo",
    "scheme": "Profil",
    "suggestScheme": "Zalecany plan",
    "text1": "W tej konfiguracji rozmiar wideo trwającego 1 minutę wynosi około",
    "text2": "Ta funkcja korzysta z dodatkowych zasobów systemowych",
    "low": "Niski",
    "mid": "Strona główna",
    "high": "Wysoki",
    "1080p": "Natywny",
    "RecordingFPS": "Rejestruj FPS",
    "bitRate": "Prędkość transmisji wideo",
    "videoResolution": "Rozdzielczość wideo",
    "startStopRecord": "Rozpocznij/Zatrzymaj nagrywanie",
    "instantReplay": "Natychmiastowa powtórzka",
    "instantReplayTime": "Czas odtwarzania natychmiastowego",
    "showIngame": "Uruchom panel sterowania w grze",
    "CaptureMode": "Metoda przechwytywania",
    "gameWindow": "Okno gry",
    "desktopWindow": "Okno pulpitu",
    "fileSavePath": "Ścieżka przechowywania plików",
    "selectVideoSavePath": "Wybierz ścieżkę zapisu nagrania",
    "diskFreeSpace": "Dostępne miejsce na dysku twardym:",
    "edit": "Zmień",
    "open": "Otwórz",
    "displayMouse": "Wyświetl kursor myszy",
    "recordMicrophone": "Rejestruj mikrofon",
    "gameGraphics": "Oryginalny tryb wyświetlania gry"
  },
  "Setting": {
    "common": "Ogólne",
    "personal": "Personalizacja",
    "messageNotification": "Powiadomienia",
    "sensorReading": "Wartości z czujnika",
    "OLEDscreen": "Ochrona przed efektem burinu OLED",
    "performanceStatistics": "Statystyki wydajności",
    "shortcut": "Skrót klawiaturowy",
    "ingameSetting": "Zapisz ustawienia gry",
    "other": "Inne",
    "otherSettings": "Inne ustawienia",
    "GeneralSetting": "Ustawienia ogólne",
    "softwareVersion": "Wersja oprogramowania",
    "checkForUpdates": "Sprawdź aktualizacje",
    "updateNow": "Zaktualizuj teraz",
    "currentVersion": "Obecna wersja",
    "latestVersion": "Najnowsza wersja",
    "isLatestVersion": "Obecna wersja jest już najnowszą wersją.",
    "functionModuleUpdate": "Aktualizacja modułu funkcyjnego",
    "alwaysUpdateModules": "Upewnij się, że wszystkie zainstalowane moduły funkcyjne są aktualne",
    "lang": "Język",
    "bootstrap": "Automatyczny start",
    "powerOn_m1": "Uruchomienie",
    "powerOn_m2": "Automatyczne uruchamianie po sekundach",
    "defaultDelay": "Domyślnie: 40 sekund",
    "followSystemScale": "Zastosuj skalowanie systemowe",
    "privacySettings": "Ustawienia prywatności",
    "JoinGamePPPlan": "Dołącz do programu ulepszenia doświadczenia użytkownika GamePP",
    "personalizedSetting": "Personalizacja",
    "restoreDefault": "Przywróć ustawienia domyślne",
    "color": "Kolor",
    "picture": "Obraz",
    "video": "Wideo",
    "browse": "Przeglądaj",
    "clear": "Wyczyść",
    "mp4VideoOrPNGImagesCanBeUploaded": "Prześlij pliki wideo MP4 lub obrazy PNG",
    "transparency": "Przezroczystość",
    "backgroundColor": "Kolor tła",
    "textFont": "Czcionka głównego tekstu",
    "message": "Wiadomość",
    "enableInGameNotifications": "Włącz powiadomienia w grze",
    "messagePosition": "Pozycja wyświetlania w grze",
    "leftTop": "Lewy górny róg",
    "leftCenter": "Środek Lewy",
    "leftBottom": "Lewy dolny róg",
    "rightTop": "górny prawy róg",
    "rightCenter": "Prawo Środek",
    "rightBottom": "prawy dolny róg",
    "noticeContent": "Zawartość powiadomienia",
    "gameInjection": "Iniekcja gry",
    "ingameShow": "Wyświetlaj w grze",
    "inGameMonitoring": "Monitorowanie w grze",
    "gameFilter": "Filtr gier",
    "start": "Uruchom",
    "endMarkStatistics": "Statystyki markerów końcowych",
    "readHwinfoFail": "Nie można pobrać informacji o sprzęcie!",
    "dataSaveDesktop": "Dane zostały zapisane do schowka i pliku na pulpicie.",
    "TheSensorCacheCleared": "Dane pamięci podręcznej sensora zostały usunięte",
    "defaultSensor": "Domyślny czujnik",
    "setSensor": "Wybierz czujnik",
    "refreshTime": "Czas odświeżania danych",
    "recommend": "Domyślnie",
    "sensorMsg": "Im krótszy interwał czasowy, tym wyższe zużycie wydajności. Proszę starannie wybrać。",
    "exportData": "Eksport danych",
    "exportHwData": "Eksportuj dane informacji o sprzęcie",
    "sensorError": "Nieprawidłowe odczytywanie czujnika",
    "clearCache": "Wyczyść pamięć podręczną",
    "littleTips": "Wskazówka: Raport wydajności zostanie wygenerowany 2 minuty po uruchomieniu gry",
    "disableAutoShow": "Wyłącz automatyczne otwieranie okna statystyk wydajności",
    "AutoClosePopUpWindow_m1": "Okno statystyk wydajności automatycznie zamyka się po ustalonym czasie:",
    "AutoClosePopUpWindow_m2": "sekundy",
    "abnormalShutdownReport": "Raport nieprawidłowego wyłączania",
    "showWeaAndAddress": "Wyświetl informacje o pogodzie i lokalizacji",
    "autoScreenShots": "Automatycznie przechwyć ekran gry po oznaczeniu",
    "keepRecent": "Liczba ostatnich zapisanych rekordów:",
    "noLimit": "Nieograniczone",
    "enableInGameSettingsSaving": "Włącz zapisywanie ustawień w grze",
    "debugMode": "Tryb debugowania",
    "enableDisableDebugMode": "Włącz/Wyłącz tryb debugowania",
    "audioCompatibilityMode": "Tryb kompatybilności dźwięku",
    "quickClose": "Szybkie zamknięcie",
    "closeTheGameQuickly": "Szybko zamknij proces gry",
    "cancel": "Anuluj",
    "confirm": "Potwierdź",
    "MoveInterval_m1": "Monitorowanie pulpitu i w grze ulegnie lekkiemu przesunięciu:",
    "MoveInterval_m2": "minuty",
    "text3": "Po wyjściu z gry nie pojawi się okno raportu wydajności, a jedynie zostaną zachowane historie",
    "text5": "Po nieoczekiwanym wyłączaniu systemu zostanie automatycznie wygenerowany raport. Włączanie tej funkcji spowoduje zużycie dodatkowych zasobów systemowych。",
    "text6": "Użycie skrótów może kolidować z innymi skrótami gry. Proszę ustawiać je ostrożnie。",
    "text7": "Ustaw skrót klawiaturowy na „Brak”, proszę użyć klawisza Backspace",
    "text8": "Utrzymanie filtrów, monitorowania w grze i innych stanów funkcjonalnych podczas gry na podstawie nazw procesów",
    "text9": "Włączenie będzie ciągle rejestrować dane uruchamiane; wyłączenie usunie pliki dziennika (Zalecane, aby nie włączać)",
    "text10": "Po włączeniu, czujniki płyty głównej będą niedostępne, aby rozwiązać problemy dźwiękowe spowodowane przez GamePP。",
    "text11": "Naciśnij Alt+F4 dwukrotnie z rzędu, aby szybko zamknąć aktualnie uruchomioną grę",
    "text12": "Czy chcesz kontynuować? Ten tryb wymaga ponownego uruchomienia GamePP.",
    "openMainUI": "Pokaż aplikację",
    "setting": "Ustawienia",
    "feedback": "Feedback",
    "help": "Pomoc",
    "sensorReadingSetting": "Ustawienia odczytu czujnika",
    "searchlanguage": "Język wyszukiwania"
  },
  "GamePlusOne": {
    "year": "Rok",
    "month": "Miesiąc",
    "day": "dzień",
    "success": "Sukces",
    "fail": "Nie powiodło się",
    "will": "Obecny",
    "missedGame": "Nieodrzucone gry",
    "text1": "Kwota, ok. ￥",
    "text2": "Całkowite odzyskane gry",
    "text3": "Wersja",
    "gamevalue": "Wartość gry",
    "gamevalue1": "Wniosek",
    "total": "Całkowita ilość zgłoszona",
    "text4": "Gry, łączna liczba zapisanych",
    "text6": "Produkt, Wartość",
    "Platformaccountmanagement": "Zarządzanie kontem na platformie",
    "Missed1": "Nieodzyskany",
    "Received2": "（Pomyślnie odebrano）",
    "Receivedsoon2": "(Obecnie dostępne)",
    "Receivedsoon": "Dostępne",
    "Missed": "Brakujące zbiorstwo",
    "Received": "Pomyślnie otrzymano",
    "Getaccount": "Zajmij konto",
    "Worth": "Wartość",
    "Auto": "Automatycznie",
    "Manual": "Ręczny",
    "Pleasechoose": "Wybierz grę",
    "Receive": "Zajmij teraz",
    "Selected": "Wybrany",
    "text5": "Gry",
    "Automatic": "Automatyczne pobieranie...",
    "Collecting": "W trakcie odbioru...",
    "ReceiveTimes": "Miesięczna liczba zgłoszeń",
    "Thefirst": "#",
    "Week": "Tydzień",
    "weekstotal": "Razem 53 tygodnie",
    "Return": "Strona główna",
    "Solutionto": "Rozwiązanie problemu z niepowodzeniem wiązania konta",
    "accounts": "Liczba powiązanych kont",
    "Addaccount": "Dodaj konto",
    "Clearcache": "Wyczyść pamięć podręczną",
    "Bindtime": "Czas przypisania",
    "Status": "Status",
    "Normal": "Tryb Normalny",
    "Invalid": "Nieprawidłowy",
    "text7": "Gry, łączna liczba zapisanych dla Ciebie",
    "Yuan": "Yuan",
    "untie": "Odblokuj",
    "disable": "Dezaktywuj",
    "enable": "Włącz",
    "gamePlatform": "Platforma gier",
    "goStorePage": "Przejdź do strony sklepu",
    "receiveEnd": "Po terminie",
    "loginPlatformAccount": "Zalogowany konto platformy",
    "waitReceive": "Oczekujące oświadczenie",
    "receiveSuccess": "Sukces",
    "accountInvalid": "Konto wygasło",
    "alreadyOwn": "Już posiadane",
    "networkError": "Błąd sieci",
    "noGame": "Brak Game Core",
    "manualReceiveInterrupt": "Ręczne Przerwanie Przechwytywania",
    "receiving": "Reklamacja w toku",
    "agree": "Zgadzam się dołączyć do programu darmowego odbioru GamePP.",
    "again": "Ponownie odbierz"
  },
  "shutdownTimer": {
    "timedShutdown": "Zaplanowane wyłaczenie",
    "currentTime": "Obecna godzina:",
    "setCountdown": "Ustaw odliczanie",
    "shutdownInSeconds": "Wyłączenie za X sekund",
    "shutdownIn": "Po wyłączaniu",
    "goingToBe": "będzie",
    "executionPlan": "Plan wykonania",
    "startTheClock": "Rozpocznij odliczanie",
    "stopTheClock": "Anuluj plan",
    "isShuttingDown": "Wykonywanie zaplanowanego wyłączania:",
    "noplan": "Brak obecnego planu wyłączania",
    "hour": "Godzina",
    "min": "Minuta",
    "sec": "Sekunda",
    "ms": "Milisekunda",
    "year": "Rok",
    "month": "Miesiąc",
    "day": "Dzień",
    "hours": "Godzina"
  },
  "screenshotpage": {
    "screenshot": "Zrzut ekranu",
    "screenshotFormat": "Stworzono specjalnie do przechwytywania ekranów gier, obsługuje zapisywanie w formacie JPG/PNG/BMP, umożliwia szybkie przechwytywanie ekranów gier i zapewnia wyjście w wysokiej rozdzielczości bez utraty jakości",
    "Turnon": "Włącz automatyczne zrzuty ekranu, co",
    "seconds": "Sekunda",
    "takeScreenshot": "Wykonaj automatyczny zrzut ekranu",
    "screenshotSettings": "Ustawienie to jest nieprawidłowe, gdy jest włączone w grze",
    "saveGameFilterAndMonitoring": "Zapisz efekty 'Game Filter' i 'Monitorowanie w grze' w zrzucie ekranu",
    "disableScreenshotSound": "Wyłącz dźwiękowe powiadomienie dla zrzutu ekranu",
    "imageFormat": "Format obrazu",
    "recommended": "Polecanie",
    "viewingdetails": "Zachowuje szczegóły jakości obrazu, średnie rozmiary, odpowiednie do przeglądania szczegółów",
    "saveSpace": "Dostosowalna jakość obrazu, minimalny rozmiar pliku, oszczędność miejsca",
    "ultraQuality": "Najwyższej jakości, niekompresowane wizualizacje z dużą wielkością plików. Zalecane dla graczy, którzy priorytetyzują wysokiej jakości zapisy gier.",
    "fileSavePath": "Ścieżka zapisu pliku",
    "hardDiskSpace": "Dostępne miejsce na dysku:",
    "minutes": "Minuta"
  },
  "DesktopMonitoring": {
    "desktopMonitoring": "Monitorowanie Pulpitu",
    "SomeSensors": "Zaleca się niektóre czujniki do monitorowania. Można je usunąć lub dodać",
    "AddComponent": "Dodaj nowy komponent",
    "Type": "Typ",
    "Remarks": "Uwaga",
    "AssociatedSensor": "Połącz czujnik",
    "Operation": "Operacja",
    "Return": "Wstecz",
    "TimeSelection": "Wybór czasu:",
    "Format": "Format:",
    "Rule": "Reguła：",
    "Coordinate": "Koordynaty:",
    "CustomTextContent": "Niestandardowy treść tekstowa:",
    "SystemTime": "Czas systemowy",
    "China": "Chiny",
    "Britain": "Wielka Brytania",
    "America": "Stany Zjednoczone",
    "Russia": "Rosja",
    "France": "Francja",
    "DateAndTime": "Data i czas",
    "Time": "Czas",
    "Date": "Data",
    "Week": "Dzień tygodnia",
    "DateAndTimeAndWeek": "Data+Czas+Dzień tygodnia",
    "TimeAndWeek": "Czas+Dzień tygodnia",
    "Hour12": "Format 12-godzinny",
    "Hour24": "format 24-godzinny",
    "SelectSensor": "Wybierz czujnik:",
    "AssociatedSensor1": "Połącz czujnik:",
    "SensorUnit": "Jednostka czujnika：",
    "Second": "Sekunda:",
    "Corner": "Wygładzone narożniki:",
    "BackgroundColor": "Kolor tła:",
    "ProgressColor": "Kolor postępu:",
    "Font": "Czcionka：",
    "SelectFont": "Wybierz czcionkę",
    "FontSize": "Rozmiar czcionki:",
    "Color": "Kolor：",
    "Style": "Styl:",
    "Bold": "Pogrubienie",
    "Italic": "Kursywa",
    "Shadow": "Cień",
    "ShadowPosition": "Pozycja cienia：",
    "ShadowEffect": "Efekty cieni：",
    "Blur": "Rozmycie",
    "ShadowColor": "Kolor cieni：",
    "SelectFromLocalFiles": "Wybierz z plików lokalnych:",
    "UploadImageVideo": "Prześlij obraz/klip",
    "UploadSVGFile": "Prześlij plik SVG",
    "Width": "Szerokość:",
    "Height": "Wysoki: ",
    "Effect": "Efekt:",
    "Rotation": "Obracanie:",
    "WhenTheSensorValue": "Wartość czujnika jest większa niż",
    "conditions": "Gdy warunek nie jest spełniony (brak obrotu, gdy wartość czujnika wynosi 0)",
    "Clockwise": "Zgodnie z ruchem wskazówek zegara",
    "Counterclockwise": "Przeciwnie do ruchu wskazówek zegara",
    "QuickRotation": "Szybka rotacja",
    "SlowRotation": "Wolne obroty",
    "StopRotation": "Zatrzymaj obracanie",
    "StrokeColor": "Kolor konturu：",
    "Path": "Ścieżka",
    "Color1": "Kolor",
    "ChangeColor": "Zmień kolor",
    "When": "Kiedy",
    "SensorValue": "Wartość czujnika większa lub równa",
    "SensorValue1": "Wartość czujnika mniejsza lub równa",
    "SensorValue2": "Wartość czujnika jest równa",
    "MonitoringSettings": "Ustawienia monitorowania",
    "RestoreDefault": "Przywróć ustawienia domyślne",
    "Monitor": "Monitor",
    "AreaSize": "Wielkość obszaru",
    "Background": "Tło",
    "ImageVideo": "Obrazy/Wideo",
    "PureColor": "Jednobaro",
    "Select": "Wybierz",
    "ImageVideoDisplayMode": "Tryb wyświetlania obrazów/wideo",
    "Transparency": "Przezroczystość",
    "DisplayPosition": "Pokaż pozycję",
    "Stretch": "Rozciąganie",
    "Fill": "Wypełnienie",
    "Adapt": "Dopasować",
    "SelectThePosition": "Kliknij komórkę, aby szybko wybrać lokalizację",
    "CurrentPosition": "Bieżąca lokalizacja:",
    "DragLock": "Blokada przesuwania",
    "LockMonitoringPosition": "Pozycja monitora zablokowana (Po zablokowaniu monitora nie można go przeciągać)",
    "Unlockinterior": "Włącz przeciąganie elementów wewnętrznych",
    "Font1": "Czcionka",
    "GameSettings": "Ustawienia gry",
    "CloseDesktopMonitor": "Automatycznie wyłącz monitorowanie pulpitu podczas uruchamiania gry.",
    "OLED": "Ochrona OLED przed efektem wypalenia",
    "Display": "Pokaż",
    "PleaseEnterContent": "Wprowadź treść",
    "NextStep": "Dalej",
    "Add": "Dodaj",
    "StylesForYou": "Zalecaliśmy kilka stylów monitorowania. Możesz je wybrać i zastosować. W przyszłości będą dodawane nowe style.",
    "EditPlan": "Edytuj profil",
    "MonitoringStylePlan": "Schemat stylu monitorowania",
    "AddDesktopMonitoring": "Dodaj monitorowanie pulpit",
    "TextLabel": "Etykieta tekstowa",
    "ImageVideo1": "Obrazy, Wideo",
    "SensorGraphics": "Grafika czujnika",
    "SensorData": "Dane z czujnika",
    "CustomText": "Tekst niestandardowy",
    "DateTime": "Data i godzina",
    "Image": "Obraz",
    "Video": "Wideo",
    "SVG": "SVG",
    "ProgressBar": "Pasek postępu",
    "Graphics": "Grafika",
    "UploadImage": "Prześlij obraz",
    "UploadVideo": "Prześlij film",
    "RealTimeMonitoring": "Rzeczywistym czasie monitoruj temperaturę i wykorzystanie CPU/GPU, swobodnie przesuwaj i konfiguruj układ, dostosuj styl – opanuj wydajność i estetykę pulpitu",
    "Chart": "Wykres",
    "Zigzagcolor": "Kolor linii (Początek)",
    "Zigzagcolor1": "Kolor linii (Koniec)",
    "Zigzagcolor2": "Kolor obszaru wykresu liniowego (Początek)",
    "Zigzagcolor3": "Kolor obszaru wykresu liniowego (koniec)",
    "CustomMonitoring": "Niestandardowy monitoring"
  }
}
//messageEnd 
 export default pl 