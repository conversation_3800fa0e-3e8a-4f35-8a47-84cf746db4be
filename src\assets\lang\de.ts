const de = {
  "update": {
    "Game": "Game",
    "PP": "PP",
    "upgradingInProgress": "Wird aktualisiert",
    "theModuleIsBeingUpdated": "Modul wird aktualisiert",
    "dataIsBeingUpdated": "Daten werden aktualisiert...",
    "checkingUpdate": "Aktualisierungen werden geprüft",
    "checkingUpgrade": "Aktualisierung wird geprüft",
    "loadingProgramComponent": "Programmkomponenten werden geladen...",
    "loadingHotkeyModules": "Hotkey-Komponente wird geladen",
    "loadingGPPModules": "Lädt GamePP-Komponenten",
    "loadingBlackWhiteList": "Schwarze Liste/Weisse Liste wird geladen",
    "loadingGameSetting": "Laden der Spiel-Einstellungen...",
    "loadingUserAbout": "Lädt Benutzerauthentifizierungsdaten",
    "loadingGameBenchmark": "<PERSON>ädt Spiel-Benchmark",
    "loadingHardwareInfo": "Lädt Hardware-Informationen-Komponente",
    "loadingDBModules": "Datenbankmodul wird geladen",
    "loadingIGCModules": "Lädt IGC-Modul",
    "loadingFTPModules": "Lade FTP-Unterstützungsmodul",
    "loadingDialogModules": "Lädt Dialogfeld-Modul",
    "loadingDataStatisticsModules": "Laden des Statistikmoduls wird durchgeführt",
    "loadingSysModules": "Systemkomponenten werden geladen",
    "loadingGameOptimization": "Spieloptimierung wird geladen",
    "loadingGameAcceleration": "Lade Spielbeschleunigung",
    "loadingScreenshot": "Lade Aufnahmescreenshot",
    "loadingVideoComponent": "Lädt Videokompressionskomponente",
    "loadingFileFix": "Datei-Reparatur wird geladen",
    "loadingGameAI": "Lade Spiel-AI-Qualität",
    "loadingNVAPIModules": "NVAPI-Modul wird geladen",
    "loadingAMDADLModules": "Lädt AMDADL-Modul",
    "loadingModules": "Modul wird geladen"
  },
  "messages": {
    "append": "Hinzufügen",
    "confirm": "Bestätigen",
    "cancel": "Abbrechen",
    "default": "Standard",
    "quickSelect": "Schnellauswahl",
    "onoffingame": "Spielinternen Monitoring aktivieren/deaktivieren:",
    "changeKey": "Klicken Sie, um die Tastenkombination zu ändern",
    "clear": "Leeren",
    "hotkeyOccupied": "Die Hotkey-Taste ist bereits belegt, bitte legen Sie eine neue fest!",
    "minimize": "Minimieren",
    "exit": "Beenden",
    "export": "Exportieren",
    "import": "Importieren",
    "screenshot": "Bildschirmaufnahme",
    "showHideWindow": "Fenster anzeigen/ausblenden",
    "ingameControlPanel": "Spielsteuerungspanel",
    "openOrCloseGameInSettings": "Einstellungen in Spiel ein-/ausschalten",
    "openOrCloseGameInSettings2": "Drücken Sie diese Tastenkombination, um zu aktivieren",
    "openOrCloseGameInSettings3": "Spielmonitoring aktivieren/deaktivieren",
    "openOrCloseGameInSettings4": "Spiel-Filter aktivieren/deaktivieren",
    "startManualRecord": "Manuelle Statistikaufnahme starten/stoppen",
    "performanceStatisticsMark": "Leistungsstatistik-Marker",
    "EnableAIfilter": "Der AI-Filter muss durch Drücken dieser Tastenkombination aktiviert werden",
    "Start_stop": "Manuelle Statistik-Aufzeichnung starten/stoppen",
    "pressureTest": "Stress-Test",
    "moduleNotInstalled": "Funktionsmodul nicht installiert",
    "installingPressureTest": "Drucktestmodul wird installiert...",
    "importFailed": "Import fehlgeschlagen",
    "gamepp": "GamePP",
    "copyToClipboard": "In die Zwischenablage kopiert"
  },
  "home": {
    "homeTitle": "Startseite",
    "hardwareInfo": "Hardware-Informationen",
    "functionIntroduction": "Funktionen",
    "fixedToNav": "Anheften an die Navigationsleiste",
    "cancelFixedToNav": "Von der Navigationsleiste lösen",
    "hardwareInfoLoading": "Laden der Hardwareinformationen...",
    "performanceStatistics": "Leistungsstatistik",
    "updateNow": "Jetzt aktualisieren",
    "recentRun": "Kürzliche Aktivität",
    "resolution": "Auflösung:",
    "duration": "Dauer:",
    "gameFilter": "Spiel-Filter",
    "gameFilterHasAccompany": "Spiel-Filter begleitet Sie",
    "gameFilterHasAccompany2": "Benutzer spielen Spiele wie Cyberpunk, APEX und Hogwarts Legacy",
    "currentList": "Überwachungselemente in der aktuellen Liste",
    "moreFunction": "Benchmark, Stresstest, Desktop-Monitoring und weitere Funktionen sind in Entwicklung.",
    "newVersion": "Neue Version verfügbar！",
    "discoverUpdate": "Update gefunden!",
    "downloading": "Wird heruntergeladen",
    "retry": "Erneut versuchen",
    "erhaAI": "2HaAI",
    "recordingmodule": "Diese Funktion hängt vom Aufnahmemodul ab",
    "superPower": "Ultra-Modus",
    "autoRecord": "Automatisch Kill-Momente im Spiel aufzeichnen und Highlights einfach speichern",
    "externalDevice": "Dynamische Peripheriebeleuchtung",
    "linkage": "Kill-Szenen in Spielen auslösen und über angeschlossene Peripheriegeräte anzeigen",
    "AI": "Leistungstest für KI",
    "test": "Testen Sie KI-Modelle mit GPU und zeigen Sie die KI-Leistungsbewertung an",
    "supportedGames": "Unterstützte Spiele",
    "games": "Naraka: Bladepoint, Delta Force, FragPunk, League of Legends, PUBG, Valorant, The Finals, APEX Legends, COD20",
    "videoRecording": "Videoaufnahme",
    "videoRecording2": "OBS-basierte Videorekordfunktion, unterstützt die Einstellung von Videobitrate und Framerate (FPS) für unterschiedliche Anforderungen an Bildqualität und Flüssigkeit; unterstützt auch „Sofortwiedergabe“, drücken Sie die Tastenkombination, um Highlights jederzeit zu speichern!",
    "addOne": "Kostenlos erhalten",
    "gamePlatform": "Spieleplattform",
    "goShop": "Zur Store-Seite gehen",
    "receiveDeadline": "Frist für nachfolgende Einlösung",
    "2Ai": "2 Lachen AI",
    "questionDesc": "Problem-Beschreibung",
    "inputYourQuestion": "Geben Sie hier Ihre Vorschläge oder Kommentare ein。",
    "uploadLimit": "Bis zu 3 lokale Bilder im JPG/PNG/BMP-Format hochladen",
    "email": "E-Mail",
    "contactWay": "Kontaktinformationen",
    "qqNumber": "QQ-Nummer (optional)",
    "submit": "Einreichen"
  },
  "hardwareInfo": {
    "hardwareOverview": "Hardware-Übersicht",
    "copyAllHardwareInfo": "Alle Hardwareinformationen kopieren",
    "processor": "Prozessor",
    "coreCount": "Kerne:",
    "threadCount": "Thread-Anzahl:",
    "currentFrequency": "Aktuelle Frequenz:",
    "currentVoltage": "Aktuelle Spannung:",
    "copy": "Kopieren",
    "releaseDate": "Erscheinungsdatum",
    "codeName": "Code-Name",
    "thermalDesignPower": "Thermische Designleistung",
    "maxTemperature": "Maximale Temperatur",
    "graphicsCard": "Grafikkarte",
    "brand": "Marke:",
    "streamProcessors": "Stream-Prozessor:",
    "Videomemory": "Videospeicher:",
    "busSpeed": "Bustakt",
    "driverInfo": "Treiberinformationen",
    "driverInstallDate": "Treiberinstallationsdatum",
    "hardwareID": "Hardware-ID",
    "motherboard": "Mainboard",
    "chipGroup": "Chipsatz:",
    "BIOSDate": "BIOS-Datum",
    "BIOSVersion": "BIOS-Version",
    "PCIESlots": "PCIe-Slot",
    "PCIEVersion": "Unterstützte PCIe-Version",
    "memory": "Speicher",
    "memoryBarCount": "Anzahl:",
    "totalSize": "Größe:",
    "channelCount": "Kanal:",
    "Specificmodel": "Konkretes Modell",
    "Pellet": "Partikelhersteller",
    "memoryBarEquivalentFrequency": "Effektive Speicherfrequenz:",
    "hardDisk": "Festplatte",
    "hardDiskCount": "Anzahl der Festplatten:",
    "actualCapacity": "Tatsächliche Kapazität",
    "type": "Typ",
    "powerOnTime": "Einschaltzeit",
    "powerOnCount": "Einschaltzyklen",
    "SSDRemainingLife": "Verbleibende Lebensdauer der SSD",
    "partitionInfo": "Partitionsinformation",
    "hardDiskController": "Festplattencontroller",
    "driverNumber": "Laufwerksnummer",
    "display": "Monitor",
    "refreshRate": "Aktualisierungsrate:",
    "screenSize": "Bildschirmgröße:",
    "inches": "Zoll",
    "productionDate": "Produktionsdatum",
    "supportRefreshRate": "Unterstützung der Bildwiederholrate",
    "screenLongAndShort": "Bildschirmgröße",
    "systemInfo": "Systeminformationen",
    "version": "Version",
    "systemInstallDate": "Datum der Systeminstallation",
    "systemBootTime": "Aktuelle Systemstartzeit",
    "systemRunTime": "Laufzeit",
    "Poccupied": "P-Verwendung",
    "Eoccupied": "E Belegt",
    "occupied": "Belegt",
    "temperature": "Temperatur",
    "Pfrequency": "P Frequenz",
    "Efrequency": "E-Frequenz",
    "thermalPower": "Thermische Leistung",
    "frequency": "Frequenz",
    "current": "Aktuell",
    "noData": "Keine Daten",
    "loadHwinfo_SDK": "Fehler beim Laden von Hwinfo_SDK.dll, Hardware-/Sensordaten können nicht gelesen werden",
    "loadHwinfo_SDK_reason": "Mögliche Ursachen dieses Problems:",
    "reason": "Grund",
    "BlockIntercept": "Durch Antivirensoftware blockiert, z.B.: 2345 Antivirensoftware (2345 aktiver Schutzprozess, McAfee aktiver Schutzprozess)",
    "solution": "Lösung:",
    "solution1": "Nachdem Sie die zugehörigen Prozesse geschlossen und deinstalliert haben, starten Sie GamePP neu",
    "solution2": "Ziehen Sie die zugehörigen Geräte aus, und starten Sie GamePP neu",
    "RestartGamePP": "Trennen Sie den Controller, warten Sie auf die Geräteantwort und starten Sie dann GamePP neu",
    "HWINFOcannotrun": "Hwinfo lässt sich nicht ordnungsgemäß ausführen",
    "downloadHWINFO": "Hwinfo herunterladen",
    "openHWINFO": "Kann Hwinfo nach dem Start durch Klicken auf RUN normal geöffnet werden?",
    "hardwareDriverProblem": "Hardware-Treiberprobleme",
    "checkHardwareManager": "Überprüfen Sie den Gerätemanager, um sicherzustellen, dass die Treiber für Hauptplatine und Grafikkarte ordnungsgemäß installiert sind",
    "systemProblem": "Systemprobleme, beispielsweise: Die Verwendung von Aktivierungstools wie Baofeng oder Xiaoma kann zu Treiberlade-Fehlern führen und verhindert die automatische Installation von Windows 7-Systempatches",
    "reinstallSystem": "System neu installieren, um es zu aktivieren, WIN7 herunterladen und installieren: *********-Update",
    "Windows7": "Windows 7: Installieren Sie das SHA-256-Update, Windows 10: Aktivieren Sie mit digitalem Zertifikat, Windows 11 Preview-Version: Deaktivieren Sie die Speicherintegrität",
    "ViolenceActivator": "Wenn Sie unbefugte Aktivierungstools wie Xiaoma verwendet haben, reparieren oder installieren Sie das System bitte neu",
    "MultipleGraphicsCardDrivers": "Auf dem Computer sind Grafikkartentreiber verschiedener Marken installiert, z.B. AMD- und Nvidia-Treiber gleichzeitig",
    "UninstallUnused": "Nachdem Sie die nicht benötigten Grafikkartentreiber deinstalliert haben, starten Sie den Computer neu",
    "OfficialQgroup": "Keiner der oben genannten Gründe trifft zu. Tritt unserer offiziellen QQ-Gruppe bei: 908287288 (Gruppe 5) zur Problemlösung.",
    "ExportHardwareData": "Hardware-Daten exportieren",
    "D3D": "D3D-Verwendung",
    "Total": "Gesamtverbrauch",
    "VRAM": "VRAM-Verbrauch",
    "VRAMFrequency": "VRAM-Frequenz",
    "SensorData": "Sensordaten",
    "CannotGetSensorData": "Fehler beim Abrufen der Sensordaten",
    "LoadingHardwareInfo": "Lade Hardware-Informationen...",
    "ScanTime": "Letzter Scan:",
    "Rescan": "Erneut scannen",
    "Screenshot": "Screenshot",
    "configCopyed": "Die Konfigurationsinformationen wurden in die Zwischenablage kopiert.",
    "LegalRisks": "Potenzielle rechtliche Risiken vorhanden",
    "brandLegalRisks": "Die Anzeige des Markennamens birgt mögliche rechtliche Risiken",
    "professionalVersion": "Professional Edition",
    "professionalWorkstationVersion": "Professionelle Workstation-Ausgabe",
    "familyEdition": "Home Edition",
    "educationEdition": "Bildungsedition",
    "enterpriseEdition": "Enterprise Edition",
    "flagshipEdition": "Premium Edition",
    "familyPremiumEdition": "Familien-Premiumversion",
    "familyStandardEdition": "Familien-Standardversion",
    "primaryVersion": "Grundversion",
    "bit": "Bit",
    "tempWall": "Temperaturgrenze",
    "error": "Fehler",
    "screenshotSuccess": "Screenshot erfolgreich gespeichert",
    "atLeastOneData": "Mindestens einen Datensatz behalten",
    "atMostSixData": "Maximal 6 Datensätze hinzufügen",
    "screenNotActivated": "Nicht aktiviert"
  },
  "psc": {
    "processCoreAssign": "Prozessorkern-Zuordnung",
    "CoreAssign": "Kernzuweisung:",
    "groupName": "Gruppenname:",
    "notGameProcess": "Nicht-Spiel-Prozesse",
    "unNamedProcess": "Unbenannte Gruppe",
    "Group2": "Gruppe",
    "selectTheCore": "Kern auswählen",
    "controls": "Operation",
    "tips": "Hinweis",
    "search": "Suchen",
    "shiftOut": "Auswerfen",
    "ppValue": "PP-Wert",
    "ppDesc": "Der PP-Wert zeigt den historischen Verbrauch von Hardware-Ressourcen an. Höhere Werte bedeuten stärkeren Hardware-Ressourcenverbrauch.",
    "littletips": "Hinweis: Halten Sie den Prozess gedrückt, um ihn in die linke Gruppe zu ziehen",
    "warning1": "Das Auswählen von Kernthreads zwischen Gruppen kann die Leistung beeinflussen. Es wird empfohlen, Kerne derselben Gruppe zu verwenden.",
    "warning2": "Möchten Sie diesen Gruppennamen wirklich leer lassen?",
    "warning3": "Der Core-Zuweisungseffekt wird nach dem Löschen ungültig. Möchten Sie die Gruppe wirklich löschen?",
    "allprocess": "Alle Prozesse",
    "pleaseCheckProcess": "Bitte Prozess aktivieren",
    "dataSaveDesktop": "Daten wurden auf den Desktop gespeichert.",
    "createAGroup": "Gruppe erstellen",
    "delGroup": "Gruppe löschen",
    "Group": "Gruppe",
    "editGroup": "Gruppe bearbeiten",
    "groupinfo": "Gruppeninformation",
    "moveOutGrouping": "Aus der Gruppe entfernen",
    "createANewGroup": "Neue Gruppe erstellen",
    "unallocatedCore": "Nicht zugewiesener Kern",
    "inactiveProcess": "Inaktiver Prozess",
    "importGroupingScheme": "Gruppierungsprofil importieren",
    "derivedPacketScheme": "Gruppenkonfiguration exportieren",
    "addNowProcess": "Aktuellen laufenden Prozess hinzufügen",
    "displaySystemProcess": "Systemprozesse anzeigen",
    "max64": "Maximal 64 Threads auswählbar",
    "processName": "Prozessname",
    "chooseCurProcess": "Aktuellen Prozess auswählen",
    "selectNoProcess": "Kein Prozess ausgewählt",
    "coreCount": "Kerne",
    "threadCount": "Threads",
    "process": "Prozess",
    "plzInputProcessName": "Geben Sie den Prozessnamen ein, um manuell hinzuzufügen",
    "has_allocation": "Prozesse mit Thread-Zuordnungsschemas",
    "not_made": "Sie haben keinen Prozessen Kerne zugewiesen",
    "startUse": "Optimierung aktivieren",
    "stopUse": "Optimierung deaktivieren",
    "threadAllocation": "Thread-Zuweisung",
    "configProcess": "Prozesskonfiguration",
    "selectThread": "Thread auswählen",
    "hyperthreadingState": "Hyper-Threading-Status",
    "open": "Aktiviert",
    "notYetUnlocked": "Deaktiviert",
    "nonhyperthreading": "Ohne Hyper-Threading",
    "intervalSelection": "Intervallauswahl",
    "invertSelection": "Auswahl invertieren",
    "description": "Sperrt Spielprozesse auf spezifischen CPU-Kernen und isoliert Hintergrundprogramme intelligent. Erhöht effektiv die FPS-Obergrenze und stabilisiert die Bildrate. Reduziert plötzliche Spiel-Lags und Frame-Rate-Einbrüche, entfesselt die volle Leistungsfähigkeit von Mehrkernprozessoren und gewährleistet stets eine flüssige, hochperformante Spielwiedergabe.",
    "importSuccess": "Import erfolgreich",
    "importFailed": "Import fehlgeschlagen"
  },
  "InGameMonitor": {
    "onoffingame": "In-Spiel-Überwachung aktivieren/deaktivieren:",
    "InGameMonitor": "Überwachung im Spiel",
    "CustomMode": "Benutzerdefinierter Modus",
    "Developing": "In Entwicklung...",
    "NewMonitor": "Neues Überwachungselement hinzufügen",
    "Data": "Parameter",
    "Des": "Hinweis",
    "Function": "Funktionalität",
    "Editor": "Bearbeiten",
    "Top": "Oben anheften",
    "Delete": "Löschen",
    "Use": "Verwenden",
    "DragToSet": "Nachdem Sie das Panel aufgerufen haben, können Sie es zum Konfigurieren ziehen",
    "MonitorItem": "Überwachungselement",
    "addMonitorItem": "Monitoring-Element hinzufügen",
    "hide": "Verstecken",
    "show": "Anzeigen",
    "generalstyle": "Allgemeine Einstellungen",
    "restoredefault": "Auf Standardwerte zurücksetzen",
    "arrangement": "Anordnung",
    "horizontal": "Horizontal",
    "vertical": "Vertikal",
    "monitorposition": "Überwachungsort",
    "canquickselectposition": "Ort auf der linken Karte schnell auswählen",
    "curposition": "Aktueller Standort:",
    "background": "Hintergrund",
    "backgroundcolor": "Hintergrundfarbe:",
    "font": "Schriftart",
    "fontStyle": "Schriftstil",
    "fontsize": "Schriftgröße:",
    "fontcolor": "Schriftfarbe:",
    "style": "Stil:",
    "style2": "Stil",
    "performance": "Leistung",
    "refreshTime": "Aktualisierungszeit:",
    "goGeneralSetting": "Gehe zu Allgemeine Einstellungen",
    "selectMonitorItem": "Monitorierungselement auswählen",
    "selectedSensor": "Ausgewählter Sensor:",
    "showTitle": "Titel anzeigen",
    "hideTitle": "Titel ausblenden",
    "showStyle": "Anzeigemodus:",
    "remarkSize": "Größe der Anmerkung:",
    "remarkColor": "Anmerkungsfarbe:",
    "parameterSize": "Parametergröße:",
    "parameterColor": "Parameterfarbe:",
    "lineChart": "Liniendiagramm",
    "lineColor": "Farbe der gebrochenen Linie:",
    "lineThickness": "Linienstärke:",
    "areaHeight": "Bereichshöhe:",
    "sort": "Sortieren",
    "displacement": "Verschiebung:",
    "up": "Nach oben verschieben",
    "down": "Nach unten verschieben",
    "bold": "Fett",
    "stroke": "Umriss",
    "text": "Text",
    "textLine": "Text + Liniendiagramm",
    "custom": "Benutzerdefiniert",
    "upperLeft": "Oben links",
    "upper": "Mittlere obere",
    "upperRight": "Oben rechts",
    "Left": "Linkes Zentrum",
    "middle": "Mitte",
    "Right": "Rechte Mitte",
    "lowerLeft": "Unten links",
    "lower": "Mittel Unten",
    "lowerRight": "Unten rechts",
    "notSupport": "Das Periphergerät unterstützt das Anzeigen oder Verstecken durch Klicken nicht",
    "notSupportRate": "Die Rendite rate lässt sich nicht durch Klicken ein-/ausblenden",
    "notFindSensor": "Sensor nicht gefunden. Klicken Sie zum Ändern.",
    "monitoring": "Überwachung",
    "condition": "Bedingung",
    "bigger": "Größer als",
    "smaller": "Weniger als",
    "biggerThan": "Überschreitet Schwelle",
    "biggerThanthreshold": "Größer als Schwellenwert-Prozentsatz",
    "smallerThan": "Unterhalb des Schwellenwerts",
    "smallerThanthreshold": "Weniger als Schwellenwertprozentsatz",
    "biggerPercent": "Prozentsatz der Abnahme des aktuellen Werts",
    "smallerPercent": "Prozentsatz der aktuellen Wertsteigerung",
    "replay": "Funktion Sofortwiedergabe",
    "screenshot": "Bildschirmaufnahme",
    "text1": "Sensorwert bei",
    "text2": ", und in",
    "text3": "Wartezeit",
    "text4": "Wenn in den nächsten Sekunden kein höherer Wert angezeigt wird, wird sofort ausgelöst",
    "text5": "Nach jedem Replay-Auslösen den Schwellenwert auf den zum Auslösezeitpunkt geltenden Wert aktualisieren, um häufige Auslösungen zu reduzieren",
    "text6": "Anzeigen des aktuellen Schwellenwerts zum Auslösen der Replay-Funktion",
    "text7": "Anzeigen der Sensorwerte",
    "text8": "Initialen Schwellenwert überschreiten",
    "text9": "Unterhalb des Anfangsschwellenwerts",
    "text10": "Anfangsschwellwertanzahl",
    "initThreshold": "Anfänglicher Schwellenwert",
    "curThreshold": "Aktueller Schwellenwert:",
    "curThreshold2": "Aktueller Schwellenwert",
    "resetCurThreshold": "Aktuellen Schwellenwert zurücksetzen",
    "action": "Funktion aktivieren",
    "times": "mal",
    "percentage": "Prozent",
    "uninstallobs": "Aufnahmemodul nicht heruntergeladen",
    "install": "Herunterladen",
    "performanceAndAudioMode": "Leistungs- und Audio-Kompatibilitätsmodus",
    "isSaving": "Wird gespeichert",
    "video_replay": "Sofortige Wiederholung",
    "saved": "Gespeichert",
    "loadQualitysScheme": "Grafikpräset laden",
    "notSet": "Nicht konfiguriert",
    "mirrorEnable": "Der Filter ist aktiviert",
    "canBeTurnedOff": "Zurück",
    "mirrorClosed": "Spiel-Filter deaktiviert",
    "closed": "Geschlossen",
    "openMirror": "Filter aktivieren",
    "wonderfulScenes": "Höhepunkte",
    "VulkanModeHaveProblem": "Der Vulkan-Modus weist Kompatibilitätsprobleme auf",
    "suggestDxMode": "Es wird empfohlen, in den Dx-Modus zu wechseln",
    "functionNotSupported": "Funktion wird nicht unterstützt",
    "NotSupported": "Nicht unterstützt",
    "gppManualRecording": "GamePP, manuelle Aufzeichnung",
    "perfRecordsHaveBeenSaved": "Leistungsprotokoll wurde gespeichert",
    "redoClickF8": "Um die Aufzeichnung fortzusetzen, drücken Sie erneut F8.",
    "startIngameMonitor": "Aktiviert die In-Game-Überwachungsfunktion",
    "inGameMarkSuccess": "Im Spiel wurde das Markieren erfolgreich ausgeführt",
    "recordingFailed": "Aufnahme fehlgeschlagen",
    "recordingHasNotDownload": "Die Aufnahmefunktion wurde noch nicht heruntergeladen",
    "hotkeyDetected": "Funktionstastenkonflikt erkannt",
    "plzEditIt": "Bitte ändern Sie die Einstellungen innerhalb der Software und verwenden Sie diese dann",
    "onePercentLowFrame": "1% Niedrige Frames",
    "pointOnePercentLowFrame": "0.1% Niedrige Frames",
    "frameGenerationTime": "Frame-Generierungszeit",
    "curTime": "Aktuelle Uhrzeit",
    "runTime": "Laufzeitdauer",
    "cpuTemp": "CPU-Temperatur",
    "cpuUsage": "CPU-Auslastung",
    "cpuFreq": "CPU-Taktfrequenz",
    "cpuPower": "CPU-Thermaldesignleistung",
    "gpuTemp": "GPU-Temperatur",
    "gpuUsage": "GPU-Auslastung",
    "gpuPower": "GPU-thermische Leistungsaufnahme",
    "gpuFreq": "GPU-Taktfrequenz",
    "memUsage": "Speichernutzung"
  },
  "LoginArea": {
    "login": "Anmelden",
    "loginOut": "Abmelden",
    "vipExpire": "Abgelaufen",
    "remaining": "Verbleibend",
    "day": "Himmel",
    "openVip": "Mitgliedschaft aktivieren",
    "vipPrivileges": "Mitgliedsrechte",
    "rechargeRenewal": "Aufladen und Erneuern",
    "Exclusivefilter": "Filter",
    "configCloudSync": "Konfiguration der Cloud-Synchronisation",
    "comingSoon": "Demnächst verfügbar"
  },
  "GameMirror": {
    "filterStatus": "Filterstatus",
    "filterPlan": "Filtervorgabe",
    "filterShortcut": "Filter-Tastenkombinationen",
    "openCloseFilter": "Filter aktivieren/deaktivieren:",
    "effectDemo": "Effekt-Demonstration",
    "demoConfig": "Demo-Konfiguration",
    "AiFilter": "Die Effekte des KI-Filters sind den In-Game-Effekten unterworfen",
    "AiFilterFAQ": "Häufige Probleme mit AI-Filtern",
    "gamePPAiFilter": "GamePP AI-Filter",
    "gamePPAiFilterVip": "GamePP VIP-exklusiver KI-Filter, passt dynamisch Filterparameter basierend auf Spielszenarien an, optimiert visuelle Effekte und verbessert das Spielerlebnis.",
    "AiMingliangTips": "KI-Helligkeit: Empfohlen für den Fall, dass der Spielebildschirm zu dunkel ist.",
    "AiBrightTips": "AI Lebendig: Empfohlen für die Verwendung, wenn die Spielgrafik zu dunkel erscheint.",
    "AiDarkTips": "KI-Dimming: Empfohlen für den Einsatz bei zu lebhaften Spieldarstellungen.",
    "AiBalanceTips": "KI-Gleichgewicht: Geeignet für die meisten Spielszenarien",
    "AiTips": "Hinweise: Der AI-Filter muss durch Drücken der Tastenkombination im Spiel verwendet werden",
    "AiFilterUse": "Bitte verwenden Sie innerhalb des Spiels",
    "AiFilterAdjust": "Tastenkombination für AI-Filter anpassen",
    "Bright": "Lebendig",
    "Soft": "Sanft",
    "Highlight": "Hervorheben",
    "Film": "Film",
    "Benq": "BenQ",
    "AntiGlare": "Antiblendschutz",
    "HighSaturation": "Hohe Sättigung",
    "Brightness": "Lebendig",
    "Day": "Tag",
    "Night": "Nacht",
    "Nature": "Natürlich",
    "smooth": "Fein",
    "elegant": "Geschmackvoll",
    "warm": "Warmton",
    "clear": "Klar",
    "sharp": "Schärfe",
    "vivid": "Dynamisch",
    "beauty": "Vivid",
    "highDefinition": "Hochauflösend",
    "AiMingliang": "KI Hell",
    "AiBright": "KI Lebendig",
    "AiDark": "AI Abgedunkelt",
    "AiBalance": "KI-Ausgleich",
    "BrightTips": "Der lebendige Filter eignet sich für Casual-, Action- oder Adventure-Spiele und verstärkt die Farbsättigung, um die Spielgrafik dynamischer und ansprechender zu gestalten.",
    "liangTips": "Filter werden empfohlen, wenn画面 des Spiels zu dunkel ist.",
    "anTips": "Der Filter wird empfohlen, wenn das Spielbild zu dunkel ist.",
    "jianyiTips": "Filter wird empfohlen, wenn die Spielgrafik zu lebhaft ist.",
    "shiTips": "Der Filter eignet sich für die meisten Spiele-Szenen.",
    "shi2Tips": "Der Filter eignet sich für Casual-, Action- oder Adventure-Spiele, erhöht die Farbsättigung und macht die Spielgrafik lebendiger und ansprechender.",
    "ruiTips": "Feine Filterfarben, sanfte Beleuchtungseffekte, geeignet für Traum-, Wärme- oder Nostalgieszenen",
    "qingTips": "Heller Ton, hoher Kontrast, scharfe Details, ideal für lebendige und gut beleuchtete Szenen.",
    "xianTips": "Höhere Kontrast- und Helligkeitseinstellungen gewährleisten klare Details in dunklen Szenen ohne Verzerrung und angenehme Betrachtung in hellen Szenen.",
    "dianTips": "Valenzia: Optimieren Sie moderat Helligkeit und Farbsättigung für ein filmähnliches Darstellungserlebnis",
    "benTips": "Reduziert den Effekt des weißen Lichts, um reine weiße Spielszenen weniger blendend zu gestalten",
    "fangTips": "Optimiert für Open-World- und Adventure-Spiele, erhöht Helligkeit und Kontrast für schärfere Darstellung",
    "jiaoTips": "Geeignet für Rollenspiele und Simulationsspiele, ausgewogene Farbtöne, verbesserte visuelle Realität",
    "jieTips": "Optimiert für geschichtenreiche, emotionale Spiele, verbessert Details und Weichheit für eine präzisere Bildqualität",
    "jingTips": "Optimiert für Action- und Wettbewerbspiele, verbessert die Klarheit und den Kontrast für schärfere Bilder",
    "xiuTips": "Optimiert für heilende und Casual-Spiele, verstärkt warme Töne und Weichheit, um eine gemütlichere Atmosphäre zu erzeugen",
    "qihuanTips": "Geeignet für Szenen mit reichhaltigen Fantasy-Elementen und lebendigen Farben, verstärkt die Farbsättigung zur Erzeugung intensiver visueller Effekte",
    "shengTips": "Farben und Details verstärken, um Lebendigkeit und Realismus der Szene hervorzuheben,",
    "sheTips": "Eignet sich für FPS, Rätsel- oder Abenteuer-Spiele, verstärkt Details und Kontraste und verbessert die Realismus des Spielwelt.",
    "she2Tips": "Für Shooter, Rennspiele oder Kampfspiele geeignet, hebt hochauflösende Details und dynamische Darstellung hervor, um die Intensität und visuelle Effekte des Spielerlebnisses zu verstärken",
    "an2Tips": "Verbessert die Klarheit der Szene in dunklen Umgebungen, geeignet für dunkle oder nächtliche Szenen.",
    "wenTips": "Eignet sich für kunstvoll, Abenteuer oder Relax-Spiele, erzeugt weiche Farbtöne und Lichtschatten-Effekte, um Szenerie mit Eleganz und Wärme zu versehen。",
    "jing2Tips": "Geeignet für Wettbewerbs-, Musikrhythmus- oder nächtliche Stadtszenario-Spiele, hervorheben lebendiger Farben und Beleuchtungseffekte,",
    "jing3Tips": "Optimiert für Wettbewerbs-, Action- oder Fantasy-Spiele, verstärkt den Farbkontrast, wodurch die Darstellung lebendiger und dynamischer wird.",
    "onlyVipCanUse": "Dieser Filter ist nur für VIP-Nutzer verfügbar"
  },
  "GameRebound": {
    "noGame": "Keine Spielprotokolle vorhanden",
    "noGameRecord": "Noch keine Spielverläufe vorhanden! Starten Sie jetzt eine Runde!",
    "gameDuration": "Spieldauer heute:",
    "gameElectricity": "Täglicher Stromverbrauch",
    "degree": "Grad",
    "gameCo2": "CO₂-Emission des Tages:",
    "gram": "Schlüssel",
    "manualRecord": "Manuelle Aufzeichnung",
    "recordDuration": "Aufnahmedauer:",
    "details": "Details",
    "average": "Durchschnitt",
    "minimum": "Mindestwert",
    "maximum": "Maximum",
    "occupancyRate": "Auslastung",
    "voltage": "Spannung",
    "powerConsumption": "Leistungsaufnahme",
    "start": "Starten:",
    "end": "Beenden",
    "Gametime": "Spieldauer:",
    "Compactdata": "Datenoptimierung",
    "FullData": "Vollständige Daten",
    "PerformanceAnalysis": "Leistungsanalyse",
    "PerformanceAnalysis2": "Ereignisbericht",
    "HardwareStatus": "Hardware-Status",
    "totalPower": "Gesamtstromverbrauch",
    "TotalEmissions": "Gesamtemission",
    "PSS": "Hinweis: Die unten stehenden Diagrammdaten repräsentieren Durchschnittswerte",
    "FrameGenerationTime": "Frame-Erstellungszeit",
    "GameResolution": "Spielauflösung",
    "FrameGenerationTimeTips": "Dieser Datenpunkt ist außergewöhnlich hoch und nicht in die Statistik einbezogen",
    "FrameGenerationTimeTips2": "Dieser Datensatz ist ungewöhnlich niedrig und daher nicht in die Statistik einbezogen",
    "noData": "Keine",
    "ProcessorOccupancy": "CPU-Auslastung",
    "ProcessorFrequency": "Prozessorfrequenz",
    "ProcessorTemperature": "Prozessortemperatur",
    "ProcessorHeatPower": "Thermal Design Power",
    "GraphicsCardOccupancy": "Nutzung von D3D auf der Grafikkarte",
    "GraphicsCardOccupancyTotal": "Gesamtnutzung der Grafikkarte",
    "GraphicsCardFrequency": "GPU-Frequenz",
    "GraphicsCardTemperature": "GPU-Temperatur",
    "GraphicsCardCoreTemperature": "GPU-Kern-Hotspot-Temperatur",
    "GraphicsCardHeatPower": "Thermische Leistung der GPU",
    "GraphicsCardMemoryTemperature": "GPU-Speichertemperatur",
    "MemoryOccupancy": "Speicherverbrauch",
    "MemoryTemperature": "Speichertemperatur",
    "MemoryPageFaults": "Speicher-Paging-Unterbrechung",
    "Duration": "Dauer",
    "Time": "Zeit",
    "StartStatistics": "Statistik starten",
    "Mark": "Markierung",
    "EndStatistics": "Statistik beenden",
    "LineChart": "Linien-Diagramm",
    "AddPointInGame_m1": "Drücken Sie innerhalb des Spiels",
    "AddPointInGame_m2": "Markierungspunkt hinzufügbar",
    "LeftMouse": "Linksklick zum Wechseln zwischen Anzeigen/Ausblenden, Rechtsklick zum Ändern der Farbe",
    "DeleteThisLine": "Polylinie löschen",
    "AddCurve": "Kurve hinzufügen",
    "AllCurvesAreHidden": "Alle Kurvendiagramme sind ausgeblendet",
    "ThereAreSamplingData": "Gesamtstichprobendaten:",
    "Items": "Eintrag",
    "StatisticsData": "Statistik",
    "electricity": "Stromverbrauch",
    "carbonEmission": "Kohlenstoffemissionen",
    "carbonEmissionTips": "Kohlendioxid-Emissionen (kg) = Stromverbrauch (kWh) × 0.785",
    "D3D": "D3D-Verwendung:",
    "TOTAL": "Gesamtverbrauch:",
    "Process": "Prozess:",
    "L3Cache": "L3-Cache:",
    "OriginalFrequency": "Originalfrequenz:",
    "MaximumBoostFrequency": "Max. Turbo Boost:",
    "DriverVersion": "Treiber Version:",
    "GraphicsCardMemoryBrand": "VRAM-Marke:",
    "Bitwidth": "Busbreite",
    "System": "System:",
    "Screen": "Bildschirm",
    "Interface": "Schnittstelle:",
    "Channel": "Kanal:",
    "Timing": "Sequenz:",
    "Capacity": "Kapazität:",
    "Generation": "Algebra",
    "AddPoint_m1": "Drücken Sie im Spiel",
    "AddPoint_m2": "Markierungspunkt hinzufügen",
    "Hidden": "Ausgeblendet",
    "Totalsampling": "Gesamtstichprobendaten:",
    "edition": "Treiber-Version:",
    "MainHardDisk": "Primäre Festplatte",
    "SetAsStartTime": "Als Startzeit festlegen",
    "SetAsEndTime": "Als Endzeit festlegen",
    "WindowWillBe": "Das Leistungsstatistik-Fenster befindet sich in",
    "After": "Schließen nach",
    "NoLongerPopUpThisGame": "Dieses Spiel wird nicht mehr angezeigt",
    "HideTemperatureReason": "Temperaturgrund ausblenden",
    "HideTemperatureReason2": "Ereignisbericht ausblenden",
    "HideOtherReason": "Andere Gründe Ausblenden",
    "CPUanalysis": "Leistungsanalyse der CPU",
    "TemperatureCause": "Temperaturursache",
    "tempSensorEvent": "Temperatursensoreignis",
    "NoTemperatureLimitation": "Keine temperaturbedingte CPU-Leistungsbegrenzung erkannt. Ihr Kühlungssystem ist optimal für die Anforderungen dieses Spiels geeignet.",
    "NoTemperatureLimitation2": "Keine Temperatursensorevents, Ihr Kühlsystem kann die Anforderungen dieses Spiels perfekt bewältigen.",
    "performanceis": "In ausgewähltem",
    "Inside": "Intern,",
    "TheStatisticsTimeOf": "Der statistische Zeitraum erfüllt die relevanten Auslösebedingungen. Der Grund mit der höchsten Auslösefrequenz ist",
    "limited": "Anteil der Gesamtzeit aufgrund temperaturbedingter Leistungseinschränkungen",
    "SpecificReasons": "Konkrete Ursachen und ihr Anteil an Temperaturproblemen:",
    "OptimizationSuggestion": "Optimierungsvorschläge:",
    "CPUtemperature": "CPU-Temperatur ist überhitzt. Bitte prüfen/verbessern Sie die Kühlumgebung der CPU.",
    "CPUoverheat": "CPU-Überhitzung aufgrund der Motherboard-Stromversorgung. Überprüfen Sie die Motherboard-bezogenen Einstellungen oder verbessern Sie die Kühlumgebung.",
    "OtherReasons": "Sonstige Gründe",
    "NoPowerSupplyLimitation": "Die Leistung Ihres CPUs ist nicht durch Stromversorgung oder Energieverbrauch eingeschränkt. Die Stromverbrauchseinstellungen Ihres BIOS sind optimal für die Anforderungen dieses Spiels.",
    "PowerSupplyLimitation": "Durch Stromversorgung/Energieverbrauch bedingte Leistungseinschränkungen",
    "SpecificReasonsInOtherReasons": "Spezifische Ursache und ihr Anteil an anderen Ursachen:",
    "PleaseCheckTheMainboard": "Überprüfen Sie den Stromversorgungszustand des Mainboards oder passen Sie die BIOS-Leistungseinstellungen an, um CPU-Leistungseinschränkungen durch andere Faktoren zu beheben",
    "CPUcoretemperature": "Kern Temperatur hat Tj,Max erreicht und wird begrenzt",
    "CPUCriticalTemperature": "CPU-Temperatur erreicht kritischen Bereich",
    "CPUCircuitTemperature": "CPU-Gehäuse/Ringbus durch Erreichen von Tj,Max eingeschränkt",
    "CPUCircuitCriticalTemperature": "CPU Package/Ringbus hat die kritische Temperatur erreicht",
    "CPUtemperatureoverheating": "CPU-Überhitzung erkannt, dies wird eine automatische Frequenzreduktion auslösen, um die Temperatur zu senken und Hardwareprobleme zu vermeiden",
    "CPUoverheatingtriggered": "Bei Überhitzung wird der Kühlmechanismus aktiviert und die CPU passt Spannung und Frequenz zur Reduzierung des Stromverbrauchs und der Temperatur an",
    "CPUPowerSupplyOverheating": "CPU ist aufgrund schwerer Überhitzung der Mainboard-Stromversorgung eingeschränkt",
    "CPUPowerSupplyLimitation": "CPU eingeschränkt aufgrund von Überhitzung der Mainboard-Stromversorgung",
    "CPUMaximumPowerLimitation": "Kern ist durch maximale Leistungsbegrenzung eingeschränkt",
    "CPUCircuitPowerLimitation": "CPU-Gehäuse/Ringbus hat das Leistungslimit erreicht",
    "CPUElectricalDesignLimitation": "Elektrische Design-Begrenzungen aktivieren (inkl. ICCmax Stromgrenze, PL4 Spitzenleistungs-Grenze, SVID Spannungs-Begrenzung usw.)",
    "CPULongTermPowerLimitation": "Langfristige CPU-Leistungsaufnahme hat das Limit erreicht",
    "CPULongTermPowerinstantaneous": "Die sofortige Stromaufnahme der CPU hat das Limit erreicht",
    "CPUPowerLimitation": "Turbo-Frequenz-Abnahme-Mechanismus der CPU, typischerweise durch BIOS oder spezifische Software eingeschränkt",
    "CPUPowerWallLimitation": "CPU-Leistungsgrenze",
    "CPUcurrentwalllimit": "CPU-Strombegrenzung",
    "AiAgent": "GamePP-Agent (AI Agent)",
    "AgentDesc": "Zurück zur Startseite",
    "fnBeta": "Diese Funktion befindet sich derzeit in der Testphase auf Einladung. Ihr GamePP-Konto hat noch keinen Testzugang erhalten.",
    "getAIReport": "KI-Bericht abrufen",
    "waitingAi": "Warten auf die Fertigstellung der Berichterstattung",
    "no15mins": "Spieldauer unter 15 Minuten, kein gültiger KI-Bericht verfügbar",
    "timeout": "Anfrage an den Server ist abgelaufen",
    "agentId": "Agent-ID:",
    "reDo": "Bericht neu generieren",
    "text2": "GamePP-Agent: Online-AI-Analysedaten, folgende Inhalte wurden von AI generiert und dienen nur zur Referenz.",
    "amdAiagentTitle": "GamePP Agent: AMD Ryzen AI Analysebericht, der folgende Inhalt wurde von AI generiert und dient nur als Referenz.",
    "noCurData": "Keine aktuellen Daten",
    "dataScreening": "Datenfilterung",
    "dataScreeningDescription": "Dieses Feature dient dazu, Datenstatistiken von nicht effektiven Spielzeitsabschnitten wie Kartenladevorgängen oder Wartezuständen im Lobbybereich auszuschließen. 0 bedeutet, dass keine Ausschlüsse erfolgen。",
    "excessivelyHighParameter": "Übermäßige Parameter",
    "tooLowParameter": "Zu niedrige Parameter",
    "theMaximumValueIs": "Maximalwert ist",
    "theMinimumValueIs": "Mindestwert ist",
    "exclude": "Ausschließen",
    "dataStatisticsAtThatTime": "Datenstatistik zu dieser Zeit",
    "itHasBeenGenerated": "Erstellung abgeschlossen,",
    "clickToView": "Klicken Sie hier an",
    "onlineAnalysis": "Online-Analyse",
    "amdRyzenAILocalAnalysis": "AMD Ryzen AI Lokale Analyse",
    "useTerms": "Nutzungsbedingungen:",
    "term1": "1. Ryzen AI Max Prozessor oder Ryzen Al 300-Serie Prozessor",
    "term2": "2.AMD NPU Treiber Version",
    "term3": "3. Integrierte Grafik aktivieren",
    "conformsTo": "Entsprechen",
    "notInLineWith": "Nicht konform",
    "theVersionIsTooLow": "Version zu niedrig",
    "canNotUseAmdNpu": "Ihre Konfiguration erfüllt die Anforderungen nicht. Die AMD NPU-Objektanalyse kann nicht verwendet werden。",
    "unusable": "Nicht verfügbar",
    "downloadTheFile": "Datei herunterladen",
    "downloadSource": "Downloadquelle:",
    "fileSize": "Dateigröße: ca. 8,34 GB",
    "cancelDownload": "Download abbrechen",
    "filePath": "Dateipfad",
    "generateAReport": "Bericht generieren",
    "fileMissing": "Datei fehlt. Neuer Download erforderlich。",
    "downloading": "Herunterladen...",
    "theModelConfigurationLoadingFailed": "Die Modellkonfiguration konnte nicht geladen werden",
    "theModelDirectoryDoesNotExist": "Das Modell-Verzeichnis existiert nicht",
    "thereIsAMistakeInReasoning": "Fehlschluss",
    "theInputExceedsTheModelLimit": "Eingabe überschreitet das Modell-Limit",
    "selectModelNotSupport": "Der ausgewählte Download-Modus wird nicht unterstützt",
    "delDirFail": "Fehler beim Löschen des vorhandenen Model-Verzeichnisses",
    "failedCreateModelDir": "Erstellen des Modellverzeichnisses fehlgeschlagen",
    "modelNotBeenFullyDownload": "Modelle wurden nicht vollständig heruntergeladen",
    "agentIsThinking": "Jiajia Agent denkt nach",
    "reasoningModelFile": "Modell-Datei für Inferenz",
    "modelReasoningTool": "Modell-Verarbeitungstool"
  },
  "SelectSensor": {
    "DefaultSensor": "Standard-Sensor",
    "Change": "Ändern",
    "FanSpeed": "Lüftergeschwindigkeit",
    "MainGraphicsCard": "Hauptgrafikkarte",
    "SetAsMainGraphicsCard": "Als Haupt-GPU festlegen",
    "GPUTemperature": "GPU-Temperatur",
    "GPUHeatPower": "Thermische Leistung der GPU",
    "GPUTemperatureD3D": "GPU-D3D-Nutzung",
    "GPUTemperatureTOTAL": "GPU-Gesamtlast",
    "GPUTemperatureCore": "GPU-Core-Hotspot-Temperatur",
    "MotherboardTemperature": "Mainboard-Temperatur",
    "MyAttention": "Meine Favoriten",
    "All": "Alle",
    "Unit": "Einheit:",
    "NoAttention": "Nicht beobachtete Sensoren",
    "AttentionSensor": "Überwachte Sensoren (Beta)",
    "GoToAttention": "Zum Fokus wechseln",
    "CancelAttention": "Entfolgen",
    "noThisSensor": "Kein Sensor",
    "deviceAbout": "Zusammenhängend mit Peripheriegeräten",
    "deviceBattery": "Batteriestand der Peripheriegeräte",
    "testFunction": "Testfunktion",
    "mouseEventRate": "Abfragefrequenz",
    "relatedWithinTheGame": "Spielbezogen",
    "winAbout": "System",
    "trackDevicesBattery": "Peripheriebatteriepegel verfolgen",
    "ingameRealtimeMouseRate": "Aktuelle Maus-Abtastrate im Echtzeit-Spielbetrieb",
    "notfoundDevice": "Kein unterstütztes Gerät gefunden",
    "deviceBatteryNeedMythcool": "Liste der unterstützten Batteriedarstellung-Geräte: (erfordert Myth.Cool)",
    "vkm1mouse": "Valkyrie M1-Maus",
    "vkm2mouse": "Valkyrie M2-Maus",
    "vk99keyboard": "Valkyrie 99 Magnet-Achsen-Tastatur",
    "logitechProWireless": "Logitech Pro Wireless",
    "logitechProXSUPERLIGHT": "Logitech PRO X SUPERLIGHT",
    "logitechProXSUPERLIGHT2": "Logitech PRO",
    "logitechPro2LIGHTSPEED": "Logitech PRO 2 LIGHTSPEED",
    "steelSeriesArctisGameBuds": "SteelSeries Arctis GameBuds",
    "steelSeriesArctis7PPlus": "SteelSeries Arctis 7P Plus",
    "razerV3": "Razer Valkyrie V3 Profi",
    "razerV2": "Razer Viper V2 Professional Edition",
    "wireless": "Kabellos",
    "logitechNeedGhub": "Wenn Logitech nicht in der Lage ist, das Gerätemodell abzurufen, muss GHUB heruntergeladen werden",
    "chargingInProgress": "Laden",
    "inHibernation": "Im Schlafmodus"
  },
  "video": {
    "videoRecord": "Videoaufnahme",
    "recordVideo": "Aufgenommenes Video",
    "scheme": "Profil",
    "suggestScheme": "Empfohlener Plan",
    "text1": "Bei dieser Konfiguration beträgt die Videogröße von 1 Minute etwa",
    "text2": "Diese Funktion beansprucht zusätzliche Systemressourcen",
    "low": "Niedrig",
    "mid": "Startseite",
    "high": "Hoch",
    "1080p": "Nativ",
    "RecordingFPS": "Aufnahme FPS",
    "bitRate": "Video-Bitrate",
    "videoResolution": "Videoauflösung",
    "startStopRecord": "Aufnahme starten/stoppen",
    "instantReplay": "Echtzeit-Wiederholung",
    "instantReplayTime": "Sofortige Wiedergabedauer",
    "showIngame": "Steuerungspanel im Spiel starten",
    "CaptureMode": "Erfassungsmethode",
    "gameWindow": "Spiel-Fenster",
    "desktopWindow": "Desktop-Fenster",
    "fileSavePath": "Dateispeicherpfad",
    "selectVideoSavePath": "Speicherpfad für Aufnahme auswählen",
    "diskFreeSpace": "Freier Speicherplatz auf der Festplatte:",
    "edit": "Ändern",
    "open": "Aufrufen",
    "displayMouse": "Mauszeiger anzeigen",
    "recordMicrophone": "Mikrofon aufnehmen",
    "gameGraphics": "Originalanzeige des Spiels"
  },
  "Setting": {
    "common": "Allgemein",
    "personal": "Personalisierung",
    "messageNotification": "Benachrichtigungen",
    "sensorReading": "Sensormesswerte",
    "OLEDscreen": "OLED-Burn-in-Schutz",
    "performanceStatistics": "Leistungsstatistik",
    "shortcut": "Tastenkombination",
    "ingameSetting": "Spiel-Einstellungen speichern",
    "other": "Andere",
    "otherSettings": "Weitere Einstellungen",
    "GeneralSetting": "Allgemeine Einstellungen",
    "softwareVersion": "Softwareversion",
    "checkForUpdates": "Auf Updates prüfen",
    "updateNow": "Jetzt aktualisieren",
    "currentVersion": "Aktuelle Version",
    "latestVersion": "Neueste Version",
    "isLatestVersion": "Die aktuelle Version ist bereits die neueste.",
    "functionModuleUpdate": "Aktualisierung des Funktionsmoduls",
    "alwaysUpdateModules": "Alle installierten Funktionsmodule auf der neuesten Version halten",
    "lang": "Sprache",
    "bootstrap": "Autostart",
    "powerOn_m1": "Starten",
    "powerOn_m2": "Automatischer Start nach Sekunden",
    "defaultDelay": "Standard: 40 Sekunden",
    "followSystemScale": "Systemvergrößerung folgen",
    "privacySettings": "Datenschutzeinstellungen",
    "JoinGamePPPlan": "Treten Sie dem GamePP-Benutzererfahrungsverbesserungsprogramm bei",
    "personalizedSetting": "Personalisierung",
    "restoreDefault": "Standardwerte wiederherstellen",
    "color": "Farbe",
    "picture": "Bild",
    "video": "Video",
    "browse": "Durchsuchen",
    "clear": "Löschen",
    "mp4VideoOrPNGImagesCanBeUploaded": "MP4-Videos oder PNG-Bilder hochladen",
    "transparency": "Transparenz",
    "backgroundColor": "Hintergrundfarbe",
    "textFont": "Hauptschriftart",
    "message": "Nachricht",
    "enableInGameNotifications": "Benachrichtigungen im Spiel aktivieren",
    "messagePosition": "Anzeigeposition im Spiel",
    "leftTop": "Obere linke Ecke",
    "leftCenter": "Linkes Zentrum",
    "leftBottom": "Untere linke Ecke",
    "rightTop": "Obere rechte Ecke",
    "rightCenter": "Rechte Mitte",
    "rightBottom": "Untere rechte Ecke",
    "noticeContent": "Inhalt der Benachrichtigung",
    "gameInjection": "Spielinjektion",
    "ingameShow": "Anzeige im Spiel",
    "inGameMonitoring": "Spielinternes Monitoring",
    "gameFilter": "Spielefilter",
    "start": "Starten",
    "endMarkStatistics": "Ende der Kennzeichnungsstatistik",
    "readHwinfoFail": "HWINFO Fehler beim Lesen der Hardware-Informationen",
    "dataSaveDesktop": "Daten wurden in die Zwischenablage und Desktop-Datei gespeichert",
    "TheSensorCacheCleared": "Zwischendaten des Sensors gelöscht",
    "defaultSensor": "Standard-Sensor",
    "setSensor": "Sensor Auswählen",
    "refreshTime": "Datenaktualisierungszeit",
    "recommend": "Standard",
    "sensorMsg": "Je kürzer das Zeitintervall, desto höher der Leistungsverbrauch. Bitte wählen Sie sorgfältig aus.",
    "exportData": "Daten exportieren",
    "exportHwData": "Hardware-Informationen exportieren",
    "sensorError": "Sensorlesung unnormal",
    "clearCache": "Cache leeren",
    "littleTips": "Hinweis: Der Leistungsbericht wird 2 Minuten nach dem Spielstart generiert",
    "disableAutoShow": "Automatisches Pop-up-Fenster für Leistungsstatistiken deaktivieren",
    "AutoClosePopUpWindow_m1": "Das Leistungsstatistikfenster wird nach dem festgelegten Zeitraum automatisch geschlossen:",
    "AutoClosePopUpWindow_m2": "Sekunden",
    "abnormalShutdownReport": "Unerwarteter Herunterfahrbericht",
    "showWeaAndAddress": "Wetter und Standortinformationen anzeigen",
    "autoScreenShots": "Spielescreen automatisch erfassen, wenn markiert wird",
    "keepRecent": "Anzahl der kürzlich gespeicherten Datensätze:",
    "noLimit": "Unbegrenzt",
    "enableInGameSettingsSaving": "Speicherung der Spieleinstellungen aktivieren",
    "debugMode": "Debug-Modus",
    "enableDisableDebugMode": "Debug-Modus aktivieren/deaktivieren",
    "audioCompatibilityMode": "Audio-Kompatibilitätsmodus",
    "quickClose": "Schnelles Schließen",
    "closeTheGameQuickly": "Schnell den Spielprozess beenden",
    "cancel": "Abbrechen",
    "confirm": "Bestätigen",
    "MoveInterval_m1": "Das Desktop- und In-Game-Monitoring wird leicht verschoben:",
    "MoveInterval_m2": "Minuten",
    "text3": "Nach dem Beenden des Spiels wird das Leistungsberichtfenster nicht angezeigt, sondern nur historische Aufzeichnungen gespeichert",
    "text5": "Nach einem unerwarteten Systemabschalten wird automatisch ein Bericht erstellt. Die Aktivierung dieser Funktion beansprucht zusätzliche Systemressourcen.",
    "text6": "Die Verwendung von Tastenkombinationen könnte mit anderen Spiele-Shortcuts in Konflikt geraten, bitte vorsichtig konfigurieren.",
    "text7": "Legen Sie die Tastenkombination auf \"Keine\" fest, verwenden Sie die Rücktaste",
    "text8": "Den Status von Filtern, In-Game-Monitoring und anderen Funktionen während der Spieleausführung basierend auf dem Prozessnamen beibehalten",
    "text9": "Aktiviert protokolliert kontinuierlich Laufzeitprotokolle; deaktiviert leert Protokolldateien (Deaktivierung empfohlen)",
    "text10": "Nach Aktivierung können keine Mainboard-Sensoren abgerufen werden, um Audio-Probleme durch GamePP zu beheben",
    "text11": "Drücken Sie Alt+F4 zweimal hintereinander, um das aktuelle Spiel schnell zu beenden",
    "text12": "Möchten Sie fortfahren? Dieser Modus erfordert einen Neustart von GamePP.",
    "openMainUI": "Anwendung anzeigen",
    "setting": "Einstellungen",
    "feedback": "Probleme Rückmeldung",
    "help": "Hilfe",
    "sensorReadingSetting": "Sensoreinstellungen",
    "searchlanguage": "Suchsprache"
  },
  "GamePlusOne": {
    "year": "Jahr",
    "month": "Monat",
    "day": "Tag",
    "success": "Erfolg",
    "fail": "Fehler",
    "will": "Aktuell",
    "missedGame": "Verpasste Spiele",
    "text1": "Betrag, ca. ￥",
    "text2": "Gesamtzahl der erhaltenen Spiele",
    "text3": "Version",
    "gamevalue": "Spielwert",
    "gamevalue1": "Erhalten",
    "total": "Gesamt beansprucht",
    "text4": "Spiele, kumulativ gesparte Zeit",
    "text6": "Produkt, Wert",
    "Platformaccountmanagement": "Plattform-Kontoverwaltung",
    "Missed1": "(Nicht abgeholt)",
    "Received2": "(Erfolgreich erhalten)",
    "Receivedsoon2": "Jetzt verfügbar",
    "Receivedsoon": "Jetzt verfügbar",
    "Missed": "Nicht Abgeholte",
    "Received": "Erfolgreich abgeholt",
    "Getaccount": "Konto abrufen",
    "Worth": "Wert",
    "Auto": "Automatisch",
    "Manual": "Manuell",
    "Pleasechoose": "Wählen Sie ein Spiel aus",
    "Receive": "Jetzt erhalten",
    "Selected": "Ausgewählt",
    "text5": "Spiele",
    "Automatic": "Automatisch abrufen...",
    "Collecting": "Wird abgeholt...",
    "ReceiveTimes": "Anzahl der Abrufe in diesem Monat",
    "Thefirst": "Nr.",
    "Week": "Woche",
    "weekstotal": "53 Wochen insgesamt",
    "Return": "Startseite",
    "Solutionto": "Fehler beim Binden des Kontos - Lösung",
    "accounts": "Anzahl der gebundenen Konten",
    "Addaccount": "Konto hinzufügen",
    "Clearcache": "Cache leeren",
    "Bindtime": "Bindungszeit",
    "Status": "Status",
    "Normal": "Normal",
    "Invalid": "Ungültig",
    "text7": "Spiele, insgesamt gespart",
    "Yuan": "Yuan",
    "untie": "Lösen",
    "disable": "Deaktivieren",
    "enable": "Aktivieren",
    "gamePlatform": "Spieleplattform",
    "goStorePage": "Zur Store-Seite wechseln",
    "receiveEnd": "Nach Fristende",
    "loginPlatformAccount": "Angemeldetes Plattformkonto",
    "waitReceive": "Warten auf Einlösung",
    "receiveSuccess": "Erfolgreich empfangen",
    "accountInvalid": "Konto abgelaufen",
    "alreadyOwn": "Bereits Besessen",
    "networkError": "Netzwerkanomalie",
    "noGame": "Kein Basisspiel",
    "manualReceiveInterrupt": "Unterbrechung der manuellen Erwerbung",
    "receiving": "Beantragen",
    "agree": "Ich stimme zu, an dem 'Erhaltungsplan für GamePP' teilzunehmen.",
    "again": "Erneut erhalten"
  },
  "shutdownTimer": {
    "timedShutdown": "Geplantes Herunterfahren",
    "currentTime": "Aktuelle Uhrzeit:",
    "setCountdown": "Countdown-Einstellungen",
    "shutdownInSeconds": "Herunterfahren in X Sekunden",
    "shutdownIn": "Nach dem Herunterfahren",
    "goingToBe": "wird",
    "executionPlan": "Ausführungsplan",
    "startTheClock": "Timer starten",
    "stopTheClock": "Absagen des Plans",
    "isShuttingDown": "Geplanter Shutdown wird ausgeführt:",
    "noplan": "Kein aktueller Abschaltplan",
    "hour": "Stunde",
    "min": "Minute",
    "sec": "Sekunde",
    "ms": "Millisekunde",
    "year": "Jahr",
    "month": "Monat",
    "day": "Tag",
    "hours": "Stunde"
  },
  "screenshotpage": {
    "screenshot": "Bildschirmfoto",
    "screenshotFormat": "Speziell für die Aufnahme von Spielbildschirmen entwickelt, unterstützt das Speichern in JPG/PNG/BMP-Formaten, ermöglicht schnelle Screenshots von Spielen und stellt eine verlustfreie Ausgabe in hoher Auflösung sicher",
    "Turnon": "Aktivieren Sie automatische Screenshots, alle",
    "seconds": "Sekunde",
    "takeScreenshot": "Erstellen Sie einen automatischen Screenshot",
    "screenshotSettings": "Diese Einstellung hat keine Wirkung, wenn sie im Spiel aktiviert wird",
    "saveGameFilterAndMonitoring": "Effekte von 'Spiel-Filter' und 'In-Game-Überwachung' im Screenshot speichern",
    "disableScreenshotSound": "Bildschirmaufnahme-Sound Benachrichtigung ausschalten",
    "imageFormat": "Bildformat",
    "recommended": "Empfehlen",
    "viewingdetails": "Behält Bildqualitätsdetails, moderate Größe, geeignet zum Anzeigen von Details",
    "saveSpace": "Bildqualität komprimierbar, minimaler Speicherbedarf, platzsparend",
    "ultraQuality": "Ultrahochauflösende, nicht komprimierte Grafik mit großer Dateigröße. Empfohlen für Spieler, die höchste Bildqualität bei Spielständen bevorzugen.",
    "fileSavePath": "Dateipfad zum Speichern",
    "hardDiskSpace": "Freier Speicherplatz auf der Festplatte:",
    "minutes": "Minute"
  },
  "DesktopMonitoring": {
    "desktopMonitoring": "Desktopüberwachung",
    "SomeSensors": "Wir empfehlen einige Sensoren zur Überwachung. Sie können diese entfernen oder hinzufügen",
    "AddComponent": "Neue Komponente hinzufügen",
    "Type": "Typ",
    "Remarks": "Hinweis",
    "AssociatedSensor": "Sensor verknüpfen",
    "Operation": "Operation",
    "Return": "Zurück",
    "TimeSelection": "Zeitauswahl:",
    "Format": "Format:",
    "Rule": "Regel：",
    "Coordinate": "Koordinaten:",
    "CustomTextContent": "Benutzerdefinierte Textinhalte:",
    "SystemTime": "Systemzeit",
    "China": "China",
    "Britain": "Großbritannien",
    "America": "Vereinigte Staaten",
    "Russia": "Russland",
    "France": "Frankreich",
    "DateAndTime": "Datum und Uhrzeit",
    "Time": "Zeit",
    "Date": "Datum",
    "Week": "Wochentag",
    "DateAndTimeAndWeek": "Datum+Uhrzeit+Wochentag",
    "TimeAndWeek": "Zeit+Wochentag",
    "Hour12": "12-Stunden-Format",
    "Hour24": "24-Stunden-Format",
    "SelectSensor": "Sensor auswählen:",
    "AssociatedSensor1": "Sensor verknüpfen:",
    "SensorUnit": "Sensor-Einheit：",
    "Second": "Sekunde:",
    "Corner": "Abgerundete Ecken:",
    "BackgroundColor": "Hintergrundfarbe:",
    "ProgressColor": "Fortschritt Farbe:",
    "Font": "Schriftart：",
    "SelectFont": "Schriftart auswählen",
    "FontSize": "Schriftgröße:",
    "Color": "Farbe：",
    "Style": "Stil:",
    "Bold": "Fett",
    "Italic": "Kursiv",
    "Shadow": "Schatten",
    "ShadowPosition": "Schattenposition：",
    "ShadowEffect": "Schatteneffekte：",
    "Blur": "Unschärfe",
    "ShadowColor": "Schattenfarbe：",
    "SelectFromLocalFiles": "Aus lokalen Dateien auswählen:",
    "UploadImageVideo": "Bilder/Videos hochladen",
    "UploadSVGFile": "SVG-Datei hochladen",
    "Width": "Breite:",
    "Height": "Hoch: ",
    "Effect": "Effekt:",
    "Rotation": "Drehung:",
    "WhenTheSensorValue": "Sensorwert ist größer als",
    "conditions": "Wenn die Bedingung nicht erfüllt ist (wird nicht gedreht, wenn Sensorwert 0 ist)",
    "Clockwise": "Im Uhrzeigersinn",
    "Counterclockwise": "Gegen den Uhrzeigersinn",
    "QuickRotation": "Schnell drehen",
    "SlowRotation": "Langsame Rotation",
    "StopRotation": "Drehung stoppen",
    "StrokeColor": "Rahmenfarbe：",
    "Path": "Pfad",
    "Color1": "Farbe",
    "ChangeColor": "Farbe ändern",
    "When": "Wenn",
    "SensorValue": "Sensorwert ist größer oder gleich",
    "SensorValue1": "Sensorwert kleiner gleich",
    "SensorValue2": "Sensorwert ist gleich",
    "MonitoringSettings": "Überwachungseinstellungen",
    "RestoreDefault": "Zurücksetzen auf Standard",
    "Monitor": "Monitor",
    "AreaSize": "Bereichsgröße",
    "Background": "Hintergrund",
    "ImageVideo": "Bilder/Videos",
    "PureColor": "Einfarb",
    "Select": "Auswählen",
    "ImageVideoDisplayMode": "Bilder/Videos Anzeigemodus",
    "Transparency": "Transparenz",
    "DisplayPosition": "Position anzeigen",
    "Stretch": "Strecken",
    "Fill": "Füllung",
    "Adapt": "Anpassen",
    "SelectThePosition": "Klicken Sie auf die Zelle, um die Position schnell auszuwählen",
    "CurrentPosition": "Aktuelle Position:",
    "DragLock": "Ziehensperre",
    "LockMonitoringPosition": "Monitorposition sperren (Nach dem Sperren kann der Monitor nicht mehr verschoben werden)",
    "Unlockinterior": "Bewegen interner Elemente ermöglichen",
    "Font1": "Schriftart",
    "GameSettings": "Spiel-Einstellungen",
    "CloseDesktopMonitor": "Automatisch deaktivieren Sie die Desktopüberwachung, wenn das Spiel läuft.",
    "OLED": "OLED-Brennschutz",
    "Display": "Anzeigen",
    "PleaseEnterContent": "Geben Sie den Inhalt ein",
    "NextStep": "Weiter",
    "Add": "Hinzufügen",
    "StylesForYou": "Wir empfehlen Ihnen einige Überwachungs-Stile. Sie können diese auswählen und anwenden. In Zukunft werden weitere Stile hinzugefügt.",
    "EditPlan": "Profil bearbeiten",
    "MonitoringStylePlan": "Überwachungsstil-Option",
    "AddDesktopMonitoring": "Desktop-Bewegungserfassung hinzufügen",
    "TextLabel": "Text-Label",
    "ImageVideo1": "Bilder, Videos",
    "SensorGraphics": "Sensorgraphik",
    "SensorData": "Sensordaten",
    "CustomText": "Benutzerdefinierter Text",
    "DateTime": "Datum und Uhrzeit",
    "Image": "Bild",
    "Video": "Video",
    "SVG": "SVG",
    "ProgressBar": "Fortschrittsbalken",
    "Graphics": "Grafik",
    "UploadImage": "Bild hochladen",
    "UploadVideo": "Video hochladen",
    "RealTimeMonitoring": "Echtzeitüberwachung von CPU- und GPU-Temperaturen sowie Auslastung, frei verschiebbare Layouts, personalisierte Designanpassung – beherrschen Sie Leistung und Desktopästhetik",
    "Chart": "Diagramm",
    "Zigzagcolor": "Linienfarbe (Startpunkt)",
    "Zigzagcolor1": "Linienfarbe (Endpunkt)",
    "Zigzagcolor2": "Linien-Diagramm-Bereichsfarbe (Start)",
    "Zigzagcolor3": "Farbe des Bereichs im Liniendiagramm (Ende)",
    "CustomMonitoring": "Benutzerdefinierte Überwachung"
  }
}
//messageEnd 
 export default de 