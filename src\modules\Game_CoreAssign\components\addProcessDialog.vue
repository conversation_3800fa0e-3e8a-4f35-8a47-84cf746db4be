<script setup lang="ts">
import {ProcessWithGroup,processCoreAssignStore} from "../stores"
import {computed, reactive, watch} from "vue";
import {CirclePlus} from "@element-plus/icons-vue";
import {useI18n} from "vue-i18n";

const $store = processCoreAssignStore()
const {t} = useI18n()
const data = reactive({
    switchValue: false,
    sortBy:['cpu_usage','desc'],
})
const methods = {
    closeDialog() {
        $store.display.showAddProcessDialog = false
    },
}

const tableData = computed(()=>{
    let _arr:any = []
    if (data.switchValue) {
        _arr = $store.process_list.map((item)=>{
            for (let i = 0; i < $store.display.local_process.length; i++) {
                const element = $store.display.local_process[i];
                if (element.name === item.name) {
                    item.group = element.group
                    break;
                }
            }
            return item
        });
    }else{
        _arr = $store.process_list.filter(item=>{
            return !item.path.includes('C:\\Windows')
        }).map((item)=>{
            for (let i = 0; i < $store.display.local_process.length; i++) {
                const element = $store.display.local_process[i];
                if (element.name === item.name) {
                    item.group = element.group
                    break;
                }
            }
            return item
        });
    }

    // 排序
    let sortKey:string = data.sortBy[0]
    if (sortKey) {
        _arr.sort((a:any,b:any)=>{
            if (data.sortBy[1] === 'desc') {
                return b[sortKey] - a[sortKey]
            }else{
                return a[sortKey] - b[sortKey]
            }
        })
    }

    return _arr
})

function showGroup(item:ProcessWithGroup) {
    if (item.group) {
        const i = $store.display.process_groups.findIndex(element=>element.id === item.group)
        if (i !== -1) {
            if ($store.display.process_groups[i]['name'] === 'psc.notGameProcess' || $store.display.process_groups[i]['name'] === 'psc.unNamedProcess') {
                return t($store.display.process_groups[i]['name'])
            }
            return $store.display.process_groups[i]['name'];
        }
        return ''
    }else{
        return ''
    }
}

function setSort(str: string) {
    console.log('sortBy',str)
    if (data.sortBy[0] === str) {
        if (data.sortBy[1] === 'desc') {
            data.sortBy = [str,'asc'] // 点第二次设置成升序
        } else {
            data.sortBy = ['',''] // 点第三次设置成默认排序
        }
    }else{
        data.sortBy = [str,'desc'] // 点第一次设置成降序
    }
}
</script>

<template>
    <div class="dialog-wrap" v-if="$store.display.showAddProcessDialog">
        <div class="dialog-container">
            <header>
                <span class="titleName">{{$t('psc.addNowProcess')}}</span>
                <span class="ml-auto" @click="data.switchValue = !data.switchValue">{{$t('psc.displaySystemProcess')}}</span>
                <el-switch active-color="#508DE2" v-model="data.switchValue"></el-switch>
                <span class="iconfont icon-Close" style="color: #999999" @click="methods.closeDialog"></span>
            </header>
            <div class="dialog-content">
                <div class="table-name">
                    <div class="name">{{$t('psc.processName')}}</div>
                    <div class="cpu" :class="{'sortBy':data.sortBy[0] === 'cpu_usage'}" @click="setSort('cpu_usage')">
                        <span >CPU{{$t('hardwareInfo.occupied')}}</span>
                        <span class="iconfont icon-hideshow"
                              :class="{ 'ro180': data.sortBy[1] === 'asc' }"
                              v-show="data.sortBy[0] === 'cpu_usage'"
                        ></span>
                    </div>
                    <div class="gpu" :class="{'sortBy':data.sortBy[0] === 'gpu_usage'}" @click="setSort('gpu_usage')">
                        <span>GPU{{$t('hardwareInfo.occupied')}}</span>
                        <span class="iconfont icon-hideshow"
                              :class="{ 'ro180': data.sortBy[1] === 'asc' }"
                              v-show="data.sortBy[0] === 'gpu_usage'"
                        ></span>
                    </div>
                    <div class="mem" :class="{'sortBy':data.sortBy[0] === 'mem_usage'}" @click="setSort('mem_usage')">
                        <span>{{$t('GameRebound.MemoryOccupancy')}}</span>
                        <span class="iconfont icon-hideshow"
                              :class="{ 'ro180': data.sortBy[1] === 'asc' }"
                              v-show="data.sortBy[0] === 'mem_usage'"
                        ></span>
                    </div>
                    <div class="group">{{$t('psc.Group')}}</div>
                    <div class="controls">{{$t('psc.controls')}}</div>
                </div>
                <ul class="table-list scroll-y">
                    <li
                        v-for="item in tableData"
                        :key="item.pid"
                    >
                        <div class="name">
                            <div class="point"></div>
                            <img :src="item.icon" alt="">
                            <span>{{item.name}}</span>
                        </div>
                        <div class="cpu">{{item.cpu_usage}}%</div>
                        <div class="gpu">{{item.gpu_usage}}%</div>
                        <div class="mem">{{item.mem_usage}}%</div>
                        <div class="group">
                            {{showGroup(item)}}
                        </div>
                        <div class="controls">
                            <div class="add-btn" @click="$store.addLocalProcess(item)">
                                <el-icon color="#508DE2"><CirclePlus /></el-icon>
                                {{$t('messages.append')}}
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>

<style scoped lang="scss">
.dialog-wrap {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #000000C7;
  z-index: 999999;
}

.dialog-container {
  width: 620px;
  height: 400px;
  background: #2B2C37;
  box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 10px 16px;

  header {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;

    .titleName {
      color: #ffffff;
      font-size: 12px;
    }

    .icon-Close {
      cursor: pointer;
      margin-left: 20px;
    }

    .ml-auto {
      margin-left: auto;
      color: #ffffff;
      font-size: 12px;
      margin-right: 10px;
    }
  }

  .dialog-content {
    margin-top: 10px;
    height: 335px;
    font-size: 12px;
    color: #777777;

    .table-name,.table-list {
      width: 587px;
      flex: 0;
    }
    .table-name,.table-list li {
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      height: 30px;

      span {
        display: inline-block;
        position: relative;
      }

      .name {
        width: 180px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
          height: 30px;
          line-height: 32px;
      }

      .cpu,.gpu,.mem  {
        width: 80px;
          height: 30px;
          line-height: 32px;
          text-align: center;
      }

      .group {
        width: 100px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
          height: 30px;
          line-height: 32px;
          text-align: center;
      }
      .controls {
        width: 61px;
          height: 30px;
          line-height: 32px;
          text-align: center;
      }

        .cpu,.gpu,.mem {
            cursor: pointer;
            position: relative;
            &.sortBy {
                background-color: #393B50;
                color: #ffffff;
            }

            .icon-hideshow {
                position: absolute;
                top: -10px;
                left: 50%;
                transform: translateX(-50%);
                font-size: 8px;
                display: inline-block;
            }

            .ro180 {
                transform: translateX(-50%) rotate(180deg);
            }
        }
    }

    .table-list {
      overflow: auto;
      height: 305px;

      li {
        cursor: pointer;
        line-height: 30px;
        &:hover {
          background-color: #343647;
          border-radius: 4px;
          color: #ffffff;

          .add-btn {
            display: flex;
            align-items: center;
            gap: 5px;
          }
        }

        .name {
          display: flex;
          align-items: center;
          gap: 8px;
          padding-left: 10px;

          .point {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #35D57D;
          }

          img {
            width: 20px;
            height: 20px;
          }

          span {
            width: 120px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .add-btn {
          display: none;
          color: #508DE2;
        }
      }
    }
  }
}
</style>
