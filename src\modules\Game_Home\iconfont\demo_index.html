<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4572172" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe673;</span>
                <div class="name">detail</div>
                <div class="code-name">&amp;#xe673;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe674;</span>
                <div class="name">cloud</div>
                <div class="code-name">&amp;#xe674;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe675;</span>
                <div class="name">chart</div>
                <div class="code-name">&amp;#xe675;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe676;</span>
                <div class="name">ban</div>
                <div class="code-name">&amp;#xe676;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe677;</span>
                <div class="name">weather</div>
                <div class="code-name">&amp;#xe677;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe678;</span>
                <div class="name">addmanage</div>
                <div class="code-name">&amp;#xe678;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe660;</span>
                <div class="name">delete</div>
                <div class="code-name">&amp;#xe660;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe661;</span>
                <div class="name">fastadd</div>
                <div class="code-name">&amp;#xe661;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe662;</span>
                <div class="name">Monitor</div>
                <div class="code-name">&amp;#xe662;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe663;</span>
                <div class="name">star</div>
                <div class="code-name">&amp;#xe663;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe664;</span>
                <div class="name">edit</div>
                <div class="code-name">&amp;#xe664;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe665;</span>
                <div class="name">bilibili</div>
                <div class="code-name">&amp;#xe665;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe666;</span>
                <div class="name">Copy</div>
                <div class="code-name">&amp;#xe666;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe667;</span>
                <div class="name">locate</div>
                <div class="code-name">&amp;#xe667;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe668;</span>
                <div class="name">proccessdistribution</div>
                <div class="code-name">&amp;#xe668;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe669;</span>
                <div class="name">hardwaredetail</div>
                <div class="code-name">&amp;#xe669;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66a;</span>
                <div class="name">fpsyc</div>
                <div class="code-name">&amp;#xe66a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66b;</span>
                <div class="name">board</div>
                <div class="code-name">&amp;#xe66b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66c;</span>
                <div class="name">Battery</div>
                <div class="code-name">&amp;#xe66c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66d;</span>
                <div class="name">hideshow</div>
                <div class="code-name">&amp;#xe66d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66e;</span>
                <div class="name">Dram</div>
                <div class="code-name">&amp;#xe66e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66f;</span>
                <div class="name">export</div>
                <div class="code-name">&amp;#xe66f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe670;</span>
                <div class="name">add</div>
                <div class="code-name">&amp;#xe670;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe671;</span>
                <div class="name">holding</div>
                <div class="code-name">&amp;#xe671;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe672;</span>
                <div class="name">date</div>
                <div class="code-name">&amp;#xe672;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65f;</span>
                <div class="name">download</div>
                <div class="code-name">&amp;#xe65f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64d;</span>
                <div class="name">manage</div>
                <div class="code-name">&amp;#xe64d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64e;</span>
                <div class="name">style</div>
                <div class="code-name">&amp;#xe64e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64f;</span>
                <div class="name">sensor</div>
                <div class="code-name">&amp;#xe64f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe650;</span>
                <div class="name">system</div>
                <div class="code-name">&amp;#xe650;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe651;</span>
                <div class="name">invoke</div>
                <div class="code-name">&amp;#xe651;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe652;</span>
                <div class="name">VIPfilter</div>
                <div class="code-name">&amp;#xe652;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe653;</span>
                <div class="name">retract</div>
                <div class="code-name">&amp;#xe653;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe654;</span>
                <div class="name">Maximize</div>
                <div class="code-name">&amp;#xe654;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe639;</span>
                <div class="name">Harddisk</div>
                <div class="code-name">&amp;#xe639;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe655;</span>
                <div class="name">hide</div>
                <div class="code-name">&amp;#xe655;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe656;</span>
                <div class="name">switch</div>
                <div class="code-name">&amp;#xe656;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe657;</span>
                <div class="name">Performance statistics</div>
                <div class="code-name">&amp;#xe657;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe658;</span>
                <div class="name">GPU</div>
                <div class="code-name">&amp;#xe658;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe659;</span>
                <div class="name">normalize</div>
                <div class="code-name">&amp;#xe659;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65a;</span>
                <div class="name">Close</div>
                <div class="code-name">&amp;#xe65a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65b;</span>
                <div class="name">nav_toolbox</div>
                <div class="code-name">&amp;#xe65b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65c;</span>
                <div class="name">parameter</div>
                <div class="code-name">&amp;#xe65c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65d;</span>
                <div class="name">remove</div>
                <div class="code-name">&amp;#xe65d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65e;</span>
                <div class="name">notice</div>
                <div class="code-name">&amp;#xe65e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63f;</span>
                <div class="name">show</div>
                <div class="code-name">&amp;#xe63f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe640;</span>
                <div class="name">Screenshot</div>
                <div class="code-name">&amp;#xe640;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63a;</span>
                <div class="name">error</div>
                <div class="code-name">&amp;#xe63a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe638;</span>
                <div class="name">CPU</div>
                <div class="code-name">&amp;#xe638;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63d;</span>
                <div class="name">Launch</div>
                <div class="code-name">&amp;#xe63d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63e;</span>
                <div class="name">hardware</div>
                <div class="code-name">&amp;#xe63e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe641;</span>
                <div class="name">minimize</div>
                <div class="code-name">&amp;#xe641;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63c;</span>
                <div class="name">Scan</div>
                <div class="code-name">&amp;#xe63c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe642;</span>
                <div class="name">threads</div>
                <div class="code-name">&amp;#xe642;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe643;</span>
                <div class="name">nav_test</div>
                <div class="code-name">&amp;#xe643;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63b;</span>
                <div class="name">process</div>
                <div class="code-name">&amp;#xe63b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe644;</span>
                <div class="name">homepage</div>
                <div class="code-name">&amp;#xe644;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe645;</span>
                <div class="name">option</div>
                <div class="code-name">&amp;#xe645;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe646;</span>
                <div class="name">topping</div>
                <div class="code-name">&amp;#xe646;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe647;</span>
                <div class="name">VSl</div>
                <div class="code-name">&amp;#xe647;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe648;</span>
                <div class="name">processname</div>
                <div class="code-name">&amp;#xe648;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe649;</span>
                <div class="name">relog</div>
                <div class="code-name">&amp;#xe649;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64a;</span>
                <div class="name">share</div>
                <div class="code-name">&amp;#xe64a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64b;</span>
                <div class="name">monitor_option</div>
                <div class="code-name">&amp;#xe64b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64c;</span>
                <div class="name">Video</div>
                <div class="code-name">&amp;#xe64c;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1718613626644') format('woff2'),
       url('iconfont.woff?t=1718613626644') format('woff'),
       url('iconfont.ttf?t=1718613626644') format('truetype'),
       url('iconfont.svg?t=1718613626644#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-detail"></span>
            <div class="name">
              detail
            </div>
            <div class="code-name">.icon-detail
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-cloud"></span>
            <div class="name">
              cloud
            </div>
            <div class="code-name">.icon-cloud
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chart"></span>
            <div class="name">
              chart
            </div>
            <div class="code-name">.icon-chart
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ban"></span>
            <div class="name">
              ban
            </div>
            <div class="code-name">.icon-ban
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-weather"></span>
            <div class="name">
              weather
            </div>
            <div class="code-name">.icon-weather
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-addmanage"></span>
            <div class="name">
              addmanage
            </div>
            <div class="code-name">.icon-addmanage
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-delete"></span>
            <div class="name">
              delete
            </div>
            <div class="code-name">.icon-delete
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fastadd"></span>
            <div class="name">
              fastadd
            </div>
            <div class="code-name">.icon-fastadd
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Monitor"></span>
            <div class="name">
              Monitor
            </div>
            <div class="code-name">.icon-Monitor
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-star"></span>
            <div class="name">
              star
            </div>
            <div class="code-name">.icon-star
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-edit"></span>
            <div class="name">
              edit
            </div>
            <div class="code-name">.icon-edit
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bilibili"></span>
            <div class="name">
              bilibili
            </div>
            <div class="code-name">.icon-bilibili
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Copy"></span>
            <div class="name">
              Copy
            </div>
            <div class="code-name">.icon-Copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-locate"></span>
            <div class="name">
              locate
            </div>
            <div class="code-name">.icon-locate
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-proccessdistribution"></span>
            <div class="name">
              proccessdistribution
            </div>
            <div class="code-name">.icon-proccessdistribution
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-hardwaredetail"></span>
            <div class="name">
              hardwaredetail
            </div>
            <div class="code-name">.icon-hardwaredetail
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fpsyc"></span>
            <div class="name">
              fpsyc
            </div>
            <div class="code-name">.icon-fpsyc
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-board"></span>
            <div class="name">
              board
            </div>
            <div class="code-name">.icon-board
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Battery"></span>
            <div class="name">
              Battery
            </div>
            <div class="code-name">.icon-Battery
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-hideshow"></span>
            <div class="name">
              hideshow
            </div>
            <div class="code-name">.icon-hideshow
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Dram"></span>
            <div class="name">
              Dram
            </div>
            <div class="code-name">.icon-Dram
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-export"></span>
            <div class="name">
              export
            </div>
            <div class="code-name">.icon-export
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-add"></span>
            <div class="name">
              add
            </div>
            <div class="code-name">.icon-add
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-holding"></span>
            <div class="name">
              holding
            </div>
            <div class="code-name">.icon-holding
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-date"></span>
            <div class="name">
              date
            </div>
            <div class="code-name">.icon-date
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-download"></span>
            <div class="name">
              download
            </div>
            <div class="code-name">.icon-download
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-manage"></span>
            <div class="name">
              manage
            </div>
            <div class="code-name">.icon-manage
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-style"></span>
            <div class="name">
              style
            </div>
            <div class="code-name">.icon-style
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-sensor"></span>
            <div class="name">
              sensor
            </div>
            <div class="code-name">.icon-sensor
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-system"></span>
            <div class="name">
              system
            </div>
            <div class="code-name">.icon-system
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-invoke"></span>
            <div class="name">
              invoke
            </div>
            <div class="code-name">.icon-invoke
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-VIPfilter"></span>
            <div class="name">
              VIPfilter
            </div>
            <div class="code-name">.icon-VIPfilter
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-retract"></span>
            <div class="name">
              retract
            </div>
            <div class="code-name">.icon-retract
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Maximize"></span>
            <div class="name">
              Maximize
            </div>
            <div class="code-name">.icon-Maximize
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Harddisk"></span>
            <div class="name">
              Harddisk
            </div>
            <div class="code-name">.icon-Harddisk
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-hide"></span>
            <div class="name">
              hide
            </div>
            <div class="code-name">.icon-hide
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-switch"></span>
            <div class="name">
              switch
            </div>
            <div class="code-name">.icon-switch
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-Performancestatistics"></span>
            <div class="name">
              Performance statistics
            </div>
            <div class="code-name">.icon-a-Performancestatistics
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-GPU"></span>
            <div class="name">
              GPU
            </div>
            <div class="code-name">.icon-GPU
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-normalize"></span>
            <div class="name">
              normalize
            </div>
            <div class="code-name">.icon-normalize
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Close"></span>
            <div class="name">
              Close
            </div>
            <div class="code-name">.icon-Close
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-nav_toolbox"></span>
            <div class="name">
              nav_toolbox
            </div>
            <div class="code-name">.icon-nav_toolbox
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-parameter"></span>
            <div class="name">
              parameter
            </div>
            <div class="code-name">.icon-parameter
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-remove"></span>
            <div class="name">
              remove
            </div>
            <div class="code-name">.icon-remove
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-notice"></span>
            <div class="name">
              notice
            </div>
            <div class="code-name">.icon-notice
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-show"></span>
            <div class="name">
              show
            </div>
            <div class="code-name">.icon-show
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Screenshot"></span>
            <div class="name">
              Screenshot
            </div>
            <div class="code-name">.icon-Screenshot
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-error"></span>
            <div class="name">
              error
            </div>
            <div class="code-name">.icon-error
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-CPU"></span>
            <div class="name">
              CPU
            </div>
            <div class="code-name">.icon-CPU
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Launch"></span>
            <div class="name">
              Launch
            </div>
            <div class="code-name">.icon-Launch
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-hardware"></span>
            <div class="name">
              hardware
            </div>
            <div class="code-name">.icon-hardware
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-minimize"></span>
            <div class="name">
              minimize
            </div>
            <div class="code-name">.icon-minimize
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Scan"></span>
            <div class="name">
              Scan
            </div>
            <div class="code-name">.icon-Scan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-threads"></span>
            <div class="name">
              threads
            </div>
            <div class="code-name">.icon-threads
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-nav_test"></span>
            <div class="name">
              nav_test
            </div>
            <div class="code-name">.icon-nav_test
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-process"></span>
            <div class="name">
              process
            </div>
            <div class="code-name">.icon-process
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-homepage"></span>
            <div class="name">
              homepage
            </div>
            <div class="code-name">.icon-homepage
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-option"></span>
            <div class="name">
              option
            </div>
            <div class="code-name">.icon-option
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-topping"></span>
            <div class="name">
              topping
            </div>
            <div class="code-name">.icon-topping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-VSl"></span>
            <div class="name">
              VSl
            </div>
            <div class="code-name">.icon-VSl
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-processname"></span>
            <div class="name">
              processname
            </div>
            <div class="code-name">.icon-processname
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-relog"></span>
            <div class="name">
              relog
            </div>
            <div class="code-name">.icon-relog
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-share"></span>
            <div class="name">
              share
            </div>
            <div class="code-name">.icon-share
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-monitor_option"></span>
            <div class="name">
              monitor_option
            </div>
            <div class="code-name">.icon-monitor_option
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Video"></span>
            <div class="name">
              Video
            </div>
            <div class="code-name">.icon-Video
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-detail"></use>
                </svg>
                <div class="name">detail</div>
                <div class="code-name">#icon-detail</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-cloud"></use>
                </svg>
                <div class="name">cloud</div>
                <div class="code-name">#icon-cloud</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chart"></use>
                </svg>
                <div class="name">chart</div>
                <div class="code-name">#icon-chart</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ban"></use>
                </svg>
                <div class="name">ban</div>
                <div class="code-name">#icon-ban</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-weather"></use>
                </svg>
                <div class="name">weather</div>
                <div class="code-name">#icon-weather</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-addmanage"></use>
                </svg>
                <div class="name">addmanage</div>
                <div class="code-name">#icon-addmanage</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-delete"></use>
                </svg>
                <div class="name">delete</div>
                <div class="code-name">#icon-delete</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fastadd"></use>
                </svg>
                <div class="name">fastadd</div>
                <div class="code-name">#icon-fastadd</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Monitor"></use>
                </svg>
                <div class="name">Monitor</div>
                <div class="code-name">#icon-Monitor</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-star"></use>
                </svg>
                <div class="name">star</div>
                <div class="code-name">#icon-star</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-edit"></use>
                </svg>
                <div class="name">edit</div>
                <div class="code-name">#icon-edit</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bilibili"></use>
                </svg>
                <div class="name">bilibili</div>
                <div class="code-name">#icon-bilibili</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Copy"></use>
                </svg>
                <div class="name">Copy</div>
                <div class="code-name">#icon-Copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-locate"></use>
                </svg>
                <div class="name">locate</div>
                <div class="code-name">#icon-locate</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-proccessdistribution"></use>
                </svg>
                <div class="name">proccessdistribution</div>
                <div class="code-name">#icon-proccessdistribution</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-hardwaredetail"></use>
                </svg>
                <div class="name">hardwaredetail</div>
                <div class="code-name">#icon-hardwaredetail</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fpsyc"></use>
                </svg>
                <div class="name">fpsyc</div>
                <div class="code-name">#icon-fpsyc</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-board"></use>
                </svg>
                <div class="name">board</div>
                <div class="code-name">#icon-board</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Battery"></use>
                </svg>
                <div class="name">Battery</div>
                <div class="code-name">#icon-Battery</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-hideshow"></use>
                </svg>
                <div class="name">hideshow</div>
                <div class="code-name">#icon-hideshow</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Dram"></use>
                </svg>
                <div class="name">Dram</div>
                <div class="code-name">#icon-Dram</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-export"></use>
                </svg>
                <div class="name">export</div>
                <div class="code-name">#icon-export</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-add"></use>
                </svg>
                <div class="name">add</div>
                <div class="code-name">#icon-add</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-holding"></use>
                </svg>
                <div class="name">holding</div>
                <div class="code-name">#icon-holding</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-date"></use>
                </svg>
                <div class="name">date</div>
                <div class="code-name">#icon-date</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-download"></use>
                </svg>
                <div class="name">download</div>
                <div class="code-name">#icon-download</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-manage"></use>
                </svg>
                <div class="name">manage</div>
                <div class="code-name">#icon-manage</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-style"></use>
                </svg>
                <div class="name">style</div>
                <div class="code-name">#icon-style</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-sensor"></use>
                </svg>
                <div class="name">sensor</div>
                <div class="code-name">#icon-sensor</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-system"></use>
                </svg>
                <div class="name">system</div>
                <div class="code-name">#icon-system</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-invoke"></use>
                </svg>
                <div class="name">invoke</div>
                <div class="code-name">#icon-invoke</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-VIPfilter"></use>
                </svg>
                <div class="name">VIPfilter</div>
                <div class="code-name">#icon-VIPfilter</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-retract"></use>
                </svg>
                <div class="name">retract</div>
                <div class="code-name">#icon-retract</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Maximize"></use>
                </svg>
                <div class="name">Maximize</div>
                <div class="code-name">#icon-Maximize</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Harddisk"></use>
                </svg>
                <div class="name">Harddisk</div>
                <div class="code-name">#icon-Harddisk</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-hide"></use>
                </svg>
                <div class="name">hide</div>
                <div class="code-name">#icon-hide</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-switch"></use>
                </svg>
                <div class="name">switch</div>
                <div class="code-name">#icon-switch</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-Performancestatistics"></use>
                </svg>
                <div class="name">Performance statistics</div>
                <div class="code-name">#icon-a-Performancestatistics</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-GPU"></use>
                </svg>
                <div class="name">GPU</div>
                <div class="code-name">#icon-GPU</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-normalize"></use>
                </svg>
                <div class="name">normalize</div>
                <div class="code-name">#icon-normalize</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Close"></use>
                </svg>
                <div class="name">Close</div>
                <div class="code-name">#icon-Close</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-nav_toolbox"></use>
                </svg>
                <div class="name">nav_toolbox</div>
                <div class="code-name">#icon-nav_toolbox</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-parameter"></use>
                </svg>
                <div class="name">parameter</div>
                <div class="code-name">#icon-parameter</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-remove"></use>
                </svg>
                <div class="name">remove</div>
                <div class="code-name">#icon-remove</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-notice"></use>
                </svg>
                <div class="name">notice</div>
                <div class="code-name">#icon-notice</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-show"></use>
                </svg>
                <div class="name">show</div>
                <div class="code-name">#icon-show</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Screenshot"></use>
                </svg>
                <div class="name">Screenshot</div>
                <div class="code-name">#icon-Screenshot</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-error"></use>
                </svg>
                <div class="name">error</div>
                <div class="code-name">#icon-error</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-CPU"></use>
                </svg>
                <div class="name">CPU</div>
                <div class="code-name">#icon-CPU</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Launch"></use>
                </svg>
                <div class="name">Launch</div>
                <div class="code-name">#icon-Launch</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-hardware"></use>
                </svg>
                <div class="name">hardware</div>
                <div class="code-name">#icon-hardware</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-minimize"></use>
                </svg>
                <div class="name">minimize</div>
                <div class="code-name">#icon-minimize</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Scan"></use>
                </svg>
                <div class="name">Scan</div>
                <div class="code-name">#icon-Scan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-threads"></use>
                </svg>
                <div class="name">threads</div>
                <div class="code-name">#icon-threads</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-nav_test"></use>
                </svg>
                <div class="name">nav_test</div>
                <div class="code-name">#icon-nav_test</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-process"></use>
                </svg>
                <div class="name">process</div>
                <div class="code-name">#icon-process</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-homepage"></use>
                </svg>
                <div class="name">homepage</div>
                <div class="code-name">#icon-homepage</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-option"></use>
                </svg>
                <div class="name">option</div>
                <div class="code-name">#icon-option</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-topping"></use>
                </svg>
                <div class="name">topping</div>
                <div class="code-name">#icon-topping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-VSl"></use>
                </svg>
                <div class="name">VSl</div>
                <div class="code-name">#icon-VSl</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-processname"></use>
                </svg>
                <div class="name">processname</div>
                <div class="code-name">#icon-processname</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-relog"></use>
                </svg>
                <div class="name">relog</div>
                <div class="code-name">#icon-relog</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-share"></use>
                </svg>
                <div class="name">share</div>
                <div class="code-name">#icon-share</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-monitor_option"></use>
                </svg>
                <div class="name">monitor_option</div>
                <div class="code-name">#icon-monitor_option</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Video"></use>
                </svg>
                <div class="name">Video</div>
                <div class="code-name">#icon-Video</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
