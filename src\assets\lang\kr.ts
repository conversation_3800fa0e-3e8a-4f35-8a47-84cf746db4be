const kr = {
  "update": {
    "Game": "Game",
    "PP": "PP",
    "upgradingInProgress": "업데이트 중",
    "theModuleIsBeingUpdated": "모듈 업데이트 중",
    "dataIsBeingUpdated": "데이터를 업데이트하는 중입니다",
    "checkingUpdate": "업데이트 확인 중입니다",
    "checkingUpgrade": "업데이트 확인 중",
    "loadingProgramComponent": "프로그램 구성 요소를 불러오는 중입니다",
    "loadingHotkeyModules": "핫키 구성 요소를 로드하는 중입니다",
    "loadingGPPModules": "게임PP 구성 요소를 로드하는 중입니다",
    "loadingBlackWhiteList": "검은색/흰색 목록이 로드 중입니다",
    "loadingGameSetting": "게임 설정 매개변수를 불러오는 중입니다",
    "loadingUserAbout": "사용자 인증 관련 정보를 불러오는 중입니다",
    "loadingGameBenchmark": "게임 벤치마크 로드 중",
    "loadingHardwareInfo": "하드웨어 정보 구성 요소를 로드하는 중입니다",
    "loadingDBModules": "데이터베이스 모듈을 불러오는 중입니다",
    "loadingIGCModules": "IGC 모듈을 로드 중입니다",
    "loadingFTPModules": "FTP 지원 모듈을 로드 중입니다",
    "loadingDialogModules": "대화상자 모듈을 불러오는 중입니다",
    "loadingDataStatisticsModules": "통계 모듈을 로드 중입니다",
    "loadingSysModules": "시스템 구성 요소를 로드하는 중입니다",
    "loadingGameOptimization": "게임 최적화를 로드 중입니다",
    "loadingGameAcceleration": "게임 가속을 로딩 중입니다",
    "loadingScreenshot": "녹화된 스크린샷을 불러오는 중",
    "loadingVideoComponent": "동영상 압축 구성요소를 불러오는 중입니다",
    "loadingFileFix": "파일 복구를 불러오는 중입니다.",
    "loadingGameAI": "게임 AI 품질을 로드하는 중입니다",
    "loadingNVAPIModules": "NVAPI 모듈을 로드하고 있습니다",
    "loadingAMDADLModules": "AMDADL 모듈을 불러오는 중입니다",
    "loadingModules": "모듈을 불러오는 중입니다"
  },
  "messages": {
    "append": "추가",
    "confirm": "확인",
    "cancel": "취소",
    "default": "기본",
    "quickSelect": "빠른 선택",
    "onoffingame": "게임 내 모니터링 활성화/비활성화:",
    "changeKey": "클릭하여 키보드 단축키 변경",
    "clear": "비우기",
    "hotkeyOccupied": "핫키가 이미 사용 중입니다. 다시 설정해 주세요!",
    "minimize": "최소화",
    "exit": "종료",
    "export": "내보내기",
    "import": "가져오기",
    "screenshot": "스크린샷",
    "showHideWindow": "창 표시/숨기기",
    "ingameControlPanel": "게임 내 제어 패널",
    "openOrCloseGameInSettings": "게임 내 설정 패널 켜기/끄기",
    "openOrCloseGameInSettings2": "이 단축 키를 눌러 활성화하십시오",
    "openOrCloseGameInSettings3": "게임 내 모니터링 켜기/끄기",
    "openOrCloseGameInSettings4": "게임 필터 활성화/비활성화",
    "startManualRecord": "수동 통계 기록 시작/중지",
    "performanceStatisticsMark": "성능 통계 마커",
    "EnableAIfilter": "이 단축키를 눌러야 AI 필터가 활성화됩니다",
    "Start_stop": "수동 통계 기록 시작/중지",
    "pressureTest": "스트레스 테스트",
    "moduleNotInstalled": "기능 모듈이 설치되지 않았습니다",
    "installingPressureTest": "압력 테스트 모듈을 설치하는 중입니다...",
    "importFailed": "가져오기에 실패했습니다",
    "gamepp": "게임PP",
    "copyToClipboard": "클립보드에 복사되었습니다"
  },
  "home": {
    "homeTitle": "홈",
    "hardwareInfo": "하드웨어 정보",
    "functionIntroduction": "기능",
    "fixedToNav": "탐색 표시줄에 고정",
    "cancelFixedToNav": "네비게이션 바에서 고정 해제",
    "hardwareInfoLoading": "하드웨어 정보 로딩 중...",
    "performanceStatistics": "성능 통계",
    "updateNow": "지금 업데이트",
    "recentRun": "최근 실행",
    "resolution": "해상도：",
    "duration": "기간:",
    "gameFilter": "게임 필터",
    "gameFilterHasAccompany": "게임 필터가 동행하고 있습니다",
    "gameFilterHasAccompany2": "사용자가 Cyberpunk, APEX, Hogwarts Legacy 등의 게임을 플레이합니다",
    "currentList": "현재 목록의 모니터링 항목",
    "moreFunction": "벤치마크, 스트레스 테스트, 데스크탑 모니터링 등 추가 기능들이 개발 중입니다.",
    "newVersion": "새로운 버전이 사용할 수 있습니다。",
    "discoverUpdate": "업데이트를 찾았습니다!",
    "downloading": "다운로드 중",
    "retry": "다시 시도",
    "erhaAI": "2하AI",
    "recordingmodule": "이 기능은 녹화 모듈에 의존합니다",
    "superPower": "울트라 모드",
    "autoRecord": "게임 중 킬 순간 자동 기록 및 하이라이트 순간 간편 저장",
    "externalDevice": "주변기기 동적 조명",
    "linkage": "게임에서 킬 장면을 트리거하고 연결된 주변 장치로 표시합니다",
    "AI": "AI 성능 테스트",
    "test": "GPU를 사용하여 AI 모델을 테스트하고 GPU의 AI 성능 점수를 확인하십시오",
    "supportedGames": "지원 게임",
    "games": "Naraka: Bladepoint, Delta Force, FragPunk, League of Legends, PUBG, Valorant, The Finals, APEX Legends, COD20",
    "videoRecording": "비디오 녹화",
    "videoRecording2": "OBS 기반 동영상 녹화 기능, 동영상 비트레이트 및 프레임률(FPS) 조절 지원으로 다양한 화질과 부드러움 요구사항 충족; \"인스턴트 리플레이\" 기능도 지원하여 단축키를 눌러 언제든지 하이라이트를 저장할 수 있습니다!",
    "addOne": "무료로 다운로드",
    "gamePlatform": "게임 플랫폼",
    "goShop": "스토어 페이지로 이동",
    "receiveDeadline": "이후 수령 마감",
    "2Ai": "2하AI",
    "questionDesc": "문제 설명",
    "inputYourQuestion": "여기에 귀하가 피드백하고자 하는 제안이나 의견을 입력하시기 바랍니다。",
    "uploadLimit": "최대 3장의 로컬 이미지를 JPG/PNG/BMP 형식으로 업로드하십시오",
    "email": "이메일",
    "contactWay": "연락처",
    "qqNumber": "QQ 번호 (선택 사항)",
    "submit": "제출"
  },
  "hardwareInfo": {
    "hardwareOverview": "하드웨어 개요",
    "copyAllHardwareInfo": "모든 하드웨어 정보 복사",
    "processor": "프로세서",
    "coreCount": "코어 수:",
    "threadCount": "스레드 수:",
    "currentFrequency": "현재 주파수:",
    "currentVoltage": "현재 전압:",
    "copy": "복사",
    "releaseDate": "출시 날짜",
    "codeName": "코드명",
    "thermalDesignPower": "열 설계 소비 전력",
    "maxTemperature": "최대 온도",
    "graphicsCard": "그래픽 카드",
    "brand": "브랜드:",
    "streamProcessors": "스트림 프로세서:",
    "Videomemory": "비디오 메모리:",
    "busSpeed": "버스 속도",
    "driverInfo": "드라이버 정보",
    "driverInstallDate": "드라이버 설치 날짜",
    "hardwareID": "하드웨어 ID",
    "motherboard": "메인보드",
    "chipGroup": "칩셋:",
    "BIOSDate": "BIOS 날짜",
    "BIOSVersion": "BIOS 버전",
    "PCIESlots": "PCIe 슬롯",
    "PCIEVersion": "지원하는 PCIe 버전",
    "memory": "메모리",
    "memoryBarCount": "수량:",
    "totalSize": "크기:",
    "channelCount": "채널:",
    "Specificmodel": "특정 모델",
    "Pellet": "입자 제조사",
    "memoryBarEquivalentFrequency": "메모리 모듈의 유호 주파수:",
    "hardDisk": "하드 드라이브",
    "hardDiskCount": "하드디스크 수량：",
    "actualCapacity": "실제 용량",
    "type": "유형",
    "powerOnTime": "전원 시간",
    "powerOnCount": "전원 사이클 수",
    "SSDRemainingLife": "SSD 잔여 수명",
    "partitionInfo": "파티션 정보",
    "hardDiskController": "하드디스크 컨트롤러",
    "driverNumber": "드라이브 일련 번호",
    "display": "모니터",
    "refreshRate": "새로 고침 주파수:",
    "screenSize": "화면 크기:",
    "inches": "인치",
    "productionDate": "제조일",
    "supportRefreshRate": "새로 고침 속도 지원",
    "screenLongAndShort": "화면 크기",
    "systemInfo": "시스템 정보",
    "version": "버전",
    "systemInstallDate": "시스템 설치 날짜",
    "systemBootTime": "이번 시작 시간",
    "systemRunTime": "실행 시간",
    "Poccupied": "P 사용량",
    "Eoccupied": "E 사용 중",
    "occupied": "사용 중",
    "temperature": "온도",
    "Pfrequency": "P 주파수",
    "Efrequency": "E 주파수",
    "thermalPower": "열소비 전력",
    "frequency": "주파수",
    "current": "현재",
    "noData": "데이터 없음",
    "loadHwinfo_SDK": "Hwinfo_SDK.dll 로딩 실패, 하드웨어 및 센서 데이터를 읽을 수 없음",
    "loadHwinfo_SDK_reason": "이 문제의 가능한 원인:",
    "reason": "원인",
    "BlockIntercept": "백신 소프트웨어에 의해 차단됨, 예시: 2345 백신 소프트웨어(2345 액티브 방어 프로세스, McAfee 액티브 방어 프로세스)",
    "solution": "솔루션:",
    "solution1": "관련 프로세스를 닫고 제거한 후 GamePP를 재시작하십시오",
    "solution2": "관련 장치를 분리한 후 GamePP를 재시작하십시오.",
    "RestartGamePP": "컨트롤러를 분리하고 장치 응답을 대기한 후 GamePP를 재시작하십시오",
    "HWINFOcannotrun": "Hwinfo가 정상적으로 실행되지 않습니다",
    "downloadHWINFO": "Hwinfo 다운로드",
    "openHWINFO": "Hwinfo를 실행한 후 RUN을 클릭하면 정상적으로 열 수 있나요?",
    "hardwareDriverProblem": "하드웨어 드라이버 문제",
    "checkHardwareManager": "하드웨어 관리자를 확인하여 메인보드 및 그래픽 카드 드라이버가 제대로 설치되었는지 확인하십시오",
    "systemProblem": "시스템 문제, 예를 들어: Baofeng, Xiaoma 등의 활성화 도구 사용 시 드라이버가 로드되지 않거나 Windows 7 시스템 패치가 자동 설치되지 않을 수 있습니다",
    "reinstallSystem": "시스템을 다시 설치하여 활성화,WI7 다운로드 및 설치: ********* 업데이트",
    "Windows7": "Windows 7: SHA-256 패치 설치, Windows 10: 디지털 인증서로 활성화, Windows 11 미리 보기 버전: 메모리 무결성 비활성화",
    "ViolenceActivator": "Xiaoma와 같은 무단 활성화 도구를 사용한 경우 시스템을 복구하거나 다시 설치하십시오",
    "MultipleGraphicsCardDrivers": "컴퓨터에 AMD와 Nvidia 드라이버가 동시에 설치된 다양한 브랜드의 그래픽 카드 드라이버가 설치되어 있습니다",
    "UninstallUnused": "불필요한 그래픽 카드 드라이버를 제거한 후 컴퓨터를 다시 시작하십시오",
    "OfficialQgroup": "위의 사유에 해당하지 않습니다. 공식 QQ 그룹: 908287288(5그룹)에 참여하여 문제를 해결하시기 바랍니다.",
    "ExportHardwareData": "하드웨어 데이터 내보내기",
    "D3D": "D3D 사용량",
    "Total": "총 사용량",
    "VRAM": "VRAM 사용량",
    "VRAMFrequency": "VRAM 주파수",
    "SensorData": "센서 데이터",
    "CannotGetSensorData": "센서 데이터를 가져올 수 없습니다",
    "LoadingHardwareInfo": "하드웨어 정보 로드 중...",
    "ScanTime": "최근 검색됨：",
    "Rescan": "다시 스캔",
    "Screenshot": "스크린샷",
    "configCopyed": "설정 정보가 클립보드에 복사되었습니다",
    "LegalRisks": "잠재적인 법적 리스크가 존재함",
    "brandLegalRisks": "브랜드 표시에 잠재적 법적 리스크가 존재합니다",
    "professionalVersion": "전문가 버전",
    "professionalWorkstationVersion": "전문 워크스테이션 에디션",
    "familyEdition": "홈 에디션",
    "educationEdition": "교육판",
    "enterpriseEdition": "엔터프라이즈 에디션",
    "flagshipEdition": "플래그십 에디션",
    "familyPremiumEdition": "가족 프리미엄",
    "familyStandardEdition": "가정용 표준판",
    "primaryVersion": "기본 버전",
    "bit": "비트",
    "tempWall": "온도 벽",
    "error": "오류",
    "screenshotSuccess": "스크린샷 저장 완료",
    "atLeastOneData": "최소한 1개의 데이터를 보관하십시오",
    "atMostSixData": "최대 6개의 데이터를 추가할 수 있습니다",
    "screenNotActivated": "비활성화"
  },
  "psc": {
    "processCoreAssign": "프로세스의 코어 할당",
    "CoreAssign": "코어 할당:",
    "groupName": "그룹 이름:",
    "notGameProcess": "비게임 프로세스",
    "unNamedProcess": "미명명 그룹",
    "Group2": "그룹",
    "selectTheCore": "코어 선택",
    "controls": "작업",
    "tips": "프롬프트",
    "search": "검색",
    "shiftOut": "제거",
    "ppValue": "PP값",
    "ppDesc": "PP값은 과거 하드웨어 리소스 소비 상황을 반영합니다. 이 값이 클수록 하드웨어 리소스 사용량이 증가합니다.",
    "littletips": "팁: 프로세스를 길게 눌러 왼쪽 그룹으로 드래그할 수 있습니다",
    "warning1": "다른 그룹의 코어 스레드를 선택하면 성능에 영향을 줄 수 있습니다. 동일 그룹의 코어 사용을 권장합니다.",
    "warning2": "이 그룹 이름을 비워도 괜찮습니까?",
    "warning3": "삭제 후 코어 할당 효과가 무효화됩니다. 그룹을 삭제하시겠습니까?",
    "allprocess": "모든 프로세스",
    "pleaseCheckProcess": "프로세스를 선택해 주세요",
    "dataSaveDesktop": "데이터가 데스크탑에 저장되었습니다。",
    "createAGroup": "그룹 생성",
    "delGroup": "그룹 삭제",
    "Group": "그룹",
    "editGroup": "그룹 수정",
    "groupinfo": "그룹 정보",
    "moveOutGrouping": "그룹에서 제거",
    "createANewGroup": "새 그룹 만들기",
    "unallocatedCore": "할당되지 않은 코어",
    "inactiveProcess": "비활성 프로세스",
    "importGroupingScheme": "그룹화 프로필 가져오기",
    "derivedPacketScheme": "그룹 구성 내보내기",
    "addNowProcess": "현재 실행 중인 프로세스 추가",
    "displaySystemProcess": "시스템 프로세스 표시",
    "max64": "최대 64개 스레드만 선택 가능",
    "processName": "프로세스 이름",
    "chooseCurProcess": "현재 프로세스 선택",
    "selectNoProcess": "아직 프로세스가 선택되지 않았습니다",
    "coreCount": "코어 수",
    "threadCount": "스레드 수",
    "process": "프로세스",
    "plzInputProcessName": "프로세스 이름을 입력하여 수동으로 추가하십시오",
    "has_allocation": "스레드 할당 스킴이 있는 프로세스",
    "not_made": "프로세스에 코어 할당이 완료되지 않았습니다",
    "startUse": "최적화 활성화",
    "stopUse": "최적화 비활성화",
    "threadAllocation": "스레드 할당",
    "configProcess": "프로세스 구성",
    "selectThread": "스레드 선택",
    "hyperthreadingState": "하이퍼스레딩 상태",
    "open": "켜짐",
    "notYetUnlocked": "비활성화됨",
    "nonhyperthreading": "하이퍼스레딩 없음",
    "intervalSelection": "간격 선택",
    "invertSelection": "선택 반전",
    "description": "게임 프로세스를 특정 CPU 코어에 고정하여 실행하고 백그라운드 프로그램 간섭을 스마트하게 분리합니다. FPS 상한을 효과적으로 향상시켜 게임의 프레임률을 안정화! 게임 실행 중 갑작스러운 끊김과 프레임률 급감을 감소시키고 멀티코어 프로세서의 성능을 완전히 해제하여 전체 게임 플레이 동안 높은 프레임률을 유지합니다.",
    "importSuccess": "가져오기 성공",
    "importFailed": "가져오기 실패"
  },
  "InGameMonitor": {
    "onoffingame": "게임 내 감시 켜기/끄기:",
    "InGameMonitor": "게임 내 모니터링",
    "CustomMode": "사용자 정의 모드",
    "Developing": "개발 중...",
    "NewMonitor": "모니터링 항목 추가",
    "Data": "매개변수",
    "Des": "비고",
    "Function": "기능",
    "Editor": "편집",
    "Top": "맨 위에 고정",
    "Delete": "삭제",
    "Use": "사용",
    "DragToSet": "패널을 호출한 후 드래그하여 설정할 수 있습니다",
    "MonitorItem": "모니터링 항목",
    "addMonitorItem": "모니터링 항목 추가",
    "hide": "숨기기",
    "show": "표시",
    "generalstyle": "일반 설정",
    "restoredefault": "기본 설정으로 복원",
    "arrangement": "정렬 방식",
    "horizontal": "가로",
    "vertical": "세로 방향",
    "monitorposition": "모니터링 위치",
    "canquickselectposition": "왼쪽 지도에서 위치를 빠르게 선택할 수 있음",
    "curposition": "현재 위치:",
    "background": "배경",
    "backgroundcolor": "배경색:",
    "font": "글꼴",
    "fontStyle": "폰트 스타일",
    "fontsize": "글꼴 크기:",
    "fontcolor": "폰트 색상:",
    "style": "스타일:",
    "style2": "스타일",
    "performance": "성능",
    "refreshTime": "새로 고침 시간:",
    "goGeneralSetting": "일반 설정으로 이동",
    "selectMonitorItem": "모니터링 항목 선택",
    "selectedSensor": "선택된 센서：",
    "showTitle": "제목 표시",
    "hideTitle": "제목 숨기기",
    "showStyle": "표시 방식:",
    "remarkSize": "비고 크기:",
    "remarkColor": "메모 색상:",
    "parameterSize": "매개변수 크기:",
    "parameterColor": "매개변수 색상:",
    "lineChart": "선 그래프",
    "lineColor": "폴리라인 색상:",
    "lineThickness": "꺾은선 굵기:",
    "areaHeight": "영역 높이:",
    "sort": "정렬",
    "displacement": "변위:",
    "up": "위로 이동",
    "down": "아래로 이동",
    "bold": "굵게",
    "stroke": "윤곽",
    "text": "텍스트",
    "textLine": "텍스트 +折선",
    "custom": "커스텀",
    "upperLeft": "왼쪽 상단",
    "upper": "중상",
    "upperRight": "오른쪽 상단",
    "Left": "좌측 중앙",
    "middle": "중앙",
    "Right": "오른쪽 중앙",
    "lowerLeft": "왼쪽 아래",
    "lower": "중하",
    "lowerRight": "오른쪽 아래",
    "notSupport": "주변 장치는 클릭하여 표시 또는 숨기기를 지원하지 않습니다",
    "notSupportRate": "수익률은 클릭으로 표시/숨기기 불가능",
    "notFindSensor": "센서를 찾을 수 없습니다. 수정하려면 클릭하십시오.",
    "monitoring": "모니터링",
    "condition": "조건",
    "bigger": "보다 큼",
    "smaller": "미만",
    "biggerThan": "임계값 초과",
    "biggerThanthreshold": "임계값 초과 퍼센트",
    "smallerThan": "임계값 미만",
    "smallerThanthreshold": "임계값 미만의 퍼센트",
    "biggerPercent": "현재 값 감소율 퍼센트",
    "smallerPercent": "현재 값 증가율(%)",
    "replay": "인스턴트 리플레이 기능",
    "screenshot": "스크린샷 기능",
    "text1": "센서 값이",
    "text2": ", 및",
    "text3": "대기 시간",
    "text4": "지정된 초 동안 더 높은 수치가 나타나지 않으면 즉시 트리거됩니다",
    "text5": "리플레이를 트리거할 때마다 임계값을 트리거 발생 시점의 값으로 업데이트하여 빈번한 트리거를 감소시킵니다",
    "text6": "재생 트리거에 사용되는 현재 임계값 표시",
    "text7": "센서 수치 표시",
    "text8": "초기 임계값을 초과",
    "text9": "초기 임계값 이하",
    "text10": "초기 임계값 횟수",
    "initThreshold": "초기 임계값",
    "curThreshold": "현재 임계값:",
    "curThreshold2": "현재 임계값",
    "resetCurThreshold": "현재 임계값 재설정",
    "action": "기능 활성화",
    "times": "회",
    "percentage": "퍼센트",
    "uninstallobs": "녹화 모듈이 다운로드되지 않음",
    "install": "다운로드",
    "performanceAndAudioMode": "성능 및 오디오 호환 모드",
    "isSaving": "저장 중",
    "video_replay": "즉시 다시 보기",
    "saved": "저장됨",
    "loadQualitysScheme": "그래픽 프리셋 불러오기",
    "notSet": "설정되지 않음",
    "mirrorEnable": "필터가 활성화되었습니다",
    "canBeTurnedOff": "돌아가기",
    "mirrorClosed": "게임 필터가 비활성화되었습니다",
    "closed": "닫혔습니다",
    "openMirror": "필터 시작",
    "wonderfulScenes": "하이라이트",
    "VulkanModeHaveProblem": "Vulkan 모드에는 호환성 문제가 있습니다",
    "suggestDxMode": "Dx 모드로 전환하는 것이 권장됩니다",
    "functionNotSupported": "이 기능은 지원되지 않습니다",
    "NotSupported": "지원하지 않음",
    "gppManualRecording": "게임PP, 수동 기록",
    "perfRecordsHaveBeenSaved": "성능 기록이 저장되었습니다",
    "redoClickF8": "녹화를 계속하려면 다시 F8 키를 누르세요",
    "startIngameMonitor": "게임 내 모니터링 기능을 활성화하는 중입니다",
    "inGameMarkSuccess": "게임 내에서 마크가 성공했습니다",
    "recordingFailed": "녹화 실패",
    "recordingHasNotDownload": "녹화 기능이 다운로드되지 않았습니다",
    "hotkeyDetected": "기능 단축키 충돌이 감지되었습니다",
    "plzEditIt": "소프트웨어 내에서 수정한 후 사용하십시오",
    "onePercentLowFrame": "1% 저프레임",
    "pointOnePercentLowFrame": "0.1% 저프레임",
    "frameGenerationTime": "프레임 생성 시간",
    "curTime": "현재 시간",
    "runTime": "실행 시간",
    "cpuTemp": "CPU 온도",
    "cpuUsage": "CPU 점유율",
    "cpuFreq": "CPU 주파수",
    "cpuPower": "CPU 열소모",
    "gpuTemp": "GPU 온도",
    "gpuUsage": "GPU 사용률",
    "gpuPower": "GPU 열설계 전력",
    "gpuFreq": "GPU 주파수",
    "memUsage": "메모리 사용량"
  },
  "LoginArea": {
    "login": "로그인",
    "loginOut": "로그아웃",
    "vipExpire": "만료됨",
    "remaining": "남은",
    "day": "하늘",
    "openVip": "멤버십 활성화",
    "vipPrivileges": "회원 특권",
    "rechargeRenewal": "충전 및 갱신",
    "Exclusivefilter": "필터",
    "configCloudSync": "클라우드 동기화 설정",
    "comingSoon": "곧 출시 예정"
  },
  "GameMirror": {
    "filterStatus": "필터 상태",
    "filterPlan": "필터 프리셋",
    "filterShortcut": "필터 단축키",
    "openCloseFilter": "필터 켜기/끄기:",
    "effectDemo": "이펙트 데모",
    "demoConfig": "데모 구성",
    "AiFilter": "AI 필터 효과는 게임 내 효과에 따릅니다",
    "AiFilterFAQ": "AI 필터의 일반적인 문제",
    "gamePPAiFilter": "GamePP AI 필터",
    "gamePPAiFilterVip": "GamePP VIP 전용 AI 필터, 게임 장면에 따라 실시간으로 필터 파라미터를 조정하여 게임 화면 효과를 최적화하고 게임 경험을 향상시킵니다.",
    "AiMingliangTips": "AI 밝기: 게임 화면이 너무 어두울 때 사용하는 것이 좋습니다.",
    "AiBrightTips": "AI 선명하게: 게임 화면이 너무 어두울 때 사용하는 것이 좋습니다.",
    "AiDarkTips": "AI 디밍: 게임 그래픽이 지나치게 선명할 때 사용하는 것이 좋습니다.",
    "AiBalanceTips": "AI 균형: 대부분의 게임 시나리오에 적합함",
    "AiTips": "팁: AI 필터는 게임 내에서 단축키를 눌러 사용해야 합니다",
    "AiFilterUse": "게임 내에서 사용해 주세요",
    "AiFilterAdjust": "AI 필터 단축키 조정",
    "Bright": "생동감",
    "Soft": "부드러운",
    "Highlight": "하이라이트",
    "Film": "영화",
    "Benq": "벤Q",
    "AntiGlare": "안티글레어",
    "HighSaturation": "높은 채도",
    "Brightness": "비비드",
    "Day": "낮",
    "Night": "밤",
    "Nature": "자연",
    "smooth": "세부",
    "elegant": "담아",
    "warm": "웜톤",
    "clear": "명확",
    "sharp": "선명도",
    "vivid": "동적",
    "beauty": "선명",
    "highDefinition": "고화질",
    "AiMingliang": "AI 브라이트",
    "AiBright": "AI 생생",
    "AiDark": "AI 어두운 상태",
    "AiBalance": "AI 균형",
    "BrightTips": "선명한 필터는 캐주얼, 액션 또는 어드벤처 게임에 적합하며 색상 포화도를 강화하여 게임 화면을 더욱 생동감 있고 매력적으로 만듭니다.",
    "liangTips": "게임 화면이 너무 어두울 때 필터 사용을 권장합니다.",
    "anTips": "게임 화면이 너무 어두울 때는 필터를 사용하는 것이 좋습니다.",
    "jianyiTips": "필터는 게임 그래픽이 너무 생생할 때 사용하는 것이 좋습니다.",
    "shiTips": "필터는 대부분의 게임 시나리오에 적합합니다.",
    "shi2Tips": "필터는 캐주얼, 액션 또는 어드벤처 게임에 적합하며, 색채 포화도를 강화하여 게임 화면을 더욱 생생하고 매력적이게 합니다.",
    "ruiTips": "필터 색상이 섬세하고 빛과 그림자가 부드러워 환상적, 따뜻하거나 향수를 자극하는 장면에 적합",
    "qingTips": "밝은 톤, 높은 대비, 선명한 디테일, 생생하고 밝은 장면을 표현하는 데 적합",
    "xianTips": "높은 대비 및 밝기 설정으로 어두운 장면에서 디테일이 왜곡되지 않으면서 밝은 장면에서는 눈부심을 방지합니다.",
    "dianTips": "화면 밝기와 색상을 적절히 증가시켜 영화와 같은 시각 효과를 최대한 구현하십시오",
    "benTips": "하얀색 빛 효과를 줄여 순백색 게임 장면의 눈부심을 완화합니다",
    "fangTips": "오픈월드/어드벤처 게임 최적화, 밝기와 대비 개선으로 선명한 화면 구현",
    "jiaoTips": "롤플레잉 및 시뮬레이션 게임에 적합, 톤 밸런스 조정, 시각적 사실감 향상",
    "jieTips": "스토리가 풍부하고 감정이 섬세한 게임에 적합, 디테일과 부드러움을 강화해 더 정교한 화면 표현",
    "jingTips": "액션/경쟁 게임 최적화, 선명도 및 대비 개선으로 더 날카로운 화면 제공",
    "xiuTips": "힐링/캐주얼 게임에 적합, 따뜻한 색조 및 부드러움 강조로 더욱 따뜻한 화면 효과 제공",
    "qihuanTips": "판타지 요소와 풍부한 색채가 있는 장면에 적합하며 색상 채도를 높여 강한 시각적 효과를 제공합니다.",
    "shengTips": "색상과 디테일을 강화하여 장면의 생생함과 현실감을 강조합니다,",
    "sheTips": "FPS, 퍼즐 또는 모험 게임에 적합하며, 디테일과 대비를 강화하여 게임 세계의 사실감을 향상시킵니다.",
    "she2Tips": "슈팅, 레이싱 또는 격투 게임에 적합. 고화질 디테일과 다이내믹한 표현 강조로 게임 몰입감과 시각적 효과 향상",
    "an2Tips": "어두운 환경의 장면을 더 선명하게 표시하며, 어두운 또는 야간 시나리오에 적합합니다.",
    "wenTips": "예술, 모험 또는 카주얼 게임에 적합하며, 부드러운 색조와 라이트닝 효과를 연출하여 장면의 우아함과 따뜻한 느낌을 더합니다。",
    "jing2Tips": "경쟁형, 음악 리듬 또는 야간 도시 시나리오에 적합한 게임, 화려한 색상과 조명 효과 강조,",
    "jing3Tips": "경쟁, 액션 또는 판타지 장르 게임에 적합하며 색상 대비를 강화하여 시각 효과를 더욱 풍부하고 생동감 있게 합니다.",
    "onlyVipCanUse": "이 필터는 VIP 사용자만 이용할 수 있습니다"
  },
  "GameRebound": {
    "noGame": "게임 기록 없음",
    "noGameRecord": "아직 게임 기록이 없습니다! 지금 바로 게임을 시작해 보세요!",
    "gameDuration": "오늘의 게임 시간:",
    "gameElectricity": "일일 전력 사용량",
    "degree": "도",
    "gameCo2": "당일 CO₂ 배출량:",
    "gram": "키",
    "manualRecord": "수동 기록",
    "recordDuration": "녹화 시간：",
    "details": "세부 정보",
    "average": "평균",
    "minimum": "최소값",
    "maximum": "최대값",
    "occupancyRate": "사용률",
    "voltage": "전압",
    "powerConsumption": "소비 전력",
    "start": "시작:",
    "end": "종료",
    "Gametime": "게임 시간:",
    "Compactdata": "데이터 최적화",
    "FullData": "전체 데이터",
    "PerformanceAnalysis": "성능 분석",
    "PerformanceAnalysis2": "이벤트 보고",
    "HardwareStatus": "하드웨어 상태",
    "totalPower": "총 전력 소비",
    "TotalEmissions": "총 배출",
    "PSS": "참고: 아래 그래프의 데이터는 평균값을 나타냅니다",
    "FrameGenerationTime": "프레임 생성 시간",
    "GameResolution": "게임 해상도",
    "FrameGenerationTimeTips": "이 데이터 포인트는 비정상적으로 높아 통계에 포함되지 않았습니다",
    "FrameGenerationTimeTips2": "이 데이터 포인트는 비정상적으로 낮아서 통계에 포함되지 않았습니다",
    "noData": "없음",
    "ProcessorOccupancy": "CPU 사용량",
    "ProcessorFrequency": "프로세서 주파수",
    "ProcessorTemperature": "프로세서 온도",
    "ProcessorHeatPower": "프로세서 열 설계 전력",
    "GraphicsCardOccupancy": "그래픽 카드의 D3D 사용량",
    "GraphicsCardOccupancyTotal": "GPU 사용량 총합",
    "GraphicsCardFrequency": "GPU 주파수",
    "GraphicsCardTemperature": "GPU 온도",
    "GraphicsCardCoreTemperature": "GPU 코어 핫스팟 온도",
    "GraphicsCardHeatPower": "GPU 열 출력",
    "GraphicsCardMemoryTemperature": "GPU 메모리 온도",
    "MemoryOccupancy": "메모리 사용량",
    "MemoryTemperature": "메모리 온도",
    "MemoryPageFaults": "메모리 페이징 인터럽트",
    "Duration": "기간",
    "Time": "시간",
    "StartStatistics": "통계 시작",
    "Mark": "태그",
    "EndStatistics": "통계 종료",
    "LineChart": "선 그래프",
    "AddPointInGame_m1": "게임 내에서 누르세요",
    "AddPointInGame_m2": "마킹 포인트 추가 가능",
    "LeftMouse": "마우스 왼쪽 버튼으로 표시/숨김 전환, 오른쪽 버튼으로 색상 변경",
    "DeleteThisLine": "폴리라인 삭제",
    "AddCurve": "곡선 추가",
    "AllCurvesAreHidden": "모든 곡선 그래프가 숨겨져 있습니다",
    "ThereAreSamplingData": "총 샘플 데이터:",
    "Items": "항목",
    "StatisticsData": "통계 데이터",
    "electricity": "전력 사용",
    "carbonEmission": "탄소 배출",
    "carbonEmissionTips": "이산화탄소 배출량(kg) = 소비 전력량(kWh) × 0.785",
    "D3D": "D3D 사용량:",
    "TOTAL": "총 사용량:",
    "Process": "공정：",
    "L3Cache": "L3 캐시:",
    "OriginalFrequency": "원래 주파수:",
    "MaximumBoostFrequency": "최대 터보 부스트:",
    "DriverVersion": "드라이버 버전:",
    "GraphicsCardMemoryBrand": "비디오 메모리 브랜드:",
    "Bitwidth": "버스 너비",
    "System": "시스템:",
    "Screen": "화면",
    "Interface": "인터페이스:",
    "Channel": "채널:",
    "Timing": "시퀀스:",
    "Capacity": "용량:",
    "Generation": "대수",
    "AddPoint_m1": "게임에서 누르기",
    "AddPoint_m2": "마킹 포인트 추가",
    "Hidden": "숨김",
    "Totalsampling": "총 샘플링 데이터:",
    "edition": "드라이버 버전:",
    "MainHardDisk": "기본 하드디스크",
    "SetAsStartTime": "시작 시간으로 설정",
    "SetAsEndTime": "종료 시간으로 설정",
    "WindowWillBe": "성능 통계 창이 있는 위치는",
    "After": "닫기 후",
    "NoLongerPopUpThisGame": "이 게임은 더 이상 표시되지 않습니다",
    "HideTemperatureReason": "온도 원인 숨기기",
    "HideTemperatureReason2": "이벤트 보고서 숨기기",
    "HideOtherReason": "다른 이유 숨기기",
    "CPUanalysis": "CPU 성능 분석",
    "TemperatureCause": "온도 원인",
    "tempSensorEvent": "온도 센서 이벤트",
    "NoTemperatureLimitation": "온도로 인한 CPU 성능 저하가 감지되지 않았습니다. 귀하의 쿨링 시스템은 이 게임의 요구사항을 완벽하게 충족하고 있습니다.",
    "NoTemperatureLimitation2": "온도 센서 이벤트가 없습니다. 귀하의 냉각 시스템은 이 게임의 요구사항을 완벽히 처리할 수 있습니다.",
    "performanceis": "선택한 내의",
    "Inside": "내부,",
    "TheStatisticsTimeOf": "통계 기간이 해당 트리거 조건을 충족합니다. 트리거 빈도가 가장 높은 원인은",
    "limited": "온도로 인한 성능 제한 총 시간 비율",
    "SpecificReasons": "구체적인 원인 및 온도 원인에서의 비율：",
    "OptimizationSuggestion": "최적화 제안:",
    "CPUtemperature": "CPU 온도가 과열되었습니다. CPU의 냉각 환경을 확인/개선하십시오.",
    "CPUoverheat": "메인보드 전원 공급으로 인한 CPU 과열 발생. 메인보드 관련 설정 확인 또는 냉각 환경 개선이 필요합니다.",
    "OtherReasons": "기타 이유",
    "NoPowerSupplyLimitation": "전원 공급/전력 소비로 인한 CPU 성능 제한이 없습니다. BIOS 전력 소비 설정이 이 게임의 요구사항을 완벽하게 충족합니다.",
    "PowerSupplyLimitation": "전원/전력 소모로 인한 성능 제한 총 시간의",
    "SpecificReasonsInOtherReasons": "구체적인 원인 및 다른 원인들 중 비중：",
    "PleaseCheckTheMainboard": "메인보드 전원 공급 상태를 확인하거나 BIOS 전력 설정을 조정하여 다른 원인으로 인한 CPU 성능 제한을 해결하십시오",
    "CPUcoretemperature": "코어 온도가 Tj,Max에 도달하여 제한됨",
    "CPUCriticalTemperature": "CPU 온도가 임계 온도에 도달함",
    "CPUCircuitTemperature": "CPU 패키지/링 버스가 Tj,Max 도달로 제한됨",
    "CPUCircuitCriticalTemperature": "CPU 패키지/링 버스가 임계 온도에 도달했습니다",
    "CPUtemperatureoverheating": "CPU 과열이 감지되었습니다. 자동으로 주파수를 낮추어 온도를 제어하고 하드웨어 오류를 방지합니다",
    "CPUoverheatingtriggered": "과열로 인해 냉각 메커니즘이 작동되고 CPU가 전압 및 주파수를 조정하여 전력 소모와 온도를 낮춥니다",
    "CPUPowerSupplyOverheating": "CPU가 메인보드 전원 공급의 심각한 과열로 인해 제한되었습니다",
    "CPUPowerSupplyLimitation": "메인보드 전원 과열로 인해 CPU가 제한됨",
    "CPUMaximumPowerLimitation": "코어는 최대 전력 소모 제한에 도달했습니다",
    "CPUCircuitPowerLimitation": "CPU 패키지/링 버스가 전력 제한에 도달했습니다",
    "CPUElectricalDesignLimitation": "전기 설계 제한 트리거 (ICCmax 전류 제한, PL4 최대 소비 전력 제한, SVID 전압 제한 등)",
    "CPULongTermPowerLimitation": "CPU 장기 전력 소모 한계 도달",
    "CPULongTermPowerinstantaneous": "CPU의 순간 소비 전력이 제한에 도달했습니다",
    "CPUPowerLimitation": "CPU 터보 주파수 저하 메커니즘 - 일반적으로 BIOS 또는 특정 소프트웨어에 의해 제한됨",
    "CPUPowerWallLimitation": "CPU 전력 제한",
    "CPUcurrentwalllimit": "CPU 전류 제한",
    "AiAgent": "GamePP 에이전트(AI Agent)",
    "AgentDesc": "홈으로 돌아가기",
    "fnBeta": "이 기능은 현재 초청 테스트 단계에 있습니다. 귀하의 GamePP 계정은 아직 테스트 액세스 권한을 받지 못했습니다.",
    "getAIReport": "AI 보고서 가져오기",
    "waitingAi": "보고서 생성 완료를 대기 중입니다",
    "no15mins": "게임 시간이 15분 미만이므로 유효한 AI 보고서를 가져올 수 없습니다",
    "timeout": "서버 요청 시간 초과",
    "agentId": "에이전트 ID：",
    "reDo": "보고서 다시 생성",
    "text2": "게임PP 에이전트: 온라인 AI 분석 보고서, 다음 내용은 AI 생성이며 참고용입니다.",
    "amdAiagentTitle": "게임PP 에이전트: AMD Ryzen AI 분석 보고서, 다음 내용은 AI 생성이며 참고용입니다.",
    "noCurData": "현재 데이터가 없습니다",
    "dataScreening": "데이터 필터링",
    "dataScreeningDescription": "이 기능은 지도 로딩, 로비 대기 시간 등과 같은 유효하지 않은 게임 시간대의 데이터 통계를 제외하는 데 목적이 있습니다. 0은 제외하지 않음을 의미합니다。",
    "excessivelyHighParameter": "과도한 매개변수",
    "tooLowParameter": "매개변수가 너무 낮음",
    "theMaximumValueIs": "최대값은",
    "theMinimumValueIs": "최소값은",
    "exclude": "제외",
    "dataStatisticsAtThatTime": "시의 데이터 통계",
    "itHasBeenGenerated": "생성 완료입니다。",
    "clickToView": "클릭하여 보기",
    "onlineAnalysis": "온라인 분석",
    "amdRyzenAILocalAnalysis": "AMD Ryzen AI 로컬 분석",
    "useTerms": "사용 조건:",
    "term1": "1. 라이젠 AI Max 프로세서 또는 라이젠 Al 300 시리즈 프로세서",
    "term2": "2.AMD NPU 드라이버 버전",
    "term3": "3. 통합 그래픽스 활성화",
    "conformsTo": "일치",
    "notInLineWith": "부적합",
    "theVersionIsTooLow": "버전이 너무 낮습니다",
    "canNotUseAmdNpu": "귀하의 설정은 사용 요건을 충족하지 않습니다. AMD NPU 엔티티 분석을 사용할 수 없습니다.",
    "unusable": "사용 불가",
    "downloadTheFile": "파일 다운로드",
    "downloadSource": "다운로드 소스：",
    "fileSize": "파일 크기: 약 8.34GB",
    "cancelDownload": "다운로드 취소",
    "filePath": "파일 위치",
    "generateAReport": "보고서 생성",
    "fileMissing": "파일이 누락되었습니다. 다시 다운로드해야 합니다。",
    "downloading": "다운로드 중...",
    "theModelConfigurationLoadingFailed": "모델 구성 불러오기 실패",
    "theModelDirectoryDoesNotExist": "모델 디렉터리가 존재하지 않습니다",
    "thereIsAMistakeInReasoning": "추론 오류",
    "theInputExceedsTheModelLimit": "입력이 모델 제한을 초과했습니다",
    "selectModelNotSupport": "선택한 다운로드 모델이 지원되지 않습니다",
    "delDirFail": "기존 모델 디렉터리 삭제 실패",
    "failedCreateModelDir": "모델 디렉터리 생성 실패",
    "modelNotBeenFullyDownload": "모델이 모두 다운로드되지 않았습니다",
    "agentIsThinking": "가가 에이전트가 생각 중입니다",
    "reasoningModelFile": "추론 모델 파일",
    "modelReasoningTool": "모델 추론 도구"
  },
  "SelectSensor": {
    "DefaultSensor": "기본 센서",
    "Change": "변경",
    "FanSpeed": "팬 속도",
    "MainGraphicsCard": "메인 GPU",
    "SetAsMainGraphicsCard": "메인 GPU로 설정",
    "GPUTemperature": "GPU 온도",
    "GPUHeatPower": "GPU 열 소비",
    "GPUTemperatureD3D": "GPU D3D 사용량",
    "GPUTemperatureTOTAL": "GPU 총 부하",
    "GPUTemperatureCore": "GPU 코어 핫스팟 온도",
    "MotherboardTemperature": "메인보드 온도",
    "MyAttention": "즐겨찾기",
    "All": "모두",
    "Unit": "단위:",
    "NoAttention": "팔로우되지 않은 센서",
    "AttentionSensor": "모니터링된 센서(Beta)",
    "GoToAttention": "포커스로 이동",
    "CancelAttention": "언팔로우",
    "noThisSensor": "센서 없음",
    "deviceAbout": "주변 기기 관련",
    "deviceBattery": "주변 장치 배터리 수준",
    "testFunction": "테스트 기능",
    "mouseEventRate": "폴링 속도",
    "relatedWithinTheGame": "게임 관련",
    "winAbout": "시스템",
    "trackDevicesBattery": "외장 장치 배터리 수준 추적",
    "ingameRealtimeMouseRate": "게임 실시간 사용 마우스 풀링 레이트",
    "notfoundDevice": "지원되는 장치를 찾을 수 없습니다。",
    "deviceBatteryNeedMythcool": "전력 표시를 지원하는 장치 목록: (Myth.Cool과 함께 사용해야 함)",
    "vkm1mouse": "발키리 M1 마우스",
    "vkm2mouse": "발키리 M2 마우스",
    "vk99keyboard": "Valkyrie 99 자석축 키보드",
    "logitechProWireless": "로지텍 PRO WIRELESS",
    "logitechProXSUPERLIGHT": "로지텍 PRO X SUPERLIGHT",
    "logitechProXSUPERLIGHT2": "로지텍 PRO",
    "logitechPro2LIGHTSPEED": "로지텍 PRO 2 LIGHTSPEED",
    "steelSeriesArctisGameBuds": "세레Arctis GameBuds",
    "steelSeriesArctis7PPlus": "스틸시리즈 Arctis 7P Plus",
    "razerV3": "레이저 발키리 V3 프로페셔널",
    "razerV2": "레이저 바이퍼 V2 프로페셔널 에디션",
    "wireless": "무선",
    "logitechNeedGhub": "로지텍이 장치 모델을 가져올 수 없는 경우 GHUB을 다운로드하십시오",
    "chargingInProgress": "충전 중",
    "inHibernation": "휴면 중"
  },
  "video": {
    "videoRecord": "비디오 녹화",
    "recordVideo": "녹화된 동영상",
    "scheme": "프로필",
    "suggestScheme": "권장 플랜",
    "text1": "이 설정에서 1분 동영상 크기는 약",
    "text2": "이 기능은 추가 시스템 리소스를 사용합니다",
    "low": "낮음",
    "mid": "홈",
    "high": "높음",
    "1080p": "네이티브",
    "RecordingFPS": "녹화 FPS",
    "bitRate": "비디오 비트레이트",
    "videoResolution": "비디오 해상도",
    "startStopRecord": "녹화 시작/중지",
    "instantReplay": "인스턴트 리플레이",
    "instantReplayTime": "즉시 재생 기간",
    "showIngame": "게임 내 컨트롤 패널 실행",
    "CaptureMode": "캡처 방법",
    "gameWindow": "게임 창",
    "desktopWindow": "데스크톱 창",
    "fileSavePath": "파일 저장 경로",
    "selectVideoSavePath": "녹화 저장 경로 선택",
    "diskFreeSpace": "하드디스크 남은 공간：",
    "edit": "수정",
    "open": "열기",
    "displayMouse": "마우스 커서 표시",
    "recordMicrophone": "마이크 녹음",
    "gameGraphics": "게임 원본 화면"
  },
  "Setting": {
    "common": "일반",
    "personal": "개인 설정",
    "messageNotification": "메시지 알림",
    "sensorReading": "센서 읽기 값",
    "OLEDscreen": "OLED 화면 고정 방지",
    "performanceStatistics": "성능 통계",
    "shortcut": "단축키",
    "ingameSetting": "게임 설정 저장",
    "other": "기타",
    "otherSettings": "기타 설정",
    "GeneralSetting": "일반 설정",
    "softwareVersion": "소프트웨어 버전",
    "checkForUpdates": "업데이트 확인",
    "updateNow": "지금 업데이트",
    "currentVersion": "현재 버전",
    "latestVersion": "최신 버전",
    "isLatestVersion": "현재 버전이 이미 최신 버전입니다.",
    "functionModuleUpdate": "기능 모듈 업데이트",
    "alwaysUpdateModules": "모든 설치된 기능 모듈을 최신 버전으로 유지합니다",
    "lang": "언어",
    "bootstrap": "자동 시작",
    "powerOn_m1": "시작",
    "powerOn_m2": "초 후에 자동 시작됨",
    "defaultDelay": "기본값은 40초입니다",
    "followSystemScale": "시스템 확대 따라가기",
    "privacySettings": "개인정보 설정",
    "JoinGamePPPlan": "GamePP 사용자 경험 개선 프로그램에 참여하세요",
    "personalizedSetting": "개인화 설정",
    "restoreDefault": "기본 설정 복원",
    "color": "색",
    "picture": "이미지",
    "video": "비디오",
    "browse": "탐색",
    "clear": "지우기",
    "mp4VideoOrPNGImagesCanBeUploaded": "MP4 동영상/PNG 이미지 업로드 가능",
    "transparency": "투명도",
    "backgroundColor": "배경 색상",
    "textFont": "본문 글꼴",
    "message": "메시지",
    "enableInGameNotifications": "게임 내 알림을 활성화",
    "messagePosition": "게임 내 표시 위치",
    "leftTop": "좌상단",
    "leftCenter": "왼쪽 중앙",
    "leftBottom": "왼쪽 하단 모서리",
    "rightTop": "오른쪽 상단 모서리",
    "rightCenter": "우측 중앙",
    "rightBottom": "오른쪽 하단 모서리",
    "noticeContent": "알림 내용",
    "gameInjection": "게임 인젝션",
    "ingameShow": "게임 내 표시",
    "inGameMonitoring": "게임 내 모니터링",
    "gameFilter": "게임 필터",
    "start": "시작",
    "endMarkStatistics": "종료 마커 통계",
    "readHwinfoFail": "HWINFO 하드웨어 정보 읽기 실패",
    "dataSaveDesktop": "데이터가 클립보드와 바탕 화면 파일에 저장되었습니다",
    "TheSensorCacheCleared": "센서 캐시 데이터가 삭제되었습니다",
    "defaultSensor": "기본 센서",
    "setSensor": "센서 선택",
    "refreshTime": "데이터 새로 고침 시간",
    "recommend": "기본",
    "sensorMsg": "시간 간격이 짧을수록 성능 소비가 커집니다. 신중하게 선택하십시오.",
    "exportData": "데이터 내보내기",
    "exportHwData": "하드웨어 정보 데이터 내보내기",
    "sensorError": "센서 읽기 이상",
    "clearCache": "캐시 삭제",
    "littleTips": "팁: 성능 보고서는 게임 시작 후 2분 이내에 생성됩니다",
    "disableAutoShow": "성능 통계 창 자동 팝업 비활성화",
    "AutoClosePopUpWindow_m1": "성능 통계 창은 지정된 시간 후에 자동으로 닫힙니다:",
    "AutoClosePopUpWindow_m2": "초",
    "abnormalShutdownReport": "비정상 종료 보고서",
    "showWeaAndAddress": "날씨 및 위치 정보 표시",
    "autoScreenShots": "표시할 때 게임 화면을 자동으로 스크린샷",
    "keepRecent": "최근 기록을 보관할 수：",
    "noLimit": "무제한",
    "enableInGameSettingsSaving": "게임 내 설정 저장 사용",
    "debugMode": "디버그 모드",
    "enableDisableDebugMode": "디버그 모드 켜기/끄기",
    "audioCompatibilityMode": "오디오 호환 모드",
    "quickClose": "빠른 종료",
    "closeTheGameQuickly": "게임 프로세스를 빠르게 종료합니다",
    "cancel": "취소",
    "confirm": "확인",
    "MoveInterval_m1": "데스크톱 및 게임 내 모니터링은 약간 이동합니다:",
    "MoveInterval_m2": "분",
    "text3": "게임 종료 후 성능 보고서 창이 팝업되지 않으며 기록 내역만 보존됩니다",
    "text5": "예외 종료 시 자동으로 보고서가 생성됩니다. 이 기능을 활성화하면 추가 시스템 리소스를 소비합니다.",
    "text6": "단축키 기능 사용 시 다른 게임의 단축키와 충돌할 수 있습니다. 신중하게 설정해 주세요.",
    "text7": "키보드 단축키를 \"없음\"으로 설정하려면 백스페이스 키를 사용하십시오",
    "text8": "프로세스 이름을 기반으로 게임 실행 시 필터, 게임 내 모니터링 등의 기능 상태를 유지합니다",
    "text9": "실행 시 계속해서 실행 로그를 기록함; 비활성화 시 로그 파일 삭제됨 (비활성화 권장)",
    "text10": "활성화 후 메인보드 센서를 가져올 수 없으며, GamePP로 인한 오디오 문제를 해결할 수 있습니다",
    "text11": "Alt+F4를 연속 2번 사용하면 현재 게임에서 빠르게 종료할 수 있습니다",
    "text12": "이 모드를 사용하려면 GamePP를 다시 시작해야 합니다. 계속하시겠습니까?",
    "openMainUI": "앱 표시",
    "setting": "설정",
    "feedback": "문제 피드백",
    "help": "도움말",
    "sensorReadingSetting": "센서 읽기 설정",
    "searchlanguage": "검색 언어"
  },
  "GamePlusOne": {
    "year": "년",
    "month": "월",
    "day": "일",
    "success": "성공",
    "fail": "실패",
    "will": "현재",
    "missedGame": "놓친 게임",
    "text1": "금액, 약￥",
    "text2": "누적 수령 게임 수량",
    "text3": "버전",
    "gamevalue": "게임 가치",
    "gamevalue1": "받기",
    "total": "총 청구",
    "text4": "게임 수, 누적 절약 시간",
    "text6": "제품, 가치",
    "Platformaccountmanagement": "플랫폼 계정 관리",
    "Missed1": "(수령하지 않음)",
    "Received2": "(수령 성공)",
    "Receivedsoon2": "현재 받을 수 있음",
    "Receivedsoon": "현재 받을 수 있음",
    "Missed": "수령하지 못함",
    "Received": "성공적으로 수령",
    "Getaccount": "계정 수령",
    "Worth": "값",
    "Auto": "자동",
    "Manual": "수동",
    "Pleasechoose": "게임을 선택하십시오",
    "Receive": "지금 받기",
    "Selected": "선택됨",
    "text5": "게임",
    "Automatic": "자동으로 수령 중...",
    "Collecting": "수령 중...",
    "ReceiveTimes": "이번 달 수령 횟수",
    "Thefirst": "№",
    "Week": "주",
    "weekstotal": "총 53주",
    "Return": "홈",
    "Solutionto": "계정 바인딩 실패 - 해결 방법",
    "accounts": "바인딩된 계정 수",
    "Addaccount": "계정 추가",
    "Clearcache": "캐시 지우기",
    "Bindtime": "바인딩 시간",
    "Status": "상태",
    "Normal": "정상",
    "Invalid": "무효",
    "text7": "게임 수, 누적 절약 금액",
    "Yuan": "원",
    "untie": "해제",
    "disable": "비활성화",
    "enable": "활성화",
    "gamePlatform": "게임 플랫폼",
    "goStorePage": "상점 페이지로 이동",
    "receiveEnd": "마감 후 수령",
    "loginPlatformAccount": "로그인한 플랫폼 계정",
    "waitReceive": "수령 대기 중",
    "receiveSuccess": "수령 성공",
    "accountInvalid": "계정 만료됨",
    "alreadyOwn": "보유됨",
    "networkError": "네트워크 이상",
    "noGame": "게임 본체 없음",
    "manualReceiveInterrupt": "수동 수령 중단",
    "receiving": "수령 중",
    "agree": "저는 '시가이치 수령 계획'에 가입하는 것에 동의합니다。",
    "again": "다시 받기"
  },
  "shutdownTimer": {
    "timedShutdown": "예약 종료",
    "currentTime": "현재 시간：",
    "setCountdown": "카운트다운 설정",
    "shutdownInSeconds": "X초 후에 종료",
    "shutdownIn": "종료 후",
    "goingToBe": "진행됩니다",
    "executionPlan": "실행 계획",
    "startTheClock": "타이머 시작",
    "stopTheClock": "계획 취소",
    "isShuttingDown": "예약된 종료 계획 실행 중:",
    "noplan": "현재 종료 계획 없음",
    "hour": "시간",
    "min": "분",
    "sec": "초",
    "ms": "밀리초",
    "year": "년",
    "month": "월",
    "day": "일",
    "hours": "시간"
  },
  "screenshotpage": {
    "screenshot": "스크린샷",
    "screenshotFormat": "게임 화면 캡처를 위해 설계되었으며, JPG/PNG/BMP 형식으로 저장을 지원하고, 빠르게 게임 화면을 캡처할 수 있으며, 고해상도 품질의 손상 없는 출력을 보장합니다",
    "Turnon": "자동 스크린샷 활성화, 매",
    "seconds": "초",
    "takeScreenshot": "화면 캡처를 자동으로 실행하십시오",
    "screenshotSettings": "이 설정은 게임 내에서 체크해도 무효입니다",
    "saveGameFilterAndMonitoring": "스크린샷에 '게임 필터' 및 '게임 내 모니터링' 효과 저장",
    "disableScreenshotSound": "화면 캡처 음성 알림 끄기",
    "imageFormat": "이미지 형식",
    "recommended": "추천",
    "viewingdetails": "화질 디테일 유지, 적절한 크기, 디테일 확인에 적합",
    "saveSpace": "이미지 품질 압축 가능, 최소 크기, 공간 절약",
    "ultraQuality": "초고화질 비압축 영상, 파일 용량이 매우 큽니다. 화질을 중시하는 플레이어의 세이브 데이터 저장에 추천",
    "fileSavePath": "파일 저장 경로",
    "hardDiskSpace": "하드디스크 사용 가능 공간:",
    "minutes": "분"
  },
  "DesktopMonitoring": {
    "desktopMonitoring": "데스크탑 모니터링",
    "SomeSensors": "모니터링용으로 일부 센서를 추천합니다. 삭제하거나 추가할 수 있습니다",
    "AddComponent": "새로운 구성 요소 추가",
    "Type": "유형",
    "Remarks": "메모",
    "AssociatedSensor": "센서 연결",
    "Operation": "작업",
    "Return": "뒤로",
    "TimeSelection": "시간 선택：",
    "Format": "형식：",
    "Rule": "규칙：",
    "Coordinate": "좌표：",
    "CustomTextContent": "사용자 지정 텍스트 내용:",
    "SystemTime": "시스템 시간",
    "China": "중국",
    "Britain": "영국",
    "America": "미국",
    "Russia": "러시아",
    "France": "프랑스",
    "DateAndTime": "날짜 및 시간",
    "Time": "시간",
    "Date": "날짜",
    "Week": "요일",
    "DateAndTimeAndWeek": "날짜+시간+요일",
    "TimeAndWeek": "시간+요일",
    "Hour12": "12시간 형식",
    "Hour24": "24시간제",
    "SelectSensor": "센서 선택:",
    "AssociatedSensor1": "센서 연결:",
    "SensorUnit": "센서 단위：",
    "Second": "초:",
    "Corner": "둥근 모서리:",
    "BackgroundColor": "배경색：",
    "ProgressColor": "진행 색상:",
    "Font": "폰트：",
    "SelectFont": "글꼴 선택",
    "FontSize": "폰트 크기：",
    "Color": "색상：",
    "Style": "스타일:",
    "Bold": "볼드체",
    "Italic": "이텔릭체",
    "Shadow": "그림자",
    "ShadowPosition": "그림자 위치：",
    "ShadowEffect": "그림자 효과：",
    "Blur": "흐릿하게",
    "ShadowColor": "그림자 색상：",
    "SelectFromLocalFiles": "로컬 파일에서 선택:",
    "UploadImageVideo": "이미지/동영상을 업로드",
    "UploadSVGFile": "SVG 파일 업로드",
    "Width": "너비：",
    "Height": "고: ",
    "Effect": "효과:",
    "Rotation": "회전:",
    "WhenTheSensorValue": "센서 값이 이상일 때",
    "conditions": "조건이 충족되지 않을 때（센서 값이 0이면 회전하지 않습니다）",
    "Clockwise": "시계 방향",
    "Counterclockwise": "반시계 방향",
    "QuickRotation": "빠른 회전",
    "SlowRotation": "느린 회전",
    "StopRotation": "회전 중지",
    "StrokeColor": "윤곽선 색상：",
    "Path": "경로",
    "Color1": "색상",
    "ChangeColor": "색 변경",
    "When": "당",
    "SensorValue": "센서 값이 이상",
    "SensorValue1": "센서 값이 이하인 경우",
    "SensorValue2": "센서 값이 동일합니다",
    "MonitoringSettings": "모니터링 설정",
    "RestoreDefault": "기본 설정으로 복원",
    "Monitor": "모니터",
    "AreaSize": "영역 크기",
    "Background": "배경",
    "ImageVideo": "이미지/동영상",
    "PureColor": "단색",
    "Select": "선택",
    "ImageVideoDisplayMode": "이미지/영상 표시 방법",
    "Transparency": "투명도",
    "DisplayPosition": "표시 위치",
    "Stretch": "스트레치",
    "Fill": "채우기",
    "Adapt": "적응",
    "SelectThePosition": "칸을 클릭하여 빠르게 위치를 선택할 수 있습니다",
    "CurrentPosition": "현재 위치：",
    "DragLock": "드래그 잠금",
    "LockMonitoringPosition": "모니터 위치 잠금(잠금 후 모니터를 드래그할 수 없습니다)",
    "Unlockinterior": "내부 요소 드래그 허용",
    "Font1": "글꼴",
    "GameSettings": "게임 설정",
    "CloseDesktopMonitor": "게임이 실행 중일 때 데스크톱 모니터링을 자동으로 비활성화합니다.",
    "OLED": "OLED 번인 보호",
    "Display": "표시",
    "PleaseEnterContent": "내용을 입력하세요",
    "NextStep": "다음",
    "Add": "추가",
    "StylesForYou": "일부 모니터링 스타일을 추천드립니다. 선택 후 적용할 수 있으며 향후 더 많은 스타일이 추가될 예정입니다.",
    "EditPlan": "프로필 편집",
    "MonitoringStylePlan": "감시 스타일 설정",
    "AddDesktopMonitoring": "데스크톱 모니터링 추가",
    "TextLabel": "텍스트 라벨",
    "ImageVideo1": "이미지, 동영상",
    "SensorGraphics": "센서 그래픽",
    "SensorData": "센서 데이터",
    "CustomText": "사용자 정의 텍스트",
    "DateTime": "날짜 및 시간",
    "Image": "이미지",
    "Video": "동영상",
    "SVG": "SVG",
    "ProgressBar": "진행 표시줄",
    "Graphics": "그래픽",
    "UploadImage": "이미지 업로드",
    "UploadVideo": "동영상 업로드",
    "RealTimeMonitoring": "CPU 및 GPU 온도와 사용률 등의 데이터를 실시간으로 모니터링하고, 자유로운 드래그 앤 드롭 레이아웃 설정과 맞춤형 스타일 조정으로 성능과 데스크탑 미학을 조절하세요",
    "Chart": "차트",
    "Zigzagcolor": "선형 색상(시작점)",
    "Zigzagcolor1": "절선 색상(종점)",
    "Zigzagcolor2": "선형 차트 영역 색상(시작)",
    "Zigzagcolor3": "선형 차트 영역 색상(끝점)",
    "CustomMonitoring": "사용자 지정 모니터링"
  }
}
//messageEnd 
 export default kr 