<template>
  <!--其他设置-->
  <div id="qt"></div>
  <el-collapse-item :title="$t('Setting.otherSettings')" name="10">
    <div class="setting-item">
      <section class="left-box">
        <p class="setting-item-title" style="margin-bottom: 10px">{{$t('Setting.debugMode')}}</p>
        <el-checkbox v-model="debugMode" @change="DebugModeChange">
          <span style="color: var(--font-color); font-size: 0.12rem"
          >{{$t('Setting.enableDisableDebugMode')}}</span>
        </el-checkbox>
        <br />
        <text>{{$t('Setting.text9')}}</text>

        <p class="setting-item-title mt-25" style="margin-bottom: 10px">
          {{$t('Setting.audioCompatibilityMode')}}
        </p>
        <el-checkbox v-model="musicMode" @change="set490">
          <span style="color: var(--font-color); font-size: 0.12rem"
            >{{$t('Setting.audioCompatibilityMode')}}</span
          >
        </el-checkbox>
        <br />
        <text>{{$t('Setting.text10')}}</text>
      </section>
      <section class="right-box">
        <p class="setting-item-title" style="margin-bottom: 10px">{{$t('Setting.quickClose')}}</p>
        <el-checkbox v-model="doubleAltF4" @change="set329">
          <span style="color: var(--font-color); font-size: 0.12rem"
            >{{$t('Setting.closeTheGameQuickly')}}</span
          >
        </el-checkbox>
        <br />
        <text>{{$t('Setting.text11')}}</text>
      </section>
    </div>
  </el-collapse-item>
  <el-dialog v-model="dialogVisible" width="500">
    <span style="color: var(--font-color)"
    >{{$t('Setting.text12')}}</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{$t('Setting.cancel')}}</el-button>
        <el-button type="primary" @click="confirm">{{$t('Setting.confirm')}}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { useScroll } from "../hooks/useScroll";
import { gameppBaseSetting } from "@/modules/Game_Home/stores";
import { onMounted, ref } from "vue";
import { GPP_SendStatics } from '@/uitls/sendstatics';
// @ts-ignore
const gamepp = window.gamepp;
useScroll("qt");
const store = gameppBaseSetting();
let dialogVisible = ref(false);
let debugMode = ref(false);
let doubleAltF4 = ref(false);
let musicMode = ref(false);

let flag = "";

onMounted(() => {
  try {
    debugMode.value = gamepp.setting.getGamePPLogState.sync();
  } catch (e) {}
  try {
    doubleAltF4.value = gamepp.setting.getInteger.sync(329) === 1;
  } catch (error) {}
  try {
    musicMode.value = gamepp.setting.getInteger.sync(490) === 1;
  } catch (error) {}
});

async function handleClose() {
  dialogVisible.value = false;
  if (flag == "debug") {
    debugMode.value = false;
    await gamepp.setting.enableGamePPLog.promise(false);
    await gamepp.setting.setInteger.promise(1000000, -1);
  }
  if (flag == "music") {
    musicMode.value = false;
    await gamepp.setting.setInteger.promise(490, 0);
    await gamepp.setting.enableHWiNFOAudioCompatibleMode.promise(false);
  }
}

async function confirm() {
  dialogVisible.value = false;
  if (flag == "debug") {
    doTry(async () => {
      await gamepp.setting.enableGamePPLog.promise(true);
    });
    doTry(async () => {
      await gamepp.setting.setInteger.promise(1000000, 1);
    });
  }
  if (flag == "music") {
    doTry(async () => {
      await gamepp.setting.setInteger.promise(490, 1);
    });
    doTry(async () => {
      await gamepp.setting.enableHWiNFOAudioCompatibleMode.promise(true);
    });
  }
  setTimeout(() => {
    doTry(async () => {
      await gamepp.restart.promise();
    });
  }, 150);
}

function DebugModeChange() {
  if (debugMode.value) {
    GPP_SendStatics(100780)
    dialogVisible.value = true;
    flag = "debug";
  } else {
    GPP_SendStatics(100781)
    handleClose();
  }
}
function doTry(func: Function, ...args: any[]) {
  try {
    func(...args);
  } catch (e) {
    console.log("%c err: ", "background:red;font-size:16px", e);
  }
}

function set329() {
  if (doubleAltF4.value) {
    GPP_SendStatics(100784)
    gamepp.setting.setInteger.sync(329, 1);
  } else {
    GPP_SendStatics(100785)
    gamepp.setting.setInteger.sync(329, 0);
  }
}

function set490() {
  if (musicMode.value) {
    GPP_SendStatics(100782)
    dialogVisible.value = true;
    flag = "music";
    gamepp.setting.setInteger.sync(490, 1);
  } else {
    GPP_SendStatics(100783)
    gamepp.setting.setInteger.sync(490, 0);
  }
}
</script>

<style scoped lang="scss">
#qt {
  margin-top: 20px;
}
</style>
<style>
.el-dialog {
  --el-dialog-bg-color: #22232e;
}

.el-dialog__footer {
  position: relative;
  height: 66px;
}



.el-button:hover {
  background-color: #383949;
  border-color: var(--el-button-hover-border-color);
  color: var(--font-color);
  outline: none;
}
</style>
