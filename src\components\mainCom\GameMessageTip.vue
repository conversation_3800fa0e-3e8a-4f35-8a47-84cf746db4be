<script setup lang="ts">

import {onMounted, reactive,defineEmits} from "vue";
import RightTopIcons from "@/components/mainCom/RightTopIcons.vue";

const emits = defineEmits(['close','show'])
const data = reactive({
  time: 5,
  show: false,
  process: 'DeltaForceClient-Win64-Shipping.exe',
  no_more_show: false,
  isInGame: false,
  msg: '游戏加加坚决抵制外挂！只欢迎绿玩使用！如有使用外设“宏“或外挂等影响游戏平衡的行为，请不要使用游戏加加！如果因外挂、外设宏导致账号封禁，从而抹黑游戏加加，我们有权停止为您提供软件服务！'
})

onMounted(() => {
  try {
    const window_title = gamepp.webapp.windows.getTitle.sync()
    const GameMessageInGameShow = window.localStorage.getItem('GameMessageInGameShow')
    const GameMessageExitShow = window.localStorage.getItem('GameMessageExitShow')
    let localData = null
    if (window_title === '游戏加加游戏内提示窗口' && GameMessageInGameShow) {
      localData = JSON.parse(GameMessageInGameShow)
      window.localStorage.removeItem('GameMessageInGameShow')
    } else if (GameMessageExitShow) {
      localData = JSON.parse(GameMessageExitShow)
      window.localStorage.removeItem('GameMessageExitShow')
    }
      let ingame_message_noshow: any = window.localStorage.getItem('ingame_message_noshow')
      if (ingame_message_noshow) {
          ingame_message_noshow = JSON.parse(ingame_message_noshow)
          ingame_message_noshow = ingame_message_noshow.map((item:any)=>{
              if (!item.includes('.exe')) {
                  item = item+'.exe'
              }
              return item
          })
      }else{
          ingame_message_noshow = []
      }
    if (localData && !ingame_message_noshow?.includes(localData.process)) {
      if (!localData.time) localData.time = 10000
      data.process = localData.process;
      data.msg = localData.describe;
      data.time = localData.time / 1000;
      data.isInGame = localData.isInGame;
      const str = window.localStorage.getItem('rebound_details_v2_open_by')
      if (str === 'bg') {
        emits('show')
      }
      data.show = true;
      setInterval(() => {
        data.time--
        if (data.time <= 0) {
          closeWindow()
        }
      }, 1000)
    }
  }catch (e) {

  }
})

function closeWindow() {
  try {
    emits('close')
    gamepp.webapp.windows.close.promise('ingame_message')
  } catch (e) {
    data.show = false;
  }
}

function changeNoMoreShow() {
  if (data.no_more_show) {
    let _arr = []
    const localData = window.localStorage.getItem('ingame_message_noshow')
    if (localData) {
      _arr = JSON.parse(localData)
    }
    if (data.process) _arr.push(data.process)
    window.localStorage.setItem('ingame_message_noshow', JSON.stringify(_arr))
  } else {
    let _arr = []
    const localData = window.localStorage.getItem('ingame_message_noshow')
    if (localData) {
      _arr = JSON.parse(localData)
    }
    if (data.process) _arr.splice(_arr.indexOf(data.process), 1)
    window.localStorage.setItem('ingame_message_noshow', JSON.stringify(_arr))
  }
}
</script>

<template>
    <div class="GameMessageTip" v-show="data.show" :style="{'--opacity':data.isInGame?0.8:1}">
        <div class="GameMessageTip_header">
            <img class="logo_gpp" src="../../assets/img/Public/logo_gpp.png" alt="">
            <span class="statement">声明</span>

            <span class="close_tip">窗口将于 <span style="color: #3579d5;">{{data.time}}s</span> 后关闭</span>

            <div>
                <el-checkbox v-model="data.no_more_show" @change="changeNoMoreShow" label="不再提示"></el-checkbox>
            </div>
            <RightTopIcons close-icon @close="closeWindow"/>
        </div>
        <div class="content">
            <div class="process-info"><span v-show="data.isInGame">检测到正在运行</span> {{ data.process }}</div>
            <div class="msg-content" v-html="data.msg"></div>

            <img src="../assets/img/1.webp" alt="">
        </div>
        <div class="bottom" @click="closeWindow">
            <el-button style="width: 120px;height: 40px;" color="#3579d5" type="primary">我知道了</el-button>
        </div>
        <!--<div  class="isingame-bottom">连按两次ESC以快速关闭本窗口</div>-->
    </div>
</template>

<style scoped lang="scss">
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.GameMessageTip {
  --opacity: 0.8;
  --el-color-primary: #3579d5;
  width: 420px;
  height: 520px;
  background: rgba(34, 35, 46, var(--opacity));
  box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  position: relative;

  .GameMessageTip_header {
    width: 420px;
    height: 40px;
    background: rgba(52, 54, 71, var(--opacity));
    padding-left: 10px;
    box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    overflow: hidden;
    font-size: 12px;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    color: #FFFFFF;

    :deep(.el-checkbox__label) {
      font-size: 12px !important;
    }

    .logo_gpp {
      width: 18px;
      height: 18px;
      margin-right: 9px;
    }

    .statement {
      margin-right: auto;
    }

    .close_tip {
      display: flex;
      flex-flow: row nowrap;
      margin-right: 15px;
      gap: 5px;
      color: #777777;
    }
  }

  .content {
    display: flex;
    flex-flow: column nowrap;
    align-items: center;
    font-size: 14px;
    padding: 0 39px;

    .process-info {
      color: #3579D5;
      font-size: 14px;
      margin-top: 29px;
      margin-bottom: 31px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .msg-content {
      text-indent: 1em;
      line-height: 30px;
      font-weight: 400;
      font-size: 14px;
      color: #FFFFFF;
    }
  }

  .bottom {
    position: absolute;
    width: 120px;
    left: 50%;
    transform: translateX(-50%);
    bottom: 50px;
  }

  .isingame-bottom {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 25px;
    color: #777777;
  }
}
</style>
