<script setup lang="ts">
import { ref,reactive,watch,computed,onBeforeMount,onUnmounted,onMounted,watchEffect } from 'vue';
import * as XLSX from 'xlsx';
import { gamepp } from 'gamepp';
import { ElMessage } from "element-plus";
import { ElConfigProvider } from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import { AVGNum,MaxNum,MinNum,FormatSeconds,FormatTimestamp,FormatTimeCn,FormatTime,FormatTimePlus, DiskCapacityConversion,FormartMonthToNumber,RemoveAllSpace,RegExGetNum} from '../../uitls/GameppTools'
//引入天气照片
import 冰雹 from './assets/icon/ic_report_bingbao.png'
import 雨 from './assets/icon/ic_report_yu.png'
import 阴 from './assets/icon/ic_report_yin.png'
import 沙尘 from './assets/icon/ic_report_shachen.png'
import 晴天 from './assets/icon/ic_report_qing.png'
import 雾霾 from './assets/icon/ic_report_wumai.png'
import 云 from './assets/icon/ic_report_yun.png'
import 雪 from './assets/icon/ic_report_xue.png'
import 雾 from './assets/icon/ic_report_wu.png'
import 雷 from './assets/icon/ic_report_lei.png'
import 没有游戏记录 from './assets/img/illutrate_keyboard.png'
import RightTopIcons from "@/components/mainCom/RightTopIcons.vue";
import { useLanguage } from '@/uitls/useLanguage';
useLanguage()
//gamepp.rebound.sync()
const weaIcons:any = { "晴": 晴天, "阴": 阴, "雨": 雨, "云":  云, "雪": 雪, "冰": 冰雹, "雾": 雾, "雷": 雷, "霾": 雾霾 };
let packageversion = ref('3.2.192.0000')
let starttimeArr:any = ref([])
let DateArr:any  = ref([])
let uniqueArr = ref()
let TableListDetailData:any = ref([])
// let curItem:any = ref([])
let curItem:any = ref([])
let markItem:any = ref([]) //实验室打点
let AppDataDir:any = ref('')
let mirrorWidth = ref(1280)
let mirrorHeight = ref(720)
let checkRebound = ref([])
let checkLab = ref([])
let fullScreen = ref(false)
let isReady = ref(true)
let copyInfo = ref({
    name:'',
    startTime:'',
    runningTime:'',
    fps:{
        max:'',
        avg:'',
        min:''
    },
    cpu:{
        temp:'',
        usage:'',
        power:'',
    }
})
let curDayPlayTotal:any = ref({
    gametime:0,
    co2:0,
    electronic:0
})
let value = ref('')
let tabInfo = ref([
    {
        name:"游戏统计",
        choosen:true
    },
    {
        name:"标记统计",
        choosen:false
    }
])

onMounted( async () =>
{
    await initDataBase()
    window.addEventListener('resize', getWindowSize);
    await setZoom()
    let rick = await gamepp.package.getversion.promise("GameRebound")
    packageversion.value = rick.version
    gamepp.webapp.onInternalAppEvent.addEventListener(async data =>
   {
      console.warn('游戏完毕：更新性能统计:',data);

      if(data['action'] == 'updateGameRebound') //更新
      {
        setTimeout(async() => {
          await initDataBase()
        }, 1000);
      }
  });
})

let zoomLevel = ref(1)

const setZoom = async () =>
{
    const initialZoom = await gamepp.display.getScaleFromWindowInMonitor.promise();
    const zoomWithSystem = gamepp.setting.getInteger.sync(313) // 是否点了跟随系统缩放开关
      if (zoomWithSystem === 1) {
        zoomLevel.value = initialZoom

        gamepp.webapp.windows.resize.sync('game_rebound', Math.floor(1300*initialZoom) , Math.floor(740*initialZoom))
      }


    gamepp.display.onDisplayMetricsChanged.addEventListener( async (scaleFactor:number)=>

    {
      const zoomWithSystem = gamepp.setting.getInteger.sync(313) // 是否点了跟随系统缩放开关
      if (zoomWithSystem === 1) {
        console.warn('性能统计接收到缩放消息');
        gamepp.webapp.windows.resize.sync('game_rebound', Math.floor(1300 * scaleFactor), Math.floor(740 * scaleFactor))
        zoomLevel.value = scaleFactor
      }
    })
}

const handleDate = async(manual:boolean) =>
{
  if(DateArr.value.length>0)
  {
    if(manual){

    }
    else{
        console.warn('初始化列表数据：',DateArr.value);
        value.value = DateArr.value[0] //选择当前日期
        uniqueArr.value = [...new Set(DateArr.value)]
        currentdays.value = uniqueArr.value.slice(0, 5).reverse();//截取前五项
    }

    await getItemlist()

  }else{
        console.warn('没有性能统计数据');
        currentdays.value = []
        isReady.value = false
  }
}

const TransProcessName = async(name:any) =>
{
   let reflex = JSON.parse(JSON.stringify(name))
   const reflexArr = [{path:'Hearthstone',name:'炉石传说'},{path:"bg3",name:'博德之门3'},{path:'DaveTheDiver',name:'潜水员戴夫'},{path:'P5R',name:'女神异闻录'}]
   reflexArr.forEach((v)=>{
    if(name.includes(v.path))
    {
        reflex = v.name
    }
   })

   return reflex
}

const getItemlist = async() =>  //处理当前所有
{
    let AppDataDir = await gamepp.getAppDataDir.promise();
    let ok = TableListDetailData.value.filter((item:any) => {return item.date == value.value})//所有符合当前日期的项
    //获取数据库位置

    let DatabaseId = await gamepp.database.open.promise(AppDataDir + '\\common\\Data\\GamePP5.dll');
    const weaIcons:any = { "晴": 晴天, "阴": 阴, "雨": 雨, "云":  云, "雪": 雪, "冰": 冰雹, "雾": 雾, "雷": 雷, "霾": 雾霾 };
    ok.forEach(async( item:any,index:number) =>
    {
        if(item['processname'])//游戏统计
        {
            item.ProcesIcon = AppDataDir + '\\common\\Icon\\' + (item['processname'].replace('.exe','.png'));
        }
        else//实验室统计
        {
            item.ProcesIcon = ''
        }
        // item['newprocessname'] = await TransProcessName(item['processname'])
        item.starttime = FormatTimePlus(item.timestamp)
        item.gametime = FormatSeconds(item.timeend - item.timestamp)
        item.gametimeT = item.timeend - item.timestamp
        item.hardwareInfo = ''
        item.data = ''
        let hardwareInfo:any = await PM_GetHardwareBase(item['hd_info']);//获取硬件信息
        item.hardwareInfo = hardwareInfo
        if(item.hd_list_data)
            {
                item.data = JSON.parse(decodeURIComponent(item.hd_list_data))
                //获取天气
                for (let weaType in weaIcons) {
                                if (item.wea.includes(weaType)) {
                                    item.wea_img = weaIcons[weaType];
                                    break;
                                }
                            }
            }
        else
        {
            // ElMessage({message: '统计数据已损坏',type: 'warning',})
            item.data = ListHtmlProcess(item,DatabaseId)
        }


    });

    setTimeout(() =>
    {
        console.warn('newOk',ok);
        curItem.value = reactive(ok)
        isReady.value = true
    }, 100);
}

async function ListHtmlProcess (b:any,DatabaseId:any)
{
    let AllDate = '', DateArr = [], DateArr1 = [], RecordTimeAll = 0, RecordPowerAll = 0, RecordcarbonAll = 0, getAppDataDir = null
    var DetailedDataHtml = '';
    const DetailedData_id = await gamepp.database.query.promise(DatabaseId, "'" + b['starttime'] + "'", "COUNT(id)");
    if (DetailedData_id.length === 0) {
        await gamepp.database.delete.promise(DatabaseId, "GamePP_BaseInfo", "starttime = " + b['starttime'] + "");//列表数据
        return false
    }
    console.warn('DetailedData_id',DetailedData_id);

    let list_length = DetailedData_id[0]['COUNT(id)'];
    let list_length_spacing = 1;
    if (list_length >= 5000) {list_length_spacing = Math.ceil(list_length / 5000);}
    let DetailedData =  await gamepp.database.query.promise(DatabaseId, "'" + b['starttime'] + "'", "*", "id-(id/" + list_length_spacing + ")*" + list_length_spacing + "=0");
    if (DetailedData.length === 0) return false;
    if(DetailedData[0].hasOwnProperty('fps'))
    {
        handleDetailedData(DetailedData,DatabaseId,b,false)
        console.warn('性能统计详情数据：',DetailedData);
    }
    else
    {
        console.warn('实验室详情数据：',DetailedData);
        handleDetailedDataLab(DetailedData,DatabaseId,b,false)
    }
    // 使用filter方法找到所有值为1的元素，然后使用map方法获取它们的索引
    let indicesOfOne:any = []
    let starttime = b['starttime'] //开始时间

    DetailedData.filter((item:any,index:number) => indicesOfOne)
    DetailedData.forEach((item:any,index:number)=>
    {
        if(item.pointmark == 1)
        {
            indicesOfOne.push(index)
        }
    })

    try
    {
        let Obj2:any = {};
        Obj2['action'] = 'changeRebound';
        if (gamepp.webapp.windows.isValid.sync('desktop'))
        {
          await gamepp.webapp.sendInternalAppEvent.promise('desktop', Obj2)
        }

    }
    catch
    {
        console.warn('message send Error:desktop');
    }

    if(indicesOfOne.length == 0){console.warn('未进行打点标记：');return}
    console.log('标记点的秒数',indicesOfOne); // 输出所有值为1的索引
    console.warn('开始时间');

    indicesOfOne.forEach((item:number,index:number)=>
    {
          console.warn(`第${index+1}个标记点：`,FormatTimePlus(starttime+item));
    })
}


const ExportPerformanceData = async(index:number) =>

{
    let AppDataDir = await gamepp.getAppDataDir.promise();
    let DatabaseId = await gamepp.database.open.promise(AppDataDir + '\\common\\Data\\GamePP5.dll');
    let timeTable:any =  Number(curItem.value[index]['timestamp']);

    let queryField_cpu_temperature_core = await gamepp.database.queryField.promise(DatabaseId, timeTable, 'cpu_temperature_core');
    let queryField = '';
    if (queryField_cpu_temperature_core)
    {
    queryField = 'performance_gpu,amd_gpu_thermalmemory,amd_gpu_thermalhotspot,amd_cpu_thermal,performance,disk_load,disk_temp,fps01,fps1,pointmark,id,fps,frametime,cpuclock,cpuload,cpupower,cputemperature,cpu_temperature_core,cpuvoltage,gpuclock,gpuload,gpumemoryload,gpupower,gputemperature,memory,memorytemperature';
    }
    else
    {
    queryField = 'performance_gpu,amd_gpu_thermalmemory,amd_gpu_thermalhotspot,amd_cpu_thermal,performance,disk_load,disk_temp,fps01,fps1,pointmark,id,fps,frametime,cpuclock,cpuload,cpupower,cputemperature,cpuvoltage,gpuclock,gpuload,gpumemoryload,gpupower,gputemperature,memory,memorytemperature';
    }

    const DetailedData = await gamepp.database.query.promise(DatabaseId, "'" + timeTable + "'", queryField);
    console.warn('DetailedData:',DetailedData);

    let allSheetData = []
    for (let i = 0; i < DetailedData.length; i++) {
    // console.log(DetailedData[i])
        let tempObjBase:any = {}, tempObjCPU:any = {}, tempObjGPU:any = {}, tempObjMemory:any = {};
        tempObjBase['FPS'] = (DetailedData[i].fps);
        tempObjBase['FPS1'] = (DetailedData[i].fps1);
        tempObjBase['FPS01'] = (DetailedData[i].fps01);

        tempObjBase['performance'] = (DetailedData[i].performance);
        tempObjBase['pointmark'] = (DetailedData[i].pointmark);

        tempObjBase['disk_load'] = (DetailedData[i].disk_load);
        tempObjBase['disk_temp'] = (DetailedData[i].disk_temp);
        tempObjBase['performance_gpu'] = (DetailedData[i].performance_gpu);
        tempObjBase['amd_gpu_thermalmemory'] = (DetailedData[i].amd_gpu_thermalmemory);
        tempObjBase['amd_gpu_thermalhotspot'] = (DetailedData[i].amd_gpu_thermalhotspot);
        tempObjBase['amd_cpu_thermal'] = (DetailedData[i].amd_cpu_thermal);


        tempObjBase['FrameTime'] = (DetailedData[i].frametime);
        //---------------------------------CPU-------------------------
        tempObjCPU["CPU Temp [℃]"] = (DetailedData[i].cputemperature);
        tempObjCPU['CPU Power [W]'] = (DetailedData[i].cpupower);
        tempObjCPU['CPU Voltage  [V]'] = (DetailedData[i].cpuvoltage);

        //CPU每个核心频率--cpuclock
        let cpuclockArr = (DetailedData[i].cpuclock).split('|');

        //CPU每个核心占用--cpuload
        let cpuloadArr = (DetailedData[i].cpuload).split('|');

        //CPU每个核心占用--cpu_temperature_core
        let cpu_temperature_coreArr = [];
        if (queryField_cpu_temperature_core)
        {
            cpu_temperature_coreArr = (DetailedData[i].cpu_temperature_core).split('|');
        }

        for (let j = 0; j < (cpuclockArr.length) - 1; j++)
        {
            tempObjCPU['CPU Core ' + (j + 1) + ' Load [%]'] = cpuloadArr[j];
            if (queryField_cpu_temperature_core) {tempObjCPU['CPU Core ' + (j + 1) + ' Temp [℃]'] = cpu_temperature_coreArr[j];}
            tempObjCPU['CPU Core ' + (j + 1) + ' Clock [MHz]'] = cpuclockArr[j];
        }
        //---------------------------------GPU-------------------------
        tempObjGPU['GPU Temp [℃]'] = (DetailedData[i].gputemperature).replace('|', '');
        tempObjGPU['GPU Power [W]'] = (DetailedData[i].gpupower).replace('|', '');
        tempObjGPU['GPU Core Clock [MHz]'] = (DetailedData[i].gpuclock).replace('|', '');
        tempObjGPU['GPU Core Load [%]'] = (DetailedData[i].gpuload).replace('|', '');
        tempObjGPU['GPU Memory Load [%]'] = (DetailedData[i].gpumemoryload).replace('|', '');
        //---------------------------------Memory-------------------------
        tempObjMemory['Memory Temp [℃]'] = (DetailedData[i].memorytemperature);
        tempObjMemory['Memory Load [%]'] = (DetailedData[i].memory);

        //-------------------------------数据推送表------------------------
        allSheetData.push({ ...tempObjBase, ...tempObjCPU, ...tempObjGPU, ...tempObjMemory })
    }
    /* 创建worksheet */
    let sheetAll = XLSX.utils.json_to_sheet(allSheetData);
    /* 新建空workbook，然后加入worksheet */
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, sheetAll, "HWINFO");
    // /* 生成xlsx文件 */
    let fildName = ((FormatTimestamp(timeTable))) + ' ' + curItem.value[index]['processname'].replace('.exe', '') + '.csv'
    console.log(fildName)
    XLSX.writeFile(workbook, fildName);//csv xlsx  csv不支持多sheet
}



const handleDetailedData = async (DetailedData:any,DatabaseId:any,b:any,ponitmark:boolean) =>
{
    var data_Det_arr:any = {};
        data_Det_arr['cpuload'] = {};
        data_Det_arr['cpuloadP'] = {};
        data_Det_arr['cpuloadE'] = {};
        data_Det_arr['cpuclock'] = {};
        data_Det_arr['cpuclockP'] = {};
        data_Det_arr['cpuclockE'] = {};
        data_Det_arr['cpupower'] = {};
        data_Det_arr['cputemperature'] = {};
        data_Det_arr['cpuvoltage'] = {};
        data_Det_arr['fps'] = {};
        data_Det_arr['fps1'] = {};
        data_Det_arr['fps01'] = {};
        data_Det_arr['frametime'] = {};
        data_Det_arr['gpuload'] = [];
        data_Det_arr['gpuload1'] = [];
        data_Det_arr['gpumemoryload'] = [];
        data_Det_arr['gpumemorytemp'] = [];
        data_Det_arr['gpumemoryclock'] = [];
        data_Det_arr['gpupower'] = [];
        data_Det_arr['gputemperature'] = [];
        data_Det_arr['gpuhotspottemp'] = [];
        data_Det_arr['gpuclock'] = [];
        data_Det_arr['gpuvoltage'] = [];
        data_Det_arr['memory'] = {};
        data_Det_arr['memorytemperature'] = {};

        data_Det_arr['diskload'] = {};
        data_Det_arr['disktemp'] = {};

        data_Det_arr['performance'] = {};
        data_Det_arr['amd_cpu_thermal'] = [];
        data_Det_arr['performance_gpu'] = [];
        data_Det_arr['amd_gpu_thermalhotspot'] = [];
        data_Det_arr['amd_gpu_thermalmemory'] = [];

        data_Det_arr['cpu_clock_core'] = [];
        data_Det_arr['cpu_clock_effective_core'] = [];

        data_Det_arr['disk_temp'] = {};
        data_Det_arr['disk_load'] = {};

        let cpuloadDetail = [],cpuloadPDetail = [],cpuloadEDetail = [], cpuclockDetail = [],cpuclockPDetail = [],cpuclockEDetail = [] ,cpupowerDetail = [], cputemperatureDetail = [], cpuvoltageDetail = [],
        gpuloadDetail0 = [], gpuloadDetail1 = [],gpuload1Detail0 = [], gpumemoryloadDetail0 = [], gpumemoryloadDetail1 = [], gpumemorytempDetail = [], gpumemoryclockDetail = [], gpupowerDetail0 = [], gpupowerDetail1 = [], gputemperatureDetail0 = [], gputemperatureDetail1 = [], gpuhotspottempDetail = [], gpuclockDetail0 = [], gpuclockDetail1 = [],
        gpuvoltageDetail0 = [], gpuvoltageDetail1 = [],
        fpsDetail = [],fps1Detail = [],fps01Detail = [], frametimeDetail = [], memoryDetail = [], memorytemperatureDetail = [], diskloadDetail = [], disktempDetail = [], performanceDetail = [], amd_cpu_thermalDetail = [], performance_gpu_THERMALDetail = [],amd_gpu_thermalhotspotDetail=[],amd_gpu_thermalmemoryDetail=[],
        cpu_clock_coreDetail     = [], cpu_clock_effective_coreDetail = [],disk_tempDetail=[],disk_loadDetail=[];
        let del_interval = 1, DetailedDataLen = DetailedData.length;
        if (DetailedDataLen <= 5000) {
            del_interval = 1;
        } else if (DetailedDataLen <= 10000) {
            del_interval = 40;
        } else if (DetailedDataLen <= 20000) {
            del_interval = 60;
        } else if (DetailedDataLen <= 30000) {
            del_interval = 100;
        } else if (DetailedDataLen <= 40000) {
            del_interval = 140;
        } else if (DetailedDataLen <= 50000) {
            del_interval = 200;
        } else if (DetailedDataLen <= 60000) {
            del_interval = 300;
        } else {
            del_interval = 400;
        }
        let cpuTemperatureCore = [], cpuLoadCore = [], cpuClockCore = [];
    for (let i = 0; i < DetailedData.length; i++) {
        if (DetailedData[i]['cpuload'].indexOf('NaN') === -1) {
            if (Number.isInteger(i / del_interval)) {

                cpuloadDetail.push(Number(DetailedData[i]['cpuload'].split('|')[0]));
                if (DetailedData[i]['cpuloadP']) {cpuloadPDetail.push(DetailedData[i]['cpuloadP']);}
                if (DetailedData[i]['cpuloadE']) {cpuloadEDetail.push(DetailedData[i]['cpuloadE']);}

                if (DetailedData[i]['cpuclockAVG']) {
                    cpuclockDetail.push(Number(DetailedData[i]['cpuclockAVG']));
                } else {
                    cpuclockDetail.push(Number(DetailedData[i]['cpuclock'].split('|')[0]));
                }

                if (DetailedData[i]['cpuclockP']) {cpuclockPDetail.push(DetailedData[i]['cpuclockP']);}
                if (DetailedData[i]['cpuclockE']) {cpuclockEDetail.push(DetailedData[i]['cpuclockE']);}


                cpupowerDetail.push(Number(DetailedData[i]['cpupower']));

                if (typeof (DetailedData[i]['cputemperature']) != 'number') {cputemperatureDetail.push(Number(DetailedData[i]['cputemperature'].split('|')[0]));} else {cputemperatureDetail.push((DetailedData[i]['cputemperature']));}

                cpuvoltageDetail.push(Number(DetailedData[i]['cpuvoltage']))

                var gpuloadArr = DetailedData[i]['gpuload'].split('|');

                gpuloadDetail0.push(Number(DetailedData[i]['gpuload'].split('|')[0]));
                gpuloadDetail1.push(Number(DetailedData[i]['gpuload'].split('|')[1]));

                if (DetailedData[i]['gpuload1']!==undefined){
                    gpuload1Detail0.push(Number(DetailedData[i]['gpuload1'].split('|')[0]));
                }


                gpumemoryloadDetail0.push(Number(DetailedData[i]['gpumemoryload'].split('|')[0]));
                gpumemoryloadDetail1.push(Number(DetailedData[i]['gpumemoryload'].split('|')[1]));


                if (DetailedData[i]['gpumemorytemp']) {gpumemorytempDetail.push(Number(DetailedData[i]['gpumemorytemp']))}
                if (DetailedData[i]['gpumemoryclock']) {gpumemoryclockDetail.push(Number(DetailedData[i]['gpumemoryclock']))}


                gpupowerDetail0.push(Number(DetailedData[i]['gpupower'].split('|')[0]));
                gpupowerDetail1.push(Number(DetailedData[i]['gpupower'].split('|')[1]));

                gputemperatureDetail0.push(Number(DetailedData[i]['gputemperature'].split('|')[0]))
                gputemperatureDetail1.push(Number(DetailedData[i]['gputemperature'].split('|')[1]))


                gpuhotspottempDetail.push(Number(DetailedData[i]['gpuhotspottemp']))

                gpuclockDetail0.push(Number(DetailedData[i]['gpuclock'].split('|')[0]))
                gpuclockDetail1.push(Number(DetailedData[i]['gpuclock'].split('|')[1]))

                if (DetailedData[i]['gpuvoltage']) {
                    gpuvoltageDetail0.push(Number(DetailedData[i]['gpuvoltage'].split('|')[0]));
                    gpuvoltageDetail1.push(Number(DetailedData[i]['gpuvoltage'].split('|')[1]));
                }

                if (DetailedData[i]['fps']!== undefined) {fpsDetail.push(Number(DetailedData[i]['fps']))}

                if (DetailedData[i]['fps1']!== undefined) {fps1Detail.push(Number(DetailedData[i]['fps1']))}
                if (DetailedData[i]['fps01']!== undefined) {fps01Detail.push(Number(DetailedData[i]['fps01']))}

                if(DetailedData[i]['frametime'] !== undefined)
                {
                    if (DetailedData[i]['frametime']) {frametimeDetail.push(Number(DetailedData[i]['frametime']))}
                }

                memoryDetail.push(Number(DetailedData[i]['memory']));
                memorytemperatureDetail.push(Number(DetailedData[i]['memorytemperature']));

                diskloadDetail.push(Number(DetailedData[i]['disk_load']));
                disktempDetail.push(Number(DetailedData[i]['disk_temp']));


                if (DetailedData[i]['performance'] !== undefined) {
                    performanceDetail.push(DetailedData[i]['performance']);
                }
                if (DetailedData[i]['amd_cpu_thermal'] !== undefined) {
                    amd_cpu_thermalDetail.push(DetailedData[i]['amd_cpu_thermal']);
                }

                performance_gpu_THERMALDetail.push(Number(DetailedData[i]['performance_gpu']));

                amd_gpu_thermalhotspotDetail.push(DetailedData[i]['amd_gpu_thermalhotspot']);
                amd_gpu_thermalmemoryDetail.push(DetailedData[i]['amd_gpu_thermalmemory']);

                cpuTemperatureCore.push(DetailedData[i]['cpu_temperature_core'].split('|').map(Number));

                if (DetailedData[i]['cpu_load_core'])
                {
                    cpuLoadCore.push(DetailedData[i]['cpu_load_core'].split('|').map(Number));
                }
                if (DetailedData[i]['cpu_clock_core'])
                {
                    cpuClockCore.push(DetailedData[i]['cpu_clock_core'].split('|').map(Number));
                }

                if (DetailedData[i]['cpu_clock_core'])
                {
                    cpu_clock_coreDetail.push(DetailedData[i]['cpu_clock_core'])
                }

                if (DetailedData[i]['cpu_clock_effective_core'])
                {
                    cpu_clock_effective_coreDetail.push(DetailedData[i]['cpu_clock_effective_core'])
                }




                if (DetailedData[i]['disk_temp']!== undefined) {disk_tempDetail.push(Number(DetailedData[i]['disk_temp']))}
                if (DetailedData[i]['disk_load']!== undefined) {disk_loadDetail.push(Number(DetailedData[i]['disk_load']))}
            }
        }
    }
let fps01_avg = AVGNum(fps1Detail)

//Fps低于1%low值
let cputempDetailLow = [], cpuloadDetailLow = [], cpuclockDetailLow = []
let cpuClockCore1 = [], cpuLoadCore1 = [];


let gputempDetailLow = [], gpuloadd3dDetailLow = [],gpuloadtotalDetailLow = [], gpuclockDetailLow = [],memoryloadDetailLow = [];

let isLowFPS01 = false

if (DetailedData[0]['cpu_clock_core'])
{
    for (let i = 0; i < DetailedData.length; i++) {
        if (DetailedData[i]['fps'] < fps01_avg) {
            isLowFPS01 = true
            cputempDetailLow.push((DetailedData[i]['cputemperature']));
            cpuloadDetailLow.push(Number(DetailedData[i]['cpuload'].split('|')[0]));
            cpuclockDetailLow.push(Number(DetailedData[i]['cpuclockAVG']));
            if (DetailedData[i]['cpu_clock_core']) {
                cpuClockCore1.push(DetailedData[i]['cpu_clock_core'].split('|').map(Number));
            }

            cpuLoadCore1.push(DetailedData[i]['cpu_load_core'].split('|').map(Number));

            gputempDetailLow.push(Number(DetailedData[i]['gputemperature'].split('|')[0]))
            gpuloadd3dDetailLow.push(Number(DetailedData[i]['gpuload'].split('|')[0]))
            gpuloadtotalDetailLow.push(Number(DetailedData[i]['gpuload1'].split('|')[0]))
            gpuclockDetailLow.push(Number(DetailedData[i]['gpuclock'].split('|')[0]))
            memoryloadDetailLow.push(Number(DetailedData[i]['memory']));
        }
    }
}
//最高核心温度 和 最高温度核心

// if (cpuTemperatureCore.length !== 0) {
//     let coreTempTotals = new Array(cpuTemperatureCore[0].length).fill(0);
//     cpuTemperatureCore.forEach(row => {
//         row.forEach((temp, index) => {
//             coreTempTotals[index] += temp;
//         });
//     });
//     let coreAverages = coreTempTotals.map(total => Math.round(total / cpuTemperatureCore.length));
//     let maxAverageCoreIndex = coreAverages.indexOf(Math.max(...coreAverages));
//     let maxAverageTemperature = coreAverages[maxAverageCoreIndex];
// }



// if (cpuLoadCore.length !== 0) {
//     let coreLoadTotals = new Array(cpuLoadCore[0].length).fill(0);
//     cpuLoadCore.forEach(row => {
//         row.forEach((load, index) => {
//             coreLoadTotals[index] += load;
//         });
//     });
//     let coreLoadAverages = coreLoadTotals.map(total => Math.round(total / cpuLoadCore.length));
//     let maxAverageCoreLoadIndex = coreLoadAverages.indexOf(Math.max(...coreLoadAverages));
//     let maxAverageLoad = coreLoadAverages[maxAverageCoreLoadIndex];
// }

// if (cpuClockCore.length !== 0) {
//     let coreClockTotals = new Array(cpuClockCore[0].length).fill(0);
//     cpuClockCore.forEach(row => {
//         row.forEach((closk, index) => {
//             coreClockTotals[index] += closk;
//         });
//     });
//     let coreClockAverages = coreClockTotals.map(total => Math.round(total / cpuClockCore.length));

//     let maxAverageCoreClockIndex = coreClockAverages.indexOf(Math.max(...coreClockAverages));
//     let maxAverageClock = coreClockAverages[maxAverageCoreClockIndex];

// }

for (let i = 0; i < Object.keys(data_Det_arr).length; i++) {
        let key=Object.keys(data_Det_arr)[i]
        if (['cpuload', 'cpuloadP', 'cpuloadE', 'cpuclock', 'cpuclockP', 'cpuclockE', 'cpupower', 'cputemperature', 'cpuvoltage', 'fps','fps1','fps01', 'frametime', 'memory', 'memorytemperature','diskload','disktemp', 'gpuhotspottemp', 'gpumemorytemp', 'gpumemoryclock','disk_temp','disk_load'].includes(key)) {
            DetailProcess(data_Det_arr, Object.keys(data_Det_arr)[i], eval(Object.keys(data_Det_arr)[i] + 'Detail'));
        }
    }

    function DetailProcess(data_Det_arr:any, ObjectKeys:any, Detail:any)
    {
        data_Det_arr[ObjectKeys]['detail'] = Detail;
        data_Det_arr[ObjectKeys]['avg'] = AVGNum(Detail)
        data_Det_arr[ObjectKeys]['max'] = MaxNum(Detail)
        data_Det_arr[ObjectKeys]['min'] = MinNum(Detail)
    }


        data_Det_arr['cpuclock']['performance'] = performanceDetail;
        data_Det_arr['amd_cpu_thermal'] = amd_cpu_thermalDetail;


        data_Det_arr['performance_gpu'] = performance_gpu_THERMALDetail;
        data_Det_arr['amd_gpu_thermalhotspot'] = amd_gpu_thermalhotspotDetail;
        data_Det_arr['amd_gpu_thermalmemory'] = amd_gpu_thermalmemoryDetail;

        for (let j = 0; j < (gpuloadArr.length - 1); j++) {
            let data_Det_arr1:any = {};
            data_Det_arr1['detail'] = eval('gpuloadDetail' + j);
            data_Det_arr1['avg'] = AVGNum(eval('gpuloadDetail' + j));
            data_Det_arr1['max'] = MaxNum(eval('gpuloadDetail' + j));
            data_Det_arr1['min'] = MinNum(eval('gpuloadDetail' + j));
            data_Det_arr['gpuload'].push(data_Det_arr1);

            let data_Det_arr7:any = {};
            data_Det_arr7['detail'] = eval('gpuload1Detail' + j);
            data_Det_arr7['avg'] = AVGNum(eval('gpuload1Detail' + j));
            data_Det_arr7['max'] = MaxNum(eval('gpuload1Detail' + j));
            data_Det_arr7['min'] = MinNum(eval('gpuload1Detail' + j));
            data_Det_arr['gpuload1'].push(data_Det_arr7);

            let data_Det_arr2:any = {};
            data_Det_arr2['detail'] = eval('gputemperatureDetail' + j);
            data_Det_arr2['avg'] = AVGNum(eval('gputemperatureDetail' + j));
            data_Det_arr2['max'] = MaxNum(eval('gputemperatureDetail' + j));
            data_Det_arr2['min'] = MinNum(eval('gputemperatureDetail' + j));
            data_Det_arr['gputemperature'].push(data_Det_arr2);

            let data_Det_arr3:any = {};
            data_Det_arr3['detail'] = eval('gpumemoryloadDetail' + j);
            data_Det_arr3['avg'] = AVGNum(eval('gpumemoryloadDetail' + j));
            data_Det_arr3['max'] = MaxNum(eval('gpumemoryloadDetail' + j));
            data_Det_arr3['min'] = MinNum(eval('gpumemoryloadDetail' + j));
            data_Det_arr['gpumemoryload'].push(data_Det_arr3);

            let data_Det_arr4:any = {};
            data_Det_arr4['detail'] = eval('gpupowerDetail' + j);
            data_Det_arr4['avg'] = AVGNum(eval('gpupowerDetail' + j));
            data_Det_arr4['max'] = MaxNum(eval('gpupowerDetail' + j));
            data_Det_arr4['min'] = MinNum(eval('gpupowerDetail' + j));
            data_Det_arr['gpupower'].push(data_Det_arr4);

            let data_Det_arr5:any = {};
            data_Det_arr5['detail'] = eval('gpuclockDetail' + j);
            data_Det_arr5['avg'] = AVGNum(eval('gpuclockDetail' + j));
            data_Det_arr5['max'] = MaxNum(eval('gpuclockDetail' + j));
            data_Det_arr5['min'] = MinNum(eval('gpuclockDetail' + j));
            data_Det_arr['gpuclock'].push(data_Det_arr5);

            let data_Det_arr6:any = {};
            data_Det_arr6['detail'] = eval('gpuvoltageDetail' + j);
            data_Det_arr6['avg'] = AVGNum(eval('gpuvoltageDetail' + j));
            data_Det_arr6['max'] = MaxNum(eval('gpuvoltageDetail' + j));
            data_Det_arr6['min'] = MinNum(eval('gpuvoltageDetail' + j));
            data_Det_arr['gpuvoltage'].push(data_Det_arr6);
  }
  let dataData = PerformanceProcessing(data_Det_arr);

  console.warn('评测详情信息：处理后',dataData);
        let EndTimeData = DetailedData[DetailedData.length - 1];
        let StandGPULocaIndex = 0
        if (data_Det_arr.gpuload !== null) {
            if (data_Det_arr.gpuload.length === 2) {
                if (data_Det_arr.gpuload[0]['avg'] < data_Det_arr.gpuload[1]['avg']) {
                    StandGPULocaIndex = 1;
                } else {
                    StandGPULocaIndex = 0;
                }
            } else {
                StandGPULocaIndex = 0;
            }
        }

        function PowerProcess(data:any, cpuload:any, gpuload:any, GameTime:any, GPUIndex:number) {
               let  GPUEstimatePower:any = 0
               let GameTimeH:any = 0
               let  CPUEstimatePower:any = 0
               var cpower = (data.cpupower.avg * 2);
               var gpower = data.gpupower[GPUIndex].avg;
               if (cpuload <= 25) {
                  CPUEstimatePower = cpower * 1.7;
               } else if (cpuload > 25 && cpuload <= 50) {
                  CPUEstimatePower = cpower * 1.5;
               } else if (cpuload > 50 && cpuload <= 75) {
                  CPUEstimatePower = cpower * 1.3;
               } else if (cpuload > 75 && cpuload <= 100) {
                  CPUEstimatePower = cpower * 1.1;
               }
               if (gpuload <= 25) {
                  GPUEstimatePower = gpower * 1.25;
               } else if (gpuload > 25 && gpuload <= 50) {
                  GPUEstimatePower = gpower * 1.2;
               } else if (gpuload > 50 && gpuload <= 75) {
                  GPUEstimatePower = gpower * 1.15;
               } else if (gpuload > 75 && gpuload <= 100) {
                  GPUEstimatePower = gpower * 1.05;
               }
               GameTimeH = (((GameTime) / 3600));
               var TotalPower:any = CPUEstimatePower + GPUEstimatePower;
               var TotalPowerKWh:any = ((TotalPower / 1000) * GameTimeH) === 0 ? 0.01 : (TotalPower / 1000) * GameTimeH.toFixed(4);
               var TotalCarbon:any = parseFloat(((TotalPowerKWh * 0.785) * 1000).toFixed(4));
               var DataArr:any = [];
               DataArr['power'] = TotalPowerKWh;
               DataArr['carbon'] = TotalCarbon;
               return DataArr;
        }

        let PowerCarbon
        if (data_Det_arr.cpuload !== null && data_Det_arr.gpuload !== null && data_Det_arr.cpuload.avg) {
            // console.warn('b',b);
            PowerCarbon = PowerProcess(data_Det_arr, data_Det_arr.cpuload.avg, data_Det_arr.gpuload[StandGPULocaIndex].avg, (b.endtime - b.starttime), StandGPULocaIndex);
        } else {
            PowerCarbon = { "power": "0", "carbon": "0" };
        }
        try {
            // eval 赋值
            let hd_list_data:any = {};
            let hd_list_data_fps = {},hd_list_data_fps1 = {},hd_list_data_fps01 = {}, hd_list_data_memory = {},hd_list_data_memorytemperature={}, hd_list_data_diskload = {},hd_list_data_disktemp={};
            let hd_list_data_cpuload = {}, hd_list_data_cputemperature = {}, hd_list_data_cpuclock = {}, hd_list_data_cpupower = {}, hd_list_data_cpuvoltage = {};
            let hd_list_data_gpuload = {},hd_list_data_gpuload1 = {}, hd_list_data_gpumemoryload = {}, hd_list_data_gputemperature = {}, hd_list_data_gpuclock = {}, hd_list_data_gpupower = {}, hd_list_data_gpuvoltage = {};
            let hd_list_data_disk_temp = {},hd_list_data_disk_load = {};

            let KeyArr = ['fps', 'fps1', 'fps01', 'memory', 'memorytemperature', 'cpuclock', 'cpuload', 'cpupower', 'cputemperature', 'cpuvoltage', 'gpuclock', 'gpuload', 'gpuload1', 'gpumemoryload', 'gpupower', 'gputemperature', 'gpuvoltage', 'disk_temp', 'disk_load'];

            for (let i = 0; i < KeyArr.length; i++) {
                let key = KeyArr[i];
                let hd_list_data_ = 'hd_list_data_';
                if (key.includes('gpu')) {
                    eval("hd_list_data_" + [key])['max'] = data_Det_arr[key][StandGPULocaIndex].max;
                    eval("hd_list_data_" + [key])['min'] = data_Det_arr[key][StandGPULocaIndex].min;
                    eval("hd_list_data_" + [key])['avg'] = data_Det_arr[key][StandGPULocaIndex].avg;
                } else {
                    eval("hd_list_data_" + [key])['max'] = data_Det_arr[key].max;
                    eval("hd_list_data_" + [key])['min'] = data_Det_arr[key].min;
                    eval("hd_list_data_" + [key])['avg'] = data_Det_arr[key].avg;
                }
            }

            for (let i = 0; i < KeyArr.length; i++) {
                let key = KeyArr[i];
                hd_list_data[key] = eval('hd_list_data_' + key);
            }
            // console.warn('hd_list_data',hd_list_data);
            if(ponitmark){return} //如果是手动标记数据 不上传数据库

            hd_list_data['power'] = Number(parseFloat(PowerCarbon['power']));
            hd_list_data['co2'] = Number(parseFloat(PowerCarbon['carbon']));
            let _Info = encodeURIComponent(JSON.stringify(hd_list_data));
            try
            {
                await gamepp.database.update.promise(DatabaseId, "GamePP_BaseInfo", ['hd_list_data="' + _Info + '"'], 'id = "' + b.id + '"');
                console.warn('成功上传数据库:',_Info);

            }
            catch
            {
                console.warn('错误！！上传数据库:');
            }
            }
            catch(err)
            {
                console.log(err)
            }
}

const handleDetailedDataLab = async (DetailedData:any,DatabaseId:any,b:any,ponitmark:boolean) =>
{
    var data_Det_arr:any = {};
    data_Det_arr['cpuload'] = {};
    data_Det_arr['cpuclock'] = {};
    data_Det_arr['cpupower'] = {};
    data_Det_arr['cputemperature'] = {};
    data_Det_arr['cpuvoltage'] = {};
    data_Det_arr['fps'] = {};
    data_Det_arr['fps1'] = {};
    data_Det_arr['fps01'] = {};
    data_Det_arr['frametime'] = {};
    data_Det_arr['gpuload'] = [];
    data_Det_arr['gpumemoryload'] = [];
    data_Det_arr['gpupower'] = [];
    data_Det_arr['gputemperature'] = [];
    data_Det_arr['gpuclock'] = [];
    data_Det_arr['gpuvoltage'] = [];
    data_Det_arr['memory'] = {};
    data_Det_arr['memorytemperature'] = {};
    data_Det_arr['performance'] = {};
    data_Det_arr['amd_cpu_thermal'] = [];

  let cpuloadDetail = [], cpuclockDetail = [], cpupowerDetail = [], cputemperatureDetail = [], cpuvoltageDetail = [], gpuloadDetail0 = [], gpuloadDetail1 = [], gpumemoryloadDetail0 = [], gpumemoryloadDetail1 = [], gpupowerDetail0 = [], gpupowerDetail1 = [], gputemperatureDetail0 = [], gputemperatureDetail1 = [], gpuclockDetail0 = [], gpuclockDetail1 = [], gpuvoltageDetail0 = [], gpuvoltageDetail1 = [], fpsDetail = [],fps1Detail = [],fps01Detail = [], frametimeDetail = [], memoryDetail = [],memorytemperatureDetail = [], performanceDetail = [], amd_cpu_thermalDetail = [];
  let del_interval = 1, DetailedDataLen = DetailedData.length;
    if (DetailedDataLen <= 5000) {
    del_interval = 1;
    } else if (DetailedDataLen <= 10000) {
    del_interval = 40;
    } else if (DetailedDataLen <= 20000) {
    del_interval = 60;
    } else if (DetailedDataLen <= 30000) {
    del_interval = 100;
    } else if (DetailedDataLen <= 40000) {
    del_interval = 140;
    } else if (DetailedDataLen <= 50000) {
    del_interval = 200;
    } else if (DetailedDataLen <= 60000) {
    del_interval = 300;
    } else {
    del_interval = 400;
    }

for (let i = 0; i < DetailedData.length; i++) {
    if (DetailedData[i]['cpuload'].indexOf('NaN') === -1) {
      if (Number.isInteger(i / del_interval)) {

        cpuloadDetail.push(Number(DetailedData[i]['cpuload'].split('|')[0]));

        if (DetailedData[i]['cpuclockAVG']) {
          cpuclockDetail.push(Number(DetailedData[i]['cpuclockAVG']));
        } else {
          cpuclockDetail.push(Number(DetailedData[i]['cpuclock'].split('|')[0]));
        }

        cpupowerDetail.push(Number(DetailedData[i]['cpupower']));
        if (typeof (DetailedData[i]['cputemperature']) != 'number') {cputemperatureDetail.push(Number(DetailedData[i]['cputemperature'].split('|')[0]));} else {cputemperatureDetail.push((DetailedData[i]['cputemperature']));}
        cpuvoltageDetail.push(Number(DetailedData[i]['cpuvoltage']))

        var gpuloadArr = DetailedData[i]['gpuload'].split('|');

        gpuloadDetail0.push(Number(DetailedData[i]['gpuload'].split('|')[0]));
        gpuloadDetail1.push(Number(DetailedData[i]['gpuload'].split('|')[1]));

        gpumemoryloadDetail0.push(Number(DetailedData[i]['gpumemoryload'].split('|')[0]));
        gpumemoryloadDetail1.push(Number(DetailedData[i]['gpumemoryload'].split('|')[1]));

        gpupowerDetail0.push(Number(DetailedData[i]['gpupower'].split('|')[0]));
        gpupowerDetail1.push(Number(DetailedData[i]['gpupower'].split('|')[1]));

        gputemperatureDetail0.push(Number(DetailedData[i]['gputemperature'].split('|')[0]))
        gputemperatureDetail1.push(Number(DetailedData[i]['gputemperature'].split('|')[1]))

        gpuclockDetail0.push(Number(DetailedData[i]['gpuclock'].split('|')[0]))
        gpuclockDetail1.push(Number(DetailedData[i]['gpuclock'].split('|')[1]))

        if (DetailedData[i]['gpuvoltage']) {
          gpuvoltageDetail0.push(Number(DetailedData[i]['gpuvoltage'].split('|')[0]));
          gpuvoltageDetail1.push(Number(DetailedData[i]['gpuvoltage'].split('|')[1]));
        }

        if (DetailedData[i]['fps']) {fpsDetail.push(Number(DetailedData[i]['fps']))}
        if (DetailedData[i]['fps1']) {fps1Detail.push(Number(DetailedData[i]['fps1']))}
        if (DetailedData[i]['fps01']) {fps01Detail.push(Number(DetailedData[i]['fps01']))}

        if (DetailedData[i]['frametime']) {frametimeDetail.push(Number(DetailedData[i]['frametime']))}
        memoryDetail.push(Number(DetailedData[i]['memory']));
        memorytemperatureDetail.push(Number(DetailedData[i]['memorytemperature']));

        if (DetailedData[i]['performance'] !== undefined) {
          performanceDetail.push(DetailedData[i]['performance']);
        }
        if (DetailedData[i]['amd_cpu_thermal']){
          amd_cpu_thermalDetail.push(DetailedData[i]['amd_cpu_thermal']);
        }
      }
    }
  }

  for (let i = 0; i < Object.keys(data_Det_arr).length; i++) {
    if (['cpuload', 'cpuclock', 'cpupower', 'cputemperature', 'cpuvoltage', 'fps','fps1','fps01', 'frametime', 'memory','memorytemperature'].includes(Object.keys(data_Det_arr)[i])) {
      DetailProcess(data_Det_arr, Object.keys(data_Det_arr)[i], eval(Object.keys(data_Det_arr)[i] + 'Detail'));
    }
  }

  data_Det_arr['cpuclock']['performance'] = performanceDetail;
  data_Det_arr['amd_cpu_thermal'] = amd_cpu_thermalDetail;

    function DetailProcess(data_Det_arr:any, ObjectKeys:any, Detail:any) {
    data_Det_arr[ObjectKeys]['detail'] = Detail;
    data_Det_arr[ObjectKeys]['avg'] = AVGNum(Detail)
    data_Det_arr[ObjectKeys]['max'] = MaxNum(Detail)
    data_Det_arr[ObjectKeys]['min'] = MinNum(Detail)
    }


    for (let j = 0; j < (gpuloadArr.length - 1); j++) {
    let data_Det_arr1:any = {};
    data_Det_arr1['detail'] = eval('gpuloadDetail' + j);
    data_Det_arr1['avg'] = AVGNum(eval('gpuloadDetail' + j));
    data_Det_arr1['max'] = MaxNum(eval('gpuloadDetail' + j));
    data_Det_arr1['min'] = MinNum(eval('gpuloadDetail' + j));
    data_Det_arr['gpuload'].push(data_Det_arr1);

    let data_Det_arr2:any = {};
    data_Det_arr2['detail'] = eval('gputemperatureDetail' + j);
    data_Det_arr2['avg'] = AVGNum(eval('gputemperatureDetail' + j));
    data_Det_arr2['max'] = MaxNum(eval('gputemperatureDetail' + j));
    data_Det_arr2['min'] = MinNum(eval('gputemperatureDetail' + j));
    data_Det_arr['gputemperature'].push(data_Det_arr2);

    let data_Det_arr3:any = {};
    data_Det_arr3['detail'] = eval('gpumemoryloadDetail' + j);
    data_Det_arr3['avg'] = AVGNum(eval('gpumemoryloadDetail' + j));
    data_Det_arr3['max'] = MaxNum(eval('gpumemoryloadDetail' + j));
    data_Det_arr3['min'] = MinNum(eval('gpumemoryloadDetail' + j));
    data_Det_arr['gpumemoryload'].push(data_Det_arr3);

    let data_Det_arr4:any = {};
    data_Det_arr4['detail'] = eval('gpupowerDetail' + j);
    data_Det_arr4['avg'] = AVGNum(eval('gpupowerDetail' + j));
    data_Det_arr4['max'] = MaxNum(eval('gpupowerDetail' + j));
    data_Det_arr4['min'] = MinNum(eval('gpupowerDetail' + j));
    data_Det_arr['gpupower'].push(data_Det_arr4);

    let data_Det_arr5:any = {};
    data_Det_arr5['detail'] = eval('gpuclockDetail' + j);
    data_Det_arr5['avg'] = AVGNum(eval('gpuclockDetail' + j));
    data_Det_arr5['max'] = MaxNum(eval('gpuclockDetail' + j));
    data_Det_arr5['min'] = MinNum(eval('gpuclockDetail' + j));
    data_Det_arr['gpuclock'].push(data_Det_arr5);

    let data_Det_arr6:any = {};
    data_Det_arr6['detail'] = eval('gpuvoltageDetail' + j);
    data_Det_arr6['avg'] = AVGNum(eval('gpuvoltageDetail' + j));
    data_Det_arr6['max'] = MaxNum(eval('gpuvoltageDetail' + j));
    data_Det_arr6['min'] = MinNum(eval('gpuvoltageDetail' + j));
    data_Det_arr['gpuvoltage'].push(data_Det_arr6);
  }
  let dataData = PerformanceProcessing(data_Det_arr);
  console.warn('评测详情信息：处理后',dataData);
      //   function create_variable (num:number) {
      //       var name = "DetailedData_" + num    //生成函数名
      //       var Hd_Info = "HdInfo_" + num    //生成函数名
      //       window[name] = PerformanceProcessing(data_Det_arr);
      //       window[Hd_Info] = b['hd_info'];
      //   }
        function PowerProcess(data:any, cpuload:any, gpuload:any, GameTime:any, GPUIndex:number) {
              //  console.warn('GameTime',GameTime);

               let  GPUEstimatePower:any = 0
               let GameTimeH:any = 0
               let  CPUEstimatePower:any = 0
               var cpower = (data.cpupower.avg * 2);
               var gpower = data.gpupower[GPUIndex].avg;
               if (cpuload <= 25) {
                  CPUEstimatePower = cpower * 1.7;
               } else if (cpuload > 25 && cpuload <= 50) {
                  CPUEstimatePower = cpower * 1.5;
               } else if (cpuload > 50 && cpuload <= 75) {
                  CPUEstimatePower = cpower * 1.3;
               } else if (cpuload > 75 && cpuload <= 100) {
                  CPUEstimatePower = cpower * 1.1;
               }
               if (gpuload <= 25) {
                  GPUEstimatePower = gpower * 1.25;
               } else if (gpuload > 25 && gpuload <= 50) {
                  GPUEstimatePower = gpower * 1.2;
               } else if (gpuload > 50 && gpuload <= 75) {
                  GPUEstimatePower = gpower * 1.15;
               } else if (gpuload > 75 && gpuload <= 100) {
                  GPUEstimatePower = gpower * 1.05;
               }
               GameTimeH = (((GameTime) / 3600));
               var TotalPower:any = CPUEstimatePower + GPUEstimatePower;
               // var TotalPowerKWh:any = ((TotalPower / 1000) * GameTimeH) === 0 ? 0.01 : parseFloat((TotalPower / 1000) * GameTimeH).toFixed(4);
               var TotalPowerKWh:any = ((TotalPower / 1000) * GameTimeH) === 0 ? 0.01 : (TotalPower / 1000) * GameTimeH.toFixed(4);
               var TotalCarbon:any = parseFloat(((TotalPowerKWh * 0.785) * 1000).toFixed(4));
              //  console.warn('GameTimeH',GameTimeH);
              //  console.warn('TotalPower',TotalPower);
              //  console.warn('TotalPowerKWh',TotalPowerKWh);
              //  console.warn('TotalCarbon',TotalCarbon);
               var DataArr:any = [];
               DataArr['power'] = TotalPowerKWh;
               DataArr['carbon'] = TotalCarbon;
               return DataArr;
        }

        let PowerCarbon
        if (data_Det_arr.cpuload !== null && data_Det_arr.gpuload !== null && data_Det_arr.cpuload.avg) {
            // console.warn('b',b);
            PowerCarbon = PowerProcess(data_Det_arr, data_Det_arr.cpuload.avg, data_Det_arr.gpuload[0].avg, (b.endtime - b.starttime), 0);
        } else {
            PowerCarbon = { "power": "0", "carbon": "0" };
        }
        console.warn('p1');
        try {
            // eval 赋值
            // eval 赋值
            let hd_list_data:any = {};
            let hd_list_data_fps = {}, hd_list_data_memory = {},hd_list_data_memorytemperature={};
            let hd_list_data_cpuload = {}, hd_list_data_cputemperature = {}, hd_list_data_cpuclock = {}, hd_list_data_cpupower = {}, hd_list_data_cpuvoltage = {};
            let hd_list_data_gpuload = {}, hd_list_data_gpumemoryload = {}, hd_list_data_gputemperature = {}, hd_list_data_gpuclock = {}, hd_list_data_gpupower = {}, hd_list_data_gpuvoltage = {};

            let KeyArr = ['memory','memorytemperature', 'cpuclock', 'cpuload', 'cpupower', 'cputemperature', 'cpuvoltage', 'gpuclock', 'gpuload', 'gpumemoryload', 'gpupower', 'gputemperature', 'gpuvoltage'];
            for (let i = 0; i < KeyArr.length; i++) {
                let key = KeyArr[i];
                let hd_list_data_ = 'hd_list_data_';
                if (key.includes('gpu')) {
                eval("hd_list_data_" + [key])['max'] = data_Det_arr[key][0].max;
                eval("hd_list_data_" + [key])['min'] = data_Det_arr[key][0].min;
                eval("hd_list_data_" + [key])['avg'] = data_Det_arr[key][0].avg;
                } else {
                eval("hd_list_data_" + [key])['max'] = data_Det_arr[key].max;
                eval("hd_list_data_" + [key])['min'] = data_Det_arr[key].min;
                eval("hd_list_data_" + [key])['avg'] = data_Det_arr[key].avg;
                }
            }
            console.warn('p2');
            for (let i = 0; i < KeyArr.length; i++) {
                let key = KeyArr[i];
                hd_list_data[key] = eval('hd_list_data_' + key);
            }
            // console.warn('hd_list_data',hd_list_data);
            hd_list_data['power'] = Number(parseFloat(PowerCarbon['power']));
            hd_list_data['co2'] = Number(parseFloat(PowerCarbon['carbon']));
            console.warn('p3',hd_list_data);
            let _Info = encodeURIComponent(JSON.stringify(hd_list_data));
            try
            {
                await gamepp.database.update.promise(DatabaseId, "GamePP_BaseInfo", ['hd_list_data="' + _Info + '"'], 'id = "' + b.id + '"');
                console.warn('hd_list_data成功上传数据库:',_Info);

            }
            catch
            {
                console.warn('错误！！上传数据库:');
            }
            }
            catch(err)
            {
                console.log(err)
            }
}
watch(curItem,()=>
{
    calculateTotal()
})


const calculateTotal = () => //统计当天资源消耗
{
   let total =  curItem.value.reduce((accumulator:number, currentValue:any) => {
    return accumulator + currentValue.gametimeT;
   }, 0);

   let elec =  Number(curItem.value.reduce((accumulator:number, currentValue:any) => {
    return accumulator + currentValue.data.power;
   }, 0)).toFixed(2)

   curDayPlayTotal.value.electronic = elec
   curDayPlayTotal.value.co2 = Number(curItem.value.reduce((accumulator:number, currentValue:any) => {
    return accumulator + currentValue.data.co2;
   }, 0)).toFixed(2)

   curDayPlayTotal.value.gametime = FormatSeconds(total)
}

const DetailDetail = async (index:number) => //删除表
{
    let AppDataDir = await gamepp.getAppDataDir.promise();
    let DatabaseId = await gamepp.database.open.promise(AppDataDir + '\\common\\Data\\GamePP5.dll');
    console.warn('需要删除的项',curItem.value[index]);

    await gamepp.database.delete.sync(DatabaseId, "GamePP_BaseInfo", "starttime = " + curItem.value[index]['timestamp'] + "");//列表数据

    curItem.value = curItem.value.filter((v:any,i:number)=>{
        return i != index
    })

    let exists = true;
    try
    {
        exists = await gamepp.database.exists.promise(DatabaseId, 'GamePP_BaseInfo');
    }
    catch
    {
        console.warn('错误！！！:数据库文件损坏');
    }

    if (!exists) {
        return false
    }

    let TableListData = null;

    try
    {
    //    TableListData = await gamepp.database.query.promise(DatabaseId, "GamePP_BaseInfo", "*", "where data_type is null order by id desc");
       TableListData = await gamepp.database.query.promise(DatabaseId, "GamePP_BaseInfo", "*","where hd_list_data is not null order by id desc");
       console.warn('性能统计数据删除后更新:',TableListData);
       if(TableListData.lenth == 0)
       {
        const obj =
        {
          action:'clearRebound'
        }
        await gamepp.webapp.sendInternalAppEvent.promise('desktop', obj)

        let recent =  {
            gameName:'???',
            resolution:'???',
            gametime:'???',
            iconsrc:'',
            starttime:0,
            fps:{
               avg:'???',
               min:'???',
               max:'???',
               fps01:'???'
            }
         }

        localStorage.setItem('recentGame',JSON.stringify(recent))
        isReady.value = false
       }

       if(curItem.value.length == 0)
        {
            console.warn('此时已经删完了');
            GPP_HandleTableListData(TableListData,DatabaseId,false) //格式化页面
        }
        else
        {
            GPP_HandleTableListData(TableListData,DatabaseId,true)
        }
    }
    catch
    {
      console.warn('错误！！！:获取性能统计数据');
    }
}

const clipDate = (date:any) =>  //选择日期段
{
     const arr1 = uniqueArr.value
     let arr = arr1.reduce((acc:any, current:any) => {
            acc.unshift(current);
            return acc;
    }, []);
     let index = arr.indexOf(date)
     let start = Math.max(0, index - 2);
     let end

     if(index == 0 || index == 1)
     {
        start = 0
        end = 6
     }
     else if(index == uniqueArr.value.length-1 || index == uniqueArr.value.length -2 )
     {
        end = uniqueArr.value.length + 1
        start = uniqueArr.value.length - 4
        if(start < 0){start = 0}
     }
     else
     {
       end = Math.min(uniqueArr.value.length, index + 3);
     }
     currentdays.value = arr.slice(start, end)
}

const getWindowSize = () =>
{
   mirrorWidth.value = window.innerWidth;
   mirrorHeight.value = window.innerHeight;
};

const initDataBase = async () => //连接数据库
{
   let getAppDataDir,DatabaseId
   try {
        let AppDataDir = await gamepp.getAppDataDir.promise();
        DatabaseId = await gamepp.database.open.promise(AppDataDir + '\\common\\Data\\GamePP5.dll');
        getAppDataDir = AppDataDir
        GPP_LoadPerformaceInfo(DatabaseId);

    } catch {
        GPP_LoadPerformaceInfo(DatabaseId);
    }
}

const settings = ref([{name:'最小化',icon: "icon-minimize"},{name:'退出',icon:"icon-Close"},])


const GPP_LoadPerformaceInfo = async (DatabaseId:string) =>
{
   let exists = true;
    try {
        exists = await gamepp.database.exists.promise(DatabaseId, 'GamePP_BaseInfo');
    } catch {
    }

    if (!exists) {
        return false
    }

    let TableListData = null;
    try
    {
    //    TableListData = await gamepp.database.query.promise(DatabaseId, "GamePP_BaseInfo", "*", "where data_type is null order by id desc");
       TableListData = await gamepp.database.query.promise(DatabaseId, "GamePP_BaseInfo", "*", "where hd_list_data is not null order by id desc");
       checkRebound.value = TableListData
       console.warn('性能统计数据:',TableListData);
       GPP_HandleTableListData(TableListData,DatabaseId,false)

    }
    catch
    {
      console.warn('错误！！！:获取性能统计数据');
    }
}

const GPP_HandleTableListData = async (TableListData:any,DatabaseId:any,manul:boolean) =>  //处理列表数据
{
     console.warn('传入的日期',TableListData);
    if (TableListData.length === 0)
      {
         console.warn('无性能统计数据');
         isReady.value = false
         console.warn('无性能统计数据:',isReady.value);
         const obj =
        {
          action:'clearRebound'
        }
        await gamepp.webapp.sendInternalAppEvent.promise('desktop', obj)

        let recent =  {
            gameName:'???',
            resolution:'???',
            gametime:'???',
            iconsrc:'',
            starttime:0,
            fps:{
               avg:'???',
               min:'???',
               max:'???',
               fps01:'???'
            }
         }

        localStorage.setItem('recentGame',JSON.stringify(recent))
      }
    else
      {
          starttimeArr.value = [] //初始化
          DateArr.value = []
          TableListDetailData.value = []
            TableListData.forEach((v:any,index:number)=>
            {
                if (starttimeArr.value.includes(v['starttime'])) return true;
                if (v['endtime'] !== null) {
                    starttimeArr.value.push(v.starttime);
                    DateArr.value.push(FormatTime(v.starttime)['ymd']);
                    TableListDetailData.value.push(getDataOption(FormatTime(v.starttime)['ymd'], v));
                }
            });
         console.warn('starttimeArr',starttimeArr.value);
         console.warn('DateArr', DateArr.value);
         console.warn('TableListDetailDat',TableListDetailData.value);
         await handleDate(manul)
      }
}

const OpenDetailpage = async(index:number) => //打开详情页面
{
        let Object:any= {};
        Object['table'] = Number(curItem.value[index]['timestamp']);
        Object['vip'] = gamepp.user.isVIP.sync();
        Object['not_show'] = Number(0);
        Object['is_upload'] = false;
        Object['is_open_windows'] = true;
        Object['data'] = JSON.parse(JSON.stringify(curItem.value[index]['data']))
        console.warn('打开的详情时间戳',curItem.value[index]['timestamp']);

        await gamepp.setting.setBool2.promise('window', 'rebound_details_v2', false);
        if (gamepp.webapp.windows.isVisible.sync('rebound_details_v2'))
        {
            gamepp.webapp.windows.close.sync('rebound_details_v2')
        }

        window.localStorage.setItem('rebound_details_v2_open_by', 'front');
        localStorage.setItem('isMaualOpendetail',JSON.stringify(false))
        gamepp.webapp.windows.show.sync('rebound_details_v2',false)
        await gamepp.webapp.sendInternalAppEvent.promise('rebound_details_v2', Object);
        console.warn('已发送消息',Object);

}

async function setting(index:number)
{
    if(index == 3)
    {
        gamepp.webapp.windows.close.sync('game_rebound')
    }
    else if(index == 0)
    { // 最小化
        await gamepp.webapp.windows.minimize.promise('game_rebound');
    }
    else if(index == 1)
    { // 最大化
        gamepp.webapp.windows.close.sync('game_rebound')
        // fullScreen.value = true
        // await gamepp.webapp.windows.maximize.promise('game_rebound');
    }
    else if(index == 2)
    { // 还原
        fullScreen.value = false
        await gamepp.webapp.windows.unmaximize.promise('game_rebound');
    }
  console.warn(index);
}

async function IsReadyShowSendPage (sectionName:string, keyName:string, value = false) {
    return new Promise((resolve, reject) => {
        let nRet = false;
        let setInterval_getbool2 = setInterval(async () => {
            nRet = await gamepp.setting.getBool2.promise(sectionName, keyName, value);
            if (nRet) {
                clearInterval(setInterval_getbool2);
                resolve(1);
            }
        }, 100)
    })

}

const PM_GetHardwareBase = async (BaseJsonStr:any) =>
{
  let res:any = []
   let data_arr = null;
   if (BaseJsonStr) {
      data_arr = JSON.parse(decodeURIComponent(BaseJsonStr));
   } else {
      data_arr = JSON.parse(await gamepp.hardware.getBaseJsonInfo.promise());
   }
   for(let key in data_arr)
   {
      let HWInfoV = data_arr[key]
      // console.warn('DataArrINfo',key,data_arr[key]);
      switch(key)
      {
         case 'COMPUTER':
         // let Languagevalue = 'cn'
         let SystemName = (HWInfoV.OperatingSystem
            .replace('Microsoft ', '')
            .replace('Build ', '')
            // .replace('Home', eval(Languagevalue)['Home'])
            // .replace('Home Basic', eval(Languagevalue)['HomeBasic'])
            // .replace('Home Premium', eval(Languagevalue)['HomePremium'])
            // .replace('Professional',  eval(Languagevalue)['Professional'])
            // .replace('Professional Workstation', eval(Languagevalue)['ProfessionalWorkstation'])
            // .replace('Education', eval(Languagevalue)['Education'])
            // .replace('Enterprise', eval(Languagevalue)['Enterprise'])
            // .replace('Ultimate', eval(Languagevalue)['Ultimate'])
            // .replace('Starter', eval(Languagevalue)['Starter'])
            // .replace('(x64)', '64' + eval(Languagevalue)['Bit'])
            // .replace('(x32)', '32' + eval(Languagevalue)['Bit'])
        );
        res['SystemName'] =  SystemName
        // console.warn('SystemName',SystemName);
        // console.warn(res);
        break;
        case 'CPU':
        let CPUType
        var CPUSubNode = HWInfoV['SubNode'][0];
        let OriginalProcessorFrequency = Number(CPUSubNode["OriginalProcessorFrequency[MHz]"]) - 50;
        if ((CPUSubNode['ProcessorName']).includes('Intel')) {CPUType = 'intel'} else {CPUType = 'amd'}
        // console.warn('CPUType',CPUType);
        res['CPUType'] =  CPUType
        res['ProcessorName'] =  CPUSubNode['ProcessorName']

        break;
        case 'MOBO':
          let MainboardName,SystemManufacturer,MotherboardChipset
           MainboardName =  HWInfoV['Mainboard']['MainboardName'];
            if (HWInfoV['Mainboard']['MainboardManufacturer'] === 'Notebook') {
              SystemManufacturer = HWInfoV['System']['SystemManufacturer']
            } else {
              SystemManufacturer = HWInfoV['Mainboard']['MainboardManufacturer'].replace('Technology', '').replace('And', '').replace('Development', '').replace('Computer', '').replace('COMPUTER', '').replace('Co.,LTD', '').replace('INC.', '')
            }
              MotherboardChipset = HWInfoV['Property']['MotherboardChipset'].replace(/\(.*?\)/g, '')

              res['MainboardName'] =  MainboardName
              res['SystemManufacturer'] =  SystemManufacturer
              res['MotherboardChipset'] =  MotherboardChipset
        break;
        case 'DRIVES':
            let gameDiskName =  res['gameDiskName']
            let drive_index:any = HWInfoV['SubNode'].findIndex((item:any) => item['DriveModel'] === gameDiskName);
            drive_index = drive_index !== -1 ? drive_index : 0;
            let DriveData = HWInfoV['SubNode'][drive_index]
            let drivesType = 'HDD'
            if ((DriveData.MediaRotationRate && (DriveData.MediaRotationRate).indexOf('SSD') !== -1) || (DriveData.DriveController)?.includes('NVMe') || (DriveData.Interface)?.includes('NVMe')) {drivesType = 'SSD';} else if (DriveData.MediaRotationRate && (DriveData.MediaRotationRate).includes('RPM')) {drivesType === 'HDD'} else {drivesType === 'HDD'}
            if (((DriveData.DriveModel)?.toLowerCase()).includes('ssd')) {drivesType = 'SSD'}
            res['drivesType'] = drivesType
            res['Drive_size'] = DiskCapacityConversion(DriveData['DriveCapacity[MB]'], 1024)
            if (!gameDiskName) {
                res['disk_name'] = DriveData.DriveModel
            }
        break;
        case 'GPU':
            let GPUIndex = parseInt(String(localStorage.getItem('gpu_index'))) || 0;
            let GPUData = HWInfoV['SubNode'][GPUIndex]
            // res['GPU'] = GPUData['VideoChipset']
            res['GPU'] = GPUData['VideoCard'].replace(/\([^)]*\)|{[^}]*}|<[^>]*>|\[[^]*]/g, '')
            res['GPU_DriverVersion'] = GPUData['DriverVersion']
            res['GPU_DriverDate'] = FormartMonthToNumber(GPUData['DriverDate'])
            let videoCardName = GPUData.VideoCard;
            let VideoBrand = videoCardName.match(/\[(.+?)\]$/) ? RegExp.$1 : videoCardName.split(' ')[0];
            res['VideoBrand'] = VideoBrand

            if (GPUData.VideoMemory && GPUData.VideoMemory !== 'Unknown') {
                let memorySizeGB = Math.ceil(parseInt(GPUData.VideoMemory, 10) / 1024);
                let memoryType = GPUData.VideoMemory.split(' ')[3] || '';
                let memoryBrand = GPUData.VideoMemory.match(/\[(.+?)\]$/) ? RegExp.$1 : '';
                let typeAndBrandDetail = memoryType || memoryBrand ? ` (${memoryType} ${memoryBrand})` : '';
                res['GPU_VideoMemor'] = `${memorySizeGB}G${typeAndBrandDetail}`
            }
                let gpu_name = GPUData['VideoChipset'];
                let GPUType
            if (['Radeon', 'AMD', 'Vega','amd'].find(item => gpu_name.includes(item))) {GPUType = 'amd';} else if (['GeForce', 'NVIDIA'].find(item => gpu_name.includes(item))) {GPUType = 'nvidia'}
                 res['GPUType'] = GPUType

       break;
        case 'MEMORY':
            let MemoryHtml = '';
            if (HWInfoV['SubNode'] != null) {
               res['memory'] = HWInfoV['SubNode'][0]['ModuleManufacturer'] + "  " + +(HWInfoV.Property['TotalMemorySize[MB]']) / 1024 + 'GB' + "  " + (Math.ceil(HWInfoV.Property['CurrentMemoryClock'].split('MHz')[0])) * 2 + 'MHz' + "  " + HWInfoV.Property['CurrentTiming(tCAS-tRCD-tRP-tRAS)']

                // MemoryHtml = '<div class="memory">' + HWInfoV['SubNode'][0]['ModuleManufacturer'] + "  " + +(HWInfoV.Property['TotalMemorySize[MB]']) / 1024 + 'GB' + "  " + (Math.ceil(HWInfoV.Property['CurrentMemoryClock'].split('MHz')[0])) * 2 + 'MHz' + "  " + HWInfoV.Property['CurrentTiming(tCAS-tRCD-tRP-tRAS)'] + '</div></li>';
            }
            res['Memory_size']  = (HWInfoV['Property']['TotalMemorySize[MB]'] / 1024) + 'G'
            res['Memory_channels_active'] = HWInfoV['Property']['MemoryChannelsActive']

        break;
        case 'MONITOR':
            let MONITORDataV = HWInfoV['SubNode'][0]
            let MonitorNameStr
            if (MONITORDataV.MonitorName !== 'Unknown') {
              res['MonitorName'] = MONITORDataV.MonitorName.replace(/\[.*?\]/g, '');
            } else {
              res['MonitorName'] = ''
            }
            if (MONITORDataV['MonitorName(Manuf)']) {
                let brand = RemoveAllSpace( res['MonitorName']);
                let model = RemoveAllSpace(MONITORDataV['MonitorName(Manuf)']);
                if (brand.toLowerCase() === model.toLowerCase()) {
                    MonitorNameStr = MONITORDataV['MonitorName'];
                } else {
                    MonitorNameStr =res['MonitorName']+ MONITORDataV['MonitorName(Manuf)'];
                }
            } else {
                MonitorNameStr = MONITORDataV['MonitorName'];
            }
            if (MonitorNameStr !== '') {
              res['MonitorNameStr'] = MonitorNameStr
              res['refresh_rate'] = MONITORDataV['RefreshFrequency'] + 'Hz'
              let WH = MONITORDataV.Resolutions.split('*')
              res['resolutiop'] = WH[0] + '*' + WH[1]

                if (MONITORDataV['Max.HorizontalSize'] && MONITORDataV['Max.VerticalSize']) {
                    let HorizontalSize = RegExGetNum(MONITORDataV['Max.HorizontalSize']);
                    let VerticalSize = RegExGetNum(MONITORDataV['Max.VerticalSize']);
                    let MonitorSize = parseFloat((Math.sqrt(Math.pow(HorizontalSize, 2) + Math.pow(VerticalSize, 2)) / 2.54).toFixed(1));
                    res['display_screen_size'] = MonitorSize + '英寸'
                }
            }
            break;
      }
   }
   return res
}



function PerformanceProcessing (PerformanceInfoObj:any) {
    // let objStr = JSON.stringify(PerformanceInfoObj);
    // let tempObj = JSON.parse(objStr);
    // console.log(tempObj);
    return DetailedDataProcessing(PerformanceInfoObj, 0.45, 1.05);
}

// controlRateMin 越小，最小FPS限制越小
// controlRateMax 越大，最大FPS限制越大
function DetailedDataProcessing(DetailedDataObj:any, controlRateMin:any, controlRateMax:any) {
    function MathArraySum(arr:any) {
        return eval(arr.join("+"));
    }

    function MathArrayAvgInt(arr:any) {
        let sum = eval(arr.join("+"));
        let avgValue = ~~(sum / arr.length * 100) / 100;
        return parseInt(parseFloat(String(avgValue)).toFixed(0));
    }

    function MathArrayMax(arr:any) {
        if (arr.length === 0) return 0;
        return Math.max.apply(null, arr);
    }

    function MathArrayMin(arr:any) {
        if (arr.length === 0) return 0;
        return Math.min.apply(null, arr);
    }

    function PreProcessing(arr:any) {
        function sortFPS(a:any, b:any) {
            // 升序
            return a - b;
        }
        let arrStr = JSON.stringify(arr);
        let arrObj = JSON.parse(arrStr);

        arrObj.sort(sortFPS);

        // 中FPS区域平均
        let midSta = parseInt(parseFloat(String(arrObj.length * 0.325)).toFixed(0));
        let midEnd = parseInt(parseFloat(String(arrObj.length * 0.775)).toFixed(0));
        let midArr = arrObj.slice(midSta, midEnd);
        let midAvg = MathArrayAvgInt(midArr);

        // 低FPS区域平均
        let minLeftIndexRate = 0.005;
        let minRightIndexRate = 0.105;
        let minAvg = midAvg * 0.35;
        while(1) {
            let minSta = parseInt(parseFloat(String(arrObj.length * minLeftIndexRate)).toFixed(0));
            let minEnd = parseInt(parseFloat(String(arrObj.length * minRightIndexRate)).toFixed(0));
            let minArr = arrObj.slice(minSta, minEnd);
            minAvg = MathArrayAvgInt(minArr);

            // 如果 (低FPS区域平均/中FPS区域平均) < 0.35
            if ((minAvg / midAvg) < 0.35) {
                minLeftIndexRate += 0.005;
                minRightIndexRate += 0.005;
            } else {
                break;
            }
        }

        // 高FPS区域平均
        let maxLeftIndexRate = 0.895;
        let maxRightIndexRate = 0.995;
        let maxAvg = midAvg * 1.35;
        while(1) {
            let maxSta = parseInt(parseFloat(String(arrObj.length * maxLeftIndexRate)).toFixed(0));
            let maxEnd = parseInt(parseFloat(String(arrObj.length * maxRightIndexRate)).toFixed(0));
            let maxArr = arrObj.slice(maxSta, maxEnd);
            maxAvg = MathArrayAvgInt(maxArr);

            // 如果 (高FPS区域平均/中FPS区域平均) > 1.35
            if ((maxAvg / midAvg) > 1.35) {
                maxLeftIndexRate -= 0.005;
                maxRightIndexRate -= 0.005;
            } else {
                break;
            }
        }

        let infoObj = Object();
        infoObj.minAvg = minAvg;
        infoObj.midAvg = midAvg;
        infoObj.maxAvg = maxAvg;
        return infoObj;
    }

    // FPS Obj
    let fpsInfoObj = DetailedDataObj["fps"];
    let fpsInfoDetailArr = JSON.parse(JSON.stringify(fpsInfoObj["detail"]));

    let PreInfo = PreProcessing(fpsInfoDetailArr);

    // Pre Cal
    let calMax = PreInfo.maxAvg;
    let calMin = PreInfo.minAvg;

    // Cal Limit
    let FPS_LIMIT_MIN = parseInt(parseFloat(String(calMin * controlRateMin)).toFixed(0));
    let FPS_LIMIT_MAX = parseInt(parseFloat(String(calMax * controlRateMax)).toFixed(0));

    // Process Obj by FPS_LIMIT_MIN
    let cpuclock = DetailedDataObj["cpuclock"];
    let cpuload = DetailedDataObj["cpuload"];
    let cpupower = DetailedDataObj["cpupower"];
    let cputemperature = DetailedDataObj["cputemperature"];
    let cpuvoltage = DetailedDataObj["cpuvoltage"];
    let fps = DetailedDataObj["fps"];
    let fps1 = DetailedDataObj["fps1"];
    let fps01 = DetailedDataObj["fps01"];
    let frametime = DetailedDataObj["frametime"];

    let gpuclock = DetailedDataObj["gpuclock"];
    let gpuload = DetailedDataObj["gpuload"];
    let gpumemoryload = DetailedDataObj["gpumemoryload"];
    let gpupower = DetailedDataObj["gpupower"];
    let gputemperature = DetailedDataObj["gputemperature"];
    let gpuhotspottemp = DetailedDataObj["gpuhotspottemp"];
    let gpumemoryclock = DetailedDataObj["gpumemoryclock"];

    let memory = DetailedDataObj["memory"];
    let memorytemperature = DetailedDataObj["memorytemperature"];

    let disk_temp = DetailedDataObj["disk_temp"];
    let disk_load = DetailedDataObj["disk_load"];


    var isClip = false;
    let fps_process = JSON.parse(JSON.stringify(fpsInfoObj["detail"]));
    for (let idx = fps_process.length - 1; idx >= 0; idx--) {
        let fps_current = fps_process[idx];
        let delete_flag = false;
        if (fps_current < FPS_LIMIT_MIN) {
            delete_flag = true;
        } else if (fps_current > FPS_LIMIT_MAX) {
            delete_flag = true;
        }

        if (delete_flag) {
            isClip = true;

            // cpuclock
            let cpuclockDetail = cpuclock["detail"];
            cpuclockDetail.splice(idx, 1);

            //cpuclock_performance
            let cpuclockPLR = cpuclock["performance"]
            cpuclockPLR.splice(idx, 1);

            // cpuload
            let cpuloadDetail = cpuload["detail"];
            cpuloadDetail.splice(idx, 1);

            // cpupower
            let cpupowerDetail = cpupower["detail"];
            cpupowerDetail.splice(idx, 1);

            // cputemperature
            let cputemperatureDetail = cputemperature["detail"];
            cputemperatureDetail.splice(idx, 1);

            // cpuvoltage
            let cpuvoltageDetail = cpuvoltage["detail"];
            cpuvoltageDetail.splice(idx, 1);

            // fps
            let fpsDetail = fps["detail"];
            fpsDetail.splice(idx, 1);

            // fps1
            let fps1Detail = fps1["detail"];
            fps1Detail.splice(idx, 1);

            // fps01
            let fps01Detail = fps01["detail"];
            fps01Detail.splice(idx, 1);

            // frametime
            let frametimeDetail = frametime["detail"];
            frametimeDetail.splice(idx, 1);

            // gpuclock
            gpuclock.forEach(function (info:any) {
                let infoDetail = info["detail"];
                infoDetail.splice(idx, 1);
            });

            // gpuload
            gpuload.forEach(function (info:any) {
                let infoDetail = info["detail"];
                infoDetail.splice(idx, 1);
            });

            // gpumemoryload
            gpumemoryload.forEach(function (info:any) {
                let infoDetail = info["detail"];
                infoDetail.splice(idx, 1);
            });

            // gpupower
            gpupower.forEach(function (info:any) {
                let infoDetail = info["detail"];
                infoDetail.splice(idx, 1);
            });

            // gputemperature
            gputemperature.forEach(function (info:any) {
                let infoDetail = info["detail"];
                infoDetail.splice(idx, 1);
            });

            // memory
            let memoryDetail = memory["detail"];
            memoryDetail.splice(idx, 1);

            let memorytemperatureDetail = memorytemperature["detail"];
            memorytemperatureDetail.splice(idx, 1)

            // disk
            if (disk_temp) {
                let disk_tempDetail = disk_temp["detail"];
                disk_tempDetail.splice(idx, 1)
            }

            if (disk_load) {
                let disk_loadDetail = disk_load["detail"];
                disk_loadDetail.splice(idx, 1)
            }
        }
    }

    // Cal new avg | min | max
    cpuclock["avg"] = MathArrayAvgInt(cpuclock["detail"]);
    cpuclock["min"] = MathArrayMin(cpuclock["detail"]);
    cpuclock["max"] = MathArrayMax(cpuclock["detail"]);

    cpuload["avg"] = MathArrayAvgInt(cpuload["detail"]);
    cpuload["min"] = MathArrayMin(cpuload["detail"]);
    cpuload["max"] = MathArrayMax(cpuload["detail"]);

    cpupower["avg"] = MathArrayAvgInt(cpupower["detail"]);
    cpupower["min"] = MathArrayMin(cpupower["detail"]);
    cpupower["max"] = MathArrayMax(cpupower["detail"]);

    cputemperature["avg"] = MathArrayAvgInt(cputemperature["detail"]);
    cputemperature["min"] = MathArrayMin(cputemperature["detail"]);
    cputemperature["max"] = MathArrayMax(cputemperature["detail"]);


    fps["avg"] = MathArrayAvgInt(fps["detail"]);
    fps["min"] = MathArrayMin(fps["detail"]);
    fps["max"] = MathArrayMax(fps["detail"]);


    fps1["avg"] = MathArrayAvgInt(fps1["detail"]);
    fps1["min"] = MathArrayMin(fps1["detail"]);
    fps1["max"] = MathArrayMax(fps1["detail"]);

    fps01["avg"] = MathArrayAvgInt(fps01["detail"]);
    fps01["min"] = MathArrayMin(fps01["detail"]);
    fps01["max"] = MathArrayMax(fps01["detail"]);



    frametime["avg"] = MathArrayAvgInt(frametime["detail"]);
    frametime["min"] = MathArrayMin(frametime["detail"]);
    frametime["max"] = MathArrayMax(frametime["detail"]);

    gpuclock.forEach(function (info:any) {
        info["avg"] = MathArrayAvgInt(info["detail"]);
        info["min"] = MathArrayMin(info["detail"]);
        info["max"] = MathArrayMax(info["detail"]);
    });

    gpuload.forEach(function (info:any) {
        info["avg"] = MathArrayAvgInt(info["detail"]);
        info["min"] = MathArrayMin(info["detail"]);
        info["max"] = MathArrayMax(info["detail"]);
    });

    gpumemoryload.forEach(function (info:any) {
        info["avg"] = MathArrayAvgInt(info["detail"]);
        info["min"] = MathArrayMin(info["detail"]);
        info["max"] = MathArrayMax(info["detail"]);
    });

    gpupower.forEach(function (info:any) {
        info["avg"] = MathArrayAvgInt(info["detail"]);
        info["min"] = MathArrayMin(info["detail"]);
        info["max"] = MathArrayMax(info["detail"]);
    });

    gputemperature.forEach(function (info:any) {
        info["avg"] = MathArrayAvgInt(info["detail"]);
        info["min"] = MathArrayMin(info["detail"]);
        info["max"] = MathArrayMax(info["detail"]);
    });

    memory["avg"] = MathArrayAvgInt(memory["detail"]);
    memory["min"] = MathArrayMin(memory["detail"]);
    memory["max"] = MathArrayMax(memory["detail"]);

    DetailedDataObj["isClip"] = isClip;
    return DetailedDataObj;
}


function getDataOption(name:any, k:any)
{ //获取列表详情信息
    let jsonData:any = {};
    jsonData['id'] = k.id;
    jsonData['date'] = name;
    jsonData['timestamp'] = Number(k.starttime);
    jsonData['timeend'] = Number(k.endtime);
    jsonData['gametime'] = Number(k.gametime);
    jsonData['icon'] = k.icon;
    jsonData['processname'] = (k['processpath']);
    jsonData['province'] = k.province;
    jsonData['city'] = k.city;
    jsonData['wea'] = k.wea;
    jsonData['wea_img'] = k.wea_img;
    jsonData['tem'] = k.tem;
    jsonData['recordstart'] = k.recordstart;
    jsonData['hd_info'] = k.hd_info;
    jsonData['refreshtime'] = k.refresh_time;
    jsonData['hd_list_data'] = k.hd_list_data;
    return jsonData;
}

async function ListHtmlProcess2 (b:any,DatabaseId:any)
{
    let AllDate = '', DateArr = [], DateArr1 = [], RecordTimeAll = 0, RecordPowerAll = 0, RecordcarbonAll = 0, getAppDataDir = null
    var DetailedDataHtml = '';
    // if (b.hd_list_data) {  //如果存在b.hd_list_data字段
    //     let hd_data = JSON.parse(decodeURIComponent(b.hd_list_data));
    //     let PowerCarbon = { "power": hd_data['power'], "carbon": hd_data['co2'] };
    //     console.log('hd_data',hd_data)
    //     let ProcessName = b.processname;
    //     let RecordTime = b.gametime;
    //     RecordTimeAll += RecordTime;
    //     RecordPowerAll += Number(PowerCarbon['power']);
    //     RecordcarbonAll += Number(PowerCarbon['carbon']);
    //     if (ProcessName.length >= 50) {ProcessName = ProcessName.substring(0, 50);}
    //     let ProcesIcon = getAppDataDir + '\\common\\icon\\' + (b.icon);
    //     let Icon_src = '../../../../../static/icon/Public/ic_rp_game.png';
    //     let FileExists = true;
    //     let weaName = '';
    //     let data_Det_arr = hd_data;
    // }
    // else
    {
      const DetailedData_id = await gamepp.database.query.promise(DatabaseId, "'" + b['timestamp'] + "'", "COUNT(id)");
        if (DetailedData_id.length === 0) {
            await gamepp.database.delete.promise(DatabaseId, "GamePP_BaseInfo", "starttime = " + b['timestamp'] + "");//列表数据
            return false
        }
        let list_length = DetailedData_id[0]['COUNT(id)'];
        let list_length_spacing = 1;
        if (list_length >= 5000) {list_length_spacing = Math.ceil(list_length / 5000);}
        let DetailedData =  await gamepp.database.query.promise(DatabaseId, "'" + b['timestamp'] + "'", "*", "id-(id/" + list_length_spacing + ")*" + list_length_spacing + "=0");
        // console.log(DetailedData)
        if (DetailedData.length === 0) return false;
        console.warn('DetailedData',DetailedData)
        console.warn('DetailasdasdasdasdsadsasdsedData',DetailedData)
        var data_Det_arr:any = {};
        data_Det_arr['cpuload'] = {};
        data_Det_arr['cpuloadP'] = {};
        data_Det_arr['cpuloadE'] = {};
        data_Det_arr['cpuclock'] = {};
        data_Det_arr['cpuclockP'] = {};
        data_Det_arr['cpuclockE'] = {};
        data_Det_arr['cpupower'] = {};
        data_Det_arr['cputemperature'] = {};
        data_Det_arr['cpuvoltage'] = {};
        data_Det_arr['fps'] = {};
        data_Det_arr['fps1'] = {};
        data_Det_arr['fps01'] = {};
        data_Det_arr['frametime'] = {};
        data_Det_arr['gpuload'] = [];
        data_Det_arr['gpuload1'] = [];
        data_Det_arr['gpumemoryload'] = [];
        data_Det_arr['gpumemorytemp'] = [];
        data_Det_arr['gpumemoryclock'] = [];
        data_Det_arr['gpupower'] = [];
        data_Det_arr['gputemperature'] = [];
        data_Det_arr['gpuhotspottemp'] = [];
        data_Det_arr['gpuclock'] = [];
        data_Det_arr['gpuvoltage'] = [];
        data_Det_arr['memory'] = {};
        data_Det_arr['memorytemperature'] = {};

        data_Det_arr['diskload'] = {};
        data_Det_arr['disktemp'] = {};

        data_Det_arr['performance'] = {};
        data_Det_arr['amd_cpu_thermal'] = [];
        data_Det_arr['performance_gpu'] = [];
        data_Det_arr['amd_gpu_thermalhotspot'] = [];
        data_Det_arr['amd_gpu_thermalmemory'] = [];

        data_Det_arr['cpu_clock_core'] = [];
        data_Det_arr['cpu_clock_effective_core'] = [];

        data_Det_arr['disk_temp'] = {};
        data_Det_arr['disk_load'] = {};

        let cpuloadDetail = [],cpuloadPDetail = [],cpuloadEDetail = [], cpuclockDetail = [],cpuclockPDetail = [],cpuclockEDetail = [] ,cpupowerDetail = [], cputemperatureDetail = [], cpuvoltageDetail = [],
        gpuloadDetail0 = [], gpuloadDetail1 = [],gpuload1Detail0 = [], gpumemoryloadDetail0 = [], gpumemoryloadDetail1 = [], gpumemorytempDetail = [], gpumemoryclockDetail = [], gpupowerDetail0 = [], gpupowerDetail1 = [], gputemperatureDetail0 = [], gputemperatureDetail1 = [], gpuhotspottempDetail = [], gpuclockDetail0 = [], gpuclockDetail1 = [],
        gpuvoltageDetail0 = [], gpuvoltageDetail1 = [],
        fpsDetail = [],fps1Detail = [],fps01Detail = [], frametimeDetail = [], memoryDetail = [], memorytemperatureDetail = [], diskloadDetail = [], disktempDetail = [], performanceDetail = [], amd_cpu_thermalDetail = [], performance_gpu_THERMALDetail = [],amd_gpu_thermalhotspotDetail=[],amd_gpu_thermalmemoryDetail=[],
        cpu_clock_coreDetail     = [], cpu_clock_effective_coreDetail = [],disk_tempDetail=[],disk_loadDetail=[];
        let del_interval = 1, DetailedDataLen = DetailedData.length;
        if (DetailedDataLen <= 5000) {
            del_interval = 1;
        } else if (DetailedDataLen <= 10000) {
            del_interval = 40;
        } else if (DetailedDataLen <= 20000) {
            del_interval = 60;
        } else if (DetailedDataLen <= 30000) {
            del_interval = 100;
        } else if (DetailedDataLen <= 40000) {
            del_interval = 140;
        } else if (DetailedDataLen <= 50000) {
            del_interval = 200;
        } else if (DetailedDataLen <= 60000) {
            del_interval = 300;
        } else {
            del_interval = 400;
        }
        let cpuTemperatureCore = [], cpuLoadCore = [], cpuClockCore = [];
    for (let i = 0; i < DetailedData.length; i++) {
        if (DetailedData[i]['cpuload'].indexOf('NaN') === -1) {
            if (Number.isInteger(i / del_interval)) {

                cpuloadDetail.push(Number(DetailedData[i]['cpuload'].split('|')[0]));
                if (DetailedData[i]['cpuloadP']) {cpuloadPDetail.push(DetailedData[i]['cpuloadP']);}
                if (DetailedData[i]['cpuloadE']) {cpuloadEDetail.push(DetailedData[i]['cpuloadE']);}

                if (DetailedData[i]['cpuclockAVG']) {
                    cpuclockDetail.push(Number(DetailedData[i]['cpuclockAVG']));
                } else {
                    cpuclockDetail.push(Number(DetailedData[i]['cpuclock'].split('|')[0]));
                }

                if (DetailedData[i]['cpuclockP']) {cpuclockPDetail.push(DetailedData[i]['cpuclockP']);}
                if (DetailedData[i]['cpuclockE']) {cpuclockEDetail.push(DetailedData[i]['cpuclockE']);}


                cpupowerDetail.push(Number(DetailedData[i]['cpupower']));

                if (typeof (DetailedData[i]['cputemperature']) != 'number') {cputemperatureDetail.push(Number(DetailedData[i]['cputemperature'].split('|')[0]));} else {cputemperatureDetail.push((DetailedData[i]['cputemperature']));}

                cpuvoltageDetail.push(Number(DetailedData[i]['cpuvoltage']))

                var gpuloadArr = DetailedData[i]['gpuload'].split('|');

                gpuloadDetail0.push(Number(DetailedData[i]['gpuload'].split('|')[0]));
                gpuloadDetail1.push(Number(DetailedData[i]['gpuload'].split('|')[1]));

                if (DetailedData[i]['gpuload1']){
                    gpuload1Detail0.push(Number(DetailedData[i]['gpuload1'].split('|')[0]));
                }


                gpumemoryloadDetail0.push(Number(DetailedData[i]['gpumemoryload'].split('|')[0]));
                gpumemoryloadDetail1.push(Number(DetailedData[i]['gpumemoryload'].split('|')[1]));


                if (DetailedData[i]['gpumemorytemp']) {gpumemorytempDetail.push(Number(DetailedData[i]['gpumemorytemp']))}
                if (DetailedData[i]['gpumemoryclock']) {gpumemoryclockDetail.push(Number(DetailedData[i]['gpumemoryclock']))}


                gpupowerDetail0.push(Number(DetailedData[i]['gpupower'].split('|')[0]));
                gpupowerDetail1.push(Number(DetailedData[i]['gpupower'].split('|')[1]));

                gputemperatureDetail0.push(Number(DetailedData[i]['gputemperature'].split('|')[0]))
                gputemperatureDetail1.push(Number(DetailedData[i]['gputemperature'].split('|')[1]))


                gpuhotspottempDetail.push(Number(DetailedData[i]['gpuhotspottemp']))

                gpuclockDetail0.push(Number(DetailedData[i]['gpuclock'].split('|')[0]))
                gpuclockDetail1.push(Number(DetailedData[i]['gpuclock'].split('|')[1]))

                if (DetailedData[i]['gpuvoltage']) {
                    gpuvoltageDetail0.push(Number(DetailedData[i]['gpuvoltage'].split('|')[0]));
                    gpuvoltageDetail1.push(Number(DetailedData[i]['gpuvoltage'].split('|')[1]));
                }

                if (DetailedData[i]['fps']!== undefined) {fpsDetail.push(Number(DetailedData[i]['fps']))}

                if (DetailedData[i]['fps1']!== undefined) {fps1Detail.push(Number(DetailedData[i]['fps1']))}
                if (DetailedData[i]['fps01']!== undefined) {fps01Detail.push(Number(DetailedData[i]['fps01']))}


                if (DetailedData[i]['frametime']) {frametimeDetail.push(Number(DetailedData[i]['frametime']))}
                memoryDetail.push(Number(DetailedData[i]['memory']));
                memorytemperatureDetail.push(Number(DetailedData[i]['memorytemperature']));

                diskloadDetail.push(Number(DetailedData[i]['disk_load']));
                disktempDetail.push(Number(DetailedData[i]['disk_temp']));


                if (DetailedData[i]['performance'] !== undefined) {
                    performanceDetail.push(DetailedData[i]['performance']);
                }
                if (DetailedData[i]['amd_cpu_thermal'] !== undefined) {
                    amd_cpu_thermalDetail.push(DetailedData[i]['amd_cpu_thermal']);
                }

                performance_gpu_THERMALDetail.push(Number(DetailedData[i]['performance_gpu']));

                amd_gpu_thermalhotspotDetail.push(DetailedData[i]['amd_gpu_thermalhotspot']);
                amd_gpu_thermalmemoryDetail.push(DetailedData[i]['amd_gpu_thermalmemory']);

                cpuTemperatureCore.push(DetailedData[i]['cpu_temperature_core'].split('|').map(Number));

                if (DetailedData[i]['cpu_load_core']) {
                    cpuLoadCore.push(DetailedData[i]['cpu_load_core'].split('|').map(Number));
                }
                if (DetailedData[i]['cpu_clock_core']){
                    cpuClockCore.push(DetailedData[i]['cpu_clock_core'].split('|').map(Number));
                }


                if (DetailedData[i]['cpu_clock_core']){
                    cpu_clock_coreDetail.push(DetailedData[i]['cpu_clock_core'])
                }

                if (DetailedData[i]['cpu_clock_effective_core']){
                    cpu_clock_effective_coreDetail.push(DetailedData[i]['cpu_clock_effective_core'])
                }


                if (DetailedData[i]['disk_temp']!== undefined) {disk_tempDetail.push(Number(DetailedData[i]['disk_temp']))}
                if (DetailedData[i]['disk_load']!== undefined) {disk_loadDetail.push(Number(DetailedData[i]['disk_load']))}
            }
        }
    }
    let fps01_avg = AVGNum(fps1Detail)

//Fps低于1%low值
let cputempDetailLow = [], cpuloadDetailLow = [], cpuclockDetailLow = []
let cpuClockCore1 = [], cpuLoadCore1 = [];


let gputempDetailLow = [], gpuloadd3dDetailLow = [],gpuloadtotalDetailLow = [], gpuclockDetailLow = [],memoryloadDetailLow = [];

let isLowFPS01 = false
console.log(DetailedData)

if (DetailedData[0]['cpu_clock_core'])
{
    for (let i = 0; i < DetailedData.length; i++) {
        if (DetailedData[i]['fps'] < fps01_avg) {
            isLowFPS01 = true
            cputempDetailLow.push((DetailedData[i]['cputemperature']));
            cpuloadDetailLow.push(Number(DetailedData[i]['cpuload'].split('|')[0]));
            cpuclockDetailLow.push(Number(DetailedData[i]['cpuclockAVG']));
            if (DetailedData[i]['cpu_clock_core']) {
                cpuClockCore1.push(DetailedData[i]['cpu_clock_core'].split('|').map(Number));
            }

            cpuLoadCore1.push(DetailedData[i]['cpu_load_core'].split('|').map(Number));

            gputempDetailLow.push(Number(DetailedData[i]['gputemperature'].split('|')[0]))
            gpuloadd3dDetailLow.push(Number(DetailedData[i]['gpuload'].split('|')[0]))
            gpuloadtotalDetailLow.push(Number(DetailedData[i]['gpuload1'].split('|')[0]))
            gpuclockDetailLow.push(Number(DetailedData[i]['gpuclock'].split('|')[0]))
            memoryloadDetailLow.push(Number(DetailedData[i]['memory']));
        }
    }
}

//最高核心温度 和 最高温度核心

// if (cpuTemperatureCore.length !== 0) {
//     let coreTempTotals = new Array(cpuTemperatureCore[0].length).fill(0);
//     cpuTemperatureCore.forEach(row => {
//         row.forEach((temp, index) => {
//             coreTempTotals[index] += temp;
//         });
//     });
//     let coreAverages = coreTempTotals.map(total => Math.round(total / cpuTemperatureCore.length));
//     let maxAverageCoreIndex = coreAverages.indexOf(Math.max(...coreAverages));
//     let maxAverageTemperature = coreAverages[maxAverageCoreIndex];
// }



// if (cpuLoadCore.length !== 0) {
//     let coreLoadTotals = new Array(cpuLoadCore[0].length).fill(0);
//     cpuLoadCore.forEach(row => {
//         row.forEach((load, index) => {
//             coreLoadTotals[index] += load;
//         });
//     });
//     let coreLoadAverages = coreLoadTotals.map(total => Math.round(total / cpuLoadCore.length));
//     let maxAverageCoreLoadIndex = coreLoadAverages.indexOf(Math.max(...coreLoadAverages));
//     let maxAverageLoad = coreLoadAverages[maxAverageCoreLoadIndex];
// }

// if (cpuClockCore.length !== 0) {
//     let coreClockTotals = new Array(cpuClockCore[0].length).fill(0);
//     cpuClockCore.forEach(row => {
//         row.forEach((closk, index) => {
//             coreClockTotals[index] += closk;
//         });
//     });
//     let coreClockAverages = coreClockTotals.map(total => Math.round(total / cpuClockCore.length));

//     let maxAverageCoreClockIndex = coreClockAverages.indexOf(Math.max(...coreClockAverages));
//     let maxAverageClock = coreClockAverages[maxAverageCoreClockIndex];

// }

for (let i = 0; i < Object.keys(data_Det_arr).length; i++) {
        let key=Object.keys(data_Det_arr)[i]
        if (['cpuload', 'cpuloadP', 'cpuloadE', 'cpuclock', 'cpuclockP', 'cpuclockE', 'cpupower', 'cputemperature', 'cpuvoltage', 'fps','fps1','fps01', 'frametime', 'memory', 'memorytemperature','diskload','disktemp', 'gpuhotspottemp', 'gpumemorytemp', 'gpumemoryclock','disk_temp','disk_load'].includes(key)) {
            DetailProcess(data_Det_arr, Object.keys(data_Det_arr)[i], eval(Object.keys(data_Det_arr)[i] + 'Detail'));
        }
    }

         function DetailProcess(data_Det_arr:any, ObjectKeys:any, Detail:any) {
            data_Det_arr[ObjectKeys]['detail'] = Detail;
            data_Det_arr[ObjectKeys]['avg'] = AVGNum(Detail)
            data_Det_arr[ObjectKeys]['max'] = MaxNum(Detail)
            data_Det_arr[ObjectKeys]['min'] = MinNum(Detail)
          }


        data_Det_arr['cpuclock']['performance'] = performanceDetail;
        data_Det_arr['amd_cpu_thermal'] = amd_cpu_thermalDetail;


        data_Det_arr['performance_gpu'] = performance_gpu_THERMALDetail;
        data_Det_arr['amd_gpu_thermalhotspot'] = amd_gpu_thermalhotspotDetail;
        data_Det_arr['amd_gpu_thermalmemory'] = amd_gpu_thermalmemoryDetail;

        for (let j = 0; j < (gpuloadArr.length - 1); j++) {
            let data_Det_arr1:any = {};
            data_Det_arr1['detail'] = eval('gpuloadDetail' + j);
            data_Det_arr1['avg'] = AVGNum(eval('gpuloadDetail' + j));
            data_Det_arr1['max'] = MaxNum(eval('gpuloadDetail' + j));
            data_Det_arr1['min'] = MinNum(eval('gpuloadDetail' + j));
            data_Det_arr['gpuload'].push(data_Det_arr1);

            let data_Det_arr7:any = {};
            data_Det_arr7['detail'] = eval('gpuload1Detail' + j);
            data_Det_arr7['avg'] = AVGNum(eval('gpuload1Detail' + j));
            data_Det_arr7['max'] = MaxNum(eval('gpuload1Detail' + j));
            data_Det_arr7['min'] = MinNum(eval('gpuload1Detail' + j));
            data_Det_arr['gpuload1'].push(data_Det_arr7);

            let data_Det_arr2:any = {};
            data_Det_arr2['detail'] = eval('gputemperatureDetail' + j);
            data_Det_arr2['avg'] = AVGNum(eval('gputemperatureDetail' + j));
            data_Det_arr2['max'] = MaxNum(eval('gputemperatureDetail' + j));
            data_Det_arr2['min'] = MinNum(eval('gputemperatureDetail' + j));
            data_Det_arr['gputemperature'].push(data_Det_arr2);

            let data_Det_arr3:any = {};
            data_Det_arr3['detail'] = eval('gpumemoryloadDetail' + j);
            data_Det_arr3['avg'] = AVGNum(eval('gpumemoryloadDetail' + j));
            data_Det_arr3['max'] = MaxNum(eval('gpumemoryloadDetail' + j));
            data_Det_arr3['min'] = MinNum(eval('gpumemoryloadDetail' + j));
            data_Det_arr['gpumemoryload'].push(data_Det_arr3);

            let data_Det_arr4:any = {};
            data_Det_arr4['detail'] = eval('gpupowerDetail' + j);
            data_Det_arr4['avg'] = AVGNum(eval('gpupowerDetail' + j));
            data_Det_arr4['max'] = MaxNum(eval('gpupowerDetail' + j));
            data_Det_arr4['min'] = MinNum(eval('gpupowerDetail' + j));
            data_Det_arr['gpupower'].push(data_Det_arr4);

            let data_Det_arr5:any = {};
            data_Det_arr5['detail'] = eval('gpuclockDetail' + j);
            data_Det_arr5['avg'] = AVGNum(eval('gpuclockDetail' + j));
            data_Det_arr5['max'] = MaxNum(eval('gpuclockDetail' + j));
            data_Det_arr5['min'] = MinNum(eval('gpuclockDetail' + j));
            data_Det_arr['gpuclock'].push(data_Det_arr5);

            let data_Det_arr6:any = {};
            data_Det_arr6['detail'] = eval('gpuvoltageDetail' + j);
            data_Det_arr6['avg'] = AVGNum(eval('gpuvoltageDetail' + j));
            data_Det_arr6['max'] = MaxNum(eval('gpuvoltageDetail' + j));
            data_Det_arr6['min'] = MinNum(eval('gpuvoltageDetail' + j));
            data_Det_arr['gpuvoltage'].push(data_Det_arr6);
  }

        let dataData = PerformanceProcessing(data_Det_arr);
        console.warn('dataData',dataData );
        let EndTimeData = DetailedData[DetailedData.length - 1];
        console.warn('EndTimeData',EndTimeData);
        let StandGPULocaIndex = 0
        if (data_Det_arr.gpuload !== null) {
            if (data_Det_arr.gpuload.length === 2) {
                if (data_Det_arr.gpuload[0]['avg'] < data_Det_arr.gpuload[1]['avg']) {
                    StandGPULocaIndex = 1;
                } else {
                    StandGPULocaIndex = 0;
                }
            } else {
                StandGPULocaIndex = 0;
            }
        }

        function PowerProcess(data:any, cpuload:any, gpuload:any, GameTime:any, GPUIndex:number) {
               console.warn('GameTime',GameTime);

               let  GPUEstimatePower:any = 0
               let GameTimeH:any = 0
               let  CPUEstimatePower:any = 0
               var cpower = (data.cpupower.avg * 2);
               var gpower = data.gpupower[GPUIndex].avg;
               if (cpuload <= 25) {
                  CPUEstimatePower = cpower * 1.7;
               } else if (cpuload > 25 && cpuload <= 50) {
                  CPUEstimatePower = cpower * 1.5;
               } else if (cpuload > 50 && cpuload <= 75) {
                  CPUEstimatePower = cpower * 1.3;
               } else if (cpuload > 75 && cpuload <= 100) {
                  CPUEstimatePower = cpower * 1.1;
               }
               if (gpuload <= 25) {
                  GPUEstimatePower = gpower * 1.25;
               } else if (gpuload > 25 && gpuload <= 50) {
                  GPUEstimatePower = gpower * 1.2;
               } else if (gpuload > 50 && gpuload <= 75) {
                  GPUEstimatePower = gpower * 1.15;
               } else if (gpuload > 75 && gpuload <= 100) {
                  GPUEstimatePower = gpower * 1.05;
               }
               GameTimeH = (((GameTime) / 3600));
               var TotalPower:any = CPUEstimatePower + GPUEstimatePower;
               // var TotalPowerKWh:any = ((TotalPower / 1000) * GameTimeH) === 0 ? 0.01 : parseFloat((TotalPower / 1000) * GameTimeH).toFixed(4);
               var TotalPowerKWh:any = ((TotalPower / 1000) * GameTimeH) === 0 ? 0.01 : (TotalPower / 1000) * GameTimeH.toFixed(4);
               var TotalCarbon:any = parseFloat(((TotalPowerKWh * 0.785) * 1000).toFixed(4));
               console.warn('GameTimeH',GameTimeH);
               console.warn('TotalPower',TotalPower);
               console.warn('TotalPowerKWh',TotalPowerKWh);
               console.warn('TotalCarbon',TotalCarbon);

               var DataArr:any = [];
               DataArr['power'] = TotalPowerKWh;
               DataArr['carbon'] = TotalCarbon;
               return DataArr;
        }

        let PowerCarbon
        if (data_Det_arr.cpuload !== null && data_Det_arr.gpuload !== null && data_Det_arr.cpuload.avg) {

            PowerCarbon = PowerProcess(data_Det_arr, data_Det_arr.cpuload.avg, data_Det_arr.gpuload[StandGPULocaIndex].avg, (b.timeend - b.timestamp), StandGPULocaIndex);
        } else {
            PowerCarbon = { "power": "0", "carbon": "0" };
        }
        try {
            // eval 赋值
            let hd_list_data:any = {};
            let hd_list_data_fps = {},hd_list_data_fps1 = {},hd_list_data_fps01 = {}, hd_list_data_memory = {},hd_list_data_memorytemperature={}, hd_list_data_diskload = {},hd_list_data_disktemp={};
            let hd_list_data_cpuload = {}, hd_list_data_cputemperature = {}, hd_list_data_cpuclock = {}, hd_list_data_cpupower = {}, hd_list_data_cpuvoltage = {};
            let hd_list_data_gpuload = {},hd_list_data_gpuload1 = {}, hd_list_data_gpumemoryload = {}, hd_list_data_gputemperature = {}, hd_list_data_gpuclock = {}, hd_list_data_gpupower = {}, hd_list_data_gpuvoltage = {};
            let hd_list_data_disk_temp = {},hd_list_data_disk_load = {};

            let KeyArr = ['fps', 'fps1', 'fps01', 'memory', 'memorytemperature', 'cpuclock', 'cpuload', 'cpupower', 'cputemperature', 'cpuvoltage', 'gpuclock', 'gpuload', 'gpuload1', 'gpumemoryload', 'gpupower', 'gputemperature', 'gpuvoltage', 'disk_temp', 'disk_load'];

            for (let i = 0; i < KeyArr.length; i++) {
                let key = KeyArr[i];
                let hd_list_data_ = 'hd_list_data_';
                if (key.includes('gpu')) {
                    eval("hd_list_data_" + [key])['max'] = data_Det_arr[key][StandGPULocaIndex].max;
                    eval("hd_list_data_" + [key])['min'] = data_Det_arr[key][StandGPULocaIndex].min;
                    eval("hd_list_data_" + [key])['avg'] = data_Det_arr[key][StandGPULocaIndex].avg;
                } else {
                    eval("hd_list_data_" + [key])['max'] = data_Det_arr[key].max;
                    eval("hd_list_data_" + [key])['min'] = data_Det_arr[key].min;
                    eval("hd_list_data_" + [key])['avg'] = data_Det_arr[key].avg;
                }
            }

            for (let i = 0; i < KeyArr.length; i++) {
                let key = KeyArr[i];
                hd_list_data[key] = eval('hd_list_data_' + key);
            }
            console.warn('hd_list_data',hd_list_data);
            hd_list_data['power'] = Number(parseFloat(PowerCarbon['power']));
            hd_list_data['co2'] = Number(parseFloat(PowerCarbon['carbon']));
            let _Info = encodeURIComponent(JSON.stringify(hd_list_data));
            try {await gamepp.database.update.promise(DatabaseId, "GamePP_BaseInfo", ['hd_list_data="' + _Info + '"'], 'id = "' + b.id + '"');
            return hd_list_data
           } catch {}
            }catch(err){
                console.log(err)
            }
   }
}


    const getDate = async() =>
    {

        console.warn('@change触发',value.value);
        clipDate(value.value)
        await getItemlist()
        //   console.warn(formatDate(currentDate.value));

    }
   const disabledDate = (time:any) =>
   {
    const date = new Date(time);
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const dateString = `${year}-${month}-${day}`;
      // 禁用日期的逻辑，例如禁用所有周六和周日
      return !DateArr.value.includes(dateString);
   }
   const currentdays = ref([
        '2024-07-15',
        '2024-07-14',
        '2024-07-13',
        '2024-07-12',
        '2024-07-11',
])



    const chooseDate = async (index:number) => //选择日期
    {
        if(value.value == currentdays.value[index]){return}
        value.value = currentdays.value[index] //当前日期
        clipDate(currentdays.value[index])
        await getItemlist()

    }

    let curTab = ref<number>(0)
    const toggleTab = (index:number)  =>
    {

        curTab.value = index

        tabInfo.value.forEach((v)=>{
            v.choosen = false
        })
        tabInfo.value[index].choosen = true
    }

    const getTemperatureStyle = ((item:any) =>
    {
        const temperature = item
        let color;
        if (temperature < 50) {
            color = '#35D57D';
        } else if (temperature >= 50 && temperature < 80) {
            color = '#3579D5';
        } else {
            color = '#BF4040';
        }
        return { color: color };
    });

    const activeName = ref(0)


</script>

<template>
 <el-config-provider :locale="zhCn">
   <div class="GameRebound" :style="{'zoom':zoomLevel}">
    <!-- <index type="text" style="position: absolute;top: 9999px;z-index: -1" tabindex="0"/> -->
      <header class="nav">
         <div class="left"><img src="../../assets/img/Public/logo_gpp.png" alt=""><p>{{ $t('home.performanceStatistics') }}</p><p style="margin-left: 13px;"><span>V</span>{{ packageversion }}</p></div>
          <div class="drag-bar"></div>
          <div class="setting">
              <RightTopIcons close-icon minimize-icon @close="setting(1)" @minimize="setting(0)" hover-color="#22232e" />
            <!--<div class="dataitem" v-for="(item,index) in settings" @click="setting(index)">-->
            <!--    <span :class="['iconfont',item.icon]"></span>-->
            <!--  </div>-->
            </div>
      </header>
      <div class="container">
        <div class="content" v-show="!isReady" style="display: flex;justify-content: center;align-items: center;">
            <div style="display: flex;flex-direction: column;align-items: center;justify-content: center;">
                 <img :src="没有游戏记录" alt="" style="width: 450px;height: 200px;">
                 <div style="color:#777777;" class="emptyBox">{{ $t('GameRebound.noGameRecord') }}</div>
            </div>

        </div>
        <div class="content" v-show="isReady">
            <div class="dataTable">
                <div class="demo-date-picker">
                    <el-date-picker
                    v-model="value"
                    type="date"
                    placeholder="Pick a day"
                    tabindex = "-1"
                    format="YYYY/MM/DD"
                    value-format="YYYY-MM-DD"
                    @change="getDate"
                    :disabled-date="disabledDate"
                    :default-visible = false
                    :locale = "zhCn"
                    >
                </el-date-picker>
            </div>
            <div class="chooseTime">
                <div class="line"></div>
                 <div class="item" v-for="(item,index) in currentdays" @click="chooseDate(index)">
                    <div class="dot" :class="[item == value?'active':'']">

                    </div>
                    <div class="linearDiv" v-show="item == value"> </div>
                    <!-- <div class="linearDiv2"></div> -->
                    <p :style="{top:index% 2 === 0?'30px':'-20px'}">{{ item }}</p>
                </div>
            </div>
        </div>
            <div class="electronic">
                    <span>{{ $t('GameRebound.gameDuration') }}</span><span class="elecnum" style="color:#3579D5;">{{ curDayPlayTotal.gametime }}</span>
                    <span style="margin-left: 20px">{{ $t('GameRebound.gameElectricity') }}</span><span class="elecnum" style="color:#FFC600;">{{ curDayPlayTotal.electronic }}</span><span>{{ $t('GameRebound.degree') }}</span>
                    <span style="margin-left: 20px">{{ $t('GameRebound.gameCo2') }}</span><span class="elecnum" style="color:#FF7E00;">{{ curDayPlayTotal.co2 }}</span><span>{{ $t('GameRebound.gram') }}</span>
            </div>
            <div class="detailBox scroll">
                 <div class="item" v-for="(item,index) in curItem">
                    <el-collapse v-model="activeName">
                    <el-collapse-item :name="index" tabindex="-1">
                        <template #title>
                            <span class="time"><span style="color: #777777;white-space: nowrap;"></span>{{item.starttime}}</span>
                            <div v-if="item.processname" class="iconexe" style="display: flex;align-items:center;width: 260px;">
                                <img class="gameicon" :src="item.ProcesIcon" alt="">
                                 <p class="exe">{{item.processname}}</p>
                                 <p style="white-space: nowrap;">FPS: <span style="color: #3579D5;font-weight: bold;font-size: 16px;">{{item['data']['fps']['avg']}}</span></p>
                            </div>
                            <div v-if="!item.processname" class="iconexe" style="display: flex;align-items:center;width: 240px;">
                                <p class="exe">{{ $t('GameRebound.manualRecord') }}</p>
                            </div>
                            <p class="exe exe_rtl" style="margin-left: 20px">
                               <span class="gametime">{{ $t('GameRebound.recordDuration') }}</span><span>{{item.gametime}}</span>
                            </p>
                            <img  class="wea" :src="item.wea_img" alt="" v-show="item.tem !== '' && $i18n.locale === 'CN'">
                            <p class="exe" style="display: flex;" v-show="item.wea!== undefined && item.wea!== '' && $i18n.locale === 'CN'">
                                <span class="tem">{{ item.tem }}</span><span style="margin-right: 5px;">°C</span><span style="width: 50px;display: block">{{item.wea}}</span>
                               <span class="iconfont icon-locate" style="color:#3579D5;margin-left: 20px;"></span><span>{{ ' '+item.province }}{{ ' '+item.city }}</span>
                            </p>
                            <div class="detailitem detail detailitem_rtl" @click.stop="ExportPerformanceData(index)" style="margin-left: auto;"><span class="iconfont icon-detail"></span>{{ $t('messages.export') }}</div>
                            <div class="detailitem detail" @click.stop="OpenDetailpage(index)"><span class="iconfont icon-detail"></span>{{ $t('GameRebound.details') }}</div>
                            <div class="detailitem delete" @click.stop="DetailDetail(index)"><span class="iconfont icon-remove"></span>{{ $t('InGameMonitor.Delete') }}</div>
                            <!-- <info-filled /> -->
                        </template>
                        <div class="inner">
                            <div class="inner_box" v-if="item['data'].hasOwnProperty('fps')&&item['data']['fps']['avg'] !== 0">
                                <p class="inner_title"><span>FPS</span></p>
                                <div class="box wild">
                                    <p>{{ $t('GameRebound.average') }}</p>
                                    <span>{{item['data']['fps']['avg']}}</span>
                                </div>
                                <div class="box">
                                    <p>{{ $t('GameRebound.minimum') }}</p>
                                     <span>{{item['data']['fps']['min']}}</span>
                                </div>
                                <div class="box">
                                    <p>{{ $t('GameRebound.maximum') }}</p>
                                     <span>{{item['data']['fps']['max']}}</span>
                                </div>
                                <div class="box">
                                    <p>1% low </p>
                                     <span>{{item['data']['fps1']['avg']}}</span>
                                </div>
                                <div class="box">
                                    <p>0.1% low </p>
                                     <span>{{item['data']['fps01']['avg']}}</span>
                                </div>
                            </div>
                            <div class="inner_box">
                                <p class="inner_title"><span>CPU</span><span class="hardName">{{ item['hardwareInfo']['ProcessorName'] }}</span></p>
                                <div class="box">
                                    <p>{{ $t('GameRebound.occupancyRate') }} </p>
                                    <span :style="getTemperatureStyle(item['data']['cpuload']['avg'])">{{item['data']['cpuload']['avg']}}%</span>
                                </div>
                                <div class="box">
                                    <p>{{ $t('hardwareInfo.temperature') }} </p>
                                    <span :style="getTemperatureStyle(item['data']['cputemperature']['avg'])">{{item['data']['cputemperature']['avg']}}°C</span>
                                </div>
                                <div class="box wild">
                                    <p>{{ $t('GameRebound.powerConsumption') }} </p>
                                    <span>{{item['data']['cpupower']['avg']}}W</span>
                                </div>
                                <div class="box wild">
                                    <p>{{ $t('GameRebound.voltage') }} </p>
                                    <span :style="getTemperatureStyle(Number(item['data']['cpuvoltage']['avg']))">{{item['data']['cpuvoltage']['avg']}}V</span>
                                </div>
                            </div>
                            <div class="inner_box">
                                <p class="inner_title"><span>GPU</span><span class="hardName">{{ item['hardwareInfo']['GPU'] }}</span></p>
                                <div class="box">
                                    <p>D3D </p>
                                    <span :style="getTemperatureStyle(item['data']['gpuload']['avg'])">{{item['data']['gpuload']['avg']}}%</span>
                                </div>
                                <!-- <div class="box" v-if="item['data'].hasOwnProperty('gpuload1')&&item['data']['gpuload1']['avg'] !== 0"> -->
                                    <div class="box">
                                    <p>total </p>
                                     <span :style="getTemperatureStyle(item['data']['gpuload1']['avg'])">{{item['data']['gpuload1']['avg']}}%</span>
                                </div>
                                <div class="box wild">
                                    <p>{{ $t('hardwareInfo.temperature') }}</p>
                                    <span :style="getTemperatureStyle(item['data']['gputemperature']['avg'])">{{item['data']['gputemperature']['avg']}}°C</span>
                                </div>
                                <div class="box wild">
                                    <p>{{ $t('GameRebound.powerConsumption') }} </p>
                                    <span>{{item['data']['gpupower']['avg']}}W</span>
                                </div>
                            </div>
                            <div class="inner_box">
                                <p class="inner_title"><span>DRAM</span><span class="hardName">{{ item['hardwareInfo']['memory'] }}</span></p>
                                <div class="box wild">
                                    <p>{{ $t('hardwareInfo.occupied') }} </p>
                                     <span :style="getTemperatureStyle(item['data']['memory']['avg'])">{{item['data']['memory']['avg']}}%</span>
                                </div>
                            </div>

                        </div>

                    </el-collapse-item>
                </el-collapse>
                 </div>
            </div>
        </div>
    </div>
   </div>
  </el-config-provider>
</template>



<style lang="scss" scoped>
span{
    white-space:nowrap;
}
.GameRebound{
    border-radius: 6px;
    margin: 10px 0 0 10px;
    box-shadow: 0px 1px 6px rgba(0, 0, 0, .4);
    background-color:#22232E;
    position: relative;
    overflow: hidden;
    width: 1280px;
    height: 720px;
    user-select: none;
     .nav{
      .left{
         display: flex;
         font-size: 12px;
         display: flex;
         align-items: center;
         color: #FFFFFF;
         img{
         width: 14px;
         height: 14px;
         margin:0 13px
        }
        p{
        //  width: 120px;
        white-space: nowrap;
        }
      }
      .drag-bar{
      width: 90%;
      height: 20px;
      -webkit-app-region: drag;
      cursor: pointer;
    }
      width: 100%;
      height: 40px;
      // border: 1px solid #FFFFFF;
      background-color:#2D2E39;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .setting{
        display: flex;
        .dataitem{
          width: 40px;
          height: 40px;
          text-align: center;
          line-height: 40px;
          cursor: pointer;
          color: #3579D5;
          border-radius: 2px;
          .iconfont {
            font-size: 14px;
          }

          &:hover{
            background: #22232e;
          }
          &:last-child {
            margin-right: 10px;
          }
        }
      }
    }
  .container{
    overflow: hidden;
    display: flex;
    flex-direction: column;
    // .tab{
    //     display: flex;
    //     color: #FFFFFF;
    //     .item{
    //         width: 90px;
    //         height: 40px;
    //         background-color: rgba(62, 64, 80, 1);
    //         border-radius: 4px;
    //         margin:20px 0 0 20px;
    //         display: flex;
    //         align-items: center;
    //         justify-content: center;
    //         box-sizing: border-box;
    //         font-size: 12px;
    //     }
    //     .itemchoosen{
    //         border: 2px solid rgba(53, 121, 213, 1);
    //     }
    // }

    .content{
        width:1240px;
        height: 645px;
        border-radius: 4px;
        background-color:rgba(45, 46, 57, 1);
        margin: 10px 0 0 20px;
        .dataTable{
            margin: 20px 0 0 20px;
            width: 100%;
            display: flex;
            align-items: center;
        }
        .chooseTime{
            display: flex;
            align-items: center;
            position: relative;
            .line{
                    position: absolute;
                    width: 1060px;
                    height: 10px;
                    background-color:rgba(62, 64, 80, 1);
                    border-radius: 3px;
                    margin-left: 10px
                }
            .item{
                z-index: 2;
                // position: absolute;
                width: 100px;
                height: 30px;
                display: flex;
                align-items: center;
                position: relative;
                justify-content: center;
                margin-left: 100px;
                font-size: 12px;
                color:rgba(119, 119, 119, 1);
                cursor: pointer;
                // border: 1px solid pink;
                .dot{
                    width: 20px;
                    height: 20px;
                    background-color:rgba(62, 64, 80, 1);
                    border-radius: 50%;
                    position: relative;
                    z-index: 3;

                }
                .linearDiv{
                  position: absolute;
                  width: 100px;
                  height: 10px;
                  background: radial-gradient(circle at center, rgba(53, 121, 213, 1) 0%, rgba(53, 121, 213, 1) 40%, rgba(0, 0, 0, 0) 100%);
                }
                .linearDiv2{
                  position: absolute;
                  display: none;
                  width: 100px;
                  height: 10px;
                  background: radial-gradient(circle at center, rgba(53, 121, 213, 1) 0%, rgba(53, 121, 213, 1) 40%, rgba(0, 0, 0, 0) 100%);
                }
               .active{
                background-color:rgba(53, 121, 213, 1);
               }
               p{
                position: absolute;
               }
            }
            .item:hover{
                p{
                color: #FFFFFF;
                }
                .dot{
                    background-color:rgba(53, 121, 213, 1);
                }
                .linearDiv2{
                    display: block
                }
            }

        }

        .electronic
        {
            margin: 25px 0 0 20px;
            font-size: 12px;
            color: #ffffff;
            display: flex;
            .elecnum{
               font-weight:bold;
            //    margin:0 5px
            }
        }
        .detailBox
        {
            width: 1200px;
            height: 530px;
            // border: 1px solid #FFFFFF;
            margin: 10px 0 0 20px;
            font-size: 12px;
            overflow: auto;
            .item{
                margin: 10px 5px 0 0
            }
            .inner{
                margin-left: 10px
            }
            .time{
                text-align: left;
                margin-left: 20px;
                margin-right: 20px;
                //width: 75px;
            }
            .gameicon{
                width: 20px;
                height: 20px;
                margin: 0 5px 0 0
            }
            .exe{
                //width: 152px;
                text-align: left;
                white-space: nowrap; /* 防止文本换行 */
                overflow: hidden; /* 隐藏超出部分的文本 */
                text-overflow: ellipsis; /* 当文本溢出时显示省略号 */
            }
            .gametime{
                color:#777777;
                margin-left:10px
            }
            .wea{
                width: 20px;
                height: 16px;
                margin: 0 10px 0 10px
            }
            .tem{
                margin: 0 0 0 5px;
            }
            .detail{
                    color:#3579D5 !important;
                }
            .delete{
                    color:#BF4040;
                }
            .detailitem{
                display: flex;
                align-items: center;
                justify-content: center;
                // width: 60px;
                min-width: 60px;
                height: 30px;
                background-color: #343647;
                border-radius: 4px;
                .icon-detail{
                    color:#3579D5 !important;
                }
                .icon-remove{
                    color:#BF4040;
                }
            }
            .detailitem:hover{
                background-color:#3E4050;
            }

            .inner{
                width: 1170px;
                height: 250px;
                margin-left: 20px;
                display: flex;
                flex-wrap: wrap;
                .inner_box{
                    width: 575px;
                    height: 120px;
                    background-color: #22232E;
                    border-radius: 5px;
                    margin-right: 10px;
                    display: flex;
                    position: relative;
                }
                .hardName{
                    width: 400px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    text-align: right;
                }
                .inner_title{
                    position: absolute;
                    color: #FFFFFF;
                    // top:-10px;
                    left: 10px;
                    width: 95%;
                    display: flex;
                    justify-content: space-between;
                    margin-top: 5px;
                }
                .box{
                    width:83px;
                    height: 80px;
                    background-color:#323444;
                    border-radius: 4px;
                    margin-left: 10px;
                    margin-top: 30px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    p{
                            color:#777777;
                            margin-top:5px;
                            margin-bottom: 10px
                     }
                     span{
                        font-size: 18px;
                        font-weight: bold;
                        color:#3579D5
                     }

                }
                .wild{
                    width:176px;
                    height: 80px
                }
            }
        }
    }
  }
}

  .scroll::-webkit-scrollbar {
    width: 5px;
    transition: 0.25s;
}

.scroll::-webkit-scrollbar-thumb {
    background: #71738C;
    border-radius: 3px;
}

.scroll::-webkit-scrollbar-thumb:hover {
    background-color: #71738C;
}
.scroll::-webkit-scrollbar-track {
    background-color: #22232E;
    width: 2px;
}

.cell {
  height: 30px;
  padding: 3px 0;
  box-sizing: border-box;
}
.cell .text {
  width: 24px;
  height: 24px;
  display: block;
  margin: 0 auto;
  line-height: 24px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 50%;
}
.cell.current .text {
  background: #626aef;
  color: #fff;
}
.cell .holiday {
  position: absolute;
  width: 6px;
  height: 6px;
  background: #67C23A;
  border-radius: 50%;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
}
</style>
<style lang="scss">
.el-collapse-item__arrow{
    margin: 0 8px 0 8px;
}
.GameRebound{
    .el-input__inner {
        color: #FFFFFF;
    }

    .el-date-editor.el-input, .el-date-editor.el-input__wrapper{
        height: 40px;
        width: 120px !important
    }
    .el-input__suffix-inner{
        display: none !important;
    }
    .el-input__wrapper{
        background-color:rgba(52, 54, 71, 1);
        box-shadow: none;
    }
    .dataTable{
        .el-icon{
        font-size: 20px;
        color: rgba(53, 121, 213, 1)
    }
    }
    .el-collapse{
        border: none;
    }
    .el-collapse-item__header{
        border: none;
        background-color:#343647;
        color:#FFFFFF;
        width: 100%;
        border-radius: 5px
    }
    .el-collapse-item__wrap{
        background-color:#343647;
        border-bottom-left-radius: 5px;
        border-bottom-right-radius: 5px;
        border-bottom: 1px solid #343647;
    }
    .is-active{
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
        border-bottom-right-radius: 0px;
        border-bottom-left-radius: 0px;
    }
}

</style>
