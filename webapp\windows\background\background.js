// "strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
// @ts-ignore
var gamepp = window.gamepp;
// import gameclient from './js/game_client'
var WindowName = {
    BACKGROUND: 'background',
    DESKTOP: 'desktop',
    UPDATE: 'update',
    TRAYMENU: 'traymenu',
    Desktop_Monitor: 'desktop_monitor',
    IN_GAME_MAIN: "ingame_main",
    IN_GAME_MONITOR: "ingame_monitor",
    IN_GAME_TipsPage: "ingame_tipspage",
    IN_GAME_Quit_Game_Tips: "ingame_quit_game_tips",
};
//模块安装状态
var moduleInstalled = {
    "rebound": false, //游戏退弹
    "mirror": false //游戏滤镜
};
var ProcessAcess = {
    GPP_MONITORY_ACCESS: 1, //游戏内监控位移
    GPP_RESHADE_ACCESS: 2, //游戏滤镜位移
    GPP_VIDEO_ACCESS: 4, //录像位移
    GPP_PHOTO_ACCESS: 8, //截图位移
    GPP_DIRECTOR_ACCESS: 16, //导演模式 位移
    GPP_NOTES_ACCESS: 32, //笔记位移
    GPP_BARRAGE_ACCESS: 64, //弹幕位移
    GPP_TARKOV_PRICE_ACCESS: 128, //塔科夫价格位移(已取消功能)
    GPP_TARKOV_DELAY_ACCESS: 256, //塔科夫延迟位移(已取消功能)
    GPP_TFT_ACCESS: 512, //云顶工具位移
};
var COMMANDID = {
    CM_ERROR: 0,
    CM_UNDEFINE: 1,
    CM_ENABLE_EFFECT: 2, //  画质开关(boolean)
    CM_ENABLE_HW_MONITING: 3, //  硬件信息显示开关(boolean)
    CM_SHOW_MAINUI: 4, //  以指定方式显示主程序(桌面上唤出窗体)
    CM_SET_SCREENPATH: 5, //  设置截图路径
    CM_SET_VIDEOPATH: 6, //  设置录像路径
    CM_SET_GENERALPATH: 7, //  设置通用存储路径
    CM_HOTKEY_SHOW_MAINUI: 8, //  设置游戏内显示主程序的快捷键
    CM_HOTKEY_SHOW_MAINUI_INFO: 9, //  设置游戏内显示主程序的快捷键文字描述(Ctrl+F1)
    CM_HOTKEY_SWITCH_EFFECT_RESOLUTION: 10, //  设置游戏内切换画质优化方案的快捷键(Ctrl+F3)
    CM_HOTKEY_SWITCH_EFFECT_RESOLUTION_INFO: 11, //  设置游戏内切换画质优化方案的快捷键值
    CM_HOTKEY_PRESENT_EFFECT_SWITCH: 12, //  设置开关画质优化效果的快捷键(Ctrl+F5)
    CM_HOTKEY_PRESENT_EFFECT_SWITCH_INFO: 13, //  设置开关画质优化效果的快捷键值
    CM_HOTKEY_HARDWARE_MONITING_SWITCH: 14, //  设置开关游戏内数据显示的快捷键(F10)
    CM_HOTKEY_HARDWARE_MONITING_SWITCH_INFO: 15, //  设置开关游戏内数据显示的快捷键值
    CM_HOTKEY_RANKS_QUERY_SWITCH: 16, //  设置开关游戏内战绩查询的快捷键(Ctrl+L)
    CM_HOTKEY_RANKS_QUERY_SWITCH_INFO: 17, //  设置开关游戏内战绩查询的快捷键值
    CM_HWM_RUNTIME_SWITCH: 18, //  运行时间
    CM_HWM_CURRENT_TIME_SWITCH: 19, //  当前时间
    CM_HWM_DOWNLOAD_SPEED_SWITCH: 20, //  下载速度
    CM_HWM_UPLOAD_SPEED_SWITCH: 21, //  上传速度
    CM_HWM_NETWORK_PING_SWITCH: 22, //  网络延迟信息
    CM_HWM_GPU_USAGE_SWITCH: 23, //  显卡占用
    CM_HWM_GPU_TEMP_SWITCH: 24, //  显卡温度
    CM_HWM_GPU_FREQUENCY_SWITCH: 25, //  显卡频率
    CM_HWM_GPU_FAN_SPEED_SWITCH: 26, //  显卡风扇转速
    CM_HWM_GPU_TYPE_INFO: 27, //  显卡名称
    CM_HWM_GPU_DRAW_COUNT: 28, //  显卡渲染数目
    CM_HWM_GPU_VENDER_ID: 29, //  显卡VenderId
    CM_HWM_VM_USAGE_SWITCH: 30, //  显存占用
    CM_HWM_VM_DISPLAY_TYPE: 31, //  显存占用的显示方式（百分比/使用/剩余）
    CM_HWM_VM_FREQUENCY_SWITCH: 32, //  显存频率
    CM_HWM_SM_USAGE_SWITCH: 33, //  系统内存占用
    CM_HWM_SM_DISPLAY_TYPE: 34, //  系统内存占用的显示方式（百分比/使用/剩余）
    CM_HWM_SM_SIZE: 35, //  内存大小
    CM_HWM_CPU_USAGE_SWITCH: 36, //  CPU占用
    CM_HWM_CPU_TEMPRATURE_SWITCH: 37, //  CPU温度
    CM_HWM_CPU_FREQUENCY_SWITCH: 38, //  CPU频率
    CM_HWM_CPU_FAN_SPEED_SWITCH: 39, //  CPU风扇转速
    CM_HWM_CPU_TYPE_INFO: 40, //  CPU名称
    CM_VC_VIDEO_SWITCH: 41, //  是否启用录像的大开关
    CM_VC_NORMAL_MODE_SWITCH: 42, //  是否使用正常录制的功能开关
    CM_VC_NORMAL_HOTKEY: 43, //  正常录制的快捷键开按键值
    CM_VC_NORMAL_HOTKEY_INFO: 44, //  正常录制的快捷键名称
    CM_VC_STREAMING_MODE_SWITCH: 45, //  回溯录制的功能开关， 如果该功能关闭， 那么回溯录制的任何操作将不可用， 包括快捷键
    CM_VC_STREAMING_HOTKEY: 46, //  回溯录制的快捷键开关按键值
    CM_VC_STREAMING_HOTKEY_INFO: 47, //  回溯录制的快捷键开关显示名称
    CM_VC_STREAMING_EX_MODE_SWITCH: 48, //  增强版回溯录制---》 高能录制模式功能开关
    CM_VC_STREAMING_EX_MODE_STRONG_SWITCH: 49, //  高能录制模式下（牛逼时刻）
    CM_VC_STREAMING_EX_MODE_LOSER_SWITCH: 50, //  高能录制模式下（菜鸟时刻）
    CM_VR_VIDEO_RELIVE_SWITCH: 51, //  AMD relive
    CM_VR_VIDEO_NORMAL_SWITCH: 52, //  精彩一刻
    CM_VR_VIDEO_RESOLUTION: 53, //  视频分辨率
    CM_VR_VIDEO_FPS: 54, //  视频FPS
    CM_VR_VIDEO_BITRATE: 55, //  视频码率
    CM_VR_VIDEO_STREAMING_TIME: 56, //  回溯时间段
    CM_PC_PHOTO_SWITCH: 57, //  是否启用截图的功能开关
    CM_PC_NORMAL_MODE_SWITCH: 58, //  是否使用正常截图的功能开关
    CM_PC_NORMAL_MODE_HOTKEY: 59, //  正常截图快捷键开按键值
    CM_PC_NORMAL_MODE_HOTKEY_INFO: 60, //  正常截图快捷键名称
    CM_PC_STREAMING_EX_MODE_SWITCH: 61, //  高能截图模式功能开关呢
    CM_PC_STREAMING_EX_MODE_STRONG_SWITCH: 62, //  高能截图模式下（牛逼时刻）
    CM_PC_STREAMING_EX_MODE_LOSER_SWITCH: 63, //  高能截图模式下（菜鸟时刻）
    CM_FPS_LIMIT_CAPPED_SWITCH: 64, //  FPS 限制上限开关
    CM_FPS_LIMIT_CAPPED: 65, //  FPS 限制上限值
    CM_RI_RECORD_NAME: 66, //  战绩查询所查询的游戏名称
    CM_WS_GENERATE_SQUAD: 67, //  创建小队（包含创建和重建）
    CM_WS_JOIN_SQUAD_INITIATIVE: 68, //  加入小队(主动加入)
    CM_WS_LEAVE_SQUAD_INITIATIVE: 69, //  离开小队(主动离开)
    CM_WS_JOIN_SQUAD_NOTIFY: 70, //  加入小队(通知有人加入)
    CM_WS_LEAVE_SQUAD_NOTIFY: 71, //  离开小队(通知有人离开)
    CM_HWM_FS_CURRENT_TYPE: 72, //  当前硬件显示信息的字体大小(0,1,2,,,,)
    CM_RESHADE_TYPE: 73, //  画质方案选项（0,1,2,,,）
    CM_KS_WIN: 74, //  屏蔽win
    CM_KS_SHIFT: 75, //  屏蔽shift
    CM_KS_CTRL_SPACE: 76, //  屏蔽ctrl+space
    CM_KS_CTRL_SHFIT: 77, //  屏蔽ctrl+shift
    CM_QUIT: 78, //  退出命令
    CM_WS_SEND_PICTURE_HOTKEY: 79, //  发送图片热键值
    CM_WS_SEND_PICTURE_HOTKEY_INFO: 80, //  发送图片热键文本
    CM_WS_FIRST_PLAYER_OPEN_PICTURE_HOTKEY: 81, //  1号位置查看/关闭图片热键值
    CM_WS_FIRST_PLAYER_OPEN_PICTURE_HOTKEY_INFO: 82, //  1号位置查看/关闭图片热键文本
    CM_WS_SECOND_PLAYER_OPEN_PICTURE_HOTKEY: 83, //  2号位置查看/关闭图片热键值
    CM_WS_SECON_PLAYER_OPEN_PICTURE_HOTKEY_INFO: 84, //  2号位置查看/关闭图片热键文本
    CM_WS_THIRD_PLAYER_OPEN_PICTURE_HOTKEY: 85, //  3号位置查看/关闭图片热键值
    CM_WS_THIRD_PLAYER_OPEN_PICTURE_HOTKEY_INFO: 86, //  3号位置查看/关闭图片热键文本
    CM_WS_FOURTH_PLAYER_OPEN_PICTURE_HOTKEY: 87, //  4号位置查看/关闭图片热键值
    CM_WS_FOURTH_PLAYER_OPEN_PICTURE_HOTKEY_INFO: 88, //  4号位置查看/关闭图片热键文本
    CM_WS_SHOW_PICTURE_TIME: 89, //  图片展示时长
    CM_WS_AUTO_SHOW_PICTURE: 90, //  接收到图片自动展示
    CM_WS_PLAYER_NAME: 91, //  勇士系统里的用户名
    CM_WS_PLAYER_TOKEN: 92, //  勇士系统的唯一标识
    CM_WS_SCREEN_AZIMUTH: 93, //  勇士系统发送图片是否包含方位角
    CM_WS_SWITCH_USER_PICTURE_HOTKEY: 94, //  切换显示下一张图片热键
    CM_WS_SWITCH_USER_PICTURE_HOTKEY_INFO: 95, //  切换显示下一张图片热键文本
    CM_RESHADE_SELECTED: 96, //  是否选择了加载Reshade方案
    CM_RESHADE_SELECTED_INFO: 97, //  选择的reshade文件名
    CM_SET_SELF_RESHADE_FILEPATH: 98, //  设置需要加载的reshade配置文件路径
    CM_SET_PUSHMESSAGE: 99, //  设置推送消息状态
    CM_HOTKEY_TIPS: 100, //  热键提示标志
    CM_USER_BIGJUMP: 101, //  大跳标志
    CM_SEND_PICTURE_HOTKEY_SPACTIME: 102, //  发送图片热键按键间隔
    CM_MAPSFRAME_HOTKEY: 103, //  唤出地图显示热键值
    CM_MAPSFRAME_HOTKEY_INFO: 104, //  唤出地图显示热键文本
    CM_VIDEO_CAPTURE_MOUSE: 105, //  录像捕获鼠标
    CM_VIDEO_CAPTURE_MICROPHONE: 106, //  录像捕获麦克风
    CM_ISSET_LOBBY_LANGUGE_CHINESE: 107, //  是否设置游戏大厅为中文
    CM_SHARE_MATCHEND_PICTURE: 108, //  是否开启分享游戏结束时的图像
    CM_CAPTURE_VOICE_SWITCH: 109, //  是否开启语音
    CM_CAPTURE_VOICE_HOTKEY: 110, //  语音热键
    CM_CAPTURE_VOICE_HOTKEY_INFO: 111, //  语音热键文本
    CM_FLIGHT_PATH_SWITCH: 112, //  飞行航行路线（是否显示航线的开关）
    CM_FLIGHT_FRAME_SWITCH: 113, //  飞行航行图的开关(整个功能的服务器开关)
    CM_FLIGHT_PATH_HOTKEY: 114, //  飞行航行路线热键
    CM_FLIGHT_PATH_HOTKEY_INFO: 115, //  飞行航行路线热键文本
    CM_SHARE_MATCHEND_PICTURE_SWITCH: 116, //  是否开启分享功能开关
    CM_SHARE_MATCHEND_PICTURE_URL: 117, //  分享上传路径
    CM_FLIGHT_FRAME_CHECK: 118, //  飞行航行图 选中与否(整个功能的界面开关)
    CM_MAP_FRAME_SWITCH: 119, //  资源点地图功能开关
    CM_MODULE_RUN_SWITCH: 120, //  硬件显示开关(网络开关)
    CM_IMGAGE_OPTI_SWITCH: 121, //  画质优化开关(网络开关)
    CM_XUNFEI_APPID: 122, //  讯飞语翻ID
    CM_HWM_FS_CURRENT_COLOR: 123, //  硬件信息当前字体颜色
    CM_HWM_FS_CURRENT_CATEGORY: 124, //  硬件信息当前字体类别（宋体、 雅黑）
    CM_HWM_CPU_USAGE_DETAILED_SWITCH: 125, //  CPU占用每个核心详细信息
    CM_HWM_CPU_FREQUENCY_DETAILED_SWITCH: 126, //  CPU频率每个核心详细信息
    CM_HWM_CPU_TEMPRATURE_DETAILED_SWITCH: 127, //  CPU温度每个核心详细信息
    CM_HWM_VER_POS_X: 128, //  纵向显示硬件信息窗体的X坐标
    CM_HWM_VER_POS_Y: 129, //  纵向显示硬件信息窗体的Y坐标
    CM_HWM_HOR_POS_X: 130, //  横向显示硬件信息窗体的X坐标
    CM_HWM_HOR_POS_Y: 131, //  横向显示硬件信息窗体的Y坐标
    CM_CUT_MOUSE_SPEED_SWITCH: 132, //  减小鼠标速度状态开关
    CM_CUT_MOUSE_SPEED_HOTKEY: 133, //  减小鼠标速度热键
    CM_CUT_MOUSE_SPEED_HOTKEY_INFO: 134, //  减小鼠标速度热键文本
    CM_ADD_MOUSE_SPEED_SWITCH: 135, //  增加鼠标速度状态
    CM_ADD_MOUSE_SPEED_HOTKEY: 136, //  增加鼠标速度热键
    CM_ADD_MOUSE_SPEED_HOTKEY_INFO: 137, //  增加鼠标速度热键文本
    CM_MUTE_SWITCH: 138, //  静音开关
    CM_MUTE_HOTKEY: 139, //  静音开关热键
    CM_MUTE_HTOKEY_INFO: 140, //  静音开关热键文本
    CM_CUT_VOLUME_SWITCH: 141, //  减小音量开关
    CM_CUT_VOLUME_HOTKEY: 142, //  减小音量开关热键
    CM_CUT_VOLUME_HOTKEY_INFO: 143, //  减小音量开关热键文本
    CM_ADD_VOLUME_SWITCH: 144, //  增加音量开关
    CM_ADD_VOLUME_HOTKEY: 145, //  增加音量开关热键
    CM_ADD_VOLUME_HOTKEY_INFO: 146, //  增加音量开关热键文本
    CM_HOTKEY_SHOW_RESHDE_OPTI: 147, //  设置游戏内显示画质优化的快捷键
    CM_HOTKEY_SHOW_RESHDE_OPTI_INFO: 148, //  设置游戏内显示画质优化的快捷键文字描述(Ctrl+F1)
    CM_PC_INCLUDE_GAMEUI: 149, //  截图时保存产品的信息
    CM_SCREENHOT_AUTO_SWITCH: 150, //  自动保存截图开关
    CM_SCREENHOT_AUTO_INTERVAL_TIME: 151, //  自动保存截图间隔时间
    CM_SAVE_IP_SWITCH: 152, //  保存IP 到本地文件中
    CM_SHOW_SOFT_VERSION_SWITCH: 153, //  显示本软件的版本号
    CM_SHOW_SOFT_VERSION_INFO: 154, //  显示软件的版本号文本
    CM_NOT_SHOW_SCREEN_SUCCESS_SWITCH: 155, //  不显示截图保存路径信息
    CM_NOT_SHOW_RELEASE_MEMORY_SWITCH: 156, //  不显示自动释放内存信息
    CM_AUTO_RELEASE_MEMORY_SWITCH: 157, //  自动释放内存
    CM_AUTO_RELEASE_MEMORY_TIME: 158, //  自动释放内存间隔时间
    CM_TELL_HOUR_TIME_SWITCH: 159, //  整点报时
    CM_TELL_ZERO_TIME_SWITCH: 160, //  凌晨提醒
    CM_SET_BKIMAGE_FRAME_SWITCH: 161, //  带边框背景
    CM_USE_SHOW_HOR_INFO: 162, //  使用横向信息显示(0:竖向， 1：横向 ，2：竖向2)
    CM_VIDEO_INCLUDE_GAMEUI: 163, //  录像保留信息
    CM_VR_VIDEO_BITRATE_INDEX: 164, //  视频码率选择项
    CM_VR_VIDEO_STREAMING_TIME_INDEX: 165, //  回溯时间段选择项
    CM_VR_VIDEO_PLAN_INDEX: 166, //  视频录制选择方案
    CM_RUN_WITH_WINDOWS_START: 167, //  随windows开机启动
    CM_CHECK_UPDATE_AUTO: 168, //  自动检查更新
    CM_TRAY_ICON_HIDE: 169, //  隐藏托盘图标
    CM_VIDEO_COMPATIBLE_MODEL: 170, //  录像使用兼容模式
    CM_PC_FILE_FORMAT_INDEX: 171, //  截图文件保存格式（1：bmp,2:png 3:jpg）
    CM_SOFT_RUN_TIME: 172, //  软件运行时长
    CM_USE_DESKTOP_FRAME: 173, //  使用 桌面显示
    CM_PLAY_IDENTITY_VOICE_SWITCH: 174, //  播放语音开关
    CM_PLAY_IDENTITY_VOICE_HOTKEY: 175, //  播放语音热键值
    CM_PLAY_IDENTITY_VOICE_HOTKEY_INFO: 176, //  播放语音热键值文本
    CM_HWM_FPS_SWITCH: 177, //  FPS显示
    CM_USE_VISION_SWITCH: 178, //  使用勇士系统的标识
    CM_RECORD_DATA_SWITCH: 179, //  战报显示开关
    CM_RECORD_DATA_HOTKEY: 180, //  战报显示热键
    CM_RECORD_DATA_HOTKEY_INFO: 181, //  战报显示热键文本
    CM_LAN_SOCKET_QRCODE: 182, //  局域网socket链接二维码
    CM_HTML_RESOUCE_TEAM_MD5: 183, //  HTML Team本地资源包MD5
    CM_HTML_RESOUCE_RECORD_MD5: 184, //  HTML Record本地资源包MD5
    CM_AUDIO_SERVICE_SWITCH: 185, //  音频服务开关
    CM_PUBG_TEAM_SYSTEM_HOTKEY: 186, //  pubg++组队显示或关闭热键
    CM_PUBG_TEAM_SYSTEM_HOTKEY_INFO: 187, //  pubg++组队显示或关闭热键文本
    CM_STOP_SPECIFY_VOICE_HOTKEY: 188, //  停止正在播放的语音热键
    CM_STOP_SPECIFY_VOICE_HOTKEY_INFO: 189, //  停止正在播放的语音热键文本
    CM_PC_COMPRESSION_RATIO: 190, //  可视化报点保存图片的压缩比例
    CM_SWITCH_INPUT_METHOD: 191, //  游戏时自动切换为英文输入法
    CM_HWM_VOLUME_VALUE: 192, //  扬声器音量值
    CM_HWM_MICROPHONE_VALUE: 193, //  麦克风音量值
    CM_HWM_MOUSE_SPEED_VALUE: 194, //  鼠标速度值
    CM_HTML_VOICELIST_REFRESH: 195, //  语音列表HTML刷新命令
    CM_IQ_ENHANCED_DARK_VALUE: 196, //  暗部细节增强 值
    CM_IQ_ENHANCED_DARK_SWITCH: 197, //  暗部细节增强开关（针对游戏生效）
    CM_IQ_DIGITAL_VIBRATION_VALUE: 198, //  数字振动 数值
    CM_IQ_VERTREFRESH_SWITCH: 199, //  设置刷新率为144的开关
    CM_HWM_CPU_POWERS_SWITCH: 200, //  CPU功耗开关
    CM_HWM_INTERVAL_TIME: 201, //  获取硬件信息的间隔时间
    CM_HWM_MEMORY_FREQUENCY_SWITCH: 202, //  内存频率开关
    CM_IQ_VERTREFRESH_VALUE: 203, //  当前刷新率值
    CM_HTML_SETTING_REFRESH: 204, //  设置HTML刷新
    CM_RECORD_DATA_REFRESH: 205, //  战报更新消息
    CM_IQ_ENHANCED_DARK_ALL_SWITCH: 206, //  暗部细节增强开关
    CM_IQ_DIGITAL_VIBRATION_SWITCH: 207, //  数字振动 开关
    CM_IQ_VERTREFRESH_ALL_SWITCH: 208, //  设置刷新率 开关
    CM_IQ_DIGITAL_VIBRATION_GAME_SWITCH: 209, //  数字振动针对游戏有效的开关
    CM_IQ_ENHANCED_DARK_DEFAULT_VALUE: 210, //  暗部细节增强默认值
    CM_IQ_DIGITAL_VIBRATION_DEFAULT_VALUE: 211, //  数字振动默认值
    CM_PUBG_USE_MAKE_TEAM_SYSTEM: 212, //  是否使用组队系统标志
    CM_IQ_ENHANCED_DARK_SETTING_DONE: 213, //  暗部细节增强 值更改完成标志
    CM_IQ_DIGITAL_VIBRATION_SETTTING_DONE: 214, //  数字振动 数值 更改完成标志
    CM_IQ_HIGHEST_DISPLAY_FREQUENCY: 215, //  当前显示器的最大刷新率
    CM_IQ_DISPLAY_FREQUENCY_DEFAULT: 216, //  显示器的刷新率默认值
    CM_REFRESH_HARDWARE_GENERAL_SITUATION: 217, //  更新所有硬件信息（主板、CPU、显卡）
    CM_REFRESH_MATCH_DATA_DONE: 218, //  当局对战数据已写入完成
    CM_MATCH_VAILED_TIME: 219, //  每局对战的有效时长（记录标志）
    CM_HWM_POST_DATA_MD5: 220, //  上次硬件信息的md5值
    CM_LOCAL_IP_PORT_APP_CONNECT: 221, //  与APP相连接的本机 socket信息 ip 和 端口
    CM_DESK_CPU_SWITCH: 222, //  桌面显示CPU信息
    CM_DESK_GPU_SWITCH: 223, //  桌面显示GPU信息
    CM_DESK_MEMORY_SWITCH: 224, //  桌面显示内存信息
    CM_DESK_GPUMEMORY_SWITCH: 225, //  桌面显示显存信息
    CM_DESK_NETWORK_SWITCH: 226, //  桌面显示网络信息
    CM_DESK_TITLEBAR_SWITCH: 227, //  桌面显示标识
    CM_DESK_TRANSPARENT_VALUE: 228, //  桌面透明度值
    CM_SPEED_POWER_SOLUTION_SWITCH: 229, //  Power Solution
    CM_SPEED_CPU_CORES_PLAN_SWITCH: 230, //  CPU Cores Plan
    CM_SPEED_AUTOMATIC_SYSTEM_SWITCH: 231, //  Automatic Closing of System in Game
    CM_SPEDD_RELEASE_MEMORY_SWITCH: 232, //  Auto Release Memory
    CM_DESK_LOCATION_X: 233, //  桌面显示x坐标
    CM_DESK_LOCATION_Y: 234, //  桌面显示y坐标
    CM_KS_SHIFT_SPACE: 235, //  Shielding‘Shift+Space’
    CM_KS_SHIFT_ALT: 236, //  Shielding‘Shift + Alt’
    CM_KS_SHIFT_CAPSLOCK: 237, //  Shielding‘Shift + Capslock’
    CM_KS_CTRL_CAPSLOCK: 238, //  Shielding‘Ctrl+Capslock’
    CM_KS_ALT_HEN: 239, //  Shielding‘Alt + ~
    CM_KS_SWITCH_TOTAL: 240, //  热键屏蔽总开关
    CM_KS_WIN_SPACE: 241, //  Shielding‘win + space’
    CM_LANGUAGE_TYPE: 242, //  语言类别
    CM_TANKS_WOTDISABLEMULTICORESUPPORT_SWTICH: 243, //  禁用坦克世界多核心支持---硬盘温度
    CM_TANKS_WOTREDUCEOBJECTDETAILLEVEL_SWITCH: 244, //  减少对象细节级别
    CM_TANKS_WOTREDUCEDSOUNDQUALITY_SWITCH: 245, //  降低音频质量
    CM_TANKS_WOTEXHAUSTSMOKE_SWITCH: 246, //  坦克发动机排气烟雾
    CM_TANKS_WOTWRECKAGESMOKE_SWITCH: 247, //  建筑残骸破坏烟雾
    CM_TANKS_WOTSHOTSMOKEANDFLAMES_SWITCH: 248, //  开火烟雾及火焰
    CM_TANKS_WOTSHELLEXPLOSIONEFFECTS_SWITCH: 249, //  炮弹爆炸效果
    CM_TANKS_WOTTANKHITEFFECTS_SWITCH: 250, //  坦克击中效果
    CM_TANKS_WOTTANKDESTRUCTIONEFFECTS_SWITCH: 251, //  坦克破坏效果
    CM_TANKS_WOTCLOUDS_SWITCH: 252, //  天上的云朵朵
    CM_TANKS_QUALITY_SELECTION_SWITCH: 253, //  画质开关
    CM_TANKS_QUALITY_TYPE_VALUE: 254, //  方案值
    CM_TANKS_INSTALL_PATH: 255, //  坦克世界安装路径
    CM_TANKS_BACKGROUND_PATH: 256, //  坦克世界加载背景的路径
    CM_PUBG_CPU_CORES_SWITCH: 257, //  cpu cores plan
    CM_PUBG_TANK_ROLLING_SWITCH: 258, //  close tank rolling
    CM_PUBG_DEATH_PLAYBACK_SWITCH: 259, //  death playback
    CM_PUBG_WHOLE_PLAYBACK_SWITCH: 260, //  whole playback
    CM_PUBG_QUALITY_SWITCH: 261, //  画质开关
    CM_PUBG_QUALITY_PROGRAM_TYPE: 262, //  方案值
    CM_SETTING_TRAY_MODEL_SWITCH: 263, //  在托盘模式下运行
    CM_SETTING_ADVERTISING_SWITCH: 264, //  不显示广告
    CM_SETTING_UPDATE_MODEL_TYPE: 265, //  更新设置 0：启用自动更新 1：有新的更新时通知我
    CM_SETTING_EXPERIENCE_IMPROVE_SWITCH: 266, //  加入GamePP用户体验改善计划
    CM_BARRGE_COMMENT_URL: 267, //  直播链接URL
    CM_BARRGE_SHOW_HOTKEY: 268, //  唤出 锁定热键
    CM_BARRGE_SHOW_HOTKEY_INFO: 269, //  唤出 锁定热键值
    CM_BARRGE_STATE: 270, //  当前状态（show 1 /unlock 2 /lock 3 /hide 4）
    CM_BARRGE_BACKGROUND_COLOR: 271, //  背景颜色值
    CM_BARRGE_BACKGROUND_TRANSPARENT: 272, //  背景透明度
    CM_BARRGE_WINDOW_POS_X: 273, //  窗体相对显示的位置 x坐标
    CM_BARRGE_WINDOW_POS_Y: 274, //  窗体相对显示的位置 y坐标
    CM_BARRGE_WINDOW_SIZE_WIDTH: 275, //  窗体大小 宽度
    CM_BARRGE_WINDOW_SIZE_HEIGHT: 276, //  窗体大小 高度
    CM_BARRGE_OPEN: 277, //  开启弹幕功能
    CM_POWER_PLAN_TAKE_EFFECT: 278, //  电源方法是否生效
    CM_VIDEO_SHOW_WATERMARK_SWITCH: 279, //  录像显示水印
    CM_VIDEO_CAPTURE_MODEL_WINDOW: 280, //  录制窗体选择（1：游戏窗体， 2：显示器桌面）
    CM_VIDEO_CAPTURE_SRCEEN_TYPE: 281, //  录制显示器的选择（）
    CM_HWM_GPU_POWER_SWITCH: 282, //  显卡功耗开关
    CM_USER_LOGIN_CHANGE: 283, //  当前用户状态(1:普通登录 2: VIP登录，3:退出)
    CM_BARRGE_RE_CONNECT: 284, //  重新链接直播间
    CM_BARRGE_DISCONNECT: 285, //  直播间断开
    CM_VR_VIDEO_INVERT_MOUSE_CLICK: 286, //  是否启用鼠标点击效果反转
    CM_CURRENT_GAME_PROCESS_ID: 287, //  当前游戏进程id
    CM_CPU_CORE_PROGRAM_EFFECT: 288, //  通知CPU core方案是否生效
    CM_CLIPBOARD_RELEASE_SWITCH: 289, //  剪切板清除开关
    CM_HWM_REFRESH_UI_EFFECT: 290, //  界面通知更新硬件信息
    CM_BARRGE_COMMENT_STATE: 291, //  直播间链接状态
    CM_BARRGE_HOTKYE_TIPS_SWITCH: 292, //  直播间热键提示开关
    CM_HWM_FRAMETIME_SWITCH: 293, //  帧生成时间开关
    CM_APEX_QUALITY_SWITCH: 294, //  apex画质开关
    CM_APEX_QUALITY_PROGRAM_TYPE: 295, //  apex方案值
    CM_PERFORMANCE_OPEN_SWITCH: 296, //  性能分析开关
    CM_PERFORMANCE_NOTIFY_SWITCH: 297, //  性能分析通知开关
    CM_PERFORMANCE_RECORD_TIME: 298, //  性能分析记录间隔时间
    CM_PERFORMANCE_CLEAR_SWITCH: 299, //  性能分析清理开关
    CM_PERFORMANCE_CLEAR_TIME: 300, //  性能分析清理时间段
    CM_PERFORMANCE_PROCESS_QUIT: 301, //  性能分析有游戏退出
    CM_HTTP_SERVER_PORT: 302, //  http ip port信息
    CM_HTTP_PHONE_NAME: 303, //  链接到http server的设备名称
    CM_SHOW_NOTIFY_SWITH: 304, //  桌面通知显示开关
    CM_SHOW_NOTIFY_UI_SWITH: 305, //  游戏内通知显示开关
    CM_MEDIA_PATH: 306, //  媒体路径
    CM_DESK_MODEL: 307, //  桌面显示类型： 0：壁纸模式， 1：浮窗模式
    CM_DESK_MODEL_WALLPAPER: 308, //  壁纸模式下的背景图， 为空时表示使用桌面壁纸
    CM_DESK_MODEL_FLOATWINDOW: 309, //  DESK模型浮动窗口
    CM_DESK_OFF_WHEN_PLAYING: 310, //  进入游戏时，关闭桌面显示开关
    CM_DESK_FLOATWINDOW_LOCK: 311, //  浮窗锁定状态
    CM_HWM_IS_HAVE_SCAN: 312, //  是否扫描过的状态
    CM_ENABLED_DPI: 313, //  是否适配DPI
    CM_DESK_TYPE: 314, //  桌面显示格式  1：默认 2：简约 ，3:测评
    CM_TFT_OPEN: 315, //  打开TFT
    CM_TFT_ITEM_CHEATSHEET: 316, //  TFT
    CM_TFT_ROLLING_CHANCE: 317, //  TFT
    CM_TFT_TEAM_TRACKER: 318, //  TFT
    CM_TFT_LANGAUGE: 319, //  TFT
    CM_TFT_TEAM_COMP: 320, //  TFT
    CM_TFT_HOTKEY_VALUE: 321, //  TFT
    CM_TFT_HOTKEY_STRIING: 322, //  TFT
    CM_TFT_HOTKEY_SETTING_VALUE: 323, //  唤出TFT 设置界面
    CM_TFT_HOTKEY_SETTING_STRIING: 324, //  唤出TFT 设置界面 文本
    CM_TFT_EFFECTIVE: 325, //  TFT是否生效
    CM_TFT_ACTIVATION: 326, //  TFT激活
    CM_TFT_SKIN_VERSION: 327, //  TFT版本
    CM_TFT_SKIN_HASH: 328, //  TFT hash值
    CM_TERMINATION_SWITCH: 329, //  快速关闭进程热键（Alt+F4）
    CM_LOGOUT: 330, //  注销
    CM_RESTART_PROGRAM: 331, //  重开软件
    CM_TFT_UPDATE_CHANGE: 332, //  TFT更新
    CM_USER_GPUPREFERENCES_SWITCH: 333, //  AI游戏进程切换到高性能模式
    CM_VIDEO_DIRECTOR_SWITCH: 334, //  导演模式开关
    CM_VIDEO_DIRECTOR_CENTER_TAG_SWITCH: 335, //  导演模式 中心标记开关
    CM_VIDEO_DIRECTOR_CENTER_COLOR: 336, //  导演模式 中心标记颜色
    CM_VIDEO_DIRECTOR_CENTER_LINE_WIDTH: 337, //  导演模式 中心标记线宽
    CM_VIDEO_DIRECTOR_CENTER_TRANSPARENCY: 338, //  导演模式 中心标记透明度
    CM_VIDEO_DIRECTOR_CENTER_STYLE: 339, //  导演模式 中心标记样式
    CM_VIDEO_DIRECTOR_BORDER_SWITCH: 340, //  导演模式 四周标记开关
    CM_VIDEO_DIRECTOR_BORDER_TRANSPARENCY: 341, //  导演模式 四周标记透明度
    CM_VIDEO_HIGHLIGHT_PUBG_SWITCH: 342, //  HighLight PUBG 开关
    CM_VIDEO_HIGHLIGHT_TARKOV_SWITCH: 343, //  HighLight Tarkov 开关
    CM_VIDEO_HIGHLIGHT_HOTKEY_SWITCH_VALUE: 344, //  HighLight 热键开关值
    CM_VIDEO_HIGHLIGHT_HOTKEY_SWITCH_STRING: 345, //  HighLight 热键开关文本
    CM_VIDEO_HIGHLIGHT_SWITCH: 346, //  HighLight 开关
    CM_TFT_PERFORMANCE_SWTICH: 347, //  TFT关闭对局分析开关
    CM_STRESS_CPU_SWITCH: 348, //  压力测试 CPU开关
    CM_STRESS_CPU_AVX2_SWITCH: 349, //  压力测试 CPU AVX 2开关
    CM_STRESS_CPU_LEVEL: 350, //  压力测试 CPU 压力等级
    CM_STRESS_MEMORY_SWITCH: 351, //  内存开关
    CM_STRESS_MEMORY_THREAD: 352, //  内存线程数
    CM_STRESS_MEMORY_SIZE: 353, //  内存容量
    CM_STRESS_MEMORY_LEVEL: 354, //  内存压力等级
    CM_STRESS_GPU_SWITCH: 355, //  GPU开关
    CM_STRESS_GPU_SAWTOOTH: 356, //  GPU抗锯齿
    CM_STRESS_GPU_DISPLAY: 357, //  GPU分辨率
    CM_STRESS_GPU_LEVEL: 358, //  GPU压力等级
    CM_STRESS_GPU_DISPLAY_WIDTH: 359, //  GPU分辨率 宽
    CM_STRESS_GPU_DISPLAY_HEIGHT: 360, //  GPU分辨率 高
    CM_OPTIMIZE_AUTO_SWITCH: 361, //  进入游戏时自动优化开关
    CM_OPTIMIZE_HOTKEY_VALUE: 362, //  取消自动优化 热键值
    CM_OPTIMIZE_HOTKEY_TEXT: 363, //  取消自动优化 热键文本
    CM_PERFORMANCE_RECORD_HOTKEY_VALUE: 364, //  手动记录 热键值
    CM_PERFORMANCE_RECORD_HOTKEY_TEXT: 365, //  手动记录 热键文本
    CM_NOTE_SWITCH: 366, //  笔记游戏内显示开关
    CM_NOTE_CURRENT_SHOW_ID: 367, //  笔记游戏内当前显示ID
    CM_NOTE_REMVOE_ID: 368, //  删除某个笔记
    CM_NOTE_UPLOAD_SWITCH: 369, //  笔记上报开关
    CM_NOTE_SAVE_ID: 370, //  笔记保存通知
    CM_NOTE_SHOW_HOTKEY_VALUE: 371, //  笔记显示热键值
    CM_NOTE_SHOW_HOTKEY_TEXT: 372, //  笔记显示热键值文本
    CM_PC_VOICE_SWITCH: 373, //  主动截图声音开关
    CM_DESK_DEFAULT_POS: 374, //  桌面显示恢复到默认位置
    CM_OCR_DPI_VALUE: 375, //  DPI默认值
    CM_MACHENIKE_DESK_MONITOR_NOTIFY_SWITCH: 376, //  桌面监控提示开关
    CM_MACHENIKE_DESK_SKIN_NOTIFY_SWITCH: 377, //  桌面监控皮肤开关
    CM_MACHENIKE_OVERLAY_MONITOR_NOTIFY_SWITCH: 378, //  游戏内监控提示开关
    CM_MACHENIKE_BACKGROUND_RUN_GAMEPP_SWITCH: 379, //  打开游戏加加
    CM_MACHENIKE_WINDOW_START_WITH_GAMEPP_SWITCH: 380, //  游戏加加开启启动提示开关
    CM_MACHENIKE_GAMEPP_DESKTOP_LINK_SWITCH: 381, //  游戏加加桌面快捷方式生成提示开关
    CM_MACHENIKE_FIRST_NOTIFY_SWITCH: 382, //  第一次加载时，Ctrl+Tab提示开关
    CM_STRESS_ACTIVATION: 383, //  压力测试 激活
    CM_MACHENIKE_HIDE_OR_QUIT: 384, //  隐藏到托盘或直接退出 0: hide 1:quite
    CM_MACHENIKE_Quit_NOTIFY_SWITCH: 385, //  隐藏到托盘或直接退出提示开关
    CM_OCR_SHOW_HOTKEY_VALUE: 386, //  OCR图像识别热键值
    CM_OCR_SHOW_HOTKEY_TEXT: 387, //  OCR图像识别热键值文本
    CM_AI_VIEW_SHOW_HOTKEY_VALUE: 388, //  AI画质热键值
    CM_AI_VIEW_SHOW_HOTKEY_TEXT: 389, //  AI画质热键值文本
    CM_TRAKOV_SHOW_CURRENT_IP_ADDRESS: 390, //  通知显示ip等信息
    CM_TRAKOV_CHANGE_IP_ADDRESS: 391, //  通知切换ip等信息
    CM_TRAKOV_CHANGE_HOTKEY_VALUE: 392, //  通知切换ip热键值
    CM_TRAKOV_CHANGE_HOTKEY_TEXT: 393, //  通知切换ip热键文本
    CM_TRAKOV_CHANGE_HIDE_HOTKEY_VALUE: 394, //  通知隐藏ip界面热键值
    CM_TRAKOV_CHANGE_HIDE_HOTKEY_TEXT: 395, //  通知隐藏ip界面热键文本
    CM_TRAKOV_NOTIFY_IP_CHANGE_FINISH: 396, //  ip切换完成通知
    CM_TRAKOV_HIDE_CURRENT_IP_ADDRESS: 397, //  通知隐藏ip界面
    CM_TRAKOV_PRICE_SHOW_SWITCH: 398, //  塔科夫 价格查询 显示开关
    CM_TRAKOV_IP_SHOW_SWITCH: 399, //  塔科夫 IP 显示开关
    CM_VIDEO_HIGHLIGHT_LOL_SWITCH: 400, //  HighLight LOL 开关
    CM_VIDEO_HIGHLIGHT_VALORANT_SWITCH: 401, //  HighLight valorant 开关
    CM_TRAKOV_ORIGIN_COLOR_INDEX: 402, //  塔科夫 原生画质 选择
    CM_PS_SAVE_RESHADE_VIEW: 403, //  截图是否保存滤镜效果
    CM_TASKBAR_MONITOR_OPEN: 404, //  任务栏监控开关
    CM_VIDEO_CAPTURE_WITH_RESHADE_OPEN: 405, //  录制游戏内显示滤镜开关
    CM_VIDEO_CAPTURE_QUERY_OPEN: 406, //  录制启用开关
    CM_MEMORY_TEMPERATURE_OPEN: 407, //  内存温度开关
    CM_GAME_AUTO_OR_MAN_RECEVICE: 408, //  游戏自动还是手动领取
    CM_GAME_CLOUD_RECEVICE_OPEN: 409, //  游戏云端领取开关
    CM_NARAKA_GAMEPATH: 410, //  NARAKA 游戏路径
    CM_NARAKA_ALL_VOICE_ACTION: 411, //  全角色语音方案 (经典语音)
    CM_NARAKA_KURUMI_VOICE_ACTION: 412, //  胡桃语音方案
    CM_NARAKA_MATARI_VOICE_ACTION: 413, //  迦南语音方案
    CM_NARAKA_TARKA_VOICE_ACTION: 414, //  季沧海语音方案
    CM_NARAKA_TEMULCH_VOICE_ACTION: 415, //  特木尔语音方案
    CM_NARAKA_TIANHAI_VOICE_ACTION: 416, //  天海语音方案
    CM_NARAKA_VIPER_VOICE_ACTION: 417, //  宁红夜语音方案
    CM_NARAKA_YOTOHIME_VOICE_ACTION: 418, //  妖刀姬语音方案
    CM_NARAKA_VALDA_VOICE_ACTION: 419, //  崔三娘语音方案
    CM_NARAKA_VOICE_PLAN: 420, //  语言方案选择（经典，守望先锋，王者荣耀方案）
    CM_NARAKA_KURUMI_OVERWATCH_VOICE_ACTION: 421, //  胡桃守望先锋语音方案
    CM_NARAKA_MATARI_OVERWATCH_VOICE_ACTION: 422, //  迦南守望先锋语音方案
    CM_NARAKA_TARKA_OVERWATCH_VOICE_ACTION: 423, //  季沧海守望先锋语音方案
    CM_NARAKA_TEMULCH_OVERWATCH_VOICE_ACTION: 424, //  特木尔守望先锋语音方案
    CM_NARAKA_TIANHAI_OVERWATCH_VOICE_ACTION: 425, //  天海守望先锋语音方案
    CM_NARAKA_VIPER_OVERWATCH_VOICE_ACTION: 426, //  宁红夜守望先锋语音方案
    CM_NARAKA_YOTOHIME_OVERWATCH_VOICE_ACTION: 427, //  妖刀姬守望先锋语音方案
    CM_NARAKA_VALDA_OVERWATCH_VOICE_ACTION: 428, //  崔三娘守望先锋语音方案
    CM_NARAKA_ALL_OVERWATCH_VOICE_ACTION: 429, //  全部英雄守望先锋语音方案
    CM_NARAKA_MATARI_KINGHONOUR_VOICE_ACTION: 430, //  迦南王者荣耀语音方案
    CM_NARAKA_TARKA_KINGHONOUR_VOICE_ACTION: 431, //  季沧海王者荣耀语音方案
    CM_NARAKA_TEMULCH_KINGHONOUR_VOICE_ACTION: 432, //  特木尔王者荣耀语音方案
    CM_NARAKA_TIANHAI_KINGHONOUR_VOICE_ACTION: 433, //  天海王者荣耀语音方案
    CM_NARAKA_VIPER_KINGHONOUR_VOICE_ACTION: 434, //  宁红夜王者荣耀语音方案
    CM_NARAKA_YOTOHIME_KINGHONOUR_VOICE_ACTION: 435, //  妖刀姬王者荣耀语音方案
    CM_NARAKA_VALDA_KINGHONOUR_VOICE_ACTION: 436, //  崔三娘王者荣耀语音方案
    CM_NARAKA_KURUMI_KINGHONOUR_VOICE_ACTION: 437, //  胡桃王者荣耀语音方案
    CM_NARAKA_ALL_KINGHONOUR_VOICE_ACTION: 438, //  全部英雄王者荣耀语音方案
    CM_HWM_CPU_EFFICIENT_CORE_SWITCH: 439, //  CPU节能核心占用
    CM_HWM_GPU_TOTAL_SWITCH: 440, //  GPU TOTAL 占用率显示
    CM_DESPTOP_MONITOR_ONOFF: 441, //  监控背景
    CM_DESPTOP_LOW_DISPLAY_ONOFF: 442, //  low 显示
    CM_DESPTOP_MEMUSE_DISPLAY_ONOFF: 443, //  内存使用量显示
    CM_DESPTOP_VOLTAGE_DISPLAY_ONOFF: 444, //  电压显示
    CM_DESPTOP_MUTLECORE_DISPLAY_ONOFF: 445, //  多核数据显示
    CM_DESPTOP_MONITOR_FAMILY_WINDOW: 446, //  桌面监控是否有父窗口
    CM_NARAKA_YUESHAN_VOICE_ACTION: 447, //  岳山经典语音方案
    CM_NARAKA_YUESHAN_OVERWATCH_VOICE_ACTION: 448, //  岳山守望先锋语音方案
    CM_NARAKA_YUESHAN_KINGHONOUR_VOICE_ACTION: 449, //  岳山王者荣耀语音方案
    CM_DATA_REFRESH_TIME_SETTING: 450, //  数据刷新时间
    CM_FPS_ONOFF_HOTKEY: 451, //  启用关闭热键
    CM_FPS_FRAME_CAPTURE: 452, //  画面捕获模式
    CM_FPS_FPS_LIMIT: 453, //  帧数限制
    CM_FPS_RUNNING_DISPLAYCARD: 454, //  运行游戏使用的显卡
    CM_FPS_FRAME_DELAY: 455, //  提高画面延迟提升性能
    CM_FPS_WINDOWS_SCREEN_STOP: 456, //  禁用窗口大小设置
    CM_FPS_WINDOWS_UWP_HIDEN: 457, //  隐藏UWP窗口
    CM_FPS_CURSOR_HIDEN: 458, //  隐藏光标
    CM_FPS_CURSOR_SPEED: 459, //  缩放时调整光标速度
    CM_FPS_CURSOR_SANDGAME_LIMIT: 460, //  在3D游戏中限制光标
    CM_FPS_CURSOR_SIZE: 461, //  光标大小
    CM_FPS_DIFF_ALGO: 462, //  差值算法
    CM_FPS_DIRECFILP_STOP: 463, //  禁用DirecFilp
    CM_FPS_SIMU_MONOPOLY_FULLSCR: 464, //  模拟独占全屏
    CM_FPS_DX_COM_MODE: 465, //  0 FSR; 1 FSRDX10 显卡兼容模式
    CM_FRE_REDUCE_RANGE: 466, //  频率降低幅度
    CM_FRE_REDUCE_PRECENT: 467, //  百分比
    CM_MAN_RECORD_ONOFF_HOTKEY: 468, //  手动记录性能统计 启用关闭热键
    CM_NARAKA_JUSTINAGU_VOICE_ACTION: 469, //  顾清寒经典语音方案
    CM_NARAKA_JUSTINAGU_OVERWATCH_VOICE_ACTION: 470, //  顾清寒守望先锋语音方案
    CM_NARAKA_JUSTINAGU_KINGHONOUR_VOICE_ACTION: 471, //  顾清寒王者荣耀语音方案
    CM_NARAKA_WUCHEN_VOICE_ACTION: 472, //  无尘经典语音方案
    CM_NARAKA_WUCHEN_OVERWATCH_VOICE_ACTION: 473, //  无尘守望先锋语音方案
    CM_NARAKA_WUCHEN_KINGHONOUR_VOICE_ACTION: 474, //  无尘王者荣耀语音方案
    CM_GPPSHOW_CPU_VOLTAGE: 475, //  CPU电压
    CM_GPPSHOW_GPU_MEMTMP: 476, //  GPU显存温度
    CM_GPPSHOW_GPU_MEMFRE: 477, //  GPU显存频率
    CM_GPPSHOW_GPU_COREHOTTMP: 478, //  GPU核心热点温度
    CM_GPP_SETTING_ERROR_SHUTDOWN_REPORT: 479, //	异常关机报告开关
    CM_LOW_0_DEL_1_PERCENT: 480, //	Low 0.1%   int 默认 0
    CM_GPUPERFORMANCESETTING_GPU_FAN_LOW_TMP: 481, //	显卡风扇最低温度控制
    CM_GPUPERFORMANCESETTING_GPU_FAN_AUTO: 482, //	显卡风扇自动调整开关
    CM_GPUPERFORMANCESETTING_GPU_FAN_RPM_PLAN: 483, //	显卡风扇转速推荐方案
    CM_GPUPERFORMANCESETTING_GPU_FAN_RPM: 484, //	显卡风扇转速
    CM_GPUPERFORMANCESETTING_GPU_TMP: 485, //	显卡温度->改为  主板Vcore电压
    CM_GPUPERFORMANCESETTING_GPU_MAXIMUM_POWER_CONSUMPTION_LIMIT: 486, //	最高功耗限制
    CM_GPUPERFORMANCESETTING_GPU_MAXIMUM_TMP_LIMIT: 487, //	最高温度限制
    CM_DESK_MONITOR_CPU_FAN: 488, //	桌面监控CPU转速
    CM_DESK_MONITOR_GPU_FAN: 489, //	桌面监控GPU转速
    CM_AUDIO_COMPATIBLE_MODE: 490, //	性能与音频兼容模式
    CM_DESK_MONITOR_AUTO_ADSORPTION: 491, //	游戏监控边缘吸附(支持上下屏幕边缘吸)
    CM_GPPSETTING_GPU_COREFRE: 492, //	核心频率 int 默认0
    CM_GPPSETTING_GPU_MEMFRE: 493, //	显存频率 int 默认0
    CM_GPPSETTING_GPU_PERF_SWITCH: 494, //	显卡性能开关  int类型  默认值0
    CM_GPPSETTING_OLEND_SCREEN_BURNIN_PREVENTION_SWITCH: 495, //	OLED防烧屏开关 默认值0
    CM_GPPSETTING_OLEND_SCREEN_BURNIN_PREVENTION_SWITCH_MINUTES: 496, //	OLED防烧屏开关分钟数 默认值5
    CM_GPPSETTING_BATTERY_LEVEL_DISPLAY_SWITCH: 497, //	电池电量开关 默认值0
    CM_GPPSETTING_AMD_ONLY_MOVE_GAME_TO_CCD0: 498, //  AMD专属优化-转移游戏进程至缓存堆叠核心(CCD0)
    CM_GPPSETTING_NPU_CLOCK_VALUE: 499, //  AMD NPU 频率
    CM_GPPSETTING_NPU_USAGE_VALUE: 500, //  AMD NPU 使用率
    CM_HWM_GPU_USAGE_SWITCH_1: 501, //  显卡1 占用
    CM_HWM_GPU_TEMP_SWITCH_1: 502, //  显卡1 温度
    CM_HWM_GPU_FREQUENCY_SWITCH_1: 503, //  显卡1 频率
    CM_HWM_GPU_FAN_SPEED_SWITCH_1: 504, //  显卡1 风扇转速
    CM_HWM_VM_USAGE_SWITCH_1: 505, //  显卡1 显存占用
    CM_HWM_VM_DISPLAY_TYPE_1: 506, //  显卡1 显存占用的显示方式（百分比/使用/剩余）
    CM_HWM_VM_FREQUENCY_SWITCH_1: 507, //  显卡1 显存频率
    CM_HWM_GPU_POWER_SWITCH_1: 508, //  显卡1 功耗开关
    CM_GPPSHOW_GPU_MEMTMP_1: 509, //  显卡1 显存温度
    CM_GPPSHOW_GPU_COREHOTTMP_1: 510, //  显卡1 核心热点温度
    CM_HWM_GPU_USAGE_SWITCH_2: 511, //  显卡2 占用
    CM_HWM_GPU_TEMP_SWITCH_2: 512, //  显卡2 温度
    CM_HWM_GPU_FREQUENCY_SWITCH_2: 513, //  显卡2 频率
    CM_HWM_GPU_FAN_SPEED_SWITCH_2: 514, //  显卡2 风扇转速
    CM_HWM_VM_USAGE_SWITCH_2: 515, //  显卡2 显存占用
    CM_HWM_VM_DISPLAY_TYPE_2: 516, //  显卡2 显存占用的显示方式（百分比/使用/剩余）
    CM_HWM_VM_FREQUENCY_SWITCH_2: 517, //  显卡2 显存频率
    CM_HWM_GPU_POWER_SWITCH_2: 518, //  显卡2 功耗开关
    CM_GPPSHOW_GPU_MEMTMP_2: 519, //  显卡2 显存温度
    CM_GPPSHOW_GPU_COREHOTTMP_2: 520, //  显卡2 核心热点温度
    CM_GPPSETTING_WEATHER_LOCATION: 521, //  天气和位置信息
    CM_GPPSETTING_2HAAI_AIMASTER: 522, //  2哈AIAI教练开关
    CM_GPPSETTING_CENTER_MARK_POSITION_SWITCH: 523, //  中心标记位置控制开关 默认0
    CM_GPPSETTING_MAGIC_GLASS_SRV_SWITCH: 524, //  fps++开关 默认0  关闭
    CM_SCALE_ENABLESCALE: 525, //  getEnableScale 是否允许缩放
    CM_STATISTICS_SAVE_ADDRESS: 526, //  性能统计的保存路径
};
var DataBaseId = {
    GPP5DatabaseId: null,
    GPPDownDatabaseId: null
};
var keysEventsLog = [];
var Kill_Time = null;
var mouseEventsLog = [];
var setInterval_chooseSensor_list = null, localSensorListX = '', chooseSensor_list_xStr = '', sensorInfoModifying = false;
var setInterval_starSensor_list = null;
var timerIds = new Set(); // 存储所有的定时器ID
var sparkMessageListener;
var sparkMessageListenerId;
var onHotkeyTriggeredId;
var bc = new BroadcastChannel('bg_sensor_data');
var FirstTrayRightClick = true;
var Background = /** @class */ (function () {
    function Background() {
        this.MonitoringArr = [];
        this.setInterval_data = null;
        this.setInterval_option = null;
        this.setInterval_manual = null; //手动记录定时器
        this.default_color = [255, 255, 255]; //dark
        this.LoadingTime = 0;
        this.setTimeout_VACUUM_GPP5 = null;
        this.GlobalBaseJsonInfo = null;
        this.GlobalShutdownStartTime = 0;
        this.setInterval_shutdown = 0;
        this.saveShutdownNumber = 30; //保留异常关机多少条数据
        this.isSaveGPUFan = false;
        this.setInterval_gpu_fan_control = null;
        this.setFanLevelValue = 0;
        this.CloseGameProcessTipsCount = 0;
        this.InGameBarrageHotKeyStatus = 1;
        this.GamePP_MainApp_Position = null;
        /*
         快捷键
         */
        this._hotkey_IngameHomeUi = 0; //游戏内控制面板
        this._hotkey_inGameMonitoring = 0; //游戏内监控显示
        this._hotkey_QuicklyCloseGameProcess = 0; //快速关闭游戏进程
        this._hotkey_CancelQuicklyCloseGameProcess = 0; //快速关闭游戏进程
        this._hotkey_OpenOrCloseInGameQuality = 0; //游戏内画质
        this._hotkey_InGameManual = 0; //
        this._hotkey_GlobalManualRecordRebound = null;
        this.GlobalManualRecordReboundCount = 0;
        this.GlobalManualRecordReboundStartTime = 0;
        this.isServiceStarted = false;
    }
    Background.prototype.AppTrayOnItemClick = function (content) {
        return __awaiter(this, void 0, void 0, function () {
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        console.log(content);
                        if (!content) {
                            return [2 /*return*/];
                        }
                        if (!(content["appId"] === gamepp.webapp.getAppId.sync())) return [3 /*break*/, 6];
                        _a = content["id"];
                        switch (_a) {
                            case 1004: return [3 /*break*/, 1];
                            case 1001: return [3 /*break*/, 2];
                        }
                        return [3 /*break*/, 4];
                    case 1:
                        {
                            gamepp.webapp.windows.show.sync(WindowName.DESKTOP);
                            gamepp.webapp.windows.focus.sync(WindowName.DESKTOP);
                            return [3 /*break*/, 5];
                        }
                        _b.label = 2;
                    case 2:
                        gamepp.exit.sync();
                        return [4 /*yield*/, gamepp.lansevice.stop.promise()];
                    case 3:
                        _b.sent();
                        return [3 /*break*/, 5];
                    case 4:
                        {
                            console.log("Unknown menu index.");
                            return [3 /*break*/, 5];
                        }
                        _b.label = 5;
                    case 5: return [3 /*break*/, 7];
                    case 6:
                        if (content["gameId"]) {
                            gamepp.launchExternalApp.sync(content["gameId"]);
                        }
                        else if (content["appId"]) {
                            gamepp.webapp.windows.launchWebapp.promise(content["appId"], content);
                        }
                        _b.label = 7;
                    case 7: return [2 /*return*/];
                }
            });
        });
    };
    Background.prototype.AppTrayOnTrayClick = function (value) {
        if (gamepp.webapp.windows.isVisible.sync(WindowName.DESKTOP)) {
            if (gamepp.webapp.windows.isMinimized.sync(WindowName.DESKTOP)) {
                gamepp.webapp.windows.show.sync(WindowName.DESKTOP);
            }
            else {
                gamepp.webapp.windows.hide.sync(WindowName.DESKTOP);
            }
        }
        else {
            if (this.GamePP_MainApp_Position !== null) {
                gamepp.webapp.windows.show.sync(WindowName.DESKTOP, true);
                gamepp.webapp.windows.setPosition.sync(WindowName.DESKTOP, this.GamePP_MainApp_Position[0], this.GamePP_MainApp_Position[1]);
                gamepp.webapp.windows.show.sync(WindowName.DESKTOP);
            }
            else {
                gamepp.webapp.windows.show.sync(WindowName.DESKTOP);
            }
        }
    };
    Background.prototype.createTray = function () {
        var _this = this;
        console.log("background CreateTray");
        // create tray.
        if (gamepp.tray.createAppTray.sync()) {
            console.log("background CreateTray True ");
            // Listen on the click event of tray icon menu item.
            gamepp.tray.onMenuItemClick.addEventListener(function (value) { return _this.AppTrayOnItemClick(value); });
            gamepp.tray.onClick.addEventListener(function (value) { return _this.AppTrayOnTrayClick(value); });
            gamepp.tray.onRightClick.addEventListener(function (value) {
                console.log(value);
                gamepp.webapp.windows.show.sync(WindowName.TRAYMENU);
                gamepp.webapp.windows.focus.sync(WindowName.TRAYMENU);
                var zoomWithSystem = gamepp.setting.getInteger.sync(313); // 是否点了跟随系统缩放开关
                var zoomValue = 1;
                if (zoomWithSystem === 1 && FirstTrayRightClick) {
                    zoomValue = gamepp.display.getScaleFromWindowInMonitor.sync();
                    FirstTrayRightClick = false;
                }
                var TraymenuBounds = gamepp.webapp.windows.getBounds.sync('traymenu');
                var TaskbarPosition = gamepp.getTaskbarPosition.sync();
                //当前鼠标位置
                var CursorPoint = gamepp.tray.getCursorScreenPoint.sync();
                console.log(CursorPoint);
                var newX = CursorPoint['x'], newY = CursorPoint['y'];
                switch (TaskbarPosition) {
                    case 0:
                        break;
                    case 1:
                        newY -= Math.floor(TraymenuBounds['height'] * zoomValue);
                        break;
                    case 2:
                        newY -= Math.floor(TraymenuBounds['height'] * zoomValue);
                        break;
                    case 3:
                        newX -= Math.floor(TraymenuBounds['width'] * zoomValue);
                        newY -= Math.floor(TraymenuBounds['height'] * zoomValue);
                        break;
                }
                console.log(newX, newY);
                gamepp.webapp.windows.setPosition.sync(WindowName.TRAYMENU, newX, newY);
                console.log("Tray menu right clicked.");
            });
            console.log("background CreateTray Leave");
        }
        else {
            console.log("background CreateTray False");
        }
    };
    /*
     热键事件
     */
    Background.prototype.AppEvent_OnHotkeyTrigggered = function (info) {
        if (!gameclient.GAME_ProcessName)
            return;
        if (info.State !== "Keydown")
            return;
        console.log(info);
        switch (info.id) { //6778
            case this._hotkey_InGameManual:
                this.OnInGameManualRecording();
                break;
            case this._hotkey_IngameHomeUi:
                this.OnShowOrCloseIngameMainUi();
                break;
            case this._hotkey_inGameMonitoring:
                this.OnShowOrCloseInGameMonitoring();
                break;
            case this._hotkey_QuicklyCloseGameProcess:
                this.OnQuicklyCloseGameProcessTips();
                break;
            case this._hotkey_CancelQuicklyCloseGameProcess:
                this.OnCancelQuicklyCloseGameProcessTips();
                break;
            case this._hotkey_OpenOrCloseInGameQuality:
                this.OnOpenOrCloseInGameQuality();
                break;
            default:
                return;
        }
    };
    /**
     *全局热键事件
     */
    Background.prototype.AppEvent_OnHotkeyGlobalTraggerInfo = function (info) {
        console.log(info);
        switch (info) {
            case this._hotkey_GlobalManualRecordRebound:
                var Rebound_exist = gamepp.package.isexists.sync('GameRebound');
                if (!Rebound_exist)
                    return;
                this.OnManualRecordReboundProcess();
                break;
            default:
                return;
        }
    };
    // GameMirror
    Background.prototype.OnInGameManualRecording = function () {
        var Rebound_exist = gamepp.package.isexists.sync('GameRebound');
        if (!Rebound_exist)
            return;
        localStorage.setItem('pointmark', JSON.stringify(1)); //打点
        this.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 35, '游戏内标记成功', 3); //成功
        console.warn('游戏内：实验室打点标记');
    };
    /*
     显示隐藏游戏内控制窗口
     */
    Background.prototype.OnShowOrCloseIngameMainUi = function () {
        if (gamepp.webapp.windows.isVisible.sync(WindowName.IN_GAME_MAIN)) {
            gamepp.webapp.windows.minimize.sync(WindowName.IN_GAME_MAIN);
        }
        else {
            gamepp.webapp.windows.show.sync(WindowName.IN_GAME_MAIN);
        }
    };
    /*
     开关游戏内监控
     */
    Background.prototype.OnShowOrCloseInGameMonitoring = function () {
        if (gamepp.webapp.windows.isVisible.sync(WindowName.IN_GAME_MONITOR)) {
            gamepp.webapp.windows.close.sync(WindowName.IN_GAME_MONITOR);
            gamepp.setting.setInteger.sync(COMMANDID.CM_ENABLE_HW_MONITING, 0);
            this.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 12, '', 3);
            localStorage.setItem('ingameSwitch', JSON.stringify(false));
        }
        else {
            gamepp.webapp.windows.show.sync(WindowName.IN_GAME_MONITOR);
            gamepp.setting.setInteger.sync(COMMANDID.CM_ENABLE_HW_MONITING, 1);
            this.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 11, '', 3);
            localStorage.setItem('ingameSwitch', JSON.stringify(true));
        }
    };
    Background.prototype.OnQuicklyCloseGameProcessTips = function () {
        return __awaiter(this, void 0, void 0, function () {
            var TERMINATION_SWITCH, execPath, ProcessName;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, gamepp.setting.getInteger.promise(COMMANDID.CM_TERMINATION_SWITCH)];
                    case 1:
                        TERMINATION_SWITCH = _a.sent();
                        if (TERMINATION_SWITCH) {
                            if (this.CloseGameProcessTipsCount === 0) {
                                // await gamepp.setting.setBool2.promise('window', WindowName.IN_GAME_Quit_Game_Tips, false);
                                // gamepp.webapp.windows.show.sync(WindowName.IN_GAME_Quit_Game_Tips);
                                // gamepp.webapp.windows.focus.sync(WindowName.IN_GAME_Quit_Game_Tips);
                                // await this.IsReadyShowSendPage('window', WindowName.IN_GAME_Quit_Game_Tips, false);
                                // await gamepp.webapp.sendInternalAppEvent.promise(WindowName.IN_GAME_Quit_Game_Tips, this.CloseGameProcessTipsCount);
                                this.CloseGameProcessTipsCount++;
                            }
                            else if (this.CloseGameProcessTipsCount === 1) {
                                execPath = CurrentGameInfo['execPath'].split('\\');
                                ProcessName = execPath[execPath.length - 1];
                                gamepp.killProcess.sync(ProcessName);
                                this.CloseGameProcessTipsCount = 0;
                            }
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    Background.prototype.OnManualRecordReboundProcess = function () {
        return __awaiter(this, void 0, void 0, function () {
            var startTime, DetailedStatus, Obj, Mode, endTime, GameTime, Obj, is_show, SendObject, Rebound_exist, Bsend, Obj, Object1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!(this.GlobalManualRecordReboundCount === 0)) return [3 /*break*/, 11];
                        console.log('%c性能统计实验室记录开始: ', 'color: orange;');
                        this.GlobalManualRecordReboundCount++;
                        startTime = Date.parse(new Date().toISOString()) / 1000;
                        this.GlobalManualRecordReboundStartTime = startTime;
                        return [4 /*yield*/, gamepp.database.insert.promise(DataBaseId.GPP5DatabaseId, "GamePP_BaseInfo", ["starttime", "data_type"], [[startTime, "manual"]])];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, gamepp.database.exists.promise(DataBaseId.GPP5DatabaseId, startTime)];
                    case 2:
                        DetailedStatus = _a.sent();
                        if (!!DetailedStatus) return [3 /*break*/, 4];
                        return [4 /*yield*/, gamepp.database.create.promise(DataBaseId.GPP5DatabaseId, "'" + startTime + "'", '("id"INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,"memory"INTEGER,"memorytemperature"INTEGER,"cpuload"TEXT,"cputemperature"TEXT,"cpuclock"TEXT,"cpupower"TEXT,"cpuvoltage"INTEGER,"gpuload"TEXT,"gpuload1"TEXT,"gputemperature"TEXT,"gpuclock"TEXT,"gpupower"TEXT,"gpumemoryload"INTEGER,"gpuvoltage"INTEGER,"currenttime"INTEGER,"performance"INTEGER)')];
                    case 3:
                        _a.sent();
                        _a.label = 4;
                    case 4:
                        this.GPP_InstallManualData(startTime);
                        if (!gamepp.webapp.windows.isVisible.sync(WindowName.DESKTOP)) return [3 /*break*/, 6];
                        Obj = {};
                        Obj['action'] = 'manual_recording_tips';
                        Obj['msg'] = '开始手动记录,再次按下停止记录';
                        return [4 /*yield*/, gamepp.webapp.sendInternalAppEvent.promise(WindowName.DESKTOP, Obj)];
                    case 5:
                        _a.sent();
                        _a.label = 6;
                    case 6:
                        Mode = gamepp.isDesktopMode.sync();
                        if (!Mode) return [3 /*break*/, 8];
                        return [4 /*yield*/, gamepp.webapp.windows.show.promise('desktop_lab')];
                    case 7:
                        _a.sent();
                        return [3 /*break*/, 10];
                    case 8: return [4 /*yield*/, gamepp.webapp.windows.show.promise('laboratory_tips_ingame')];
                    case 9:
                        _a.sent();
                        _a.label = 10;
                    case 10:
                        window.localStorage.setItem('laboratoryStatus', JSON.stringify(1));
                        return [3 /*break*/, 29];
                    case 11:
                        if (!(this.GlobalManualRecordReboundCount === 1)) return [3 /*break*/, 29];
                        endTime = Date.parse(new Date().toISOString()) / 1000;
                        GameTime = endTime - this.GlobalManualRecordReboundStartTime;
                        if (!(GameTime <= 10)) return [3 /*break*/, 13];
                        Obj = {};
                        Obj['action'] = 'manual_recording_tips';
                        Obj['msg'] = '记录间隔时间太短,请至少记录10秒!';
                        return [4 /*yield*/, gamepp.webapp.sendInternalAppEvent.promise(WindowName.DESKTOP, Obj)];
                    case 12:
                        _a.sent();
                        return [2 /*return*/, false];
                    case 13: return [4 /*yield*/, this.GPP_EndManualData(endTime, GameTime)];
                    case 14:
                        _a.sent(); //结束手动记录 保存
                        return [4 /*yield*/, gamepp.webapp.windows.isVisible.promise('rebound_details_v2')];
                    case 15:
                        is_show = _a.sent();
                        if (!is_show) return [3 /*break*/, 17];
                        return [4 /*yield*/, gamepp.webapp.windows.close.promise('rebound_details_v2')];
                    case 16:
                        _a.sent();
                        _a.label = 17;
                    case 17:
                        SendObject = {};
                        SendObject['table'] = Number(this.GlobalManualRecordReboundStartTime);
                        SendObject['is_upload'] = true;
                        console.log(SendObject);
                        return [4 /*yield*/, gamepp.package.isexists.promise('GameRebound')];
                    case 18:
                        Rebound_exist = _a.sent();
                        if (!Rebound_exist) return [3 /*break*/, 23];
                        return [4 /*yield*/, gamepp.setting.setBool2.promise('window', 'rebound_details_v2', false)];
                    case 19:
                        _a.sent();
                        gamepp.webapp.windows.show.sync('rebound_details_v2', false);
                        Bsend = 0;
                        return [4 /*yield*/, this.IsReadyShowSendPage('window', "rebound_details_v2", false)];
                    case 20:
                        _a.sent();
                        _a.label = 21;
                    case 21:
                        if (!(Bsend === 0)) return [3 /*break*/, 23];
                        gamepp.webapp.windows.isValid.sync('rebound_details_v2');
                        return [4 /*yield*/, gamepp.webapp.sendInternalAppEvent.promise("rebound_details_v2", SendObject)];
                    case 22:
                        _a.sent();
                        Bsend = 1;
                        return [3 /*break*/, 23];
                    case 23:
                        if (!gamepp.webapp.windows.isVisible.sync(WindowName.DESKTOP)) return [3 /*break*/, 26];
                        Obj = {};
                        Obj['action'] = 'manual_recording_tips';
                        Obj['msg'] = '结束手动记录';
                        return [4 /*yield*/, gamepp.webapp.sendInternalAppEvent.promise(WindowName.DESKTOP, Obj)];
                    case 24:
                        _a.sent();
                        Object1 = {};
                        Object1['action'] = 'RefreshLaboratoryList';
                        Object1['table'] = this.GlobalManualRecordReboundStartTime;
                        return [4 /*yield*/, gamepp.webapp.sendInternalAppEvent.promise(WindowName.DESKTOP, Object1)];
                    case 25:
                        _a.sent();
                        _a.label = 26;
                    case 26: return [4 /*yield*/, gamepp.webapp.windows.close.promise('desktop_lab')];
                    case 27:
                        _a.sent();
                        return [4 /*yield*/, gamepp.webapp.windows.close.promise('laboratory_tips_ingame')];
                    case 28:
                        _a.sent();
                        window.localStorage.setItem('laboratoryStatus', JSON.stringify(0));
                        _a.label = 29;
                    case 29: return [2 /*return*/];
                }
            });
        });
    };
    //保存手动记录性能数据
    Background.prototype.GPP_InstallManualData = function (startTime) {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                this.setInterval_manual = setInterval(function () { return __awaiter(_this, void 0, void 0, function () {
                    var SensorDataStr, SensorData, HWInfo, performance_1, PlrArr, performanceStr, Data_Field, Data_Content;
                    return __generator(this, function (_a) {
                        switch (_a.label) {
                            case 0:
                                SensorDataStr = window.localStorage.getItem('bg_sensor_data');
                                SensorData = SensorDataStr ? JSON.parse(SensorDataStr) : null;
                                if (!SensorData) return [3 /*break*/, 2];
                                HWInfo = {
                                    "memory": SensorData['memory']['usage'],
                                    "memorytemperature": SensorData['memory']['temp'],
                                    "cpuload": SensorData['cpu']['usage'],
                                    "cputemperature": SensorData['cpu']['temp'],
                                    "cpuclock": SensorData['cpu']['clock'],
                                    "cpupower": SensorData['cpu']['power'],
                                    "cpuvoltage": SensorData['cpu']['voltage'],
                                    "gpuload": SensorData['gpu']['d3d_usage'] + '|',
                                    "gpuload1": SensorData['gpu']['total_usage'] + '|',
                                    "gputemperature": SensorData['gpu']['temp'] + '|',
                                    "gpuclock": SensorData['gpu']['clock'] + '|',
                                    "gpupower": SensorData['gpu']['power'] + '|',
                                    "gpumemoryload": SensorData['gpu']['mem_usage'] + '|',
                                    "gpuvoltage": SensorData['gpu']['voltage'] + '|',
                                    "currenttime": Date.parse(new Date().toISOString()) / 1000,
                                };
                                performance_1 = [];
                                PlrArr = SensorData['cpu']['limit'];
                                PlrArr.forEach(function (itemDetail) {
                                    var infoKey = Object.keys(itemDetail)[0];
                                    var infoValue = itemDetail[infoKey];
                                    if (infoValue !== 0) {
                                        var performanceObj = {};
                                        performanceObj[infoKey] = infoValue;
                                        performance_1.push(performanceObj);
                                    }
                                });
                                performanceStr = performance_1.length !== 0 ? JSON.stringify(performance_1) : '';
                                Data_Field = ["memory", "memorytemperature", "cpuload", "cputemperature", "cpuclock", "cpupower", "cpuvoltage", "gpuload", "gpuload1", "gputemperature", "gpuclock", "gpupower", "gpumemoryload", "gpuvoltage", "currenttime", "performance"];
                                Data_Content = [[HWInfo['memory'], HWInfo['memorytemperature'], HWInfo['cpuload'], HWInfo['cputemperature'], HWInfo['cpuclock'], HWInfo['cpupower'], HWInfo['cpuvoltage'], HWInfo['gpuload'], HWInfo['gpuload1'], HWInfo['gputemperature'], HWInfo['gpuclock'], HWInfo['gpupower'], HWInfo['gpumemoryload'], HWInfo['gpuvoltage'], HWInfo['currenttime'], performanceStr]];
                                return [4 /*yield*/, gamepp.database.insert.promise(DataBaseId.GPP5DatabaseId, "'" + startTime + "'", Data_Field, Data_Content)];
                            case 1:
                                _a.sent();
                                _a.label = 2;
                            case 2: return [2 /*return*/];
                        }
                    });
                }); }, 1000);
                return [2 /*return*/];
            });
        });
    };
    //保存手动记录性能数据结束
    Background.prototype.GPP_EndManualData = function (endTime, GameTime) {
        return __awaiter(this, void 0, void 0, function () {
            var BaseJsonInfo, BaseJsonInfoJson, HD_Info, WeatherInfo, _a, response_obj_new, _b;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0: return [4 /*yield*/, gamepp.hardware.getBaseJsonInfo.promise()];
                    case 1:
                        BaseJsonInfo = _c.sent();
                        BaseJsonInfoJson = JSON.parse(BaseJsonInfo);
                        delete BaseJsonInfoJson["SOUND"];
                        delete BaseJsonInfoJson["NETWORK"];
                        HD_Info = encodeURIComponent(JSON.stringify(BaseJsonInfoJson));
                        return [4 /*yield*/, gamepp.database.update.promise(DataBaseId.GPP5DatabaseId, "GamePP_BaseInfo", ['endtime="' + endTime + '"', 'gametime="' + GameTime + '"', 'hd_info="' + HD_Info + '"'], 'starttime = "' + this.GlobalManualRecordReboundStartTime + '"')];
                    case 2:
                        _c.sent();
                        _c.label = 3;
                    case 3:
                        _c.trys.push([3, 5, , 6]);
                        return [4 /*yield*/, gameclient.GetTianqiApiInfo()];
                    case 4:
                        WeatherInfo = _c.sent();
                        console.warn('WeatherInfo', WeatherInfo);
                        return [3 /*break*/, 6];
                    case 5:
                        _a = _c.sent();
                        response_obj_new = {};
                        response_obj_new['type'] = 'tencent';
                        response_obj_new['city'] = '';
                        response_obj_new['province'] = '';
                        response_obj_new['cityEn'] = '';
                        response_obj_new['wea'] = '';
                        response_obj_new['wea_img'] = '';
                        response_obj_new['tem'] = '';
                        WeatherInfo = response_obj_new;
                        return [3 /*break*/, 6];
                    case 6:
                        _c.trys.push([6, 9, , 10]);
                        if (!WeatherInfo) return [3 /*break*/, 8];
                        return [4 /*yield*/, gamepp.database.update.promise(DataBaseId.GPP5DatabaseId, "GamePP_BaseInfo", ['city="' + WeatherInfo['city'] + '"', 'province="' + WeatherInfo['province'] + '"', 'wea="' + WeatherInfo['wea'] + '"', 'wea_img="' + WeatherInfo['wea_img'] + '"', 'tem="' + WeatherInfo['tem'] + '"'], 'starttime = "' + this.GlobalManualRecordReboundStartTime + '"')];
                    case 7:
                        _c.sent();
                        _c.label = 8;
                    case 8: return [3 /*break*/, 10];
                    case 9:
                        _b = _c.sent();
                        return [3 /*break*/, 10];
                    case 10:
                        /**
                         * 显示退弹窗口
                         */
                        this.GlobalManualRecordReboundCount = 0;
                        clearInterval(this.setInterval_manual);
                        return [2 /*return*/];
                }
            });
        });
    };
    Background.prototype.OnCancelQuicklyCloseGameProcessTips = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!gamepp.webapp.windows.isVisible.sync(WindowName.IN_GAME_Quit_Game_Tips)) return [3 /*break*/, 2];
                        return [4 /*yield*/, gamepp.webapp.sendInternalAppEvent.promise(WindowName.IN_GAME_Quit_Game_Tips, -1)];
                    case 1:
                        _a.sent();
                        _a.label = 2;
                    case 2: return [2 /*return*/];
                }
            });
        });
    };
    //用于创建窗口后需要发送数据到新窗口 判断窗口是否初始化完成
    Background.prototype.IsReadyShowSendPage = function (sectionName_1, keyName_1) {
        return __awaiter(this, arguments, void 0, function (sectionName, keyName, value) {
            var _this = this;
            if (value === void 0) { value = false; }
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve, reject) {
                        var nRet = false;
                        var setInterval_getbool2 = setInterval(function () { return __awaiter(_this, void 0, void 0, function () {
                            return __generator(this, function (_a) {
                                switch (_a.label) {
                                    case 0: return [4 /*yield*/, gamepp.setting.getBool2.promise(sectionName, keyName, value)];
                                    case 1:
                                        nRet = _a.sent();
                                        if (nRet) {
                                            clearInterval(setInterval_getbool2);
                                            resolve(1);
                                        }
                                        return [2 /*return*/];
                                }
                            });
                        }); }, 100);
                    })];
            });
        });
    };
    //快捷键开关游戏滤镜
    Background.prototype.OnOpenOrCloseInGameQuality = function () {
        return __awaiter(this, void 0, void 0, function () {
            var GameMirror, IsOpen, CurrentQuality;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        GameMirror = gamepp.package.isexists.sync('GameMirror');
                        if (!GameMirror)
                            return [2 /*return*/];
                        return [4 /*yield*/, gamepp.utils.sendstatics.promise(Number(100628))];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, gamepp.setting.getInteger.promise(COMMANDID.CM_ENABLE_EFFECT)];
                    case 2:
                        IsOpen = _a.sent();
                        console.log("IsOpen::", IsOpen);
                        if (!(IsOpen === 1)) return [3 /*break*/, 5];
                        return [4 /*yield*/, gamepp.game.ingame.disableShader.promise()];
                    case 3:
                        _a.sent();
                        localStorage.setItem('mirrorSwitch', 'false');
                        return [4 /*yield*/, gamepp.utils.sendstatics.promise(Number(100601))];
                    case 4:
                        _a.sent();
                        return [3 /*break*/, 10];
                    case 5:
                        if (!(IsOpen === 0)) return [3 /*break*/, 10];
                        return [4 /*yield*/, gamepp.utils.sendstatics.promise(Number(100602))];
                    case 6:
                        _a.sent();
                        return [4 /*yield*/, gamepp.game.ingame.enableShader.promise()];
                    case 7:
                        _a.sent();
                        localStorage.setItem('mirrorSwitch', 'true');
                        return [4 /*yield*/, gamepp.setting.getString.promise(COMMANDID.CM_RESHADE_TYPE)];
                    case 8:
                        CurrentQuality = _a.sent();
                        return [4 /*yield*/, gamepp.game.ingame.setShaderPreset.promise(CurrentQuality)];
                    case 9:
                        _a.sent();
                        _a.label = 10;
                    case 10: return [4 /*yield*/, gamepp.setting.setInteger.promise(COMMANDID.CM_ENABLE_EFFECT, Math.pow(0, IsOpen))];
                    case 11:
                        _a.sent(); //同时设置配置
                        return [2 /*return*/];
                }
            });
        });
    };
    Background.prototype.CheckHotkeyConflict = function () {
        return __awaiter(this, void 0, void 0, function () {
            var KeyInfo, ConflictHotKeyArr, i, KeyDef, KeyId, KeyText, KeyValSet, state, obj, length;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        window.localStorage.removeItem('conflict_hotkey');
                        KeyInfo = [
                            { "def": "Ctrl+TAB", "id": 9, "text": "打开/关闭游戏内设置面板" },
                            { "def": "Ctrl+F10", "id": 15, "text": "开启/关闭游戏内数据显示" },
                            { "def": "Ctrl+F5", "id": 13, "text": "开启/关闭游戏滤镜" },
                            { "def": "F9", "id": 60, "text": "截图热键" },
                        ];
                        ConflictHotKeyArr = {};
                        i = 0;
                        _a.label = 1;
                    case 1:
                        if (!(i < KeyInfo.length)) return [3 /*break*/, 6];
                        KeyDef = KeyInfo[i]['def'], KeyId = KeyInfo[i]['id'], KeyText = KeyInfo[i]['text'];
                        return [4 /*yield*/, gamepp.setting.getString.promise(KeyId)];
                    case 2:
                        KeyValSet = _a.sent();
                        if (!!['None', 'None_cn', '无'].includes(KeyValSet)) return [3 /*break*/, 5];
                        return [4 /*yield*/, gamepp.utils.registerGlobalHotKey.promise(KeyValSet)];
                    case 3:
                        state = _a.sent();
                        if (!state) {
                            obj = {};
                            obj['id'] = KeyId;
                            obj['default'] = KeyDef;
                            obj['text'] = KeyText;
                            ConflictHotKeyArr[KeyId] = obj;
                        }
                        return [4 /*yield*/, gamepp.utils.unregisterGlobalHotKey.promise(KeyValSet)];
                    case 4:
                        _a.sent();
                        _a.label = 5;
                    case 5:
                        i++;
                        return [3 /*break*/, 1];
                    case 6:
                        length = (Object.keys(ConflictHotKeyArr)).length;
                        if (length === 0) {
                            console.log('无冲突热键');
                            window.localStorage.removeItem('conflict_hotkey');
                        }
                        else {
                            console.log('冲突热键:', ConflictHotKeyArr);
                            window.localStorage.setItem('conflict_hotkey', JSON.stringify(ConflictHotKeyArr));
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    Background.prototype.registerIngameHotkey = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _hotkey_ingameMainUiShotkey, _hotkey_inGameMonitoringKey, _hotkey_OpenOrCloseInGameQualityKey, _hotkey_inGamePointMark, _a, _b, _c, _d, _e, _f, _hotkey_manualRecordRebound, manualRecordRebound;
            var _this = this;
            return __generator(this, function (_g) {
                switch (_g.label) {
                    case 0: return [4 /*yield*/, gamepp.setting.getString.promise(COMMANDID.CM_HOTKEY_SHOW_MAINUI_INFO)];
                    case 1:
                        _hotkey_ingameMainUiShotkey = _g.sent();
                        return [4 /*yield*/, gamepp.setting.getString.promise(COMMANDID.CM_HOTKEY_HARDWARE_MONITING_SWITCH_INFO)];
                    case 2:
                        _hotkey_inGameMonitoringKey = _g.sent();
                        return [4 /*yield*/, gamepp.setting.getString.promise(COMMANDID.CM_HOTKEY_PRESENT_EFFECT_SWITCH_INFO)];
                    case 3:
                        _hotkey_OpenOrCloseInGameQualityKey = _g.sent();
                        return [4 /*yield*/, gamepp.setting.getString.promise(527)];
                    case 4:
                        _hotkey_inGamePointMark = _g.sent();
                        _a = this;
                        return [4 /*yield*/, gamepp.game.ingame.registerHotkey.promise(_hotkey_inGamePointMark)];
                    case 5:
                        _a._hotkey_InGameManual = _g.sent();
                        console.log("性能统计打点热键: ", 'Shift+F11', "Id: ", this._hotkey_InGameManual);
                        _b = this;
                        return [4 /*yield*/, gamepp.game.ingame.registerHotkey.promise(_hotkey_ingameMainUiShotkey)];
                    case 6:
                        _b._hotkey_IngameHomeUi = _g.sent(); //'Ctrl+Tab'
                        console.log("游戏内控制面板热键: ", _hotkey_ingameMainUiShotkey, "Id: ", this._hotkey_IngameHomeUi);
                        _c = this;
                        return [4 /*yield*/, gamepp.game.ingame.registerHotkey.promise(_hotkey_inGameMonitoringKey)];
                    case 7:
                        _c._hotkey_inGameMonitoring = _g.sent();
                        console.log("游戏内监控开关热键: ", _hotkey_inGameMonitoringKey, "Id: ", this._hotkey_inGameMonitoring);
                        _d = this;
                        return [4 /*yield*/, gamepp.game.ingame.registerHotkey.promise('Alt+F4')];
                    case 8:
                        _d._hotkey_QuicklyCloseGameProcess = _g.sent();
                        console.log("快速关闭游戏进程热键: ", 'Alt+F4', "Id: ", this._hotkey_QuicklyCloseGameProcess);
                        _e = this;
                        return [4 /*yield*/, gamepp.game.ingame.registerHotkey.promise('Escape')];
                    case 9:
                        _e._hotkey_CancelQuicklyCloseGameProcess = _g.sent();
                        console.log("取消快速关闭游戏进程热键: ", 'Escape', "Id: ", this._hotkey_CancelQuicklyCloseGameProcess);
                        _f = this;
                        return [4 /*yield*/, gamepp.game.ingame.registerHotkey.promise(_hotkey_OpenOrCloseInGameQualityKey)];
                    case 10:
                        _f._hotkey_OpenOrCloseInGameQuality = _g.sent();
                        console.log("开关游戏内滤镜热键: ", _hotkey_OpenOrCloseInGameQualityKey, "Id: ", this._hotkey_OpenOrCloseInGameQuality);
                        return [4 /*yield*/, gamepp.setting.getString.promise(468)];
                    case 11:
                        _hotkey_manualRecordRebound = _g.sent();
                        this._hotkey_GlobalManualRecordRebound = _hotkey_manualRecordRebound;
                        console.log("实验室热键: ", 'Shift+F10', "Id: ", this._hotkey_GlobalManualRecordRebound);
                        if (!['无', 'None', 'None_cn'].includes(_hotkey_manualRecordRebound)) {
                            manualRecordRebound = gamepp.utils.registerGlobalHotKey.sync(_hotkey_manualRecordRebound);
                            console.log("试验室手动记录硬件数据: ", _hotkey_manualRecordRebound, manualRecordRebound);
                        }
                        if (onHotkeyTriggeredId) {
                            gamepp.game.ingame.onHotkeyTriggered.removeEventListener(onHotkeyTriggeredId);
                            onHotkeyTriggeredId = false;
                        }
                        onHotkeyTriggeredId = gamepp.game.ingame.onHotkeyTriggered.addEventListener(function (value) { return _this.AppEvent_OnHotkeyTrigggered(value); }); //注册游戏内热键 游戏内使用
                        gamepp.utils.globalHotKeyTraggerInfo.addEventListener(function (value) { return _this.AppEvent_OnHotkeyGlobalTraggerInfo(value); }); //注册全局热键   游戏外使用
                        return [2 /*return*/];
                }
            });
        });
    };
    //喜加一自动领取 程序起来之后过10分钟执行 与HwInfo占用避开
    Background.prototype.AutoRecevice = function () {
        var AutoFreeGame = JSON.parse(localStorage.getItem('autoReceive'));
        var autoseconds = JSON.parse(localStorage.getItem('autoSeconds'));
        if (!autoseconds) {
            autoseconds = 60 * 60 * 3;
        }
        if (AutoFreeGame) {
            if (!gamepp.webapp.windows.isValid.sync('FreeGame')) {
                autoseconds--;
                if (autoseconds == 0) {
                    localStorage.setItem('needclose', JSON.stringify(1));
                    gamepp.webapp.windows.show.sync('FreeGame', true); //执行完毕就关窗口
                    autoseconds = 60 * 60 * 3;
                }
                localStorage.setItem('autoSeconds', JSON.stringify(autoseconds));
            }
        }
    };
    Background.prototype.AppEvent_OnInternalEvent = function (e) {
        return __awaiter(this, void 0, void 0, function () {
            var execPath, ProcessName, _i, timerIds_1, id, _loop_1, i, LogState, PresentBounds, handle, result, resultI, _a, _b, item, r, AppDataDir, GPP5DatabaseDir, GPP5DatabaseId, GPPDownDatabaseId;
            var _this = this;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        console.warn('Backround receive message:', e);
                        if (!(e['action'] === 'CancelExitGame')) return [3 /*break*/, 1];
                        this.CloseGameProcessTipsCount = 0;
                        return [3 /*break*/, 40];
                    case 1:
                        if (!(e['action'] === 'autoFree')) return [3 /*break*/, 2];
                        gamepp.webapp.windows.close.sync('FreeGame'); //执行完毕就关窗口
                        return [3 /*break*/, 40];
                    case 2:
                        if (!(e['action'] === 'ImmediateExitGame')) return [3 /*break*/, 3];
                        execPath = CurrentGameInfo['execPath'].split('\\');
                        ProcessName = execPath[execPath.length - 1];
                        gamepp.killProcess.sync(ProcessName);
                        this.CloseGameProcessTipsCount = 0;
                        return [3 /*break*/, 40];
                    case 3:
                        if (!(e['action'] === 'UpdateHotKey')) return [3 /*break*/, 4];
                        console.warn('更新热键');
                        console.log(e);
                        this.UpdateHotKeyRegistration();
                        return [3 /*break*/, 40];
                    case 4:
                        if (!(e['action'] === 'refreshUserToken')) return [3 /*break*/, 9];
                        console.log('refreshUserToken');
                        for (_i = 0, timerIds_1 = timerIds; _i < timerIds_1.length; _i++) {
                            id = timerIds_1[_i];
                            clearTimeout(id);
                        }
                        timerIds.clear(); // 清空Set
                        _loop_1 = function (i) {
                            var delay;
                            return __generator(this, function (_d) {
                                switch (_d.label) {
                                    case 0:
                                        delay = i === 0 ? 0 : 5000;
                                        if (!(i === 0)) return [3 /*break*/, 1];
                                        console.log('Loop iteration:', i + 1);
                                        return [3 /*break*/, 3];
                                    case 1: return [4 /*yield*/, new Promise(function (resolve) {
                                            var timerId = setTimeout(function () { return __awaiter(_this, void 0, void 0, function () {
                                                return __generator(this, function (_a) {
                                                    switch (_a.label) {
                                                        case 0:
                                                            console.log('Loop iteration:', i + 1);
                                                            return [4 /*yield*/, gamepp.user.refreshUserToken.promise(true)];
                                                        case 1:
                                                            _a.sent();
                                                            resolve(true);
                                                            return [2 /*return*/];
                                                    }
                                                });
                                            }); }, delay);
                                            timerIds.add(timerId);
                                        })];
                                    case 2:
                                        _d.sent();
                                        _d.label = 3;
                                    case 3: return [2 /*return*/];
                                }
                            });
                        };
                        i = 0;
                        _c.label = 5;
                    case 5:
                        if (!(i < 5)) return [3 /*break*/, 8];
                        return [5 /*yield**/, _loop_1(i)];
                    case 6:
                        _c.sent();
                        _c.label = 7;
                    case 7:
                        i++;
                        return [3 /*break*/, 5];
                    case 8: return [3 /*break*/, 40];
                    case 9:
                        if (!(e['action'] === 'ExitGamePP')) return [3 /*break*/, 25];
                        console.log('background收到APP退出通知');
                        return [4 /*yield*/, gamepp.webapp.windows.hide.promise('desktop')];
                    case 10:
                        _c.sent();
                        return [4 /*yield*/, gamepp.setting.getInteger.promise(479)];
                    case 11:
                        if (!((_c.sent()) === 1)) return [3 /*break*/, 14];
                        return [4 /*yield*/, gamepp.webapp.sendInternalAppEvent.promise('background', {
                                "action": "sendShutdownReportState",
                                "value": 0
                            })];
                    case 12:
                        _c.sent();
                        return [4 /*yield*/, gamepp.setting.setInteger.promise(479, 0)];
                    case 13:
                        _c.sent();
                        _c.label = 14;
                    case 14: return [4 /*yield*/, gamepp.setting.getGamePPLogState.promise()];
                    case 15:
                        LogState = _c.sent();
                        if (!LogState) return [3 /*break*/, 17];
                        return [4 /*yield*/, gamepp.setting.emptyGamePPLog.promise()];
                    case 16:
                        _c.sent();
                        _c.label = 17;
                    case 17:
                        if (!(gamepp.setting.getInteger.sync(311) === 0)) return [3 /*break*/, 24];
                        //锁定桌面监控
                        return [4 /*yield*/, gamepp.setting.setInteger.promise(COMMANDID.CM_DESK_FLOATWINDOW_LOCK, 1)];
                    case 18:
                        //锁定桌面监控
                        _c.sent();
                        return [4 /*yield*/, gamepp.webapp.windows.getBounds.promise(WindowName.Desktop_Monitor)];
                    case 19:
                        PresentBounds = _c.sent();
                        return [4 /*yield*/, gamepp.setting.setInteger.promise(COMMANDID.CM_DESK_LOCATION_X, PresentBounds['x'])];
                    case 20:
                        _c.sent();
                        return [4 /*yield*/, gamepp.setting.setInteger.promise(COMMANDID.CM_DESK_LOCATION_Y, PresentBounds['y'])];
                    case 21:
                        _c.sent();
                        return [4 /*yield*/, gamepp.dialog.showdeskMonitor.promise()];
                    case 22:
                        handle = _c.sent();
                        return [4 /*yield*/, gamepp.displayDestopWindow.promise(handle)];
                    case 23:
                        _c.sent();
                        _c.label = 24;
                    case 24:
                        setTimeout(function () { return __awaiter(_this, void 0, void 0, function () {
                            return __generator(this, function (_a) {
                                switch (_a.label) {
                                    case 0: return [4 /*yield*/, gamepp.lansevice.stop.promise()];
                                    case 1:
                                        _a.sent();
                                        return [4 /*yield*/, gamepp.exit.promise()];
                                    case 2:
                                        _a.sent();
                                        return [2 /*return*/];
                                }
                            });
                        }); }, 250);
                        return [3 /*break*/, 40];
                    case 25:
                        if (!(e.action === 'reloadXMLInfo')) return [3 /*break*/, 29];
                        return [4 /*yield*/, gamepp.update.checkHWInfo.promise()
                            //加载XML解析文件
                        ];
                    case 26:
                        _c.sent();
                        return [4 /*yield*/, this.getScript('js/HWInfoDataAnalyzer/HwInfoXMLProcess.js')];
                    case 27:
                        result = _c.sent();
                        console.warn('load HwInfoXMLProcess result', result);
                        return [4 /*yield*/, this.getScript('js/HWInfoDataAnalyzer/HWInfoSensorProcess.js')];
                    case 28:
                        resultI = _c.sent();
                        console.warn('load HWInfoSensorProcess result', resultI);
                        gamepp.update.updateHWInfo.promise();
                        console.warn('gamepp.update.updateHWInfW');
                        //XML解析
                        if (result && resultI) {
                            this.ConvertXMLData2JSON('reload'); //转换XML数据2 JSON
                        }
                        else {
                            console.warn('未进行XML解析');
                        }
                        return [3 /*break*/, 40];
                    case 29:
                        if (!(e.action === 'moveDataBase')) return [3 /*break*/, 39];
                        return [4 /*yield*/, gamepp.database.close.promise(DataBaseId.GPPDownDatabaseId)];
                    case 30:
                        _c.sent();
                        return [4 /*yield*/, gamepp.database.close.promise(DataBaseId.GPP5DatabaseId)];
                    case 31:
                        _c.sent();
                        _a = 0, _b = ['\\GamePP5.dll', '\\GamePPStressNew.dll', '\\GamePPShutdown.dll'];
                        _c.label = 32;
                    case 32:
                        if (!(_a < _b.length)) return [3 /*break*/, 35];
                        item = _b[_a];
                        return [4 /*yield*/, gamepp.utils.copyFile.promise(e.oldPath + item, e.newPath + item)];
                    case 33:
                        r = _c.sent();
                        console.log(r);
                        if (r) {
                            gamepp.utils.unlinkFile.promise(e.oldPath + item);
                        }
                        _c.label = 34;
                    case 34:
                        _a++;
                        return [3 /*break*/, 32];
                    case 35: return [4 /*yield*/, gamepp.getAppDataDir.promise()];
                    case 36:
                        AppDataDir = _c.sent();
                        GPP5DatabaseDir = gamepp.setting.getString.sync(COMMANDID.CM_STATISTICS_SAVE_ADDRESS);
                        if (GPP5DatabaseDir == '')
                            GPP5DatabaseDir = AppDataDir + '\\common\\Data';
                        return [4 /*yield*/, gamepp.database.open.promise(GPP5DatabaseDir + '\\GamePP5.dll')];
                    case 37:
                        GPP5DatabaseId = _c.sent();
                        return [4 /*yield*/, gamepp.database.open.promise(GPP5DatabaseDir + '\\GamePPShutdown.dll')];
                    case 38:
                        GPPDownDatabaseId = _c.sent();
                        DataBaseId.GPPDownDatabaseId = GPPDownDatabaseId;
                        DataBaseId.GPP5DatabaseId = GPP5DatabaseId;
                        return [3 /*break*/, 40];
                    case 39:
                        if (e.action === 'ThresholdTriggered') {
                            video_client.AppEvent_OnInGameThresholdTriggered(e.value);
                        }
                        _c.label = 40;
                    case 40: return [2 /*return*/];
                }
            });
        });
    };
    Background.prototype.GPP_InstallShutdownData = function (startTime) {
        var _this = this;
        //setTimeout setInterval
        var insertIndex = 1;
        this.setInterval_shutdown = setInterval(function () { return __awaiter(_this, void 0, void 0, function () {
            var SensorDataStr, SensorData, HWInfo, deleteId, Data_Field, Data_Content;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        SensorDataStr = window.localStorage.getItem('bg_sensor_data');
                        SensorData = SensorDataStr ? JSON.parse(SensorDataStr) : null;
                        if (!SensorData) return [3 /*break*/, 4];
                        HWInfo = {
                            "memory": SensorData['memory']['usage'],
                            "memorytemp": SensorData['memory']['temp'],
                            "cpuload": SensorData['cpu']['usage'],
                            "cpuloadP": SensorData['cpu']['usageP'],
                            "cpuloadE": SensorData['cpu']['usageE'],
                            "cputemp": SensorData['cpu']['temp'],
                            "cpuclock": SensorData['cpu']['clock'],
                            "cpuclockP": SensorData['cpu']['clockP'],
                            "cpuclockE": SensorData['cpu']['clockE'],
                            "cputdp": SensorData['cpu']['power'],
                            "cpuvoltage": SensorData['cpu']['voltage'],
                            "cpufan": SensorData['cpu']['fan'],
                            "gpuload": SensorData['gpu']['total_usage'],
                            "gputemp": SensorData['gpu']['temp'],
                            "gpuhotspottemp": SensorData['gpu']['hot_spot_temp'],
                            "gpuclock": SensorData['gpu']['clock'],
                            "gputdp": SensorData['gpu']['power'],
                            "gpufan": SensorData['gpu']['fan'],
                            "gpuvoltage": SensorData['gpu']['voltage'],
                            "gpumemoryload": SensorData['gpu']['mem_usage'],
                            "gpumemoryclock": SensorData['gpu']['mem_clock'],
                            "gpumemorytemp": SensorData['gpu']['mem_temp'],
                            "drivetemp": SensorData['drive']['temp'],
                            "driveload": SensorData['drive']['usage'],
                            "currenttime": Date.parse(new Date().toISOString()) / 1000,
                        };
                        if (!(insertIndex > (this.saveShutdownNumber))) return [3 /*break*/, 2];
                        deleteId = Number(insertIndex - (this.saveShutdownNumber));
                        return [4 /*yield*/, gamepp.database.delete.promise(DataBaseId.GPPDownDatabaseId, "'" + startTime + "'", "id = " + deleteId + "")];
                    case 1:
                        _a.sent();
                        _a.label = 2;
                    case 2:
                        Data_Field = ["memory", "memorytemp", "cpuload", "cpuloadP", "cpuloadE", "cputemp", "cpuclock", "cpuclockP", "cpuclockE", "cputdp", "cpuvoltage", "cpufan", "gpuload", "gputemp", "gpuhotspottemp", "gpuclock", "gputdp", "gpufan", "gpuvoltage", "gpumemoryload", "gpumemoryclock", "gpumemorytemp", "driveload", "drivetemp", "currenttime"];
                        Data_Content = [[HWInfo['memory'], HWInfo['memorytemp'], HWInfo['cpuload'], HWInfo['cpuloadP'], HWInfo['cpuloadE'], HWInfo['cputemp'], HWInfo['cpuclock'], HWInfo['cpuclockP'], HWInfo['cpuclockE'], HWInfo['cputdp'], HWInfo['cpuvoltage'], HWInfo['cpufan'], HWInfo['gpuload'], HWInfo['gputemp'], HWInfo['gpuhotspottemp'], HWInfo['gpuclock'], HWInfo['gputdp'], HWInfo['gpufan'], HWInfo['gpuvoltage'], HWInfo['gpumemoryload'], HWInfo['gpumemoryclock'], HWInfo['gpumemorytemp'], HWInfo['driveload'], HWInfo['drivetemp'], HWInfo['currenttime']]];
                        return [4 /*yield*/, gamepp.database.insert.promise(DataBaseId.GPPDownDatabaseId, "'" + startTime + "'", Data_Field, Data_Content)];
                    case 3:
                        _a.sent();
                        insertIndex++;
                        _a.label = 4;
                    case 4: return [2 /*return*/];
                }
            });
        }); }, 1000);
    };
    Background.prototype.ColorProcessDesc = function (num) {
        var DescColor = [];
        if (num <= 40) {
            DescColor = [96, 229, 77]; //#60e54d
        }
        else if (num <= 80) {
            DescColor = [227, 197, 33]; //#e3c521
        }
        else {
            DescColor = [224, 78, 58]; //#e04e3a
        }
        return DescColor;
    };
    Background.prototype.BytesToSize = function (bytes) {
        if (bytes === 0)
            return '0 KB';
        var k = 1024, sizes = ['KB', 'MB'];
        var i = Math.floor(Math.log(bytes) / Math.log(k));
        // return (bytes / Math.pow(k, i)).toPrecision(2) + sizes[i];
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + sizes[i];
    };
    /*
     更新热键注册
     */
    Background.prototype.UpdateHotKeyRegistration = function () {
        return __awaiter(this, void 0, void 0, function () {
            var unregisterHotkeyArr, i;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        console.log('UpdateHotKeyRegistration');
                        unregisterHotkeyArr = [
                            this._hotkey_InGameManual,
                            this._hotkey_IngameHomeUi,
                            this._hotkey_inGameMonitoring,
                            this._hotkey_QuicklyCloseGameProcess,
                            this._hotkey_OpenOrCloseInGameQuality,
                            this._hotkey_CancelQuicklyCloseGameProcess,
                            video_client._hotkey_NormalVideoToggle,
                            video_client._hotkey_ReplaySave,
                            video_client._hotkey_ShotcutTriggered,
                            video_client._hotkey_AIViesSHOW,
                            gameclient._hotkey_RecordHotkey
                        ];
                        for (i = 0; i < unregisterHotkeyArr.length; i++) {
                            this.unregisterHotkey(unregisterHotkeyArr[i]);
                        }
                        //全局热键
                        if (!['None_cn', 'None', '无'].includes(this._hotkey_GlobalManualRecordRebound)) {
                            gamepp.utils.unregisterGlobalHotKey.sync(this._hotkey_GlobalManualRecordRebound);
                        }
                        return [4 /*yield*/, this.registerIngameHotkey()];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, video_client.registerMediaHotkey()];
                    case 2:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    Background.prototype.unregisterHotkey = function (value) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!!['None_cn', 'None', '无'].includes(value)) return [3 /*break*/, 2];
                        return [4 /*yield*/, gamepp.game.ingame.unregisterHotkey.promise(value)];
                    case 1:
                        _a.sent();
                        _a.label = 2;
                    case 2: return [2 /*return*/];
                }
            });
        });
    };
    Background.prototype.AppEvent_OnWindowReadyToShow = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        console.log("App running now!");
                        return [4 /*yield*/, gamepp.whenMainServicesStarted.promise()];
                    case 1:
                        _a.sent();
                        console.log("检查热键冲突");
                        return [4 /*yield*/, this.CheckHotkeyConflict()];
                    case 2:
                        _a.sent();
                        console.log("Services started!");
                        return [4 /*yield*/, this.registerIngameHotkey()];
                    case 3:
                        _a.sent();
                        return [4 /*yield*/, this.InitAppEvent()];
                    case 4:
                        _a.sent();
                        console.log("Initialize the system tray.");
                        this.createTray();
                        console.log("Initialize the ig component.");
                        gameclient.Run();
                        console.log("Initialize the video recorder.");
                        video_client.Run();
                        this.isServiceStarted = true;
                        console.log("All things good. initialisation done.");
                        return [2 /*return*/];
                }
            });
        });
    };
    Background.prototype.registerSecondInstanceNotifier = function () {
        var _this = this;
        gamepp.onSecondIntanceLaunched.addEventListener(function (argv) {
            console.log(argv);
            if (_this.isServiceStarted) {
                gamepp.webapp.windows.show.sync(WindowName.DESKTOP);
                gamepp.webapp.windows.focus.sync(WindowName.DESKTOP);
            }
            else {
                //如果是第二个实例不应该再启动这个页面
                //gamepp.webapp.windows.show(WindowName.UPDATE);
                //gamepp.webapp.windows.focus(WindowName.UPDATE);
            }
        });
    };
    Background.prototype.registerNotifyReceiver = function () {
        var _this = this;
        this.registerSecondInstanceNotifier();
        gamepp.webapp.onInternalAppEvent.addEventListener(function (value) { return _this.AppEvent_OnInternalEvent(value); });
        try {
            gamepp.desktopmonitor.onScreenNumsChange.addEventListener(function (value) { }); //监听显示器数量变化
        }
        catch (_a) {
            console.log('onScreenNumsChange error');
        }
    };
    //转换XML数据为JSON并保存
    Background.prototype.ConvertXMLData2JSON = function (reload) {
        return __awaiter(this, void 0, void 0, function () {
            var HWinfoXmlState, HWInfoXmlStr, JsonObj, HwInfoJsonStr, HwInfoJson, taskbar_monitor_status, _a;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        HWinfoXmlState = 0;
                        try {
                            HWinfoXmlState = gamepp.hardware.getXMLState.sync();
                        }
                        catch (_c) {
                            HWinfoXmlState = 1;
                        }
                        if (!(HWinfoXmlState === 1)) return [3 /*break*/, 10];
                        return [4 /*yield*/, gamepp.hardware.getBaseXmlInfo.promise()];
                    case 1:
                        HWInfoXmlStr = _b.sent();
                        if (!(HWInfoXmlStr !== '' && HWInfoXmlStr !== null && HWInfoXmlStr !== undefined)) return [3 /*break*/, 9];
                        JsonObj = $.xml2json(string2XML(HWInfoXmlStr));
                        console.log(JsonObj);
                        window.localStorage.setItem('HWInfoXml', JSON.stringify(JsonObj));
                        return [4 /*yield*/, HWInfoXMLPageDisplayProcess(JsonObj)];
                    case 2:
                        _b.sent(); //XML解析
                        if (!(reload === 'init')) return [3 /*break*/, 9];
                        return [4 /*yield*/, gamepp.hardware.getBaseJsonInfo.promise()];
                    case 3:
                        HwInfoJsonStr = _b.sent();
                        HwInfoJson = JSON.parse(HwInfoJsonStr);
                        _b.label = 4;
                    case 4:
                        _b.trys.push([4, 6, , 7]);
                        this.saveHardwareInformation(HwInfoJson);
                        return [4 /*yield*/, gamepp.setting.getInteger.promise(COMMANDID.CM_TASKBAR_MONITOR_OPEN)];
                    case 5:
                        taskbar_monitor_status = _b.sent();
                        if (taskbar_monitor_status) {
                            setTimeout(function () { return __awaiter(_this, void 0, void 0, function () {
                                return __generator(this, function (_a) {
                                    // await gamepp.deskband.startDeskBand.promise();
                                    gamepp.webapp.windows.show.sync('taskbar_hardware');
                                    return [2 /*return*/];
                                });
                            }); }, 500);
                        }
                        return [3 /*break*/, 7];
                    case 6:
                        _a = _b.sent();
                        return [3 /*break*/, 7];
                    case 7:
                        this.GlobalBaseJsonInfo = HwInfoJson;
                        setTimeout(function () {
                            var _a;
                            var refresh_time = 1000;
                            try {
                                refresh_time = (_a = gamepp.setting) === null || _a === void 0 ? void 0 : _a.getInteger.sync(COMMANDID.CM_DATA_REFRESH_TIME_SETTING);
                            }
                            catch (_b) {
                            }
                            _this.BgSensorDataProcess(refresh_time);
                            _this.StarSensorDataProcess(refresh_time);
                            _this.getSensorStatistics();
                        }, 2000);
                        if (!(gamepp.setting.getInteger.sync(COMMANDID.CM_USE_DESKTOP_FRAME) === 1)) return [3 /*break*/, 9];
                        return [4 /*yield*/, gamepp.webapp.windows.show.promise(WindowName.Desktop_Monitor)];
                    case 8:
                        _b.sent();
                        _b.label = 9;
                    case 9: return [3 /*break*/, 11];
                    case 10:
                        setTimeout(function () {
                            app.ConvertXMLData2JSON(reload);
                        }, 1000);
                        _b.label = 11;
                    case 11: return [2 /*return*/];
                }
            });
        });
    };
    Background.prototype.SensorAddAverageData = function (SensorInfo, SensorInfoKeys) {
        var vidSum = 0, vidCount = 0, vidSumP = 0, vidCountP = 0, vidSumE = 0, vidCountE = 0;
        var vidMax = 0, vidMaxP = 0, vidMaxE = 0;
        var clockSum = 0, clockCount = 0, clockSumP = 0, clockCountP = 0, clockSumE = 0, clockCountE = 0;
        var tempSum = 0, tempCount = 0, tempSumP = 0, tempCountP = 0, tempSumE = 0, tempCountE = 0;
        var usageSum = 0, usageCount = 0, usageSumP = 0, usageCountP = 0, usageSumE = 0, usageCountE = 0;
        var vidRegex = /^Core[ \f\r\t\n][0-9]+[ \f\r\t\n]VID$/i;
        var vidRegexP = /^P-Core[ \f\r\t\n][0-9]+[ \f\r\t\n]VID$/i;
        var vidRegexE = /^E-Core[ \f\r\t\n][0-9]+[ \f\r\t\n]VID$/i;
        var clockRegex = /^Core[ \f\r\t\n][0-9]+[ \f\r\t\n]Clock[.\n]*$/i;
        var clockRegexP = /^P-Core[ \f\r\t\n][0-9]+[ \f\r\t\n]Clock[.\n]*$/i;
        var clockRegexE = /^E-Core[ \f\r\t\n][0-9]+[ \f\r\t\n]Clock[.\n]*$/i;
        var tempRegex = /^Core[ \f\r\t\n][0-9]{1,2}$/i;
        var tempRegexP = /^P-Core[ \f\r\t\n][0-9]{1,2}$/i;
        var tempRegexE = /^E-Core[ \f\r\t\n][0-9]{1,2}$/i;
        var usageRegex = /^CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]T[0-9][ \f\r\t\n]USAGE$/i;
        var usagePRegex = /^P-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]T[0-9][ \f\r\t\n]USAGE$/i;
        var usageERegex = /^E-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]T[0-9][ \f\r\t\n]USAGE$/i;
        SensorInfoKeys.forEach(function (key) {
            if (SensorInfo[key]) {
                SensorInfo[key].forEach(function (item) {
                    var itemKey = String(Object.keys(item)[0]);
                    var value = parseFloat(item[itemKey].value);
                    var type = item[itemKey]['type'];
                    itemKey = itemKey.toLowerCase();
                    if (type === "voltage" && itemKey.includes('core') && key.includes('CPU')) {
                        vidSum += value;
                        vidMax = Math.max(vidMax, value);
                        vidCount++;
                        if (itemKey.includes('p-core')) {
                            vidSumP += value;
                            vidMaxP = Math.max(vidMaxP, value);
                            vidCountP++;
                        }
                        else if (itemKey.includes('e-core')) {
                            vidSumE += value;
                            vidMaxE = Math.max(vidMaxE, value);
                            vidCountE++;
                        }
                    }
                    else if (type === "clock" && itemKey.includes('core') && itemKey.includes('clock') && key.includes('CPU') && !itemKey.includes('effective')) {
                        clockSum += value;
                        clockCount++;
                        if (itemKey.includes('p-core')) {
                            clockSumP += value;
                            clockCountP++;
                        }
                        else if (itemKey.includes('e-core')) {
                            clockSumE += value;
                            clockCountE++;
                        }
                    }
                    else if (tempRegex.test(itemKey) || tempRegexP.test(itemKey) || tempRegexE.test(itemKey)) {
                        tempSum += value;
                        tempCount++;
                        if (itemKey.includes('p-core')) {
                            tempSumP += value;
                            tempCountP++;
                        }
                        else if (itemKey.includes('e-core')) {
                            tempSumE += value;
                            tempCountE++;
                        }
                    }
                    else if (type === "usage" && itemKey.includes('core') && itemKey.includes('usage') && key.includes('CPU')) {
                        usageSum += value;
                        usageCount++;
                        if (itemKey.includes('p-core')) {
                            usageSumP += value;
                            usageCountP++;
                        }
                        else if (itemKey.includes('e-core')) {
                            usageSumE += value;
                            usageCountE++;
                        }
                    }
                });
            }
        });
        var averageVidObj = { "Core Max VID": { "type": "voltage", "value": vidCount !== 0 ? vidMax : 0 } };
        var averageVidPObj = { "P Core Max VID": { "type": "voltage", "value": vidCountP !== 0 ? vidMaxP : 0 } };
        var averageVidEObj = { "E Core Max VID": { "type": "voltage", "value": vidCountE !== 0 ? vidMaxE : 0 } };
        var averageClockObj = { "Core Clocks": { "type": "clock", "value": clockCount !== 0 ? clockSum / clockCount : 0 } };
        var averageClockPObj = { "P Core Clocks": { "type": "clock", "value": clockCountP !== 0 ? clockSumP / clockCountP : 0 } };
        var averageClockEObj = { "E Core Clocks": { "type": "clock", "value": clockCountE !== 0 ? clockSumE / clockCountE : 0 } };
        // const averageTempObj = { "Core Temps": { "type": "temperature", "value": tempCount !== 0 ? tempSum / tempCount : 0 } };
        // const averageTempPObj = { "P Core Temps": { "type": "temperature", "value": tempCountP !== 0 ? tempSumP / tempCountP : 0 } };
        // const averageTempEObj = { "E Core Temps": { "type": "temperature", "value": tempCountE !== 0 ? tempSumE / tempCountE : 0 } };
        var averageUsageObj = { "Core Usages": { "type": "usage", "value": usageCount !== 0 ? usageSum / usageCount : 0 } };
        var averageUsagePObj = { "P Core Usages": { "type": "usage", "value": usageCountP !== 0 ? usageSumP / usageCountP : 0 } };
        var averageUsageEObj = { "E Core Usages": { "type": "usage", "value": usageCountE !== 0 ? usageSumE / usageCountE : 0 } };
        var CoreVidFound = false, CoreClockFound = false, CoreTempFound = false, CoreUsageFound = false;
        var pCoreVidFound = false, pCoreClockFound = false, pCoreTempFound = false, pCoreUsageFound = false;
        var eCoreVidFound = false, eCoreClockFound = false, eCoreTempFound = false, eCoreUsageFound = false;
        SensorInfoKeys.forEach(function (sensorKey) {
            var resultData = [];
            if (SensorInfo[sensorKey]) {
                SensorInfo[sensorKey].forEach(function (sensorItem) {
                    var sensorItemKey = Object.keys(sensorItem)[0];
                    var sensorItemKeyUpper = sensorItemKey.toUpperCase();
                    var type = sensorItem[sensorItemKey]['type'];
                    if (sensorKey.includes('CPU')) {
                        if (vidCount !== 0 && !CoreVidFound) {
                            resultData.push(averageVidObj);
                            CoreVidFound = true;
                        }
                        if (vidCountP !== 0 && !pCoreVidFound) {
                            resultData.push(averageVidPObj);
                            pCoreVidFound = true;
                        }
                        if (vidCountE !== 0 && !eCoreVidFound) {
                            resultData.push(averageVidEObj);
                            eCoreVidFound = true;
                        }
                        if (clockCount !== 0 && !CoreClockFound) {
                            resultData.push(averageClockObj);
                            CoreClockFound = true;
                        }
                        if (clockCountP !== 0 && !pCoreClockFound) {
                            resultData.push(averageClockPObj);
                            pCoreClockFound = true;
                        }
                        if (clockCountE !== 0 && !eCoreClockFound) {
                            resultData.push(averageClockEObj);
                            eCoreClockFound = true;
                        }
                        if (usageCount !== 0 && !CoreUsageFound) {
                            resultData.push(averageUsageObj);
                            CoreUsageFound = true;
                        }
                        if (usageCountP !== 0 && !pCoreUsageFound) {
                            resultData.push(averageUsagePObj);
                            pCoreUsageFound = true;
                        }
                        if (usageCountE !== 0 && !eCoreUsageFound) {
                            resultData.push(averageUsageEObj);
                            eCoreUsageFound = true;
                        }
                    }
                    resultData.push(sensorItem);
                });
                SensorInfo[sensorKey] = resultData;
            }
        });
        return SensorInfo;
    };
    //每秒获取传感器数据存bg,便于页面使用  setInterval setTimeout
    Background.prototype.BgSensorDataProcess = function (refresh_time) {
        var _this = this;
        var _a;
        chooseSensor_list_xStr = window.localStorage.getItem('chooseSensor_list_v1');
        console.log('数据刷新时间: ' + refresh_time / 1000 + '秒');
        var cpuType = 'intel';
        var GPU0Data = null, GPU1Data = null, GPU2Data = null, isGPUData = false;
        var gpuName0 = null, gpuName1 = null, gpuName2 = null;
        var MemoryData = null;
        var containsGPU = true;
        var mainDisk = { ProductId: "", SerialNumber: "", VendorId: "" };
        try {
            mainDisk = ((_a = gamepp === null || gamepp === void 0 ? void 0 : gamepp.hardware) === null || _a === void 0 ? void 0 : _a.getMainDiskInfo.sync()) || mainDisk;
        }
        catch (_b) {
        }
        // setTimeout,setInterval
        setInterval_chooseSensor_list = setInterval(function () {
            var _a, _b, _c;
            var SensorInfoStr = null;
            try {
                SensorInfoStr = gamepp.hardware.getSensorInfo.sync();
            }
            catch (_d) {
            }
            if (SensorInfoStr && !sensorInfoModifying) {
                var SensorInfoOriginal = JSON.parse(SensorInfoStr);
                var SensorInfoKeys = Object.keys(SensorInfoOriginal);
                if (!isGPUData) {
                    containsGPU = SensorInfoKeys.some(function (name) { return name.includes("GPU"); });
                    SensorInfoKeys.forEach(function (item) {
                        if (item.startsWith('CPU')) {
                            if (String(item).toLowerCase().includes('intel')) {
                                cpuType = 'intel';
                            }
                            else {
                                cpuType = 'amd';
                            }
                        }
                        if (item.startsWith("GPU [#0]")) {
                            gpuName0 = item.split(':')[1].trim();
                        }
                        else if (item.startsWith("GPU [#1]")) {
                            gpuName1 = item.split(':')[1].trim();
                        }
                        else if (item.startsWith("GPU [#2]")) {
                            gpuName2 = item.split(':')[1].trim();
                        }
                    });
                    GPU0Data = _this.GetHardwareDetails('GPU', gpuName0);
                    GPU1Data = _this.GetHardwareDetails('GPU', gpuName1);
                    GPU2Data = _this.GetHardwareDetails('GPU', gpuName2);
                    isGPUData = true;
                    MemoryData = _this.GlobalBaseJsonInfo.MEMORY;
                }
                var SensorInfo_1 = _this.SensorAddAverageData(SensorInfoOriginal, SensorInfoKeys);
                // console.log('%cSensorInfo','color:green',SensorInfo);   //
                var cpu_temp = ['', ''];
                if (cpuType === 'intel') { // Intel CPU 优先选择 CPU Package 或者 Core Max
                    cpu_temp = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', 'CPU Package', 'temperature');
                    if (cpu_temp[1] === '') {
                        cpu_temp = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', 'Core Max', 'temperature');
                    }
                }
                else { // AMD CPU 优先选择 CPU Die (average) 或者 CPU (Tctl/Tdie)
                    cpu_temp = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', 'CPU Die (average)', 'temperature');
                    if (cpu_temp[1] === '') {
                        cpu_temp = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', 'CPU (Tctl/Tdie)', 'temperature');
                    }
                }
                if (cpu_temp[1] === '') { // 都没找到
                    cpu_temp = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', false, 'temperature');
                }
                var cpu_temp_p = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', false, 'temperature');
                var cpu_temp_e = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', 'E-core', 'temperature');
                var cpu_clock = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', false, 'clock');
                var cpu_clock_p = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', 'P Core Clocks', 'clock');
                var cpu_clock_e = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', 'E Core Clocks', 'clock');
                // 电压先从主板找Vcore
                var cpu_voltage = _this.findValueByKeyAndType(SensorInfo_1, 'Mainboard', 'Vcore', 'voltage');
                if (cpu_voltage[1] === '') { // 找不到从CPU里找
                    cpu_voltage = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', false, 'voltage');
                }
                var cpu_voltage_p = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', 'P Core Max VID', 'voltage');
                var cpu_voltage_e = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', 'E Core Max VID', 'voltage');
                var cpu_usage = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', 'Total CPU Usage', 'usage');
                var cpu_usage_p = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', 'P Core Usages', 'usage');
                var cpu_usage_e = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', 'E Core Usages', 'usage');
                var cpu_fan = _this.findValueByKeyAndType(SensorInfo_1, 'Mainboard', 'CPU|Chassis', 'fan');
                if (cpu_fan[0] === '') {
                    cpu_fan = _this.findValueByKeyAndType(SensorInfo_1, 'Mainboard', false, 'fan');
                }
                var cpu_power = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', false, 'power');
                //AMD Enhanced Thermal Limit(AMD温度墙实际百分比)
                var cpu_amd_thermal_limit = _this.findValueByKeyAndType(SensorInfo_1, 'Enhanced', 'Thermal Limit', 'usage');
                //NPU 数据
                var npu_clock = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', 'NPU Clock', 'clock');
                var npu_usage = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', 'NPU Usage', 'usage');
                //================================================================   GPU   ================================================================
                var gpu_temp = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#0]', false, 'temperature');
                var gpu_clock = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#0]', false, 'clock');
                var gpu_mem_clock = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#0]', 'Memory', 'clock');
                var gpu_power = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#0]', false, 'power');
                var gpu_fan = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#0]', false, 'fan');
                var gpu_fan_amd = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#0]', 'Fan PWM', 'other');
                var gpu_fan_unit = 'RPM';
                if (gpu_fan[0] === '' && gpu_fan_amd[0] !== '') {
                    gpu_fan = gpu_fan_amd;
                    gpu_fan_unit = '%';
                }
                var gpu_mem_usage = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#0]', 'Memory Usage', 'usage');
                var gpu_mem_usage_amd = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#0]', 'Memory Dedicated', 'other');
                if (gpu_mem_usage[0] === '' && gpu_mem_usage_amd[0] !== '' && GPU0Data) {
                    gpu_mem_usage = [gpu_mem_usage_amd[0], gameclient.toPercent(gpu_mem_usage_amd[1], GPU0Data === null || GPU0Data === void 0 ? void 0 : GPU0Data.VideoMemoryNumber)];
                }
                var gpu_hot_spot_temp = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#0]', 'Hot Spot Temperature', 'temperature');
                var gpu_total_usage = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#0]', false, 'usage');
                var gpu_d3d_usage = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#0]', 'D3D Usage', 'usage');
                var gpu_mem_temp = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#0]', 'Memory Temperature|Memory Junction Temperature', 'temperature');
                var gpu_voltage = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#0]', 'Voltage', 'voltage');
                if (!containsGPU) {
                    gpu_temp = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', false, 'temperature');
                    gpu_clock = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', 'GPU Clock', 'clock');
                    gpu_total_usage = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', 'GPU D3D Usage', 'usage');
                    gpu_d3d_usage = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', 'GPU D3D Usage', 'usage');
                    gpu_power = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', 'GT Cores Power', 'power');
                    GPU0Data = _this.GlobalBaseJsonInfo.GPU.SubNode[0];
                    gpuName0 = GPU0Data.VideoChipset;
                }
                var gpu_hotspot_thermal_limit = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#0]', 'Hotspot Thermal Limit', 'usage');
                var gpu_memory_thermal_limit = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#0]', 'Memory Thermal Limit', 'usage');
                var gpu_performance_limit = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#0]', 'Performance Limit - Thermal', 'other');
                var gpu_performance_limit1 = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#0]', 'Performance Limit - Reliability Voltage', 'other');
                var gpu_performance_limit2 = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#0]', 'Performance Limit - Max Operating Voltage', 'other');
                //GPU1
                var gpu1_temp = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#1]', false, 'temperature');
                var gpu1_clock = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#1]', false, 'clock');
                var gpu1_mem_clock = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#1]', 'Memory', 'clock');
                var gpu1_power = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#1]', false, 'power');
                var gpu1_fan = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#1]', false, 'fan');
                var gpu1_fan_amd = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#1]', 'Fan PWM', 'other');
                var gpu1_fan_unit = 'RPM';
                if (gpu1_fan[0] === '' && gpu1_fan_amd[0] !== '') {
                    gpu1_fan = gpu1_fan_amd;
                    gpu1_fan_unit = '%';
                }
                var gpu1_mem_usage = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#1]', 'Memory Usage', 'usage');
                var gpu1_mem_usage_amd = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#1]', 'Memory Dedicated', 'other');
                if (gpu1_mem_usage[0] === '' && gpu1_mem_usage_amd[0] !== '' && GPU1Data) {
                    gpu1_mem_usage = [gpu1_mem_usage_amd[0], gameclient.toPercent(gpu1_mem_usage_amd[1], GPU1Data === null || GPU1Data === void 0 ? void 0 : GPU1Data.VideoMemoryNumber)];
                }
                var gpu1_hot_spot_temp = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#1]', 'Hot Spot Temperature', 'temperature');
                var gpu1_total_usage = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#1]', false, 'usage');
                var gpu1_d3d_usage = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#1]', 'D3D Usage', 'usage');
                var gpu1_mem_temp = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#1]', 'Memory Temperature|Memory Junction Temperature', 'temperature');
                var gpu1_voltage = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#1]', 'Voltage', 'voltage');
                var gpu1_hotspot_thermal_limit = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#1]', 'Hotspot Thermal Limit', 'usage');
                var gpu1_memory_thermal_limit = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#1]', 'Memory Thermal Limit', 'usage');
                var gpu1_performance_limit = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#1]', 'Performance Limit - Thermal', 'other');
                var gpu1_performance_limit1 = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#1]', 'Performance Limit - Reliability Voltage', 'other');
                var gpu1_performance_limit2 = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#1]', 'Performance Limit - Max Operating Voltage', 'other');
                //GPU2
                var gpu2_temp = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#2]', false, 'temperature');
                var gpu2_clock = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#2]', false, 'clock');
                var gpu2_mem_clock = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#2]', 'Memory', 'clock');
                var gpu2_power = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#2]', false, 'power');
                var gpu2_fan = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#2]', false, 'fan');
                var gpu2_fan_amd = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#2]', 'Fan PWM', 'other');
                var gpu2_fan_unit = 'RPM';
                if (gpu1_fan[0] === '' && gpu2_fan_amd[0] !== '') {
                    gpu1_fan = gpu2_fan_amd;
                    gpu1_fan_unit = '%';
                }
                var gpu2_mem_usage = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#2]', 'Memory Usage', 'usage');
                var gpu2_mem_usage_amd = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#2]', 'Memory Dedicated', 'other');
                if (gpu2_mem_usage[0] === '' && gpu2_mem_usage_amd[0] !== '' && GPU2Data) {
                    gpu2_mem_usage = [gpu1_mem_usage_amd[0], gameclient.toPercent(gpu1_mem_usage_amd[1], GPU2Data === null || GPU2Data === void 0 ? void 0 : GPU2Data.VideoMemoryNumber)];
                }
                var gpu2_hot_spot_temp = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#2]', 'Hot Spot Temperature', 'temperature');
                var gpu2_total_usage = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#2]', false, 'usage');
                var gpu2_d3d_usage = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#2]', 'D3D Usage', 'usage');
                var gpu2_mem_temp = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#2]', 'Memory Temperature|Memory Junction Temperature', 'temperature');
                var gpu2_voltage = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#2]', 'Voltage', 'voltage');
                var gpu2_hotspot_thermal_limit = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#2]', 'Hotspot Thermal Limit', 'usage');
                var gpu2_memory_thermal_limit = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#2]', 'Memory Thermal Limit', 'usage');
                var gpu2_performance_limit = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#2]', 'Performance Limit - Thermal', 'other');
                var gpu2_performance_limit1 = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#2]', 'Performance Limit - Reliability Voltage', 'other');
                var gpu2_performance_limit2 = _this.findValueByKeyAndType(SensorInfo_1, 'GPU [#2]', 'Performance Limit - Max Operating Voltage', 'other');
                //================================================================   GPUEnd   ================================================================
                var mainboard_temp = _this.findValueByKeyAndType(SensorInfo_1, 'Mainboard', false, 'temperature');
                var mainboard_voltage = _this.findValueByKeyAndType(SensorInfo_1, 'Mainboard', 'Vcore', 'voltage');
                // const drive_temp = this.findValueByKeyAndType(SensorInfo, 'S.M.A.R.T', false, 'temperature')
                var drive_temp = _this.findValueByKeyAndType(SensorInfo_1, 'S.M.A.R.T.: ' + (mainDisk === null || mainDisk === void 0 ? void 0 : mainDisk.ProductId), false, 'temperature');
                var drive_usage = _this.findValueByKeyAndType(SensorInfo_1, 'Drive: ' + (mainDisk === null || mainDisk === void 0 ? void 0 : mainDisk.ProductId), 'Total Activity', 'usage');
                var memory_usage = _this.findValueByKeyAndType(SensorInfo_1, 'System', 'Physical Memory Load', 'other');
                var memory_used_mb = _this.findValueByKeyAndType(SensorInfo_1, 'System', 'Physical Memory Used', 'other');
                var memory_temp = _this.findValueByKeyAndType(SensorInfo_1, 'DIMM', false, 'temperature');
                var memory_clock = _this.findValueByKeyAndType(SensorInfo_1, 'Memory Timings', 'Memory Clock', 'clock');
                var memory_tcas = _this.findValueByKeyAndType(SensorInfo_1, 'Memory Timings', 'Tcas', 'other');
                var memory_trcd = _this.findValueByKeyAndType(SensorInfo_1, 'Memory Timings', 'Trcd', 'other');
                var memory_trp = _this.findValueByKeyAndType(SensorInfo_1, 'Memory Timings', 'Trp', 'other');
                var memory_tras = _this.findValueByKeyAndType(SensorInfo_1, 'Memory Timings', 'Tras', 'other');
                var memory_voltage1 = _this.findValueByKeyAndType(SensorInfo_1, 'Mainboard', 'DRAM', 'voltage');
                var memory_voltage2 = _this.findValueByKeyAndType(SensorInfo_1, 'DIMM', 'VDD|VDDQ', 'voltage');
                var memory_voltage = (memory_voltage1 && memory_voltage1[1] !== '') ? memory_voltage1[1] : (memory_voltage2 && memory_voltage2[1] !== '') ? memory_voltage2[1] : 1.2;
                // const network_download = this.findValueByKeyAndType(SensorInfo, 'Network', 'Current DL', 'other')
                // const network_upload   = this.findValueByKeyAndType(SensorInfo, 'Network', 'Current UP', 'other')
                var network_info = _this.findValueByKeyAndType(SensorInfo_1, 'Network', false, 'other');
                var battery_chargelevel = _this.findValueByKeyAndType(SensorInfo_1, 'Battery', 'Charge Level', 'other');
                var battery_voltage = _this.findValueByKeyAndType(SensorInfo_1, 'Battery', 'Battery Voltage', 'other');
                var windows_hardware_errors = _this.findValueByKeyAndType(SensorInfo_1, 'Windows Hardware Errors', 'Total Errors', 'other');
                var defaultConfiguration = {
                    cpuData: [
                        { name: 'temp', describe: 'CPU温度', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' ℃', outKey: '', inKey: '' },
                        { name: 'tempP', describe: 'CPU温度P', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' ℃', outKey: '', inKey: '' },
                        { name: 'tempE', describe: 'CPU温度E', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' ℃', outKey: '', inKey: '' },
                        { name: 'clock', describe: 'CPU频率', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'MHz', outKey: '', inKey: '' },
                        { name: 'clockP', describe: 'CPU频率P', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'MHz', outKey: '', inKey: '' },
                        { name: 'clockE', describe: 'CPU频率E', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'MHz', outKey: '', inKey: '' },
                        { name: 'usage', describe: 'CPU占用', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' %', outKey: '', inKey: '' },
                        { name: 'usageP', describe: 'CPU占用P', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' %', outKey: '', inKey: '' },
                        { name: 'usageE', describe: 'CPU占用E', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' %', outKey: '', inKey: '' },
                        { name: 'voltage', describe: 'CPU电压', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'V', outKey: '', inKey: '' },
                        { name: 'voltageP', describe: 'CPU电压P', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'V', outKey: '', inKey: '' },
                        { name: 'voltageE', describe: 'CPU电压E', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'V', outKey: '', inKey: '' },
                        { name: 'power', describe: 'CPU热功耗', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'W', outKey: '', inKey: '' },
                        { name: 'fan', describe: 'CPU风扇转速', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' RPM', outKey: '', inKey: '' },
                    ],
                    gpu0Data: [
                        { name: 'temp', describe: 'GPU温度', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' ℃', outKey: '', inKey: '' },
                        { name: 'clock', describe: 'GPU频率', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'MHz', outKey: '', inKey: '' },
                        { name: 'power', describe: 'GPU热功耗', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'W', outKey: '', inKey: '' },
                        { name: 'd3d_usage', describe: 'GPU占用', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' %', outKey: '', inKey: '' },
                        { name: 'total_usage', describe: 'GPU占用', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' %', outKey: '', inKey: '' },
                        { name: 'fan', describe: 'GPU风扇转速', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' ' + gpu_fan_unit, outKey: '', inKey: '' },
                        { name: 'hot spot temp', describe: '核心热点温度', isDef: true, groupIndex: 0, itemIndex: 0, Unit: '℃', outKey: '', inKey: '' },
                        { name: 'mem_usage', describe: '显存使用率', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' %', outKey: '', inKey: '' },
                        { name: 'mem_clock', describe: '显存频率', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'MHz', outKey: '', inKey: '' },
                        { name: 'mem_temp', describe: '显存温度', isDef: true, groupIndex: 0, itemIndex: 0, Unit: '℃', outKey: '', inKey: '' },
                        { name: 'voltage', describe: 'GPU电压', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'V', outKey: '', inKey: '' },
                    ],
                    gpu1Data: [
                        { name: 'temp', describe: 'GPU温度', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' ℃', outKey: '', inKey: '' },
                        { name: 'clock', describe: 'GPU频率', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'MHz', outKey: '', inKey: '' },
                        { name: 'power', describe: 'GPU热功耗', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'W', outKey: '', inKey: '' },
                        { name: 'd3d_usage', describe: 'GPU占用', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' %', outKey: '', inKey: '' },
                        { name: 'total_usage', describe: 'GPU占用', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' %', outKey: '', inKey: '' },
                        { name: 'fan', describe: 'GPU风扇转速', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' ' + gpu1_fan_unit, outKey: '', inKey: '' },
                        { name: 'hot spot temp', describe: '核心热点温度', isDef: true, groupIndex: 0, itemIndex: 0, Unit: '℃', outKey: '', inKey: '' },
                        { name: 'mem_usage', describe: '显存使用率', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' %', outKey: '', inKey: '' },
                        { name: 'mem_clock', describe: '显存频率', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'MHz', outKey: '', inKey: '' },
                        { name: 'mem_temp', describe: '显存温度', isDef: true, groupIndex: 0, itemIndex: 0, Unit: '℃', outKey: '', inKey: '' },
                        { name: 'voltage', describe: 'GPU电压', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'V', outKey: '', inKey: '' },
                    ],
                    gpu2Data: [
                        { name: 'temp', describe: 'GPU2温度', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' ℃', outKey: '', inKey: '' },
                        { name: 'clock', describe: 'GPU频率', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'MHz', outKey: '', inKey: '' },
                        { name: 'power', describe: 'GPU热功耗', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'W', outKey: '', inKey: '' },
                        { name: 'd3d_usage', describe: 'GPU占用', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' %', outKey: '', inKey: '' },
                        { name: 'total_usage', describe: 'GPU占用', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' %', outKey: '', inKey: '' },
                        { name: 'fan', describe: 'GPU风扇转速', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' ' + gpu2_fan_unit, outKey: '', inKey: '' },
                        { name: 'hot spot temp', describe: '核心热点温度', isDef: true, groupIndex: 0, itemIndex: 0, Unit: '℃', outKey: '', inKey: '' },
                        { name: 'mem_usage', describe: '显存使用率', isDef: true, groupIndex: 0, itemIndex: 0, Unit: '  %', outKey: '', inKey: '' },
                        { name: 'mem_clock', describe: '显存频率', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'MHz', outKey: '', inKey: '' },
                        { name: 'mem_temp', describe: '显存温度', isDef: true, groupIndex: 0, itemIndex: 0, Unit: '℃', outKey: '', inKey: '' },
                        { name: 'voltage', describe: 'GPU电压', isDef: true, groupIndex: 0, itemIndex: 0, Unit: 'V', outKey: '', inKey: '' },
                    ],
                    memoryData: [
                        { name: 'usage', describe: '内存占用', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' %', outKey: '', inKey: '' },
                        { name: 'temperature', describe: '内存温度', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' ℃', outKey: '', inKey: '' },
                    ],
                    boardData: [
                        { name: 'temp', describe: '主板温度', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' ℃', outKey: '', inKey: '' }
                    ],
                    networkData: [
                        { name: 'download', describe: '下载', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' KB/s', outKey: '', inKey: '' },
                        { name: 'upload', describe: '上传', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' KB/s', outKey: '', inKey: '' }
                    ],
                    diskData: [],
                    default: true
                };
                var smartCount = 0;
                var originalSMART = [];
                for (var key in SensorInfo_1) {
                    if (key.startsWith("S.M.A.R.T.:")) {
                        smartCount++; // 如果是，递增S.M.A.R.T.的计数器
                        var foundTemperature = false;
                        var key_name = (key.replace(/ \(.*\)$/, '').replace('S.M.A.R.T.: ', ''));
                        for (var _i = 0, _e = SensorInfo_1[key]; _i < _e.length; _i++) {
                            var item = _e[_i];
                            if (foundTemperature) {
                                break;
                            }
                            for (var subKey in item) {
                                if (item[subKey].type === "temperature") {
                                    var disk_temp = parseFloat(item[subKey].value);
                                    var data = {
                                        name: 'temperature',
                                        describe: key_name,
                                        value: disk_temp,
                                        isDef: true,
                                        groupIndex: 0,
                                        itemIndex: 0,
                                        defVal: disk_temp,
                                        Unit: ' ℃',
                                        inGameMonitor: false,
                                        outKey: '',
                                        inKey: ''
                                    };
                                    defaultConfiguration.diskData.push(data);
                                    originalSMART.push(data);
                                    foundTemperature = true;
                                    break;
                                }
                            }
                        }
                    }
                }
                //硬盘更换后 重新更新 local diskData
                if (localSensorListX && localSensorListX.diskData) {
                    var allMatched = true;
                    for (var i = 0; i < Math.min(originalSMART.length, localSensorListX.diskData.length); i++) {
                        if (originalSMART[i].describe !== localSensorListX.diskData[i].describe) {
                            allMatched = false;
                            break;
                        }
                    }
                    if (smartCount !== localSensorListX.diskData.length || !allMatched) {
                        localSensorListX.diskData = originalSMART;
                        window.localStorage.setItem('chooseSensor_list_v1', JSON.stringify(localSensorListX));
                    }
                }
                var _f = [cpu_temp[1], cpu_temp_p[1], cpu_temp_e[1], cpu_usage[1], cpu_usage_p[1], cpu_usage_e[1], cpu_clock[1], cpu_clock_p[1], cpu_clock_e[1], cpu_power[1], cpu_voltage[1], cpu_voltage_p[1], cpu_voltage_e[1], cpu_fan[1]], cpu_temp_value = _f[0], cpu_temp_value_p = _f[1], cpu_temp_value_e = _f[2], cpu_load_value = _f[3], cpu_load_value_p = _f[4], cpu_load_value_e = _f[5], cpu_clock_value = _f[6], cpu_clock_value_p = _f[7], cpu_clock_value_e = _f[8], cpu_power_value = _f[9], cpu_voltage_value = _f[10], cpu_voltage_value_p = _f[11], cpu_voltage_value_e = _f[12], cpu_fan_value = _f[13];
                var _g = [gpu_temp[1], gpu_power[1], gpu_hot_spot_temp[1], gpu_clock[1], gpu_d3d_usage[1], gpu_fan[1], gpu_mem_usage[1], gpu_mem_clock[1], gpu_mem_temp[1], gpu_total_usage[1]], gpu_temp_value = _g[0], gpu_power_value = _g[1], gpu_hot_spot_temp_value = _g[2], gpu_clock_value = _g[3], gpu_d3d_usage_value = _g[4], gpu_fan_value = _g[5], gpu_mem_usage_value = _g[6], gpu_mem_clock_value = _g[7], gpu_mem_temp_value = _g[8], gpu_total_usage_value = _g[9];
                var _h = [gpu1_temp[1], gpu1_power[1], gpu1_hot_spot_temp[1], gpu1_clock[1], gpu1_d3d_usage[1], gpu1_fan[1], gpu1_mem_usage[1], gpu1_mem_clock[1], gpu1_mem_temp[1], gpu1_total_usage[1]], gpu1_temp_value = _h[0], gpu1_power_value = _h[1], gpu1_hot_spot_temp_value = _h[2], gpu1_clock_value = _h[3], gpu1_d3d_usage_value = _h[4], gpu1_fan_value = _h[5], gpu1_mem_usage_value = _h[6], gpu1_mem_clock_value = _h[7], gpu1_mem_temp_value = _h[8], gpu1_total_usage_value = _h[9];
                var _j = [gpu2_temp[1], gpu2_power[1], gpu2_hot_spot_temp[1], gpu2_clock[1], gpu2_d3d_usage[1], gpu2_fan[1], gpu2_mem_usage[1], gpu2_mem_clock[1], gpu1_mem_temp[1], gpu2_total_usage[1]], gpu2_temp_value = _j[0], gpu2_power_value = _j[1], gpu2_hot_spot_temp_value = _j[2], gpu2_clock_value = _j[3], gpu2_d3d_usage_value = _j[4], gpu2_fan_value = _j[5], gpu2_mem_usage_value = _j[6], gpu2_mem_clock_value = _j[7], gpu2_mem_temp_value = _j[8], gpu2_total_usage_value = _j[9];
                // let drive_temp_value = drive_temp[1]
                var mainboard_temp_value = mainboard_temp[1];
                var memory_temp_value = memory_temp[1];
                var memory_usage_value = memory_usage[1];
                // let network_dl_value = network_upload[1]
                // let network_up_value = network_download[1]
                var drive_list_1 = [], drive_usage_list_1 = [], drive_temp_list_1 = [];
                // console.warn('SensorInfo',SensorInfo);
                var obj = Object.keys(SensorInfo_1);
                // console.warn('obj',obj);
                if (chooseSensor_list_xStr) {
                    localSensorListX = JSON.parse(chooseSensor_list_xStr);
                    //================================================================   CPU   ================================================================
                    var _k = localSensorListX.cpuData, temp = _k[0], temp_p = _k[1], temp_e = _k[2], clock = _k[3], clock_p = _k[4], clock_e = _k[5], load = _k[6], load_p = _k[7], load_e = _k[8], voltage = _k[9], voltage_p = _k[10], voltage_e = _k[11], power = _k[12], fan = _k[13];
                    cpu_temp_value = (!temp.isDef || temp.isDefServer) ? _this.SensorInfoDataProcess(temp.isDef, temp.groupIndex, temp.itemIndex, true, SensorInfo_1, temp.outKey, temp.inKey) : cpu_temp[1];
                    cpu_temp_value_p = (!temp_p.isDef || temp_p.isDefServer) ? _this.SensorInfoDataProcess(temp_p.isDef, temp_p.groupIndex, temp_p.itemIndex, true, SensorInfo_1, temp_p.outKey, temp_p.inKey) : cpu_temp_p[1];
                    cpu_temp_value_e = (!temp_e.isDef || temp_e.isDefServer) ? _this.SensorInfoDataProcess(temp_e.isDef, temp_e.groupIndex, temp_e.itemIndex, true, SensorInfo_1, temp_e.outKey, temp_e.inKey) : cpu_temp_e[1];
                    cpu_load_value = (!load.isDef || load.isDefServer) ? _this.SensorInfoDataProcess(load.isDef, load.groupIndex, load.itemIndex, true, SensorInfo_1, load.outKey, load.inKey) : cpu_usage[1];
                    cpu_load_value_p = (!load_p.isDef || load_p.isDefServer) ? _this.SensorInfoDataProcess(load_p.isDef, load_p.groupIndex, load_p.itemIndex, true, SensorInfo_1, load_p.outKey, load_p.inKey) : cpu_usage_p[1];
                    cpu_load_value_e = (!load_e.isDef || load_e.isDefServer) ? _this.SensorInfoDataProcess(load_e.isDef, load_e.groupIndex, load_e.itemIndex, true, SensorInfo_1, load_e.outKey, load_e.inKey) : cpu_usage_e[1];
                    cpu_clock_value = (!clock.isDef || clock.isDefServer) ? _this.SensorInfoDataProcess(clock.isDef, clock.groupIndex, clock.itemIndex, true, SensorInfo_1, clock.outKey, clock.inKey) : cpu_clock[1];
                    cpu_clock_value_p = (!clock_p.isDef || clock_p.isDefServer) ? _this.SensorInfoDataProcess(clock_p.isDef, clock_p.groupIndex, clock_p.itemIndex, true, SensorInfo_1, clock_p.outKey, clock_p.inKey) : cpu_clock_p[1];
                    cpu_clock_value_e = (!clock_e.isDef || clock_e.isDefServer) ? _this.SensorInfoDataProcess(clock_e.isDef, clock_e.groupIndex, clock_e.itemIndex, true, SensorInfo_1, clock_e.outKey, clock_e.inKey) : cpu_clock_e[1];
                    cpu_voltage_value = (!voltage.isDef || voltage.isDefServer) ? _this.SensorInfoDataProcess(voltage.isDef, voltage.groupIndex, voltage.itemIndex, true, SensorInfo_1, voltage.outKey, voltage.inKey) : cpu_voltage[1];
                    cpu_voltage_value_p = (!voltage_p.isDef || voltage_p.isDefServer) ? _this.SensorInfoDataProcess(voltage_p.isDef, voltage_p.groupIndex, voltage_p.itemIndex, true, SensorInfo_1, voltage_p.outKey, voltage_p.inKey) : cpu_voltage_p[1];
                    cpu_voltage_value_e = (!voltage_e.isDef || voltage_e.isDefServer) ? _this.SensorInfoDataProcess(voltage_e.isDef, voltage_e.groupIndex, voltage_e.itemIndex, true, SensorInfo_1, voltage_e.outKey, voltage_e.inKey) : cpu_voltage_e[1];
                    cpu_power_value = (!power.isDef || power.isDefServer) ? _this.SensorInfoDataProcess(power.isDef, power.groupIndex, power.itemIndex, true, SensorInfo_1, power.outKey, power.inKey) : cpu_power[1];
                    cpu_fan_value = (!fan.isDef || fan.isDefServer) ? _this.SensorInfoDataProcess(fan.isDef, fan.groupIndex, fan.itemIndex, true, SensorInfo_1, fan.outKey, fan.inKey, fan.Unit) : cpu_fan[1];
                    //================================================================   GPU   ================================================================
                    var _l = localSensorListX.gpu0Data, gpu0_temp_l = _l[0], gpu0_clock_l = _l[1], gpu0_power_l = _l[2], gpu0_load_d3d_l = _l[3], gpu0_total_usage_l = _l[4], gpu0_fan_l = _l[5], gpu0_hot_temp_l = _l[6], gpu0_mem_usage_l = _l[7], gpu0_mem_clock_l = _l[8], gpu0_mem_temp_l = _l[9];
                    gpu_temp_value = !gpu0_temp_l.isDef ? _this.SensorInfoDataProcess(gpu0_temp_l.isDef, gpu0_temp_l.groupIndex, gpu0_temp_l.itemIndex, true, SensorInfo_1, gpu0_temp_l.outKey, gpu0_temp_l.inKey) : gpu_temp[1];
                    gpu_power_value = !gpu0_power_l.isDef ? _this.SensorInfoDataProcess(gpu0_power_l.isDef, gpu0_power_l.groupIndex, gpu0_power_l.itemIndex, true, SensorInfo_1, gpu0_power_l.outKey, gpu0_power_l.inKey) : gpu_power[1];
                    gpu_hot_spot_temp_value = !gpu0_hot_temp_l.isDef ? _this.SensorInfoDataProcess(gpu0_hot_temp_l.isDef, gpu0_hot_temp_l.groupIndex, gpu0_hot_temp_l.itemIndex, true, SensorInfo_1, gpu0_hot_temp_l.outKey, gpu0_hot_temp_l.inKey) : gpu_hot_spot_temp[1];
                    gpu_clock_value = !gpu0_clock_l.isDef ? _this.SensorInfoDataProcess(gpu0_clock_l.isDef, gpu0_clock_l.groupIndex, gpu0_clock_l.itemIndex, true, SensorInfo_1, gpu0_clock_l.outKey, gpu0_clock_l.inKey) : gpu_clock[1];
                    gpu_d3d_usage_value = !gpu0_load_d3d_l.isDef ? _this.SensorInfoDataProcess(gpu0_load_d3d_l.isDef, gpu0_load_d3d_l.groupIndex, gpu0_load_d3d_l.itemIndex, true, SensorInfo_1, gpu0_load_d3d_l.outKey, gpu0_load_d3d_l.inKey) : gpu_d3d_usage[1];
                    gpu_total_usage_value = !gpu0_total_usage_l.isDef ? _this.SensorInfoDataProcess(gpu0_total_usage_l.isDef, gpu0_total_usage_l.groupIndex, gpu0_total_usage_l.itemIndex, true, SensorInfo_1, gpu0_total_usage_l.outKey, gpu0_total_usage_l.inKey) : gpu_total_usage[1];
                    gpu_fan_value = !gpu0_fan_l.isDef ? _this.SensorInfoDataProcess(gpu0_fan_l.isDef, gpu0_fan_l.groupIndex, gpu0_fan_l.itemIndex, true, SensorInfo_1, gpu0_fan_l.outKey, gpu0_fan_l.inKey) : gpu_fan[1];
                    gpu_mem_usage_value = !gpu0_mem_usage_l.isDef ? _this.SensorInfoDataProcess(gpu0_mem_usage_l.isDef, gpu0_mem_usage_l.groupIndex, gpu0_mem_usage_l.itemIndex, true, SensorInfo_1, gpu0_mem_usage_l.outKey, gpu0_mem_usage_l.inKey) : gpu_mem_usage[1];
                    gpu_mem_clock_value = !gpu0_mem_clock_l.isDef ? _this.SensorInfoDataProcess(gpu0_mem_clock_l.isDef, gpu0_mem_clock_l.groupIndex, gpu0_mem_clock_l.itemIndex, true, SensorInfo_1, gpu0_mem_clock_l.outKey, gpu0_mem_clock_l.inKey) : gpu_mem_clock[1];
                    gpu_mem_temp_value = !gpu0_mem_temp_l.isDef ? _this.SensorInfoDataProcess(gpu0_mem_temp_l.isDef, gpu0_mem_temp_l.groupIndex, gpu0_mem_temp_l.itemIndex, true, SensorInfo_1, gpu0_mem_temp_l.outKey, gpu0_mem_temp_l.inKey) : gpu_mem_temp[1];
                    var _m = localSensorListX.gpu1Data, gpu1_temp_l = _m[0], gpu1_clock_l = _m[1], gpu1_power_l = _m[2], gpu1_load_d3d_l = _m[3], gpu1_total_usage_l = _m[4], gpu1_fan_l = _m[5], gpu1_hot_temp_l = _m[6], gpu1_mem_usage_l = _m[7], gpu1_mem_clock_l = _m[8], gpu1_mem_temp_l = _m[9];
                    gpu1_temp_value = !gpu1_temp_l.isDef ? _this.SensorInfoDataProcess(gpu1_temp_l.isDef, gpu1_temp_l.groupIndex, gpu1_temp_l.itemIndex, true, SensorInfo_1, gpu1_temp_l.outKey, gpu1_temp_l.inKey) : gpu1_temp[1];
                    gpu1_power_value = !gpu1_power_l.isDef ? _this.SensorInfoDataProcess(gpu1_power_l.isDef, gpu1_power_l.groupIndex, gpu1_power_l.itemIndex, true, SensorInfo_1, gpu1_power_l.outKey, gpu1_power_l.inKey) : gpu1_power[1];
                    gpu1_hot_spot_temp_value = !gpu1_hot_temp_l.isDef ? _this.SensorInfoDataProcess(gpu1_hot_temp_l.isDef, gpu1_hot_temp_l.groupIndex, gpu1_hot_temp_l.itemIndex, true, SensorInfo_1, gpu1_hot_temp_l.outKey, gpu1_hot_temp_l.inKey) : gpu1_hot_spot_temp[1];
                    gpu1_clock_value = !gpu1_clock_l.isDef ? _this.SensorInfoDataProcess(gpu1_clock_l.isDef, gpu1_clock_l.groupIndex, gpu1_clock_l.itemIndex, true, SensorInfo_1, gpu1_clock_l.outKey, gpu1_clock_l.inKey) : gpu1_clock[1];
                    gpu1_d3d_usage_value = !gpu1_load_d3d_l.isDef ? _this.SensorInfoDataProcess(gpu1_load_d3d_l.isDef, gpu1_load_d3d_l.groupIndex, gpu1_load_d3d_l.itemIndex, true, SensorInfo_1, gpu1_load_d3d_l.outKey, gpu1_load_d3d_l.inKey) : gpu1_d3d_usage[1];
                    gpu1_total_usage_value = !gpu1_total_usage_l.isDef ? _this.SensorInfoDataProcess(gpu1_total_usage_l.isDef, gpu1_total_usage_l.groupIndex, gpu1_total_usage_l.itemIndex, true, SensorInfo_1, gpu1_total_usage_l.outKey, gpu1_total_usage_l.inKey) : gpu1_total_usage[1];
                    gpu1_fan_value = !gpu1_fan_l.isDef ? _this.SensorInfoDataProcess(gpu1_fan_l.isDef, gpu1_fan_l.groupIndex, gpu1_fan_l.itemIndex, true, SensorInfo_1, gpu1_fan_l.outKey, gpu1_fan_l.inKey) : gpu1_fan[1];
                    gpu1_mem_usage_value = !gpu1_mem_usage_l.isDef ? _this.SensorInfoDataProcess(gpu1_mem_usage_l.isDef, gpu1_mem_usage_l.groupIndex, gpu1_mem_usage_l.itemIndex, true, SensorInfo_1, gpu1_mem_usage_l.outKey, gpu1_mem_usage_l.inKey) : gpu1_mem_usage[1];
                    gpu1_mem_clock_value = !gpu1_mem_clock_l.isDef ? _this.SensorInfoDataProcess(gpu1_mem_clock_l.isDef, gpu1_mem_clock_l.groupIndex, gpu1_mem_clock_l.itemIndex, true, SensorInfo_1, gpu1_mem_clock_l.outKey, gpu1_mem_clock_l.inKey) : gpu1_mem_clock[1];
                    gpu1_mem_temp_value = !gpu1_mem_temp_l.isDef ? _this.SensorInfoDataProcess(gpu1_mem_temp_l.isDef, gpu1_mem_temp_l.groupIndex, gpu1_mem_temp_l.itemIndex, true, SensorInfo_1, gpu1_mem_temp_l.outKey, gpu1_mem_temp_l.inKey) : gpu1_mem_temp[1];
                    var _o = localSensorListX.gpu2Data, gpu2_temp_l = _o[0], gpu2_clock_l = _o[1], gpu2_power_l = _o[2], gpu2_load_d3d_l = _o[3], gpu2_total_usage_l = _o[4], gpu2_fan_l = _o[5], gpu2_hot_temp_l = _o[6], gpu2_mem_usage_l = _o[7], gpu2_mem_clock_l = _o[8], gpu2_mem_temp_l = _o[9];
                    gpu2_temp_value = !gpu2_temp_l.isDef ? _this.SensorInfoDataProcess(gpu2_temp_l.isDef, gpu2_temp_l.groupIndex, gpu2_temp_l.itemIndex, true, SensorInfo_1, gpu2_temp_l.outKey, gpu2_temp_l.inKey) : gpu2_temp[1];
                    gpu2_power_value = !gpu2_power_l.isDef ? _this.SensorInfoDataProcess(gpu2_power_l.isDef, gpu2_power_l.groupIndex, gpu2_power_l.itemIndex, true, SensorInfo_1, gpu2_power_l.outKey, gpu2_power_l.inKey) : gpu2_power[1];
                    gpu2_hot_spot_temp_value = !gpu2_hot_temp_l.isDef ? _this.SensorInfoDataProcess(gpu2_hot_temp_l.isDef, gpu2_hot_temp_l.groupIndex, gpu2_hot_temp_l.itemIndex, true, SensorInfo_1, gpu2_hot_temp_l.outKey, gpu2_hot_temp_l.inKey) : gpu2_hot_spot_temp[1];
                    gpu2_clock_value = !gpu2_clock_l.isDef ? _this.SensorInfoDataProcess(gpu2_clock_l.isDef, gpu2_clock_l.groupIndex, gpu2_clock_l.itemIndex, true, SensorInfo_1, gpu2_clock_l.outKey, gpu2_clock_l.inKey) : gpu2_clock[1];
                    gpu2_d3d_usage_value = !gpu2_load_d3d_l.isDef ? _this.SensorInfoDataProcess(gpu2_load_d3d_l.isDef, gpu2_load_d3d_l.groupIndex, gpu2_load_d3d_l.itemIndex, true, SensorInfo_1, gpu2_load_d3d_l.outKey, gpu2_load_d3d_l.inKey) : gpu2_d3d_usage[1];
                    gpu2_total_usage_value = !gpu2_total_usage_l.isDef ? _this.SensorInfoDataProcess(gpu2_total_usage_l.isDef, gpu2_total_usage_l.groupIndex, gpu2_total_usage_l.itemIndex, true, SensorInfo_1, gpu2_total_usage_l.outKey, gpu2_total_usage_l.inKey) : gpu2_total_usage[1];
                    gpu2_fan_value = !gpu2_fan_l.isDef ? _this.SensorInfoDataProcess(gpu2_fan_l.isDef, gpu2_fan_l.groupIndex, gpu2_fan_l.itemIndex, true, SensorInfo_1, gpu2_fan_l.outKey, gpu2_fan_l.inKey) : gpu2_fan[1];
                    gpu2_mem_usage_value = !gpu2_mem_usage_l.isDef ? _this.SensorInfoDataProcess(gpu2_mem_usage_l.isDef, gpu2_mem_usage_l.groupIndex, gpu2_mem_usage_l.itemIndex, true, SensorInfo_1, gpu2_mem_usage_l.outKey, gpu2_mem_usage_l.inKey) : gpu2_mem_usage[1];
                    gpu2_mem_clock_value = !gpu2_mem_clock_l.isDef ? _this.SensorInfoDataProcess(gpu2_mem_clock_l.isDef, gpu2_mem_clock_l.groupIndex, gpu2_mem_clock_l.itemIndex, true, SensorInfo_1, gpu2_mem_clock_l.outKey, gpu2_mem_clock_l.inKey) : gpu2_mem_clock[1];
                    gpu2_mem_temp_value = !gpu2_mem_temp_l.isDef ? _this.SensorInfoDataProcess(gpu2_mem_temp_l.isDef, gpu2_mem_temp_l.groupIndex, gpu2_mem_temp_l.itemIndex, true, SensorInfo_1, gpu2_mem_temp_l.outKey, gpu2_mem_temp_l.inKey) : gpu2_mem_temp[1];
                    //================================================================   硬盘   ================================================================
                    // const temp2 = localSensorListX.diskData[0]
                    // if (temp2) {
                    //     drive_temp_value = !temp2.isDef ? this.SensorInfoDataProcess(temp2.isDef, temp2.groupIndex, temp2.itemIndex, true, SensorInfo) : drive_temp[1];
                    // }
                    //================================================================   主板   ================================================================
                    var board_temp_l = localSensorListX.boardData[0];
                    mainboard_temp_value = (!board_temp_l.isDef || board_temp_l.isDefServer) ? _this.SensorInfoDataProcess(board_temp_l.isDef, board_temp_l.groupIndex, board_temp_l.itemIndex, true, SensorInfo_1, board_temp_l.outKey, board_temp_l.inKey) : mainboard_temp[1];
                    //================================================================   内存   ================================================================
                    var memory_temp_l = localSensorListX.memoryData[1];
                    memory_temp_value = !memory_temp_l.isDef ? _this.SensorInfoDataProcess(memory_temp_l.isDef, memory_temp_l.groupIndex, memory_temp_l.itemIndex, true, SensorInfo_1, memory_temp_l.outKey, memory_temp_l.inKey) : memory_temp[1];
                    var memory_load_l = localSensorListX.memoryData[0];
                    memory_usage_value = !memory_load_l.isDef ? _this.SensorInfoDataProcess(memory_load_l.isDef, memory_load_l.groupIndex, memory_load_l.itemIndex, true, SensorInfo_1, memory_load_l.outKey, memory_load_l.inKey) : memory_usage[1];
                    //================================================================   网络   ================================================================
                    // const [network_dl_l, network_up_l] = localSensorListX.networkData;
                    // network_dl_value = !network_dl_l.isDef ? this.SensorInfoDataProcess(network_dl_l.isDef, network_dl_l.groupIndex, network_dl_l.itemIndex, true, SensorInfo) : network_download[1];
                    // network_up_value = !network_up_l.isDef ? this.SensorInfoDataProcess(network_up_l.isDef, network_up_l.groupIndex, network_up_l.itemIndex, true, SensorInfo) : network_upload[1];
                    localSensorListX.diskData.forEach(function (disk, index) {
                        if (disk.inGameMonitor) {
                            var disk_value = disk.value;
                            if (!disk.isDef) {
                                disk_value = _this.SensorInfoDataProcess(false, disk.groupIndex, disk.itemIndex, false, SensorInfo_1, disk.outKey, disk.inKey);
                            }
                            drive_list_1.push(disk_value);
                        }
                        var disk_usage_value = [0, 0];
                        if (disk.describe) {
                            disk_usage_value = _this.findValueByKeyAndType(SensorInfo_1, 'Drive: ' + disk.describe, 'Total Activity', 'usage');
                        }
                        drive_usage_list_1.push(parseInt(disk_usage_value[1]));
                        var disk_temp_value = _this.findValueByKeyAndType(SensorInfo_1, 'S.M.A.R.T.: ' + disk.describe, 'Temperature', 'temperature');
                        drive_temp_list_1.push(parseInt(disk_temp_value[1]));
                    });
                }
                else {
                    window.localStorage.setItem('chooseSensor_list_v1', JSON.stringify(defaultConfiguration));
                    chooseSensor_list_xStr = JSON.stringify(defaultConfiguration);
                }
                ///////////////////// 传感器全匹配查找/////////////////////////
                var res = localStorage.getItem('chooseSensor_list_v1');
                var list_v1 = JSON.parse(res);
                // console.warn('list_v1',list_v1);
                // console.warn('sadsaaaaaaaaaaaaaaaddddddddddddddddsa',SensorInfo);
                if (!list_v1['cpuData'][0].hasOwnProperty('outKey')) {
                    console.warn('更新脚本local');
                    window.localStorage.setItem('chooseSensor_list_v1', JSON.stringify(defaultConfiguration));
                    chooseSensor_list_xStr = JSON.stringify(defaultConfiguration);
                }
                var gpu_mem_usage_str = ((GPU0Data === null || GPU0Data === void 0 ? void 0 : GPU0Data.VideoMemoryNumber) * gpu_mem_usage_value) / 100;
                gpu_mem_usage_str = isNaN(gpu_mem_usage_str) ? 0 : gpu_mem_usage_str;
                var gpu1_mem_usage_str = ((GPU1Data === null || GPU1Data === void 0 ? void 0 : GPU1Data.VideoMemoryNumber) * gpu1_mem_usage_value) / 100;
                gpu1_mem_usage_str = isNaN(gpu1_mem_usage_str) ? 0 : gpu1_mem_usage_str;
                var gpu2_mem_usage_str = ((GPU2Data === null || GPU2Data === void 0 ? void 0 : GPU2Data.VideoMemoryNumber) * gpu2_mem_usage_value) / 100;
                gpu2_mem_usage_str = isNaN(gpu2_mem_usage_str) ? 0 : gpu2_mem_usage_str;
                var Drive_Read = _this.findValueByKeyAndType(SensorInfo_1, 'Drive:', 'Read Rate', 'other'); //null
                var Drive_Write = _this.findValueByKeyAndType(SensorInfo_1, 'Drive:', 'Write Rate', 'other'); //null
                //limit
                var PL1 = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', 'IA: Package-Level RAPL/PBM PL1', 'other');
                var PL23 = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', 'IA: Package-Level RAPL/PBM PL2,PL3', 'other');
                var elecTri = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', 'IA: Electrical Design Point/Other (ICCmax,PL4,SVID,DDR RAPL)', 'other');
                var PPT = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', 'CPU PPT Limit', 'usage');
                var EDC = _this.findValueByKeyAndType(SensorInfo_1, 'CPU', 'CPU EDC Limit', 'usage');
                console.log('%cPPT', 'color:red', PPT);
                console.log('%cEDC', 'color:red', EDC);
                var limitAll = {
                    PL1: PL1,
                    PL23: PL23,
                    elecTri: elecTri,
                    PPT: PPT,
                    EDC: EDC,
                };
                //DDR5
                var ddr5_temp_1 = _this.findValueByKeyAndType(SensorInfo_1, 'DDR5 DIMM [#0]', false, 'temperature'); //null
                var ddr5_temp_2 = _this.findValueByKeyAndType(SensorInfo_1, 'DDR5 DIMM [#1]', false, 'temperature'); //null
                var ddr5_temp_3 = _this.findValueByKeyAndType(SensorInfo_1, 'DDR5 DIMM [#2]', false, 'temperature'); //null
                var ddr5_temp_4 = _this.findValueByKeyAndType(SensorInfo_1, 'DDR5 DIMM [#3]', false, 'temperature'); //null
                var ddr5_temp_5 = _this.findValueByKeyAndType(SensorInfo_1, 'DDR5 DIMM [#4]', false, 'temperature'); //null
                var ddr5_voltage_1 = _this.findValueByKeyAndType(SensorInfo_1, 'DDR5 DIMM [#0]', false, 'voltage'); //null
                var ddr5_voltage_2 = _this.findValueByKeyAndType(SensorInfo_1, 'DDR5 DIMM [#1]', false, 'voltage'); //null
                var ddr5_voltage_3 = _this.findValueByKeyAndType(SensorInfo_1, 'DDR5 DIMM [#2]', false, 'voltage'); //null
                var ddr5_voltage_4 = _this.findValueByKeyAndType(SensorInfo_1, 'DDR5 DIMM [#3]', false, 'voltage'); //null
                var ddr5_voltage_5 = _this.findValueByKeyAndType(SensorInfo_1, 'DDR5 DIMM [#4]', false, 'voltage'); //null
                var ddr5_temperature = {
                    '#1': ddr5_temp_1[0] === '' ? null : ddr5_temp_1[1],
                    '#2': ddr5_temp_2[0] === '' ? null : ddr5_temp_2[1],
                    '#3': ddr5_temp_3[0] === '' ? null : ddr5_temp_3[1],
                    '#4': ddr5_temp_4[0] === '' ? null : ddr5_temp_4[1],
                    '#5': ddr5_temp_5[0] === '' ? null : ddr5_temp_5[1],
                };
                var ddr5_voltage = {
                    '#1': ddr5_voltage_1[0] === '' ? null : ddr5_voltage_1[1],
                    '#2': ddr5_voltage_2[0] === '' ? null : ddr5_voltage_2[1],
                    '#3': ddr5_voltage_3[0] === '' ? null : ddr5_voltage_3[1],
                    '#4': ddr5_voltage_4[0] === '' ? null : ddr5_voltage_4[1],
                    '#5': ddr5_voltage_5[0] === '' ? null : ddr5_voltage_5[1],
                };
                // 性能限制数据
                var organizeSensorInfoData = _this.organizeSensorInfo(SensorInfo_1);
                var gpu0_mem_usage_mb = Math.floor(gpu_mem_usage_str) + 'M/' + ((GPU0Data === null || GPU0Data === void 0 ? void 0 : GPU0Data.VideoMemoryNumber) || 0) + 'M';
                var gpu1_mem_usage_mb = Math.floor(gpu1_mem_usage_str) + 'M/' + ((GPU1Data === null || GPU1Data === void 0 ? void 0 : GPU1Data.VideoMemoryNumber) || 0) + 'M';
                var gpu2_mem_usage_mb = Math.floor(gpu2_mem_usage_str) + 'M/' + ((GPU2Data === null || GPU2Data === void 0 ? void 0 : GPU2Data.VideoMemoryNumber) || 0) + 'M';
                var data_arr_1 = {
                    cpu: {
                        name: _this.GlobalBaseJsonInfo.CPU && _this.GlobalBaseJsonInfo.CPU.SubNode ? _this.GlobalBaseJsonInfo.CPU.SubNode[0].ProcessorName : 'Null',
                        temp: _this.CheckValue(cpu_temp_value),
                        tempP: _this.CheckValue(cpu_temp_value_p),
                        tempE: _this.CheckValue(cpu_temp_value_e),
                        clock: _this.CheckValue(cpu_clock_value),
                        clockP: _this.CheckValue(cpu_clock_value_p),
                        clockE: _this.CheckValue(cpu_clock_value_e),
                        usage: _this.checkValueWithLimit(cpu_load_value),
                        usageP: _this.CheckValue(cpu_load_value_p),
                        usageE: _this.CheckValue(cpu_load_value_e),
                        voltage: _this.CheckValue(cpu_voltage_value),
                        voltageP: _this.CheckValue(cpu_voltage_value_p),
                        voltageE: _this.CheckValue(cpu_voltage_value_e),
                        power: _this.CheckValue(cpu_power_value),
                        fan: _this.CheckValue(cpu_fan_value),
                        limit: organizeSensorInfoData['PLR'],
                        core_info: organizeSensorInfoData['Core'],
                        effective_clock: organizeSensorInfoData['EffectiveClock'],
                        thread: organizeSensorInfoData['Thread'],
                        amd_thermal: _this.CheckValue(cpu_amd_thermal_limit[1]),
                        npu_clock: _this.CheckValue(npu_clock[1]),
                        npu_usage: _this.CheckValue(npu_usage[1]),
                        limitAll: limitAll
                    },
                    gpu_list: [
                        {
                            temp: _this.CheckValue(gpu_temp_value),
                            hot_spot_temp: _this.CheckValue(gpu_hot_spot_temp_value),
                            clock: _this.CheckValue(gpu_clock_value),
                            power: _this.CheckValue(gpu_power_value),
                            fan: _this.CheckValue(gpu_fan_value),
                            d3d_usage: _this.CheckValue(gpu_d3d_usage_value),
                            total_usage: _this.CheckValue(gpu_total_usage_value),
                            usage_str: _this.CheckValue(gpu_d3d_usage_value) + '%/' + _this.CheckValue(gpu_total_usage_value) + '%',
                            mem_usage: _this.CheckValue(gpu_mem_usage_value),
                            mem_usage_mb: gpu0_mem_usage_mb === "0M/0M" ? null : gpu0_mem_usage_mb,
                            mem_clock: _this.CheckValue(gpu_mem_clock_value),
                            mem_temp: _this.CheckValue(gpu_mem_temp_value),
                            mem_size: GPU0Data === null || GPU0Data === void 0 ? void 0 : GPU0Data.VideoMemoryNumber,
                            name: gpuName0,
                            shadres: Number(GPU0Data === null || GPU0Data === void 0 ? void 0 : GPU0Data.NumberOfUnifiedShaders) || Number(GPU0Data === null || GPU0Data === void 0 ? void 0 : GPU0Data['NumberOfALUs(cores)']),
                            videobus: GPU0Data === null || GPU0Data === void 0 ? void 0 : GPU0Data.VideoBus,
                            voltage: _this.CheckValue(gpu_voltage[1]),
                            thermal_hotspot: _this.CheckValue(gpu_hotspot_thermal_limit[1]),
                            thermal_memory: _this.CheckValue(gpu_memory_thermal_limit[1]),
                            pl_thermal: _this.CheckValue(gpu_performance_limit[1]),
                            pl_reliability_voltage: _this.CheckValue(gpu_performance_limit1[1]),
                            pl_max_operating_voltage: _this.CheckValue(gpu_performance_limit2[1]),
                            power_list: (_a = organizeSensorInfoData === null || organizeSensorInfoData === void 0 ? void 0 : organizeSensorInfoData.GPUTDP[0]) === null || _a === void 0 ? void 0 : _a.TDP
                        }, {
                            temp: _this.CheckValue(gpu1_temp_value),
                            hot_spot_temp: _this.CheckValue(gpu1_hot_spot_temp_value),
                            clock: _this.CheckValue(gpu1_clock_value),
                            power: _this.CheckValue(gpu1_power_value),
                            fan: _this.CheckValue(gpu1_fan_value),
                            d3d_usage: _this.CheckValue(gpu1_d3d_usage_value),
                            total_usage: _this.CheckValue(gpu1_total_usage_value),
                            usage_str: _this.CheckValue(gpu1_d3d_usage_value) + '%/' + _this.CheckValue(gpu1_total_usage_value) + '%',
                            mem_usage: _this.CheckValue(gpu1_mem_usage_value),
                            mem_usage_mb: gpu1_mem_usage_mb === "0M/0M" ? null : gpu1_mem_usage_mb,
                            mem_clock: _this.CheckValue(gpu1_mem_clock_value),
                            mem_temp: _this.CheckValue(gpu1_mem_temp_value),
                            mem_size: GPU1Data === null || GPU1Data === void 0 ? void 0 : GPU1Data.VideoMemoryNumber,
                            name: gpuName1,
                            shadres: Number(GPU1Data === null || GPU1Data === void 0 ? void 0 : GPU1Data.NumberOfUnifiedShaders) || Number(GPU1Data === null || GPU1Data === void 0 ? void 0 : GPU1Data['NumberOfALUs(cores)']),
                            videobus: GPU1Data === null || GPU1Data === void 0 ? void 0 : GPU1Data.VideoBus,
                            voltage: _this.CheckValue(gpu1_voltage[1]),
                            thermal_hotspot: _this.CheckValue(gpu1_hotspot_thermal_limit[1]),
                            thermal_memory: _this.CheckValue(gpu1_memory_thermal_limit[1]),
                            pl_thermal: _this.CheckValue(gpu1_performance_limit[1]),
                            pl_reliability_voltage: _this.CheckValue(gpu1_performance_limit1[1]),
                            pl_max_operating_voltage: _this.CheckValue(gpu1_performance_limit2[1]),
                            power_list: (_b = organizeSensorInfoData === null || organizeSensorInfoData === void 0 ? void 0 : organizeSensorInfoData.GPUTDP[1]) === null || _b === void 0 ? void 0 : _b.TDP
                        }, {
                            temp: _this.CheckValue(gpu2_temp_value),
                            hot_spot_temp: _this.CheckValue(gpu2_hot_spot_temp_value),
                            clock: _this.CheckValue(gpu2_clock_value),
                            power: _this.CheckValue(gpu2_power_value),
                            fan: _this.CheckValue(gpu2_fan_value),
                            d3d_usage: _this.CheckValue(gpu2_d3d_usage_value),
                            total_usage: _this.CheckValue(gpu2_total_usage_value),
                            usage_str: _this.CheckValue(gpu2_d3d_usage_value) + '%/' + _this.CheckValue(gpu2_total_usage_value) + '%',
                            mem_usage: _this.CheckValue(gpu2_mem_usage_value),
                            mem_usage_mb: gpu2_mem_usage_mb === "0M/0M" ? null : gpu2_mem_usage_mb,
                            mem_clock: _this.CheckValue(gpu2_mem_clock_value),
                            mem_temp: _this.CheckValue(gpu2_mem_temp_value),
                            mem_size: GPU2Data === null || GPU2Data === void 0 ? void 0 : GPU2Data.VideoMemoryNumber,
                            name: gpuName2,
                            shadres: Number(GPU2Data === null || GPU2Data === void 0 ? void 0 : GPU2Data.NumberOfUnifiedShaders) || Number(GPU2Data === null || GPU2Data === void 0 ? void 0 : GPU2Data['NumberOfALUs(cores)']),
                            videobus: GPU2Data === null || GPU2Data === void 0 ? void 0 : GPU2Data.VideoBus,
                            voltage: _this.CheckValue(gpu2_voltage[1]),
                            thermal_hotspot: _this.CheckValue(gpu2_hotspot_thermal_limit[1]),
                            thermal_memory: _this.CheckValue(gpu2_memory_thermal_limit[1]),
                            pl_thermal: _this.CheckValue(gpu2_performance_limit[1]),
                            pl_reliability_voltage: _this.CheckValue(gpu2_performance_limit1[1]),
                            pl_max_operating_voltage: _this.CheckValue(gpu2_performance_limit2[1]),
                            power_list: (_c = organizeSensorInfoData === null || organizeSensorInfoData === void 0 ? void 0 : organizeSensorInfoData.GPUTDP[2]) === null || _c === void 0 ? void 0 : _c.TDP
                        }
                    ],
                    mainboard: {
                        temp: _this.checkValueWithLimit(mainboard_temp_value),
                        voltage: _this.CheckValue(mainboard_voltage[1])
                    },
                    drive: {
                        temp: drive_temp_list_1[drive_index],
                        sys_temp: _this.CheckValue(drive_temp[1]),
                        usage: _this.CheckValue(drive_usage[1]),
                        temp_list: drive_list_1,
                        usage_list: drive_usage_list_1,
                        temp_list_all: drive_temp_list_1
                    },
                    memory: {
                        name: MemoryData && MemoryData.SubNode ? MemoryData.SubNode[0].ModuleManufacturer + ' ' + (MemoryData.SubNode[0].MemoryType).split(' ')[0] : "null",
                        temp: _this.CheckValue(memory_temp_value),
                        usage: _this.CheckValue(memory_usage_value),
                        usage_mb: memory_used_mb[1],
                        size: MemoryData && MemoryData.Property ? Number(MemoryData.Property['TotalMemorySize[MB]']) : 0,
                        clock: Number(memory_clock[1] * 2),
                        channel: MemoryData && (MemoryData === null || MemoryData === void 0 ? void 0 : MemoryData.Property) ? Number(MemoryData === null || MemoryData === void 0 ? void 0 : MemoryData.Property.MemoryChannelsActive) : 0,
                        tcas: memory_tcas[1],
                        trcd: memory_trcd[1],
                        trp: memory_trp[1],
                        tras: memory_tras[1],
                        voltage: memory_voltage,
                        ddr5_temp: ddr5_temperature,
                        ddr5_voltage: ddr5_voltage
                    },
                    network: {
                        download: network_info[0],
                        upload: network_info[1]
                    },
                    battery: {
                        chargelevel: _this.CheckValue(battery_chargelevel[1]),
                        voltage: _this.CheckValue(battery_voltage[1])
                    },
                    whea: _this.CheckValue(windows_hardware_errors[1])
                };
                var gpu_names = [gpuName0, gpuName1, gpuName2];
                var isAllNull = gpu_names.every(function (item) { return item === null; });
                if (!isAllNull) {
                    gpu_names.forEach(function (name, index) {
                        if (!name) {
                            data_arr_1.gpu_list[index] = [];
                        }
                    });
                    var nonEmptyObjects = data_arr_1.gpu_list.filter(function (item) { return item && typeof item === 'object' && Object.keys(item).length > 0; });
                    if (nonEmptyObjects.length === 1) {
                        gpu_index = data_arr_1.gpu_list.findIndex(function (item) { return item && typeof item === 'object' && Object.keys(item).length > 0; });
                    }
                    data_arr_1.gpu = data_arr_1.gpu_list[gpu_index];
                }
                else {
                    data_arr_1.gpu_list[1] = [];
                    data_arr_1.gpu_list[2] = [];
                    data_arr_1.gpu = data_arr_1.gpu_list[0];
                }
                // console.log(data_arr)
                window.localStorage.setItem('bg_sensor_data', JSON.stringify(data_arr_1));
                // Broadcast Channel API
                bc.postMessage(data_arr_1); // 发送数据
                /** 其他窗口接收
                 * const bc = new BroadcastChannel('bg_sensor_data');
                 * bc.onmessage = function(event) {
                 *     console.log(event.data);
                 * };
                 */
            }
        }, refresh_time);
    };
    //每秒获取收藏的传感器数据存bg,便于页面使用  setInterval setTimeout
    Background.prototype.StarSensorDataProcess = function (refresh_time) {
        var _this = this;
        setInterval_starSensor_list = setInterval(function () {
            var SensorList = [];
            var Star_Sensor_Data = [];
            var sensor_collected = window.localStorage.getItem('collected_sensor_list');
            if (sensor_collected) {
                sensor_collected = JSON.parse(sensor_collected);
            }
            else {
                sensor_collected = [];
            }
            var SensorInfoStr = '';
            try {
                SensorInfoStr = gamepp.hardware.getSensorInfo.sync();
            }
            catch (_a) {
                SensorInfoStr = JSON.stringify({});
            }
            var booleanArr = ['Critical Temperature', 'Thermal Throttling', 'Power Limit Exceeded', 'IA: ', 'GT: ', 'RING:', 'Drive Failure', 'Drive Warning', 'Chassis Intrusion', 'Performance Limit'];
            var SensorInfo = JSON.parse(SensorInfoStr);
            // console.warn('传感器数据：：',SensorInfo);
            var SensorInfoKeys = Object.keys(SensorInfo);
            var SensorInfoTidyUp = _this.SensorAddAverageData(SensorInfo, SensorInfoKeys);
            var regex = /^(.*?)(:|\s|$)/; // 匹配第一个冒号或空格之前的内容
            for (var i = 0; i < SensorInfoKeys.length; i++) {
                var SensorInfoKey = SensorInfoKeys[i];
                var Datas = SensorInfoTidyUp[SensorInfoKey];
                var SensorObj = {};
                var SensorListText = '';
                SensorObj.name = SensorInfoKey;
                var match = SensorInfoKey.match(regex);
                SensorObj.type = match ? match[1].trim() : '';
                SensorObj.Sensoritem = [];
                // if (!Datas) continue
                if (!Datas) {
                    Datas = [{ Null: '', name: '' }];
                }
                var _loop_2 = function (j) {
                    var Key = Object.keys(Datas[j])[0];
                    var Data = Datas[j][Key];
                    var SensoritemObj = {};
                    var ProcessKey = ProcessSensorUnit(Key, Data.type);
                    if (!SensorListText.includes(ProcessKey.UnitText)) {
                        SensorListText += ProcessKey.UnitText;
                    }
                    if (booleanArr.find(function (item) { return Key.includes(item); })) {
                        var booleanValue = 'No';
                        if (Number(Data.value)) {
                            booleanValue = 'Yes';
                        }
                        SensoritemObj.value = booleanValue;
                    }
                    else {
                        if (Data.value) {
                            SensoritemObj.value = Number(Data.value).toFixed(ProcessKey.ToFixed);
                        }
                        else {
                            SensoritemObj.value = 'Null';
                        }
                    }
                    SensoritemObj.name = Key;
                    SensoritemObj.unit = ProcessKey.DataUnit;
                    SensorObj.Sensoritem.push(SensoritemObj);
                };
                for (var j = 0; j < Datas.length; j++) {
                    _loop_2(j);
                }
                SensorObj.UnitText = SensorListText;
                SensorList.push(SensorObj);
            }
            if (sensor_collected.length > 0) {
                sensor_collected.forEach(function (item) {
                    try {
                        var obj = SensorList[item.OutIndex]['Sensoritem'][item.InnerIndex];
                        Star_Sensor_Data.push(obj);
                    }
                    catch (e) { }
                });
                window.localStorage.setItem('star_sensor_data', JSON.stringify(Star_Sensor_Data));
            }
            else {
                window.localStorage.setItem('star_sensor_data', JSON.stringify([]));
            }
        }, refresh_time);
        function ProcessSensorUnit(Key, DataType) {
            var condition_MB = ['Memory Dynamic', 'Read Total', 'Write Total', 'Memory Allocated', 'Memory Dedicated', 'Total DL', 'Total UP', 'Memory Committed', 'Memory Available', 'Memory Used', 'GPU Memory Usage'];
            var condition_GB = ['Total Host Writes', 'Total Host Reads'];
            var condition_T = ['Tcas', 'Trcd', 'Trp', 'Tras', 'Trc', 'Trfc', 'Command Rate'];
            var condition_KBs = ['Current DL rate', 'Current UP rate'];
            var DataUnit = '';
            var ToFixed = 0;
            var UnitText = '';
            if (DataType === 'temperature') {
                DataUnit = ' ℃';
                UnitText = ' 温度';
            }
            else if (DataType === 'voltage') {
                DataUnit = ' V';
                ToFixed = 3;
                UnitText = ' 电压';
            }
            else if (DataType === 'fan') {
                DataUnit = ' RPM';
                UnitText = ' 转速';
            }
            else if (DataType === 'power') {
                DataUnit = ' W';
                ToFixed = 3;
                UnitText = ' 功耗';
            }
            else if (DataType === 'clock') {
                DataUnit = ' MHz';
                UnitText = ' 频率';
            }
            else if (DataType === 'usage') {
                DataUnit = ' %';
                ToFixed = 1;
                UnitText = ' 占用';
            }
            else if (DataType === 'other') {
                if (Key.includes('Ratio')) {
                    DataUnit = ' x';
                    ToFixed = 1;
                }
                else if (condition_MB.find(function (item) { return Key.includes(item); })) {
                    DataUnit = ' MB';
                }
                else if (['Read Rate', 'Write Rate'].includes(Key)) {
                    DataUnit = ' MB/s';
                }
                else if (Key.match(new RegExp('^GPU Fan[0-9]$')) || Key === 'GPU Fan PWM') {
                    DataUnit = ' %';
                }
                else if (Key.includes('[% of TDP]') || Key.includes('Memory Load') || Key.includes('Page File Usage')) {
                    DataUnit = ' %';
                    ToFixed = 1;
                    UnitText = ' 占用';
                }
                else if (Key === 'PCIe Link Speed') {
                    DataUnit = ' GT/s';
                    ToFixed = 1;
                }
                else if (condition_T.find(function (item) { return Key.includes(item); })) {
                    DataUnit = ' T';
                }
                else if (condition_KBs.find(function (item) { return Key.includes(item); })) {
                    DataUnit = ' KB/s';
                    ToFixed = 3;
                }
                else if (condition_GB.find(function (item) { return Key.includes(item); })) {
                    DataUnit = ' GB';
                }
            }
            var TypeData = {};
            TypeData.DataUnit = DataUnit;
            TypeData.ToFixed = ToFixed;
            TypeData.UnitText = UnitText;
            return TypeData;
        }
    };
    Background.prototype.CheckValue = function (value) {
        return value !== '' ? value : null;
    };
    Background.prototype.checkValueWithLimit = function (value) {
        var result = this.CheckValue(value);
        // 如果结果是数字且大于100，则限制为100
        if (typeof result === 'number' && result > 100) {
            result = 100;
        }
        return result;
    };
    Background.prototype.GetHardwareDetails = function (type, name) {
        if (!name || !this.GlobalBaseJsonInfo[type] || !this.GlobalBaseJsonInfo[type].SubNode)
            return null;
        var node = this.GlobalBaseJsonInfo[type].SubNode.find(function (node) { return node.VideoChipset === name || name.includes(node.VideoChipset); });
        return node || null;
    };
    Background.prototype.findValueByKeyAndType = function (data, key, keySubstring, type) {
        if (keySubstring === void 0) { keySubstring = false; }
        var ToFixed = 0;
        if (type.includes('voltage')) {
            ToFixed = 3;
        }
        var key1 = false, key2 = false;
        if (key === 'Mainboard') {
            var sift = JSON.parse(JSON.stringify(data)); //深拷贝
            var keywords = ['CPU', 'GPU', 'Memory', 'S.M.A.R.T', 'System:', 'Windows Hardware', 'Drive', 'Network', 'DIMM'];
            var filteredData = {};
            for (var key1_1 in sift) {
                var shouldKeep = true;
                for (var _i = 0, keywords_1 = keywords; _i < keywords_1.length; _i++) {
                    var keyword = keywords_1[_i];
                    if (data[key1_1] === null || key1_1.toLowerCase().includes(keyword.toLowerCase())) {
                        shouldKeep = false;
                        break;
                    }
                }
                if (shouldKeep) {
                    filteredData[key1_1] = sift[key1_1];
                }
            }
            data = filteredData;
            if (type === 'temperature' && Object.keys(data).some(function (key) { return key.includes('Maxsun'); })) {
                keySubstring = 'Temp5';
            } // 铭瑄主板温度异常,厂商要求取 Temp5
            if (type === 'temperature' && Object.keys(data).some(function (key) { return key.includes('BIOSTAR Group B850MT-E PRO'); })) {
                keySubstring = 'Temperature 3';
            } // 映泰主板温度异常,厂商要求这个主板取 Temp3
            if (type === 'temperature' && Object.keys(data).some(function (key) { return key.includes('BIOSTAR Group B850MT2-E DJ'); })) {
                keySubstring = 'Temperature 2';
            } // 映泰主板温度异常,厂商要求这个主板取 Temp2
            key = Object.keys(filteredData)[0];
            key1 = Object.keys(filteredData)[1];
            key2 = Object.keys(filteredData)[2];
        }
        else if (key === 'Network') {
            var totalDLRate_1 = 0, totalUPRate_1 = 0;
            for (var key1_2 in data) {
                if (key1_2.startsWith("Network")) {
                    var networkData = data[key1_2] || [];
                    networkData.forEach(function (item) {
                        if (item["Current DL rate"]) {
                            totalDLRate_1 += parseFloat(item["Current DL rate"].value);
                        }
                        if (item["Current UP rate"]) {
                            totalUPRate_1 += parseFloat(item["Current UP rate"].value);
                        }
                    });
                }
            }
            return [Number(totalDLRate_1.toFixed(0)), Number(totalUPRate_1.toFixed(0))];
        }
        //除开主板与NetWork都在此处进行查找
        for (var _a = 0, _b = Object.entries(data); _a < _b.length; _a++) {
            var _c = _b[_a], parentKey = _c[0], arr = _c[1];
            // console.warn('ArrArrArr',arr);
            if (data[parentKey] && (parentKey.includes(key) || (key1 && parentKey.includes(key1)) || (key2 && parentKey.includes(key2)))) {
                for (var _d = 0, arr_1 = arr; _d < arr_1.length; _d++) {
                    var obj = arr_1[_d];
                    var _e = Object.entries(obj)[0], itemKey = _e[0], itemValue = _e[1];
                    // const [itemValue,itemKey] = Object.entries(obj)[0]
                    if (keySubstring) {
                        if (keySubstring.includes('|')) {
                            var substrings = keySubstring.split('|');
                            for (var _f = 0, substrings_1 = substrings; _f < substrings_1.length; _f++) {
                                var substring = substrings_1[_f];
                                if (itemKey.includes(substring) && type === (itemValue.type)) {
                                    return [itemKey, Number(parseFloat(itemValue.value).toFixed(ToFixed))];
                                }
                            }
                        }
                        else {
                            if (itemKey.includes(keySubstring) && type.includes(itemValue.type)) {
                                return [itemKey, Number(parseFloat((itemValue.value)).toFixed(ToFixed))];
                            }
                        }
                    }
                    else {
                        if (type.includes(itemValue.type)) {
                            return [itemKey, Number(parseFloat((itemValue.value)).toFixed(ToFixed))];
                        }
                    }
                }
            }
        }
        return ['', ''];
    };
    Background.prototype.organizeSensorInfo = function (SensorInfos) {
        var CPUData = {};
        CPUData.Core = { Clock: [], Temp: [], Load: [] };
        CPUData.EffectiveClock = {};
        CPUData.Thread = {};
        CPUData.PLR = [];
        CPUData.GPUTDP = null;
        var GPU = {};
        var AllMainKeys = Object.keys(SensorInfos);
        var NeedPLR = ['IA: PROCHOT', 'IA: Thermal Event', 'IA: VR Thermal Alert', 'IA: VR TDC', 'IA: Electrical Design Point/Other (ICCmax,PL4,SVID,DDR RAPL)', 'IA: Package-Level RAPL/PBM PL1', 'IA: VmaxStress', 'Thermal Limit', 'CPU PPT Limit', 'CPU TDC Limit', 'CPU EDC Limit', 'Thermal Throttling (HTC)', 'Thermal Throttling (PROCHOT CPU)', 'Thermal Throttling (PROCHOT EXT)'];
        var _loop_3 = function (i) {
            var mainKey = AllMainKeys[i];
            var upperMainKey = mainKey.toUpperCase();
            var itemValue = SensorInfos[mainKey];
            if (upperMainKey.startsWith('CPU [#0]')) {
                if (upperMainKey.includes('PERFORMANCE LIMIT REASONS') || upperMainKey.includes('ENHANCED')) {
                    itemValue.filter(function (itemDetail) { return NeedPLR.includes(Object.keys(itemDetail)[0]); }).map(function (itemDetail) {
                        var _a, _b;
                        var infoKey = Object.keys(itemDetail)[0];
                        var value = parseInt(itemDetail[infoKey].value);
                        var UNIT = itemDetail[infoKey].type;
                        if (value) {
                            if (UNIT !== 'usage') {
                                CPUData.PLR.push((_a = {}, _a[infoKey] = value, _a));
                            }
                            else if (value >= 99) {
                                CPUData.PLR.push((_b = {}, _b[infoKey] = value, _b));
                            }
                        }
                    });
                }
                else if (upperMainKey.includes('DTS')) {
                    itemValue.forEach(function (itemDetail) {
                        var _a;
                        var infoKey = Object.keys(itemDetail)[0];
                        if (infoKey.includes('Thermal Throttling') || infoKey === 'Package/Ring Critical Temperature' || infoKey === 'Package/Ring Thermal Throttling') {
                            var value = parseInt(itemDetail[infoKey].value);
                            if (value) {
                                CPUData.PLR.push((_a = {}, _a[infoKey] = value, _a));
                            }
                        }
                    });
                }
                itemValue.forEach(function (itemDetail) {
                    var infoKey = Object.keys(itemDetail)[0];
                    var itemInfo = itemDetail[infoKey];
                    var infoType = itemInfo.type.toUpperCase();
                    var infoValue = itemInfo.value;
                    var upperInfoKey = infoKey.toUpperCase();
                    if (infoType === 'CLOCK') {
                        if (upperInfoKey.match(new RegExp('^CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]CLOCK[.\n]*')) ||
                            upperInfoKey.match(new RegExp('^P-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]CLOCK [(]PERF #1[)][.\n]*')) ||
                            upperInfoKey.match(new RegExp('^E-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]CLOCK [(]PERF #1[)][.\n]*')) ||
                            upperInfoKey.match(new RegExp('^P-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]CLOCK[.\n]*')) ||
                            upperInfoKey.match(new RegExp('^E-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]CLOCK[.\n]*'))) {
                            CPUData.Core.Clock.push(Number(parseFloat(infoValue).toFixed(0)));
                        }
                        if (upperInfoKey.match(new RegExp('^CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]T[0-9][ \f\r\t\n]EFFECTIVE CLOCK$'))) {
                            var clock = Number(parseFloat(infoValue).toFixed(0));
                            var infoKeyItems = upperInfoKey.split(' ');
                            var EffectiveClock = CPUData.EffectiveClock;
                            if (EffectiveClock.hasOwnProperty(infoKeyItems[1])) {
                                EffectiveClock[infoKeyItems[1]][infoKeyItems[2]] = clock;
                            }
                            else {
                                var Detail_1 = {};
                                Detail_1[infoKeyItems[2]] = clock;
                                CPUData.EffectiveClock[infoKeyItems[1]] = Detail_1;
                            }
                        }
                    }
                    else if (infoType === 'USAGE') {
                        // 占用
                        if (upperInfoKey.match(new RegExp('^CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]T[0-9][ \f\r\t\n]USAGE$')) ||
                            upperInfoKey.match(new RegExp('^P-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]T[0-9][ \f\r\t\n]USAGE$')) ||
                            upperInfoKey.match(new RegExp('^E-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]T[0-9][ \f\r\t\n]USAGE$'))) {
                            // 超线程技术
                            var usage = Number(parseFloat(infoValue).toFixed(0));
                            var infoKeyItems = upperInfoKey.split(' ');
                            var Thread = CPUData.Thread;
                            if (Thread.hasOwnProperty(infoKeyItems[1])) {
                                Thread[infoKeyItems[1]][infoKeyItems[2]] = usage;
                            }
                            else {
                                var Detail = {};
                                Detail[infoKeyItems[2]] = usage;
                                CPUData.Thread[infoKeyItems[1]] = Detail;
                            }
                        }
                        else if (upperInfoKey.match(new RegExp('^CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]USAGE$')) ||
                            upperInfoKey.match(new RegExp('^P-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]USAGE$')) ||
                            upperInfoKey.match(new RegExp('^E-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]USAGE$'))) {
                            // 没有超线程技术
                            var usage = Number(parseFloat(infoValue).toFixed(0));
                            var infoKeyItems = upperInfoKey.split(' ');
                            var Thread = CPUData.Thread;
                            if (Thread.hasOwnProperty(infoKeyItems[1])) {
                                Thread[infoKeyItems[1]].T0 = usage;
                            }
                            else {
                                var Detail_2 = {};
                                Detail_2.T0 = usage;
                                CPUData.Thread[infoKeyItems[1]] = Detail_2;
                            }
                        }
                    }
                    else if (infoType === 'TEMPERATURE') {
                        if (upperInfoKey.match(new RegExp('^CORE[0-9]'))) {
                            CPUData.Core.Temp.push(Number(parseFloat(infoValue).toFixed(0)));
                        }
                        else if (upperInfoKey.match(new RegExp('^CORE[ \f\r\t\n][0-9]')) || upperInfoKey.match(new RegExp('^P-CORE[ \f\r\t\n][0-9]')) || upperInfoKey.match(new RegExp('^E-CORE[ \f\r\t\n][0-9]'))) {
                            if (infoType === 'TEMPERATURE') {
                                if (upperInfoKey.indexOf('DISTANCE') === -1) {
                                    CPUData.Core.Temp.push(Number(parseFloat(infoValue).toFixed(0)));
                                }
                            }
                        }
                    }
                });
            }
            else if (upperMainKey.startsWith('GPU')) {
                var strIdx = upperMainKey.indexOf('#') + 1;
                var GPUIDX = upperMainKey.substring(strIdx, strIdx + 1);
                var GPUData_1 = {};
                GPUData_1.TDP = [];
                var IsArrBool1 = Array.isArray(itemValue);
                if (itemValue && IsArrBool1) {
                    itemValue.forEach(function (itemDetail) {
                        var infoKey = Object.keys(itemDetail)[0];
                        var itemInfo = itemDetail[infoKey];
                        var infoType = itemInfo.type.toUpperCase();
                        var infoValue = itemInfo.value;
                        if (infoType === 'POWER') {
                            if (infoKey !== 'GPU Power') {
                                var tdp = {};
                                tdp[infoKey] = Number(parseFloat(infoValue).toFixed(3));
                                GPUData_1.TDP.push(tdp);
                            }
                        }
                    });
                }
                GPU[GPUIDX] = GPUData_1;
            }
        };
        for (var i = 0; i < AllMainKeys.length; i++) {
            _loop_3(i);
        }
        CPUData.GPUTDP = GPU;
        var Count = Object.keys(CPUData.Core.Clock);
        for (var i = 0; i < Count.length; i++) {
            var coreId = i.toString();
            var T0 = CPUData.Thread[coreId].T0;
            var T1 = CPUData.Thread[coreId].T1;
            if (T0 && T1 !== undefined) {
                CPUData.Core.Load.push(Math.round((T0 + T1) / 2));
            }
            else {
                CPUData.Core.Load.push(T0);
            }
        }
        return CPUData;
    };
    Background.prototype.SensorInfoDataProcess = function (isDef, groupIndex, itemIndex, isUnit, SensorInfo, outKey, inKey, Unit) {
        if (Unit === void 0) { Unit = ''; }
        try {
            if (!outKey || outKey === '') {
                var ToFixed = 0;
                // console.warn('SensorInfo',SensorInfo);
                var obj = Object.keys(SensorInfo);
                // console.warn('obj',obj);
                var groupKey = obj[groupIndex];
                var results = '';
                if (SensorInfo[groupKey]) {
                    var ChoseData = SensorInfo[groupKey][itemIndex];
                    if (!ChoseData) {
                        return results;
                    }
                    var _a = Object.entries(ChoseData)[0], itemKey = _a[0], itemValue = _a[1];
                    if (itemValue.type === 'voltage') {
                        ToFixed = 3;
                    }
                    results = Number(parseFloat(itemValue.value).toFixed(ToFixed));
                }
                return results;
            }
            else {
                var SensorOut = SensorInfo[outKey];
                var ToFixed = 0;
                for (var i = 0; i < SensorOut.length; i++) {
                    var keyName = Object.keys(SensorOut[i])[0];
                    if (keyName === inKey) {
                        if (Unit && this.getTypeFromUnit(Unit) !== SensorOut[i][keyName]['type'])
                            continue;
                        if (SensorOut[i][keyName]['type'] && SensorOut[i][keyName]['type'] === 'voltage') {
                            ToFixed = 3;
                        }
                        return Number(parseFloat(SensorOut[i][keyName]['value']).toFixed(ToFixed));
                    }
                }
            }
        }
        catch (_b) {
            return 0;
        }
    };
    Background.prototype.getTypeFromUnit = function (unit) {
        if (unit.includes('℃')) {
            return 'temperature';
        }
        else if (unit.includes('V')) {
            return 'voltage';
        }
        else if (unit.includes('RPM')) {
            return 'fan';
        }
        else if (unit.includes('W')) {
            return 'power';
        }
        else if (unit.includes('MHz')) {
            return 'clock';
        }
        else if (unit.includes('%')) {
            return 'usage';
        }
        else {
            return 'other';
        }
    };
    /**
     * getSensorIndexFromName
     */
    Background.prototype.getSensorIndexFromName = function (data, key, keySubstring, type) {
        if (key === 'Mainboard') {
            var keywords = ['CPU', 'GPU', 'Memory', 'S.M.A.R.T', 'System:', 'Windows Hardware', 'Drive', 'Network', 'DIMM'];
            var filteredData = {};
            for (var key1 in data) {
                var shouldKeep = true;
                for (var _i = 0, keywords_2 = keywords; _i < keywords_2.length; _i++) {
                    var keyword = keywords_2[_i];
                    if (data[key1] === null || key1.toLowerCase().includes(keyword.toLowerCase())) {
                        shouldKeep = false;
                        break;
                    }
                }
                if (!shouldKeep) {
                    filteredData[key1] = data[key1];
                }
                else {
                    filteredData['Mainboard ' + key1] = data[key1];
                }
            }
            data = filteredData;
        }
        var keys = Object.keys(data);
        for (var i = 0; i < keys.length; i++) {
            var parentKey = keys[i];
            var arr = data[parentKey];
            if (data[parentKey] && (parentKey.includes(key))) {
                for (var _a = 0, _b = arr.entries(); _a < _b.length; _a++) {
                    var _c = _b[_a], j = _c[0], obj = _c[1];
                    var _d = Object.entries(obj)[0], itemKey = _d[0], itemValue = _d[1];
                    if (itemKey.includes(keySubstring) && type.includes(itemValue.type)) {
                        return [i, j];
                    }
                }
            }
        }
        return null;
    };
    //游戏内提示信息窗口
    Background.prototype.InGame_Tips_Information = function (WindowName, type, svalue, time) {
        return __awaiter(this, void 0, void 0, function () {
            var SendData_1;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!(gamepp.setting.getInteger.sync(COMMANDID.CM_SHOW_NOTIFY_UI_SWITH) === 1)) return [3 /*break*/, 5];
                        return [4 /*yield*/, gamepp.isDesktopMode.promise()];
                    case 1:
                        if (_a.sent())
                            return [2 /*return*/, false];
                        SendData_1 = {};
                        SendData_1['type'] = type;
                        SendData_1['language'] = gamepp.setting.getString.sync(COMMANDID.CM_LANGUAGE_TYPE);
                        SendData_1['svalue'] = svalue;
                        SendData_1['time'] = time;
                        if (!(type === 30)) return [3 /*break*/, 2];
                        setTimeout(function () { return __awaiter(_this, void 0, void 0, function () {
                            return __generator(this, function (_a) {
                                switch (_a.label) {
                                    case 0:
                                    // await gamepp.webapp.sendInternalAppEvent.promise(WindowName, SendData);
                                    return [4 /*yield*/, window.localStorage.setItem('ingame_tips', JSON.stringify(SendData_1))];
                                    case 1:
                                        // await gamepp.webapp.sendInternalAppEvent.promise(WindowName, SendData);
                                        _a.sent();
                                        return [4 /*yield*/, gamepp.webapp.windows.show.promise(WindowName)];
                                    case 2:
                                        _a.sent();
                                        return [2 /*return*/];
                                }
                            });
                        }); }, this.LoadingTime);
                        this.LoadingTime += 200;
                        return [3 /*break*/, 5];
                    case 2:
                    // await gamepp.webapp.sendInternalAppEvent.promise(WindowName, SendData);
                    return [4 /*yield*/, window.localStorage.setItem('ingame_tips', JSON.stringify(SendData_1))];
                    case 3:
                        // await gamepp.webapp.sendInternalAppEvent.promise(WindowName, SendData);
                        _a.sent();
                        return [4 /*yield*/, gamepp.webapp.windows.show.promise(WindowName)];
                    case 4:
                        _a.sent();
                        _a.label = 5;
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    //监听配置信息改变
    Background.prototype.BGonConfigChanged = function () {
        var _this = this;
        gamepp.setting.onConfigChanged.addEventListener(function (type, id, value) {
            if (id === COMMANDID.CM_ENABLE_EFFECT) {
                var _hotkey_OpenOrCloseInGameQuality = gamepp.setting.getString.sync(COMMANDID.CM_HOTKEY_PRESENT_EFFECT_SWITCH_INFO);
                if (value === 0) {
                    _this.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 10, _hotkey_OpenOrCloseInGameQuality, 3);
                }
                else if (value === 1) {
                    _this.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 9, _hotkey_OpenOrCloseInGameQuality, 3);
                }
            }
            else if (id === 450) {
                //监听刷新时间修改
                clearInterval(setInterval_chooseSensor_list);
                gameclient.RefreshTime = value;
                _this.BgSensorDataProcess(value);
                _this.StarSensorDataProcess(value);
            }
        });
    };
    /**
     *初始化事件
     */
    Background.prototype.InitAppEvent = function () {
        return __awaiter(this, void 0, void 0, function () {
            function checkIsSetScheduledShutdown() {
                // 检查用户是否取消关机
                var isUserSetScheduledShutdown = window.localStorage.getItem('isUserSetScheduledShutdown');
                if (!isUserSetScheduledShutdown)
                    return;
                if (isUserSetScheduledShutdown === '1') {
                    // 用户设置关机时间戳 - 当前时间戳
                    var scheduledShutdownTimestamp = window.localStorage.getItem('ScheduledShutdownTimestamp');
                    if (!scheduledShutdownTimestamp) {
                        return;
                    }
                    try {
                        gamepp.tray.setImage.promise('./assets/500.ico');
                    }
                    catch (e) {
                        console.log(e);
                    }
                    var timeleft = scheduledShutdownTimestamp - Date.now();
                    if (timeleft <= 0 && timeleft >= (-5000)) { // 时间超过了5秒就不执行了
                        window.localStorage.removeItem('isUserSetScheduledShutdown');
                        window.localStorage.removeItem('ScheduledShutdownTimestamp');
                        setTimeout(function () {
                            console.log('定时关机');
                            gamepp.sysShutdown.promise();
                        }, 500);
                    }
                }
                else {
                    try {
                        gamepp.tray.setImage.promise('./assets/logo.ico');
                    }
                    catch (e) {
                        console.log(e);
                    }
                }
            }
            function sendSetSparkMessage(server_config) {
                return __awaiter(this, void 0, void 0, function () {
                    var msg, ryzenOrDML, needClose;
                    var _this = this;
                    return __generator(this, function (_a) {
                        switch (_a.label) {
                            case 0:
                                msg = {
                                    server_config: server_config,
                                    'type': 'SetSparkServerInfo',
                                };
                                ryzenOrDML = window.localStorage.getItem('lastSelectedType');
                                console.log(ryzenOrDML);
                                needClose = false;
                                if (!(ryzenOrDML && !gamepp.sparkserver.isSparkServerRunning.sync())) return [3 /*break*/, 6];
                                needClose = true; // 服务没启动，开启后需要关闭
                                if (!(ryzenOrDML === 'Ryzen')) return [3 /*break*/, 2];
                                return [4 /*yield*/, gamepp.sparkserver.runRyzenAIClient.promise()]; //启动RyzenAI的版本调用
                            case 1:
                                _a.sent(); //启动RyzenAI的版本调用
                                return [3 /*break*/, 6];
                            case 2:
                                if (!(ryzenOrDML === 'btn')) return [3 /*break*/, 4];
                                return [4 /*yield*/, gamepp.sparkserver.runClient.promise()]; //启动通用模式的版本调用
                            case 3:
                                _a.sent(); //启动通用模式的版本调用
                                return [3 /*break*/, 6];
                            case 4: return [4 /*yield*/, gamepp.sparkserver.runUltraAIClient.promise()];
                            case 5:
                                _a.sent();
                                _a.label = 6;
                            case 6:
                                // 等sparkserver启动
                                setTimeout(function () { return __awaiter(_this, void 0, void 0, function () {
                                    return __generator(this, function (_a) {
                                        switch (_a.label) {
                                            case 0:
                                            // 发消息
                                            return [4 /*yield*/, gamepp.sparkserver.sendMessage.promise(JSON.stringify(msg))];
                                            case 1:
                                                // 发消息
                                                _a.sent();
                                                if (!needClose) return [3 /*break*/, 3];
                                                return [4 /*yield*/, gamepp.sparkserver.stopClient.promise()];
                                            case 2:
                                                _a.sent();
                                                _a.label = 3;
                                            case 3: return [2 /*return*/];
                                        }
                                    });
                                }); }, 200);
                                return [2 /*return*/];
                        }
                    });
                });
            }
            var AppDataDir, GPP5DatabaseDir, GPP5DatabaseId, status, queryField_endtime, queryField_hd_info, queryField_province, queryField_data_type, queryField_refresh_time, queryField_hdLis, queryField_resolutions, queryField_dx_version, queryField_is_check, queryField_exec_path, GPPBase_table_exists, ListData, PERFORMANCE_CLEAR_TIME, i, TableExists, j, dataLen, list_length, StressNewDatabaseDir, StressNewDatabaseId, list_table_exists, queryField_gpu_loop_data, ListData, i, TableExists, _a, queryField, chooseSensor_list_v1Str, chooseSensor_list_temp, LanStr, isStartedWithOs, Id, update_storage, update_storage_object, captureId, benchmark_str, benchmark_data, DisplayInfo, i, HighPerformanceGPUStr, HighPerformanceGPU, RefreshTime, GPPDownDatabaseDir, GPPDownDatabaseId, status_BaseInfo;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        console.log('清理性能统计数据');
                        return [4 /*yield*/, gamepp.getAppDataDir.promise()];
                    case 1:
                        AppDataDir = _b.sent();
                        GPP5DatabaseDir = gamepp.setting.getString.sync(COMMANDID.CM_STATISTICS_SAVE_ADDRESS);
                        if (GPP5DatabaseDir == '')
                            GPP5DatabaseDir = AppDataDir + '\\common\\Data';
                        return [4 /*yield*/, gamepp.database.open.promise(GPP5DatabaseDir + '\\GamePP5.dll')];
                    case 2:
                        GPP5DatabaseId = _b.sent();
                        return [4 /*yield*/, gamepp.database.exists.promise(GPP5DatabaseId, 'GamePP_BaseInfo')];
                    case 3:
                        status = _b.sent();
                        if (!!status) return [3 /*break*/, 5];
                        return [4 /*yield*/, gamepp.database.create.promise(GPP5DatabaseId, "GamePP_BaseInfo", '("id"INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,"gamepath"TEXT,"starsensorInfo"TEXT,"processpath"TEXT,"exec_path"TEXT,"starttime"INTEGER,"endtime"INTEGER,"gametime"INTEGER,"icon"TEXT,"city"TEXT,"wea"TEXT,"wea_img"TEXT,"tem"TEXT,"hd_info"TEXT,"data_type"TEXT,"refresh_time"INTEGER)')];
                    case 4:
                        _b.sent();
                        _b.label = 5;
                    case 5: return [4 /*yield*/, gamepp.database.queryField.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'endtime')];
                    case 6:
                        queryField_endtime = _b.sent();
                        if (!!queryField_endtime) return [3 /*break*/, 8];
                        return [4 /*yield*/, gamepp.database.alter.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'endtime INTEGER')];
                    case 7:
                        _b.sent();
                        _b.label = 8;
                    case 8: return [4 /*yield*/, gamepp.database.queryField.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'hd_info')];
                    case 9:
                        queryField_hd_info = _b.sent();
                        if (!!queryField_hd_info) return [3 /*break*/, 11];
                        return [4 /*yield*/, gamepp.database.alter.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'hd_info TEXT')];
                    case 10:
                        _b.sent();
                        _b.label = 11;
                    case 11: return [4 /*yield*/, gamepp.database.queryField.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'province')];
                    case 12:
                        queryField_province = _b.sent();
                        if (!!queryField_province) return [3 /*break*/, 14];
                        return [4 /*yield*/, gamepp.database.alter.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'province TEXT')];
                    case 13:
                        _b.sent();
                        _b.label = 14;
                    case 14: return [4 /*yield*/, gamepp.database.queryField.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'data_type')];
                    case 15:
                        queryField_data_type = _b.sent();
                        if (!!queryField_data_type) return [3 /*break*/, 17];
                        return [4 /*yield*/, gamepp.database.alter.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'data_type TEXT')];
                    case 16:
                        _b.sent();
                        _b.label = 17;
                    case 17: return [4 /*yield*/, gamepp.database.queryField.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'refresh_time')];
                    case 18:
                        queryField_refresh_time = _b.sent();
                        if (!!queryField_refresh_time) return [3 /*break*/, 20];
                        return [4 /*yield*/, gamepp.database.alter.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'refresh_time INTEGER')];
                    case 19:
                        _b.sent();
                        _b.label = 20;
                    case 20: return [4 /*yield*/, gamepp.database.queryField.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'hd_list_data')];
                    case 21:
                        queryField_hdLis = _b.sent();
                        if (!!queryField_hdLis) return [3 /*break*/, 23];
                        return [4 /*yield*/, gamepp.database.alter.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'hd_list_data TEXT')];
                    case 22:
                        _b.sent();
                        _b.label = 23;
                    case 23: return [4 /*yield*/, gamepp.database.queryField.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'resolutions')];
                    case 24:
                        queryField_resolutions = _b.sent();
                        if (!!queryField_resolutions) return [3 /*break*/, 26];
                        return [4 /*yield*/, gamepp.database.alter.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'resolutions TEXT')];
                    case 25:
                        _b.sent();
                        _b.label = 26;
                    case 26: return [4 /*yield*/, gamepp.database.queryField.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'dx_version')];
                    case 27:
                        queryField_dx_version = _b.sent();
                        if (!!queryField_dx_version) return [3 /*break*/, 29];
                        return [4 /*yield*/, gamepp.database.alter.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'dx_version TEXT')];
                    case 28:
                        _b.sent();
                        _b.label = 29;
                    case 29: return [4 /*yield*/, gamepp.database.queryField.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'is_check')];
                    case 30:
                        queryField_is_check = _b.sent();
                        if (!!queryField_is_check) return [3 /*break*/, 32];
                        return [4 /*yield*/, gamepp.database.alter.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'is_check INTEGER')];
                    case 31:
                        _b.sent();
                        _b.label = 32;
                    case 32: return [4 /*yield*/, gamepp.database.queryField.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'exec_path')];
                    case 33:
                        queryField_exec_path = _b.sent();
                        if (!!queryField_exec_path) return [3 /*break*/, 35];
                        return [4 /*yield*/, gamepp.database.alter.promise(GPP5DatabaseId, 'GamePP_BaseInfo', 'exec_path INTEGER')];
                    case 34:
                        _b.sent();
                        _b.label = 35;
                    case 35:
                        DataBaseId.GPP5DatabaseId = GPP5DatabaseId;
                        return [4 /*yield*/, gamepp.database.exists.promise(GPP5DatabaseId, 'GamePP_BaseInfo')];
                    case 36:
                        GPPBase_table_exists = _b.sent();
                        if (!GPPBase_table_exists) return [3 /*break*/, 55];
                        return [4 /*yield*/, gamepp.database.query.promise(GPP5DatabaseId, "GamePP_BaseInfo", "*", "order by id asc")];
                    case 37:
                        ListData = _b.sent();
                        return [4 /*yield*/, gamepp.setting.getInteger.promise(COMMANDID.CM_PERFORMANCE_CLEAR_TIME)];
                    case 38:
                        PERFORMANCE_CLEAR_TIME = _b.sent();
                        if (!(ListData.length > PERFORMANCE_CLEAR_TIME)) return [3 /*break*/, 44];
                        i = 0;
                        _b.label = 39;
                    case 39:
                        if (!(i < (ListData.length - PERFORMANCE_CLEAR_TIME))) return [3 /*break*/, 44];
                        return [4 /*yield*/, gamepp.database.exists.promise(GPP5DatabaseId, "" + ListData[i]['starttime'] + "")];
                    case 40:
                        TableExists = _b.sent();
                        if (!TableExists) return [3 /*break*/, 43];
                        return [4 /*yield*/, gamepp.database.delete.promise(GPP5DatabaseId, "GamePP_BaseInfo", "id = " + ListData[i]['id'] + "")];
                    case 41:
                        _b.sent(); //列表数据
                        return [4 /*yield*/, gamepp.database.drop.promise(GPP5DatabaseId, "\'" + ListData[i]['starttime'] + "\'", "")];
                    case 42:
                        _b.sent(); //详细表
                        _b.label = 43;
                    case 43:
                        i++;
                        return [3 /*break*/, 39];
                    case 44:
                        j = 0;
                        _b.label = 45;
                    case 45:
                        if (!(j < ListData.length)) return [3 /*break*/, 55];
                        if (!(ListData[j]['is_check'] !== 1)) return [3 /*break*/, 54];
                        if (!(ListData[j]['endtime'] === null)) return [3 /*break*/, 48];
                        return [4 /*yield*/, gamepp.database.delete.promise(GPP5DatabaseId, "GamePP_BaseInfo", "id = " + ListData[j]['id'] + "")];
                    case 46:
                        _b.sent(); //列表数据
                        return [4 /*yield*/, gamepp.database.drop.promise(GPP5DatabaseId, "\'" + ListData[j]['starttime'] + "\'", "")];
                    case 47:
                        _b.sent(); //详细表
                        _b.label = 48;
                    case 48: return [4 /*yield*/, gamepp.database.query.promise(GPP5DatabaseId, "'" + ListData[j]['starttime'] + "'", "COUNT(id)")];
                    case 49:
                        dataLen = _b.sent();
                        if (!(dataLen.length !== 0)) return [3 /*break*/, 52];
                        list_length = dataLen[0]['COUNT(id)'];
                        if (!(list_length === 0)) return [3 /*break*/, 52];
                        return [4 /*yield*/, gamepp.database.delete.promise(GPP5DatabaseId, "GamePP_BaseInfo", "starttime = " + ListData[j]['starttime'] + "")];
                    case 50:
                        _b.sent(); //列表数据
                        return [4 /*yield*/, gamepp.database.drop.promise(GPP5DatabaseId, "\'" + ListData[j]['starttime'] + "\'", "")];
                    case 51:
                        _b.sent(); //详细表
                        _b.label = 52;
                    case 52:
                    //设置当前表已经检查
                    return [4 /*yield*/, gamepp.database.update.promise(GPP5DatabaseId, "GamePP_BaseInfo", ['is_check="' + 1 + '"'], 'id = "' + ListData[j]['id'] + '"')];
                    case 53:
                        //设置当前表已经检查
                        _b.sent();
                        _b.label = 54;
                    case 54:
                        j++;
                        return [3 /*break*/, 45];
                    case 55:
                        StressNewDatabaseDir = gamepp.setting.getString.sync(COMMANDID.CM_STATISTICS_SAVE_ADDRESS);
                        if (StressNewDatabaseDir == '')
                            StressNewDatabaseDir = AppDataDir + '\\common\\Data';
                        return [4 /*yield*/, gamepp.database.open.promise(StressNewDatabaseDir + '\\GamePPStressNew.dll')];
                    case 56:
                        StressNewDatabaseId = _b.sent();
                        return [4 /*yield*/, gamepp.database.exists.promise(StressNewDatabaseId, 'list')];
                    case 57:
                        list_table_exists = _b.sent();
                        if (!list_table_exists) return [3 /*break*/, 72];
                        return [4 /*yield*/, gamepp.database.queryField.promise(StressNewDatabaseId, 'list', 'gpu_loop_data')];
                    case 58:
                        queryField_gpu_loop_data = _b.sent();
                        if (!!queryField_gpu_loop_data) return [3 /*break*/, 60];
                        return [4 /*yield*/, gamepp.database.alter.promise(StressNewDatabaseId, 'list', 'gpu_loop_data TEXT')];
                    case 59:
                        _b.sent();
                        _b.label = 60;
                    case 60: return [4 /*yield*/, gamepp.database.query.promise(StressNewDatabaseId, "list", "*", "order by id asc")];
                    case 61:
                        ListData = _b.sent();
                        if (!(ListData.length > 10)) return [3 /*break*/, 69];
                        i = 0;
                        _b.label = 62;
                    case 62:
                        if (!(i < (ListData.length - 10))) return [3 /*break*/, 69];
                        return [4 /*yield*/, gamepp.database.exists.promise(StressNewDatabaseId, "" + ListData[i]['starttime'] + "")];
                    case 63:
                        TableExists = _b.sent();
                        if (!TableExists) return [3 /*break*/, 65];
                        return [4 /*yield*/, gamepp.database.drop.promise(StressNewDatabaseId, "\'" + ListData[i]['starttime'] + "\'", "")];
                    case 64:
                        _b.sent(); //详细表
                        _b.label = 65;
                    case 65:
                        _b.trys.push([65, 67, , 68]);
                        return [4 /*yield*/, gamepp.database.delete.promise(StressNewDatabaseId, "list", "id = " + ListData[i]['id'] + "")];
                    case 66:
                        _b.sent(); //列表数据
                        return [3 /*break*/, 68];
                    case 67:
                        _a = _b.sent();
                        return [3 /*break*/, 68];
                    case 68:
                        i++;
                        return [3 /*break*/, 62];
                    case 69: return [4 /*yield*/, gamepp.database.queryField.promise(StressNewDatabaseId, 'list', 'comprehensive_data')];
                    case 70:
                        queryField = _b.sent();
                        if (!!queryField) return [3 /*break*/, 72];
                        return [4 /*yield*/, gamepp.database.alter.promise(StressNewDatabaseId, 'list', 'comprehensive_data TEXT')];
                    case 71:
                        _b.sent();
                        _b.label = 72;
                    case 72: return [4 /*yield*/, gamepp.database.close.promise(StressNewDatabaseId)
                        //传感器新增网络对象
                    ];
                    case 73:
                        _b.sent();
                        chooseSensor_list_v1Str = window.localStorage.getItem('chooseSensor_list_v1');
                        if (chooseSensor_list_v1Str) {
                            chooseSensor_list_temp = JSON.parse(chooseSensor_list_v1Str);
                            if (!chooseSensor_list_temp.networkData) {
                                chooseSensor_list_temp.networkData = [
                                    { name: 'download', describe: '下载', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' KB/s' },
                                    { name: 'upload', describe: '上传', isDef: true, groupIndex: 0, itemIndex: 0, Unit: ' KB/s' }
                                ];
                                window.localStorage.setItem('chooseSensor_list_v1', JSON.stringify(chooseSensor_list_temp));
                            }
                        }
                        /*
                         修复工具
                         */
                        return [4 /*yield*/, gamepp.repair.Initialize.promise()];
                    case 74:
                        /*
                         修复工具
                         */
                        _b.sent();
                        if (!(gamepp.opti.isInitOptiSDK.sync() === false)) return [3 /*break*/, 76];
                        return [4 /*yield*/, gamepp.opti.initOptiSDK.promise()];
                    case 75:
                        _b.sent();
                        _b.label = 76;
                    case 76: return [4 /*yield*/, gamepp.setting.getString.promise(COMMANDID.CM_LANGUAGE_TYPE)];
                    case 77:
                        LanStr = _b.sent();
                        if (!(LanStr === 'CN')) return [3 /*break*/, 79];
                        return [4 /*yield*/, gamepp.utils.sendstatics.promise(110)];
                    case 78:
                        _b.sent();
                        return [3 /*break*/, 81];
                    case 79: return [4 /*yield*/, gamepp.utils.sendstatics.promise(111)];
                    case 80:
                        _b.sent();
                        _b.label = 81;
                    case 81: return [4 /*yield*/, gamepp.isStartedWithOs.promise()];
                    case 82:
                        isStartedWithOs = _b.sent();
                        Id = isStartedWithOs === false ? 103 : 102;
                        return [4 /*yield*/, gamepp.utils.sendstatics.promise(Id)];
                    case 83:
                        _b.sent();
                        update_storage = window.localStorage.getItem('update_storage');
                        if (!(update_storage !== null && update_storage !== 'null' && update_storage !== undefined)) return [3 /*break*/, 87];
                        update_storage_object = JSON.parse(update_storage);
                        if (!(update_storage_object['update'] === 1)) return [3 /*break*/, 85];
                        return [4 /*yield*/, gamepp.utils.sendstatics.promise(14)];
                    case 84:
                        _b.sent(); //更新打点
                        _b.label = 85;
                    case 85:
                        if (!(update_storage_object['upgrade'] === 1)) return [3 /*break*/, 87];
                        return [4 /*yield*/, gamepp.utils.sendstatics.promise(3)];
                    case 86:
                        _b.sent(); //升级打点
                        _b.label = 87;
                    case 87: return [4 /*yield*/, gamepp.setting.getInteger.promise(COMMANDID.CM_VR_VIDEO_RELIVE_SWITCH)];
                    case 88:
                        captureId = _b.sent();
                        if (!(captureId !== 3)) return [3 /*break*/, 90];
                        return [4 /*yield*/, gamepp.setting.setInteger.promise(COMMANDID.CM_VR_VIDEO_RELIVE_SWITCH, 3)];
                    case 89:
                        _b.sent();
                        _b.label = 90;
                    case 90:
                        //启动时修改localStorage 用于更新数据
                        console.log('修改localStorage 用于更新 Benchmark Rank');
                        benchmark_str = window.localStorage.getItem('benchmark_data_v2');
                        if (benchmark_str !== null && benchmark_str !== 'null' && benchmark_str !== undefined) {
                            benchmark_data = JSON.parse(benchmark_str);
                            benchmark_data['need_update_rank'] = 1;
                            window.localStorage.setItem('benchmark_data_v2', JSON.stringify(benchmark_data));
                        }
                        //删除部分localStorage
                        //启动时删除硬件扫描标识(每次启动时候重新扫描硬件信息)
                        window.localStorage.removeItem('HwIsScanned');
                        window.localStorage.removeItem('gppToolboxlink');
                        window.localStorage.removeItem('game_booster_begin_time'); //加速状态
                        return [4 /*yield*/, gamepp.opti.setValue.promise(73, 0)];
                    case 91:
                        _b.sent();
                        window.localStorage.removeItem('laboratoryStatus');
                        window.localStorage.removeItem('danmu_ConnectionList'); //弹幕多窗口连接列表
                        window.localStorage.removeItem('GppConfig_url'); //在线获取的链接
                        window.localStorage.removeItem('chooseSensor_list_x'); //使用新字段
                        window.localStorage.removeItem('bg_sensor_data');
                        window.localStorage.removeItem('star_sensor_data');
                        window.localStorage.removeItem('game_run_disk');
                        window.localStorage.removeItem('Not_Inject');
                        window.localStorage.removeItem('GameMessageInGameShow');
                        window.localStorage.removeItem('GameMessageExitShow');
                        this.GetServerGppConfig();
                        return [4 /*yield*/, gamepp.hardware.getDisplayCardInfo.promise()];
                    case 92:
                        DisplayInfo = _b.sent();
                        for (i = 0; i < DisplayInfo.Count; i++) {
                            if (DisplayInfo['Element'][i]['IsPrimaryScreen'] === 1) {
                                window.localStorage.setItem('primary_screen', JSON.stringify(DisplayInfo['Element'][i]));
                            }
                        }
                        return [4 /*yield*/, gamepp.getHighPerformanceGPU.promise()];
                    case 93:
                        HighPerformanceGPUStr = _b.sent();
                        if (!HighPerformanceGPUStr) return [3 /*break*/, 95];
                        HighPerformanceGPU = JSON.parse(HighPerformanceGPUStr);
                        return [4 /*yield*/, window.localStorage.setItem('gpu_detect_list', JSON.stringify(HighPerformanceGPU))];
                    case 94:
                        _b.sent();
                        _b.label = 95;
                    case 95: return [4 /*yield*/, gamepp.setting.getInteger.promise(450)];
                    case 96:
                        RefreshTime = _b.sent();
                        if (!!([100, 300, 500, 1000, 2000, 3000, 5000].includes(RefreshTime))) return [3 /*break*/, 98];
                        return [4 /*yield*/, gamepp.setting.setInteger.promise(450, 1000)];
                    case 97:
                        _b.sent();
                        _b.label = 98;
                    case 98:
                        GPPDownDatabaseDir = gamepp.setting.getString.sync(COMMANDID.CM_STATISTICS_SAVE_ADDRESS);
                        if (GPPDownDatabaseDir == '')
                            GPPDownDatabaseDir = AppDataDir + '\\common\\Data';
                        return [4 /*yield*/, gamepp.database.open.promise(GPPDownDatabaseDir + '\\GamePPShutdown.dll')];
                    case 99:
                        GPPDownDatabaseId = _b.sent();
                        DataBaseId.GPPDownDatabaseId = GPPDownDatabaseId;
                        return [4 /*yield*/, gamepp.database.exists.promise(GPPDownDatabaseId, 'BaseInfo')];
                    case 100:
                        status_BaseInfo = _b.sent();
                        if (!!status_BaseInfo) return [3 /*break*/, 102];
                        return [4 /*yield*/, gamepp.database.create.promise(GPPDownDatabaseId, "BaseInfo", '("id"INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT ,"starttime"INTEGER,"hd_info"TEXT,"state"INTEGER)')];
                    case 101:
                        _b.sent();
                        return [3 /*break*/, 102];
                    case 102:
                        setInterval(function () {
                            checkIsSetScheduledShutdown();
                        }, 1000);
                        window.addEventListener("storage", function (event) {
                            if (event['key'] === 'chooseSensor_list_v1') {
                                sensorInfoModifying = true;
                                console.log('传感器信息修改');
                                localSensorListX = JSON.parse(event['newValue']);
                                chooseSensor_list_xStr = window.localStorage.getItem('chooseSensor_list_v1');
                                sensorInfoModifying = false;
                            }
                            else if (event['key'] === 'switchGamepad' || event['key'] === 'switchGamepad_aiMaster' || event['key'] === 'switchGamepad_aiSuperpower') {
                                handleSparkServerSwitch();
                            }
                            else if (event.key === 'lastSelectedType') {
                                var lastSelectedType = window.localStorage.getItem('lastSelectedType');
                                try {
                                    var server_config = JSON.parse(gamepp.sparkserver.getServerConfig.sync());
                                    server_config['lastSelectedType'] = lastSelectedType;
                                    sendSetSparkMessage(server_config);
                                }
                                catch (_a) { }
                            }
                            else if (event.key === 'VKDevice') {
                                var VKDevice = JSON.parse(localStorage.getItem('VKDevice'));
                                try {
                                    var server_config = JSON.parse(gamepp.sparkserver.getServerConfig.sync());
                                    server_config['VKDevice'] = VKDevice;
                                    sendSetSparkMessage(server_config);
                                }
                                catch (_b) { }
                            }
                        });
                        handleSparkServerSwitch();
                        return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 获取服务器配置传感器默认数据
     */
    Background.prototype.getSensorStatistics = function () {
        return __awaiter(this, void 0, void 0, function () {
            var hw_list_dataStr, data, encodeData, resultStr, result, decodeData, _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        console.log('获取服务器配置传感器默认数据');
                        console.time('getSensorStatistics');
                        hw_list_dataStr = window.localStorage.getItem('hw_list_data');
                        if (!hw_list_dataStr)
                            return [2 /*return*/];
                        data = {
                            hw_list: JSON.parse(hw_list_dataStr),
                            version: gamepp.getPlatformVersion.sync()
                        };
                        console.log(data);
                        _b.label = 1;
                    case 1:
                        _b.trys.push([1, 3, , 4]);
                        encodeData = gamepp.user.getEncodeInfo.sync(JSON.stringify(data));
                        return [4 /*yield*/, gamepp.http.httppost.promise('https://config.gamepp.com/hardware/v1/getSensorStatistics', '', encodeData)];
                    case 2:
                        resultStr = _b.sent();
                        if (resultStr) {
                            if (resultStr) {
                                result = JSON.parse(resultStr);
                                if (result.code === 200 && result.data) {
                                    decodeData = JSON.parse(gamepp.user.getDecodeInfo.sync(result.data));
                                    console.log(decodeData);
                                    window.localStorage.setItem('sensorStatistics', JSON.stringify(decodeData));
                                    if (decodeData.default) {
                                        this.ModifyUserSensorToServerData(decodeData.default);
                                    }
                                }
                            }
                        }
                        return [3 /*break*/, 4];
                    case 3:
                        _a = _b.sent();
                        return [3 /*break*/, 4];
                    case 4:
                        console.timeEnd('getSensorStatistics');
                        return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 修改传感器为服务器默认数据
     */
    Background.prototype.ModifyUserSensorToServerData = function (data) {
        var _a;
        var chooseSensor_list_xStr = window.localStorage.getItem('chooseSensor_list_v1');
        if (chooseSensor_list_xStr) {
            var chooseSensor_list = JSON.parse(chooseSensor_list_xStr);
            var SensorInfoStr = gamepp.hardware.getSensorInfo.sync();
            var SensorInfoOriginal = JSON.parse(SensorInfoStr);
            var SensorInfoKeys = Object.keys(SensorInfoOriginal);
            var SensorInfo = this.SensorAddAverageData(SensorInfoOriginal, SensorInfoKeys);
            var isSet = false;
            for (var type in data) {
                var _loop_4 = function (attribute) {
                    var SenName = data[type][attribute];
                    var attribute_name = attribute;
                    if (attribute === 'temperature') {
                        attribute_name = 'temp';
                    }
                    var type_name = type;
                    if (type === 'board') {
                        type_name = 'Mainboard';
                    }
                    var Data = (_a = chooseSensor_list[type + 'Data']) === null || _a === void 0 ? void 0 : _a.find(function (item) { return item.name === attribute_name; });
                    if (Data && Data.isDef) {
                        if (type_name !== 'Mainboard') {
                            type_name = type_name.toUpperCase();
                        }
                        var sensor_index = this_1.getSensorIndexFromName(SensorInfo, type_name, SenName, attribute);
                        if (sensor_index) {
                            isSet = true;
                            Data.isDefServer = true;
                            Data.keyName = SenName;
                            Data.groupIndex = sensor_index[0];
                            Data.itemIndex = sensor_index[1];
                            console.log(Data.describe + ': 已修改为: ' + SenName);
                        }
                        else {
                            console.log(Data.describe + ': ' + SenName + ' 未匹配到');
                        }
                    }
                };
                var this_1 = this;
                for (var attribute in data[type]) {
                    _loop_4(attribute);
                }
            }
            if (isSet) {
                window.localStorage.setItem('chooseSensor_list_v1', JSON.stringify(chooseSensor_list));
            }
        }
    };
    Background.prototype.IsReady = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve, reject) {
                        var nRet = 0;
                        var isready_time = setInterval(function () { return __awaiter(_this, void 0, void 0, function () {
                            var error_1;
                            return __generator(this, function (_a) {
                                switch (_a.label) {
                                    case 0:
                                        _a.trys.push([0, 2, , 3]);
                                        return [4 /*yield*/, gamepp.apiinfo.isready.promise()];
                                    case 1:
                                        nRet = _a.sent();
                                        return [3 /*break*/, 3];
                                    case 2:
                                        error_1 = _a.sent();
                                        console.log(error_1);
                                        return [3 /*break*/, 3];
                                    case 3:
                                        if (nRet === 1) {
                                            clearInterval(isready_time);
                                            resolve(1);
                                        }
                                        return [2 /*return*/];
                                }
                            });
                        }); }, 100);
                    })];
            });
        });
    };
    Background.prototype.Run = function () {
        return __awaiter(this, void 0, void 0, function () {
            var isclash, checkResult, err_1, result, resultI, timerFree;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        console.log('check if BG update');
                        console.log("Run 1");
                        //等待接口准备就绪
                        return [4 /*yield*/, this.IsReady()];
                    case 1:
                        //等待接口准备就绪
                        _a.sent();
                        console.log("Run 2");
                        this.registerNotifyReceiver();
                        // this.ConvertXMLData2JSON('init')//转换XML数据2 JSON
                        this.BGonConfigChanged();
                        this.AppEvent_OnWindowReadyToShow();
                        gamepp.webapp.onDesktopMonitorClosed.addEventListener(function (msg) { return __awaiter(_this, void 0, void 0, function () {
                            var isDesktopMonitorOpen;
                            var _this = this;
                            return __generator(this, function (_a) {
                                switch (_a.label) {
                                    case 0:
                                        if (!(msg['action'] == 'appClosed')) return [3 /*break*/, 3];
                                        if (gameclient.GAME_ProcessName)
                                            return [2 /*return*/];
                                        console.warn('桌面监控关闭');
                                        return [4 /*yield*/, gamepp.setting.getInteger.promise(COMMANDID.CM_USE_DESKTOP_FRAME)];
                                    case 1:
                                        isDesktopMonitorOpen = _a.sent();
                                        if (!(isDesktopMonitorOpen === 1)) return [3 /*break*/, 3];
                                        return [4 /*yield*/, gamepp.webapp.windows.close.promise(WindowName.Desktop_Monitor)];
                                    case 2:
                                        _a.sent();
                                        setTimeout(function () { return __awaiter(_this, void 0, void 0, function () {
                                            return __generator(this, function (_a) {
                                                switch (_a.label) {
                                                    case 0: return [4 /*yield*/, gamepp.webapp.windows.show.promise(WindowName.Desktop_Monitor)];
                                                    case 1:
                                                        _a.sent();
                                                        return [2 /*return*/];
                                                }
                                            });
                                        }); }, 1000);
                                        _a.label = 3;
                                    case 3: return [2 /*return*/];
                                }
                            });
                        }); });
                        //部分模块的注册放到了，升级模块注册函数中，所以这里得再调用一下
                        console.log("Run 3");
                        isclash = gamepp.createMutexWithName.sync('GamePPTwins') //false
                        ;
                        // //HWInfo检查更新
                        console.warn('32bit gamepp exist::', !isclash);
                        if (!!isclash) return [3 /*break*/, 2];
                        gamepp.webapp.windows.show.sync('onDesk_Conflict');
                        return [3 /*break*/, 13];
                    case 2:
                    //初始化GAMEPP模块
                    return [4 /*yield*/, gamepp.initUpdateModules.promise()];
                    case 3:
                        //初始化GAMEPP模块
                        _a.sent();
                        return [4 /*yield*/, gamepp.runSyncData.promise()];
                    case 4:
                        _a.sent();
                        return [4 /*yield*/, gamepp.startMainServices.promise()];
                    case 5:
                        _a.sent();
                        //HWInfo检查更新
                        return [4 /*yield*/, gamepp.update.checkHWInfo.promise()
                            //HWInfo检查更新
                        ];
                    case 6:
                        //HWInfo检查更新
                        _a.sent();
                        _a.label = 7;
                    case 7:
                        _a.trys.push([7, 9, , 10]);
                        return [4 /*yield*/, gamepp.update.checkHWInfo.promise()];
                    case 8:
                        checkResult = _a.sent();
                        console.warn('checkResult', checkResult);
                        return [3 /*break*/, 10];
                    case 9:
                        err_1 = _a.sent();
                        console.warn('checkHWInfoERR：：', err_1);
                        return [3 /*break*/, 10];
                    case 10: return [4 /*yield*/, this.getScript('js/HWInfoDataAnalyzer/HwInfoXMLProcess.js')];
                    case 11:
                        result = _a.sent();
                        console.warn('load HwInfoXMLProcess result', result);
                        return [4 /*yield*/, this.getScript('js/HWInfoDataAnalyzer/HWInfoSensorProcess.js')];
                    case 12:
                        resultI = _a.sent();
                        console.warn('load HWInfoSensorProcess result', resultI);
                        try {
                            gamepp.update.updateHWInfo.sync();
                        }
                        catch (err) {
                            console.warn('updateHWInfoERR：：', err);
                        }
                        console.warn('gamepp.update.updateHWInfo::');
                        //XML解析
                        if (result && resultI) {
                            this.ConvertXMLData2JSON('init'); //转换XML数据2 JSON
                        }
                        else {
                            console.warn('未进行XML解析');
                        }
                        if (gamepp.isSkipUpdate.sync()) {
                            if (!gamepp.isStartedWithOs.sync()) {
                                gamepp.webapp.windows.show.sync(WindowName.DESKTOP);
                                gamepp.webapp.windows.focus.sync(WindowName.DESKTOP);
                            }
                            gamepp.lansevice.start.promise();
                        }
                        else {
                            if (gamepp.isStartedWithOs.sync()) {
                                gamepp.webapp.windows.show.sync(WindowName.UPDATE, true);
                            }
                            else {
                                gamepp.webapp.windows.show.sync(WindowName.UPDATE);
                                gamepp.webapp.windows.focus.sync(WindowName.UPDATE);
                            }
                            gamepp.lansevice.start.promise();
                        }
                        console.log("%c _______  _______  _______  _______    _______  _______ \n" +
                            "(  ____ \\(  ___  )(       )(  ____ \\  (  ____ )(  ____ )\n" +
                            "| (    \\/| (   ) || () () || (    \\/  | (    )|| (    )|\n" +
                            "| |      | (___) || || || || (__      | (____)|| (____)|\n" +
                            "| | ____ |  ___  || |(_)| ||  __)     |  _____)|  _____)\n" +
                            "| | \\_  )| (   ) || |   | || (        | (      | (      \n" +
                            "| (___) || )   ( || )   ( || (____/\\  | )      | )      \n" +
                            "(_______)|/     \\||/     \\|(_______/  |/       |/       \n" +
                            "                                                        ", "color:#108FF0");
                        setTimeout(function () {
                            gamepp.game.ingame.RefreshActiveProcess.sync();
                            console.log('RefreshActiveProcess');
                        }, 8000);
                        _a.label = 13;
                    case 13:
                        timerFree = setInterval(function () {
                            // console.warn('bgInterval');
                            _this.AutoRecevice();
                        }, 1000);
                        this.initAutoCpuSet();
                        this.checkNeedUpdate();
                        return [2 /*return*/];
                }
            });
        });
    };
    Background.prototype.checkNeedUpdate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var nUpdateStatus;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, gamepp.update.checkUpdate.promise(2)];
                    case 1:
                        nUpdateStatus = _a.sent();
                        console.log('nUpdateStatus', nUpdateStatus);
                        if (nUpdateStatus === 1) {
                            window.localStorage.setItem('needCheckUpdate', '1');
                        }
                        else {
                            window.localStorage.removeItem('needCheckUpdate');
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    Background.prototype.initAutoCpuSet = function () {
        var dur = 10000;
        setInterval(function () {
            try {
                var enableCpuSet = JSON.parse(window.localStorage.getItem("enableCpuSet") || "false") === true;
                var localProcessListStr = window.localStorage.getItem("local_process");
                var processGroupsStr = window.localStorage.getItem("process_groups");
                if (enableCpuSet && localProcessListStr && processGroupsStr) {
                    var localProcessList_1 = JSON.parse(localProcessListStr);
                    var groupList = JSON.parse(processGroupsStr);
                    var _obj_1 = {};
                    groupList.forEach(function (_a) {
                        var id = _a.id, cpuSet = _a.cpuSet;
                        _obj_1[id] = cpuSet;
                    });
                    handleGetProcessList().then(function (systemProcessList) {
                        localProcessList_1.forEach(function (v) {
                            v.process = -1;
                            v.cpuSet = [];
                            try {
                                systemProcessList.forEach(function (item) {
                                    if (v.name === item.name) {
                                        if (v.process !== item.process) {
                                            v.process = item.process;
                                        }
                                        if (v.group) {
                                            v.cpuSet = _obj_1[v.group];
                                        }
                                        throw new Error('');
                                    }
                                });
                            }
                            catch (e) { }
                        });
                        for (var i = 0; i < localProcessList_1.length; i++) {
                            var v = localProcessList_1[i];
                            if (v['cpuSet'] && v['process'] != -1) { // 设置了cpuSet并且有这个进程
                                var processCpuSet = gamepp.getProcessCpuSet.sync(v['process']);
                                var CPUSET = v['cpuSet'];
                                if (!arraysAreEqual(processCpuSet, CPUSET)) {
                                    gamepp.setProcessCpuSet.promise(v['process'], CPUSET);
                                }
                            }
                        }
                    });
                }
            }
            catch (e) { }
        }, dur);
        function arraysAreEqual(arr1, arr2) {
            if (arr1.length !== arr2.length)
                return false;
            var set1 = new Set(arr1);
            var set2 = new Set(arr2);
            return __spreadArray([], set1, true).every(function (val) { return set2.has(val); });
        }
        function handleGetProcessList() {
            return __awaiter(this, void 0, void 0, function () {
                var processList;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0: return [4 /*yield*/, gamepp.getprocessList.promise()];
                        case 1:
                            processList = _a.sent();
                            return [2 /*return*/, processList];
                    }
                });
            });
        }
    };
    Background.prototype.getScript = function (url) {
        return __awaiter(this, void 0, void 0, function () {
            var index, bRet;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        index = 0;
                        _a.label = 1;
                    case 1:
                        if (!(index < 3)) return [3 /*break*/, 4];
                        return [4 /*yield*/, this.getScriptex(url)];
                    case 2:
                        bRet = _a.sent();
                        if (bRet) {
                            return [2 /*return*/, bRet];
                        }
                        _a.label = 3;
                    case 3:
                        index++;
                        return [3 /*break*/, 1];
                    case 4: return [2 /*return*/, false];
                }
            });
        });
    };
    Background.prototype.getScriptex = function (url) {
        return new Promise(function (resolve) {
            var script = document.createElement('script');
            script.type = 'text/javascript';
            script.src = url;
            console.warn('script.src:', script.src);
            script.onload = function () {
                console.log('Script loaded:', url);
                resolve(true); // 成功返回true
            };
            script.onerror = function () {
                resolve(false); // 失败返回false
            };
            document.head.appendChild(script);
        });
    };
    /**
     * 获取服务器配置文件信息
     */
    Background.prototype.GetServerGppConfig = function () {
        return __awaiter(this, void 0, void 0, function () {
            var response, response_obj, decode, object, regPos, VideoCon, i, type, id, value;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, gamepp.http.httpget.promise('https://config.gamepp.com/api/v2/getGppConfig', '')];
                    case 1:
                        response = _a.sent();
                        if (response) {
                            response_obj = JSON.parse(response);
                            if (response_obj['code'] === 200) {
                                decode = gamepp.user.getDecodeInfo.sync(response_obj['config']);
                                object = JSON.parse(decode);
                                regPos = /^[0-9]+.?[0-9]*/;
                                VideoCon = {};
                                for (i = 0; i < object.length; i++) {
                                    if (object[i].state === 1) {
                                        type = object[i].type;
                                        id = object[i].id, value = object[i].value;
                                        if (type === 1) {
                                            if (regPos.test(value)) {
                                                gamepp.setting.setInteger.sync(id, Number(value));
                                            }
                                        }
                                        else if (type === 2) {
                                            VideoCon[id] = value;
                                        }
                                    }
                                    if (object[i].id === 522) { // ai教练允不允许开
                                        window.localStorage.setItem('GppConfigAIMaster', object[i].value);
                                    }
                                }
                                if (VideoCon.length !== 0) {
                                    window.localStorage.setItem('GppConfig_url', JSON.stringify(VideoCon));
                                }
                            }
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 保存CPU GPU 名字
     * 更换硬件后恢复默认传感器index
     */
    Background.prototype.saveHardwareInformation = function (HwInfoJson) {
        var cpu_name = HwInfoJson.CPU.SubNode[0].ProcessorName;
        var gpu_name = HwInfoJson.GPU.SubNode[gpu_index].VideoChipset;
        var hdName_str = window.localStorage.getItem('cpu_gpu_Name');
        if (hdName_str !== null && hdName_str !== 'null' && hdName_str !== undefined) {
            var cpu_gpu_Name_data = JSON.parse(hdName_str);
            if (cpu_name !== cpu_gpu_Name_data.cpu || gpu_name !== cpu_gpu_Name_data.gpu) {
                window.localStorage.removeItem('monitor_setting');
                window.localStorage.removeItem('isUploadHD');
                window.localStorage.removeItem('chooseSensor_list_v1');
            }
        }
        var Object = {};
        Object['cpu'] = cpu_name;
        Object['gpu'] = gpu_name;
        window.localStorage.setItem('cpu_gpu_Name', JSON.stringify(Object));
    };
    Background.prototype.StartSpark = function () {
        return __awaiter(this, void 0, void 0, function () {
            var lastSelectedType, e_1, e_2, e_3, newStartData, json, _a;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        lastSelectedType = localStorage.getItem('lastSelectedType');
                        console.log(lastSelectedType);
                        if (!(lastSelectedType === 'Ryzen')) return [3 /*break*/, 6];
                        _b.label = 1;
                    case 1:
                        _b.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, gamepp.utils.sendstatics.promise(100802)];
                    case 2:
                        _b.sent();
                        return [3 /*break*/, 4];
                    case 3:
                        e_1 = _b.sent();
                        return [3 /*break*/, 4];
                    case 4: return [4 /*yield*/, gamepp.sparkserver.runRyzenAIClient.promise()]; //启动RyzenAI的版本调用
                    case 5:
                        _b.sent(); //启动RyzenAI的版本调用
                        return [3 /*break*/, 17];
                    case 6:
                        if (!(lastSelectedType === 'btn')) return [3 /*break*/, 12];
                        _b.label = 7;
                    case 7:
                        _b.trys.push([7, 9, , 10]);
                        return [4 /*yield*/, gamepp.utils.sendstatics.promise(100801)];
                    case 8:
                        _b.sent();
                        return [3 /*break*/, 10];
                    case 9:
                        e_2 = _b.sent();
                        return [3 /*break*/, 10];
                    case 10: return [4 /*yield*/, gamepp.sparkserver.runClient.promise()]; //启动通用模式的版本调用
                    case 11:
                        _b.sent(); //启动通用模式的版本调用
                        return [3 /*break*/, 17];
                    case 12:
                        _b.trys.push([12, 14, , 15]);
                        return [4 /*yield*/, gamepp.utils.sendstatics.promise(100803)];
                    case 13:
                        _b.sent();
                        return [3 /*break*/, 15];
                    case 14:
                        e_3 = _b.sent();
                        return [3 /*break*/, 15];
                    case 15: return [4 /*yield*/, gamepp.sparkserver.runUltraAIClient.promise()];
                    case 16:
                        _b.sent();
                        _b.label = 17;
                    case 17:
                        newStartData = {
                            type: 'GetSparkServerInfo',
                        };
                        json = JSON.stringify(newStartData);
                        _b.label = 18;
                    case 18:
                        _b.trys.push([18, 20, , 21]);
                        return [4 /*yield*/, gamepp.sparkserver.sendMessage.promise(json)];
                    case 19:
                        _b.sent();
                        return [3 /*break*/, 21];
                    case 20:
                        _a = _b.sent();
                        return [3 /*break*/, 21];
                    case 21:
                        sparkMessageListener = function (message) { return __awaiter(_this, void 0, void 0, function () {
                            var parsedMessage, errorMap, errorMessage, error_2;
                            return __generator(this, function (_a) {
                                switch (_a.label) {
                                    case 0:
                                        console.log(message);
                                        _a.label = 1;
                                    case 1:
                                        _a.trys.push([1, 8, , 9]);
                                        parsedMessage = JSON.parse(message);
                                        errorMap = {
                                            '-1': '模型未找到',
                                            '-2': '获取游戏窗口失败',
                                            '-3': '设备不支持',
                                            '-4': 'BLOB初始化失败',
                                            '-5': '捕获画面失败',
                                            '-6': 'YOLO推理初始化失败',
                                            '-7': '识别进程未匹配到游戏所在显示器连接的显卡',
                                            '-10': '当前已有推理程序运行',
                                            '-11': '模式没有下载',
                                            '-12': '服务未匹配到游戏所在显示器连接的显卡'
                                        };
                                        if (!errorMap.hasOwnProperty(String(parsedMessage.code))) return [3 /*break*/, 7];
                                        errorMessage = errorMap[String(parsedMessage.code)];
                                        console.log(errorMessage);
                                        this.InGame_Tips_Information(WindowName.IN_GAME_TipsPage, 30, errorMessage, 2);
                                        gamepp.webapp.sendInternalAppEvent.promise('ai_lab', parsedMessage.code);
                                        if (!gamepp.webapp.windows.isValid.sync('ingame_kdTips')) return [3 /*break*/, 3];
                                        return [4 /*yield*/, gamepp.webapp.windows.close.promise('ingame_kdTips')];
                                    case 2:
                                        _a.sent();
                                        _a.label = 3;
                                    case 3:
                                        if (!gamepp.webapp.windows.isValid.sync('ingame_highlightsavetips')) return [3 /*break*/, 5];
                                        return [4 /*yield*/, gamepp.webapp.windows.close.promise('ingame_highlightsavetips')];
                                    case 4:
                                        _a.sent();
                                        _a.label = 5;
                                    case 5:
                                        if (!gamepp.webapp.windows.isValid.sync('ingame_broadsword')) return [3 /*break*/, 7];
                                        return [4 /*yield*/, gamepp.webapp.windows.close.promise('ingame_broadsword')];
                                    case 6:
                                        _a.sent();
                                        _a.label = 7;
                                    case 7: return [3 /*break*/, 9];
                                    case 8:
                                        error_2 = _a.sent();
                                        console.error(error_2);
                                        return [3 /*break*/, 9];
                                    case 9: return [2 /*return*/];
                                }
                            });
                        }); };
                        sparkMessageListenerId = gamepp.sparkserver.onSparkMessage.addEventListener(sparkMessageListener);
                        return [2 /*return*/];
                }
            });
        });
    };
    Background.prototype.StopSpark = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, gamepp.sparkserver.stopClient.promise()];
                    case 1:
                        _a.sent();
                        if (sparkMessageListenerId) {
                            gamepp.sparkserver.onSparkMessage.removeEventListener(sparkMessageListenerId);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    return Background;
}());
var app = new Background();
app.Run(); //ConvertXMLData2JSON,Run
function handleSparkServerSwitch() {
    var switchGamepadData = window.localStorage.getItem('switchGamepad');
    var switchGamepad;
    try {
        switchGamepad = switchGamepadData ? JSON.parse(switchGamepadData) : [];
    }
    catch (error) {
        switchGamepad = [];
        localStorage.setItem('switchGamepad', JSON.stringify(switchGamepad));
    }
    // 取ai教练的开关状态
    var switchGamepad_aiMasterData = window.localStorage.getItem('switchGamepad_aiMaster');
    var switchGamepad_aiMaster;
    try {
        switchGamepad_aiMaster = switchGamepad_aiMasterData ? JSON.parse(switchGamepad_aiMasterData) : [];
    }
    catch (error) {
        switchGamepad_aiMaster = [];
        localStorage.setItem('switchGamepad_aiMaster', JSON.stringify(switchGamepad_aiMaster));
    }
    // 超能时刻开关状态
    var switchGamepad_HighLightData = window.localStorage.getItem('switchGamepad_aiSuperpower');
    var switchGamepad_HighLight;
    try {
        switchGamepad_HighLight = switchGamepad_HighLightData ? JSON.parse(switchGamepad_HighLightData) : [];
    }
    catch (e) {
        switchGamepad_HighLight = [];
        localStorage.setItem('switchGamepad_HighLight', JSON.stringify(switchGamepad_HighLight));
    }
    var hasTrueValue = switchGamepad.some(function (item) { return item.value === true; });
    var hasTrueValue_aiMaster = switchGamepad_aiMaster.some(function (item) { return item.value === true; });
    var hasTrueValue_HighLight = switchGamepad_HighLight.some(function (item) { return item.value === true; });
    if (hasTrueValue_HighLight) {
        try {
            gamepp.setting.setBool.sync(406, true);
            gamepp.setting.setBool.sync(41, true);
        }
        catch (_a) { }
    }
    if (hasTrueValue || hasTrueValue_aiMaster || hasTrueValue_HighLight) {
        console.log('switchGamepad', switchGamepad);
        console.log('switchGamepad_aiMaster', switchGamepad_aiMaster);
        if (window.SparkTimer)
            clearTimeout(window.SparkTimer)(window).SparkTimer = setTimeout(function () {
                app.StartSpark();
            }, 2000);
    }
    else {
        console.log('switchGamepad', switchGamepad);
        if (window.SparkTimer)
            clearTimeout(window.SparkTimer)(window).SparkTimer = setTimeout(function () {
                app.StopSpark();
            }, 2000);
    }
}
