<script setup lang="ts">
import {computed, reactive, watch,ref} from "vue";
import {processCoreAssignStore, ProcessWithGroup} from "../stores";
import {Sort, Remove, ArrowDownBold, CaretBottom, CaretTop} from "@element-plus/icons-vue";
import { useMessageBox } from '@/components/MessageBox/useMessageBox';
import TableHeaderAll from "./TableHeaderAll.vue";
import TableHeaderGroup from "./TableHeaderGroup.vue";
import DividerLine from "./DividerLine.vue";
import {useI18n} from "vue-i18n";
const { showMessageBox } = useMessageBox();

const $store = processCoreAssignStore()
const { t } = useI18n();
const group_process_checked:Set<string> = new Set()
const process_checked:Set<string> = new Set()
let zoomV:number = 1;
const data = reactive({
    search: '',
    group_name: '',
    full_checkbox: false,
    process_checked: [] as Array<string>,

    full_checkbox2: false,
    group_process_checked: [] as Array<string>,

    show_inactive_process: true,
    flexible_bar: true,

    sortBy: ['',''], // 根据什么排序

    show_system_process: true,

    divider_lines:[
      {name:'aname',className:'divider-line-name'},
      {name:'athread',className:'divider-line-thread'},
      {name:'acpu',className:'divider-line-cpu'},
      {name:'acpu-pp',className:'divider-line-cpu-pp'},
      {name:'agpu',className:'divider-line-gpu'},
      {name:'agpu-pp',className:'divider-line-gpu-pp'},
      {name:'amem',className:'divider-line-mem'},
      {name:'amem-pp',className:'divider-line-mem-pp'},
      {name:'group',className:'divider-line-group'},
    ],
    divider_lines2:[
      {name:'name',className:'divider-line-name'},
      {name:'thread',className:'divider-line-thread'},
      {name:'cpu',className:'divider-line-cpu'},
      {name:'cpu-pp',className:'divider-line-cpu-pp'},
      {name:'gpu',className:'divider-line-gpu'},
      {name:'gpu-pp',className:'divider-line-gpu-pp'},
      {name:'mem',className:'divider-line-mem'},
      {name:'mem-pp',className:'divider-line-mem-pp'},
    ],
})

watch(() => $store.display.activeGroup, (newVal) => {
    console.log(newVal)
    const name = $store.display.process_groups.find(item => item.id === newVal)?.name
    data.group_name = name ? name : '';
    process_checked.clear()
    data.process_checked = []
    data.full_checkbox = false
    group_process_checked.clear()
    data.group_process_checked = []
    data.full_checkbox2 = false
    data.sortBy = ['','']
})

function confirmChangeGroupName() {
    if (data.group_name === '') {
        showMessageBox(
            t('psc.tips'),
            t('psc.warning2'),
            () => {
                const i = $store.display.process_groups.findIndex(item => item.id === $store.display.activeGroup)
                if (i !== -1) {
                    $store.display.process_groups[i].name = data.group_name
                    $store.saveProcessGroup()
                }
            },
            () => {
                console.log('取消');
            }
        )
    } else {
        const i = $store.display.process_groups.findIndex(item => item.id === $store.display.activeGroup)
        if (i !== -1) {
            $store.display.process_groups[i].name = data.group_name
            $store.saveProcessGroup()
        }
    }
}

const allProcessTableData = computed(() => {
    if ($store.display.activeGroup === '') {
        const arr = JSON.parse(JSON.stringify($store.process_list))
        const arr2 = JSON.parse(JSON.stringify($store.display.local_process))
        let activeArr: Array<any> = []
        let inactiveArr: Array<any> = []
        const includeSet = new Set();
        arr.forEach((item: any) => {
            if (includeSet.has(item.name)) return
            includeSet.add(item.name)
            activeArr.push(item)
        })
        arr2.forEach((item: any) => {
            if (includeSet.has(item.name)) {
                activeArr.forEach((el:any)=>{
                    if (el.name === item.name) {
                        el.group = item.group
                    }
                })
            }
            includeSet.add(item.name)
            const findItem = arr.find((el: { name: any; }) => el.name === item.name)
            if (findItem) {
            } else {
                inactiveArr.push(item)
            }
        })
        if (!data.show_system_process) {
            activeArr = activeArr.filter(item => !item.path.includes('C:\\Windows'))
            inactiveArr = inactiveArr.filter(item => !item.path.includes('C:\\Windows'))
        }
        if (data.search) {
            activeArr = activeArr.filter((item: any) => {return item.name.toLowerCase().includes(data.search.toLowerCase())})
            inactiveArr = inactiveArr.filter((item: any) => {return item.name.toLowerCase().includes(data.search.toLowerCase())})
        }
        activeArr.forEach((item)=>{
            item.thread = showThreads(item);
            item.groupName = showGroupName(item);
        })
        inactiveArr.forEach((item)=>{
            item.thread = showThreads(item);
            item.groupName = showGroupName(item);
        })
        // 排序
        let sortKey = data.sortBy[0]
        if (sortKey) {
            if (sortKey === 'name' || sortKey === 'groupName') { // 根据进程名排序特殊处理,根据首字母排序
                if (data.sortBy[1] === 'desc') { // 降序
                    activeArr.sort((b:any,a:any)=>{
                        return a[sortKey].localeCompare(b[sortKey],'zh')
                    })
                    inactiveArr.sort((a:any,b:any)=>{
                        return a[sortKey].localeCompare(b[sortKey],'zh')
                    })
                }else{
                    activeArr.sort((a:any,b:any)=>{
                        return a[sortKey].localeCompare(b[sortKey],'zh')
                    })
                    inactiveArr.sort((a:any,b:any)=>{
                        return a[sortKey].localeCompare(b[sortKey],'zh')
                    })
                }
            }else{
                if (data.sortBy[1] === 'desc') { // 降序
                    activeArr.sort((a:any,b:any)=>{
                        return b[sortKey] - a[sortKey]
                    })
                    inactiveArr.sort((a:any,b:any)=>{
                        return b[sortKey] - a[sortKey]
                    })
                }else{
                    activeArr.sort((a:any,b:any)=>{
                        return a[sortKey] - b[sortKey]
                    })
                    inactiveArr.sort((a:any,b:any)=>{
                        return a[sortKey] - b[sortKey]
                    })
                }
            }

        }
        return [activeArr, inactiveArr]
    }
    return [[], []]
})
const groupProcessTableData = computed(() => {
    if ($store.display.activeGroup !== '') {
        const arr = JSON.parse(JSON.stringify($store.display.local_process)).filter((item: any) => {
            return item.group === $store.display.activeGroup
        })
        let activeArr: Array<any> = []
        let inactiveArr: Array<any> = []
        arr.forEach((item: any) => {
            const findItem = $store.process_list.find(el => el.name === item.name)
            if (findItem) {
                item.pid = findItem.pid
                activeArr.push(item)
            } else {
                item.pid = -1
                inactiveArr.push(item)
            }
        })
        if (data.search) {
            activeArr = activeArr.filter((item: any) => {return item.name.toLowerCase().includes(data.search.toLowerCase())})
            inactiveArr = inactiveArr.filter((item: any) => {return item.name.toLowerCase().includes(data.search.toLowerCase())})
        }
        // 排序
        const sortKey = data.sortBy[0]
        if (sortKey) {
            if (sortKey === 'name') { // 根据进程名排序特殊处理,根据首字母排序
                if (data.sortBy[1] === 'desc') { // 降序
                    activeArr.sort((b:any,a:any)=>{
                        return a.name.localeCompare(b.name,'zh')
                    })
                    inactiveArr.sort((a:any,b:any)=>{
                        return a.name.localeCompare(b.name,'zh')
                    })
                }else{
                    activeArr.sort((a:any,b:any)=>{
                        return a.name.localeCompare(b.name,'zh')
                    })
                    inactiveArr.sort((a:any,b:any)=>{
                        return a.name.localeCompare(b.name,'zh')
                    })
                }
            }else{
                if (data.sortBy[1] === 'desc') { // 降序
                    activeArr.sort((a:any,b:any)=>{
                        return b[sortKey] - a[sortKey]
                    })
                    inactiveArr.sort((a:any,b:any)=>{
                        return b[sortKey] - a[sortKey]
                    })
                }else{
                    activeArr.sort((a:any,b:any)=>{
                        return a[sortKey] - b[sortKey]
                    })
                    inactiveArr.sort((a:any,b:any)=>{
                        return a[sortKey] - b[sortKey]
                    })
                }
            }

        }
        return [activeArr, inactiveArr]
    }
    return [[], []]
})

const showThreads = (process:ProcessWithGroup)=>{
    if (process.group) {
        for (const g of $store.display.process_groups) {
            if (g.id === process.group) {
                return g.cpuSet.length;
            }
        }
        return $store.cpuThreads
    }
    return $store.cpuThreads
}

function showGroupName(process:ProcessWithGroup) {
    if (process.group === '' || !process.group) {
        return ''
    }else{
        for (const g of $store.display.process_groups) {
            if (g.id === process.group) {
                return t(g.name);
            }
        }
    }
}

let dragging = false
let dragItem = ''
let startX = 0
const minWidths: any = {
    '--name-w': 140,
    '--cpu-pp-w': 80,
    '--gpu-pp-w': 80,
    '--mem-pp-w': 80,
    '--all-name-w': 140,
    '--all-cpu-pp-w': 80,
    '--all-gpu-pp-w': 80,
    '--all-mem-pp-w': 80,
    '--all-group-w': 100,
};
function dragDivider(type:string, event: MouseEvent) {
    zoomV = gamepp.display.getScaleFromWindowInMonitor.sync() // 缩放比例
    const matchCssVar:any = {
        'name': '--name-w',
        'thread': '--thread-w',
        'cpu': '--cpu-w',
        'cpu-pp': '--cpu-pp-w',
        'gpu': '--gpu-w',
        'gpu-pp': '--gpu-pp-w',
        'mem': '--mem-w',
        'mem-pp': '--mem-pp-w',
        'controls': '--controls-w',
        'aname': '--all-name-w',
        'athread': '--all-thread-w',
        'acpu': '--all-cpu-w',
        'acpu-pp': '--all-cpu-pp-w',
        'agpu': '--all-gpu-w',
        'agpu-pp': '--all-gpu-pp-w',
        'amem': '--all-mem-w',
        'amem-pp': '--all-mem-pp-w',
        'group': '--all-group-w',
        'acontrols': '--all-controls-w',
    };
    (window as any).dragging = true
    dragging = true
    dragItem = matchCssVar[type]
    startX = event.clientX / zoomV; // 记录鼠标按下时的初始位置
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
}
function handleMouseMove(event:MouseEvent) {
    if (!dragging) return

    // requestAnimationFrame用来解决鼠标拖快了卡顿的问题
    requestAnimationFrame(() => {
        // 计算鼠标移动的距离
        const deltaX = (event.clientX / zoomV) - startX;
        startX = event.clientX / zoomV; // 更新初始位置为当前鼠标位置

        // 获取当前的 CSS 变量值
        const currentWidth = parseFloat(getComputedStyle(document.documentElement).getPropertyValue(dragItem));

        // 计算新的宽度
        let newWidth = currentWidth + deltaX;
        if (document.body.dir == 'rtl') {
            newWidth = currentWidth - deltaX;
        }

        // 最低宽度
        const minWidth = minWidths[dragItem] || 65;
        if (newWidth < minWidth) {
            newWidth = minWidth;
        }
        // 更新 CSS 变量值
        document.documentElement.style.setProperty(dragItem, `${newWidth}px`);
    });
}
function handleMouseUp() {
    (window as any).dragging = false;
    dragging = false;
    dragItem = '';
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
}

function showOrHideInactiveProcess () {
    data.show_inactive_process = !data.show_inactive_process
}

function changeProcessGroup (groupId:string,process:any) {
    if ($store.display.activeGroup === '') { // 位于所有进程页面
        $store.addLocalProcess(process,groupId)
    }
    $store.changeProcessGroup(groupId,process)
}
function handleFullCheck(v:boolean) {
    if (v) {
        allProcessTableData.value[0].forEach((item:any)=>{
            process_checked.add(item.name)
        })
        allProcessTableData.value[1].forEach((item:any)=>{
            process_checked.add(item.name)
        })
        data.process_checked = Array.from(process_checked)
    }else{
        process_checked.clear()
        data.process_checked = Array.from(process_checked)
    }
}
function handleFullCheck2(v:boolean) {
    if (v) {
        groupProcessTableData.value[0].forEach((item:any)=>{
            group_process_checked.add(item.name)
        })
        groupProcessTableData.value[1].forEach((item:any)=>{
            group_process_checked.add(item.name)
        })
        data.group_process_checked = Array.from(group_process_checked)
    }else{
        group_process_checked.clear()
        data.group_process_checked = Array.from(group_process_checked)
    }
}
watch(()=>data.group_process_checked,()=>{
    if (groupProcessTableData.value[0].length + groupProcessTableData.value[1].length < 1) {
        data.full_checkbox2 = false
    }else{
        data.full_checkbox2 = data.group_process_checked.length >= groupProcessTableData.value[0].length + groupProcessTableData.value[1].length;
    }
})
watch(()=>data.process_checked,()=>{
    data.full_checkbox = data.process_checked.length >= allProcessTableData.value[0].length + allProcessTableData.value[1].length;
})

function operationsMultipleProcesses (type:string,groupId?:string) {
    if ($store.display.activeGroup === '') { // 位于所有进程
        if (data.process_checked.length === 0) {
            // @ts-ignore
            ElMessage.warning(t('psc.pleaseCheckProcess'))
            return
        }
        if (type === 'update' && groupId) {
            // 修改分组
            const arr = [...allProcessTableData.value[0],...allProcessTableData.value[1]]
            data.process_checked.forEach((name:string)=>{
                const process = arr.find(item=>item.name === name)
                if (process) {
                    $store.addLocalProcess(process,groupId)
                    $store.changeProcessGroup(groupId,process)
                }
            })
        }else if (type === 'remove') {
            // 移出分组
            const arr = [...allProcessTableData.value[0],...allProcessTableData.value[1]]
            data.process_checked.forEach((name:string)=>{
                const process = arr.find(item=>item.name === name)
                if (process) {
                    $store.addLocalProcess(process,groupId)
                    $store.removeProcessFromGroup(process)
                }
            })
        }else if (type === 'new group') {
            // 创建新分组
            const newGroupId = $store.addProcessGroup()
            const arr = [...allProcessTableData.value[0],...allProcessTableData.value[1]]
            data.process_checked.forEach((name:string)=>{
                const process = arr.find(item=>item.name === name)
                console.log(process)
                if (process) {
                    $store.addLocalProcess(process,groupId)
                    $store.changeProcessGroup(newGroupId,process)
                }
            })
            $store.display.activeGroup = newGroupId
        }
        data.process_checked = []
        process_checked.clear()
        return;
    }
    if (data.group_process_checked.length === 0) {
        // @ts-ignore
        ElMessage.warning(t('psc.pleaseCheckProcess'))
        return
    }
    if (type === 'update' && groupId) {
        // 修改分组
        data.group_process_checked.forEach((name:string)=>{
            const process = $store.display.local_process.find(item=>item.name === name)
            if (process) {
                $store.changeProcessGroup(groupId,process)
            }
        })
    }else if (type === 'remove') {
        // 移出分组
        data.group_process_checked.forEach((name:string)=>{
            const process = $store.display.local_process.find(item=>item.name === name)
            if (process) {
                $store.removeProcessFromGroup(process)
            }
        })
    }else if (type === 'new group') {
        // 创建新分组
        const newGroupId = $store.addProcessGroup()
        data.group_process_checked.forEach((name:string)=>{
            const process = $store.display.local_process.find(item=>item.name === name)
            if (process) {
                $store.changeProcessGroup(newGroupId,process)
            }
        })
        $store.display.activeGroup = newGroupId
    }
}

function setSort(str: string) {
    console.log('sortBy',str)
    if (data.sortBy[0] === str) {
        if (data.sortBy[1] === 'desc') {
            data.sortBy = [str,'asc'] // 点第二次设置成升序
        } else {
            data.sortBy = ['',''] // 点第三次设置成默认排序
        }
    }else{
        data.sortBy = [str,'desc'] // 点第一次设置成降序
    }
}

function handleClickListItem(str:string) {
    if (process_checked.has(str)) {
        data.process_checked = data.process_checked.filter(item=>item!==str)
        process_checked.delete(str)
    }else{
        data.process_checked.push(str)
        process_checked.add(str)
    }
}

function handleClickListItem2(str:string) {
    if (group_process_checked.has(str)) {
        data.group_process_checked = data.group_process_checked.filter(item=>item!==str)
        group_process_checked.delete(str)
    }else{
        data.group_process_checked.push(str)
        group_process_checked.add(str)
    }
}

// region
let isDragginListItem = ref(false)
let dragItems = ref<Array<string>>([])
let showDragInfo = reactive({icon:'',text:''})
let startPos = reactive({x:0,y:0});
let currentPos = {x:0,y:0};
const dragPreView = ref()
function handleMouseOverListItem (e:MouseEvent,str:string,icon:string) {
    zoomV = gamepp.display.getScaleFromWindowInMonitor.sync(); // 缩放比例
    (window as any).dragging2 = true
    showDragInfo.icon = icon
    dragItems.value = [];
    if (data.process_checked.length + data.group_process_checked.length > 1) {
        // 多选拖动
        dragItems.value = [...Array.from(data.process_checked),...Array.from(data.group_process_checked)]
        showDragInfo.text = `${str} ...`
        if (!dragItems.value.includes(str)) {
            dragItems.value.push(str)
            showDragInfo.text = `${str} ...`
        }
    }else{
        // 未勾选拖动
        dragItems.value = [str]
        showDragInfo.text = str
    }

    // 记录初始位置
    startPos.x = e.pageX / zoomV;
    startPos.y = e.pageY / zoomV;

    // 设置初始预览位置
    currentPos.x = e.pageX / zoomV - 60;
    currentPos.y = e.pageY / zoomV - 13;

    // 添加全局事件监听
    document.addEventListener('mousemove', handleDragListItemMouseMove);
    document.addEventListener('mouseup', handleDragListItemMouseUp);
}
function handleDragListItemMouseMove (e:MouseEvent) {
    requestAnimationFrame(()=>{ // 在下次重绘前执行，避免执行太多次
        if (!isDragginListItem.value) {
            const dx = e.clientX - startPos.x;
            const dy = e.clientY - startPos.y;

            // 移动超过 5px 才认为是拖动
            if (Math.sqrt(dx*dx + dy*dy) > 5) {
                isDragginListItem.value = true;
            }
        }else{
            // 更新预览位置
            currentPos.x += e.pageX - startPos.x;
            currentPos.y += e.pageY - startPos.y;
            dragPreView.value.style.transform = `translate(${currentPos.x / zoomV}px, ${currentPos.y / zoomV}px)`;

            // 更新初始位置
            startPos.x = e.pageX;
            startPos.y = e.pageY;
        }
    })
}
function handleDragListItemMouseUp () {
    if ($store.mouseEnterGroupId) {
        const groupId = $store.mouseEnterGroupId
        let arr = []
        if ($store.display.activeGroup === '') {
            arr = [...allProcessTableData.value[0],...allProcessTableData.value[1]]
        }else{
            arr = [...groupProcessTableData.value[0],...groupProcessTableData.value[1]]
        }
        dragItems.value.forEach((name:string)=>{
            const findV = arr.find(item=>item.name === name)
            if (findV) {
                $store.addLocalProcess(findV,groupId)
                $store.changeProcessGroup(groupId,findV)
            }
        })
    }
    (window as any).dragging2 = false
    isDragginListItem.value = false;
    dragItems.value = []
    showDragInfo.text = '';
    // 移除全局事件监听
    document.removeEventListener('mousemove', handleDragListItemMouseMove);
    document.removeEventListener('mouseup', handleDragListItemMouseUp);
}
// region end

function removeProcessGroup() {
    showMessageBox(t('psc.tips'),
        t('psc.warning3'),
        ()=>{
            $store.removeProcessGroup($store.display.activeGroup)
        },
        ()=>{

        },
        t('messages.cancel'),
        t('messages.confirm'),
    )
}

function formatterInput (s: string) {
    if (s === 'psc.notGameProcess' || s === 'psc.unNamedProcess') {
        return t(s)
    }else{
        return s
    }
}
</script>

<template>
    <div class="right-layout">
        <!--所有进程界面-->
        <div class="all-process" v-if="$store.display.activeGroup === ''">
            <div class="search-line">
                <div class="tip">
                    {{$t('psc.littletips')}}
                </div>
                <div class="showOrHideSystemProcess">
                    <span>{{$t('psc.displaySystemProcess')}}</span>
                    <el-switch v-model="data.show_system_process" active-color="#508DE2"></el-switch>
                </div>
                <div>
                    <el-input :placeholder="$t('psc.search')" v-model="data.search"></el-input>
                </div>
            </div>

            <section>
                <div class="wrap" :style="{height:data.flexible_bar? 'calc(100% - 49px)':'100%'}">
                    <TableHeaderAll :data="data" :set-sort="setSort" :handleFullCheck="handleFullCheck"></TableHeaderAll>
                    <div class="scroll-box scroll-y">
                        <!--活动的进程-->
                        <div
                            class="list-item"
                            v-for="item in allProcessTableData[0]"
                            :key="item.name"
                            :class="{'checked': data.process_checked.includes(item.name) || dragItems.includes(item.name)}"
                            @click="handleClickListItem(item.name)"
                            @mousedown="handleMouseOverListItem($event,item.name,item.icon)"
                        >
                            <div class="name">
                                <span @click.stop="">
                                <el-checkbox-group v-model="data.process_checked">
                                    <el-checkbox :value="item.name"></el-checkbox>
                                </el-checkbox-group>
                                </span>
                                <div class="point"></div>
                                <img v-if="item.icon" :src="item.icon" alt="">
                                <span class="process-name">{{ item.name }}</span>
                            </div>
                            <div class="thread" :class="{'is-active':$store.enableCpuSet}">{{item.thread}}</div>
                            <div class="cpu">{{item.cpu_usage}}%</div>
                            <div class="cpu-pp">{{item.cpu_pp.toFixed(2)}}%</div>
                            <div class="gpu">{{item.gpu_usage}}%</div>
                            <div class="gpu-pp">{{item.gpu_pp.toFixed(2)}}%</div>
                            <div class="mem">{{item.mem_usage}}%</div>
                            <div class="mem-pp">{{item.mem_pp.toFixed(2)}}%</div>
                            <div class="group">{{$t(item.groupName)}}</div>
                            <div class="controls" @click.stop="">
                                <el-dropdown @command="(e)=>changeProcessGroup(e,item)" trigger="click">
                                    <span class="iconSort"><el-icon><Sort /></el-icon>{{$t('psc.editGroup')}}</span>
                                    <template #dropdown>
                                        <el-dropdown-menu>
                                            <el-dropdown-item
                                                v-for="group in $store.display.process_groups"
                                                :command="group.id"
                                            >{{$t(group.name)}}</el-dropdown-item>
                                        </el-dropdown-menu>
                                    </template>
                                </el-dropdown>
                            </div>
                        </div>
                        <div class="collapse-handler" @click="showOrHideInactiveProcess">
                            {{$t('psc.inactiveProcess')}}
                            <span class="iconfont icon-hideshow" :class="{'ro180':!data.show_inactive_process}"></span>
                        </div>
                        <!--未活动进程-->
                        <div class="list-item"
                             v-for="item in allProcessTableData[1]"
                             :key="item.name"
                             :class="{'checked': data.process_checked.includes(item.name) || dragItems.includes(item.name)}"
                             @click="handleClickListItem(item.name)"
                             @mousedown="handleMouseOverListItem($event,item.name,item.name)"
                             v-if="data.show_inactive_process"
                        >
                            <div class="name">
                                <span @click.stop>
                                    <el-checkbox-group v-model="data.process_checked">
                                    <el-checkbox :value="item.name"></el-checkbox>
                                </el-checkbox-group>
                                </span>
                                <div class="point inactive"></div>
                                <img v-if="item.icon" :src="item.icon" alt="">
                                <span class="process-name">{{ item.name }}</span>
                            </div>
                            <div class="thread" style="color: #777777">{{item.thread}}</div>
                            <div class="cpu"></div>
                            <div class="cpu-pp">{{item.cpu_pp.toFixed(2)}}%</div>
                            <div class="gpu"></div>
                            <div class="gpu-pp">{{item.gpu_pp.toFixed(2)}}%</div>
                            <div class="mem"></div>
                            <div class="mem-pp">{{item.mem_pp.toFixed(2)}}%</div>
                            <div class="group">{{showGroupName(item)}}</div>
                            <div class="controls" @click.stop>
                                <el-dropdown @command="(e)=>changeProcessGroup(e,item)" trigger="click">
                                    <span class="iconSort"><el-icon><Sort /></el-icon>{{$t('psc.editGroup')}}</span>
                                    <template #dropdown>
                                        <el-dropdown-menu>
                                            <el-dropdown-item
                                                v-for="group in $store.display.process_groups"
                                                :command="group.id"
                                            >{{group.name}}</el-dropdown-item>
                                        </el-dropdown-menu>
                                    </template>
                                </el-dropdown>
                            </div>
                        </div>
                    </div>
                    <DividerLine :list="data.divider_lines" :drag-divider="dragDivider"/>
                </div>
            </section>
        </div>
        <!--组内进程界面-->
        <div class="group-process" v-else>
            <div class="choose-core">
                <span class="title">{{$t('psc.CoreAssign')}}</span>
                <div>
                    <el-button type="primary" color="#336AB5" @click="$store.display.showChooseCPUSetDialog = true">{{$t('psc.selectTheCore')}}</el-button>
                </div>
            </div>

            <div class="group-name">
                <span class="title">{{$t('psc.groupName')}}</span>

                <div style="width:250px;">
                    <el-input v-model="data.group_name" :formatter="formatterInput"></el-input>
                </div>
                <div class="confirmBtn" style="width: 60px;">
                    <el-button color="#336AB5" @click="confirmChangeGroupName">{{$t('messages.confirm')}}</el-button>
                </div>

                <div class="remove-group" @click="removeProcessGroup"><span class="iconfont icon-remove"></span> {{$t('psc.delGroup')}}</div>
            </div>

            <div class="search-line">
                <div style="width: 136px;height: 30px;">
                    <el-button type="primary" color="#343647" @click="$store.display.showAddProcessDialog = true">
                        {{$t('psc.addNowProcess')}}
                    </el-button>
                </div>
                <div>
                    <el-input :placeholder="$t('psc.search')" v-model="data.search"></el-input>
                </div>
            </div>

            <section>
                <div class="wrap" :style="{height:data.flexible_bar? 'calc(100% - 49px)':'100%'}">
                    <TableHeaderGroup :data="data" :set-sort="setSort" :handleFullCheck="handleFullCheck2"></TableHeaderGroup>
                    <div class="scroll-box scroll-y">
                        <div class="empty-bg" v-if="groupProcessTableData[0].length === 0 && groupProcessTableData[1].length === 0"></div>
                        <!--活动的进程-->
                        <div class="list-item"
                             v-for="item in groupProcessTableData[0]"
                             :key="item.name"
                             @click="handleClickListItem2(item.name)"
                             :class="{'checked': data.group_process_checked.includes(item.name) || dragItems.includes(item.name)}"
                             @mousedown="handleMouseOverListItem($event,item.name,item.icon)">
                            <div class="name">
                                <span @click.stop="">
                                <el-checkbox-group v-model="data.group_process_checked">
                                    <el-checkbox :value="item.name"></el-checkbox>
                                </el-checkbox-group>
                                </span>
                                <div class="point"></div>
                                <img v-if="item.icon" :src="item.icon" alt="">
                                <span class="process-name">{{ item.name }}</span>
                            </div>
                            <div class="thread" :class="{'is-active':$store.enableCpuSet}">{{showThreads(item)}}</div>
                            <div class="cpu">{{item.cpu_usage}}%</div>
                            <div class="cpu-pp">{{item.cpu_pp.toFixed(2)}}%</div>
                            <div class="gpu">{{item.gpu_usage}}%</div>
                            <div class="gpu-pp">{{item.gpu_pp.toFixed(2)}}%</div>
                            <div class="mem">{{item.mem_usage}}%</div>
                            <div class="mem-pp">{{item.mem_pp.toFixed(2)}}%</div>
                            <div class="controls" @click.stop>
                                <el-dropdown @command="(e)=>changeProcessGroup(e,item)" trigger="click">
                                    <span class="iconSort"><el-icon><Sort /></el-icon>{{$t('psc.editGroup')}}</span>
                                    <template #dropdown>
                                        <el-dropdown-menu>
                                            <el-dropdown-item
                                                v-for="group in $store.display.process_groups"
                                                :command="group.id"
                                            >{{$t(group.name)}}</el-dropdown-item>
                                        </el-dropdown-menu>
                                    </template>
                                </el-dropdown>
                                <span class="iconRemove" @click="$store.removeProcessFromGroup(item)"><el-icon><Remove /></el-icon>{{$t('psc.shiftOut')}}</span>
                            </div>
                        </div>
                        <div class="collapse-handler" @click="showOrHideInactiveProcess" v-if="groupProcessTableData[1].length > 0">
                            {{$t('psc.inactiveProcess')}}
                            <span class="iconfont icon-hideshow" :class="{'ro180':!data.show_inactive_process}"></span>
                        </div>
                        <!--未活动进程-->
                        <div class="list-item"
                             v-for="item in groupProcessTableData[1]"
                             :key="item.name" v-if="data.show_inactive_process"
                             @click="handleClickListItem2(item.name)"
                             :class="{'checked': data.group_process_checked.includes(item.name) || dragItems.includes(item.name)}"
                             @mousedown="handleMouseOverListItem($event,item.name,item.icon)">
                            <div class="name">
                                <span @click.stop="">
                                <el-checkbox-group v-model="data.group_process_checked">
                                    <el-checkbox :value="item.name"></el-checkbox>
                                </el-checkbox-group>
                                </span>
                                <div class="point inactive"></div>
                                <img v-if="item.icon" :src="item.icon" alt="">
                                <span class="process-name">{{ item.name }}</span>
                            </div>
                            <div class="thread" style="color: #777777">{{showThreads(item)}}</div>
                            <div class="cpu"></div>
                            <div class="cpu-pp">{{item.cpu_pp.toFixed(2)}}%</div>
                            <div class="gpu"></div>
                            <div class="gpu-pp">{{item.gpu_pp.toFixed(2)}}%</div>
                            <div class="mem"></div>
                            <div class="mem-pp">{{item.mem_pp.toFixed(2)}}%</div>
                            <div class="controls" @click.stop="">
                                <el-dropdown @command="(e)=>changeProcessGroup(e,item)" trigger="click">
                                    <span class="iconSort"><el-icon><Sort /></el-icon>{{$t('psc.editGroup')}}</span>
                                    <template #dropdown>
                                        <el-dropdown-menu>
                                            <el-dropdown-item
                                                v-for="group in $store.display.process_groups"
                                                :command="group.id"
                                            >{{$t(group.name)}}</el-dropdown-item>
                                        </el-dropdown-menu>
                                    </template>
                                </el-dropdown>
                                <span class="iconRemove" @click="$store.removeProcessFromGroup(item)"><el-icon><Remove /></el-icon>{{$t('psc.shiftOut')}}</span>
                            </div>
                        </div>
                    </div>
                    <DividerLine :list="data.divider_lines2" :drag-divider="dragDivider"/>
                </div>
            </section>
        </div>

        <div class="flexible-bar">
            <div class="bar" @click="data.flexible_bar = !data.flexible_bar">
                <el-icon v-show="data.flexible_bar"><CaretBottom /></el-icon>
                <el-icon v-show="!data.flexible_bar"><CaretTop /></el-icon>
            </div>
            <div class="bar-content" :style="{height:data.flexible_bar?'49px':'0px' }">
                <el-dropdown trigger="click" @command="(groupId)=>operationsMultipleProcesses('update',groupId)">
                    <el-button type="primary" color="#3E4050">{{$t('psc.editGroup')}}<el-icon><ArrowDownBold /></el-icon></el-button>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item
                                v-for="group in $store.display.process_groups"
                                :command="group.id"
                            >{{$t(group.name)}}</el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
                <el-button v-show="$store.display.activeGroup !== ''" type="primary" color="#3E4050" @click="operationsMultipleProcesses('remove')" style="margin-left: 10px;">{{$t('psc.moveOutGrouping')}}</el-button>
                <el-button type="primary" color="#3E4050" @click="operationsMultipleProcesses('new group')">{{$t('psc.createANewGroup')}}</el-button>
            </div>
        </div>
    </div>
    <div class="drag-preview" v-show="isDragginListItem" ref="dragPreView">
        <img :src="showDragInfo.icon" alt=""> {{showDragInfo.text}}
    </div>
</template>

<style scoped lang="scss">
.right-layout {
  width: calc(var(--page-ww) - 180px * var(--zoomV--));
  height: 100%;
  background-color: #2B2C37;
  padding-left: 10px;
  padding-top: 16px;
  font-size: 12px;

  :deep(.el-button) {
    font-size: 12px;
  }

  :deep(.el-input__inner) {
    font-size: 12px;
  }

  .all-process {
    section {
      width: 100%;
      height: calc(var(--page-hh) - 130px);
      padding-top: 10px;
      .wrap {
        width: calc(var(--page-ww) - 245px);
        height: 100%;
        display: flex;
        flex-flow: column nowrap;
        font-size: 12px;
        color: #777777;
        line-height: 30px;
        position: relative;
        overflow-x: auto;
        &::-webkit-scrollbar {
          height: 5px;
        }

        &::-webkit-scrollbar-track {
          background-color: #21222a;
        }

        &::-webkit-scrollbar-thumb {
          background-color: #71738c;
          border-radius: 2px;
        }

        .scroll-box {
          width: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) + var(--all-cpu-pp-w) + var(--all-gpu-w) + var(--all-gpu-pp-w) + var(--all-mem-w) + var(--all-mem-pp-w) + var(--all-group-w) + var(--all-controls-w));
          height: calc(100% - 30px);
          position: relative;
          overflow-x: hidden;

          .list-item {
            display: flex;
            flex-flow: row nowrap;
            width: 100%;
            height: 30px;
            cursor: pointer;
            border-radius: 4px;

            .iconSort {
              margin-left: 5px;
              font-size:12px;
              color: #777777;
            }
            &:hover,&.checked {
              background-color: #343647;
              color: #ffffff;
              .process-name{color: #ffffff;}
              .iconRemove {color: #BF4040;}
            }
          }

          .collapse-handler {
            padding-left: 10px;
            cursor: pointer;

            .iconfont {
              font-size: 10px;
              transition: all .3s linear;
              transform: rotate(0deg);
              display: inline-block;
            }

            .ro180 {
              transform: rotate(180deg);
            }
          }
        }

        .name, .thread, .cpu, .cpu-pp, .gpu, .gpu-pp, .mem, .mem-pp, .group,.controls {
          display: flex;
          flex-flow: row nowrap;
          align-items: center;
          justify-content: flex-end;
          padding-right: 5px;
          gap: 10px;
          position: absolute;
        }

        .cpu-pp,.gpu-pp,.mem-pp{
          .iconfont {
            font-size: 10px;
            display: inline-block;
            transform: translateY(-5px);
          }
        }
        .thread {
          color: #777777;
          &.is-active {
            color: #35D57D;
          }
        }
        .controls {
          justify-content: flex-start;
          padding-left: 5px;

          span,.iconSort {
            display: flex;
            flex-flow: row nowrap;
            align-items: center;
            gap: 5px;
            cursor: pointer;
          }
        }
        .name {
          text-align: left;
          width: var(--all-name-w);
          left: 0;
          justify-content: flex-start;
          padding-left: 10px;
          padding-right: 0;

          img {
            width: 20px;
            height: 20px;
          }

          .process-name {
            width: calc(var(--all-name-w) - 90px);
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            color: #888888;
          }

          .point {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #35D57D;

            &.inactive {
              background-color: #666666;
            }
          }
        }

        .thread {
            left: var(--all-name-w);
          width: var(--all-thread-w);
        }

        .cpu {
            left: calc(var(--all-name-w) + var(--all-thread-w));
          width: var(--all-cpu-w);
        }

        .cpu-pp {
            left: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w));
          width: var(--all-cpu-pp-w);
        }

        .gpu {
            left: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) + var(--all-cpu-pp-w));
          width: var(--all-gpu-w);
        }

        .gpu-pp {
            left: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) + var(--all-cpu-pp-w) + var(--all-gpu-w));
          width: var(--all-gpu-pp-w);
        }

        .mem {
            left: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) + var(--all-cpu-pp-w) + var(--all-gpu-w) + var(--all-gpu-pp-w));
          width: var(--all-mem-w);
        }

        .mem-pp {
            left: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) + var(--all-cpu-pp-w) + var(--all-gpu-w) + var(--all-gpu-pp-w) + var(--all-mem-w));
          width: var(--all-mem-pp-w);
        }

        .group {
            left: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) + var(--all-cpu-pp-w) + var(--all-gpu-w) + var(--all-gpu-pp-w) + var(--all-mem-w) + var(--all-mem-pp-w));
          width: var(--all-group-w);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          justify-content: flex-start;
          padding-left: 5px;
          padding-right: 0;
        }

        .controls {
          height: 30px;
            left: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) + var(--all-cpu-pp-w) + var(--all-gpu-w) + var(--all-gpu-pp-w) + var(--all-mem-w) + var(--all-mem-pp-w) + var(--all-group-w));
          width: var(--all-controls-w);
        }
      }
    }
  }

  .search-line {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    padding-right: 10px;

    & > div {
      //width: 210px;
      height: 30px;
    }

    .tip {
        margin-right: auto;
        color: #999;
        font-size: 12px;
        line-height: 30px;
    }

    :deep(.el-input__inner) {
      font-size: 12px;
      color: #ffffff;
    }

    :deep(.el-input__inner::placeholder) {
      color: #666666;
    }

    .showOrHideSystemProcess {
      color: #ffffff;
      line-height: 1;
      display: flex;
      gap: 10px;
      width: auto;
      align-items: center;
      margin-right: 10px;
    }
  }

  .group-process {
    .search-line {
      justify-content: space-between;

      :deep(.el-button:hover) {
        background-color: #494C66;
        border-color: #494C66;
      }
    }

    .choose-core {
      display: flex;
      flex-direction: row;
      padding-right: 10px;
      align-items: center;
      gap: 10px;
      :deep(.el-button:hover) {
        background-color: #4A8FEE;
        border-color: #4A8FEE;
      }

      & > div {
        width: 136px;
        height: 30px;
      }
    }

    .group-name {
      display: flex;
      flex-direction: row;
      margin: 10px 0;
      align-items: center;
      gap: 10px;

      .confirmBtn {
        :deep(.el-button:hover) {
          background-color: #4A8FEE;
          border-color: #4A8FEE;
        }
      }

      & > div {
        height: 30px;
      }

      :deep(.el-input__inner) {
        color: #ffffff;
      }

      .remove-group {
        margin-left: auto;
        margin-right: 12px;
        color: #BF4040;
        cursor: pointer;

        .iconfont {
          font-size: 14px;
        }
      }
    }

    .title {
      display: inline-block;
      width: 85px;
      color: #666666;
      white-space: nowrap;
    }

    section {
      width: 100%;
      height: calc(100vh - 210px);
      padding-top: 10px;

      .wrap {
        width: calc(100vw - 245px);
        height: 100%;
        display: flex;
        flex-flow: column nowrap;
        font-size: 12px;
        color: #777777;
        line-height: 30px;
        position: relative;
        overflow-x: auto;
        &::-webkit-scrollbar {
          height: 5px;
        }

        &::-webkit-scrollbar-track {
          background-color: #21222a;
        }

        &::-webkit-scrollbar-thumb {
          background-color: #71738c;
          border-radius: 2px;
        }

        .scroll-box {
          height: calc(100% - 30px);
          width: calc(var(--name-w) + var(--thread-w) + var(--cpu-w) + var(--cpu-pp-w) + var(--gpu-w) + var(--gpu-pp-w) + var(--mem-w) + var(--mem-pp-w) + var(--controls-w));
          overflow-x: hidden;
          position: relative;

          .empty-bg {
            position: absolute;
            width: 100%;
            height: 100%;
            background-image: url("../assets/empty.png");
            background-repeat: no-repeat;
            background-position: center;
            background-size: 117.15px 121.87px;
            z-index: 34;
            background-color: #2B2C37;
          }

          .list-item {
            display: flex;
            flex-flow: row nowrap;
            width: 100%;
            height: 30px;
            cursor: pointer;
            border-radius: 4px;

            .iconSort {
              margin-left: 5px;
              font-size:12px;
              color: #777777;
            }
            &:hover,&.checked {
              background-color: #343647;
              color: #ffffff;
              .process-name{color: #ffffff;}
              .iconRemove {color: #BF4040;}
            }
          }

          .collapse-handler {
            padding-left: 10px;
            cursor: pointer;

            .iconfont {
              font-size: 10px;
              transition: all .3s linear;
              transform: rotate(0deg);
              display: inline-block;
            }

            .ro180 {
              transform: rotate(180deg);
            }
          }
        }

        .name, .thread, .cpu, .cpu-pp, .gpu, .gpu-pp, .mem, .mem-pp, .controls {
          display: flex;
          flex-flow: row nowrap;
          align-items: center;
          justify-content: flex-end;
          padding-right: 5px;
          gap: 10px;
        }

        .cpu-pp,.gpu-pp,.mem-pp{
          .iconfont {
            font-size: 10px;
            display: inline-block;
            transform: translateY(-5px);
          }
        }
        .thread {
          color: #777777;
          &.is-active {
            color: #35D57D;
          }
        }
        .controls {
          justify-content: space-between;
          padding-left: 5px;

          span,.iconSort {
            display: flex;
            flex-flow: row nowrap;
            align-items: center;
            gap: 5px;
            cursor: pointer;
          }
        }
        .name {
          text-align: left;
          width: var(--name-w);
          justify-content: flex-start;
          padding-left: 10px;
          padding-right: 0;

          img {
            width: 20px;
            height: 20px;
          }

          .process-name {
            width: calc(var(--name-w) - 90px);
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            color: #888888;
          }

          .point {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #35D57D;

            &.inactive {
              background-color: #666666;
            }
          }
        }

        .thread {
          width: var(--thread-w);
        }

        .cpu {
          width: var(--cpu-w);
        }

        .cpu-pp {
          width: var(--cpu-pp-w);
        }

        .gpu {
          width: var(--gpu-w);
        }

        .gpu-pp {
          width: var(--gpu-pp-w);
        }

        .mem {
          width: var(--mem-w);
        }

        .mem-pp {
          width: var(--mem-pp-w);
        }

        .controls {
          width: var(--controls-w);
        }
      }
    }
  }

  .flexible-bar {
    width: calc(100% - 232px);
    border-radius: 4px;
    overflow: hidden;
    position: absolute;
    bottom: 10px;
    left: 207px;
    z-index: 99;
    background-color: #2B2C37;
    .bar {
      height: 16px;
      cursor: pointer;
      background: linear-gradient(90deg, #585a6f00, #42445CFF 50%, #585a6f00);
      text-align: center;
      color: #6A6C8A;

      &:hover {
        background: linear-gradient(90deg, #585a6f00, #585A6FFF 50%, #585a6f00);
      }
    }
    .bar-content {
      height: 0;
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      overflow: hidden;
      transition: height 0.3s ease-in-out;
      padding-left: 10px;
    }
  }
}
.drag-preview {
  position: fixed;
  //width: 300px;
  height: 30px;
  border-radius: 4px;
  background-color: #585973;
  top: 0;
  left: 0;
  z-index: 200;
  pointer-events: none;// 防止遮挡
  transform: translate(0, 0);
  padding: 0 16px;
  line-height: 30px;
  color: #ffffff;
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  gap: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  font-size: 12px;
  img {
    width: 20px;
    height: 20px;
  }
}
</style>
<style lang="scss">
:root {
  --name-w: 320px;
  --thread-w: 65px;
  --cpu-w: 70px;
  --cpu-pp-w: 80px;
  --gpu-w: 70px;
  --gpu-pp-w: 80px;
  --mem-w: 70px;
  --mem-pp-w: 80px;
  --controls-w: 200px;


  --all-name-w: 290px;
  --all-thread-w: 65px;
  --all-cpu-w: 65px;
  --all-cpu-pp-w: 80px;
  --all-gpu-w: 65px;
  --all-gpu-pp-w: 80px;
  --all-mem-w: 65px;
  --all-mem-pp-w: 80px;
  --all-group-w: 110px;
  --all-controls-w: 130px;
}

.pp-tooltip {
  color: #ffffff;
}
</style>
