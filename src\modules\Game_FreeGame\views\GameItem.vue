<script setup lang="ts">
import {onMounted , reactive ,ref ,computed ,defineProps,onUnmounted,defineEmits} from "vue";

const countdown = ref('00:00:00');
let timer:any

const props = defineProps({
    GameInfo: {
        type: Object,
        required: true
    }
})
// @ts-ignore
const gamepp = window.gamepp as any;
onMounted (async() =>
{
  //喜加一
   await INIT()
  //  console.warn('props.GameInfo::',props.GameInfo);
  timer = setInterval(updateCountdown, 1000);

})

onUnmounted(() => {
  clearInterval(timer);
});

const  updateCountdown = () =>
{
  const now:any = new Date();
  const timeDifference:any = new Date(props.GameInfo.expired_date) - now;

  if (timeDifference > 0) {
    const days = Math.floor(timeDifference / (1000 * 60 * 60 * 24));
    const hours = Math.floor((timeDifference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((timeDifference % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((timeDifference % (1000 * 60)) / 1000);

    countdown.value = `${days}:${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  } else {
    countdown.value = '00:00:00';
  }
}

const GPP_OpenURL = (url:string) =>
{

  try {
    gamepp.shell.openExternal(url);
  } catch (error) {
    window.open(url)
  }
}

const INIT = async() =>
{
  //初始化账号信息
}
</script>

<template>
  <div class="GameItem">
    <div class="leftInfo">
       <img :src="props.GameInfo.cover">
       <div class="GameInformation">
        <p class="Game_title" style="font-size: 18px;"> {{ props.GameInfo.game_name }}</p>
        <p style="display: flex;justify-content: space-between;width: 100%;"><span>{{ $t('GamePlusOne.gamePlatform') }}</span><span>{{ (props.GameInfo.platform).toUpperCase()}}</span></p>
        <p style="color:#4791F6;cursor: pointer;width: 100px;" @click.stop="GPP_OpenURL(props.GameInfo.get_url)">{{ $t('GamePlusOne.goStorePage') }}</p>
        <div style="display: flex;align-items: center;"><div class="countDown"><span>{{countdown}}</span></div>{{ $t('GamePlusOne.receiveEnd') }}</div>
        <div class="saveMoney"><span>{{props.GameInfo.original_price}}</span><div class="oneHundredpercent">-100%</div></div>
       </div>
    </div>
    <div class="horitZaline">

    </div>
    <div class="right">
      <p>{{ $t('GamePlusOne.loginPlatformAccount') }}</p>
      <div class="accountInfo" v-show="props.GameInfo.accountList.length>0" v-for="(item,index) in props.GameInfo.accountList">
          <span>{{ item.platform_name}}</span>
          <div v-show="item.receive == 0" style="color: #3E7FE1;">{{ $t('GamePlusOne.waitReceive') }}</div>
          <div v-show="item.receive == 200" style="color:#35D57D;">{{ $t('GamePlusOne.receiveSuccess') }}</div>
          <div v-show="item.receive == 1" style="color:#F14343;">{{ $t('GamePlusOne.accountInvalid') }}</div>
          <div v-show="item.receive == 10086" style="color:#F14343;"><div class="rotating"><span class="iconfont icon-holding"></span></div></div>
          <div v-show="item.receive == 3" style="color:#35D57D;">{{ $t('GamePlusOne.alreadyOwn') }}</div>
          <div v-show="item.receive == 4" style="color:#F14343;">{{ $t('GamePlusOne.networkError') }}</div>
          <div v-show="item.receive == 9" style="color:#F14343;">{{ $t('GamePlusOne.noGame') }}</div>
          <div v-show="item.receive == 10" style="color:#F14343;">{{ $t('GamePlusOne.manualReceiveInterrupt') }}</div>
          <div v-show="item.receive == 7" style="color:#F14343;">{{ $t('GamePlusOne.receiving') }}</div>
          <div v-show="item.receive == 8" style="color:#F14343;">{{  '网络异常' }}</div>
          <div v-show="item.receive == -1" style="color:#F14343;">{{ '网络异常' }}</div>
      </div>
       <div class="emptyAccount" v-show="props.GameInfo.accountList.length == 0">
          <span>暂未绑定账号</span>
      </div>
    </div>

  </div>


</template>
<style lang="scss">
 .GameItem{
    display: flex;
    width: 912px;
    height: 212px;
    background-color: #666666;
    margin:10px 0 0 10px;
    border: 1px solid transparent;
    .Game_title{
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .leftInfo{
        display: flex;
        margin-top: 15px;
        img{
            width: 290px;
            height: 162px;
            margin-left: 10px;
            border-radius: 4px;
        }
        .GameInformation{
          width: 240px;
          margin:0 0 0 12px;
          display: flex;
          flex-direction: column;
          color: white;
          p{
            margin-bottom: 10px;
          }
        }
        .countDown{
          padding:0 10px;
          height: 30px;
          background-color:#1A1B24;
          color: #FFFFFF;
          font-size: 14px;
          border-radius: 6px;
          margin-right:9px;
          span{
            color: #F14343;
            font-size: 20px;
            font-family: 'Quartz',sans-serif;
            line-height: 30px;
            letter-spacing: 3px;
          }
        }
        .saveMoney{
          display: flex;
          align-items: center;
          span{
            font-size: 24px;
            font-weight: 700;
            color: #999999;
            text-decoration: line-through;
          }
          .oneHundredpercent
          {
            width:45px;
            height: 20px;
            background-color:#35D57D;
            color: white;
            border-radius: 2px;
            line-height: 20px;
            text-align: center;
            margin-left: 10px;
          }
        }
    }
    .horitZaline{
         margin-left: 15px;
         margin-top: 20px;
         width: 2px;
         height: 157px;
         background: linear-gradient(180deg, rgba(51, 106, 181, 0) 0%, #336AB5 47%, rgba(51, 106, 181, 0) 100%);
    }
    .right{
      display: flex;
      flex-direction: column;
      margin-left:20px;
      margin-top: 20px;
      width:300px;
      color: #FFFFFF;
      .accountInfo{
        width: 100%;
        display: flex;
        justify-content: space-between;
        color: #999999;
        margin-top: 10px;
      }
      .emptyAccount{
        width: 100%;
        height: 100px;
        color: #666666;
        font-weight: 700;
        font-size: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
 }

  //动画
  .rotating
  {
    animation: rotate4 2s linear infinite;
    color: #336AB5;
  }

  @keyframes rotate4
  {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
</style>
