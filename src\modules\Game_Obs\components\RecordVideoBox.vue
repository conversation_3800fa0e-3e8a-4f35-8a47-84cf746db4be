<script setup lang="ts">
import {onMounted, ref} from "vue";
import {CaretRight} from "@element-plus/icons-vue";
import PlayVideoDialog from "@/modules/Game_Obs/components/PlayVideoDialog.vue";
const localFileVideos = ref<Array<{ path: string; info: { img: string; time: number } | null }>>([]);
const playVideoDialogVisible = ref(false);
const playVideoDialogUrl = ref('');
const gamepp = (window as any).gamepp;

onMounted(() => {
    getLocalFileVideos();
    console.log('localFileVideos:', localFileVideos.value);
});

async function getLocalFileVideos() {
    const str = await gamepp.setting.getString.promise(6); // 视频目录
    const result = await gamepp.utils.getFileList.promise(str);  // 获取该目录下的视频列表

    if (result && Array.isArray(result)) {
        // 筛选出视频
        let arr = result.filter(item => {
            return ['mp4', 'avi', 'rmvb', 'rm', 'wmv', 'mkv', 'flv', 'mov', 'mpg', 'mpeg', 'm4v'].includes(item.split('.').pop()!);
        });
        // 初始化视频列表
        localFileVideos.value.push(...arr.map(item => ({
            name: '', // 暂时还不需要视频名字
            path: item,
            info: null, // 初始没有时长和封面
        })));

        // 分批次加载封面（每批5个）
        const BATCH_SIZE = 5;
        for (let i = 0; i < localFileVideos.value.length; i += BATCH_SIZE) {
            const batch = localFileVideos.value.slice(i, i + BATCH_SIZE);

            // 等待当前批次全部加载完成
            await Promise.all(batch.map(async (item, batchIndex) => {
                try {
                    localFileVideos.value[i + batchIndex].info = await getVideoBase64AndPlayTime(item.path);
                } catch (error) {
                    console.error('加载封面失败:', error);
                    localFileVideos.value[i + batchIndex].info = null;
                }
            }));

            // 每批处理完后让出主线程
            await new Promise(resolve => requestAnimationFrame(resolve));
        }
    }
}

// 根据视频路径通过video标签和canvas获取视频封面和时长
function getVideoBase64AndPlayTime(url: string) : Promise<{ img: string; time: number } | null> {
    return new Promise((resolve, reject) => {
        const video = document.createElement('video');
        const canvas = document.createElement('canvas');

        video.setAttribute('crossOrigin', 'anonymous');
        video.setAttribute('preload', 'auto');
        video.currentTime = 0.1; // 设置获取第一帧的时间点

        const cleanup = () => {
            video.remove();
            canvas.remove();
            resolve(null);
        };

        video.addEventListener('loadeddata', async () => {
            try {
                // 等待视频元数据加载
                await new Promise((res, rej) => {
                    if (video.readyState >= 2) res(true);
                    video.addEventListener('error', rej);
                });

                // 绘制封面
                canvas.width = 240;
                canvas.height = 135;
                const ctx = canvas.getContext('2d')!;
                ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

                resolve({
                    img: canvas.toDataURL('image/jpeg', 0.8),
                    time: Math.floor(video.duration)
                });
            } catch (error) {
                resolve(null);
            } finally {
                cleanup();
            }
        });

        video.addEventListener('error', error => {
            resolve(null);
            cleanup();
        });

        video.src = url;
    });
}
</script>

<template>
    <div class="record-video-box-wrapper">
        <div class="record-video-box">
            <span class="record-video-box-title">{{$t('video.recordVideo')}}</span>
            <section>
                <div
                    v-for="(video,index) in localFileVideos"
                    @click="playVideoDialogVisible = true; playVideoDialogUrl = video.path"
                    :key="index"
                    class="record-video-box-item"
                >
                    <div v-show="video.info === null" class="loading-mask"></div>
                    <img v-show="video.info !== null" :src="video.info?.img" alt="">
                    <!--<span>{{ video.info?.time }}s</span>-->
                    <div class="play-icon">
                        <el-icon><CaretRight /></el-icon>
                    </div>
                </div>
            </section>

            <PlayVideoDialog :visible="playVideoDialogVisible" :videoUrl="playVideoDialogUrl" @close="playVideoDialogVisible = false"/>
        </div>
    </div>
</template>

<style scoped lang="scss">
.record-video-box-wrapper {
    position: absolute;
    top: 500px;
    left: 17px;
    width: 1040px;
    min-height: 185px;
    &::after {
        content: '';
        display: block;
        height: 20px; /* 创建额外滚动空间 */
    }
}
.record-video-box {
    border-radius: 4px;
    background: #2B2C37;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
    padding: 10px;
    font-size: 12px;
    min-height: 185px;

    &-title {
        color: #ffffff;
    }

    section {
        margin-top: 20px;
        display: grid;
        grid-template-columns: repeat(4,25%);
        justify-items: center;
        grid-row-gap: 10px;

        .record-video-box-item {
            width: 240px;
            height: 135px;
            border-radius: 4px;
            border: 1px solid transparent;
            overflow: hidden;
            position: relative;
            cursor: pointer;

            .loading-mask {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.3);
                z-index: 1;
            }

            img {
                width: 238px;
                height: 133px;
            }

            &:hover {
                border: 1px solid #409EFF;
                .play-icon {
                    display: flex;
                }
            }

            .play-icon {
                display: none;
                width: 30px;
                height: 30px;
                position: absolute;
                top: 50%;
                left: 50%;
                background-color: #409EFF;
                transform: translate(-50%, -50%);
                font-size: 20px;
                color: #ffffff;
                justify-content: center;
                align-items: center;
                border-radius: 50%;
            }
        }
    }
}
</style>
