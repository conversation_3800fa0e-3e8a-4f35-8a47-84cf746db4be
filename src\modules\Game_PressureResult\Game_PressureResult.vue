<script setup lang="ts">
import RightTopIcons from "@/components/mainCom/RightTopIcons.vue";
import {computed, defineProps, inject, onMounted, reactive, ref} from 'vue'
import * as echarts from "echarts";
import { useI18n } from 'vue-i18n';
import { gamepp } from 'gamepp'
import { AVGNum,MaxNum,MinNum,FormatSeconds,FormatTimeCn,FormatTime,
         FormatTimePlus, DiskCapacityConversion,FormartMonthToNumber,RemoveAllSpace,RegExGetNum,gpu_brand,
         showNumberOfCPUCores,showNumberOfLogicalCpus,findMostFrequent} from '../../uitls/GameppTools'
import { ElMessage } from "element-plus";
// import ppAgent  from "./components/ppAgent.vue";
import { isArray } from "lodash-es";

let phaseList:any = ref({
  0:{data:[],},
  1:{data:[]},
  2:{data:[]},
  3:{data:[]},
  4:{data:[],mem_temp:{'#1':0,'#2':0,'#3':0,'#4':0}},
})

let CPUTYPE:any = ref('intel')

let pass:any = ref(null)

let counts:any = ref(null)
let keyName:any = ref([])
let bg_sensor_data:any = {
  cpu:{
    name:''
  },
  gpu:{
    name:''
  },
  memory:{
    channel:'',
    clock:0,
    size:0,
    tcas:0,
  }
}

let Starttime = 0

let cpu_chart:any = null

let xlength:any = ref(['',''])

let cpu_options:any = ref({series:null})

let series:any = ref(null)

//测试详情select
let chooseModel = ref(4)


let DatabaseId:any
let realdata:any = ref({
            starttime:0,
            endtime:0,
            bm_version:0,
            model:[{choosen:false,tested:false},{choosen:false,tested:false},{choosen:false,tested:false},{choosen:false,tested:false},],
            hard:{
                  cpu_name:'',
                  cpu_core:'',
                  cpu_theads:'',
                  cpu_baseFre:'',
                  gpu_name:'',
                  gpu_memory:0,
                  gpu_drive:'',
                  mem_name:'',
                  mem_channel:0,
                  mem_clock:0,
                  mem_time:0,

            },
            cpu:{
                  avg_clock:0,
                  max_temp:0,
                  max_tdp:0,
                  limit:0
            },
            gpu:{
                  avg_clock:0,
                  max_temp:0,
                  max_tdp:0,
                  limit:0
            },
            mem:{
                  mem_error:0
            }
      })

const hasPECORE = ref(true)

const isDDR5 = ref(false)

let DDR5_key:any = ref([])

const init = async() =>
{
    let AppDataDir = await gamepp.getAppDataDir.promise();
    DatabaseId = await gamepp.database.open.promise(AppDataDir + '\\common\\Data\\GamePPStressNew.dll');
    bg_sensor_data = JSON.parse(localStorage.getItem('bg_sensor_data') as any)
  
 choosenList.value =[
    ['处理器温度','cputemp','°C'],['处理器频率','cpuclock','MHZ'],['处理器TDP','cpuTDP','W'],
    ['处理器占用','cpuload','%'],['显卡频率','gpuclock','MHZ'],['显卡温度','gputemp','°C'],['显卡占用','gpuload','%'],
    ['显存温度','gpuvrantemp','%'],
    ]

    //判断大小核心
  if(bg_sensor_data['cpu']['usageP'] == 0 && bg_sensor_data['cpu']['usageE'] == 0)
  {
    hasPECORE.value = false
    choosenList.value =[
    ['处理器温度','cputemp','°C'],['处理器频率','cpuclock','MHZ'],['处理器TDP','cpuTDP','W'],
    ['处理器占用','cpuload','%'],['显卡频率','gpuclock','MHZ'],['显卡温度','gputemp','°C'],['显卡占用','gpuload','%'],
    ['显存温度','gpuvrantemp','°C'],
    ]
  }
   if(bg_sensor_data['cpu']['usageP'] == null && bg_sensor_data['cpu']['usageE'] == null)
  {
     hasPECORE.value = false
     choosenList.value =[
    ['处理器温度','cputemp','°C'],['处理器频率','cpuclock','MHZ'],['处理器TDP','cpuTDP','W'],
    ['处理器占用','cpuload','%'],['显卡频率','gpuclock','MHZ'],['显卡温度','gputemp','°C'],['显卡占用','gpuload','%'],
    ['显存温度','gpuvrantemp','°C'],
    ]
  }

  if(bg_sensor_data['memory']['ddr5_temp']['#1'] == null && bg_sensor_data['memory']['ddr5_temp']['#2'] == null)
  {
    isDDR5.value = false
  }
  else
  {
    isDDR5.value = true
  }

  DDR5_key.value = Object.keys(bg_sensor_data['memory']['ddr5_temp']).filter(key => bg_sensor_data['memory']['ddr5_temp'][key] !== null);

  console.warn('DDR5有效的键',DDR5_key.value);
  

  
  if(chooseModel.value == -1)
  {
    chooseModel.value = 0
  }
}

function getRandomColor() {
    // 生成一个0到16777215之间的随机数
    let color = Math.floor(Math.random() * 16777215).toString(16);
    // 确保颜色值长度为6位，不足6位时前面补0
    while (color.length < 6) {
        color = '0' + color;
    }
    return '#' + color;
}

const addSereis = async() =>
{
   let i:any = null
   let nameArr = []
   for(const item of chartsList.value)
   {
     nameArr.unshift(item.name)
   }
    let switchKey = true
    choosenList.value.forEach((key:any,index:number) =>
    {
      let name = key[0]
      if(!nameArr.includes(name)&& switchKey)
      {
        i =  index
        switchKey = false
        return
      }
    })

    console.log('index',i);
    let index =  chooseModel.value
    let data = phaseList.value[index].data
    let key = choosenList.value[i][1]
    let InfoData = []

     for(const item of data)
     {
       InfoData.push(item[key])
     }
     //init chartsList
     if(chartsList.value.length>4){
      ElMessage({
               message: '添加已达上限',
               type: 'warning',
               grouping:true
      })
      return
     }
     
     let color = getRandomColor()

     chartsList.value.push(
      {
        name:`${choosenList.value[i][0]}_${choosenList.value[i][2]}`,
        key:choosenList.value[i][1],
        data:InfoData,
        color:color,
        active:true
      }
     )

     console.log('chartsList.value::',chartsList.value);
     
     cpu_options.value.series.push({
            data: InfoData,
            type: 'line',
            lineStyle: {
                color:color
            },
            symbol: 'none',
            name:`${choosenList.value[i][0]}_${choosenList.value[i][2]}`,
            animation: false,
     })
     console.log('cpu_options.value::',cpu_options.value);

     cpu_chart.setOption(cpu_options.value);
}

const insertSeries = async() =>
{
   let index =  chooseModel.value
   let data = phaseList.value[index].data
   console.log('insertData::','color:red',data);
   
   let color = ['rgba(255, 144, 144, 1)','rgba(253, 139, 67, 1)','rgba(255, 115, 0, 1)',]
   for(let i = 0;i<3;i++)
   {
     let key = choosenList.value[i][1]
     let InfoData = []

     for(const item of data)
     {
       InfoData.push(item[key])
     }

     //init chartsList
     chartsList.value.push(
      {
        name:`${choosenList.value[i][0]}_${choosenList.value[i][2]}`,
        key:choosenList.value[i][1],
        data:InfoData,
        color:color[i],
        active:true
      }
     )

     cpu_options.value.series.push({
                data: InfoData,
                type: 'line',
                lineStyle: {
                    color:color[i]
                },
                symbol: 'none',
                name:`${choosenList.value[i][0]}_${choosenList.value[i][2]}`,
                animation: false,
     })
   }

   cpu_chart.setOption(cpu_options.value);
   console.log('%ccpu_chart:','color:red',cpu_options.value);
}

onMounted(async() =>
{
    await init()
    console.warn('after init');
    gamepp.webapp.onInternalAppEvent.addEventListener(async data =>
    {
      console.warn('data',data);
      const list:any  = await gamepp.database.query.promise(DatabaseId, "list", "*", 'starttime="' + data.StartTime + '"');
      console.warn('%c数据库读取::','color:green',list);
      Starttime = data.StartTime
      await handleListData(list)
      //各阶段数据分开
      const DetailedData = await gamepp.database.query.promise(DatabaseId, "'" + data.StartTime + "'", "*");
      console.log('%cDetailedData::','color:orange',DetailedData);
      await splitModelList(DetailedData)
    })
    await gamepp.setting.setBool2.promise('window', 'pressureResult', true);

})
let TabList = ref([
{
    name:'测试概览',
    choosen:true
},
{
    name:"测试详情",
    choosen:false
},
// {
//     name:"加加AI特工(Agent)",
//     choosen:false
// }
])

let chartsList:any = ref([
  // {
  //   name:'CPU温度',
  //   data:[],
  //   color:'rgba(255, 144, 144, 1)',
  //   active:true
  // },
  // {
  //   name:'CPU频率',
  //   data:[],
  //   color:'rgba(253, 139, 67, 1)',
  //   active:true
  // },
  // {
  //   name:'CPU占用',
  //   data:[],
  //   color:'rgba(255, 115, 0, 1)',
  //   active:true
  // }
])


const deleteLine = (index:number) =>
{
   if(chartsList.value.length <= 1)
   {
    ElMessage({
               message: '无法继续删除',
               type: 'warning',
               grouping:true
      })
   }
   else
   {

    chartsList.value = chartsList.value.filter((xitem:any,xindex:any) => {
      return xindex != index
    })
    
    console.warn('chartsList.value',chartsList.value);
    
    cpu_options.value.series = []

    for(const item of chartsList.value)
    {
      cpu_options.value.series.push({
                  data: item.data,
                  type: 'line',
                  lineStyle: {
                      color:item.color
                  },
                  symbol: 'none',
                  name:item.name,
                  animation: false,
      })
    }

    console.warn('cpu_options.value',cpu_options.value);
    cpu_chart.setOption(cpu_options.value,true);

   }
}

let choosenList:any = ref(null)

const changeOption = (option:any) =>
{
 if(option == 0)return
  //chartsList 
  let index = option[1]

  let oindex = option[0]
  let InfoData = []
  let data = phaseList.value[chooseModel.value].data
  console.log('choosenList::',choosenList.value);
  let key = choosenList.value[oindex][1]
  console.log('key::',key);
  console.log('data::',data);
  {
    
    for(const item of data)
     {
       InfoData.push(item[key])
     } 
  }
  console.warn('InfoData::',InfoData);
  
  chartsList.value[index].data = InfoData
  chartsList.value[index].name = `${choosenList.value[oindex][0]}_${choosenList.value[oindex][2]}`
  chartsList.value[index].key  = key

  cpu_options.value.series = []

  for(const item of chartsList.value)
  {
    cpu_options.value.series.push({
                data: item.data,
                type: 'line',
                lineStyle: {
                    color:item.color
                },
                symbol: 'none',
                name:item.name,
                animation: false,
     })
  }
  console.log('%c charts change','color:red',chartsList.value,cpu_options.value.series);
  
  console.log('%c option change','color:green',option);

  cpu_chart.setOption(cpu_options.value);
}

const selectModel = () =>
{
  xlength.value = []
  console.warn('e',chooseModel.value);
  for(const item of phaseList.value[chooseModel.value].data)
  {
    xlength.value.push(' ')
  }

  console.warn('Xlength::',xlength.value);
  console.warn('chartsList.value::',chartsList.value);
  let data = phaseList.value[chooseModel.value].data
  cpu_options.value.series = []

  for(const item of chartsList.value)
  {
    let InfoData = []
    for(const xitem of data)
     {
       InfoData.push(xitem[item.key])
     } 
     item.data = InfoData
  }

  for(const item of chartsList.value)
  {
    cpu_options.value.series.push({
                data: item.data,
                type: 'line',
                lineStyle: {
                    color:item.color
                },
                symbol: 'none',
                name:item.name,
                animation: false,
     })
  }
   
  console.warn('cpu_options.value::',cpu_options.value);
  

  cpu_chart.setOption(cpu_options.value);
}

let mem_error:any = ref(null)
  

const handleListData = async(item:any) =>
{

      let list_data = JSON.parse(decodeURIComponent(item[0].cpu_list_data));
      if(list_data['Pass'])
      {
          pass.value = list_data['Pass']
      }
      mem_error.value = list_data['mem_data']
      let hard = await PM_GetHardwareBase(item[0].hd_info)
      console.warn('hard::',hard);
      console.warn('list_data::',list_data);

      let obj = {
            starttime:FormatTimePlus(item[0].starttime/1000),
            endtime:FormatSeconds((item[0].endtime -item[0].starttime)/1000),
            bm_version:item[0].bm_version,
            model:list_data['model'],
            hard:{
                  cpu_name:hard['ProcessorName'],
                  cpu_core:hard['CPUCores']||hard['NumberofCPUCores'],
                  cpu_threads:hard['LogicalCPUs']||hard['NumberofLogicalCPUs'],
                  cpu_baseFre:hard['OriginalProcessorFrequency'],
                  gpu_name:hard['GPU'],
                  gpu_memory:hard['GPU_VideoMemor'],
                  gpu_drive:hard['GPU_DriverVersion'],
                  mem_name:hard['memory_list1'][0],
                  mem_channel:hard['memory_list2'][0],
                  mem_clock:hard['Memory_Clock'],
                  mem_time:hard['Memory_CurrentTiming'],
                  mem_size:hard['Memory_size'],

            },
            cpu:{
                  avg_clock:calculateAverage(list_data['cpu_clock']).toFixed(0),
                  max_temp:Math.max(...list_data['cpu_TEMP']),
                  max_tdp:Math.max(...list_data['cpu_TDP']),
                  limit:list_data['cpu_LIMIT'].length
            },
            gpu:{
                  avg_clock:calculateAverage(list_data['gpu_clock']).toFixed(0),
                  max_temp:Math.max(...list_data['gpu_TEMP']),
                  max_tdp:Math.max(...list_data['gpu_TDP']),
                  limit:0
            },
            mem:{
                  mem_error:list_data['mem_error']
            }
      }

      realdata.value = obj
      console.log('%crealData::','color:green',realdata.value);

    chooseModel.value = realdata.value['model'].findIndex((item:any)=>{
      return item.tested == true
    })
    if(chooseModel.value == -1)
    {
      chooseModel.value = 0
    }
    console.warn('DefaultChooseModel::','color:pink',chooseModel.value);
}

const splitModelList = async (list_data:any) =>
{
  console.warn('splitBefore:::');
  
  let count  = JSON.parse(JSON.stringify(chooseModel.value))
  console.warn('splitModeList',count);
  
  phaseList.value = {
      0:{data:[],},
      1:{data:[]},
      2:{data:[]},
      3:{data:[]},
      4:{data:[],mem_temp:{'#1':0,'#2':0,'#3':0,'#4':0}},
}
   for(const item of list_data)
   {
        phaseList.value[item.model].data.push(item)

        phaseList.value[4].data.push(item)
   }

   
   console.log('%cphaseList','color:green',phaseList.value);

   for(const key in phaseList.value)//0,1,2,3
   {
     if(phaseList.value[key].data.length>0)
     {
       console.log('%c ------------------1','color:red');

      let list = phaseList.value[key].data
      //cpu
      let tempArr = []
      let tdpArr  = []
      let clockArr = []
      let cpufanArr = []
      let cpuvolArr = []
      let cpuclockEArr = []
      let cpuclockPArr = []
      //gpu
      let gpu_tempArr = []
      let gpu_memTemp = []
      let gpu_D3D = []
      let gpu_hot = []
      let gpu_tdp = []
      let gpu_clock = []
      let gpu_load = []
      let gpu_fan = []
      //memory
      let limitARR  = []
      let memArr:any = {'#1':[],'#2':[],'#3':[],'#4':[],'#5':[],'#6':[]}
      let ppt:any = []
      let edc:any = []
      let thermal:any = []
      let amd_limit:any = []
      for(const item of list)
      {
        tempArr.push(item.cputemp)
        tdpArr.push(item.cpuTDP)
        clockArr.push(item.cpuclock)
        cpufanArr.push(item.cpufan)
        cpuvolArr.push(item.cpuvoltage)
        cpuclockEArr.push(item.cpuclockE)
        cpuclockPArr.push(item.cpuclockP)

        gpu_tempArr.push(item.gputemp)
        gpu_memTemp.push(item.gpuvramtemp)
        gpu_D3D.push(item.gpuD3D)
        gpu_hot.push(item.gpuhot)
        gpu_tdp.push(item.gpuTDP)
        gpu_clock.push(item.gpuclock)
        gpu_load.push(item.gpuload)
        gpu_fan.push(item.gpufan)

        // encodeURIComponent(JSON.stringify(HwPTinfo['memory']['ddr5']))
        let curent:any  = JSON.parse(decodeURIComponent(item.memTemp));
        //
        console.log('%cAMD限制::','color:red',item.AMD_LIMIT);
        
        amd_limit  = JSON.parse(decodeURIComponent(item.AMD_LIMIT));
        console.log('%cAMD限制解码::','color:green',amd_limit);
        
        if(amd_limit['cputype'] == 'amd')
        {
          
          CPUTYPE.value = 'amd'
          let ppt:any = []
          let edc:any = []
          let thermal:any = []
          ppt.push(amd_limit[0])
          edc.push(amd_limit[1])
          thermal.push(amd_limit[2])
          console.warn('%cpush_AMD::','color:red');
        }
        try
        {
          for(const Val of DDR5_key.value)
          {
             
            if(!isArray(memArr[Val]))
            {
              memArr[Val] = []
            }
            
            memArr[Val].push(curent[Val])
          }
          
        }catch
        {
           console.error('memError');
        }
       
        
        // memClock.push()
        if(item.performance != '')
        {
          limitARR.push(...JSON.parse(item.performance))
        }

      }

      console.warn('ppt',ppt,'edc',edc,'thermal',thermal);
      console.warn('memArr',memArr);

      if(amd_limit['cputype'] == 'amd')
      {
        console.warn('%cpush_AMD::','color:red');
        
        phaseList.value[key].amd_maxppt = MaxNum(ppt),
        phaseList.value[key].amd_minppt = MinNum(ppt),
        phaseList.value[key].amd_avgppt = AVGNum(ppt).toFixed(0)

        phaseList.value[key].amd_maxedc = MaxNum(edc),
        phaseList.value[key].amd_minedc = MinNum(edc),
        phaseList.value[key].amd_avgedc = AVGNum(edc).toFixed(0)

        phaseList.value[key].amd_maxthermal = MaxNum(thermal),
        phaseList.value[key].amd_minthermal = MinNum(thermal),
        phaseList.value[key].amd_avgthermal = AVGNum(thermal).toFixed(0)
      }

      console.warn('DDR5内存温度::',memArr);

      console.warn('list::',list);
      phaseList.value[4].mem_temp = memArr

      phaseList.value[key].cpu_maxtemp = MaxNum(tempArr),
      phaseList.value[key].cpu_mintemp = MinNum(tempArr),
      phaseList.value[key].cpu_avgtemp = AVGNum(tempArr).toFixed(0)

      phaseList.value[key].cpu_maxtdp = MaxNum(tdpArr),
      phaseList.value[key].cpu_mintdp = MinNum(tdpArr),
      phaseList.value[key].cpu_avgtdp = AVGNum(tdpArr).toFixed(0)

      phaseList.value[key].cpu_maxclock = MaxNum(clockArr),
      phaseList.value[key].cpu_minclock = MinNum(clockArr),
      phaseList.value[key].cpu_avgclock = AVGNum(clockArr).toFixed(0)

      phaseList.value[key].cpu_maxfan = MaxNum(cpufanArr),
      phaseList.value[key].cpu_minfan = MinNum(cpufanArr),
      phaseList.value[key].cpu_avgfan = AVGNum(cpufanArr).toFixed(0)

      phaseList.value[key].cpu_maxvol = MaxNum(cpuvolArr),
      phaseList.value[key].cpu_minvol = MinNum(cpuvolArr),
      phaseList.value[key].cpu_avgvol = AVGNum(cpuvolArr).toFixed(0)
      
       console.log('%c ------------------1','color:red');

       try{
        if(hasPECORE.value)
            {
              phaseList.value[key].cpu_maxE = MaxNum(cpuclockEArr),
              phaseList.value[key].cpu_minE = MinNum(cpuclockEArr),
              phaseList.value[key].cpu_avgE = AVGNum(cpuclockEArr).toFixed(0)

              phaseList.value[key].cpu_maxP = MaxNum(cpuclockPArr),
              phaseList.value[key].cpu_minP = MinNum(cpuclockPArr),
              phaseList.value[key].cpu_avgP = AVGNum(cpuclockPArr).toFixed(0)
             }
       }catch{
        console.log('error::PE');
        
       }
    
       console.log('%c ------------------2','color:red');
  

      phaseList.value[key].gpu_maxtemp = MaxNum(gpu_tempArr),
      phaseList.value[key].gpu_mintemp = MinNum(gpu_tempArr),
      phaseList.value[key].gpu_avgtemp = AVGNum(gpu_tempArr).toFixed(0)

      phaseList.value[key].gpu_maxmemTemp = MaxNum(gpu_memTemp),
      phaseList.value[key].gpu_minmemTemp = MinNum(gpu_memTemp),
      phaseList.value[key].gpu_avgmemTemp = AVGNum(gpu_memTemp).toFixed(0)

      phaseList.value[key].gpu_maxD3D = MaxNum(gpu_D3D),
      phaseList.value[key].gpu_minD3D = MinNum(gpu_D3D),
      phaseList.value[key].gpu_avgD3D = AVGNum(gpu_D3D).toFixed(0)

      phaseList.value[key].gpu_maxhot = MaxNum(gpu_hot),
      phaseList.value[key].gpu_minhot = MinNum(gpu_hot),
      phaseList.value[key].gpu_avghot = AVGNum(gpu_hot).toFixed(0)

      phaseList.value[key].gpu_maxtdp = MaxNum(gpu_tdp),
      phaseList.value[key].gpu_mintdp = MinNum(gpu_tdp),
      phaseList.value[key].gpu_avgtdp = AVGNum(gpu_tdp).toFixed(0)

      phaseList.value[key].gpu_maxclock = MaxNum(gpu_clock),
      phaseList.value[key].gpu_minclock = MinNum(gpu_clock),
      phaseList.value[key].gpu_avgclock = AVGNum(gpu_clock).toFixed(0)

      phaseList.value[key].gpu_maxload = MaxNum(gpu_load),
      phaseList.value[key].gpu_minload = MinNum(gpu_load),
      phaseList.value[key].gpu_avgload = AVGNum(gpu_load).toFixed(0)


      phaseList.value[key].gpu_maxfan = MaxNum(gpu_fan),
      phaseList.value[key].gpu_minfan = MinNum(gpu_fan),
      phaseList.value[key].gpu_avgfan = AVGNum(gpu_fan).toFixed(0)
       console.log('%c ------------------3','color:red');

      if(limitARR.length>0)
      {
       phaseList.value[key].limit = Object.keys(findMostFrequent(limitARR))[0]
      }
      phaseList.value[key].limitcount = limitARR.length
      phaseList.value[key].limitArr = limitARR

     }
   }
    console.log('%c ------------------4','color:red');
    
   //CPU_TEMP_LIST
   if(realdata.value['model'][0].tested)
   {
    CPU_TEMP_LIST.value[0].max = phaseList.value[0].cpu_maxtemp
    CPU_TEMP_LIST.value[0].min = phaseList.value[0].cpu_mintemp
    CPU_TEMP_LIST.value[0].avg = phaseList.value[0].cpu_avgtemp

    CPU_TEMP_LIST.value[1].max = phaseList.value[0].cpu_maxtdp
    CPU_TEMP_LIST.value[1].min = phaseList.value[0].cpu_mintdp
    CPU_TEMP_LIST.value[1].avg = phaseList.value[0].cpu_avgtdp

    CPU_TEMP_LIST.value[2].max = phaseList.value[0].cpu_maxclock
    CPU_TEMP_LIST.value[2].min = phaseList.value[0].cpu_minclock
    CPU_TEMP_LIST.value[2].avg = phaseList.value[0].cpu_avgclock

    console.log('%c handle CPU_TEMP_LIST::','color:red',CPU_TEMP_LIST.value);

   }

    console.log('%c ------------------5','color:red');


   if(realdata.value['model'][1].tested)
   {
    CPU_STABLE_LIST.value[0].max = phaseList.value[1].cpu_maxtemp
    CPU_STABLE_LIST.value[0].min = phaseList.value[1].cpu_mintemp
    CPU_STABLE_LIST.value[0].avg = phaseList.value[1].cpu_avgtemp

    CPU_STABLE_LIST.value[1].max = phaseList.value[1].cpu_maxtdp
    CPU_STABLE_LIST.value[1].min = phaseList.value[1].cpu_mintdp
    CPU_STABLE_LIST.value[1].avg = phaseList.value[1].cpu_avgtdp

    CPU_STABLE_LIST.value[2].max = phaseList.value[1].cpu_maxclock
    CPU_STABLE_LIST.value[2].min = phaseList.value[1].cpu_minclock
    CPU_STABLE_LIST.value[2].avg = phaseList.value[1].cpu_avgclock

    console.log('%c handle CPU_STABLE_LIST::','color:red',CPU_STABLE_LIST.value);
   }

   if(realdata.value['model'][2].tested)
   {
    MEM_STABLE_LIST.value[0].max = phaseList.value[2].cpu_maxtemp
    MEM_STABLE_LIST.value[0].min = phaseList.value[2].cpu_mintemp
    MEM_STABLE_LIST.value[0].avg = phaseList.value[2].cpu_avgtemp

    MEM_STABLE_LIST.value[1].max = phaseList.value[2].cpu_maxtdp
    MEM_STABLE_LIST.value[1].min = phaseList.value[2].cpu_mintdp
    MEM_STABLE_LIST.value[1].avg = phaseList.value[2].cpu_avgtdp

    MEM_STABLE_LIST.value[2].max = phaseList.value[2].cpu_maxclock
    MEM_STABLE_LIST.value[2].min = phaseList.value[2].cpu_minclock
    MEM_STABLE_LIST.value[2].avg = phaseList.value[2].cpu_avgclock

    // MEM_STABLE_LIST.value[3].max = phaseList.value[2].cpu_maxclock
    // MEM_STABLE_LIST.value[3].min = phaseList.value[2].cpu_maxclock
    // MEM_STABLE_LIST.value[3].avg = phaseList.value[2].cpu_maxclock

    console.log('%c handle MEM_STABLE_LIST::','color:red',MEM_STABLE_LIST.value);
   }

   if(realdata.value['model'][3].tested)
   {
    DOUBLE_STABLE_LIST.value[0].max = phaseList.value[3].cpu_maxtemp
    DOUBLE_STABLE_LIST.value[0].min = phaseList.value[3].cpu_mintemp
    DOUBLE_STABLE_LIST.value[0].avg = phaseList.value[3].cpu_avgtemp

    DOUBLE_STABLE_LIST.value[1].max = phaseList.value[3].cpu_maxtdp
    DOUBLE_STABLE_LIST.value[1].min = phaseList.value[3].cpu_mintdp
    DOUBLE_STABLE_LIST.value[1].avg = phaseList.value[3].cpu_avgtdp

    DOUBLE_STABLE_LIST.value[2].max = phaseList.value[3].cpu_maxclock
    DOUBLE_STABLE_LIST.value[2].min = phaseList.value[3].cpu_minclock
    DOUBLE_STABLE_LIST.value[2].avg = phaseList.value[3].cpu_avgclock 

    DOUBLE_STABLE_LIST.value[3].max = phaseList.value[3].gpu_maxtemp
    DOUBLE_STABLE_LIST.value[3].min = phaseList.value[3].gpu_mintemp
    DOUBLE_STABLE_LIST.value[3].avg = phaseList.value[3].gpu_avgtemp

    DOUBLE_STABLE_LIST.value[4].max = phaseList.value[3].gpu_maxmemTemp
    DOUBLE_STABLE_LIST.value[4].min = phaseList.value[3].gpu_minmemTemp
    DOUBLE_STABLE_LIST.value[4].avg = phaseList.value[3].gpu_avgmemTemp

    console.log('%c handle DOUBLE_STABLE_LIST::','color:red',DOUBLE_STABLE_LIST.value);
    xlength.value = []

    for(const item of phaseList.value[chooseModel.value].data)
    {
      xlength.value.push(' ')
    }
      console.warn('Xlength::',xlength.value);
   }

   handleLimitInfo()

}

const handleLimitInfo = () =>
{
  let LimitArr = phaseList.value[4].limitArr
  let Limit_Arr = []
  if(LimitArr.length>0)
  {
    for(const item of LimitArr)
    {
       Limit_Arr.push(Object.keys(item))
    }

        function countOccurrences(arr:any) {
      // 创建一个空对象用于存储统计结果
      const result:any = {};
      
      // 遍历数组中的每个字符串
      for (const str of arr) {
          // 如果对象中已经有该字符串的键，则将其对应的值加1
          if (result[str]) {
              result[str]++;
          } else {
              // 如果对象中没有该字符串的键，则初始化为1
              result[str] = 1;
          }
      }
      
      // 返回统计结果对象
      return result;
    }

    counts.value = countOccurrences(Limit_Arr);
   
    console.log('%c limitObject','color:red',counts);
    keyName.value = Object.keys(counts.value)
  }

  
  

}

const PM_GetHardwareBase = async (BaseJsonStr:any) =>
{
  let hardwareinfo:any = {}
  console.warn('GetHardwareBase Start');
   let data_arr = null;
   if (BaseJsonStr) {
      data_arr = JSON.parse(decodeURIComponent(BaseJsonStr));
   } else {
      data_arr = JSON.parse(await gamepp.hardware.getBaseJsonInfo.promise());
   }

   console.warn('硬件详情信息',data_arr);
   console.warn(typeof(data_arr));

   for(let key in data_arr)
   {
      let HWInfoV = data_arr[key]
      // console.warn('HWInfoV',HWInfoV);

      // console.warn('DataArrINfo',key,data_arr[key]);
      switch(key)
      {
         case 'COMPUTER':
           let SystemName = ''
           if (HWInfoV.OperatingSystem)
           {
             SystemName = (HWInfoV.OperatingSystem
               .replace('Microsoft ', '')
               .replace('Build ', ''));
           }
           hardwareinfo['SystemName'] =  SystemName

        break;
        case 'CPU':
          let CPUType = 'intel'
          if (HWInfoV['SubNode'] && HWInfoV['SubNode'][0])
          {
            const CPUSubNode = HWInfoV['SubNode'][0];
            // let OriginalProcessorFrequency = Number(CPUSubNode["OriginalProcessorFrequency[MHz]"]) - 50;
            if ((CPUSubNode['ProcessorName'] || '').includes('Intel')) {CPUType = 'intel'} else {CPUType = 'amd'}
            // console.warn('CPUType',CPUType);
            hardwareinfo['CPUType'] =  CPUType
            hardwareinfo['ProcessorName'] =  CPUSubNode['ProcessorName']
            hardwareinfo['CPUCores'] = showNumberOfCPUCores(CPUSubNode)
            hardwareinfo['LogicalCPUs'] = showNumberOfLogicalCpus(CPUSubNode)
            hardwareinfo['NumberofCPUCores'] = CPUSubNode.NumberofCPUCores
            hardwareinfo['NumberofLogicalCPUs'] = CPUSubNode.NumberofLogicalCPUs
            hardwareinfo['L3Cache'] = CPUSubNode.L3Cache
            hardwareinfo['CPUTechnology'] = CPUSubNode.CPUTechnology
            hardwareinfo['OriginalProcessorFrequency'] = CPUSubNode.OriginalProcessorFrequency
            hardwareinfo['CPUTurboMax'] = CPUSubNode.CPUTurboMax ? CPUSubNode.CPUTurboMax.toLowerCase().split('mhz')[0] + 'MHz' : ''
            hardwareinfo['InstructionTLB'] = CPUSubNode.InstructionTLB // 指令集
          }
          console.warn('%c CPUCOREINFO::',hardwareinfo);
          
        break;
        case 'MOBO':
          let MainboardName = '',SystemManufacturer,MotherboardChipset
          if (HWInfoV['Mainboard'] && HWInfoV['Mainboard']['MainboardName'])
          {
            MainboardName =  HWInfoV['Mainboard']['MainboardName'];
          }

          if (HWInfoV['Mainboard'] && HWInfoV['Mainboard']['MainboardManufacturer'])
          {
            if (HWInfoV['Mainboard']['MainboardManufacturer'] === 'Notebook')
            {
              SystemManufacturer = HWInfoV['System']['SystemManufacturer']
            }
            else
            {
              SystemManufacturer = HWInfoV['Mainboard']['MainboardManufacturer'].replace('Technology', '').replace('And', '').replace('Development', '').replace('Computer', '').replace('COMPUTER', '').replace('Co.,LTD', '').replace('INC.', '')
            }
          }
          else
          {
            SystemManufacturer = ''
          }
          if (HWInfoV['Property'] && HWInfoV['Property']['MotherboardChipset'] && typeof HWInfoV['Property']['MotherboardChipset'] === 'string')
          {
            MotherboardChipset = HWInfoV['Property']['MotherboardChipset'].replace(/\(.*?\)/g, '')
          }
          else
          {
            MotherboardChipset = ''
          }
          hardwareinfo['MainboardName'] =  MainboardName
          hardwareinfo['SystemManufacturer'] =  SystemManufacturer
          hardwareinfo['MotherboardChipset'] =  MotherboardChipset
          hardwareinfo['BIOSVersion'] =  HWInfoV.BIOS?.BIOSVersion
          if (!hardwareinfo['BIOSVersion']) hardwareinfo['BIOSVersion'] = ''
        break;
        case 'DRIVES':
            let gameDiskName =  hardwareinfo['gameDiskName']
            if (HWInfoV['SubNode'])
            {
              let drive_index:any = HWInfoV['SubNode'].findIndex((item:any) => item['DriveModel'] === gameDiskName);
              drive_index = drive_index !== -1 ? drive_index : 0;
              let DriveData = HWInfoV['SubNode'][drive_index]
              let drivesType = 'HDD'
              if ((DriveData.MediaRotationRate && (DriveData.MediaRotationRate).indexOf('SSD') !== -1) || (DriveData.DriveController)?.includes('NVMe') || (DriveData.Interface)?.includes('NVMe')) {drivesType = 'SSD';} else if (DriveData.MediaRotationRate && (DriveData.MediaRotationRate).includes('RPM')) {drivesType === 'HDD'} else {drivesType === 'HDD'}
              if (((DriveData.DriveModel)?.toLowerCase()).includes('ssd')) {drivesType = 'SSD'}
              hardwareinfo['drivesType'] = drivesType
              hardwareinfo['Drive_size'] = DiskCapacityConversion(DriveData['DriveCapacity[MB]'], 1024)
              hardwareinfo['Drive_size[MB]'] = DriveData['DriveCapacity[MB]']*1
              if (!gameDiskName) {
                hardwareinfo['disk_name'] = DriveData.DriveModel || ''
              }
              hardwareinfo['DriveController'] = DriveData.DriveController || ''
            }
            else
            {
              hardwareinfo['drivesType'] = ''
              hardwareinfo['Drive_size'] = ''
              hardwareinfo['Drive_size[MB]'] = 0
              hardwareinfo['disk_name'] = ''
              hardwareinfo['DriveController'] = ''
            }
        break;
        case 'GPU':
            let GPUIndex = FilterGPUIndexFromMemoryNumber(data_arr)

            function FilterGPUIndexFromMemoryNumber (JsonInfo:any) {
              let srcIndex = 0
              if (JsonInfo.GPU && JsonInfo.GPU.SubNode) {
                let Gpu_Count = JsonInfo.GPU.SubNode.length
                if (Gpu_Count === 1) { return(0)}
                for (let i = 0; i < JsonInfo.GPU.SubNode.length; i++) {
                  const currentNode = JsonInfo.GPU.SubNode[i];
                  const currentVideoMemoryNumber = currentNode.VideoMemoryNumber;
                  if (currentVideoMemoryNumber > (JsonInfo.GPU.SubNode[srcIndex].VideoMemoryNumber || 0)) {
                    srcIndex = i;
                  }
                }
              }else{
                return (0)
              }
              return(srcIndex)
            }
            // let GPUIndex = 0
            let GPUData = HWInfoV['SubNode'][GPUIndex]
            console.warn('显卡索引',GPUIndex);
            console.warn('显卡数据',GPUData);
            if (!GPUData) GPUData = {VideoCard:''}

            // hardwareinfo['GPU'] = GPUData['VideoChipset']
            hardwareinfo['GPU'] = String(GPUData['VideoCard'])
            hardwareinfo['GPU'] = hardwareinfo['GPU'].replace(/\(.*?\)/g, '').replace(/\[.*?\]/g, '').trim();
            hardwareinfo['GPU_DriverVersion'] = GPUData['DriverVersion']
            hardwareinfo['GPU_DriverDate'] = FormartMonthToNumber(GPUData['DriverDate'])
            hardwareinfo['GPU_Brand'] = gpu_brand(GPUData)
            hardwareinfo['GPU_NumberOfUnifiedShaders'] = GPUData['NumberOfUnifiedShaders']
            hardwareinfo['GPU_GraphicsMemoryBusWidth'] = GPUData['GraphicsMemoryBusWidth']
            hardwareinfo['GPU_ASICManufacturer'] = GPUData['ASICManufacturer']
            hardwareinfo['VideoMemoryNumber'] = GPUData['VideoMemoryNumber']

            if (GPUData['VideoMemory']) {
              const VideoMemory = String(GPUData['VideoMemory'])
              if (VideoMemory.includes('[') && VideoMemory.includes(']')) {
                try {
                  const MidStr = VideoMemory.match(/\[(.+?)\]/)[1];
                  hardwareinfo['VideoMemoryBrand'] = MidStr  
                }catch (e) {

                }
              }
            }

            if (!hardwareinfo['VideoMemoryBrand']) hardwareinfo['VideoMemoryBrand'] = ''

            let videoCardName = GPUData.VideoCard;
            let VideoBrand = videoCardName.match(/\[(.+?)\]$/) ? RegExp.$1 : videoCardName.split(' ')[0] || '';

            hardwareinfo['VideoBrand'] = VideoBrand
            const gpu_memory_size_type = (data: any) => {
              const regex = /\[(.+?)\]/g
              const VideoMemory = (data.VideoMemory).split(' ')
              const VideoMemoryBrandArr = (data.VideoMemory.match(regex))
              let VideoMemoryBrand = ''
              if (VideoMemoryBrandArr) {
                VideoMemoryBrand = (VideoMemoryBrandArr[VideoMemoryBrandArr.length - 1]).replace(/\[|]/g, '')
              }
              let VideoType = ''
              if (VideoMemory[3]) {
                VideoType = VideoMemory[3]
              }
              let typeBrand = ''
              if (VideoType || VideoMemoryBrand) {
                typeBrand = ' (' + VideoType + ' ' + VideoMemoryBrand + ')'
              }
              let n = 1024
              if (data.VideoMemory.includes('MBytes')) {
                n = 1024
              } else if (data.VideoMemory.includes('KBytes')) {
                n = 1024*1024
              } else if (data.VideoMemory.includes('GBytes')) {
                n = 1
              }
              return Math.ceil(((VideoMemory[0] / n))) + 'GB'
            }
            if (GPUData.VideoMemory && GPUData.VideoMemory !== 'Unknown') {
                hardwareinfo['GPU_VideoMemor'] = gpu_memory_size_type(GPUData)
            }
                let gpu_name = GPUData['VideoChipset'];
                let GPUType = ''
            if (['Radeon', 'AMD', 'Vega','amd'].find(item => gpu_name.includes(item))) {GPUType = 'amd';} else if (['GeForce', 'NVIDIA'].find(item => gpu_name.includes(item))) {GPUType = 'nvidia'}
                 hardwareinfo['GPUType'] = GPUType
            console.log('%c GPU END','color:green');
            
       break;
        case 'MEMORY':
            let MemoryHtml = '';
            hardwareinfo['memory_list_all'] = []
            if (HWInfoV['SubNode'] != null) {
              let arr:Array<string> = []
              let arr2:Array<number> = []
              let arr3 = []
              for (let i = 0; i < HWInfoV['SubNode'].length; i++) {
                const item = HWInfoV['SubNode'][i];
                let str = ''
                str += item.ModuleManufacturer;
                str += ' '
                str += ((item.MemorySpeed)?.match(/\((.+?)\)/g)[0])?.split('/')[0].replace('(', '') || ''
                // str += Math.floor(item.MemorySpeed?.split(' ')[0]) + ' MHz '
                str += item.ModuleSize?.split(' ')[0] + 'GB'
                hardwareinfo['memory_list_all'].push(str);
                if (arr.length === 0) {
                  arr.push(str)
                  arr2.push(1)
                  arr3.push({
                    'MemoryType': item['MemoryType']
                  })
                }else{
                  if (arr.includes(str)){
                    arr2[arr.indexOf(str)] = arr2[arr.indexOf(str)]+1
                  }else{
                    arr.push(str)
                    arr2.push(1)
                    arr3.push({
                      'MemoryType': item['MemoryType']
                    })
                  }
                }
              }
              hardwareinfo['memory_list1'] = arr; // 内存条名字
              hardwareinfo['memory_list2'] = arr2; // 内存条数量
              hardwareinfo['memory_list3'] = arr3; // 内存条类型
              hardwareinfo['memory'] = HWInfoV['SubNode'][0]['ModuleManufacturer'] + "  " + +(HWInfoV.Property['TotalMemorySize[MB]']) / 1024 + 'GB' + "  " + (Math.ceil(HWInfoV.Property['CurrentMemoryClock'].split('MHz')[0])) * 2 + 'MHz' + "  " + HWInfoV.Property['CurrentTiming(tCAS-tRCD-tRP-tRAS)']

                // MemoryHtml = '<div class="memory">' + HWInfoV['SubNode'][0]['ModuleManufacturer'] + "  " + +(HWInfoV.Property['TotalMemorySize[MB]']) / 1024 + 'GB' + "  " + (Math.ceil(HWInfoV.Property['CurrentMemoryClock'].split('MHz')[0])) * 2 + 'MHz' + "  " + HWInfoV.Property['CurrentTiming(tCAS-tRCD-tRP-tRAS)'] + '</div></li>';
            }
            let obj = HWInfoV['Property']
            if (!obj) obj = {}
            if (obj['TotalMemorySize[MB]']) {
              hardwareinfo['TotalMemorySize[MB]'] = obj['TotalMemorySize[MB]'];
              hardwareinfo['Memory_size']  = (obj['TotalMemorySize[MB]'] / 1024) + 'GB'
            } else {
              hardwareinfo['TotalMemorySize[MB]'] = 0
              hardwareinfo['Memory_size']  = ''
            }
            if (obj['MemoryChannelsActive']) {
              hardwareinfo['Memory_channels_active'] = obj['MemoryChannelsActive']
            } else {
              hardwareinfo['Memory_channels_active']  = ''
            }
            if (HWInfoV['SubNode'] && Array.isArray(HWInfoV['SubNode'])) {
              hardwareinfo['Memory_count'] = HWInfoV['SubNode'].length
            } else {
              hardwareinfo['Memory_count']  = 0
            }

            if (obj['CurrentMemoryClock']) {
              hardwareinfo['Memory_Clock'] = (Math.ceil(obj['CurrentMemoryClock'].split('MHz')[0])) * 2
            } else {
              hardwareinfo['Memory_Clock']  = ''
            }

            if (obj['CurrentTiming(tCAS-tRCD-tRP-tRAS)']) {
              hardwareinfo['Memory_CurrentTiming'] = obj['CurrentTiming(tCAS-tRCD-tRP-tRAS)']
            }else{
              hardwareinfo['Memory_CurrentTiming'] = ''
            }
            console.log('%c MEM END','color:green');

        break;
        case 'MONITOR':
            let MONITORDataV = HWInfoV['SubNode'][0]
            let MonitorNameStr
            if (MONITORDataV.MonitorName !== 'Unknown') {
              hardwareinfo['MonitorName'] = MONITORDataV.MonitorName.replace(/\[.*?\]/g, '');
            } else {
              hardwareinfo['MonitorName'] = ''
            }
            if (MONITORDataV['MonitorName(Manuf)']) {
                let brand = RemoveAllSpace( hardwareinfo['MonitorName']);
                let model = RemoveAllSpace(MONITORDataV['MonitorName(Manuf)']);
                if (brand.toLowerCase() === model.toLowerCase()) {
                    MonitorNameStr = MONITORDataV['MonitorName'];
                } else {
                    MonitorNameStr = hardwareinfo['MonitorName']+ MONITORDataV['MonitorName(Manuf)'];
                }
            } else {
                MonitorNameStr = MONITORDataV['MonitorName'];
            }
            if (MonitorNameStr !== '') {
              hardwareinfo['MonitorNameStr'] = MonitorNameStr
              hardwareinfo['refresh_rate'] = MONITORDataV['RefreshFrequency'] + 'Hz'
              let WH = MONITORDataV.Resolutions.split('*')
              hardwareinfo['resolutiop'] = WH[0] + '*' + WH[1]

                if (MONITORDataV['Max.HorizontalSize'] && MONITORDataV['Max.VerticalSize']) {
                    let HorizontalSize = RegExGetNum(MONITORDataV['Max.HorizontalSize']);
                    let VerticalSize = RegExGetNum(MONITORDataV['Max.VerticalSize']);
                    let MonitorSize = parseFloat((Math.sqrt(Math.pow(HorizontalSize, 2) + Math.pow(VerticalSize, 2)) / 2.54).toFixed(1));
                    hardwareinfo['display_screen_size'] = MonitorSize + '英寸'
                }
            }
            console.log('%c MONITOR END','color:green');

            break;
      }
   }
   console.warn('GetHardwareBase End');
   console.warn('hard1;:',);

  return hardwareinfo
}


function calculateAverage(arr:any) {
    if (!Array.isArray(arr) || arr.length === 0) {
        throw new Error('Input must be a non-empty array');
    }
    const sum = arr.reduce((sum, current) => sum + current, 0);
    return sum / arr.length;
}

const CPU_TEMP_LIST = ref([
    {
        name:'CPU温度',
        max:110,
        min:0,
        avg:20,
    },
    {
        name:'CPU热功耗',
        max:110,
        min:0,
        avg:20,
    },{
        name:'CPU频率',
        max:110,
        min:0,
        avg:20,
    },
])

const CPU_STABLE_LIST = ref([
    {
        name:'CPU温度',
        max:110,
        min:0,
        avg:20,
    },
    {
        name:'CPU热功耗',
        max:110,
        min:0,
        avg:20,
    },{
        name:'CPU频率',
        max:110,
        min:0,
        avg:20,
    },
])

const MEM_STABLE_LIST = ref([
    {
        name:'CPU温度',
        max:110,
        min:0,
        avg:20,
    },
    {
        name:'CPU热功耗',
        max:110,
        min:0,
        avg:20,
    },{
        name:'CPU频率',
        max:110,
        min:0,
        avg:20,
    },
    {
        name:'内存温度',
        max:110,
        min:0,
        avg:20,
    },

])

const DOUBLE_STABLE_LIST = ref([
    {
        name:'CPU温度',
        max:110,
        min:0,
        avg:20,
    },
    {
        name:'CPU热功耗',
        max:110,
        min:0,
        avg:20,
    },{
        name:'CPU频率',
        max:110,
        min:0,
        avg:20,
    },
    {
        name:'GPU温度',
        max:110,
        min:0,
        avg:20,
    },
    {
        name:'显存温度',
        max:110,
        min:0,
        avg:20,
    },
])

const chooseTab = async(index:number) =>
{
   await clearCharts()
    for(const item of TabList.value)
    {
        item.choosen = false
    }

    TabList.value[index].choosen = true

    initCharts()
    await insertSeries()
}

const clearCharts = async() =>
{
  chartsList.value = []
  cpu_options.value.series = []
}

async function setting(index:any)
{
  if(index == 0)
  { // 最小化
    await gamepp.webapp.windows.minimize.promise('pressureResult');
  }
  else if (index == 3)
  {
    await gamepp.webapp.windows.close.promise('pressureResult')
  }
  console.warn(index);
}

const initCharts = async() =>
{
  xlength.value = []
  console.warn('e',chooseModel.value);
  for(const item of phaseList.value[chooseModel.value].data)
  {
    xlength.value.push(' ')
  }

  var chartDom = document.getElementById("cpuCharts");

  cpu_chart = echarts.init(chartDom);

  cpu_options.value = {
            xAxis:{
                type: 'category',
                data: xlength.value,//X轴个数
                show: false,
                boundaryGap: false,
                axisTick: { show: false },
                axisLabel: {
                  show: false,
                },
            },
            tooltip: {
                trigger: 'axis',
                formatter:function(data:any)
                {
                  let AllDataHtml:any = '<div class="AllDataHtml">'
                  let time = data[0].dataIndex
                  for(const item of data)
                  {
                    AllDataHtml += `${item.seriesName.split('_')[0]} : `+ item.value + `${item.seriesName.split('_')[1]}`+'<br/>';
                  }
                  AllDataHtml +=  `${FormatTimePlus((Starttime + time*1000)/1000)}`+'<br/>';
                  // AllDataHtml =`<span class="rickxixi"> FPS :  ${data[0]['value']}</span>  '<br/>';`
                  AllDataHtml += '</div>'
                  if(data[0].value)
                  {
                    return AllDataHtml
                  }else
                  {

                  }
                },

                backgroundColor: 'rgb(44,44,51,0.8)',
                textStyle: {
                  fontSize: '12',
                  color: 'rgb(195,202,213)'
                },
            },
            yAxis: [
            {
              type: 'log', // 使用对数轴
              logBase: 10, // 对数底数为10
              min: 1, // 设置最小值
              // max: 10000, // 设置最大值
              axisLabel: {
                color: "#21222a"  //刻度线标签颜色
              },
              axisTick: { //y轴刻度线
                show: false
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: ['#333645']
                }
              }
            }
          ],
          grid: {
            left: 0,
            right: 0,
            bottom: 0,
            top: 0
           },
          series: [
           
          ],
      }
}
    const getSpanStyle = (value:any) =>
    {
      let obj = {}
      if(value>90)
      {
        obj = { color: '#F14343' } 
      }
      else if(value > 80 && value <= 90)
      {
       obj = { color:'#F7B12A' } 
      }
      else
      {
       obj = { color: '#35D57D' };
      }
      return obj
    }

</script>

<template>
    <div class="pressureResult">
        <div class="Nav">
        <div class="GPPInfo" style="width: 1100px;-webkit-app-region: drag;">
            <img src="../../assets/img/Public/logo_gpp.png" alt="">
            <span class="slogan">压力测试</span>
            <span class="slogan">V.211.0.119</span>
        </div>
        <RightTopIcons
                close-icon
                minimize-icon
                @close="setting(3)"
                @minimize="setting(0)"
                hover-color="#22232e"
            />
        </div>
        <div class="ResultBox">
         <div class="baseInfo" style="margin-top: 10px;">
            <div style="display: flex;align-items: center;margin-left: 20px;font-size: 12px;">
                <span style="color: #999999;">开始时间：</span><span>{{ realdata['starttime'] }}</span>
                <span style="margin-left: 10px;color: #999999;">测试时长：</span><span>{{ realdata['endtime']}}</span>
            </div>
            <div class="Tab" style="display: flex;align-items: center;">
                <div class="item" v-for="(item,index) in TabList" @click="chooseTab(index)" :class="[item.choosen?'choosen':'']">
                    <span>{{ item.name }}</span>
                </div>
         </div>
         </div>
         <div class="content_1" v-show="TabList[0].choosen">
            <p class="title">参与测试硬件信息</p>
         <div class="simple">
            <div class="simple_box">
                <div class="line" style="margin-top: 20px;">
                    <span class="iconfont icon-CPU"></span>
                    <span style="margin-left: 10px;">{{ realdata['hard']['cpu_name'] }}</span>
                </div>
                <div class="line" style="font-size: 12px;margin-top: 10px;">
                    <div style="width: 200px;">
                        <span class="des">核心：</span><span>{{ realdata['hard']['cpu_core']}}</span>
                    </div>
                    <span class="des">线程：</span><span>{{ realdata['hard']['cpu_threads']}}</span>
                </div>
                <div class="line" style="font-size: 12px;margin-top: 10px;">
                    <div style="width: 200px;">
                        <!-- <span class="des">Tj,max：</span><span>95</span> -->
                        <span class="des">基础频率：</span><span>{{ realdata['hard']['cpu_baseFre']}}</span>
                    </div>
                    <!-- <span class="des">基础频率：</span><span>3800</span> -->
                </div>
            </div>
            <div class="simple_box">
                <div class="line" style="margin-top: 20px;">
                    <span class="iconfont icon-GPU"></span>
                    <span style="margin-left: 10px;">{{ realdata['hard']['gpu_name']}}</span>
                </div>
                <div class="line" style="font-size: 12px;margin-top: 10px;">
                    <div style="width: 300px;">
                        <span class="des">驱动版本：</span><span>{{ realdata['hard']['gpu_drive']}}</span>
                    </div>
                    <span class="des">显存：</span><span>{{ realdata['hard']['gpu_memory']}}</span>
                </div>
            </div>
            <div class="simple_box">
                <div class="line" style="margin-top: 20px;">
                    <span class="iconfont icon-Dram"></span>
                    <span style="margin-left: 10px;">{{ realdata['hard']['mem_name']}}</span>
                </div>
                <div class="line" style="font-size: 12px;margin-top: 10px;">
                    <div style="width: 200px;">
                        <span class="des">通道：</span><span>{{ realdata['hard']['mem_channel']}}</span>
                    </div>
                    <span class="des">频率：</span><span>{{ realdata['hard']['mem_clock']}}</span>
                </div>
                <div class="line" style="font-size: 12px;margin-top: 10px;">
                    <div style="width: 200px;">
                        <span class="des">大小：</span><span>{{ realdata['hard']['mem_size']}}</span>
                    </div>
                    <span class="des">时序：</span><span>{{ realdata['hard']['mem_time']}}</span>
                </div>
            </div>
         </div>
         <p class="title">测试阶段数据</p>
         <div class="complex">
            <div class="base">
                <p style="font-size: 14px;color:#409EFF;">CPU温度测试</p>
                <span v-if="realdata['model'][0].tested">
                  <span  class="des left20">测试时长：</span><span>{{realdata['model'][0].min}}分钟</span>
                  <span  class="des left20">温度传感器事件报告：</span><span>{{phaseList['0']['limitcount']}}次</span>
                  <span   class="des left20">最多事件：</span><span>{{phaseList['0']['limit']}}</span>
                </span>

            </div>
            <div class="middleBox"  >
                  <div v-if="realdata['model'][0].tested" class="item" v-for="(item,index) in CPU_TEMP_LIST">
                     <div class="left left10">
                        <div class="des" style="margin-top: 5px;">{{item.name}}</div>
                        <p class="val"  >{{ item.max }}{{ item.name.includes('温度')?'°C':item.name.includes('热功耗')?'W':'MHZ' }}</p>
                        <span class="des">最高</span>
                     </div>
                     <div class="vertical"></div>
                     <div class="right">
                       <div style="margin-top: 15px;"><span class="des">最低：</span><span>{{ item.min }}</span></div>
                       <div style="margin-bottom: 25px;"><span class="des">平均：</span><span>{{ item.avg}}</span></div>
                     </div>
                  </div>
                  <div v-if="!realdata['model'][0].tested" class="noneTest">
                    未进行测试
                  </div>
            </div>
         </div>
         <div class="complex">
            <div class="base">
                <p style="font-size: 14px;color:#409EFF;">CPU稳定性测试</p>
                <span v-if="realdata['model'][1].tested">
                  <span  class="des left20">测试时长：</span><span>{{realdata['model'][1].round}}轮次</span>
                  <span  class="des left20">CPU性能受限次数：</span><span>{{phaseList['1']['limitcount']}}次</span>
                  <span  class="des left20">最多受限原因：</span><span>{{phaseList['1']['limit']==''?'无':phaseList['1']['limit']}}</span>
                </span>



            </div>
            <div class="middleBox">
                  <div v-if="realdata['model'][1].tested" class="item" v-for="(item,index) in CPU_STABLE_LIST">
                     <div class="left left10">
                        <div class="des" style="margin-top: 5px;">{{item.name}}</div>
                        <p class="val" >{{ item.max }}{{ item.name.includes('温度')?'°C':item.name.includes('热功耗')?'W':'Mhz' }}</p>
                        <span class="des">最高</span>
                     </div>
                     <div class="vertical"></div>
                     <div class="right">
                       <div style="margin-top: 15px;"><span class="des">最低：</span><span>{{ item.min }}</span></div>
                       <div style="margin-bottom: 25px;"><span class="des">平均：</span><span>{{ item.avg}}</span></div>
                     </div>
                  </div>
                  <div v-if="!realdata['model'][1].tested" class="noneTest">
                    未进行测试
                  </div>
            </div>
         </div>
         <div class="complex">
            <div class="base">
                <p style="font-size: 14px;color:#409EFF;">内存稳定性测试</p>
                <span v-if="realdata['model'][2].tested">
                  <span  class="des left20">测试时长：</span><span>{{realdata['model'][2].round}}轮次</span>
                  <span  class="des left20">CPU性能受限次数：</span><span>{{phaseList['2']['limitcount']}}次</span>
                  <span  class="des left20">内存报错次数：</span><span>{{realdata['mem']['mem_error']}}次</span>
                  <span  class="des left20">最多受限原因：</span><span>{{phaseList['2']['limit']==''?'无':phaseList['2']['limit']}}</span>
                </span>

            </div>
            <div class="middleBox">
                  <div v-if="realdata['model'][2].tested" class="item" v-for="(item,index) in MEM_STABLE_LIST">
                     <div class="left left10">
                        <div class="des" style="margin-top: 5px;">{{item.name}}</div>
                        <p class="val">{{ item.max }}{{ item.name.includes('温度')?'°C':item.name.includes('热功耗')?'W':'Mhz' }}</p>
                        <span class="des">最高</span>
                     </div>
                     <div class="vertical"></div>
                     <div class="right">
                       <div style="margin-top: 15px;"><span class="des">最低：</span><span>{{ item.min }}</span></div>
                       <div style="margin-bottom: 25px;"><span class="des">平均：</span><span>{{ item.avg}}</span></div>
                     </div>
                  </div>
                  <div v-if="!realdata['model'][2].tested" class="noneTest">
                    未进行测试
                  </div>
            </div>
         </div>
         <div class="complex">
            <div class="base">
                <p style="font-size: 14px;color:#409EFF;">CPU&GPU测试</p>
                <span v-if="realdata['model'][3].tested">
                  <span  class="des left20">测试时长：</span><span>{{realdata['model'][3].round}}轮次</span>
                  <span  class="des left20">CPU性能受限次数：</span><span>{{phaseList['3']['limitcount']}}次</span>
                  <span  class="des left20">最多受限原因：</span><span>{{phaseList['3']['limit']==''?'无':phaseList['3']['limit']}}</span>
                </span>

            </div>
            <div class="middleBox">
                  <div v-if="realdata['model'][3].tested" class="item" v-for="(item,index) in DOUBLE_STABLE_LIST">
                     <div class="left left10">
                        <div class="des" style="margin-top: 5px;">{{item.name}}</div>
                        <p class="val">{{ item.max }}{{ item.name.includes('温度')?'°C':item.name.includes('热功耗')?'W':'Mhz' }}</p>
                        <span class="des">最高</span>
                     </div>
                     <div class="vertical"></div>
                     <div class="right">
                       <div style="margin-top: 15px;"><span class="des">最低：</span><span>{{ item.min }}</span></div>
                       <div style="margin-bottom: 25px;"><span class="des">平均：</span><span>{{ item.avg}}</span></div>
                     </div>
                  </div>
                  <div v-if="!realdata['model'][3].tested" class="noneTest">
                    未进行测试
                  </div>
            </div>
         </div>
         </div>
         <div class="content_2" v-show="TabList[1].choosen">
            <div class="selectInfo" style="margin-top: 10px;">
            <div class="select_left">
              <span style="color: #777777;font-size: 12px;">按测试阶段查看：</span>
              <div class="inputBox" style="width: 150px;margin-left: 10px;">
                <el-select popper-class="select-family" v-model="chooseModel" @change="selectModel">
                        <el-option                                         :value="4" :label="'测试总览'">{{'测试总览'}}</el-option>
                        <el-option  v-show="realdata['model'][0].tested"  :value="0" :label="'CPU温度测试'">{{'CPU温度测试'}}</el-option>
                        <el-option  v-show="realdata['model'][1].tested"  :value="1" :label="'CPU稳定性测试'">{{'CPU稳定性测试'}}</el-option>
                        <el-option  v-show="realdata['model'][2].tested"  :value="2" :label="'内存稳定性测试'">{{'内存稳定性测试'}}</el-option>
                        <el-option  v-show="realdata['model'][3].tested"  :value="3" :label="'CPU&GPU测试'">{{'CPU&GPU测试'}}</el-option>
               </el-select>
              </div>

            </div>
            <div class="select_right">
               <div class="tinyItem" v-for="(item,index) in chartsList">
                  <div class="matrix">
                     <div :style="{backgroundColor:item.color}"></div>
                     <div :style="{backgroundColor:item.color}" style="margin-left: 1px;"></div>
                     <div :style="{backgroundColor:item.color}" style="margin-top: 1px;"></div>
                     <div :style="{backgroundColor:item.color}" style="margin-top: 1px;margin-left: 1px;"> </div>
                  </div>
                  <div class="inputBox" style="width: 125px;height: 22px;">
                    <!-- <el-select  popper-class="select-family" v-model="item.name" @change="changeOption(ite,.key)"> -->
                      <el-select  popper-class="select-family">
                        <!-- <el-select  popper-class="select-family"   @change="changeOption"> -->
                      <template #prefix>
                          <span :style="{ color: item.color }">{{item.name.split('_')[0]}}</span> <!-- 自定义占位符 -->
                        </template>
                        <el-option  style="color: red !important;" :value="0" :label="''"  @change="''" @click="deleteLine(index)">{{'删除该折线'}}</el-option>
                        <el-option  :value="option[0]"  @click="changeOption([oindex,index])":label="''" v-for="(option,oindex) in choosenList">{{option[0]}}</el-option>
                     </el-select>
                  </div>
               </div>
               <div class="tinyItem" @click="addSereis()" style="width: 100px;height: 21px;border: 1px solid #999999;border-radius: 6px;display: flex;align-items: center;justify-content: center;margin-right: 20px;cursor: pointer;">
                 <span style="color: white;font-size: 12px;">添加</span>
               </div>
            </div>                           
            </div>
            <div class="total_Info" id="cpuCharts" style="background-color: #22232E;">

            </div>
            <div class="box">
              <!-- CPU -->
              <div class="boxitem" style="height: 265px;margin-left: 20px;">
                <div class="line">
                  <span class="iconfont icon-CPU" style="color: white; font-size: 24px; margin-right: 5px;"></span>
                  <span>{{ bg_sensor_data['cpu']['name'] }}</span>
                </div>
                <!-- chooseModel -->
                <div class="line">
                  <span class="des cure">温度</span>
                  <div class="cure"><span class="des">Avg：</span><span :style="getSpanStyle(phaseList[chooseModel]['cpu_avgtemp'])">{{ phaseList[chooseModel]['cpu_avgtemp'] }}°C</span></div>
                  <div class="cure"><span class="des">Max：</span><span :style="getSpanStyle(phaseList[chooseModel]['cpu_maxtemp'])">{{ phaseList[chooseModel]['cpu_maxtemp'] }}°C</span></div>
                  <div class="cure"><span class="des">Min：</span><span :style="getSpanStyle(phaseList[chooseModel]['cpu_mintemp'])">{{ phaseList[chooseModel]['cpu_mintemp'] }}°C</span></div>
                </div>
                <div class="line" v-if="hasPECORE">
                  <span class="des cure">P-频率</span>
                  <div class="cure"><span class="des">Avg：</span><span>{{ phaseList[chooseModel]['cpu_avgP'] }}MHZ</span></div>
                  <div class="cure"><span class="des">Max：</span><span>{{ phaseList[chooseModel]['cpu_maxP'] }}MHZ</span></div>
                  <div class="cure"><span class="des">Min：</span><span>{{ phaseList[chooseModel]['cpu_minP'] }}MHZ</span></div>
                </div>
                <div class="line" v-if="hasPECORE">
                  <span class="des cure">E-频率</span>
                  <div class="cure"><span class="des">Avg：</span><span>{{ phaseList[chooseModel]['cpu_avgE'] }}MHZ</span></div>
                  <div class="cure"><span class="des">Max：</span><span>{{ phaseList[chooseModel]['cpu_avgE'] }}MHZ</span></div>
                  <div class="cure"><span class="des">Min：</span><span>{{ phaseList[chooseModel]['cpu_avgE'] }}MHZ</span></div>
                </div>
                <div class="line" v-if="!hasPECORE">
                  <span class="des cure">频率</span>
                  <div class="cure"><span class="des">Avg：</span><span>{{ phaseList[chooseModel]['cpu_avgclock'] }}MHZ</span></div>
                  <div class="cure"><span class="des">Max：</span><span>{{ phaseList[chooseModel]['cpu_maxclock'] }}MHZ</span></div>
                  <div class="cure"><span class="des">Min：</span><span>{{ phaseList[chooseModel]['cpu_minclock'] }}MHZ</span></div>
                </div>
                <div class="line">
                  <span class="des cure">电压</span>
                  <div class="cure"><span class="des">Avg：</span><span>{{ phaseList[chooseModel]['cpu_avgvol'] }}V</span></div>
                  <div class="cure"><span class="des">Max：</span><span>{{ phaseList[chooseModel]['cpu_maxvol'] }}V</span></div>
                  <div class="cure"><span class="des">Min：</span><span>{{ phaseList[chooseModel]['cpu_minvol'] }}V</span></div>
                </div>
                <div class="line">
                  <span class="des cure">TDP</span>
                  <div class="cure"><span class="des">Avg：</span><span>{{ phaseList[chooseModel]['cpu_avgtdp'] }}W</span></div>
                  <div class="cure"><span class="des">Max：</span><span>{{ phaseList[chooseModel]['cpu_maxtdp'] }}W</span></div>
                  <div class="cure"><span class="des">Min：</span><span>{{ phaseList[chooseModel]['cpu_mintdp'] }}W</span></div>
                </div>
                <div class="line">
                  <span class="des cure">风扇转速</span>
                  <div class="cure"><span class="des">Avg：</span><span>{{ phaseList[chooseModel]['cpu_avgfan'] }}RPM</span></div>
                  <div class="cure"><span class="des">Max：</span><span>{{ phaseList[chooseModel]['cpu_avgfan'] }}RPM</span></div>
                  <div class="cure"><span class="des">Min：</span><span>{{ phaseList[chooseModel]['cpu_avgfan'] }}RPM</span></div>
                </div>
                   <div class="line" v-if="CPUTYPE == 'amd'">
                  <span class="des cure">最大功耗</span>
                  <div class="cure"><span class="des">Avg：</span><span>{{ phaseList[chooseModel]['amd_maxppt'] }}%</span></div>
                  <div class="cure"><span class="des">Max：</span><span>{{ phaseList[chooseModel]['amd_minppt'] }}%</span></div>
                  <div class="cure"><span class="des">Min：</span><span>{{ phaseList[chooseModel]['amd_avgppt'] }}%</span></div>
                </div>
                   <div class="line" v-if="CPUTYPE == 'amd'">
                  <span class="des cure">最大电流</span>
                  <div class="cure"><span class="des">Avg：</span><span>{{ phaseList[chooseModel]['amd_maxedc'] }}%</span></div>
                  <div class="cure"><span class="des">Max：</span><span>{{ phaseList[chooseModel]['amd_minedc'] }}%</span></div>
                  <div class="cure"><span class="des">Min：</span><span>{{ phaseList[chooseModel]['amd_avgedc'] }}%</span></div>
                </div>
                   <div class="line" v-if="CPUTYPE == 'amd'">
                  <span class="des cure">最大温度</span>
                  <div class="cure"><span class="des">Avg：</span><span>{{ phaseList[chooseModel]['amd_maxthermal'] }}%</span></div>
                  <div class="cure"><span class="des">Max：</span><span>{{ phaseList[chooseModel]['amd_minthermal'] }}%</span></div>
                  <div class="cure"><span class="des">Min：</span><span>{{ phaseList[chooseModel]['amd_avgthermal'] }}%</span></div>
                </div>
              </div>
              <!-- GPU -->
              <div class="boxitem" style="height: 265px;">
                <div class="line">
                  <span class="iconfont icon-GPU" style="color: white; font-size: 24px; margin-right: 5px;"></span>
                  <span>{{ bg_sensor_data['gpu']['name'] }}</span>
                </div>
                <div class="line">
                  <span class="des cure">温度</span>
                  <div class="cure"><span class="des">Avg：</span><span :style="getSpanStyle(phaseList[chooseModel]['gpu_avgtemp'])">{{ phaseList[chooseModel]['gpu_avgtemp'] }}°C</span></div>
                  <div class="cure"><span class="des">Max：</span><span :style="getSpanStyle(phaseList[chooseModel]['gpu_maxtemp'] )">{{ phaseList[chooseModel]['gpu_maxtemp'] }}°C</span></div>
                  <div class="cure"><span class="des">Min：</span><span :style="getSpanStyle(phaseList[chooseModel]['gpu_mintemp'])">{{ phaseList[chooseModel]['gpu_mintemp'] }}°C</span></div>
                </div>
                <div class="line">
                  <span class="des cure">显存温度</span>
                  <div class="cure"><span class="des">Avg：</span><span :style="getSpanStyle(phaseList[chooseModel]['gpu_avgmemTemp'])">{{ phaseList[chooseModel]['gpu_avgmemTemp'] }}°C</span></div>
                  <div class="cure"><span class="des">Max：</span><span :style="getSpanStyle(phaseList[chooseModel]['gpu_maxmemTemp'])">{{ phaseList[chooseModel]['gpu_maxmemTemp'] }}°C</span></div>
                  <div class="cure"><span class="des">Min：</span><span :style="getSpanStyle(phaseList[chooseModel]['gpu_minmemTemp'])">{{ phaseList[chooseModel]['gpu_minmemTemp'] }}°C</span></div>
                </div>
                <div class="line">
                  <span class="des cure">核心热点温度</span>
                  <div class="cure"><span class="des">Avg：</span><span :style="getSpanStyle(phaseList[chooseModel]['gpu_avghot'])">{{ phaseList[chooseModel]['gpu_avghot'] }}°C</span></div>
                  <div class="cure"><span class="des">Max：</span><span :style="getSpanStyle(phaseList[chooseModel]['gpu_maxhot'])">{{ phaseList[chooseModel]['gpu_maxhot'] }}°C</span></div>
                  <div class="cure"><span class="des">Min：</span><span :style="getSpanStyle(phaseList[chooseModel]['gpu_minhot'])">{{ phaseList[chooseModel]['gpu_minhot'] }}°C</span></div>
                </div>
                <div class="line">
                  <span class="des cure">频率</span>
                  <div class="cure"><span class="des">Avg：</span><span>{{ phaseList[chooseModel]['gpu_avgclock'] }}MHZ</span></div>
                  <div class="cure"><span class="des">Max：</span><span>{{ phaseList[chooseModel]['gpu_maxclock'] }}MHZ</span></div>
                  <div class="cure"><span class="des">Min：</span><span>{{ phaseList[chooseModel]['gpu_minclock'] }}MHZ</span></div>
                </div>
                <div class="line">
                  <span class="des cure">D3D占用</span>
                  <div class="cure"><span class="des">Avg：</span><span>{{ phaseList[chooseModel]['gpu_avgD3D'] }}%</span></div>
                  <div class="cure"><span class="des">Max：</span><span>{{ phaseList[chooseModel]['gpu_maxD3D'] }}%</span></div>
                  <div class="cure"><span class="des">Min：</span><span>{{ phaseList[chooseModel]['gpu_minD3D'] }}%</span></div>
                </div>
                <div class="line">
                  <span class="des cure">Total占用</span>
                  <div class="cure"><span class="des">Avg：</span><span>{{ phaseList[chooseModel]['gpu_avgload'] }}%</span></div>
                  <div class="cure"><span class="des">Max：</span><span>{{ phaseList[chooseModel]['gpu_maxload'] }}%</span></div>
                  <div class="cure"><span class="des">Min：</span><span>{{ phaseList[chooseModel]['gpu_minload'] }}%</span></div>
                </div>
                <div class="line">
                  <span class="des cure">风扇转速</span>
                  <div class="cure"><span>Avg：</span><span>{{ phaseList[chooseModel]['gpu_avgfan'] }}RPM</span></div>
                  <div class="cure"><span>Max：</span><span>{{ phaseList[chooseModel]['gpu_maxfan'] }}RPM</span></div>
                  <div class="cure"><span>Min：</span><span>{{ phaseList[chooseModel]['gpu_minfan'] }}RPM</span></div>
                </div>
                <div class="line">
                  <span class="des cure">TDP</span>
                  <div class="cure"><span>Avg：</span><span>{{ phaseList[chooseModel]['gpu_avgtdp'] }}W</span></div>
                  <div class="cure"><span>Max：</span><span>{{ phaseList[chooseModel]['gpu_maxtdp'] }}W</span></div>
                  <div class="cure"><span>Min：</span><span>{{ phaseList[chooseModel]['gpu_mintdp'] }}W</span></div>
                </div>
              </div>
              <div class="boxitem" style="height: 120px;margin-left: 20px;">
                <div class="line">
                  <span class="iconfont icon-Dram" style="color: white; font-size: 24px; margin-right: 5px;"></span>
                  <span class="des">内存</span>
                  <span class="des" style="margin-left: 10px;">通道：</span>
                  <span>{{  bg_sensor_data['memory']['channel'] }}</span>
                  <span class="des" style="margin-left: 10px;">频率：</span>
                  <span>{{  bg_sensor_data['memory']['clock'] }}</span>
                  <span class="des" style="margin-left: 10px;">大小：</span>
                  <span>{{  (bg_sensor_data['memory']['size']/1024).toFixed(0)+'GB' }}</span>
                  <span class="des" style="margin-left: 10px;">时序：</span>
                  <span>{{ bg_sensor_data['memory']['tcas']}}-{{bg_sensor_data['memory']['trcd']}}-{{bg_sensor_data['memory']['trp']}}-{{bg_sensor_data['memory']['tras'] }}</span>
                </div>
                <div class="ddr5_tempInfo" style="display: flex;" v-if="phaseList[4].mem_temp['#1'].length>0">
                    <div style="margin-left: 20px;margin-top: 10px;"><span class="des" >最高温度：</span> <span :style="getSpanStyle(MaxNum(phaseList[4].mem_temp['#1']).toFixed(0))">{{MaxNum(phaseList[4].mem_temp['#1']).toFixed(0)}}°C</span></div>
                    <div style="margin-left: 20px;margin-top: 10px;"><span class="des">平均温度： </span> <span  :style="getSpanStyle(AVGNum(phaseList[4].mem_temp['#1']).toFixed(0))">{{AVGNum(phaseList[4].mem_temp['#1']).toFixed(0)}}°C</span></div>
                </div>
                 <div class="ddr5_tempInfo" style="display: flex;" v-if="phaseList[4].mem_temp['#1'].length == 0">
                    <span class="des" style="margin-left: 20px;margin-top: 10px;">无温度传感器</span>
                </div>

              </div>
            </div>

            <div class="stable_box">
            <div class="cpu_stable" style="margin-top: 10px;" v-show ="realdata['model'][1].choosen">
              <span style="margin-left: 20px;color: #409EFF;font-size: 16px;margin-top: 10px;">稳定性测试状况</span>
              <div class="line">
                  <span class="iconfont icon-CPU" style="color: white; font-size: 24px; margin-right: 5px;"></span>
                  <span>{{ bg_sensor_data['cpu']['name'] }}</span>
              </div>
              <div class="line">
                 <div><span class="des">单核TextRender</span><span :style="{color:pass[0]['TextRender']=='PASS'?'#35D57D':'#F14343'}">{{pass[0]['TextRender']}}</span></div>
                 <div><span class="des">单核FFpeg</span><span :style="{color:pass[0]['FFpeg']=='PASS'?'#35D57D':'#F14343'}">{{pass[0]['FFpeg']}}</span></div>
                 <div><span class="des">单核PDFRender</span><span :style="{color:pass[0]['PDFRender']=='PASS'?'#35D57D':'#F14343'}">{{pass[0]['PDFRender']}}</span></div>
                 <div><span class="des">单核TNbody</span><span :style="{color:pass[0]['TNbody']=='PASS'?'#35D57D':'#F14343'}">{{pass[0]['TNbody']}}</span></div>
                 <div><span class="des">单核7z</span><span :style="{color:pass[0]['7z']=='PASS'?'#35D57D':'#F14343'}" >{{pass[0]['7z']}}</span></div>
              </div>
              <div class="line">
                 <div><span class="des">多核TextRender</span><span :style="{color:pass[1]['TextRender']=='PASS'?'#35D57D':'#F14343'}">{{pass[1]['TextRender']}}</span></div>
                 <div><span class="des">多核FFpeg</span><span :style="{color:pass[1]['FFpeg']=='PASS'?'#35D57D':'#F14343'}">{{pass[1]['FFpeg']}}</span></div>
                 <div><span class="des">多核PDFRender</span><span :style="{color:pass[1]['PDFRender']=='PASS'?'#35D57D':'#F14343'}">{{pass[1]['PDFRender']}}</span></div>
                 <div><span class="des">多核TNbody</span><span :style="{color:pass[1]['TNbody']=='PASS'?'#35D57D':'#F14343'}">{{pass[1]['TNbody']}}</span></div>
                 <div><span class="des">多核7z</span><span :style="{color:pass[1]['7z']=='PASS'?'#35D57D':'#F14343'}">{{pass[1]['7z']}}</span></div>
              </div>
            </div>
             <div  v-show ="!realdata['model'][1].choosen" style="color:#666666;font-size: 20px;font-weight: 700;width: 100%;height: 40%;margin-top: 20px;display: flex;
            align-items: center;justify-content: center;">未进行CPU稳定测试</div>
               <div class="mem_stable"  style="margin-top: 10px;" v-show ="realdata['model'][2].choosen">             
              <div class="line">
                  <span class="iconfont icon-Dram" style="color: white; font-size: 24px; margin-right: 5px;"></span>
                  <span>内存</span><span style="margin-left: 10px;">{{ bg_sensor_data['memory']['name'] }}</span>
              </div>
              <div class="line">
                 <div><span class="des">SimpleTest 0 MB</span><span><span class="des">报错：</span><span :style="{color:mem_error['mem_st00_error']==0?'#35D57D':'#F14343'}">{{ mem_error['mem_st00_error'] }}次</span></span></div>
                 <div><span class="des">SimpleTest 1 MB</span><span><span class="des">报错：</span><span :style="{color:mem_error['mem_st01_error']==0?'#35D57D':'#F14343'}">{{ mem_error['mem_st01_error'] }}次</span></span></div>
                 <div><span class="des">SimpleTest 2 MB</span><span><span class="des">报错：</span><span :style="{color:mem_error['mem_st02_error']==0?'#35D57D':'#F14343'}">{{ mem_error['mem_st02_error'] }}次</span></span></div>
                 <div><span class="des">SimpleTest 4 MB</span><span><span class="des">报错：</span><span :style="{color:mem_error['mem_st04_error']==0?'#35D57D':'#F14343'}">{{ mem_error['mem_st04_error'] }}次</span></span></div>
                 <div><span class="des">SimpleTest 8 MB</span><span><span class="des">报错：</span><span :style="{color:mem_error['mem_st08_error']==0?'#35D57D':'#F14343'}">{{ mem_error['mem_st08_error'] }}次</span></span></div>
              </div>
              <div class="line">
                 <div><span class="des">SimpleTest 16 MB</span><span><span class="des">报错：</span><span :style="{color:mem_error['mem_st16_error']==0?'#35D57D':'#F14343'}">{{ mem_error['mem_st16_error'] }}次</span></span></div>
                 <div><span class="des">SimpleTest 32 MB</span><span><span class="des">报错：</span><span :style="{color:mem_error['mem_st32_error']==0?'#35D57D':'#F14343'}">{{ mem_error['mem_st32_error'] }}次</span></span></div>
                 <div><span class="des">SimpleTest 64 MB</span><span><span class="des">报错：</span><span :style="{color:mem_error['mem_st64_error']==0?'#35D57D':'#F14343'}">{{ mem_error['mem_st64_error'] }}次</span></span></div>
                 <div><span class="des"> RefreshTable</span><span><span class="des">报错：</span><span :style="{color:mem_error['mem_create_error']==0?'#35D57D':'#F14343'}">{{ mem_error['mem_create_error'] }}次</span></span></div>
              </div>
            
              </div>
            <div v-show ="!realdata['model'][2].choosen" style="color:#666666;font-size: 20px;font-weight: 700;width: 100%;height: 40%;margin-top: 20px;display: flex;
            align-items: center;justify-content: center;">未进行内存稳定测试</div>
            </div>
            <div class="limit_box" v-show="phaseList[4]['limitcount']>0">
                 <div style="color: #F14343;margin-left: 20px;margin-top: 8px;margin-bottom: 10px;">温度传感器事件报告（与散热器和机箱风道有关）</div>
                 <div style="color:#F14343;"><span style="color: #666666;margin-left: 20px;margin-top: 8px;">总报告次数：</span>{{ phaseList[4]['limitcount'] }}次</div>
                 <div class="content">
                  <div class="limit-item" v-for="(key,index) in keyName">
                  <span style="margin-right: 10px;">{{ key }}</span>
                  触发：<span>{{ counts[key] }}次</span>
                 </div>
                 </div>
           </div>
             <div class="limit_box" v-show="phaseList[4]['limitcount'] == 0">
                 <div style="color: #35D57D;margin-left: 20px;margin-top: 8px;">
                  未生成温度传感器事件报告
                </div>
                 
           </div>
         </div>
         <div class="content_3" v-show="TabList[1].choosen">
             
         </div>

    </div>
    </div>

</template>



<style lang="scss" scoped>
.pressureResult{
    overflow: hidden;
    box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.3);
    user-select: none;
    color: white;
    width: 1280px;
    height: 1250px;
    margin-top: 5px;
    margin-left: 5px;
    border-radius: 6px;
    background-color: #262731;
    .Nav{
        width: 1280px;
        height: 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #2B2C37;
        .GPPInfo{
          display: flex;
          align-items: center;
          img{
            width: 14px;
            height: 14px;
            margin: 0 10px;
          }
          .slogan{
            font-size: 14px;
            color: white;
            margin: 0 5px;
          }
        }
      }
    .ResultBox
    {
        display: flex;
        flex-direction: column;
        height:1210px;
        width: 100%;
        background-color: #262731;
        .baseInfo{
            width: 100%;
            display: flex;
            justify-content: space-between;
        }
        .Tab{
            font-size: 12px;
            .item{
                height: 28px;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 22px;
                color:#999999;
                background-color:#2B2C37;
                padding: 0 10px;
                white-space: nowrap;
                cursor: pointer;
            }
            .choosen{
                background-color: #4183DC;
                color: white;
            }
        }

        //
        .title{
            margin-left: 20px;
            font-family: Source Han Sans;
            font-size: 14px;
            font-weight: bold;
            line-height: normal;
            letter-spacing: 0em;

            font-variation-settings: "opsz" auto;
            font-feature-settings: "kern" on;
            color: #409EFF;

            text-shadow: 0px 6px 10px rgba(64, 158, 255, 0.6);
        }

        .simple{
            display: flex;
            .simple_box{
                width: 400px;
                height: 120px;
                border-radius: 6px;
                background-color:#2B2C37;
                margin-left: 20px;
                .line{
                    display: flex;
                    align-items: center;
                    margin-left: 20px;
                }
            }
        }
        .des{
            color: #999999;
        }
        .left20{
            margin-left: 20px;
        }
        .left10{
            margin-left:10px
        }
        .complex{
            font-size: 12px;
            width: 1240px;
            height: 185px;
            background-color:#2B2C37;
            border-radius: 6px;
            margin-left: 20px;
            margin-top: 10px;
            display: flex;
            flex-direction: column;
            .base{
                display: flex;
                align-items: center;
                margin-left: 20px;
            }
            .middleBox{
                display: flex;

                .item{
                position: relative;
                  margin-left: 20px;
                  width: 220px;
                  height: 110px;
                  border: 1px solid #4A4A58;
                  background-color:transparent;
                  display: flex;
                  .vertical{
                    position: absolute;
                    width: 1px;
                    height: 64px;
                    background-color:#4A4A58;
                    top: 20px;
                    left: 110px;
                  }
                 .left{
                    width: 109px;
                    .val{
                        width: 100%;
                        text-align: center;
                        font-size: 18px;
                        color: #F7B12A;
                    }
                 }
                 .right{
                    display: flex;
                    flex-direction: column;
                    height: 100%;
                    justify-content: space-between;
                 }
                }
            }
            .noneTest{
              font-size: 20px;
              font-weight: bold;
              color:#666666;
              margin-left:20px
            }
        }
    }
    .content_2
    {
      .total_Info{
            margin-top: 10px;
            width: 1240px;
            height: 220px;
            background-color: #22232E;
            margin-left: 20px;
            background-color:#2B2C37;
      }
      .box{
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        .boxitem{
          width: 615px;
          background-color:#2B2C37;
          border-radius: 6px;
          margin-left: 10px;
          margin-top:10px;
          display: flex;
          flex-direction: column;
          font-size: 12px;
          color: white;
          .cure{
            width: 150px;
          }
          .line{
            display: flex;
            width: 100%;
            margin-left: 10px;
            margin-top: 10px;
            align-items: center;
            // justify-content: space-between;
          }
        }
      }
      .stable_box{
        font-size: 12px;
        width: 1240px;
        height:250px;
        border-radius: 6px;
        background-color:#2B2C37;
        margin-top: 10px;
        margin-left: 20px;
        display: flex;
        flex-direction: column;
        .line{
          width: 100%;
          display: flex;
          align-items: center;
          margin-top: 10px;
          margin-left: 20px;
          div{
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-right: 10px;
            width:215px;

          }
        }
      }

      .limit_box{
        font-size:12px;
        width: 1240px;
        height:200px;
        border-radius: 6px;
        background-color:#2B2C37;
        margin-top: 10px;
        margin-left: 20px;
        padding: 10px 0 0 0;
        .content{
          display: flex;
          width: 100%;
          flex-wrap: wrap;
          .limit-item{
            height:20px;
            border-radius: 5px;
            padding: 0 10px;
            background-color: #F14343;
            color: white;
            margin-left: 20px;
            margin-top: 10px;
          }
        }
      }
    }
}

.des{
  color: #666666;
}

//测试详情
.selectInfo{
  display: flex;align-items: center;
  width: 100%;
  justify-content: space-between;
}
.select_left{
   display: flex;align-items: center;
  margin-left: 20px;
}
.select_right{
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
}
.tinyItem
{
   display: flex;align-items: center;
   flex-wrap: nowrap;
   margin-right: 10px;
}
.matrix{
  margin-right: 5px;
  display: flex;align-items: center;
  width: 11px;
  height: 11px;
  flex-wrap: wrap;
  div{
    width: 5px;
    height: 5px;
    cursor: pointer;
  }
  div:hover{
    opacity: 0.8;
  }
}
</style>
<style lang="scss">
.select_left{
  .el-select-dropdown__item.is-hovering {
        background-color: rgb(256,256,256,0.3);
        color: white;
    }
    .el-select-dropdown__list {
        background: #313242;
    }
    .el-select-dropdown__item {
        color: white;
    }
    .el-select-dropdown__item.is-selected {
        color: white;
    }
    .el-popper__arrow:before,.el-popper__arrow:after {
        background: #313242 !important;
        border-color: #313242 !important;
    }
    .el-select__wrapper{
      background-color:#22232E !important;
      box-shadow: none;
      border: 1px solid white;
    }
}

.select_right{
  .el-select__placeholder{
    display: none;
  }
  .el-select-dropdown__item.is-hovering {
        background-color: rgb(256,256,256,0.3);
        color: white;
    }
    .el-select-dropdown__list {
        background: #313242;
    }
    .el-select-dropdown__item {
        color: white;
    }
    .el-select-dropdown__item.is-selected {
        color: white;
    }
    .el-popper__arrow:before,.el-popper__arrow:after {
        background: #313242 !important;
        border-color: #313242 !important;
    }

    .el-select__wrapper{
      background-color:#22232E !important;
      line-height: 22px;
      box-shadow: none;
      border: 1px solid #999999;
      min-height: 22px;
      height: 22px;
    }
}
.el-select__popper.el-popper{
  background-color:#22232E !important;
}
.select-family{
  max-height:300px; /* 设置最大高度 */
  overflow-y: auto; /* 超出高度时显示滚动条 */
}



.select-family::-webkit-scrollbar {
    width: 5px;
    transition: 0.25s;
}

.select-family::-webkit-scrollbar-thumb {
    background: #71738C;
    border-radius: 3px;
}

.select-family::-webkit-scrollbar-thumb:hover {
    background-color: #71738C;
}
.select-family::-webkit-scrollbar-track {
    background-color: #2D2E39;
    width: 2px;
}
</style>
