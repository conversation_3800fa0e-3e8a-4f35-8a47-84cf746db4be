const en = {
  "update": {
    "Game": "Game",
    "PP": "PP",
    "upgradingInProgress": "Updating",
    "theModuleIsBeingUpdated": "Updating module",
    "dataIsBeingUpdated": "Updating data...",
    "checkingUpdate": "Checking for updates",
    "checkingUpgrade": "Checking for updates",
    "loadingProgramComponent": "Loading program components...",
    "loadingHotkeyModules": "Loading hotkey component",
    "loadingGPPModules": "Loading GamePP components",
    "loadingBlackWhiteList": "Loading black and white list",
    "loadingGameSetting": "Loading game configuration parameters...",
    "loadingUserAbout": "Loading user authentication data",
    "loadingGameBenchmark": "Loading game benchmark",
    "loadingHardwareInfo": "Loading hardware information component",
    "loadingDBModules": "Loading database module...",
    "loadingIGCModules": "Loading IGC module",
    "loadingFTPModules": "Loading FTP Support Module",
    "loadingDialogModules": "Loading dialog box module",
    "loadingDataStatisticsModules": "Loading statistics module",
    "loadingSysModules": "Loading system components",
    "loadingGameOptimization": "Loading game optimization",
    "loadingGameAcceleration": "Loading game acceleration",
    "loadingScreenshot": "Loading recording screenshot",
    "loadingVideoComponent": "Loading video compression component",
    "loadingFileFix": "Loading File Repair",
    "loadingGameAI": "Loading game AI quality",
    "loadingNVAPIModules": "Loading NVAPI module",
    "loadingAMDADLModules": "Loading AMDADL module",
    "loadingModules": "Loading module"
  },
  "messages": {
    "append": "Add",
    "confirm": "Confirm",
    "cancel": "Cancel",
    "default": "Default",
    "quickSelect": "Quick Selection",
    "onoffingame": "Enable/Disable In-Game Monitoring:",
    "changeKey": "Click to change keyboard shortcut",
    "clear": "Clear",
    "hotkeyOccupied": "Hotkey is already in use, please set a new one!",
    "minimize": "Minimize",
    "exit": "Exit",
    "export": "Export",
    "import": "Import",
    "screenshot": "Screenshot",
    "showHideWindow": "Show/Hide Window",
    "ingameControlPanel": "In-game Control Panel",
    "openOrCloseGameInSettings": "Toggle In-Game Settings Panel",
    "openOrCloseGameInSettings2": "Press this shortcut key to enable",
    "openOrCloseGameInSettings3": "Enable/Disable In-game Monitoring",
    "openOrCloseGameInSettings4": "Enable/Disable Game Filter",
    "startManualRecord": "Start/Stop Manual Statistics Recording",
    "performanceStatisticsMark": "Performance Statistics Marker",
    "EnableAIfilter": "AI Filter requires pressing this shortcut key to activate",
    "Start_stop": "Start/Stop Manual Statistics Recording",
    "pressureTest": "Stress Test",
    "moduleNotInstalled": "Function module not installed",
    "installingPressureTest": "Installing pressure test module...",
    "importFailed": "Import Failed",
    "gamepp": "GamePP"
  },
  "home": {
    "homeTitle": "Home",
    "hardwareInfo": "Hardware Info",
    "functionIntroduction": "Features",
    "fixedToNav": "Pin to Navigation Bar",
    "cancelFixedToNav": "Unpin from Navigation Bar",
    "hardwareInfoLoading": "Loading hardware information...",
    "performanceStatistics": "Performance Stats",
    "updateNow": "Update Now",
    "recentRun": "Recent Activity",
    "resolution": "Resolution:",
    "duration": "Duration:",
    "gameFilter": "Game Filter",
    "gameFilterHasAccompany": "Game Filter is now active",
    "gameFilterHasAccompany2": "Users play games such as Cyberpunk, APEX, and Hogwarts Legacy",
    "currentList": "Monitored Items in Current List",
    "moreFunction": "Benchmark, stress testing, desktop monitoring, and more features are under development.",
    "newVersion": "New version available!",
    "discoverUpdate": "Update Found!",
    "downloading": "Downloading",
    "retry": "Retry",
    "erhaAI": "2HaAI",
    "recordingmodule": "This feature depends on the recording module",
    "superPower": "Ultra Mode",
    "autoRecord": "Automatically record in-game kill moments and easily save highlight reels",
    "externalDevice": "Dynamic Peripheral Lighting",
    "linkage": "Trigger kill scenes in games and display them through connected peripherals",
    "AI": "AI Performance Test",
    "test": "Test AI models with GPU and view the GPU's AI performance score",
    "supportedGames": "Supported Games",
    "games": "Naraka: Bladepoint, Delta Operation, Wild Overland, League of Legends, PUBG, Valorant, The Finals, APEX Legends, COD20, etc.",
    "videoRecording": "Video Recording",
    "videoRecording2": "OBS-based video recording function, supports adjusting video bitrate and frame rate (FPS) to meet different quality and smoothness requirements; also supports \"Instant Replay\", press the shortcut key to save highlights at any time!",
    "addOne": "Get for Free",
    "gamePlatform": "Gaming Platform",
    "goShop": "Go to Store Page",
    "receiveDeadline": "Post-action Claim Deadline",
    "2Ai": "2 Ha AI",
    "questionDesc": "Problem Description",
    "inputYourQuestion": "Please enter your suggestions or comments here。",
    "uploadLimit": "Upload up to 3 local images in JPG/PNG/BMP format",
    "email": "Email",
    "contactWay": "Contact Information",
    "qqNumber": "QQ Number (Optional)",
    "submit": "Submit"
  },
  "hardwareInfo": {
    "hardwareOverview": "Hardware Overview",
    "copyAllHardwareInfo": "Copy all hardware information",
    "processor": "Processor",
    "coreCount": "Cores:",
    "threadCount": "Thread Count:",
    "currentFrequency": "Current Frequency:",
    "currentVoltage": "Current Voltage:",
    "copy": "Copy",
    "releaseDate": "Release Date",
    "codeName": "Code Name",
    "thermalDesignPower": "Thermal Design Power",
    "maxTemperature": "Maximum Temperature",
    "graphicsCard": "Graphics Card",
    "brand": "Brand:",
    "streamProcessors": "Stream Processor:",
    "Videomemory": "VRAM:",
    "busSpeed": "Bus Speed",
    "driverInfo": "Driver Information",
    "driverInstallDate": "Driver Installation Date",
    "hardwareID": "Hardware ID",
    "motherboard": "Motherboard",
    "chipGroup": "Chipset:",
    "BIOSDate": "BIOS Date",
    "BIOSVersion": "BIOS Version",
    "PCIESlots": "PCIe Slot",
    "PCIEVersion": "Supported PCIe Version",
    "memory": "Memory",
    "memoryBarCount": "Quantity:",
    "totalSize": "Size:",
    "channelCount": "Channel:",
    "Specificmodel": "Specific model",
    "Pellet": "Particle Manufacturer",
    "memoryBarEquivalentFrequency": "Effective Memory Frequency:",
    "hardDisk": "Hard Drive",
    "hardDiskCount": "Number of Hard Drives:",
    "actualCapacity": "Actual Capacity",
    "type": "Type",
    "powerOnTime": "Power-on Time",
    "powerOnCount": "Power Cycles",
    "SSDRemainingLife": "SSD Remaining Life",
    "partitionInfo": "Partition Info",
    "hardDiskController": "Hard Disk Controller",
    "driverNumber": "Drive Number",
    "display": "Display",
    "refreshRate": "Refresh Rate:",
    "screenSize": "Screen Size:",
    "inches": "Inch",
    "productionDate": "Production Date",
    "supportRefreshRate": "Support Refresh Rate",
    "screenLongAndShort": "Screen Dimensions",
    "systemInfo": "System Information",
    "version": "Version",
    "systemInstallDate": "System Installation Date",
    "systemBootTime": "Current Boot Time",
    "systemRunTime": "Runtime",
    "Poccupied": "P Usage",
    "Eoccupied": "E Occupied",
    "occupied": "Occupied",
    "temperature": "Temperature",
    "Pfrequency": "P Frequency",
    "Efrequency": "E-Frequency",
    "thermalPower": "Thermal Power",
    "frequency": "Frequency",
    "current": "Current",
    "noData": "No data",
    "loadHwinfo_SDK": "Failed to load Hwinfo_SDK.dll, cannot read hardware/sensor data",
    "loadHwinfo_SDK_reason": "Possible causes of this issue:",
    "reason": "Reason",
    "BlockIntercept": "Blocked by antivirus software, e.g.: 2345 Antivirus Software (2345 Active Defense Process, McAfee Active Defense Process)",
    "solution": "Solution:",
    "solution1": "After closing and uninstalling related processes, restart GamePP",
    "solution2": "After disconnecting associated devices, restart GamePP",
    "RestartGamePP": "Disconnect the controller, wait for device response, then restart GamePP",
    "HWINFOcannotrun": "Hwinfo cannot run properly",
    "downloadHWINFO": "Download Hwinfo",
    "openHWINFO": "After launching Hwinfo, can clicking RUN start the program normally?",
    "hardwareDriverProblem": "Hardware driver issues",
    "checkHardwareManager": "Check the hardware manager to ensure motherboard and graphics card drivers are properly installed",
    "systemProblem": "System issues, for example: Using activation tools like Baofeng or Xiaoma may cause drivers to fail to load, and Windows 7 system patches cannot be automatically installed.",
    "reinstallSystem": "Reinstall the system to activate,WIN7 download and install: ********* update patch",
    "Windows7": "Windows 7: Install SHA-256 Patch, Windows 10: Activate Using Digital Certificate, Windows 11 Preview Version: Disable Memory Integrity",
    "ViolenceActivator": "If you have used brute-force activation tools such as Xiaoma, please repair or reinstall the system",
    "MultipleGraphicsCardDrivers": "Different brand graphics card drivers are installed on the computer, such as AMD and Nvidia drivers installed simultaneously",
    "UninstallUnused": "Restart the computer after uninstalling unnecessary graphics drivers",
    "OfficialQgroup": "None of the above reasons apply. Please join our official QQ group: 908287288 (Group 5) for resolution.",
    "ExportHardwareData": "Export Hardware Data",
    "D3D": "D3D Usage",
    "Total": "Total Usage",
    "VRAM": "VRAM Usage",
    "VRAMFrequency": "VRAM Frequency",
    "SensorData": "Sensor Data",
    "CannotGetSensorData": "Failed to retrieve sensor data",
    "LoadingHardwareInfo": "Loading hardware information...",
    "ScanTime": "Last Scanned:",
    "Rescan": "Rescan",
    "Screenshot": "Screenshot",
    "configCopyed": "Configuration information has been copied to the clipboard.",
    "LegalRisks": "Potential legal risks detected",
    "brandLegalRisks": "Brand display may involve potential legal risks",
    "professionalVersion": "Professional Edition",
    "professionalWorkstationVersion": "Professional Workstation Edition",
    "familyEdition": "Home Edition",
    "educationEdition": "Education Edition",
    "enterpriseEdition": "Enterprise Edition",
    "flagshipEdition": "Ultimate Edition",
    "familyPremiumEdition": "Family Premium",
    "familyStandardEdition": "Home Standard Edition",
    "primaryVersion": "Basic Version",
    "bit": "bit",
    "tempWall": "Temperature Wall",
    "error": "Error",
    "screenshotSuccess": "Screenshot saved successfully"
  },
  "psc": {
    "processCoreAssign": "Process Core Assignment",
    "CoreAssign": "Core Allocation:",
    "groupName": "Group Name:",
    "notGameProcess": "Non-game Processes",
    "unNamedProcess": "Unnamed Group",
    "Group2": "Group",
    "selectTheCore": "Select Core",
    "controls": "Operation",
    "tips": "Prompt",
    "search": "Search",
    "shiftOut": "Eject",
    "ppValue": "PP Value",
    "ppDesc": "PP value reflects historical hardware resource consumption. Higher values indicate greater hardware resource usage.",
    "littletips": "Tip: Hold and drag the process to the left-side group",
    "warning1": "Selecting core threads across groups may affect performance. It is recommended to use cores from the same group.",
    "warning2": "Are you sure you want to leave this group name empty?",
    "warning3": "The core allocation effect will be invalidated after deletion. Are you sure you want to delete this group?",
    "allprocess": "All Processes",
    "createAGroup": "Create group",
    "delGroup": "Delete Group",
    "Group": "Group",
    "editGroup": "Edit Group",
    "groupinfo": "Group Information",
    "moveOutGrouping": "Remove from group",
    "createANewGroup": "Create New Group",
    "unallocatedCore": "Unassigned Core",
    "inactiveProcess": "Inactive Process",
    "importGroupingScheme": "Import Grouping Profile",
    "derivedPacketScheme": "Export Group Configuration",
    "addNowProcess": "Add current running process",
    "displaySystemProcess": "Show system processes",
    "max64": "Maximum selection is 64 threads",
    "processName": "Process Name",
    "chooseCurProcess": "Select current process",
    "selectNoProcess": "No process has been selected",
    "coreCount": "Cores",
    "threadCount": "Threads",
    "process": "Process",
    "plzInputProcessName": "Enter the process name to manually add",
    "has_allocation": "Processes with Thread Allocation Schemes",
    "not_made": "You have not assigned any processes to cores",
    "startUse": "Enable Optimization",
    "stopUse": "Disable Optimization",
    "threadAllocation": "Thread Allocation",
    "configProcess": "Process Configuration",
    "selectThread": "Select Thread",
    "hyperthreadingState": "Hyper-Threading Status",
    "open": "Enabled",
    "notYetUnlocked": "Disabled",
    "nonhyperthreading": "Non Hyper-Threading",
    "intervalSelection": "Interval Selection",
    "invertSelection": "Invert Selection",
    "description": "Lock game processes to designated CPU cores for operation, intelligently isolating background program interference. Effectively increases FPS ceiling and stabilizes gaming FPS! Reduces sudden game lag and frame rate drops, unleashing full performance of multi-core processors to ensure consistently high frame rates throughout gameplay!"
  },
  "InGameMonitor": {
    "onoffingame": "Enable/Disable In-Game Monitoring:",
    "InGameMonitor": "In-game Monitoring",
    "CustomMode": "Custom Mode",
    "Developing": "Under Development...",
    "NewMonitor": "Add Monitoring Item",
    "Data": "Parameters",
    "Des": "Note",
    "Function": "Function",
    "Editor": "Edit",
    "Top": "Pin to Top",
    "Delete": "Delete",
    "Use": "Use",
    "DragToSet": "After invoking the panel, you can drag to set",
    "MonitorItem": "Monitoring Item",
    "addMonitorItem": "Add Monitoring Item",
    "hide": "Hide",
    "show": "Display",
    "generalstyle": "General Settings",
    "restoredefault": "Restore Defaults",
    "arrangement": "Layout",
    "horizontal": "Horizontal",
    "vertical": "Vertical",
    "monitorposition": "Monitoring Location",
    "canquickselectposition": "Quickly select a location on the left map",
    "curposition": "Current Location:",
    "background": "Background",
    "backgroundcolor": "Background Color:",
    "font": "Font",
    "fontStyle": "Font Style",
    "fontsize": "Font Size:",
    "fontcolor": "Font Color:",
    "style": "Style:",
    "style2": "Style",
    "performance": "Performance",
    "refreshTime": "Refresh Time:",
    "goGeneralSetting": "Go to General Settings",
    "selectMonitorItem": "Select Monitoring Item",
    "selectedSensor": "Selected sensor:",
    "showTitle": "Display Title",
    "hideTitle": "Hide Title",
    "showStyle": "Display Mode:",
    "remarkSize": "Note Size:",
    "remarkColor": "Note Color:",
    "parameterSize": "Parameter size:",
    "parameterColor": "Parameter Color:",
    "lineChart": "Line Chart",
    "lineColor": "Line Color:",
    "lineThickness": "Line Thickness:",
    "areaHeight": "Region Height:",
    "sort": "Sort",
    "displacement": "Displacement:",
    "up": "Move Up",
    "down": "Move Down",
    "bold": "Bold",
    "stroke": "Outline",
    "text": "Text",
    "textLine": "Text + Line Chart",
    "custom": "Custom",
    "upperLeft": "Top left",
    "upper": "Middle Upper",
    "upperRight": "Top right",
    "Left": "Left Center",
    "middle": "Center",
    "Right": "Right Center",
    "lowerLeft": "Bottom Left",
    "lower": "Medium-Low",
    "lowerRight": "Lower right",
    "notSupport": "Peripheral devices do not support display/hide via mouse click",
    "notSupportRate": "Return rate cannot be toggled by clicking",
    "notFindSensor": "Sensor not found. Click to modify.",
    "monitoring": "Monitoring",
    "condition": "Condition",
    "bigger": "Greater than",
    "smaller": "Less than",
    "biggerThan": "Exceeds Threshold",
    "biggerThanthreshold": "Greater than threshold percentage",
    "smallerThan": "Less than threshold",
    "smallerThanthreshold": "Less than threshold percentage",
    "biggerPercent": "Percentage Decrease of Current Value",
    "smallerPercent": "Current Value Increase Percentage",
    "replay": "Instant Replay Function",
    "screenshot": "Screenshot Feature",
    "text1": "When sensor value",
    "text2": ", and in",
    "text3": "Wait Time",
    "text4": "Trigger immediately if no higher value appears within seconds",
    "text5": "After each replay trigger, update the threshold to the value at the time of triggering to reduce frequent activations",
    "text6": "Display the current threshold used to trigger replay",
    "text7": "Display sensor values",
    "text8": "Exceed initial threshold",
    "text9": "Below Initial Threshold",
    "text10": "Initial Threshold Count",
    "initThreshold": "Initial Threshold",
    "curThreshold": "Current Threshold:",
    "curThreshold2": "Current Threshold",
    "resetCurThreshold": "Reset Current Threshold",
    "action": "Activate Feature",
    "times": "times",
    "percentage": "Percentage",
    "uninstallobs": "Recording module not downloaded",
    "install": "Download",
    "performanceAndAudioMode": "Performance and Audio Compatibility Mode",
    "isSaving": "Saving",
    "video_replay": "Instant Replay",
    "saved": "Saved",
    "loadQualitysScheme": "Load Graphics Preset",
    "notSet": "Not Set",
    "mirrorEnable": "Filter is active",
    "canBeTurnedOff": "Return",
    "mirrorClosed": "Game filter is disabled",
    "closed": "Closed",
    "openMirror": "Enable Filter",
    "wonderfulScenes": "Highlights",
    "VulkanModeHaveProblem": "The Vulkan mode has compatibility issues",
    "suggestDxMode": "It is recommended to switch to Dx mode",
    "functionNotSupported": "Feature not supported",
    "NotSupported": "Not Supported",
    "gppManualRecording": "GamePP, manual recording",
    "perfRecordsHaveBeenSaved": "Performance data saved",
    "redoClickF8": "Press F8 again to continue recording.",
    "startIngameMonitor": "Activating in-game monitoring function",
    "inGameMarkSuccess": "Marking in the game was successful",
    "recordingFailed": "Recording failed",
    "recordingHasNotDownload": "Recording function not downloaded",
    "hotkeyDetected": "Function hotkey conflict detected",
    "plzEditIt": "Please make changes within the software and then use it",
    "onePercentLowFrame": "1% Low Frame",
    "pointOnePercentLowFrame": "0.1% Low Frames",
    "frameGenerationTime": "Frame Generation Time",
    "curTime": "Current Time",
    "runTime": "Runtime duration",
    "cpuTemp": "CPU Temperature",
    "cpuUsage": "CPU Usage",
    "cpuFreq": "CPU Frequency",
    "cpuPower": "CPU Thermal Design Power",
    "gpuTemp": "GPU Temperature",
    "gpuUsage": "GPU Usage",
    "gpuPower": "GPU Thermal Power",
    "gpuFreq": "GPU Clock Speed",
    "memUsage": "Memory Usage"
  },
  "LoginArea": {
    "login": "Log In",
    "loginOut": "Log Out",
    "vipExpire": "Expired",
    "remaining": "Remaining",
    "day": "Sky",
    "openVip": "Activate Membership",
    "vipPrivileges": "Member Privileges",
    "rechargeRenewal": "Recharge & Renew",
    "Exclusivefilter": "Filter",
    "configCloudSync": "Configure Cloud Sync",
    "comingSoon": "Coming Soon"
  },
  "GameMirror": {
    "filterStatus": "Filter Status",
    "filterPlan": "Filter Preset",
    "filterShortcut": "Filter Shortcuts",
    "openCloseFilter": "Enable/Disable Filter:",
    "effectDemo": "Effect Demonstration",
    "demoConfig": "Demo Configuration",
    "AiFilter": "AI filter effects are subject to in-game effects",
    "AiFilterFAQ": "Common Issues with AI Filters",
    "gamePPAiFilter": "GamePP AI Filter",
    "gamePPAiFilterVip": "GamePP VIP-exclusive AI filter, dynamically adjusts filter parameters based on gaming scenarios to optimize visual effects and enhance gameplay experience.",
    "AiMingliangTips": "AI Brightness: Recommended for use when the game screen is too dark.",
    "AiBrightTips": "AI Vibrant: Recommended for use when game visuals appear too dark.",
    "AiDarkTips": "AI Dimming: Recommended for use when game visuals are overly vibrant.",
    "AiBalanceTips": "AI Balance: Suitable for most game scenarios.",
    "AiTips": "Tips: The AI filter needs to be used by pressing the shortcut key in the game.",
    "AiFilterUse": "Please use in-game",
    "AiFilterAdjust": "Hotkey Adjustment for AI Filter",
    "Bright": "Vivid",
    "Soft": "Soft",
    "Highlight": "Highlight",
    "Film": "Movie",
    "Benq": "BenQ",
    "AntiGlare": "Anti-Glare",
    "HighSaturation": "High Saturation",
    "Brightness": "Vivid",
    "Day": "Day",
    "Night": "Night",
    "Nature": "Natural",
    "smooth": "Fine",
    "elegant": "Austere",
    "warm": "Warm Tone",
    "clear": "Clear",
    "sharp": "Sharpness",
    "vivid": "Dynamic",
    "beauty": "Vivid",
    "highDefinition": "HD",
    "AiMingliang": "AI Bright",
    "AiBright": "AI Vivid",
    "AiDark": "AI Dim",
    "AiBalance": "AI Balance",
    "BrightTips": "Vivid filter is suitable for casual, action or adventure games, enhancing color saturation to make game visuals more dynamic and engaging.",
    "liangTips": "Filters are recommended for use when game画面 is too dark.",
    "anTips": "The filter is recommended for use when the game screen is too dark.",
    "jianyiTips": "Filter is recommended for use when game visuals are too vibrant.",
    "shiTips": "The filter is suitable for most gaming scenarios.",
    "shi2Tips": "Filter is suitable for casual, action or adventure games, enhances color saturation to make game visuals more vivid and engaging.",
    "ruiTips": "Smooth filter colors and soft lighting effects are ideal for depicting dreamy, warm, or nostalgic scenes.",
    "qingTips": "Bright tone, high contrast, sharp details, suitable for vivid scenes with ample lighting.",
    "xianTips": "Higher contrast and brightness settings ensure clear details in dark scenes without distortion and comfortable viewing in bright scenes.",
    "dianTips": "Moderately enhance screen brightness and colors to achieve cinematic visual quality as much as possible",
    "benTips": "Reduces white light effects to prevent eye strain in pure white gaming environments",
    "fangTips": "Optimized for open-world and adventure games, enhances brightness and contrast for sharper visuals",
    "jiaoTips": "Suitable for role-playing and simulation games, balanced tones, enhanced visual realism",
    "jieTips": "Optimized for story-rich, emotionally nuanced games, enhancing details and softness to achieve more refined visuals",
    "jingTips": "Optimized for action and competitive games, enhances clarity and contrast for sharper visuals",
    "xiuTips": "Optimized for healing and casual games, enhances warm tones and softness, creating a more cozy atmosphere",
    "qihuanTips": "Suitable for scenes with rich fantasy elements and vibrant colors, enhancing color saturation to create a strong visual impact",
    "shengTips": "Enhance colors and details to highlight the vibrancy and realism of the scene,",
    "sheTips": "Optimized for FPS, puzzle, or adventure games, enhances details and contrast to improve the realism of the game world.",
    "she2Tips": "Suitable for shooting, racing, or fighting games, highlighting high-definition details and dynamic performance to enhance the intensity and visual effects of the gaming experience",
    "an2Tips": "Enhances scene clarity in dark environments, ideal for dark or night scenarios.",
    "wenTips": "Suitable for artistic, adventure, or casual games, creating soft color tones and lighting effects to add elegance and warmth to the scenes.",
    "jing2Tips": "Suitable for competitive, music rhythm or night city scenario games, highlighting vivid colors and lighting effects,",
    "jing3Tips": "Optimized for competitive, action, or fantasy genre games, enhances color contrast to make visuals more vibrant and dynamic.",
    "onlyVipCanUse": "This filter is only available to VIP users"
  },
  "GameRebound": {
    "noGame": "No game records",
    "noGameRecord": "No game records yet! Start a session now!",
    "gameDuration": "Today's Game Duration:",
    "gameElectricity": "Daily Power Usage",
    "degree": "Degree",
    "gameCo2": "CO₂ Emissions Today:",
    "gram": "Key",
    "manualRecord": "Manual Record",
    "recordDuration": "Recording duration:",
    "details": "Details",
    "average": "Average",
    "minimum": "Minimum",
    "maximum": "Maximum",
    "occupancyRate": "Usage",
    "voltage": "Voltage",
    "powerConsumption": "Power Consumption",
    "start": "Start:",
    "end": "End",
    "Gametime": "Game Duration:",
    "Compactdata": "Data Optimization",
    "FullData": "Complete Data",
    "PerformanceAnalysis": "Performance Analysis",
    "PerformanceAnalysis2": "Event Report",
    "HardwareStatus": "Hardware Status",
    "totalPower": "Total Power Consumption",
    "TotalEmissions": "Total Emission",
    "PSS": "Note: Data below the chart represents average values",
    "FrameGenerationTime": "Frame Generation Time",
    "GameResolution": "Game Resolution",
    "FrameGenerationTimeTips": "This data point is exceptionally high and has been excluded from the statistics",
    "FrameGenerationTimeTips2": "This data point is abnormally low and therefore not included in the statistics",
    "noData": "None",
    "ProcessorOccupancy": "CPU Usage",
    "ProcessorFrequency": "Processor Frequency",
    "ProcessorTemperature": "Processor Temperature",
    "ProcessorHeatPower": "Processor TDP",
    "GraphicsCardOccupancy": "GPU Usage D3D",
    "GraphicsCardOccupancyTotal": "Total GPU Usage",
    "GraphicsCardFrequency": "GPU Frequency",
    "GraphicsCardTemperature": "GPU Temperature",
    "GraphicsCardCoreTemperature": "GPU Core Hotspot Temperature",
    "GraphicsCardHeatPower": "GPU Thermal Power",
    "GraphicsCardMemoryTemperature": "GPU Memory Temperature",
    "MemoryOccupancy": "Memory Usage",
    "MemoryTemperature": "Memory Temperature",
    "MemoryPageFaults": "Memory Paging Interrupt",
    "Duration": "Duration",
    "Time": "Time",
    "StartStatistics": "Start Statistics",
    "Mark": "Tag",
    "EndStatistics": "End Statistics",
    "LineChart": "Line Chart",
    "AddPointInGame_m1": "Press within the game",
    "AddPointInGame_m2": "Marking point can be added",
    "LeftMouse": "Left-click to toggle show/hide, right-click to change color",
    "DeleteThisLine": "Delete this polyline",
    "AddCurve": "Add Curve",
    "AllCurvesAreHidden": "All curves are hidden",
    "ThereAreSamplingData": "Total sample data:",
    "Items": "Entry",
    "StatisticsData": "Statistics",
    "electricity": "Power Usage",
    "carbonEmission": "Carbon Emissions",
    "carbonEmissionTips": "Carbon Dioxide Emissions (kg) = Electricity Consumption (kWh) × 0.785",
    "D3D": "D3D Usage:",
    "TOTAL": "Total Usage:",
    "Process": "Process:",
    "L3Cache": "L3 Cache:",
    "OriginalFrequency": "Original Frequency:",
    "MaximumBoostFrequency": "Max Turbo Boost:",
    "DriverVersion": "Driver Version:",
    "GraphicsCardMemoryBrand": "VRAM Brand:",
    "Bitwidth": "Bus Width",
    "System": "System:",
    "Screen": "Screen",
    "Interface": "Interface:",
    "Channel": "Channel:",
    "Timing": "Sequence:",
    "Capacity": "Capacity:",
    "Generation": "Algebra",
    "AddPoint_m1": "Press in Game",
    "AddPoint_m2": "Add marker point",
    "Hidden": "Hidden",
    "Totalsampling": "Total Sampling Data:",
    "edition": "Driver Version:",
    "MainHardDisk": "Primary Hard Drive",
    "SetAsStartTime": "Set as Start Time",
    "SetAsEndTime": "Set as end time",
    "WindowWillBe": "Performance Statistics Window will be located in",
    "After": "Close after",
    "NoLongerPopUpThisGame": "This game will no longer pop up",
    "HideTemperatureReason": "Hide temperature reason",
    "HideTemperatureReason2": "Hide Event Report",
    "HideOtherReason": "Hide Other Reasons",
    "CPUanalysis": "CPU Performance Analysis",
    "TemperatureCause": "Temperature Cause",
    "tempSensorEvent": "Temperature Sensor Event",
    "NoTemperatureLimitation": "No temperature-induced CPU performance throttling detected. Your cooling system is fully capable of meeting the requirements of this game.",
    "NoTemperatureLimitation2": "No temperature sensor events detected, your cooling system can perfectly handle this game's requirements.",
    "performanceis": "In selected",
    "Inside": "Internal,",
    "TheStatisticsTimeOf": "The statistical period meets the relevant trigger conditions. The most frequent trigger reason is",
    "limited": "Percentage of Total Time Due to Temperature-Induced Performance Limitations",
    "SpecificReasons": "Specific reasons and their proportion in temperature-related causes:",
    "OptimizationSuggestion": "Optimization Suggestions:",
    "CPUtemperature": "CPU temperature is overheating. Please check/improve the CPU cooling environment.",
    "CPUoverheat": "CPU overheating due to motherboard power supply. Please check motherboard-related settings or improve cooling environment.",
    "OtherReasons": "Other reasons",
    "NoPowerSupplyLimitation": "CPU performance is not limited by power supply/consumption. Your BIOS power consumption settings are sufficient for this game's requirements.",
    "PowerSupplyLimitation": "Due to power supply/power consumption limitations",
    "SpecificReasonsInOtherReasons": "Specific reason and its proportion among other reasons:",
    "PleaseCheckTheMainboard": "Check motherboard power delivery status or adjust BIOS power settings to resolve CPU performance limitations caused by other factors",
    "CPUcoretemperature": "Core temperature has reached Tj,Max and been limited",
    "CPUCriticalTemperature": "CPU Temperature Critical",
    "CPUCircuitTemperature": "CPU Package/Ring Bus limited by reaching Tj,Max",
    "CPUCircuitCriticalTemperature": "CPU Package/Ring Bus has reached critical temperature",
    "CPUtemperatureoverheating": "CPU overheating detected, which will trigger automatic frequency reduction to lower temperatures and prevent hardware failures",
    "CPUoverheatingtriggered": "The CPU will activate its cooling mechanism and adjust voltage/frequency to reduce power consumption and temperature",
    "CPUPowerSupplyOverheating": "CPU is limited due to severe overheating of motherboard power supply",
    "CPUPowerSupplyLimitation": "CPU is limited due to motherboard power supply overheating",
    "CPUMaximumPowerLimitation": "Core is restricted by maximum power consumption limit",
    "CPUCircuitPowerLimitation": "CPU Package/Ring Bus has reached power limit",
    "CPUElectricalDesignLimitation": "Trigger electrical design restrictions (including ICCmax current wall, PL4 peak power wall, SVID voltage limitation, etc.)",
    "CPULongTermPowerLimitation": "CPU Long-Term Power Limit Reached",
    "CPULongTermPowerinstantaneous": "CPU Instant Power Consumption Reached Limit",
    "CPUPowerLimitation": "CPU Turbo Frequency Degradation Mechanism, typically restricted by BIOS or specific software",
    "CPUPowerWallLimitation": "CPU Power Limit",
    "CPUcurrentwalllimit": "CPU Current Wall Limit",
    "AiAgent": "GamePP Agent (AI Agent)",
    "AgentDesc": "Return to homepage",
    "fnBeta": "This feature is currently in the invited testing phase. Your GamePP account has not yet received testing access.",
    "getAIReport": "Obtain AI Report",
    "waitingAi": "Waiting for report generation to complete",
    "no15mins": "Game duration less than 15 minutes, no valid AI report available",
    "timeout": "Server request timeout",
    "agentId": "Agent ID:",
    "reDo": "Regenerate Report",
    "text2": "GamePP Agent: Performance Statistics AI Analysis Report, the following content is AI-generated and for reference only.",
    "noCurData": "No current data available",
    "dataScreening": "Data Filtering",
    "dataScreeningDescription": "This feature is designed to exclude data statistics from non-effective game time periods such as map loading or lobby idling. 0 indicates no exclusion.",
    "excessivelyHighParameter": "Excessive Parameters",
    "tooLowParameter": "Low Parameters",
    "theMaximumValueIs": "Maximum is",
    "theMinimumValueIs": "Minimum value is",
    "exclude": "Exclude",
    "dataStatisticsAtThatTime": "Data statistics at this time",
    "itHasBeenGenerated": "Generation complete,",
    "clickToView": "Click to View"
  },
  "SelectSensor": {
    "DefaultSensor": "Default Sensor",
    "Change": "Change",
    "FanSpeed": "Fan Speed",
    "MainGraphicsCard": "Primary GPU",
    "SetAsMainGraphicsCard": "Set as primary GPU",
    "GPUTemperature": "GPU Temperature",
    "GPUHeatPower": "GPU Thermal Power",
    "GPUTemperatureD3D": "GPU D3D Usage",
    "GPUTemperatureTOTAL": "GPU Total Usage",
    "GPUTemperatureCore": "GPU Core Hotspot Temperature",
    "MotherboardTemperature": "Motherboard Temperature",
    "MyAttention": "My Favorites",
    "All": "All",
    "Unit": "Unit:",
    "NoAttention": "Unfollowed Sensors",
    "AttentionSensor": "Monitored Sensors (Beta)",
    "GoToAttention": "Go to Focus",
    "CancelAttention": "Unfollow",
    "noThisSensor": "No Sensor",
    "deviceAbout": "Peripherals Related",
    "deviceBattery": "Peripheral Battery",
    "testFunction": "Test Feature",
    "mouseEventRate": "Polling Rate",
    "relatedWithinTheGame": "Game Related",
    "winAbout": "System",
    "trackDevicesBattery": "Track peripheral battery level",
    "ingameRealtimeMouseRate": "Current mouse polling rate used in real-time during gaming",
    "notfoundDevice": "No supported device found",
    "deviceBatteryNeedMythcool": "List of supported battery display devices: (requires Myth.Cool)",
    "vkm1mouse": "Valkyrie M1 Mouse",
    "vkm2mouse": "Valkyrie M2 Mouse",
    "vk99keyboard": "Valkyrie 99 Magnetic Axis Keyboard",
    "logitechProWireless": "Logitech Pro Wireless",
    "logitechProXSUPERLIGHT": "Logitech PRO X SUPERLIGHT",
    "logitechProXSUPERLIGHT2": "Logitech PRO",
    "logitechPro2LIGHTSPEED": "Logitech PRO 2 LIGHTSPEED",
    "steelSeriesArctisGameBuds": "SteelSeries Arctis GameBuds",
    "steelSeriesArctis7PPlus": "SteelSeries Arctis 7P Plus",
    "razerV3": "Razer Valkyrie V3 Professional",
    "razerV2": "Razer Viper V2 Professional Edition",
    "wireless": "Wireless",
    "logitechNeedGhub": "When Logitech cannot retrieve the device model, you need to download GHUB",
    "chargingInProgress": "Charging",
    "inHibernation": "Sleeping"
  },
  "video": {
    "videoRecord": "Video Recording",
    "recordVideo": "Recorded Video",
    "scheme": "Profile",
    "suggestScheme": "Recommended Plan",
    "text1": "With this configuration, 1 minute video size is approximately",
    "text2": "This feature uses additional system resources",
    "low": "Low",
    "mid": "Home",
    "high": "High",
    "1080p": "Native",
    "RecordingFPS": "Record FPS",
    "bitRate": "Video Bitrate",
    "videoResolution": "Video Resolution",
    "startStopRecord": "Start/Stop Recording",
    "instantReplay": "Instant Replay",
    "instantReplayTime": "Instant Playback Duration",
    "showIngame": "Launch In-Game Control Panel",
    "CaptureMode": "Capture Method",
    "gameWindow": "Game Window",
    "desktopWindow": "Desktop Window",
    "fileSavePath": "File Storage Path",
    "selectVideoSavePath": "Select Recording Save Path",
    "diskFreeSpace": "Hard Disk Free Space:",
    "edit": "Modify",
    "open": "Open",
    "displayMouse": "Show Mouse Cursor",
    "recordMicrophone": "Record Microphone",
    "gameGraphics": "Original Game Display"
  },
  "Setting": {
    "common": "General",
    "personal": "Personalization",
    "messageNotification": "Notifications",
    "sensorReading": "Sensor Readings",
    "OLEDscreen": "OLED Burn-in Protection",
    "performanceStatistics": "Performance Statistics",
    "shortcut": "Shortcut",
    "ingameSetting": "Save Game Settings",
    "other": "Other",
    "otherSettings": "Other Settings",
    "GeneralSetting": "General Settings",
    "softwareVersion": "Software Version",
    "checkForUpdates": "Check for updates",
    "updateNow": "Update Now",
    "currentVersion": "Current Version",
    "latestVersion": "Latest version",
    "isLatestVersion": "The current version is already the latest version.",
    "functionModuleUpdate": "Functional Module Update",
    "alwaysUpdateModules": "Keep all installed functional modules up to date",
    "lang": "Language",
    "bootstrap": "Auto-start",
    "powerOn_m1": "Startup",
    "powerOn_m2": "Auto-start after seconds",
    "defaultDelay": "Default: 40 seconds",
    "followSystemScale": "Follow system scaling",
    "privacySettings": "Privacy Settings",
    "JoinGamePPPlan": "Join GamePP User Experience Improvement Program",
    "personalizedSetting": "Personalization",
    "restoreDefault": "Restore Defaults",
    "color": "Color",
    "picture": "Image",
    "video": "Video",
    "browse": "Browse",
    "clear": "Clear",
    "mp4VideoOrPNGImagesCanBeUploaded": "Upload MP4 videos or PNG images",
    "transparency": "Transparency",
    "backgroundColor": "Background Color",
    "textFont": "Main Text Font",
    "message": "Message",
    "enableInGameNotifications": "Enable in-game notifications",
    "messagePosition": "In-game display location",
    "leftTop": "Top left corner",
    "leftCenter": "Left Center",
    "leftBottom": "Lower Left Corner",
    "rightTop": "Top-right corner",
    "rightCenter": "Right Center",
    "rightBottom": "Bottom-right corner",
    "noticeContent": "Notification Content",
    "gameInjection": "Game Injection",
    "ingameShow": "Display In-game",
    "inGameMonitoring": "In-game Monitoring",
    "gameFilter": "Game Filter",
    "start": "Start",
    "endMarkStatistics": "End Marker Statistics",
    "readHwinfoFail": "HWINFO Failed to retrieve hardware information!",
    "dataSaveDesktop": "Data has been saved to the clipboard and desktop file",
    "TheSensorCacheCleared": "Sensor cache data cleared",
    "defaultSensor": "Default Sensor",
    "setSensor": "Select Sensor",
    "refreshTime": "Data Refresh Time",
    "recommend": "Recommend",
    "sensorMsg": "The shorter the time interval, the higher the performance consumption. Please select carefully.",
    "exportData": "Data Export",
    "exportHwData": "Export hardware information data",
    "sensorError": "Sensor Reading Abnormal",
    "clearCache": "Clear Cache",
    "littleTips": "Tip: Performance Report will be generated 2 minutes after game launch",
    "disableAutoShow": "Disable automatic pop-up of the performance statistics window",
    "AutoClosePopUpWindow_m1": "Auto-close performance window after:",
    "AutoClosePopUpWindow_m2": "seconds",
    "abnormalShutdownReport": "Abnormal Shutdown Report",
    "showWeaAndAddress": "Display weather and location information",
    "autoScreenShots": "Automatically capture game screen when marked",
    "keepRecent": "Number of recent records to keep:",
    "noLimit": "Unlimited",
    "enableInGameSettingsSaving": "Enable In-game Settings Save",
    "debugMode": "Debug Mode",
    "enableDisableDebugMode": "Enable/Disable Debug Mode", 
    "audioCompatibilityMode": "AUDIO COMPATIBILITY MODE",
    "quickClose": "Quick Close",
    "closeTheGameQuickly": "Quickly close game process",
    "cancel": "Cancel",
    "confirm": "Confirm",
    "MoveInterval_m1": "Desktop and in-game monitoring will slightly move after:",
    "MoveInterval_m2": "minutes",
    "text3": "After exiting the game, no performance report window will pop up, only historical records are retained",
    "text5": "A report will be automatically generated after an unexpected shutdown. Enabling this feature will consume additional system resources.",
    "text6": "Using shortcut functions may conflict with other game shortcuts, please set up carefully.",
    "text7": "Set the keyboard shortcut to \"None\", please use the Backspace key",
    "text8": "Retain filters, in-game monitoring, and other functional states during gameplay based on process names",
    "text9": "Enabling will continuously log runtime data; disabling clears log files (Recommended to keep off)",
    "text10": "After enabling, motherboard sensors will be inaccessible to resolve audio issues caused by GamePP",
    "text11": "Press Alt+F4 twice consecutively to quickly exit the current game",
    "text12": "Do you want to continue? This mode requires restarting GamePP.",
    "openMainUI": "Show App",
    "setting": "Settings",
    "feedback": "Feedback",
    "help": "Help",
    "sensorReadingSetting": "Sensor Reading Settings",
    "searchlanguage": "Search Language"
  },
  "GamePlusOne": {
    "year": "Year",
    "month": "Month",
    "day": "Day",
    "success": "Success",
    "fail": "Failed",
    "will": "Current",
    "missedGame": "Missed Games",
    "text1": "Amount, approx. ￥",
    "text2": "Total Games Claimed",
    "text3": "Version",
    "gamevalue": "Game Value",
    "gamevalue1": "Claim",
    "total": "Total Claimed",
    "text4": "games, total saved",
    "text6": "Product, Value",
    "Platformaccountmanagement": "Platform Account Management",
    "Missed1": "(Missed Claim)",
    "Received2": "(Successfully received)",
    "Receivedsoon2": "(Currently Available)",
    "Receivedsoon": "Available Now",
    "Missed": "Missed Collection",
    "Received": "Successfully Claimed",
    "Getaccount": "Claim Account",
    "Worth": "Value",
    "Auto": "Auto",
    "Manual": "Manual",
    "Pleasechoose": "Please select a game",
    "Receive": "Claim Now",
    "Selected": "Selected",
    "text5": "Games",
    "Automatic": "Auto-claiming...",
    "Collecting": "Claiming...",
    "ReceiveTimes": "Monthly Claim Count",
    "Thefirst": "#",
    "Week": "Week",
    "weekstotal": "Total 53 weeks",
    "Return": "Home",
    "Solutionto": "Account Binding Failure Solution",
    "accounts": "Bound Account Count",
    "Addaccount": "Add Account",
    "Clearcache": "Clear Cache",
    "Bindtime": "Binding Time",
    "Status": "Status",
    "Normal": "Normal",
    "Invalid": "Invalid",
    "text7": "games, total saved for you",
    "Yuan": "Yuan",
    "untie": "Unbind",
    "disable": "Deactivate",
    "enable": "Enable",
    "gamePlatform": "Gaming Platform",
    "goStorePage": "Go to Store Page",
    "receiveEnd": "After Deadline",
    "loginPlatformAccount": "Logged-in Platform Account",
    "waitReceive": "Pending Claim",
    "receiveSuccess": "Success",
    "accountInvalid": "Account Expired",
    "alreadyOwn": "Already Owned",
    "networkError": "Network Anomaly",
    "noGame": "No Game Core",
    "manualReceiveInterrupt": "Manual Acquisition Interruption",
    "receiving": "Claiming",
    "agree": "I agree to join the GamePP Free Claim Program.",
    "again": "Reclaim"
  },
  "shutdownTimer": {
    "timedShutdown": "Scheduled Shutdown",
    "currentTime": "Current Time:",
    "setCountdown": "Set Countdown",
    "shutdownInSeconds": "Shut down in X seconds",
    "shutdownIn": "Post-Shutdown",
    "goingToBe": "Will be",
    "executionPlan": "Execution Plan",
    "startTheClock": "Start Timer",
    "stopTheClock": "Cancel Plan",
    "isShuttingDown": "Executing scheduled shutdown plan:",
    "noplan": "No current shutdown plan",
    "hour": "Hour",
    "min": "Minute",
    "sec": "Second",
    "ms": "Millisecond",
    "year": "Year",
    "month": "Month",
    "day": "Day",
    "hours": "Hour"
  },
  "screenshotpage": {
    "screenshot": "Screenshot",
    "screenshotFormat": "Designed specifically for game screen capture, supports saving in JPG/PNG/BMP formats, allows quick capture of game screens, and ensures high-resolution lossless output",
    "Turnon": "Enable auto screenshot, every",
    "seconds": "Second",
    "takeScreenshot": "Perform an automatic screenshot",
    "screenshotSettings": "This setting is invalid when checked in-game",
    "saveGameFilterAndMonitoring": "Save 'Game Filter' and 'In-Game Monitoring' effects in screenshot",
    "disableScreenshotSound": "Turn off screenshot sound notification",
    "imageFormat": "Image Format",
    "recommended": "Recommend",
    "viewingdetails": "Retains image quality details, moderate size, suitable for viewing details",
    "saveSpace": "Adjustable image quality, minimal file size, space saving",
    "ultraQuality": "Ultra-clear, uncompressed visuals with large file size. Recommended for players prioritizing high-quality game saves.",
    "fileSavePath": "File Save Path",
    "hardDiskSpace": "Free Disk Space:",
    "minutes": "Minute"
  },
  "DesktopMonitoring": {
    "desktopMonitoring": "Desktop Monitoring",
    "SomeSensors": "We recommend some sensors for monitoring. You can delete or add them",
    "AddComponent": "Add New Component",
    "Type": "Type",
    "Remarks": "Note",
    "AssociatedSensor": "Link Sensor",
    "Operation": "Operation",
    "Return": "Back",
    "TimeSelection": "Time Selection:",
    "Format": "Format:",
    "Rule": "Rule：",
    "Coordinate": "Coordinates:",
    "CustomTextContent": "Custom Text Content:",
    "SystemTime": "System Time",
    "China": "China",
    "Britain": "United Kingdom",
    "America": "United States",
    "Russia": "Russia",
    "France": "France",
    "DateAndTime": "Date and Time",
    "Time": "Time",
    "Date": "Date",
    "Week": "Week",
    "DateAndTimeAndWeek": "Date+Time+Day of the Week",
    "TimeAndWeek": "Time+Day of the Week",
    "Hour12": "12-hour format",
    "Hour24": "24-Hour Format",
    "SelectSensor": "Select Sensor:",
    "AssociatedSensor1": "Link Sensor:",
    "SensorUnit": "Sensor Unit：",
    "Second": "Second:",
    "Corner": "Rounded Corners:",
    "BackgroundColor": "Background Color:",
    "ProgressColor": "Progress Color:",
    "Font": "Font：",
    "SelectFont": "Select Font",
    "FontSize": "Font Size:",
    "Color": "Color：",
    "Style": "Style:",
    "Bold": "Bold",
    "Italic": "Italic",
    "Shadow": "Shadow",
    "ShadowPosition": "Shadow Position：",
    "ShadowEffect": "Shadow Effects：",
    "Blur": "Blur",
    "ShadowColor": "Shadow Color：",
    "SelectFromLocalFiles": "Select from local files:",
    "UploadImageVideo": "Upload Image/Video",
    "UploadSVGFile": "Upload SVG file",
    "Width": "Width:",
    "Height": "High: ",
    "Effect": "Effect:",
    "Rotation": "Rotation:",
    "WhenTheSensorValue": "Sensor value is greater than",
    "conditions": "When the condition is not met (no rotation when sensor value is 0)",
    "Clockwise": "Clockwise",
    "Counterclockwise": "Counter-clockwise",
    "QuickRotation": "Quick Rotate",
    "SlowRotation": "Slow Rotation",
    "StopRotation": "Stop Rotation",
    "StrokeColor": "Outline Color：",
    "Path": "Path",
    "Color1": "Color",
    "ChangeColor": "Change color",
    "When": "When",
    "SensorValue": "Sensor value is greater than or equal to",
    "SensorValue1": "Sensor value less than or equal to",
    "SensorValue2": "Sensor value equals",
    "MonitoringSettings": "Monitoring Settings",
    "RestoreDefault": "Restore Defaults",
    "Monitor": "Monitor",
    "AreaSize": "Region Size",
    "Background": "Background",
    "ImageVideo": "Images/Videos",
    "PureColor": "Solid Color",
    "Select": "Select",
    "ImageVideoDisplayMode": "Image/Video Display Mode",
    "Transparency": "Transparency",
    "DisplayPosition": "Show Position",
    "Stretch": "Stretch",
    "Fill": "Fill",
    "Adapt": "Adjust",
    "SelectThePosition": "Click the grid cell to quickly select the location",
    "CurrentPosition": "Current Location:",
    "DragLock": "Drag Lock",
    "LockMonitoringPosition": "Lock monitor position (Cannot drag the monitor after locking)",
    "Unlockinterior": "Enable Internal Element Drag",
    "Font1": "Font",
    "GameSettings": "Game Settings",
    "CloseDesktopMonitor": "Automatically disable desktop monitoring when the game is running.",
    "OLED": "OLED Burn-in Protection",
    "Display": "Show",
    "PleaseEnterContent": "Please enter content",
    "NextStep": "Next",
    "Add": "Add",
    "StylesForYou": "We have recommended some monitoring styles for you. You can select and apply them. More styles will be added in the future.",
    "EditPlan": "Edit Profile",
    "MonitoringStylePlan": "Monitoring Style Scheme",
    "AddDesktopMonitoring": "Add Desktop Monitoring",
    "TextLabel": "Text Label",
    "ImageVideo1": "Images, Videos",
    "SensorGraphics": "Sensor Graphics",
    "SensorData": "Sensor Data",
    "CustomText": "Custom Text",
    "DateTime": "Date and Time",
    "Image": "Image",
    "Video": "Video",
    "SVG": "SVG",
    "ProgressBar": "Progress Bar",
    "Graphics": "Graphics",
    "UploadImage": "Upload Image",
    "UploadVideo": "Upload Video",
    "RealTimeMonitoring": "Real-time monitoring of CPU/GPU temperatures and usage, drag-and-drop layout customization, personalized style settings – help you master performance and desktop aesthetics",
    "Chart": "Chart",
    "Zigzagcolor": "Line Color (Start)",
    "Zigzagcolor1": "Line Color (End)",
    "Zigzagcolor2": "Line Chart Area Color (Start)",
    "Zigzagcolor3": "Line Chart Area Color (End)"
  }
}
//messageEnd 
 export default en 