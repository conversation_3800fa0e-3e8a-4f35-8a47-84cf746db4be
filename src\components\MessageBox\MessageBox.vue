<template>
    <div v-if="visible" class="message-box-overlay" :class="extraOverlayClass" :style="extraOverlayStyle">
        <div class="message-box">
            <div class="message-box-header">
                <img src="../../assets/icon/Public/ic_opengamepp.png" alt="">
                <span>{{ title }}</span>

                <span class="iconfont icon-Close" @click="handleCancel"></span>
            </div>
            <div class="message-box-content">
                <span class="iconfont icon-error"></span>
                <p>{{ message }}</p>
            </div>
            <div class="message-box-footer">
                <div class="cancel"><el-button color="#3E4050" @click="handleCancel">{{cancelText}}</el-button></div>
                <div class="confirm"><el-button color="#336AB5" @click="handleConfirm">{{confirmText}}</el-button></div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import {ref} from 'vue';

export default {
    name: 'MessageBox',
    props: {
        title: {
            type: String,
            default: '提示'
        },
        cancelText:{
            type: String,
            default: '取消'
        },
        confirmText:{
            type: String,
            default: '确认'
        },
        message: {
            type: String,
            required: true
        },
        onConfirm: {
            type: Function,
            default: () => {
            }
        },
        onCancel: {
            type: Function,
            default: () => {
            }
        },
        extraOverlayClass: {
            type: String,
            default: '',
        },
        overlayPosition: {
            type: Object,
            default: {
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
            }
        }
    },
    setup(props) {
        const visible = ref(true);

        const handleConfirm = () => {
            props.onConfirm();
            visible.value = false;
        };

        const handleCancel = () => {
            props.onCancel();
            visible.value = false;
        };

        return {
            visible,
            handleConfirm,
            handleCancel
        };
    }
};
</script>

<style lang="scss" scoped>
.message-box-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.message-box {
  background: #2B2C37;
  width: 440px;
  border-radius: 4px;
  padding: 11px 13px;
}

.message-box-header {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 50px;
  color: #ffffff;
  display: flex;
  align-items: center;
  line-height: 1;

  img {
    width: 14px;
    height: 14px;
    margin-right: 10px;
  }

  .icon-Close {
    margin-left: auto;
    color: #999999;
    cursor: pointer;
  }
}

.message-box-content {
  color: #fff;
  margin-bottom: 40px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 10px;
  line-height: 1;
  font-size: 12px;

  .icon-error {
    color: #F19C34;
    font-size: 24px;
  }
}

.message-box-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  :deep(.el-button) {
    width: 80px;
    height: 30px;
  }

  .cancel {
    :deep(.el-button:hover) {
      background-color: #494C66;
      border-color: #494C66;
    }
  }
  .confirm {
    :deep(.el-button:hover) {
      background-color: #4A8FEE;
      border-color: #4A8FEE;
    }
  }
}
</style>
