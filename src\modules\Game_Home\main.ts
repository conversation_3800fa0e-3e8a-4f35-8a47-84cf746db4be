import { createApp } from 'vue'
//pinia
import { createPinia } from 'pinia'
//element plus
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import Home from './Home.vue'
//路由
import router from './router'
//pinia persist
import './reset.css'
import  './Game_Home_RTL.scss'
//国际化
import i18n from '../../assets/lang'

import ECharts from 'vue-echarts'
import "echarts";
import '../../assets/iconfont/iconfont.css'
import '../../assets/iconfont/iconfont.js'
import '../Game_FreeGame/utils/font.css'
// 挂载ECharts

const pinia = createPinia()
const app = createApp(Home)
app.component('ECharts',ECharts)

app.use(pinia)
app.use(router)
// app.use(ElementPlus, {locale: zhCn,})
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}
app.use(i18n)
app.mount('#app')
