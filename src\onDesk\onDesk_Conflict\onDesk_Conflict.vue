<script setup lang="ts">
import { onMounted, ref, watch, computed} from "vue";
import { gamepp } from "gamepp"



onMounted(async ()=>{
 
})

const GPP_OpenURL = (url:string) =>
{
  try {
    gamepp.shell.openExternal.sync(url);
  } catch (error) {
    window.open(url)
  }
}

const quitGamepp = async() =>
{
   await gamepp.exit.promise();
   await gamepp.lansevice.stop.promise()
}
</script>

<template>
  <div class="container">
    <img src="../../components/assets/img/gamepp.png" style="width: 101px;height: 24px;margin-right: 5px;margin-top: 82px;">
        <span style="cursor: pointer;letter-spacing: 2px;font-size: 13px;margin-top: 3px;" @click="GPP_OpenURL('https://gamepp.com')">GamePP.com</span>
        <span style="margin-top: 50px;">您正在运行其他版本的游戏加加，请退出其他版本后重试。</span>
        <div class="confirm" @click="quitGamepp">确定</div>
    </div>
</template>

<style lang="scss" scoped>
.container
    {
        user-select: none;
        width: 540px;
        height: 320px;
        background: linear-gradient(30deg, #1D1D24 0%, #22232E 100%);
        color: #FFF;
        display: flex;
        align-items: center;
        flex-direction: column;
        border-radius: 4px;
        margin: 10px 0 0 10px;
        box-shadow:-1px 2px 5px 0 rgba(16,16,16,1);
        position: relative;
        -webkit-app-region: drag;
    
    }
   .confirm
    {
      width:120px;
      height: 40px;
      background-color:#3579D5;
      border-radius: 2px;
      color: #FFFFFF;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      position: absolute;
      bottom: 20px;
      -webkit-app-region: no-drag;
    }
    .confirm:hover
    {
      background-color: #0FB3FF;
    }

</style>
