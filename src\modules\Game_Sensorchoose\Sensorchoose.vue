<script setup lang="ts">
import {onBeforeMount, reactive, ref, watch,onUnmounted} from "vue";
import {ProcessSensorUnit, SensorAddAverageData} from '@/uitls/sensor';
import RightTopIcons from "@/components/mainCom/RightTopIcons.vue";
import { useI18n } from "vue-i18n";
import { debounce } from 'lodash-es';
import { useLanguage } from '@/uitls/useLanguage';
useLanguage()
// @ts-ignore
const gamepp = window.gamepp as any
const I18n = useI18n();
const { t } = useI18n();
let SensorInfoShow = ref(true);
let MyAttentionShow = ref(false)
let sensor_collected_list = ref<Array<any>>([])
let filter_sensors = ref<Array<string>>(['All'])
let select_unit = ref('All')
let SensorList = ref<Array<any>>([])
let hotData = ref({})
let userSelectedSensor = reactive<any>({
  OutIndex:-1,
  InnerIndex:-1,
  name      : '',
  unit      : '',
  outKey    : '',
  inKey     : '',
})
const isMythCoolRunning = ref(false);
const zoomValue = ref(1)
watch(zoomValue,(newValue)=>{
  document.body.style.zoom = newValue
})
const is_ingame_set = ref(false)
onBeforeMount(() => {
    if (window.localStorage.getItem('isIngameSet') == '1') {
        is_ingame_set.value = true
    }
    window.localStorage.removeItem('isIngameSet')
  SensorManageFilter(200)
  ProcessSensor(true)
  const bc = new BroadcastChannel('bg_sensor_data');
  bc.onmessage = () => {
    ProcessSensor(false)
  }
  initZoom()
})

async function initZoom() {
    try {
      const zoomWithSystem = gamepp.setting.getInteger.sync(313)
      if (zoomWithSystem === 1) {
        // 设置body zoom
        // document.body.style.zoom = zoomValue.value
        zoomValue.value = await gamepp.display.getScaleFromWindowInMonitor.promise();
        gamepp.webapp.windows.setMinimumSize.sync('Sensorchoose', Math.floor(812 * zoomValue.value),Math.floor(672 * zoomValue.value))
        gamepp.webapp.windows.resize.sync('Sensorchoose',Math.floor(812 * zoomValue.value),Math.floor(672 * zoomValue.value))
      }
    }catch (e) {
      zoomValue.value = 1
    }

    try {
      gamepp.display.onDisplayMetricsChanged.addEventListener( async (scaleFactor:number)=>{
        const zoomWithSystem = gamepp.setting.getInteger.sync(313)
        if (zoomWithSystem === 1) {
          console.log('display',scaleFactor)
          zoomValue.value = scaleFactor
          try{
            gamepp.webapp.windows.setMinimumSize.sync('Sensorchoose', Math.floor(812 * zoomValue.value),Math.floor(672 * zoomValue.value))
            gamepp.webapp.windows.resize.sync('Sensorchoose',Math.floor(812 * zoomValue.value),Math.floor(672 * zoomValue.value))
          }catch (e) {
            console.log('gamepp.webapp.windows.resize.sync(gamepp_config)',e)
          }
        }
      })
    }catch (e) {
      console.log(e)

    }
  }

async function ProcessSensor (init:boolean) {
  let SensorInfoStr = null
  try {SensorInfoStr = await gamepp.hardware.getSensorInfo.promise()} catch {
    SensorInfoStr = "{}"
  }
  const SensorInfo = JSON.parse(SensorInfoStr);
  const booleanArr = ['Critical Temperature', 'Thermal Throttling', 'Power Limit Exceeded', 'IA: ', 'GT: ', 'RING:', 'Drive Failure', 'Drive Warning', 'Chassis Intrusion', 'Performance Limit']
  SensorList.value = []
  const SensorInfoKeys = Object.keys(SensorInfo)

  let SensorInfoTidyUp = SensorAddAverageData(SensorInfo, SensorInfoKeys)
  let sensor_collected:any = window.localStorage.getItem('collected_sensor_list')
  if (sensor_collected) {
    sensor_collected = JSON.parse(sensor_collected)
  }else{
    sensor_collected = []
  }
  sensor_collected_list.value = sensor_collected
  // console.log(SensorInfoTidyUp)

  // 循环标题列表

  const regex = /^(.*?)(:|\s|$)/ // 匹配第一个冒号或空格之前的内容

  for (let i = 0; i < SensorInfoKeys.length; i++) {
    const SensorInfoKey = SensorInfoKeys[i]
    let Datas = SensorInfoTidyUp[SensorInfoKey]
    const SensorObj:any = {}
    let SensorListText = ''
    SensorObj.name = SensorInfoKey
    const match = SensorInfoKey.match(regex)
    SensorObj.type = match ? match[1].trim() : ''
    SensorObj.Sensoritem = []
    // if (!Datas) continue
    if (!Datas) { Datas = [{ Null: '', name: '' }] }
    for (let j = 0; j < Datas.length; j++) {
      const Key = Object.keys(Datas[j])[0]
      const Data = Datas[j][Key]
      const SensoritemObj:any = {}
      const ProcessKey:any = ProcessSensorUnit(Key, Data.type)
      if (!SensorListText.includes(ProcessKey.UnitText)) {
        SensorListText += ProcessKey.UnitText
      }
      if (booleanArr.find(item => Key.includes(item))) {
        let booleanValue = 'No'
        if (Number(Data.value)) { booleanValue = 'Yes' }
        SensoritemObj.value = booleanValue
      } else {
        if (Data.value) { SensoritemObj.value = Number(Data.value).toFixed(ProcessKey.ToFixed) } else { SensoritemObj.value = 'Null' }
      }
      SensoritemObj.name = Key
      SensoritemObj.unit = ProcessKey.DataUnit
      SensoritemObj.choosen = false
      SensoritemObj.collect = false
      if (sensor_collected_list.value.length > 0) {
        const findIndex = sensor_collected_list.value.findIndex((item:any) => {
          return item.mainName === SensorInfoKey && item.name === Key
        })
        if (findIndex !== -1) SensoritemObj.collect = true

      }
      SensorObj.Sensoritem.push(SensoritemObj)
      // console.log(SensoritemObj,'SensoritemObj')
    }
    SensorObj.UnitText = SensorListText
    // SensorList.value.push(SensorObj)
    SensorList.value.push({ ...SensorObj });
  }
  window.localStorage.setItem('SensorList', JSON.stringify(SensorList.value))
  if (is_ingame_set.value) { // 游戏内相关
    let VKDevice = []
    try {
      const str = gamepp.hardware.getMythCoolUSBSensorInfo.sync()
      isMythCoolRunning.value = gamepp.utils.isMythCoolRunning.sync()

      if (str && isMythCoolRunning.value) {
        VKDevice = JSON.parse(str)
      }
    }catch (e) {
      console.log(e)
    }
    const arr:Array<any> = []
    const logitechs = [13,14,15,16]
    const logitech_names: Record<typeof logitechs[number], string> = {
      13: 'PRO WIRELESS',
      14: 'PRO X SUPERLIGHT',
      15: 'PRO X SUPERLIGHT 2',
      16: 'PRO 2 LIGHTSPEED'
    }
      console.log(VKDevice)
      let haveType18 = false
      VKDevice.forEach((item:any) => {
        if (item.type === 18) {
          haveType18 = true
        }
      })
    function handle24GBlueToothDevice(item:any):Array<any> {
        const res:Array<any> = []
        if (item.type === 19 && haveType18) return [] // 有18就不显示19
          const leftV:any = {name:"", value:0, unit:"", collect: false, choosen: false}
          const rightV:any = {name:"", value:0, unit:"", collect: false, choosen: false}
          const caseV:any = {name:"", value:0, unit:"", collect: false, choosen: false}
          const _o = {leftV, rightV, caseV}
          const _fn = (key:'leftV'|'rightV'|'caseV',device:any)=>{
              _o[key].name = device.name
              if (device.battery) {
                  _o[key].value = device.battery
                  _o[key].unit = '%'
                  res.push(_o[key])
              }
          }
          if (item.hasOwnProperty('devices') && Array.isArray(item.devices)) {
              for (let i = 0; i < item.devices.length; i++) {
                  const device = item.devices[i]
                  if (device.name?.includes('Left')) {
                      _fn("leftV", device)
                  }else if (device.name?.includes('Right')) {
                      _fn("rightV", device)
                  }else{
                      _fn("caseV", device)
                  }
              }
          }
          return res
      }
    VKDevice.forEach((item:any) => {
      if (item.type === 18 || item.type === 19) {
          const r = handle24GBlueToothDevice(item)
          arr.push(...r);
          return;
      }
      let name:string = ''
      let battery:string = ''
      let unit:string = ''
      if (item.type === 6 || item.type === 7) {
        name = 'Valkyrie M1 mouse'
      }
      if (item.type === 8 || item.type === 9) {
        name = 'Valkyrie 99 Magnetic Axis'
      }
      if (item.type === 21 || item.type === 22) {
        name = 'Valkyrie MAG 75 MAX'
      }
      if (logitechs.includes(item.type)) {
        name = logitech_names[item.type];
      }
      if (item.hasOwnProperty('name')) {
        name = item.name
      }

      let isAllPing = true;
      // 逻辑的单独处理
      if (item.hasOwnProperty('devices') && Array.isArray(item.devices)) {

        isAllPing = item.devices.every(DEVICE=>{
            if (!DEVICE.hasOwnProperty('ping_status')) {
                return true;
            }
            return DEVICE['ping_status'] == 1;
        })

      }

      // 获取电量
      if (item.hasOwnProperty('charging')) {
        // charging 0为没充电 1为充电中 2为充满了
        if (item.charging === 0) {
          if (item.battery === 0) {// 充电状态和电量同时为0
            if ([6,7,8,9,21,22].includes(item.type)) {
              battery = t('SelectSensor.chargingInProgress')
            }else if (logitechs.includes(item.type)){ // 逻辑鼠标
              battery = t('SelectSensor.inHibernation')
            }else{
                battery = item.battery
                unit = '%'
            }
          }else{
            battery = item.battery
            unit = '%'
          }
        }else if (item.charging === 1) {
          if (item.battery !== 0) {
            battery = item.battery + '% '
          }
          battery += t('SelectSensor.chargingInProgress')
        }else{
          battery = item.battery
          unit = '%'
        }
      }else{
        battery = item.battery
        unit = '%'
      }

      const usbDevices = gamepp.queryUsbDevices.sync() as Array<any>
      const vid = item.vid.toString(16).toUpperCase()
      const pid = item.pid.toString(16).toUpperCase()
      const usbDevice = usbDevices.find(usbDevice => {
        return usbDevice.VendorID === vid && usbDevice.ProductID === pid
      })

      if (isAllPing && usbDevice) {
          arr.push({
              name,
              value:battery,
              unit,
              collect: false,
              choosen: false
          })
      }
    })
    SensorList.value.push({
      name: t('SelectSensor.deviceBattery') + '（Beta） ',
      UnitText: " 占用",
      type: "VKDevice",
      Sensoritem: arr,
      specialName: 'SelectSensor.trackDevicesBattery',
      proto:"dl"
    })
    checkUsbDevices()

    const ingame_arr = [
        {
            name:"FPS",
            value:"",
            unit:"",
            collect: false,
            choosen: false
        },
        {
            name:"FPS 1% Low",
            value:"",
            unit:"",
            collect: false,
            choosen: false
        },
        {
            name:"FPS 0.1% Low",
            value:"",
            unit:"",
            collect: false,
            choosen: false
        },
        {
            name:"Frame Time",
            value:"",
            unit:"",
            collect: false,
            choosen: false
        },
        {
            name:"Run Time",
            value:"",
            unit:"",
            collect: false,
            choosen: false
        }
    ]
    SensorList.value.push({
          name: t('SelectSensor.relatedWithinTheGame'),
          UnitText: "other",
          type: "inGame",
          Sensoritem: ingame_arr,
    })
      const windows_arr = [
          {
              name:"Current time",
              value:new Date().toLocaleTimeString(),
              unit:"",
              collect: false,
              choosen: false
          },
      ]
      SensorList.value.push({
          name: t('SelectSensor.winAbout'),
          UnitText: "other",
          type: "winAbout",
          Sensoritem: windows_arr,
      })
  }
  if (init) {
    await getSensorStatistics();
  }
}
const mouseEventRateDevices:any = {
    "31514026": "VK M1",
    "3151402D": "VK M1",
    "046DC539": "罗技 GPW1",
    "046DC547": "罗技 GPW2",
    "046DC54D": "罗技 GPW3",
    "046DC543": "罗技 GPW4",
    "153200A5": "雷蛇 Viper V2 PRO",
    "1532A5": "雷蛇 Viper V2 PRO",
    "153200A6": "雷蛇 Viper V2 PRO",
    "1532A6": "雷蛇 Viper V2 PRO",
    "153200B6": "雷蛇 DeathAdder V3 PRO",
    "1532B6": "雷蛇 DeathAdder V3 PRO",
    "153200B7": "雷蛇 DeathAdder V3 PRO",
    "1532B7": "雷蛇 DeathAdder V3 PRO",
    "374AA304": "Valkyrie VKM2 鼠标",
    "374AA305": "Valkyrie VKM2 鼠标",
    "374AA307": "Valkyrie VKM2Pro 鼠标",
    "374AA308": "Valkyrie VKM2Pro 鼠标",
}
function checkUsbDevices() {
    try{
        const usbDevices = gamepp.queryUsbDevices.sync() as Array<any>
        // console.log(usbDevices)
        const arr:Array<any> = []
        if (usbDevices && Array.isArray(usbDevices)) {
            for (const usbDevice of usbDevices) {
                const Key:string = usbDevice.VendorID+usbDevice.ProductID
                if (mouseEventRateDevices.hasOwnProperty(Key)) {
                    const name = '鼠标回报率'
                    // 有符合要求的pid vid
                    arr.push({
                        name:name,
                        value:"",
                        unit:"",
                        collect: false,
                        choosen: false,
                        desc:"mouseEventRate"
                    })
                    break;
                }
            }
        }
        SensorList.value.push({
            name: t('SelectSensor.mouseEventRate') + '（Beta） ',
            UnitText: " 占用",
            type: "VKDevice",
            Sensoritem: arr,
            specialName: 'SelectSensor.ingameRealtimeMouseRate',
            proto:"hbl"
        })
    }catch (e) {
        console.error('-----checkUsbDevicesErr-----',e)
    }
}
async function getSensorStatistics () {
  const Data = window.localStorage.getItem('sensorStatistics')
  if (Data) {
    let SensorStatistics = JSON.parse(Data)
    console.log('Local sensorStatistics 渲染', SensorStatistics)
    hotData.value = SensorStatistics.hot || SensorStatistics;
  }
}
function MyAttention (type:any) {
  if (type !== 'listener') {
    SensorInfoShow.value = false
    MyAttentionShow.value = true
  }
  let sensor_collected:any = window.localStorage.getItem('collected_sensor_list')
  if (sensor_collected) {
    sensor_collected = JSON.parse(sensor_collected)
  }else{
    sensor_collected = []
  }
  sensor_collected_list.value = sensor_collected
}

function collectSensor (flag:boolean,OutIndex:number, InnerIndex:number,mainName:string,name:string) {
  let sensor =
    {
      "mainName"  : mainName,
      "name"      : name,
      "OutIndex"  : OutIndex,
      "InnerIndex": InnerIndex
    }
  const collectedSensor = JSON.parse(window.localStorage.getItem('collected_sensor_list')!) || []
  if (flag) {
    collectedSensor.push(sensor)
    window.localStorage.setItem('collected_sensor_list', JSON.stringify(collectedSensor))
    sensor_collected_list.value = collectedSensor
  } else {
    // 取消关注
    const findIndex = collectedSensor.findIndex((item:any) => {
      return item.name === sensor.name && item.mainName === sensor.mainName
    })
    collectedSensor.splice(findIndex, 1)
    window.localStorage.setItem('collected_sensor_list', JSON.stringify(collectedSensor))
    sensor_collected_list.value = collectedSensor
  }
  ProcessSensor(false)
}
function escapeSelector(selector:any) {
  return selector.replace(/([ #;&,.+*~':"!^$[\]()=>|/@])/g, '\\$1');
}
function GPP_WindowClose(name: string) {

  gamepp.webapp.windows.close.promise(name)
}
function GPPSensorData(){
  SensorInfoShow.value = true
  MyAttentionShow.value = false
}
function filterSensors (type:string, type1?:string, type2?:string,type3?:string) {
  console.log(type, type1, type2, type3)
  GPPSensorData();
  filter_sensors.value = []
  if (type === 'All') {
    filter_sensors.value.push(type);
    return
  }
  if (type === 'MainboardName') {
    let hw_list_dataStr = window.localStorage.getItem('hw_list_data') as string
    let hw_list_data = JSON.parse(hw_list_dataStr)
    type = hw_list_data.mainboard_name[0]
    filter_sensors.value.push('Mainboard');
  }
  if (type === 'VKDevice') {
    select_unit.value = 'All'
  }
  filter_sensors.value.push(type);
  if (type1) filter_sensors.value.push(type1);
  if (type2) filter_sensors.value.push(type2);
  if (type3) filter_sensors.value.push(type3);
}
const sk = ['CPU', 'GPU', 'Memory', 'S.M.A.R.T.', 'System:', 'Windows', 'Drive', 'Network', 'DIMM','DDR5']
function isSensorShow(Outitem:any) {
  if (select_unit.value == 'All' || Outitem.UnitText.includes(select_unit.value)) {
    if (filter_sensors.value.includes('All')) {
      return true
    }else{
      if (filter_sensors.value.includes(Outitem.type)) {
        return true
      }else{
          // 主板
          if (Outitem.type === 'VKDevice' || Outitem.type === 'inGame' || Outitem.type === 'winAbout') return false
        return filter_sensors.value.includes('Mainboard') && !sk.includes(Outitem.type);
      }
    }
  }else {
    return false
  }
}
function isSensorItemShow(sensor:any) {
  let f = false
  if (select_unit.value !== 'All') {
    let unit = ''
    if (select_unit.value === '功耗') {
      unit = 'W'
    }else if (select_unit.value === '电压') {
      unit = 'V'
    }else if (select_unit.value === '转速') {
      unit = 'RPM'
    }else if (select_unit.value === '温度') {
      unit = '℃'
    }else if (select_unit.value === '占用') {
      unit = '%'
    }else if (select_unit.value === '频率') {
      unit = 'MHz'
    }
    sensor.unit.includes(unit) ? f=true : f=false;
  }else{
    f = true
  }
  return f
}
function GPPSelected(sensor:any,OutIndex:number,InnerIndex:number,outKey:any,item:any){
  console.log(sensor,OutIndex,InnerIndex,outKey)

  userSelectedSensor.OutIndex = OutIndex
  userSelectedSensor.InnerIndex = InnerIndex
  userSelectedSensor.name = sensor.name
  userSelectedSensor.unit = sensor.unit
  userSelectedSensor.outKey = outKey
  userSelectedSensor.inKey = sensor.name
  userSelectedSensor.all = item
  if (sensor.desc) {
      userSelectedSensor.desc = sensor.desc
  }else {
      userSelectedSensor['desc'] = ''
  }
}
async function Confirm () {
  const isIngameSet = is_ingame_set.value;
  const sensorValueId = window.localStorage.getItem('sensorValue');
  const SensorType = window.localStorage.getItem('SensorType');
  console.log(sensorValueId)
  if(SensorType === 'Change'){
    window.localStorage.removeItem('SensorType')
    const obj = {
      value: userSelectedSensor,
      action: 'Change'
    }
    gamepp.webapp.sendInternalAppEvent.promise('desktop_monitor_setting', JSON.parse(JSON.stringify(obj)));
    gamepp.webapp.sendInternalAppEvent.promise('new_component', JSON.parse(JSON.stringify(obj)));
    // gamepp.webapp.sendInternalAppEvent.promise('desktop_monitor_setting', JSON.parse(JSON.stringify({obj})))
    // gamepp.webapp.sendInternalAppEvent.promise('new_component', JSON.parse(JSON.stringify({
    //   obj
    // })))
  }
  if (isIngameSet) {
    window.localStorage.removeItem('isIngameSet')
    const obj = {
      value: userSelectedSensor,
      action: 'IngameMonitor'
    }
    gamepp.webapp.sendInternalAppEvent.promise('desktop', JSON.parse(JSON.stringify({
      obj
    })))
  }else if (sensorValueId === '1') {
    const savedSelectedId = localStorage.getItem('selectedId');
    if (savedSelectedId) {
      let savedData:any = JSON.parse(localStorage.getItem('selected_sensors')!) || {};
      let dataList:Array<any> = savedData['selectedId:' , savedSelectedId] || [];
      userSelectedSensor.customname = '';
      userSelectedSensor.fontFamily = '';
      userSelectedSensor.isItalic = false;
      userSelectedSensor.isBold = false;
      userSelectedSensor.fontSize = '';
      userSelectedSensor.fontcolor = '';
      dataList.push(userSelectedSensor);
      savedData[savedSelectedId] = dataList;
      console.log(savedData);
      localStorage.setItem('selected_sensors', JSON.stringify(savedData));
      let value = 2;
      localStorage.setItem('sensorValue', JSON.stringify(value));
    }
  } else {
    let SensorManageData = window.localStorage.getItem('SensorManageData')
    let windowExist = gamepp.webapp.windows.isVisible.promise('hardware_setupsensor')
    if (!windowExist) {gamepp.webapp.windows.close.promise('Sensorchoose')}
    if ((userSelectedSensor.OutIndex === -1 && userSelectedSensor.InnerIndex === -1) || !SensorManageData) {
      gamepp.webapp.sendInternalAppEvent.promise('hardware_setupsensor', '未选择传感器')
      gamepp.webapp.windows.close.promise('Sensorchoose')
      return false
    }

    let SensorManageDataObj = JSON.parse(SensorManageData)
    if (SensorManageDataObj.type === 'networkData') {
      console.log(userSelectedSensor)
      if (!((userSelectedSensor.unit).includes('KB/s'))) {
        ElMessage('请选择正确的传感器')
        return  false
      }
    }

    const obj = {
      action     : 'ChangeMainPageSensor',
      val        : userSelectedSensor,
      Changeindex: JSON.parse(localStorage.getItem('SensorManageData')!),
    }

    console.log(obj)
    gamepp.webapp.sendInternalAppEvent.promise('hardware_setupsensor', JSON.parse(JSON.stringify(obj)))
  }
  setTimeout(() => {
    gamepp.webapp.windows.close.promise('Sensorchoose')
  }, 500)

  window.localStorage.removeItem('SensorManageFilter')
}

const debouncedConfirm = debounce(Confirm, 200);

onUnmounted(() => {
  debouncedConfirm.cancel();
});

function SensorManageFilter (timeOut:number) {
  setTimeout(() => {
    const filterObj = JSON.parse(window.localStorage.getItem('SensorManageFilter')!)
    if (filterObj && filterObj.filterType) {
      console.log(filterObj.filterType.type)
      if (filterObj.filterType.type === 'CPU') {
        filterSensors('CPU')
      }else if (filterObj.filterType.type === 'GPU') {
        filterSensors('GPU')
      }else if (filterObj.filterType.type === 'Memory') {
        filterSensors('Memory','DIMM','System','DDR5')
      }else if (filterObj.filterType.type === 'Drive') {
        filterSensors('Drive', 'S.M.A.R.T.')
      }else if (filterObj.filterType.type === 'MainboardName') {
        filterSensors('MainboardName')
      }else if (filterObj.filterType.type === 'VKDevice') {
        filterSensors('VKDevice')
      }
      if (filterObj.unit === 'W') {
        select_unit.value = '功耗'
      }else if (filterObj.unit === '℃') {
        select_unit.value = '温度'
      }else if (filterObj.unit === '%') {
        select_unit.value = '占用'
      }else if (filterObj.unit === 'MHz') {
        select_unit.value = '频率'
      }else if (filterObj.unit === 'RPM') {
        select_unit.value = '转速'
      }else if (filterObj.unit === 'V') {
        select_unit.value = '电压'
      }
    }
  }, timeOut)
}
</script>

<template>
  <div class="SensorData">
    <div class="DragBox" id="WindowHead" style="-webkit-app-region: drag"></div>
    <form class="layui-form"  action="" lay-filter="example">
      <div class="layui-tab layui-tab-brief Sensor_tab" lay-filter="docDemoTabBrief">
        <div class="Sensor_tab_left">
          <p>{{ $t('hardwareInfo.SensorData') }}</p>
          <ul class="layui-tab-title">
            <li @click="MyAttention" :class="{'layui-this': MyAttentionShow}">{{ $t('SelectSensor.MyAttention') }}</li>
            <li @click="filterSensors('All')" :class="{'layui-this': filter_sensors.includes('All') && SensorInfoShow}">{{ $t('SelectSensor.All') }}</li>
            <li id="filterType_CPU" @click="filterSensors('CPU')" :class="{'layui-this': filter_sensors.includes('CPU') && SensorInfoShow}">CPU</li>
            <li id="filterType_GPU" @click="filterSensors('GPU')" :class="{'layui-this': filter_sensors.includes('GPU') && SensorInfoShow}">GPU</li>
            <li id="filterType_Memory" @click="filterSensors('Memory','DIMM','System','DDR5')" :class="{'layui-this': filter_sensors.includes('Memory') && SensorInfoShow}">{{ $t('hardwareInfo.memory') }}</li>
            <li id="filterType_Drive" @click="filterSensors('Drive', 'S.M.A.R.T.')" :class="{'layui-this': filter_sensors.includes('Drive') && SensorInfoShow}">{{ $t('hardwareInfo.hardDisk') }}</li>
            <li id="filterType_MainboardName" @click="filterSensors('MainboardName')" :class="{'layui-this': filter_sensors.includes('Mainboard') && SensorInfoShow}">{{ $t('hardwareInfo.motherboard') }}</li>
            <li v-if="is_ingame_set" id="filterType_MainboardName" @click="filterSensors('VKDevice')" :class="{'layui-this': filter_sensors.includes('VKDevice') && SensorInfoShow}">{{ $t('SelectSensor.deviceAbout') }}</li>
            <li v-if="is_ingame_set" id="filterType_MainboardName" @click="filterSensors('inGame')" :class="{'layui-this': filter_sensors.includes('inGame') && SensorInfoShow}">{{ $t('SelectSensor.relatedWithinTheGame') }}</li>
            <li v-if="is_ingame_set" id="filterType_MainboardName" @click="filterSensors('winAbout')" :class="{'layui-this': filter_sensors.includes('winAbout') && SensorInfoShow}">{{ $t('SelectSensor.winAbout') }}</li>
          </ul>
        </div>
        <div class="layui-tab-content">
            <div class="Sensor_Close">
                <RightTopIcons close-icon @close="GPP_WindowClose('Sensorchoose')" style="margin-left: auto;"/>
            </div>
          <!--<div class="Sensor_Close" @click="GPP_WindowClose('Sensorchoose')">-->
          <!--  <span class="iconfont icon-Close"></span>-->
          <!--</div>-->
          <div class="layui-input-block">
            <label class="layui-form-label">{{ $t('SelectSensor.Unit') }}</label>
            <el-select size="small" name="quiz2" id="filterUnit" v-model="select_unit">
              <el-option :label="$t('SelectSensor.All')"  value="All">{{ $t('SelectSensor.All') }}</el-option>
              <el-option label="W"    value="功耗">W</el-option>
              <el-option label="V"    value="电压">V</el-option>
              <el-option label="RPM"  value="转速">RPM</el-option>
              <el-option label="℃"   value="温度"> ℃ </el-option>
              <el-option label="%"    value="占用">%</el-option>
              <el-option label="MHz"  value="频率">MHz</el-option>
            </el-select>
          </div>
          <div class="layui-tab-item layui-show">
            <div class="scroll_box scroll">
              <!-- 我的关注页 -->
              <div class="SenosorInfo" v-show="MyAttentionShow" id="MyAttention">
                <div v-if="sensor_collected_list.length == 0" class="rebound-empty"><img src="./assets/img_unknown_ufo.png" alt=""><p>{{ $t('SelectSensor.NoAttention') }}</p></div>
                <div
                    v-for="(item,index) in sensor_collected_list"
                    class="Detail_Info"
                    :class="{'active':item.OutIndex == userSelectedSensor.OutIndex && item.InnerIndex == userSelectedSensor.InnerIndex}"
                    :key="'Detail_Info'+index"
                    @click="GPPSelected({
                      name:item.name,
                      unit:SensorList[item.OutIndex].Sensoritem[item.InnerIndex].unit
                    },item.OutIndex,item.InnerIndex,item.mainName,item)"
                >
                  <div class="icon_check">
                    <img
                        v-show="item.OutIndex == userSelectedSensor.OutIndex && item.InnerIndex == userSelectedSensor.InnerIndex"
                        src="./assets/ic_default_sensor_pick.png"
                        alt=""
                    >
                  </div>
                  <p>{{item.name}}</p>
                  <div class="item_data ml-auto">
                    <span>{{SensorList[item.OutIndex].Sensoritem[item.InnerIndex].value}}{{SensorList[item.OutIndex].Sensoritem[item.InnerIndex].unit}}</span>
                    <div class="collect_star Favorite active collected" @click.stop="collectSensor(false,item.OutIndex,item.InnerIndex,item.mainName,item.name)"></div>
                  </div>
                </div>
              </div>

              <div class="SenosorInfo" v-show="SensorInfoShow" id="SensorListHtml">
                <div class="collapse">
                  <el-collapse>
                    <div v-for="(item,outindex) in SensorList" :key="item.name">
                      <el-collapse-item  v-show="isSensorShow(item)" :title="item.name" :name="item.name" style="margin-bottom: 10px;">
                          <template #title>
                              <span>{{item.name}}</span>
                              <span v-if="item.specialName" class="specialName">{{$t(item.specialName)}}</span>
                          </template>
                        <div
                            v-for="(sensor,innerindex) in item.Sensoritem"
                            @click="GPPSelected(sensor,outindex,innerindex,item.name,item)"
                            :key="sensor.name + innerindex"
                        >
                          <div class="drop_downContent"
                               :class="{'active':outindex == userSelectedSensor.OutIndex && innerindex == userSelectedSensor.InnerIndex}"
                               v-show="isSensorItemShow(sensor)">
                            <div class="icon_check">
                              <img
                                  v-show="outindex == userSelectedSensor.OutIndex && innerindex == userSelectedSensor.InnerIndex"
                                  src="./assets/ic_default_sensor_pick.png"
                                  alt=""
                              >
                            </div>
                            <p>{{sensor.name}}</p>
                            <div class="item_data ml-auto">
                              <span>{{sensor.value}}{{sensor.unit}}</span>
                            </div>
                            <div
                                class="collect_star collected"
                                v-if="sensor.collect && item.type !== 'VKDevice' && item.type !== 'inGame' && item.type !== 'winAbout'"
                                @click.stop="collectSensor(false,outindex,innerindex,item.name,sensor.name)"
                            ></div>
                            <div class="no_collect_star" v-else></div>
                            <div
                                class="collect_star"
                                @click.stop="collectSensor(true,outindex,innerindex,item.name,sensor.name)"
                                v-if="!sensor.collect && item.type !== 'VKDevice' && item.type !== 'inGame' && item.type !== 'winAbout'"
                            ></div>
                            <div class="no_collect_star" v-else></div>
                          </div>
                        </div>

                        <div style="color: #b9b9b9;font-size: 12px;padding-left: 27px;margin-bottom: 30px;padding-top: 30px;" v-if="item.type.includes('VKDevice') && item.Sensoritem.length === 0">
                            {{$t('SelectSensor.notfoundDevice')}}
                        </div>
                        <div class="SupportedDevices" v-if="item.type.includes('VKDevice')">
                            <p v-show="item.proto === 'dl'">{{$t('SelectSensor.deviceBatteryNeedMythcool')}}</p>

                            <template v-if="item.proto === 'dl'">
                                <span>{{$t('SelectSensor.vkm1mouse')}} {{$t('SelectSensor.wireless')}}</span>
                                <span>{{$t('SelectSensor.vk99keyboard')}} {{$t('SelectSensor.wireless')}}</span>
                                <span>{{$t('SelectSensor.logitechProWireless')}} {{$t('SelectSensor.wireless')}}</span>
                                <span>{{$t('SelectSensor.logitechProXSUPERLIGHT')}} {{$t('SelectSensor.wireless')}}</span>
                                <span>{{$t('SelectSensor.logitechProXSUPERLIGHT2')}} {{$t('SelectSensor.wireless')}}</span>
                                <span>{{$t('SelectSensor.logitechPro2LIGHTSPEED')}} {{$t('SelectSensor.wireless')}}</span>
                                <span>{{$t('SelectSensor.steelSeriesArctisGameBuds')}}</span>
                                <span>{{$t('SelectSensor.steelSeriesArctis7PPlus')}}</span>
                                <span>{{$t('SelectSensor.logitechNeedGhub')}}</span>
                            </template>
                            <template v-if="item.proto === 'hbl'">
                                <span>{{$t('SelectSensor.vkm1mouse')}}</span>
                                <span>{{$t('SelectSensor.vkm2mouse')}}</span>
                                <span>{{$t('SelectSensor.logitechProWireless')}}</span>
                                <span>{{$t('SelectSensor.logitechProXSUPERLIGHT')}}</span>
                                <span>{{$t('SelectSensor.logitechProXSUPERLIGHT2')}}</span>
                                <span>{{$t('SelectSensor.logitechPro2LIGHTSPEED')}}</span>
                                <span>{{$t('SelectSensor.razerV3')}}</span>
                                <span>{{$t('SelectSensor.razerV2')}}</span>
                            </template>
                        </div>
                      </el-collapse-item>
                    </div>
                  </el-collapse>
                </div>
              </div>
            </div>
            <div class="bottomline">
              <!-- <div class="Cur">
                  <p>当前选择显示：</p>
                  <span>传感器信息</span>
              </div> -->
              <div class="Confrim ml-auto">
                <p class="cancel" @click="GPP_WindowClose('Sensorchoose')">{{ $t("messages.cancel") }}</p>
                <p class="confirm hover" @click="debouncedConfirm">{{ $t("messages.confirm") }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</template>


<style lang="scss" scoped>
.drop_downContent{
  width: 100%;
  height: 24px;
  background: #353641;
  border-radius: 2px;
  color: #b9b9b9;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 0 10px;
  cursor: pointer;
  position: relative;
  font-size: 12px;

  &:hover,&.active {
    font-size: 14px;
    background: #4a87d9;
    color: rgb(255, 255, 255);

    .collect_star {
      visibility: visible;
    }
  }
}
#MyAttention .collect_star {
  background: url(./assets/ic_default_sensor_collect_a.png)no-repeat;
}
.Detail_Info:first-child {
  .collect_star {
    &::before {
      top: 25px;
    }
  }
}
.no_collect_star {
  width: 20px;
  height: 19px;
  margin: 0 10px 0 20px;
}
.collect_star {
  width: 20px;
  height: 19px;
  margin: 0 10px 0 20px;
  background: url(./assets/ic_default_sensor_collect.png)no-repeat;
  background-size: 100% 100%;
  position: relative;
  cursor: pointer;
  visibility: hidden;

  &:hover::before {
    content: '关注'
  }

  &::before {
    content: '';
    position: absolute;
    font-size: 12px;
    color: white;
    display: block;
    width: 50px;
    top: -25px;
    left: -25px;
    background-color: rgba(0,0,0,0.6);
    padding: 0 10px;
    text-align: center;
    z-index: 99;
    line-height: 20px;
  }

  &.active {
    visibility: visible;
  }

  &.collected {
    background: url(./assets/ic_default_sensor_collect_a.png)no-repeat;

    &:hover::before {
      content: '取消关注' !important;
    }
  }
}
.item_data{display:flex;align-items:center}
.scroll_box{height:510px;overflow:auto}
input[type=checkbox]:checked::after {content: url('./assets/checkmark.png');display: block;position: absolute;top: -3px;right: 0px;left: -5px;width: 12px !important;height: 12px !important;}
layui-input:focus, .layui-textarea:focus {border-color: #0089E9 !important;}
.layui-input,.layui-select,.layui-textarea{height:26px;background-color:rgba(37,39,47,1);border:1px solid #2D303E;float:right;width:130px;color:#E1E1E1}
.layui-form-select dl{background:#2C2C33;border:1px solid #39404D;top:32px}
.layui-form-select dl::-webkit-scrollbar{width:6px}
.layui-form-select dl::-webkit-scrollbar-thumb{background:#4B4B53;border-radius:200px}
.layui-form-select dl::-webkit-scrollbar-track{background-color:#34343B;border-radius:200px}
.layui-form-select dl dd{line-height:26px;color:#E1E1E1}
.layui-form-select dl dd.layui-this{background-color:#3579d5;color:#fff}
.layui-form-select dl dd:hover{background:#515158}
.layui-input:hover{border-color:#4a87d9 !important}
.layui-input:focus{border-color:#4a87d9 !important}
.layui-input-block{display: flex;align-items: center;margin-left: auto;margin-right: 10px;width: 200px;margin-top: 10px;margin-bottom: 10px;gap: 10px;}
.layui-form-label{padding: 0;color: #777777;white-space: nowrap}
input.layui-input::selection {background: transparent;}
.collapse{margin-bottom:5px;color: white;}
.icon_check{margin-right: 5px;width: 14px;height: 21px;}
.rebound-empty{position: absolute;top: 50%;left: 50%;margin-left: -90px;margin-top: -145px;}
.rebound-empty p{color: #E1E1E1;font-size: 20px;text-align: center;margin-top: -90px;}
.rebound-empty img{width: 254px;height: 257px;margin: 0 auto;display: block;}
.DragBox{width: 770px;height: 40px;position: absolute;top: 0;}
.SensorData{width:800px;height:660px;background:#21222A;border-radius:2px;position:relative;box-shadow: 0 1px 6px rgb(0 0 0 / 60%);position: relative;margin-left: 6px; margin-top: 6px;overflow: hidden;}
.Sensor_tab{display:flex}
.Sensor_tab_left{width:120px;height:660px;background:#2b2c37;border-radius:2px 2px 5px 2px}
.Sensor_tab .layui-tab-title{display:flex;flex-direction:column;border-bottom-width:0;margin-top:50px;text-align: center;}
.Sensor_tab .layui-tab-title li{cursor: pointer; overflow: hidden;white-space: nowrap; text-overflow: ellipsis;}
.Sensor_tab .layui-tab-title .layui-this{color:#FFFFFF;background:#3579d5;border-radius:2px;font-size:14px!important}
.Sensor_tab .layui-tab-title .layui-this:after{content:none}
.Sensor_tab_left li{color:#777777;font-size:12px!important;height:30px;line-height:30px!important}
.Sensor_tab_left p{font-size:14px;color:#FFFFFF;text-align:center;margin-top:15px}
.SensorData .layui-tab{margin:0}
.Sensor_Close{width:40px;height:40px;cursor:pointer;margin-left:auto;}
.SensorData .layui-tab-content{padding:0;width:680px;}
.SensorData .layui-tab-item{margin:0 auto}
.SenosorInfo{width:630px;margin:0 auto;height:510px;padding-top:10px}
.Detail_Info{width:613px;margin:0 auto;height:24px;background:#353641;border-radius:2px;color:#999999;display:flex;align-items:center;justify-content:flex-start;margin-bottom:10px;cursor:pointer;position:relative;font-size: 12px;padding: 0 5px;}
.Detail_Info2{background:none}
.Detail_Info:hover,.Detail_Info.active{background:#4a87d9;font-size:14px;color:#fff}
.Detail_Info p{margin-left:10px}
.Confrim{display:flex;align-items:center;}
.Confrim p{width:123px;height:38px;border-radius:2px;color:#FFFFFF;font-size:12px;display:flex;justify-content:center;align-items:center;cursor:pointer;}
.cancel{background:#343647;margin-right:10px}
.confirm{background:#3579d5;}
.confirm:hover{background:#4a87d9;}
.cancel:hover{background: #3e4050
}
.bottomline{display:flex;align-items:center;width:630px;margin:0 auto;padding-top:10px;justify-content:space-between}
.SupportedDevices {
  color: #777777 !important;
  display: flex;
  flex-flow: column nowrap;
  font-size: 12px;
  margin-left: 27px;

  p {
    margin-bottom: 20px;
  }
}

.specialName {
  font-size: 12px;
  color: #777777;
}
</style>

<style>
.el-collapse {
    --el-fill-color-blank: #353641;
    --el-text-color-primary: #fff;
    --el-border-color-lighter: transparent;
    --el-collapse-border-color: var(--el-border-color-lighter);
    --el-collapse-header-height: 48px;
    --el-collapse-header-bg-color: var(--el-fill-color-blank);
    --el-collapse-header-text-color: var(--el-text-color-primary);
    --el-collapse-header-font-size: 13px;
    --el-collapse-content-bg-color: var(--el-fill-color-blank);
    --el-collapse-content-font-size: 13px;
    --el-collapse-content-text-color: var(--el-text-color-primary);
    border-bottom: 1px solid var(--el-collapse-border-color);
    border-top: 1px solid var(--el-collapse-border-color);
}
.el-collapse-item__header {
    padding-left: 10px;
}
.el-collapse-item__wrap {
    overflow: visible;
}
.el-select {
    --el-border-color: #2D303E !important;
    --el-color-primary: #3579d5;
}
.el-select__wrapper {
    color: #E1E1E1;
    background-color: rgba(37,39,47,1);
}
.el-select__popper.el-popper .el-popper__arrow:before {
    border-color: #2D303E !important;
}
.el-popper__arrow:before {
    background-color: #2C2C33 !important;
}
.el-select__popper.el-popper {
    background: #2C2C33;
    border: 1px solid #39404D;
}
.el-select-dropdown__item {
    color: white;
}
.el-select-dropdown__item.is-hovering {
    background-color: #4a87d9;
    color: white;
}
.el-button:focus-visible{
    outline: transparent
}
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.flex-items-center {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
}

.ml-auto {
    margin-left: auto;
}
.el-select__selected-item.el-select__placeholder {
    color: #777;
}
.scroll::-webkit-scrollbar {
    width: 5px;
    transition: 0.25s;
}

.scroll::-webkit-scrollbar-thumb {
    background: #71738C;
    border-radius: 3px;
}

.scroll::-webkit-scrollbar-thumb:hover {
    background-color: #71738C;
}

.scroll::-webkit-scrollbar-track {
    background-color: #2D2E39;
    width: 2px;
}

.scroll::-webkit-scrollbar {
    width: 5px;
    transition: 0.25s;
}

.scroll::-webkit-scrollbar-thumb {
    background: #71738C;
    border-radius: 3px;
}

.scroll::-webkit-scrollbar-thumb:hover {
    background-color: #71738C;
}

.scroll::-webkit-scrollbar-track {
    background-color: #2D2E39;
    width: 2px;
}
</style>
