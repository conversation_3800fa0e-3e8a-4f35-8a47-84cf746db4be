<template>
    <div class="shortcut" :style="{'--bg-color--':props.bgColor}">
      <el-tooltip  popper-class="custom_tooltip" :content="$t('messages.changeKey')">
        <el-input
          :style="{width: props.width+'px', height: props.height+'px'}"
          :input-style="{color: 'var(--font-color)'}"
          :value="showInputValue"
          @keydown="keydownFun($event, props.id)"
          @keyup="keyupFun($event)"
          @focus="focusId = props.id"
          @blur="focusId = -1"
          tabindex="-1"
        >
          <template #suffix>
            <span v-show="focusId == props.id" class="clear" @click="ClearCurrentHotkey(props.id)">{{$t('messages.clear')}}</span>
          </template>
        </el-input>
      </el-tooltip>
            <span v-show="occupyShortcutKeys.includes(focusId) && focusId == id">{{$t('messages.hotkeyOccupied')}}</span>
    </div>
</template>

<script setup lang="ts">
import {ref, defineProps, onMounted, watch} from 'vue'
import {gameppBaseSetting} from '../../modules/Game_Home/stores/index'
import {GPP_SendStatics} from '@/uitls/sendstatics'
import {useI18n} from 'vue-i18n'

const {t} = useI18n();
// @ts-ignore
const gamepp = window.gamepp;
const store = gameppBaseSetting();

const props = defineProps({
    id: Number,
    width: {
      type: Number,
      default: 180
    },
    height: {
        type: Number,
      default: 32
    },
    bgColor: {
      type: String,
      default: '#343647'
    }
})

let iHotkey = 0
let HotId: any = null;
let HotValue: any = null;
let keyNormalText: any = null;
let focusId = ref(-1);
const occupyShortcutKeys = ref<Array<number>>([])
const showInputValue = ref<string>('')
const sc_broadcast = new BroadcastChannel('shortcut')
onMounted(()=>{
  showInputValue.value = checkNone(gamepp.setting.getString.sync(Number(props.id)))
  sc_broadcast.onmessage = (event) => {
    if (event.data.hasOwnProperty('action')) {
      if (event.data.action === 'set_default') {
        showInputValue.value = checkNone(gamepp.setting.getString.sync(Number(props.id)))
      }
    }
  }
})

watch(()=>store.state.shortcutKey,()=>{
  showInputValue.value = checkNone(gamepp.setting.getString.sync(Number(props.id)))
})

function checkNone(v:string) {
  if (v=== '无' || v === 'None' || v === 'None_cn') {
    return t('GameRebound.noData')
  }else{
    return v
  }
}

// 清空某个热键
async function ClearCurrentHotkey(this_id:number) {
  store.state.shortcutKey[this_id] = 'None_cn';
  if (this_id != 527) await GPP_WriteInteger(Number(this_id - 1), -1);
  await GPP_WriteString(this_id, 'None_cn');
  let Obj = {action: "UpdateHotKey"};
  await gamepp.webapp.sendInternalAppEvent.promise('background', Obj);

  DeleteConflictData(this_id);
}
async function keydownFun(event: any, thisID: number) {
  event.preventDefault();
  if (event.keyCode === 8) {
    ClearCurrentHotkey(thisID)
    return
  }
  if (event.keyCode === 13 || event.keyCode === 27) {
    return false;
  } else if (event.keyCode == 17) // Ctrl
    iHotkey = iHotkey | 0x10000000;
  else if (event.keyCode == 16) // Shift
    iHotkey = iHotkey | 0x20000000;
  else if (event.keyCode == 18)  // Alt
    iHotkey = iHotkey | 0x40000000;
  else
    iHotkey = (iHotkey | event.keyCode);
  let keyProcessData = HotkeyRule(iHotkey);
  console.log(keyProcessData)
  if (keyProcessData.length === 2) {
    let HotkeyState = await gamepp.game.ingame.queryHotkeyState.promise(keyProcessData[1]);
    if (!HotkeyState) {
      occupyShortcutKeys.value.push(thisID)
      return false;
    }else {
      occupyShortcutKeys.value = occupyShortcutKeys.value.filter((item) => item !== thisID)
    }
    store.state.shortcutKey[thisID] = keyProcessData[1]
    HotId = thisID;
    HotValue = keyProcessData[1];
  } else {
    if (iHotkey === 268435456 || iHotkey === 1073741824 || iHotkey === 536870912 || iHotkey === 32) {
      return false;
    } else if (iHotkey === 8) {
      store.state.shortcutKey[thisID] = ''
      return false;
    }
    // 有效的热键组合
    keyNormalText = getKeyText(event);
    console.log(keyNormalText)
    if (!isValidKeyboardInput(keyNormalText)) { // 判断字符串中所有字符都是键盘可以输入的
      store.state.shortcutKey[thisID] = ''
      return false;
    }
    let strHotkeyText = parseHotkeyValueToStr(iHotkey);
    let HotkeyState = await gamepp.game.ingame.queryHotkeyState.promise(keyNormalText);
    if (!HotkeyState) {
      occupyShortcutKeys.value.push(thisID)
      return false;
    }else {
      occupyShortcutKeys.value = occupyShortcutKeys.value.filter((item) => item !== thisID)
    }
    if (iHotkey === 8) {
      store.state.shortcutKey[thisID] = ''
      return false;
    } else if (strHotkeyText.length > 0 && iHotkey !== 8) {
      store.state.shortcutKey[thisID] = keyNormalText;
      HotId = thisID;
      HotValue = keyNormalText;
    }
  }

  await GPP_WriteString(HotId, HotValue);
  let Obj = {action: ""};
  Obj['action'] = 'UpdateHotKey';
  await gamepp.webapp.sendInternalAppEvent.promise('background', Obj);
  DeleteConflictData(HotId);
}
function keyupFun(event:any) {
    if (event.keyCode == 17) {
        iHotkey = iHotkey - 268435456
    } else {
        if (event.keyCode == 16) {
            iHotkey = iHotkey - 536870912
        } else {
            if (event.keyCode == 18) {
                iHotkey = iHotkey - 1073741824
            } else {
                iHotkey = iHotkey & 4294901760
            }
        }
    }
}

function getKeyText(e:any) {
    let result
    var keyName;
    switch (e.keyCode) {
        // case 8:
        //     keyName = "无";//Backspace
        //     break;
        case 9:
            keyName = "TAB";
            break;
        // case 13:
        //     keyName = "无";//Enter
        //     break;
        case 16:
            keyName = e.keyCode;
            break;
        case 17:
            keyName = e.keyCode;
            break;
        case 18:
            keyName = e.keyCode;
            break;
        // case 19:
        //     keyName = "无";//Pause
        //     break;
        case 20:
            keyName = "Caps_Lock";
            break;
        // case 27:
        //     keyName = "无";//Esc
        //     break;
        // case 32:
        //     keyName = "无";//sp
        //     break;
        case 33:
            keyName = "PageUp";
            break;
        case 34:
            keyName = "PageDown";
            break;
        case 35:
            keyName = "End";
            break;
        case 36:
            keyName = "Home";
            break;
        case 37:
            keyName = "←";
            break;
        case 38:
            keyName = "↑";
            break;
        case 39:
            keyName = "→";
            break;
        case 40:
            keyName = "↓";
            break;
        case 45:
            keyName = "Insert";
            break;
        // case 46:
        //     keyName = "无";
        //     break;
        case 48:
            keyName = "0";
            break;
        case 49:
            keyName = "1";
            break;
        case 50:
            keyName = "2";
            break;
        case 51:
            keyName = "3";
            break;
        case 52:
            keyName = "4";
            break;
        case 53:
            keyName = "5";
            break;
        case 54:
            keyName = "6";
            break;
        case 55:
            keyName = "7";
            break;
        case 56:
            keyName = "8";
            break;
        case 57:
            keyName = "9";
            break;
        case 91:
            keyName = "";
            break;
        case 92:
            keyName = "";
            break;
        case 93:
            keyName = "";
            break;
        case 95:
            keyName = e.key;
            break;
        case 96:
            keyName = "Num0";
            break;
        case 97:
            keyName = "Num1";
            break;
        case 98:
            keyName = "Num2";
            break;
        case 99:
            keyName = "Num3";
            break;
        case 100:
            keyName = "Num4";
            break;
        case 101:
            keyName = "Num5";
            break;
        case 102:
            keyName = "Num6";
            break;
        case 103:
            keyName = "Num7";
            break;
        case 104:
            keyName = "Num8";
            break;
        case 105:
            keyName = "Num9";
            break;
        case 106:
            keyName = "*";
            break;
        case 107:
            keyName = "+";
            break;
        case 109:
            keyName = "-";
            break;
        case 110:
            keyName = ".";
            break;
        case 111:
            keyName = "/";
            break;
        case 112:
            keyName = "F1";
            break;
        case 113:
            keyName = "F2";
            break;
        case 114:
            keyName = "F3";
            break;
        case 115:
            keyName = "F4";
            break;
        case 116:
            keyName = "F5";
            break;
        case 117:
            keyName = "F6";
            break;
        case 118:
            keyName = "F7";
            break;
        case 119:
            keyName = "F8";
            break;
        case 120:
            keyName = "F9";
            break;
        case 121:
            keyName = "F10";
            break;
        case 122:
            keyName = "F11";
            break;
        case 123:
            keyName = "F12";
            break;
        case 144:
            keyName = "Num Lock";
            break;
        case 145:
            keyName = "Scroll Lock";
            break;
        case 186:
            keyName = ";";
            break;
        case 187:
            keyName = "=";
            break;
        case 188:
            keyName = ",";
            break;
        case 189:
            keyName = "-";
            break;
        case 190:
            keyName = ".";
            break;
        case 191:
            keyName = "/";
            break;
        case 192:
            keyName = "~";
            break;
        case 219:
            keyName = "[";
            break;
        case 220:
            keyName = "\\";
            break;
        case 221:
            keyName = "]";
            break;
        case 222:
            keyName = "'";
            break;
        case 255:
            keyName = e.keyCode;
            break;
        default:
            keyName = String.fromCharCode(e.keyCode);
            break;
    }


    // if (keyName == " ") {
    //     return false;
    // }

    if ((e.shiftKey) && (e.keyCode != 16)) {
        keyName = "Shift+" + keyName
    }
    if ((e.altKey) && (e.keyCode != 18)) {
        keyName = "Alt+" + keyName
    }
    if ((e.ctrlKey) && (e.keyCode != 17)) {
        keyName = "Ctrl+" + keyName
    }
    var str = "'" + keyName + "'";
    result = str.substring(1, str.length - 1);
    if (result.includes("å")) {
        return ""
    }
    if (str.indexOf("16") == -1 && str.indexOf("17") == -1 && str.indexOf("18") == -1) {
        return result
    } else {
        return ""
    }
}
function parseHotkeyValueToStr(ikey: any) {
  var szHotkeyText = "";
  if (ikey & 268435456) {
    szHotkeyText = "Ctrl"
  }
  if (ikey & 536870912) {
    if (szHotkeyText.length == 0) {
      szHotkeyText = "Shift"
    } else {
      szHotkeyText += "+Shift"
    }
  }
  if (ikey & 1073741824) {
    if (szHotkeyText.length == 0) {
      szHotkeyText = "Alt"
    } else {
      szHotkeyText += "+Alt"
    }
  }
  var ikeyNormal = ikey & 65535;
  if (ikeyNormal > 0) {
    if (keyNormalText == "") {
      iHotkey = 0;
      szHotkeyText = ""
    } else {
      if (szHotkeyText.length == 0) {
        szHotkeyText = keyNormalText
      } else {
        szHotkeyText += "+" + keyNormalText
      }
    }
  } else {
    szHotkeyText = ""
  }
  return szHotkeyText
}

function DeleteConflictData(HotId: any) {
  //删除local冲突数据
  let data = window.localStorage.getItem('conflict_hotkey');
  if (data !== null && data !== '[]' && data !== "") {
    let conflictKey = JSON.parse(data);
    delete conflictKey[HotId];
    let length = (Object.keys(conflictKey)).length;
    if (length === 0) {
      window.localStorage.removeItem('conflict_hotkey');
    } else {
      window.localStorage.setItem('conflict_hotkey', JSON.stringify(conflictKey));
    }
  }
}

async function GPP_WriteInteger(id: number, value: number) {
  let old_value = await GPP_GetInteger(Number(id))
  if (Number(old_value) !== Number(value)) {
    console.log('setInteger,' + '' + Number(id) + ',' + Number(value));
    try {
      await gamepp.setting.setInteger.promise(Number(id), Number(value));
    } catch (error) {
    }
  }
}

async function GPP_GetInteger(id: number) {
  let value = 0;
  try {
    value = await gamepp.setting.getInteger.promise(id);
  } catch {
  }
  return value;
}

async function GPP_WriteString(id: number, value: string) {
  if (id === 9) {
    GPP_SendStatics(100772)
  }else if (id === 15) {
    GPP_SendStatics(100773)
  }else if (id === 13) {
    GPP_SendStatics(100774)
  }else if (id === 389) {
    GPP_SendStatics(100775)
  }else if (id === 468) {
    GPP_SendStatics(100776)
  }else if (id === 527) {
    GPP_SendStatics(100777)
  }
  console.log('setString,' + '' + id + ',' + value);
  try {
    await gamepp.setting.setString.promise(id, value);
    showInputValue.value = checkNone(gamepp.setting.getString.sync(props.id))
  } catch (error) {
  }
}
interface data_arr {
  [key: string]: Array<any>;
}
function HotkeyRule(iHotkey: any) {
  const RuleJson = '{"37":["1342177317","Ctrl+Alt+Left"],"38":["1342177318","Ctrl+Alt+Up"],"39":["1342177319","Ctrl+Alt+Right"],"40":["1342177320","Ctrl+Alt+Down"],"48":["1342177328","Ctrl+Alt+0"],"49":["1342177329","Ctrl+Alt+1"],"50":["1342177330","Ctrl+Alt+2"],"51":["1342177331","Ctrl+Alt+3"],"52":["1342177332","Ctrl+Alt+4"],"53":["1342177333","Ctrl+Alt+5"],"54":["1342177334","Ctrl+Alt+6"],"55":["1342177335","Ctrl+Alt+7"],"56":["1342177336","Ctrl+Alt+8"],"57":["1342177337","Ctrl+Alt+9"],"65":["1342177345","Ctrl+Alt+A"],"66":["1342177346","Ctrl+Alt+B"],"67":["1342177347","Ctrl+Alt+C"],"68":["1342177348","Ctrl+Alt+D"],"69":["1342177349","Ctrl+Alt+E"],"70":["1342177350","Ctrl+Alt+F"],"71":["1342177351","Ctrl+Alt+G"],"72":["1342177352","Ctrl+Alt+H"],"73":["1342177353","Ctrl+Alt+I"],"74":["1342177354","Ctrl+Alt+J"],"75":["1342177355","Ctrl+Alt+K"],"76":["1342177356","Ctrl+Alt+L"],"77":["1342177357","Ctrl+Alt+M"],"78":["1342177358","Ctrl+Alt+N"],"79":["1342177359","Ctrl+Alt+O"],"80":["1342177360","Ctrl+Alt+P"],"81":["1342177361","Ctrl+Alt+Q"],"82":["1342177362","Ctrl+Alt+R"],"83":["1342177363","Ctrl+Alt+S"],"84":["1342177364","Ctrl+Alt+T"],"85":["1342177365","Ctrl+Alt+U"],"86":["1342177366","Ctrl+Alt+V"],"87":["1342177367","Ctrl+Alt+W"],"88":["1342177368","Ctrl+Alt+X"],"89":["1342177369","Ctrl+Alt+Y"],"90":["1342177370","Ctrl+Alt+Z"],"192":["1342177472","Ctrl+Alt+`"]}';
  let data_arr: data_arr = JSON.parse(RuleJson)
  let RuleIHotkeyNum: Array<any> = [];
  Object.keys(data_arr).forEach(function (k: string) {
    if (iHotkey == k) {
      RuleIHotkeyNum = data_arr[k];
    }
  })
  return RuleIHotkeyNum;
}

function isValidKeyboardInput(inputString:string) {
  // 定义键盘上可输入的字符集
  const validCharsRegex = /^([A-Za-z0-9!@#$%^&*()-_=+[\]{}|;:'",.<>\/?`~ ]|ctrl|shift|alt|esc|insert|delete|home|end|pageUp|pageDown)+$/;

  // 使用正则表达式测试输入字符串
  return validCharsRegex.test(String(inputString).toLowerCase());
}
</script>

<style lang="scss">
.shortcut{
  --bg-color--: #343647;
  .el-input {
    --el-border-color: rgba(0,0,0,0);
    --el-input-bg-color: var(--bg-color--) !important;
    --el-input-text-color: #fff;
  }
}
</style>
<style>
.custom_tooltip{
    background: #343647!important;
    border: 1px solid #777777!important;
    color: #ffffff !important;
}
.custom_tooltip  .el-popper__arrow:before{
  background: #343647!important;
  border: 1px solid #777777!important;
}
</style>
