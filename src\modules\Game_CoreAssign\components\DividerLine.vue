<template>
    <div v-for="item in list" :key="item.name" class="divider-line" :class="item.className"
        @mousedown="dragDivider(item.name, $event)">
    </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue'
const props = defineProps({
    list: {
        type: Array,
        required: true
    },
    dragDivider: {
        type: Function,
        required: true
    }
})
</script>

<style lang="scss">
.all-process {
    .divider-line {
        position: absolute;
        top: 0;
        width: 16px;
        bottom: 0px;
        cursor: col-resize;
        transform: translateX(-50%);

        &:before {
            content: '';
            position: absolute;
            top: 0;
            left: calc(50% - 0.5px);
            width: 1px;
            height: 100%;
            background: #3F414F;
        }

        &-name {
            left: var(--all-name-w);
        }

        &-thread {
            left: calc(var(--all-name-w) + var(--all-thread-w));
        }

        &-cpu {
            left: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w));
        }

        &-cpu-pp {
            left: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) + var(--all-cpu-pp-w));
        }

        &-gpu {
            left: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) + var(--all-cpu-pp-w) + var(--all-gpu-w));
        }

        &-gpu-pp {
            left: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) + var(--all-cpu-pp-w) + var(--all-gpu-w) + var(--all-gpu-pp-w));
        }

        &-mem {
            left: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) + var(--all-cpu-pp-w) + var(--all-gpu-w) + var(--all-gpu-pp-w) + var(--all-mem-w));
        }

        &-mem-pp {
            left: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) + var(--all-cpu-pp-w) + var(--all-gpu-w) + var(--all-gpu-pp-w) + var(--all-mem-w) + var(--all-mem-pp-w));
        }

        &-group {
            left: calc(var(--all-name-w) + var(--all-thread-w) + var(--all-cpu-w) + var(--all-cpu-pp-w) + var(--all-gpu-w) + var(--all-gpu-pp-w) + var(--all-mem-w) + var(--all-mem-pp-w) + var(--all-group-w));
        }
    }
}

.group-process {
    .divider-line {
        position: absolute;
        top: 0;
        width: 16px;
        bottom: 0px;
        cursor: col-resize;
        transform: translateX(-50%);

        &:before {
            content: '';
            position: absolute;
            top: 0;
            left: calc(50% - 0.5px);
            width: 1px;
            height: 100%;
            background: #3F414F;
        }

        &-name {
            left: var(--name-w);
        }

        &-thread {
            left: calc(var(--name-w) + var(--thread-w));
        }

        &-cpu {
            left: calc(var(--name-w) + var(--thread-w) + var(--cpu-w));
        }

        &-cpu-pp {
            left: calc(var(--name-w) + var(--thread-w) + var(--cpu-w) + var(--cpu-pp-w));
        }

        &-gpu {
            left: calc(var(--name-w) + var(--thread-w) + var(--cpu-w) + var(--cpu-pp-w) + var(--gpu-w));
        }

        &-gpu-pp {
            left: calc(var(--name-w) + var(--thread-w) + var(--cpu-w) + var(--cpu-pp-w) + var(--gpu-w) + var(--gpu-pp-w));
        }

        &-mem {
            left: calc(var(--name-w) + var(--thread-w) + var(--cpu-w) + var(--cpu-pp-w) + var(--gpu-w) + var(--gpu-pp-w) + var(--mem-w));
        }

        &-mem-pp {
            left: calc(var(--name-w) + var(--thread-w) + var(--cpu-w) + var(--cpu-pp-w) + var(--gpu-w) + var(--gpu-pp-w) + var(--mem-w) + var(--mem-pp-w));
        }

    }
}
</style>