<template>
    <div class="screenshot_layout">
        <window-header :window-name="windowName" :maximizeIcon="false" :minimizeIcon="false">
            <span class="windowName">{{$t('hardwareInfo.Screenshot')}}</span>
            <!-- <span>V{{version}}</span> -->
        </window-header>
        <div class="screenshot_content">
            <div class="screenshot_content_left">
                <div class="shortcut_box screenshot_content_item">
                    <div class="s-title" >
                        <!--快捷键-->
                        {{$t('Setting.shortcut')}}
                    </div>
                    <div class="s-row">
                        <!--截图-->
                        <span class="s-row-title">{{$t('hardwareInfo.Screenshot')}}</span>

                        <shortcut :id="60"  style="color: white;"/>
                    </div>
                </div>
                <div class="setting screenshot_content_item">
                    <div class="s-title" >{{ $t('Setting.setting') }}</div>
                    <div class="Wrapframe">
                        <!-- <el-checkbox v-model="data.checkbox150" @change="(e: any)=>onChangeCheckbox(150,e)" label="123123" /> -->

                        <el-checkbox class="Splicestyle" v-model="data.checkbox150" @change="(e: any)=>onChangeCheckbox(150,e)">
                            <span>{{ $t('screenshotpage.Turnon') }}</span>
                            <!-- <el-input v-model="inputValue" placeholder="请输入内容" style="width: 200px;" /> -->
                            <input
                                :value="inputValue"
                                @input="handleInput"
                                @click="AutoScreenshotOnc"
                                @blur="AutoScreenshotOnb"
                                type="text"
                                class="frames-btn1"
                                maxlength="5"
                                @keyup="handleKeyup"
                            />
                            <span>{{ $t('screenshotpage.seconds') }}</span>
                            <em>{{ framesMinute }}</em>
                            <span> {{ $t('screenshotpage.takeScreenshot') }}</span><br>
                            <span class="Tips">{{ $t('screenshotpage.screenshotSettings') }}</span>
                        </el-checkbox>
                        <el-checkbox v-model="data.checkbox403" @change="(e: any)=>onChangeCheckbox(403,e)" :label="$t('screenshotpage.saveGameFilterAndMonitoring')" />
                        <el-checkbox v-model="data.checkbox373" @change="(e: any)=>onChangeCheckbox(373,e)" :label="$t('screenshotpage.disableScreenshotSound')" />
                    </div>
                </div>
                <div class="image_type screenshot_content_item">
                    <div class="s-title" >{{ $t('screenshotpage.imageFormat') }}</div>
                    <div class="Wrapframe">
                        <el-radio-group v-model="data.radio171" @change="handleChange">
                            <el-radio :title="$t('screenshotpage.viewingdetails')" :label="2" name="RecordingPlan" class="radioclass"><span class="format">PNG</span> <em>（{{ $t('screenshotpage.recommended') }}）</em><span>{{ $t('screenshotpage.viewingdetails') }}</span></el-radio>
                            <el-radio :title="$t('screenshotpage.saveSpace')" :label="3" name="RecordingPlan" class="radioclass"><span class="format">JPG</span> <span>{{ $t('screenshotpage.saveSpace') }}</span></el-radio>
                            <el-radio :title="$t('screenshotpage.ultraQuality')" :label="1" name="RecordingPlan" class="radioclass"><span class="format">BMP</span> <span>{{ $t('screenshotpage.ultraQuality') }}</span></el-radio>
                        </el-radio-group>
                    </div>
                </div>
            </div>
            <div class="screenshot_content_right">
                <div class="savePath screenshot_content_item">
                    <div class="s-title" >{{ $t('screenshotpage.fileSavePath') }}</div>
                    <div class="storage">
                        <input class="route":title="data.filePath" type="button" :value="data.filePath" >
                        <input @click="GPP_ShowOpenDialog('选择截图保存路径', 5)" class="button hover" type="button" :value="$t('video.edit')">
                        <input @click="GPP_OpenPicturePath" class="button hover" type="button" :value="$t('video.open')"  >
                    </div>
                    <div class="Size">
                        <div class="state" :style="{ backgroundColor: statusColor }"></div>
                        <p>
                            <span>{{ $t('screenshotpage.hardDiskSpace') }}</span> <span>{{diskLeftSize(data.filePath)}}</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import WindowHeader from "@/components/mainCom/windowHeader.vue";
import Shortcut from "@/components/eleCom/shortcut.vue";
import {onBeforeMount, ref,onMounted, reactive, computed} from "vue";
import { gamepp } from "gamepp"
import { useI18n } from 'vue-i18n';
import { ElMessage } from "element-plus";
import { useLanguage } from '@/uitls/useLanguage';
useLanguage()
const { t } = useI18n();
const windowName = "screenshot"
const version = ref('')
const statusColor = ref('#337F45');
const data = reactive({
    checkbox150: false,
    checkbox403: false,
    checkbox373: false,
    radio171: 2,
    filePath:"",
})

const inputValue = ref(300)
const initFramesVal = ref(300);
const minutes = 'screenshotpage.minutes';

const framesMinute = computed(() => `(${((inputValue.value) / 60).toFixed(2)} ${t(minutes)})`);

onMounted(async () => {
    data.checkbox150 = await GPP_GetInteger(150) === 1;
    data.checkbox403 = await GPP_GetInteger(403) === 1;
    data.checkbox373 = await GPP_GetInteger(373) === 1;
    data.radio171 = await GPP_GetInteger(171);
    data.filePath = await gamepp.setting.getString.promise(5);
    GPP_WriteInteger(57, 1);
})

function handleInput(event: { target: { value: string; }; }) {
    inputValue.value =  Number(event.target.value);
}
function handleKeyup(event: { target: { value: string; }; }) {
    event.target.value = event.target.value.replace(/[^\d]/g, '');
    inputValue.value =  Number(event.target.value);
    // updateFramesMinute();
}

function AutoScreenshotOnc() {

}

function AutoScreenshotOnb() {
  let val = inputValue.value;
  if (val) {
    val = parseInt(val, 10);
    if (val <= 0) {
      inputValue.value = Number(initFramesVal.value.toString());
      GPP_WriteInteger(151, initFramesVal.value);
    } else {
      initFramesVal.value = val;
      GPP_WriteInteger(151, val); // 确保这里保存的是最新的用户输入值
    }
  } else {
    inputValue.value = Number(initFramesVal.value.toString());
  }
}
async function GPP_WriteInteger (id: any, value: any) {
    console.log('setInteger,' + '' + Number(id) + ',' + Number(value));
    let old_value = await GPP_GetInteger(Number(id))
    if (Number(old_value) !== Number(value)) {
        console.log('setInteger,' + '' + Number(id) + ',' + Number(value));
        try {
            await gamepp.setting.setInteger.promise(Number(id), Number(value));
        } catch (error) {
        }
    }
}

async function GPP_GetInteger(id: number) {
    let value = 0;
    try {
        value = await gamepp.setting.getInteger.promise(id);
    } catch {
    }
    return value;
}

async function getVersion() {
    try {
        const versionObj: any = await gamepp.package.getversion.promise("GameScreenshot")
        if (Object.prototype.toString.call(versionObj) === '[object Object]' && 'version' in versionObj) {
            version.value = versionObj.version;
        } else {
            version.value = gamepp.getPlatformVersion.sync()
        }
    } catch (e) {
        console.log(e)
    }
}

const onChangeCheckbox = async (id: number, e: any) => {
    console.log(id,e)
    const ivalue = e ? 1 : 0;
    GPP_WriteInteger(id,ivalue);
    // updateFramesMinute()
}

function handleChange(value: any) {
    GPP_WriteInteger(171, value);
}

function diskLeftSize(VideoPath: string) {
    return mbToSize(gamepp.readDiskSpace.sync(VideoPath));
}
function mbToSize (mb: string | number | Promise<number>) {
    if (mb === 0) return '0 MB';
    if (mb < 1) return mb + ' MB';
    const k = 1024;
    const sizes = [' MB', ' GB', ' TB'];
    const i = Math.floor(Math.log(mb) / Math.log(k));
    return (Math.round((mb / Math.pow(k, i)) * 100)) / 100 + sizes[i];
}
//文件路径选择
function GPP_ShowOpenDialog(title: any,id: number){
    gamepp.dialog.showOpenDialog.async(
        (value: { [x: string]: string[]; }[]) => ShowOpenDialogCallback(value[0], id),
        { title: title, properties: ["openDirectory"] }
    );
}

function ShowOpenDialogCallback(Path: { [x: string]: string[]; },id: number) {
    if (!Path['canceled']) {
        let regex = /^[a-zA-Z]:(([a-zA-Z]*)||([a-zA-Z]*\\))*/;
        let array = regex.exec(Path['filePaths'][0]);
        if (array === null) {ElMessage.error('错误');return;}
        // data.filePath = Path['filePaths'][0];
        if (id === 5) {
            GPP_RefreshPicturePath(Path['filePaths'][0]);//截图保存路径
        }
        gamepp.setting.setString.promise(id, Path['filePaths'][0]);
    }
}

function GPP_RefreshPicturePath(PicturePath: string) {
    data.filePath = PicturePath;
    GPP_WriteString(5, PicturePath);
}
async function GPP_WriteString(id: number, value: string) {
  console.log('setString,' + '' + id + ',' + value);
  try {
    await gamepp.setting.setString.promise(id, value);
  } catch (error) {
  }
}

const GPP_OpenPicturePath = () => {
    const value = data.filePath;
    const szSendData = {};
    szSendData['value'] = value;
    gamepp.shell.openPath(szSendData['value'])
}

let zoomValue = ref<number>(1)
async function initZoom() {
  try {
    zoomValue.value = await gamepp.display.getScaleFromWindowInMonitor.promise();
    const zoomWithSystem = gamepp.setting.getInteger.sync(313)
    if (zoomWithSystem === 1) {
      // 设置body zoom
      document.body.style.zoom = zoomValue.value
      gamepp.webapp.windows.resize.sync('screenshot',Math.floor(842 * zoomValue.value),Math.floor(542 * zoomValue.value))
    }
  }catch (e) {
    zoomValue.value = 1
  }

  try {
    gamepp.display.onDisplayMetricsChanged.addEventListener( async (scaleFactor:number)=>{
      const zoomWithSystem = gamepp.setting.getInteger.sync(313)
      if (zoomWithSystem === 1) {
        console.log('display',scaleFactor)
        // zoom.value = display.scaleFactor
        zoomValue.value = scaleFactor
        document.body.style.zoom = zoomValue.value
        try{
          gamepp.webapp.windows.resize.sync('screenshot',Math.floor(842 * zoomValue.value),Math.floor(542 * zoomValue.value))
        }catch (e) {
          console.log(e)
        }
      }
    })
  }catch (e) {
    console.log(e)
  }
}

onBeforeMount(()=>{
    getVersion()
    initZoom()
})
</script>

<style lang="scss" scoped>
.screenshot_layout {
    width: 830px;
    height: 530px;
    position: relative;
    border-radius: 4px;
    background: #22232E;
    margin-left: 6px;
    margin-top: 6px;
    box-shadow:0 1px 6px rgba(0,0,0,.6);
    overflow: hidden;

    .windowName {
        margin-right: 10px;
    }

    :deep(.el-checkbox__inner) {
        --el-checkbox-bg-color: transparent;
    }

    :deep(.el-radio__inner) {
        --el-radio-input-bg-color: transparent;
    }
}

.screenshot_content {
    padding: 10px;
    display: flex;
    flex-flow: row nowrap;
    gap: 10px;
    width: 830px;

    .screenshot_content_left,.screenshot_content_right {
        display: flex;
        flex-flow: column nowrap;
        gap: 10px;
    }

    .screenshot_content_item {
        width: 400px;
        border-radius: 4px;
        padding: 10px;
        background: #2B2C37;
        box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
    }

    .shortcut_box {
        height: 90px;
    }
    .setting {
        height: 180px;
    }
    .image_type {
        height: 160px;
        span{
            color: #999999;
            font-size: 12px;
        }
        em{
            font-style: normal;
            color: #508DE2;
            font-size: 12px;
        }
        .format{
            color: #fff;
        }
    }
    .savePath {
        height: 110px;
    }

    .s-title {
        color: #ffffff;
        font-size: 12px;
    }
    .s-row{
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 11px 0 11px 0;
        .s-row-title{
            color: #777777;
            font-size: 12px;
        }
    }
    .storage{
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 20px;
        .route{
            width: 232px;
            height: 30px;
            border-radius: 4px;
            background: #22232E;
            border: none;
            color: #fff;
            font-size: 12px;
            text-align: left;
            padding-left: 15px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .button{
            // width: 48px;
            height: 30px;
            border-radius: 4px;
            background: #336AB5;
            font-size: 12px;
            color: #FFFFFF;
            border: none;
            cursor: pointer;
            padding: 0 10px;
            max-width: 70px;
        }

    }
    .Size{
        display: flex;
        align-items: center;
        .state{
            width: 6px;
            height: 6px;
            background: #35D57D;
            border-radius: 50%;
            margin-right: 5px;
            margin-top: 5px;
        }
        span{
            color: #999999;
            font-size: 12px;
        }
    }
    .hover:hover{
        filter: brightness(1.2);
        -webkit-filter: brightness(1.2);
        -webkit-tap-highlight-color: transparent;
    }
    .Wrapframe{
        margin-top: 15px;
        em{
            font-style: normal;
        }

        :deep(.el-checkbox__input.is-checked+.el-checkbox__label) {
            line-height: 1.2em;
        }
    }
    .frames-btn1{
        width: 54px;
        height: 30px;
        border-radius: 4px;
        background: #22232E;
        border: none;
        font-size: 12px;
        color: #777777;
        margin: 0 5px;
        text-align: center;
    }
    .frames-btn1:focus-visible{
        outline: none;
    }
    .Tips{
        color: #777777;
    }
}
</style>
<style>
.screenshot_content .el-checkbox__label{
    color: #ffffff!important;
    font-size: 12px;
}
.screenshot_content .el-radio{
    height: 35px!important;
}
.screenshot_content .el-checkbox{
    height: 35px!important;
}
.screenshot_content .el-radio__label{
    font-size: 12px!important;
    width: 348px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.screenshot_content  .Splicestyle{
    height: 50px!important;
    margin-bottom: 5px;
}
.screenshot_content .el-checkbox__original:focus-visible{
    outline: none;
}
.screenshot_content  .el-input__inner{
    text-align: center;
}
.screenshot_content .el-radio,.el-checkbox{
    white-space: normal!important;
    min-height: 35px;
    width: 100%;
}
</style>
