<template>
    <!--游戏设置保存-->
    <div id="yx"></div>
    <el-collapse-item :title="$t('Setting.ingameSetting')" name="9">
      <div class="setting-item">
        <section class="left-box">
          <el-checkbox @change="set475" v-model="store.state.ingameSettingSave">
            <span style="color: var(--font-color);font-size: .12rem;">{{$t('Setting.enableInGameSettingsSaving')}}</span>
          </el-checkbox>
          <br>
          <text>{{$t('Setting.text8')}}</text>
        </section>
      </div>
    </el-collapse-item>
  </template>

<script setup lang="ts">
import {gameppBaseSetting} from '@/modules/Game_Home/stores/index'
import {useScroll} from '../hooks/useScroll'
useScroll('yx');
const store = gameppBaseSetting()
function set475(){
  store.actions.setIngameSettingSave(store.state.ingameSettingSave)
}
</script>

<style scoped lang="scss">
#yx {
    margin-top: 20px;
}
</style>
