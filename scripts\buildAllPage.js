import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
let __dirname = dirname(__filename);

const modulesDir = path.join(__dirname, '/..','src', 'modules');
const inGameDir = path.join(__dirname, '/..','src', 'inGame');
const onDeskDir = path.join(__dirname, '/..','src', 'onDesk');

// 读取目录下的所有文件夹
const modules = fs.readdirSync(modulesDir).filter(file => {
    return fs.statSync(path.join(modulesDir, file)).isDirectory();
});

const inGame = fs.readdirSync(inGameDir).filter(file => {
    return fs.statSync(path.join(inGameDir, file)).isDirectory();
})
const onDesk = fs.readdirSync(onDeskDir).filter(file => {
    return fs.statSync(path.join(onDeskDir, file)).isDirectory();
})

const arr = [...modules, ...inGame,...onDesk];
// const arr = [...modules];

// 遍历数组，执行命令
arr.forEach(module => {
    const command = `npm run build --page=${module}`;
    console.log(`执行命令: ${command}`);
    try
    {
        execSync(command, { stdio: 'inherit' });
    }
    catch (error)
    {
        console.error(`执行命令失败: ${command}`);
        // console.error(error.stderr.toString());
        process.exit(-1)
    }
});




