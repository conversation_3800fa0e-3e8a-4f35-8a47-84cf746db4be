<script setup lang="ts">
import {computed, defineProps, inject, onMounted, reactive, ref} from 'vue'
import * as echarts from "echarts";
import { useI18n } from 'vue-i18n';
import { gamepp } from 'gamepp'
import RightTopIcons from "@/components/mainCom/RightTopIcons.vue";
import HistoryBox from './components/HistoryBox.vue'
import { AVGNum,MaxNum,MinNum,FormatSeconds,FormatTimeCn,FormatTime,
         FormatTimePlus, DiskCapacityConversion,FormartMonthToNumber,RemoveAllSpace,RegExGetNum,gpu_brand,
         showNumberOfCPUCores,showNumberOfLogicalCpus,findMostFrequent} from '../../uitls/GameppTools'
import { ElMessage } from "element-plus";
import { FALSE } from 'sass';
//Benchmark
let isDownload = ref(false)

//Echarts折线图处理
let CPU_AXIS = ref(['','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','',])
let GPU_AXIS = ref(['','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','',])

let cpuTempStart = ref(40)
let cpuTempEnd = ref(40)

let cpuStableStart = ref(40)
let cpuStableEnd = ref(40)

let memStart = ref(40)
let memEnd = ref(40)

let doubleStart = ref(40)
let doubleEnd = ref(40)

let cpuType = ref('intel')
let moVersion = ref(0)

let cpu_chart:any
let gpu_chart:any

let cpu_options:any = ref({})
let gpu_options:any = ref({})

let recordSeond:any =ref(0)
let recordTimer:any = ref(null)

//稳定测试单双核心
let coreCount:any = ref(0)

let timer1:any = null
let compeleteTimer:any= null
let timer3:any= null
let timer4:any = null


let PASS:any = ref({
  0:{
    'TextRender':'UNTEST',
    'PDFRender':'UNTEST',
    'FFmpeg':'UNTEST',
    'NBody':'UNTEST',
    '7z':'UNTEST',
  },
  1:{
    'TextRender':'UNTEST',
    'PDFRender':'UNTEST',
    'FFmpeg':'UNTEST',
    'NBody':'UNTEST',
    '7z':'UNTEST',
  }

})

let TestInfo:any = ref({
  cpu_TDP:[],
  cpu_TEMP:[],
  cpu_LIMIT:[],
  cpu_clock:[],
  gpu_TDP:[],
  gpu_TEMP:[],
  gpu_clock:[],
  mem_clock:[],
  mem_temp:[],
  mem_error:0,
  mem_data:{},
  model:'',
})

let temp_limit_all = ref([
  
])

let limit_List_intel:any = ref([
  {
    name:'长时功耗限制',
    key:'PL1',
    value:'',
    type:'intel',
    count:0,
  },
  {
    name:'短时功耗限制',
    key:'PL1',
    value:'',
    type:'intel',
    count:0,
  },
  {
    name:'最大电流限制',
    key:'elecTri',
    value:'',
    type:'intel',
    count:0,
  },
  {
    name:'最大功耗',
    key:'PPT',
    value:'',
    type:'amd',
    max:0,
    count:0,
  },
  {
    name:'最大电流',
    key:'EDC',
    value:'',
    type:'amd',
    max:0,
    count:0,
  },
  {
    name:'最大温度',
    key:'Thermal Limit',
    value:'',
    type:'amd',
    max:0,
    count:0,
  }
])

const saveLimitInfo = () =>
{
  limit_List_intel.value[0].value = bg_sensor_data.value['cpu']['limitAll']['PL1'][1]
  limit_List_intel.value[1].value = bg_sensor_data.value['cpu']['limitAll']['PL23'][1]
  limit_List_intel.value[2].value = bg_sensor_data.value['cpu']['limitAll']['elecTri'][1]
  limit_List_intel.value[3].value = bg_sensor_data.value['cpu']['limitAll']['PPT'][1]

  if( limit_List_intel.value[3].value> limit_List_intel.value[3].max)
  {
    limit_List_intel.value[3].max = limit_List_intel.value[3].value
  }

  limit_List_intel.value[4].value = bg_sensor_data.value['cpu']['limitAll']['EDC'][1]
   if( limit_List_intel.value[4].value> limit_List_intel.value[4].max)
  {
    limit_List_intel.value[4].max = limit_List_intel.value[4].value
  }

  limit_List_intel.value[5].value = bg_sensor_data.value['cpu']['amd_thermal']
   if( limit_List_intel.value[5].value> limit_List_intel.value[5].max)
  {
    limit_List_intel.value[5].max = limit_List_intel.value[5].value
  }

  console.log('%climit_List_intel.value::','color:green',limit_List_intel.value);
}

const saveTestInfo = async () =>
{
  TestInfo.value.cpu_TDP.push(bg_sensor_data.value['cpu']['power'])
  TestInfo.value.cpu_TEMP.push(bg_sensor_data.value['cpu']['temp'])
  TestInfo.value.cpu_LIMIT.push(bg_sensor_data.value['cpu']['limit'])
  TestInfo.value.cpu_clock.push(bg_sensor_data.value['cpu']['clock'])
  TestInfo.value.gpu_TEMP.push(bg_sensor_data.value['gpu']['temp'])
  TestInfo.value.gpu_clock.push(bg_sensor_data.value['gpu']['clock'])
  TestInfo.value.gpu_TDP.push(bg_sensor_data.value['gpu']['power'])
  if(bg_sensor_data.value['memory']['temp'] != null)
  {
   TestInfo.value.mem_temp.push(bg_sensor_data.value['memory']['temp'])
  }
}

let openHistory:any = ref(false)
let CmdListX:any = ref([])
let CpuThreadsStage:any = ref(0)
let DatabaseId:any = null
let StartTimestampTable:any
let curModel  = ref(0)
let HardwareInfo:any = ref({});
let cpuBase:any = ref({
  core:0,
  process:0,
  TjMax:0
})

let gpuBase:any = ref({
  drive:0,
  memSize:0,
})
let isTesting = ref(false)
let isDDR5  = ref(false)
let memoryInfo:any = ref([])
let performanceArr:any = ref([

])
let performance:any = ref([
  // {
  //   time:'',
  //   reason: []'PPT LMIRR',

  // }
])
//
let cpu_temp_info:any = ref({
    tempArr:[],
    maxtdp:0,
    tdpARrr:[],
    max:0,
    cost:0,
    cpulimit:0,
    minClock:0
})
//
let cpu_stable_info:any = ref({
    tempArr:[],
    max:0,
    cost:0,
    cpulimit:0,
    cpuerror:0,
    curRound:0,
    error:false,
    curCMD:'TextRender'
  })

let mem_stable_info:any = ref({
    mem_create_error:0,
    mem_current_task:0,
    mem_loop_count:0,
    cpu_error:0,
    mem_st00_error:0,
    mem_st01_error:0,
    mem_st02_error:0,
    mem_st04_error:0,
    mem_st08_error:0,
    mem_st016_error:0,
    mem_st032_error:0,
    mem_st064_error:0,
  })

let double_info:any = ref({
    tempArr:[],//cpu
    tempArr2:[],//gpu
    tdpARrr:[],
    max:0,
    maxgpu:0,
    cost:0,
    cpulimit:0,
    minClock:0
})

let bg_sensor_data:any = ref({
  cpu:{
    name:''
  },
  gpu:{
    name:''
  },
  memory:{
    channel:'',
    clock:0,
    size:0,
    tcas:0,
  }
})



let ModelList:any = ref([
  {
    min:10,
    choosen:false,
    tested:false,
    status:0,
    type:'cpu_temp',
    seconds:0
  },
  {
    round:1,
    choosen:false,
    tested:false,
    status:0,
    type:'cpu_stable',
    seconds:0
  },
  {
    round:1,
    choosen:false,
    tested:false,
    status:0,
    type:'memory_stable',
    seconds:0
  },
  {
    round:1,
    choosen:false,
    tested:false,
    status:0,
    type:'cpu&gpu',
    seconds:0
  }
])

let gpuInfo:any = ref([
{
  name:'温度',
  keys:'temp',
  cur:0,
  max:0,
  unit:'°C',
  showseries:true,
  color:'rgba(243, 103, 103, 1)',
  choosen:true
},
{
  name:'显存温度',
  keys:'mem_temp',
  cur:0,
  max:0,
  unit:'°C',
  showseries:true,
  color:'rgba(255, 144, 144, 1)',
  choosen:true
},
{
  name:'频率',
  keys:'mem_temp',
  cur:0,
  max:0,
  unit:'MHZ',
  showseries:true,
  color:'rgba(253, 139, 67, 1)',
  choosen:true
},
{
  name:'显存频率',
  keys:'mem_temp',
  cur:0,
  max:0,
  unit:'MHZ',
  showseries:true,
  color:'rgba(255, 115, 0, 1)',
  choosen:true
},
{
  name:'D3D占用',
  keys:'mem_temp',
  cur:0,
  max:0,
  unit:'%',
  showseries:true,
  color:'rgba(255, 232, 80, 1)',
  choosen:true
},
{
  name:'Total占用',
  keys:'mem_temp',
  cur:0,
  max:0,
  unit:'%',
  showseries:true,
  color:'rgba(133, 93, 255, 1)',
  choosen:true
},
{
  name:'显存占用',
  keys:'mem_temp',
  cur:0,
  max:0,
  unit:'%',
  showseries:true,
  color:'rgba(189, 129, 249, 1)',
  choosen:true
},
{
  name:'TDP',
  keys:'mem_temp',
  cur:0,
  max:0,
  unit:'W',
  showseries:true,
  color:'rgba(80, 184, 249, 1)',
  choosen:true
},
{
  name:'风扇转速',
  keys:'mem_temp',
  cur:0,
  max:0,
  unit:'RPM',
  showseries:true,
  color:'rgba(37, 255, 233, 1)',
  choosen:true
},

])
let cpuInfo:any = ref([
{
  name:'温度',
  keys:'mem_temp',
  cur:0,
  max:0,
  unit:'°C',
  showseries:true,
  color:'rgba(243, 103, 103, 1)',
  choosen:true
},
{
  name:'P-频率',
  keys:'mem_temp',
  cur:0,
  max:0,
  unit:'MHZ',
  showseries:true,
  color:'rgba(253, 139, 67, 1)',
  choosen:true
},
{
  name:'E-频率',
  keys:'mem_temp',
  cur:0,
  max:0,
  unit:'MHZ',
  showseries:true,
  color:'rgba(255, 232, 80, 1)',
  choosen:true
},
{
  name:'P-占用',
  keys:'mem_temp',
  cur:0,
  max:0,
  unit:'%',
  showseries:true,
  color:'rgba(85, 246, 179, 1)',
  choosen:true
},
{
  name:'E-占用',
  keys:'mem_temp',
  cur:0,
  max:0,
  unit:'%',
  showseries:true,
  color:'rgba(80, 184, 249, 1)',
  choosen:true
},
{
  name:'频率',
  keys:'cpu_clock',
  cur:0,
  max:0,
  unit:'MHZ',
  showseries:true,
  color:'rgba(253, 139, 67, 1)',
  choosen:true
},
{
  name:'占用',
  keys:'cpu_usage',
  cur:0,
  max:0,
  unit:'%',
  showseries:true,
  color:'rgba(255, 232, 80, 1)',
  choosen:true
},
{
  name:'电压',
  keys:'cpu_voltage',
  cur:0,
  max:0,
  unit:'V',
  showseries:true,
  color:'rgba(133, 93, 255, 1)',
  choosen:true
},
{
  name:'TDP',
  keys:'mem_temp',
  cur:0,
  max:0,
  unit:'W',
  showseries:true,
  color:'rgba(189, 129, 249, 1)',
  choosen:true
},
{
  name:'风扇转速',
  keys:'mem_temp',
  cur:0,
  max:0,
  unit:'RPM',
  showseries:true,
  color:'rgba(37, 255, 233, 1)',
  choosen:true
},
// {
//   name:'PPT Limit',
//   cur:0,
//   max:0,
//   unit:'%',
//   showseries:true,
// },
])
let msg_cpu_start = ref({
  "type"       : "STRESS",
  "cmd"        : "Start",	// 开始 msg_cpu_start
  "device"     : "CPU",   // CPU
  "isAVX"      : 1,       // 使用AVX2
  "mixGPU"     : 0,       // 为显卡保留2GB内存
  "containMem" : 0,       // 是否测试内存
  "nLevel"     : 10,       // 等级 1-10 越大占用越高
  "offsetCores": 0,       // 保留核心，为GPU测试保留CPU，从Core 0 开始
  "minRuns"    : 999999,  // 总测试次数（minTime * minRuns = 总测试时间）
  "minTime"    : 1000     // 单次测试持续时间 毫秒
})

let msg_mem_start = ref({
  "type"       : "STRESS",
  "cmd"        : "Start",	
  "device"     : "CPU",   
  "isAVX"      : 1,       
  "mixGPU"     : 0,       
  "containMem" : 1,       
  "nLevel"     : 10,       
  "offsetCores": 0,       
  "minRuns"    : 999999,  
  "minTime"    : 1000     
})


let msg_gpu_start = ref({
  "type"       : "STRESS",
  "cmd"        : "Start",	// 开始
  "device"     : "GPU",   // GPU
  "offsetCores": 0,       // 专属核心，从Core 0开始
  "width"      : 0,    // 输出窗口 宽 默认0
  "height"     : 0,    // 输出窗口 高 默认0
  "fullscreen" : 1,       // 是否全屏
  "raytracing" : 0,       // 是否光追模式
  "loop"       : 999999,  // 循环次数
  "gpu"        : 0  // GPU LUID
})

let msg_cpu_stop = {
  "type"  : "STRESS",
  "cmd"   : "Stop",		// 停止
  "device": "CPU"     // CPU
}

let msg_gpu_stop = {
  "type"  : "STRESS",
  "cmd"   : "Stop",		// 停止
  "device": "GPU"      // GPU
}


onMounted(async() =>
{
  
  // initColor()
  init()
  clearInfo()
  setInterval(() => 
  {
    getBgSensorData()
    if(!isTesting.value)
    {
       //数据存储 处理报错
      for(const item of  ModelList.value)
      {
        item.status = 0
        item.choosen = false
      }
    }
  }, 1000);

  setChartsHWINFO()

  let bm_version = await gamepp.package.getversion.promise("PressureTest")
  moVersion.value = bm_version.version
})



const init = async () =>
{
    try 
    {
      HardwareInfo.value = await gamepp.hardware.getBaseJsonInfo.promise();
      let Hwinfo = JSON.parse(HardwareInfo.value)
      console.warn('Hwinfo:',Hwinfo);

      cpuBase.value['TjMax'] = Hwinfo.CPU.SubNode[0]['CPUMax.JunctionTemperature(Tj,max)']
      cpuBase.value['core'] = Hwinfo.CPU.SubNode[0]['NumberofCPUCores']
      cpuBase.value['process'] = Hwinfo.CPU.SubNode[0]['NumberofLogicalCPUs']

      let gpu_index = JSON.parse(localStorage.getItem('gpu_index') as any)

      gpuBase.value['drive'] = Hwinfo.GPU.SubNode[gpu_index]['DriverVersion']
      gpuBase.value['memSize'] =  gpu_memory_size_type(Hwinfo.GPU.SubNode[gpu_index])

      

      gamepp.benchmark.onBenchMarkMessage.addEventListener(value => {BenchMarkMessageProcessNew(value);})
    } catch {}
   
    let AppDataDir = await gamepp.getAppDataDir.promise();
    DatabaseId = await gamepp.database.open.promise(AppDataDir + '\\common\\Data\\GamePPStressNew.dll');
    //创建压力测试表
    let status = await gamepp.database.exists.promise(DatabaseId, 'list');
    if (!status) 
    {
      await gamepp.database.create.promise(DatabaseId, "list", '("id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT, "starttime" INTEGER, "endtime" INTEGER, "normal_end" INTEGER,"memTemp" TEXT,"gpuD3D" INTEGER,"gpuhot" INTEGER,"model" TEXT,"test_model" INTEGER,"test_list" TEXT, "bm_version" TEXT, "gpp_version" TEXT,"gpu_index" INTEGER, "hd_info" TEXT,"cpu_list_data" TEXT, "gpu_list_data" TEXT, "memory_list_data" TEXT,"cpu_end_data" TEXT, "gpu_end_data" TEXT,"gpu_loop_data" TEXT, "cpu_error" INTEGER, "memory_error" INTEGER, "whea_error" INTEGER, "rounds" INTEGER, "real_time" INTEGER, "real_list_data" TEXT,"comprehensive_data" TEXT)');
    }
    //注册全局ESC

   
}

const chooseMode = (index:number) =>
{
  if(isTesting.value)return
  ModelList.value[index].choosen = !ModelList.value[index].choosen
}

const gpu_memory_size_type = (data: any) => {
    const VideoMemory = (data.VideoMemory).split(' ')
    let n = 1024
    if (data.VideoMemory.includes('MBytes')) {
        n = 1024
    } else if (data.VideoMemory.includes('KBytes')) {
        n = 1024*1024
    } else if (data.VideoMemory.includes('GBytes')) {
        n = 1
    }
    return Math.ceil(((VideoMemory[0] / n))) + 'GB'
}

const getBgSensorData = () =>
{

  const cpuKeys = ['temp', 'clockP', 'clockE', 'usageP', 'usageE', 'clock', 'usage', 'voltage', 'power', 'fan'];

  const gpuKeys = ['temp', 'mem_temp', 'clock', 'mem_clock', 'd3d_usage', 'total_usage','mem_usage','power', 'fan'];

  bg_sensor_data.value = JSON.parse(localStorage.getItem('bg_sensor_data') as any)

  let cpuname  = bg_sensor_data.value['cpu']['name']

  if (cpuname.toLowerCase().includes('intel')) 
  {
    cpuType.value = 'intel'
  } 
  else 
  {
    cpuType.value = 'amd'
  }

  function updateMaxValue(index:any, key:any) 
  {
    const curValue = bg_sensor_data.value['cpu'][key];

    cpuInfo.value[index].cur = curValue;

    if (curValue > cpuInfo.value[index].max) 
    {
      cpuInfo.value[index].max = curValue;
    }

    let hasbig = true
    if(bg_sensor_data.value['cpu']['usageP'] === 0 && bg_sensor_data.value['cpu']['usageE'] === 0)
    {
       hasbig = false
    }

    if(bg_sensor_data.value['cpu']['usageP'] === null && bg_sensor_data.value['cpu']['usageE'] === null)
    {
       hasbig = false
    }
    if(!hasbig)
    {
        cpuInfo.value[1].cur = 'undefined'
        cpuInfo.value[2].cur = 'undefined'
        cpuInfo.value[3].cur = 'undefined'
        cpuInfo.value[4].cur = 'undefined'
    }
    else
    {
        cpuInfo.value[5].cur = 'undefined'
        cpuInfo.value[6].cur = 'undefined'
    }

    if(cpuInfo.value[index].cur !== 'undefined')
    {
      updateChartsInfo(cpuInfo. value[index].name, index,cpuInfo.value[index].cur);
    }
  }

  console.log('%c CPUINFO::','color:green',cpuInfo.value);
  console.log('%c GPUINFO::','color:green',gpuInfo.value);
  console.log('%c limit_List_intel::','color:green',limit_List_intel.value);

  for (let i = 0; i < cpuKeys.length; i++) 
  {
    updateMaxValue(i, cpuKeys[i]);
  }

  function updateGpuMaxValue(index:any, key:any) 
  {
    const curValue = bg_sensor_data.value['gpu'][key];
    gpuInfo.value[index].cur = curValue;
    if (curValue > gpuInfo.value[index].max) 
    {
      gpuInfo.value[index].max = curValue;
    }
    updateChartsInfo2( index,gpuInfo.value[index].cur);
  }

  for (let i = 0; i < gpuKeys.length; i++) 
  {
    updateGpuMaxValue(i, gpuKeys[i]);
  }


  handleMemory()
  // console.log('%c CPUINFO::','color:green',cpuInfo.value);
  // console.log('%c GPUINFO::','color:orange',gpuInfo.value);
  saveLimitInfo()
}
 
const handleMemory = () =>
{
  if(bg_sensor_data.value['memory']['ddr5_temp']['#1'] == null && bg_sensor_data.value['memory']['ddr5_temp']['#2'] == null)
  {
    isDDR5.value = false
  }
  else
  {
    isDDR5.value = true;
    for (const key in bg_sensor_data.value['memory']['ddr5_temp']) {
        const currentValue = bg_sensor_data.value['memory']['ddr5_temp'][key];
        let existingObj = memoryInfo.value.find((item:any) => item.key === key);

        if (existingObj) {
          // 如果已经存在该key，更新值并比较最大值
          existingObj.value = currentValue;
          existingObj.max = Math.max(existingObj.max, currentValue);
        } else {
          // 如果不存在该key，添加新对象
          memoryInfo.value.push({
            key: key,
            value: currentValue,
            max: currentValue
          });
        }
      }
  }
}

const selectCharts = (name:any) =>
{
  console.warn('cpu_options::',cpu_options.value);
  console.warn('cpu_info::',cpuInfo.value);
  let serIndex = cpu_options.value.series.findIndex((item:any) =>
  {
    return item.name == name
  })
  
  console.warn('serIndex::',serIndex);
  
  // cpu_options.value.series[serIndex].data = []
  cpu_options.value.series[serIndex].choosen = !cpu_options.value.series[serIndex].choosen
}

const startPressure = async () => 
{
  //清空最大值
  for(const item of cpuInfo.value)
  {
    item.max = 0
  }

  for(const item of gpuInfo.value)
  {
    item.max = 0
  }

  let choosen = ModelList.value.some((item:any)=>{
    return item.choosen == true
  })
  if(!choosen)
  {
    ElMessage({
               message: '请选择模式',
               type: 'warning',
               grouping:true
      })
    return
  }
  
  recordTimer.value = setInterval(() => 
  {
     recordSeond.value++
  }, 1000);

    await gamepp.utils.registerGlobalHotKey.promise('ESC');
    gamepp.utils.globalHotKeyTraggerInfo.addEventListener(async value => {if (value === 'ESC') 
    {
      
      await stopPressure()
      clearInterval(saveTimer)
      //测试结束初始化
      cpu_temp_info.value.cost == 0
      cpu_temp_info.value.tempArr = []
      cpu_temp_info.value.max = 0
      cpu_stable_info.value.cost == 0
      cpu_stable_info.value.tempArr = []
      cpu_stable_info.value.max = 0
      initModelList()
    }});
  let saveTimer = setInterval(() => 
  {
    saveTestInfo()
  }, 1000);
  //存储list_data数据

  isTesting.value = true
  // 创建任务队列
  const taskQueue = ModelList.value.filter((item:any) => item.choosen);
  if(taskQueue.length == 0)return
  
   //Run benchmark
   let isExit = await gamepp.benchmark.isBenchMarkRunning.promise();
   
    if (!isExit) 
    {
      await gamepp.benchmark.setBenchMarkGpuPreferences.promise();
      await gamepp.benchmark.runBenchMarkClient.promise();
      await gamepp.preventSleep.promise();
    }
  StartTimestampTable = Date.now()//创建全局时间戳

  let ListDatabase = { "model": "", "bm_version": "", "gpp_version": "", "hd_info": "" };
  let bm_version = await gamepp.package.getversion.promise("PressureTest")
  moVersion.value = bm_version.version
  ListDatabase['bm_version'] = bm_version.version
  ListDatabase['gpp_version'] = gamepp.getPlatformVersion.sync()
  console.log('%cListDatabase','color:green',ListDatabase);
  
  let Str =  await gamepp.hardware.getBaseJsonInfo.promise();
  console.warn('Str::',Str);
  
  try 
  {
    let gpu_index = JSON.parse(localStorage.getItem('gpu_index') as any)
    let Data_Field = ["starttime", "model","test_list", "bm_version","gpp_version","gpu_index", "hd_info"];
    let Data_Content = [[StartTimestampTable, ListDatabase['model'], '', ListDatabase['bm_version'], ListDatabase['gpp_version'], gpu_index, encodeURIComponent(Str)]];//时间戳插入表
    await gamepp.database.insert.promise(DatabaseId, "list", Data_Field, Data_Content);//列表数据
    //创建详情数据表
    let statusD = await gamepp.database.exists.promise(DatabaseId, StartTimestampTable);
    if (!statusD) 
    {
      await gamepp.database.create.promise(DatabaseId, "'" + StartTimestampTable + "'", '("id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,"AMD_LIMIT" TEXT,"memTemp" TEXT,"gpuD3D" INTEGER,"gpuhot" INTEGER, "model" INTEGER, "cpuload" INTEGER, "cputemp" INTEGER, "cpuclock" INTEGER, "cpuclockP" INTEGER, "cpuclockE" INTEGER,"cpuclock_all" TEXT, "cpuTDP" INTEGER, "cpuvoltage" REAL, "cpufan" INTEGER, "gpuload" INTEGER, "gputemp" INTEGER, "gpuclock" INTEGER, "gpuTDP" INTEGER, "gpuvoltage" REAL, "gpufan" INTEGER,"gpuvramtemp" INTEGER,"gpuReliabilityVoltage" INTEGER,"gpuMaxOperatingVoltage" INTEGER, "performance" TEXT)');
    }
  } 
  catch(err) 
  {                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         
    console.log(err);
  }

  // 依次执行任务队列中的任务
  for (let i = 0; i < taskQueue.length; i++) 
  {
    const task = taskQueue[i];
   //------------------------CPU温度测试----------------------------//
    if (task.type === 'cpu_temp') 
    {
      curModel.value = 0
      cpu_temp_info.value.tempArr = []
      console.warn('开始温度测试');
       ModelList.value[0].tested = true
      cpuTempStart.value = cpu_options.value.series[0].data.length //确定起点

       timer1  = setInterval(() => {
        if(!isTesting.value)return
        //
        ModelList.value[0].seconds++
        //
        cpuTempEnd.value = cpu_options.value.series[0].data.length
        
        cpu_options.value.series[0].markArea = 
        {
          itemStyle: 
          {
            color: 'rgba(255, 173, 177, 0.4)'
          },
          data: 
          [
            [
              {
                name: 'CPU温度测试',
                xAxis: cpuTempStart.value
              }, 
              {xAxis: cpuTempEnd.value}
            ],
          ]
        }
        //cpu limit
        let PlrArr = bg_sensor_data.value['cpu']['limit'];//limit arr
        PlrArr.forEach((itemDetail:any) => 
        {
          let infoKey = Object.keys(itemDetail)[0];
          let infoValue = itemDetail[infoKey];
          if (infoValue === 1 || infoValue >= 99) 
          {
            let performanceObj:any = {};
            performanceObj[infoKey] = infoValue;
            performance.value.push(performanceObj);
          }
        })

      cpu_temp_info.value.cpulimit  = performanceArr.value.length
        //max temp
      if(bg_sensor_data.value['cpu']['temp']>cpu_temp_info.value.max)
      {
        cpu_temp_info.value.max = bg_sensor_data.value['cpu']['temp']
        cpu_temp_info.value.tempArr.push(bg_sensor_data.value['cpu']['temp'])
        //寻找最大值
        let index = cpu_temp_info.value.tempArr.findIndex((item:any)=>
        {
          return item == cpu_temp_info.value.max
        })
        console.warn('cpu_temp_info',cpu_temp_info.value);
        console.warn('max_index',index);

        if(index != -1)
        {
          cpu_temp_info.value.cost = index
        }
      }

      if(cpu_temp_info.value.minClock == 0)
      {
        cpu_temp_info.value.minClock = bg_sensor_data.value['cpu']['clock']
      }
      //min clock
      if(bg_sensor_data.value['cpu']['clock']<cpu_temp_info.value.minClock)
      {
        cpu_temp_info.value.minClock = bg_sensor_data.value['cpu']['clock']
      }

        AddDataToList(DatabaseId,0,StartTimestampTable)
      }, 1000);
      // 发送CPU温度测试消息
      msg_cpu_start.value['isAVX'] = 1
      ModelList.value[0].status = 1
     
      
      await SendBenchmarkCMD(msg_cpu_start.value)

      await new Promise(resolve => 
      {
        cpuTempEnd.value = cpu_options.value.series[0].data.length
        setTimeout(async () => {
          clearInterval(timer1)
          timer1 = null
          ModelList.value[0].status = 2
          //停止测试
          await SendBenchmarkCMD(msg_cpu_stop)
          resolve(true);
        }, task.min * 60000);
      });
    }
 //------------------------CPU稳定性测试----------------------------//
    if(task.type === 'cpu_stable')
    {
      ModelList.value[1].tested = true
      cpuStableStart.value = cpu_options.value.series[0].data.length
      
      //记录传感器数据
      let timer2:any  = setInterval(() => {
        if(!isTesting.value)return
        //
        ModelList.value[1].seconds++
        //
        cpuStableEnd.value = cpu_options.value.series[0].data.length 
        cpu_options.value.series[1].markArea = 
        {
          itemStyle: {
            color: 'rgba(85, 246, 179, 0.4)'
          },
          data: [
            [
              {
                name: 'CPU稳定性测试',
                xAxis: cpuStableStart.value
              },
              {
                xAxis: cpuStableEnd.value
              }
            ],
          ]
        }

        if(bg_sensor_data.value['cpu']['temp']>cpu_stable_info.value.max)
        {
          cpu_stable_info.value.max = bg_sensor_data.value['cpu']['temp']
          cpu_stable_info.value.tempArr.push(bg_sensor_data.value['cpu']['temp'])
          //寻找最大值
          let index = cpu_stable_info.value.tempArr.findIndex((item:any)=>{
            return item == cpu_stable_info.value.max
          })
          console.warn('cpu_stable_info',cpu_stable_info.value);
          console.warn('max_index',index);
          if(index != -1)
          {
            cpu_stable_info.value.cost = index
          }
        }
        AddDataToList(DatabaseId,1,StartTimestampTable)
      }, 1000);
      ModelList.value[1].status = 1
      let CmdList = [ '单核TextRender', '单核PDFRender', '单核FFmpeg', '单核NBody', '单核7z','多核TextRender', '多核PDFRender', '多核FFmpeg', '多核NBody', '多核7z', ];

      function repeatArray(arr:any, x:any) 
      {
        let result:any = [];
        for (let i = 0; i < x; i++) {
          result = result.concat(arr); 
        }
        return result;
      }

      CmdListX.value = repeatArray(CmdList, task.round);
      console.warn('测试轮次列表',CmdListX.value);
      let obj:any = {};
      obj['type'] = 'BENCHMARK'
      obj['cmd'] = 'TextRender'
      obj['threads'] = 0;
      obj['priority'] = 0;
      await SendBenchmarkCMD(obj);
      //TEXTRENDER
      PASS.value[coreCount.value][cpu_stable_info.value['curCMD']] = 'PASS'
      cpu_stable_info.value['curCMD'] = CmdListX.value[CpuThreadsStage.value]
      await new Promise(async resolve => 
      {
        compeleteTimer = setInterval(() => {
          if(CpuThreadsStage.value >= CmdListX.value.length || cpu_stable_info.value['error'] == true)//产生报错立即停止
          {
                ModelList.value[1].status = 2
                console.log('%c','color:green','CPU STABLE TEST OVER');
                SendBenchmarkCMD(msg_cpu_stop)
                if(cpu_stable_info.value['error'])
                {

                }else
                {
                  cpu_stable_info.value['curCMD'] = '测试完毕'
                }
                CpuThreadsStage.value = 0
                resolve(true);
                clearInterval(timer2);
                clearInterval(compeleteTimer)
              

          }
        }, 1000);
      });
    }
 //------------------------内存稳定性测试----------------------------//
    if(task.type === 'memory_stable')
    {
      await SendBenchmarkCMD(msg_mem_start.value)
      ModelList.value[2].tested = true
        //Get Start Length
      memStart.value = cpu_options.value.series[0].data.length
      console.warn('开始内存稳定性测试');

      timer3  = setInterval(() => 
      {
        if(!isTesting.value)return
         //
          ModelList.value[2].seconds++
          //
        //Update End Length
        memEnd.value = cpu_options.value.series[0].data.length 
        console.log('memInfo:',memStart.value,memEnd.value);
        
        cpu_options.value.series[2].markArea = 
        {
          itemStyle: {
            color: 'rgba(86, 24, 144, 0.4)'
          },
          data: [
            [
              {
                name: '内存稳定性测试',
                xAxis: memStart.value
              },
              {
                xAxis: memEnd.value
              }
            ],
          ]
        }
        AddDataToList(DatabaseId,2,StartTimestampTable)
      }, 1000);

      ModelList.value[2].status = 1

        await new Promise(resolve => 
        {  
          setTimeout(() => {
            ModelList.value[2].status = 2
            resolve(true);
            clearInterval(timer3)
            timer3 = null
        

          }, task.round *  600000);
        });
    }
 //------------------------双烤综合稳定性测试----------------------------//
    if(task.type === 'cpu&gpu')
    {
      ModelList.value[3].tested = true
      console.warn('开始综合稳定性测试');
      doubleStart.value = cpu_options.value.series[0].data.length 
      
      timer4  = setInterval(() => 
      {
        if(!isTesting.value)return
          //
          ModelList.value[3].seconds++
          //
        doubleEnd.value = cpu_options.value.series[0].data.length 
        //cpu limit
        let PlrArr = bg_sensor_data.value['cpu']['limit'];//limit arr
        PlrArr.forEach((itemDetail:any) => 
        {
          let infoKey = Object.keys(itemDetail)[0];
          let infoValue = itemDetail[infoKey];
          if (infoValue === 1 || infoValue >= 99) 
          {
            let performanceObj:any = {};
            performanceObj[infoKey] = infoValue;
            performance.value.push(performanceObj);
          }
        })
      if(PlrArr.length >0)
      {
        double_info.value.cpulimit += 1
      }
        //max cpu temp
      if(bg_sensor_data.value['cpu']['temp']>double_info.value.max)
      {
        double_info.value.max = bg_sensor_data.value['cpu']['temp']
        double_info.value.tempArr.push(bg_sensor_data.value['cpu']['temp'])
        
      }
        //max gpu temp
      if(bg_sensor_data.value['gpu']['temp']>double_info.value.maxgpu)
      {
        double_info.value.maxgpu = bg_sensor_data.value['gpu']['temp']
        double_info.value.tempArr2.push(bg_sensor_data.value['gpu']['temp'])
        //寻找最大值
        let index = double_info.value.tempArr2.findIndex((item:any)=>
        {
          return item == double_info.value.maxgpu
        })
        
        console.warn('double_info',double_info.value);
        console.warn('max_index',index);

        if(index != -1)
        {
          double_info.value.cost = index
        }
      }

      if(double_info.value.minClock == 0)
      {
        double_info.value.minClock = bg_sensor_data.value['gpu']['clock']
      }
      //min clock
      if(bg_sensor_data.value['gpu']['clock']<double_info.value.minClock)
      {
        double_info.value.minClock = bg_sensor_data.value['cpu']['clock']
      }

        AddDataToList(DatabaseId,3,StartTimestampTable)
        console.warn(DatabaseId,StartTimestampTable);
      }, 1000);
      ModelList.value[3].status = 1
      msg_cpu_start.value.mixGPU = 1;
      msg_cpu_start.value.offsetCores = 4;
      let DisplayInfo = await gamepp.hardware.getDisplayCardInfo.promise();
      msg_gpu_start.value.width = DisplayInfo['Element'][0]['PelsWidth'];
      msg_gpu_start.value.height = DisplayInfo['Element'][0]['PelsHeight'];
      console.warn('DisplayInfo::',DisplayInfo);
      
      const doubleMsg = JSON.parse(JSON.stringify(msg_cpu_start.value))
      doubleMsg['offsetCores'] = 4
      console.warn('doubleMsg',doubleMsg);
      
      SendBenchmarkCMD(doubleMsg)
      SendBenchmarkCMD(msg_gpu_start.value)

        await new Promise(resolve => 
        {
          setTimeout(() => {
            clearInterval(timer4)
            timer4 = null
            ModelList.value[3].status = 2
            resolve(true);
            SendBenchmarkCMD(msg_cpu_stop)
            SendBenchmarkCMD(msg_gpu_stop)
          
         }, task.round *  600000);
        });
    }
  }
  TestInfo.value['model'] = ModelList.value
  await handle_cpu_list_data()
  if(isTesting.value)
  {
   isTesting.value = false
    await stopPressure()
    clearInterval(saveTimer)
    //测试结束初始化
    cpu_temp_info.value.cost == 0
    cpu_temp_info.value.tempArr = []
    cpu_temp_info.value.max = 0
    cpu_stable_info.value.cost == 0
    cpu_stable_info.value.tempArr = []
    cpu_stable_info.value.max = 0
    initModelList()
  }
 
 
};

const handle_cpu_list_data = async () =>
{

  TestInfo.value['mem_stable_info'] = mem_stable_info.value
  TestInfo.value['Pass'] =  PASS.value
  
  let cpu_list_dataStr = encodeURIComponent(JSON.stringify(TestInfo.value));
  await gamepp.database.update.promise(DatabaseId, "list", ['cpu_list_data="' + cpu_list_dataStr + '"'], 'starttime = "' + StartTimestampTable + '"');
  //处理list_data
  console.warn('%cTestInfo::','color:green',TestInfo.value);
}

async function IsReadyShowSendPage(sectionName:any, keyName:any, value = false)
    {
        return new Promise((resolve, reject) => {
            let nRet = false;
            let setInterval_getbool2 = setInterval(async () => {
                nRet = await gamepp.setting.getBool2.promise(sectionName, keyName, value);
                if (nRet) {
                    clearInterval(setInterval_getbool2);
                    resolve(1);
                }
            }, 100)
        })
    }

const clearEcharts = () =>
{
  console.warn('执行clearEcharts');
  
  for(let i = 0;i<10;i++)
   {
    try{
      cpu_options.value.series[i].markArea = 
        {
          itemStyle: 
          {
            color: 'rgba(0, 0, 0, 0)'
          },
          data: 
          [
            [
              {
                name: 'CPU温度测试',
                xAxis: 0
              }, 
              {xAxis: 0}
            ],
          ]
        }
        cpu_chart.setOption(cpu_options.value);
    }
   catch
   {
     console.warn('OUT OF LENGTH');
     
   }
}
}
  
const clearInfo = () =>
{
  cpu_temp_info.value = {
    tempArr:[],
    maxtdp:0,
    tdpARrr:[],
    max:0,
    cost:0,
    cpulimit:0,
    minClock:0
}

  cpu_stable_info.value = {
    tempArr:[],
    max:0,
    cost:0,
    cpulimit:0,
    cpuerror:0,
    curRound:0,
    error:false,
    curCMD:'TextRender'
  }

  mem_stable_info.value = {
    mem_create_error:0,
    mem_current_task:0,
    mem_loop_count:0,
    cpu_error:0,
    mem_st00_error:0,
    mem_st01_error:0,
    mem_st02_error:0,
    mem_st04_error:0,
    mem_st08_error:0,
    mem_st016_error:0,
    mem_st032_error:0,
    mem_st064_error:0,
  }

  double_info.value = {
    tempArr:[],//cpu
    tempArr2:[],//gpu
    tdpARrr:[],
    max:0,
    maxgpu:0,
    cost:0,
    cpulimit:0,
    minClock:0
}
}

const stopPressure = async() =>
{
  isTesting.value = false
  await SendBenchmarkCMD(msg_cpu_stop)
  await SendBenchmarkCMD(msg_gpu_stop)
  //结束所有UI状态并保存

  //判断测试时长 

  //数据存储 处理报错
   for(const item of  ModelList.value)
  {
    item.status = 0
    item.choosen = false
  }
  console.warn('ModelList.value',ModelList.value);
  TestInfo.value['model'] = ModelList.value
  await handle_cpu_list_data()
  //发送通知关闭

  //注销热键
  await gamepp.utils.unregisterGlobalHotKey.promise('ESC');
  //停止benchMark服务
  await gamepp.benchmark.stopBenchMarkClient.promise();
  performanceArr.value = []
  console.warn('pressure Stoped');
  isTesting.value = false

  clearInterval(recordTimer.value)
  clearInfo()
  clearEcharts()

  setTimeout(() => {
      for(const item of  ModelList.value)
      {
        item.status = 0
        item.choosen = false
      }
     clearEcharts()
  }, 100);
  recordTimer.value = null
  let timeArr:any = [timer1,compeleteTimer,timer3,timer4]
  for(let item of timeArr)
 {
     clearInterval(item)
     item = null
 }

  if(recordSeond.value < 60)
  {
    ElMessage({
               message: '测试时间过短，不生成报告',
               type: 'warning',
               grouping:true
      })
    initModelList()
    await gamepp.database.delete.sync(DatabaseId, "list", "starttime = " + StartTimestampTable + "");//列表数据
    return false
  }
  if(recordSeond.value < 60)return


  initModelList()


  let Endtime = Date.now()
  await gamepp.database.update.promise(DatabaseId, "list", ['endtime="' + Endtime + '"'], 'starttime = "' + StartTimestampTable + '"');
  let obj = 
  {
    StartTime:StartTimestampTable,
    EndTime:Endtime,

  }
  await gamepp.setting.setBool2.promise('window', 'pressureResult', false);
  gamepp.webapp.windows.show.sync('pressureResult', false);
  await IsReadyShowSendPage('window', "pressureResult", false);
  await gamepp.webapp.sendInternalAppEvent.promise('pressureResult', obj)

  TestInfo.value = {
      cpu_TDP:[],
      cpu_TEMP:[],
      cpu_LIMIT:[],
      cpu_clock:[],
      gpu_TDP:[],
      gpu_TEMP:[],
      gpu_clock:[],
      mem_clock:[],
      mem_temp:[],
      mem_error:0,
      mem_data:{},
      model:[],
  }
}

const initModelList = () =>
{
  msg_gpu_start.value = {
  "type"       : "STRESS",
  "cmd"        : "Start",	// 开始
  "device"     : "GPU",   // GPU
  "offsetCores": 0,       // 专属核心，从Core 0开始
  "width"      : 0,    // 输出窗口 宽 默认0
  "height"     : 0,    // 输出窗口 高 默认0
  "fullscreen" : 1,       // 是否全屏
  "raytracing" : 0,       // 是否光追模式
  "loop"       : 999999,  // 循环次数
  "gpu"        : 0  // GPU LUID
}

msg_cpu_start.value = {
  "type"       : "STRESS",
  "cmd"        : "Start",	// 开始 msg_cpu_start
  "device"     : "CPU",   // CPU
  "isAVX"      : 0,       // 使用AVX2
  "mixGPU"     : 0,       // 为显卡保留2GB内存
  "containMem" : 0,       // 是否测试内存
  "nLevel"     : 10,       // 等级 1-10 越大占用越高
  "offsetCores": 0,       // 保留核心，为GPU测试保留CPU，从Core 0 开始
  "minRuns"    : 999999,  // 总测试次数（minTime * minRuns = 总测试时间）
  "minTime"    : 1000     // 单次测试持续时间 毫秒
}
  for(const item of  ModelList.value)
  {
    item.status = 0
    item.choosen = false
  }
}



//添加开始数据到List
 const  AddDataToList = async (DatabaseId:any,testModel:number,StartTimestampTable:any) =>
{
  let tempArr = ['CPU TDC Limit','IA：PROCHOT','IA：Thermal Event','Package/Ring Critical Temperature',
  'Package/Ring Thermal Throttling','IA: VR Thermal Alert','IA: VR TDC','Thermal Throotling HTC',
  'Thermal Limit','PROCHOT CPU','PROCHOT EXT','CPU TDC Limit']
  if(bg_sensor_data.value['cpu']['limit'].length > 0 )
  {
    let reasonArr:any = Object.keys(bg_sensor_data.value['cpu']['limit'])
    // console.warn('reasonArr',reasonArr);
    
    let reason:any = []
    for(const key of reasonArr)
    {
      //name---
      let name = Object.keys(bg_sensor_data.value['cpu']['limit'][key])[0]
      let val = Number(bg_sensor_data.value['cpu']['limit'][key][name])
      // console.warn('温度事件报告::',val);
      
      let has = false
      tempArr.forEach((item,index) =>
      {
         if(item.includes(name))
        {
          has = true
        }
      })
      if(has)
      {
        if(cpuType.value == 'intel')
      {
         reason.push(name)
          let time = Date.now()
          let obj = {
              time:FormatTimePlus(time/1000),
              reason:reason,
              cputdp:bg_sensor_data.value['cpu']['power'],
              cpuclock:bg_sensor_data.value['cpu']['clock'],
              chang:reason.length
          }
        // console.warn('obj',obj);
    
         performanceArr.value.unshift(obj)
      }else
      {
        if(val > 99)
        {
           reason.push(name)
            let time = Date.now()
            let obj = {
                time:FormatTimePlus(time/1000),
                reason:reason,
                cputdp:bg_sensor_data.value['cpu']['power'],
                cpuclock:bg_sensor_data.value['cpu']['clock'],
                chang:reason.length
        }
    
         performanceArr.value.unshift(obj)
        }
      }
       
      }
    }
   
  }

  //添加数据到详情表
  GPP_UpdateHWInfo3(DatabaseId, StartTimestampTable,testModel);
}

function GPP_UpdateHWInfo3 (DatabaseId:any, StartTimestampTable:any,testModel:any) {
  // setTimeout,setInterval
    let SensorDataStr = window.localStorage.getItem('bg_sensor_data');
    let SensorData = SensorDataStr ? JSON.parse(SensorDataStr) : null;
    if (SensorData) {
      /***********CPU SensorInfo**************/
      let cpu_temp_value = SensorData['cpu']['temp'];
      let cpu_tdp_value = SensorData['cpu']['power'];
      let cpu_fan_value = SensorData['cpu']['fan'];
      let cpu_clock_value = SensorData['cpu']['clock'];
      let cpu_clockP = SensorData['cpu']['clockP'];
      let cpu_clockE = SensorData['cpu']['clockE'];
      let cpu_volt_value = SensorData['cpu']['voltage'];
      let cpu_load_value = SensorData['cpu']['usage'];
      let cpuclock_core = SensorData['cpu']['core_info']['Clock'].join("|");

      /***********GPU SensorInfo**************/
      let gpu_temp_value = SensorData['gpu']['temp'];
      let gpu_load_value = SensorData['gpu']['total_usage'];
      let gpu_tdp_value = SensorData['gpu']['power'];
      let gpu_fan_value = SensorData['gpu']['fan'];
      if (gpu_fan_value < 100 && gpu_fan_value !== 0) {gpu_fan_value = gpu_fan_value + ' %'} else {gpu_fan_value = gpu_fan_value + ' RPM'}
      let gpu_clock_value = SensorData['gpu']['clock'];
      let gpu_volt_value = SensorData['gpu']['voltage'];

      //内存
      let memory_temp_value = SensorData['memory']['temp'];
      let memory_load_value = SensorData['memory']['usage'];

      // PERFORMANCE LIMIT REASONS
      let performance:any = [];
      let PlrArr = SensorData['cpu']['limit'];
      PlrArr.forEach((itemDetail) => {
        let infoKey = Object.keys(itemDetail)[0];
        let infoValue = itemDetail[infoKey];
        if (infoValue === 1 || infoValue >= 99) {
          let performanceObj = {};
          performanceObj[infoKey] = infoValue;
          performance.push(performanceObj);
        }
      })

      let performance_gpu = {};
      if (SensorData['gpu']['pl_thermal']) {
        performance_gpu.THERMAL = 1
      }

      let CpuInfo:any = {};
      CpuInfo['title'] = 'CPU'
      CpuInfo['col0width'] = 100
      CpuInfo['col1width'] = 300
      CpuInfo['table'] = [
        {
          "col0": "Name",
          "col1": SensorData.cpu.cpu_name
        }
      ]

      //Clock
      if (SensorData['cpu']['clockP'] !== null && SensorData['cpu']['clockE'] !== null) {
        CpuInfo['table'].push({ "col0": "Clock", "col1": 'P-' + SensorData['cpu']['clockP'] + 'MHz' + ' ' + 'E-' + SensorData['cpu']['clockE'] + 'MHz' })
      } else {
        if (cpu_clock_value !== 0) {CpuInfo['table'].push({ "col0": "Clock", "col1": cpu_clock_value + " MHz" })}
      }

      //Load
      if (SensorData['cpu']['usageP'] !== null && SensorData['cpu']['usageE'] !== null) {
        CpuInfo['table'].push({ "col0": "Load", "col1": 'P-' + SensorData['cpu']['usageP'] + '%%' + ' ' + 'E-' + SensorData['cpu']['usageE'] + '%%' })
      } else {
        if (cpu_load_value !== 0) {CpuInfo['table'].push({ "col0": "Load", "col1": cpu_load_value + " %%" })}
      }

      //Temp
      if (cpu_temp_value !== 0) {CpuInfo['table'].push({ "col0": "Temp", "col1": cpu_temp_value + " °C" })}

      //TDP
      if (cpu_tdp_value !== 0) {CpuInfo['table'].push({ "col0": "TDP", "col1": cpu_tdp_value + " W" })}

      //Voltage
      if (SensorData['cpu']['voltageP'] !== null && SensorData['cpu']['voltageE'] !== null) {
        CpuInfo['table'].push({ "col0": "Voltage", "col1": 'P-' + SensorData['cpu']['voltageP'] + 'V' + ' ' + 'E-' + SensorData['cpu']['voltageE'] + 'V' })
      } else {
        if (cpu_volt_value !== 0) {CpuInfo['table'].push({ "col0": "Voltage", "col1": cpu_volt_value + " V" })}
      }

      if (cpu_fan_value !== 0) {CpuInfo['table'].push({ "col0": "Fan", "col1": cpu_fan_value + " RPM" })}

      CpuInfo['pX'] = 1
      CpuInfo['pY'] = 3

      let GpuInfo:any = {};
      GpuInfo['title'] = 'GPU'
      GpuInfo['col0width'] = 200
      GpuInfo['col1width'] = 600
      GpuInfo['table'] = [
        {
          "col0": "Name",
          "col1": SensorData.gpu.gpu_name
        },
        {
          "col0": "DirectX",
          "col1": "12"
        },
        {
          "col0": "VRAM",
          "col1": SensorData.gpu.mem_size
        }
      ]

      if (gpu_clock_value !== 0) {GpuInfo['table'].push({ "col0": "Clock", "col1": gpu_clock_value + " MHz" })}

      if (gpu_load_value !== 0) {GpuInfo['table'].push({ "col0": "Load", "col1": gpu_load_value + " %%" })}

      if (gpu_temp_value !== 0) {GpuInfo['table'].push({ "col0": "Temp", "col1": gpu_temp_value + " °C" })}

      if (gpu_volt_value !== 0) {GpuInfo['table'].push({ "col0": "Voltage", "col1": gpu_volt_value + " V" })}

      if (gpu_fan_value !== 0) {GpuInfo['table'].push({ "col0": "Fan", "col1": gpu_fan_value })}

      GpuInfo['pX'] = 1
      GpuInfo['pY'] = 3

      let obj:any = {};
      obj['type'] = 'HWINFO';
      obj['cmd'] = 'HWINFO';
      obj['CPU'] = CpuInfo;
      obj['GPU'] = GpuInfo;

      const numbersObject = SensorData['cpu']['core_info']['Clock'].reduce((obj, value, index) => {
        obj[index] = value;
        return obj;
      }, {});

      let HwPTinfo:any = {
        "cpu"   : {
          "TDP"            : cpu_tdp_value,
          "clock"          : cpu_clock_value,
          "clock_all"      : cpuclock_core,
          "clock_arr"      : numbersObject,
          "clock_effective": SensorData['cpu']['effective_clock'],
          "thread"         : SensorData['cpu']['thread'],
          "performance"    : performance,
          "clockP"         : cpu_clockP,
          "clockE"         : cpu_clockE,
          "fans"           : cpu_fan_value,
          "temperature"    : cpu_temp_value,
          "usage"          : cpu_load_value,
          "usageP"         : SensorData['cpu']['usageP'],
          "usageE"         : SensorData['cpu']['usageE'],
          "voltage"        : SensorData['cpu']['voltage'],
          "thermal"        : SensorData['cpu']['amd_thermal']
        },
        "gpu"   : {
          "clock"              : gpu_clock_value,
          "usage"              : gpu_load_value,
          "temperature"        : gpu_temp_value,
          "vramtemp"           : SensorData['gpu']['mem_temp'],
          "power"              : gpu_tdp_value,
          "fans"               : gpu_fan_value,
          "fan"                : SensorData['gpu']['fan'],
          "voltage"            : gpu_volt_value,
          "performance"        : performance_gpu,
          "thermalHotspot"     : SensorData['gpu']['thermal_hotspot'],
          "thermalMemory"      : SensorData['gpu']['thermal_memory'],
          "hot"                : SensorData['gpu']['hot_spot_temp'],
          "gpuD3D"             : SensorData['gpu']['d3d_usage'],
          "powerGroup"         : SensorData['gpu']['power_list'],
          "ReliabilityVoltage" : SensorData['gpu']['pl_reliability_voltage'],
          "MaxOperatingVoltage": SensorData['gpu']['pl_max_operating_voltage']

        },
        "memory": {
          "usage"      : memory_load_value,
          "temperature": memory_temp_value,
          "ddr5":SensorData['memory']['ddr5_temp']
        },
        "whea"  : SensorData['whea']
      };
      // console.log(HwPTinfo)

      InsertToDetailedTable(HwPTinfo, DatabaseId, StartTimestampTable,testModel);
    }
}

const InsertToDetailedTable =  async (HwPTinfo:any, DatabaseId:any, StartTimestampTable:any,testModel:any) =>
{
   let limit = {cputype:'amd',0:0,1:0,2:0}
  //删除特殊字符
  function removeSpecialStr (string:any) {
      //去除特殊字符~!@#$^-&*()=|{}':;',\[].<>/?~！@#￥……&*（）——|{}【】'；：""'。，、？
      // var pattern = new RegExp("[`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]");
      const pattern=/[`~!@#$%+^\-&*()=|{}':;',\\\[\]\.<>\/——|{}\s]/g;
      return string.replace(pattern, "");
  }

  let performanceStr = HwPTinfo.cpu.performance.length !== 0 ? JSON.stringify(HwPTinfo.cpu.performance) : '';
 
  if(cpuType.value == 'amd')
  {
    limit = 
    {
      'cputype':'amd',
      0:limit_List_intel.value[3].value,
      1:limit_List_intel.value[4].value,
      2:limit_List_intel.value[5].value,
    }
  }
  else
  {
    limit = 
    {
      'cputype':'intel',
      0:limit_List_intel.value[0].value,
      1:limit_List_intel.value[1].value,
      2:limit_List_intel.value[2].value,
    }
  }
   
  let Data_Field = [
    "AMD_LIMIT",'memTemp','gpuD3D','gpuhot',"model","cpuload", "cputemp", "cpuclock", "cpuclockP", "cpuclockE", "cpuclock_all", "cpuTDP", "cpuvoltage", "cpufan",
    "gpuload", "gputemp", "gpuclock", "gpuTDP", "gpuvoltage", "gpufan", "gpuvramtemp", "gpuReliabilityVoltage", "gpuMaxOperatingVoltage", "performance"
  ];
  let Data_Content = [
    [
      encodeURIComponent(JSON.stringify(limit)),encodeURIComponent(JSON.stringify(HwPTinfo['memory']['ddr5'])),HwPTinfo['gpu']['gpuD3D'],HwPTinfo['gpu']['hot'],testModel,HwPTinfo['cpu']['usage'], HwPTinfo['cpu']['temperature'], HwPTinfo['cpu']['clock'], HwPTinfo['cpu']['clockP'], HwPTinfo['cpu']['clockE'], HwPTinfo['cpu']['clock_all'], HwPTinfo['cpu']['TDP'], HwPTinfo['cpu']['voltage'], HwPTinfo['cpu']['fans'],
      HwPTinfo['gpu']['usage'], HwPTinfo['gpu']['temperature'], HwPTinfo['gpu']['clock'], HwPTinfo['gpu']['power'], HwPTinfo['gpu']['voltage'], HwPTinfo['gpu']['fan'], HwPTinfo['gpu']['vramtemp'], HwPTinfo['gpu']['ReliabilityVoltage'], HwPTinfo['gpu']['MaxOperatingVoltage'], performanceStr
    ]
  ];
  // console.warn('SenserData to DataBse::',Data_Content);
  await gamepp.database.insert.promise(DatabaseId, "'" + StartTimestampTable + "'", Data_Field, Data_Content);
  
  let powerGroup = HwPTinfo.gpu.powerGroup;
  let repeatKey = 0;
  for (let i = 0; i < powerGroup.length; i++) {
    let key = Object.keys(powerGroup[i])[0], value = powerGroup[i][key];
    if (key === 'GPU POWER' || key === 'GPU Power') continue;
    // let dataField = key.replace(/\s+/g, "").replace(/\(|\)/g, '').replace(/\+/g, "").replace(/\-/g, "").replace(/\#/g, "");
    let dataField = removeSpecialStr(key);
    if (Data_Field.includes(dataField)) {
      repeatKey++
      Data_Field.push(dataField + '_' + repeatKey);
    } else {
      repeatKey = 0;
      Data_Field.push(dataField);
    }
    Data_Content[0].push(value);
  }

  let queryFieldGPUPower = false;
  try {
    if (!queryFieldGPUPower) {
      for (let i = 0; i < Data_Field.length; i++) {
        let Field = Data_Field[i];
        let queryField = await gamepp.database.queryField.promise(DatabaseId, "'" + StartTimestampTable + "'", "'" + Field + "'");
        if (!queryField) {
          await gamepp.database.alter.promise(DatabaseId, "'" + StartTimestampTable + "'", "'" + Field + "'" + ' REAL');
        }
      }
      queryFieldGPUPower = true;
    }
  } 
  catch(err)  
  {
    console.log('err',err);
  }

  let record_unnormal = {
    cpu_name:'',
    gpu_name:'',
    cpu_maxtemp:'',
    gpu_maxtemp:'',
  }
  // //每5秒修改 real_time (判断异常结束持续时间)
  // if (Number.isInteger(recordTime / 5)) {
  //   if (testList.includes('cpu')) {
  //     cpu_real_list['name'] = $('#main_cpu_name').text();
  //     if (HaveSmallCore) {cpu_real_list['avgclock'] = $('#avgCpuclockP').text();} else {cpu_real_list['avgclock'] = $('#avgCpuclock').text();}
  //     cpu_real_list['maxtdp'] = $('#maxCpuTDP').text();
  //     cpu_real_list['maxtemperature'] = $('#maxCputemperature').text();
  //     real_list_data['cpu'] = cpu_real_list;
  //   }

  //   if (testList.includes('gpu')) {
  //     gpu_real_list['name'] = $('#main_gpu_name').text();
  //     gpu_real_list['avgclock'] = $('#avgGpuclock').text();
  //     gpu_real_list['maxtdp'] = $('#maxGpupower').text();
  //     gpu_real_list['maxtemperature'] = $('#maxGputemperature').text();
  //     real_list_data['gpu'] = gpu_real_list;

  //   }

  //   if (testList.includes('memory')) {
  //     memory_real_list['clock'] = $('#memory_clock').text();
  //     memory_real_list['timing'] = $('#memory_timing').text();
  //     memory_real_list['memory_active'] = $('#memory_active').text();
  //     memory_real_list['memory_size'] = $('#memory_size').text();
  //     memory_real_list['memory_type'] = $('#memory_type').text();

  //     memory_real_list['maxtemperature'] = $('#maxMemorytemperature').text();
  //     memory_real_list['maxMemoryusage'] = $('#maxMemoryusage').text();


  //     real_list_data['memory'] = memory_real_list;
  //   }
  //   let real_list_dataStr = encodeURIComponent(JSON.stringify(real_list_data));

  //   try {
  //     await gamepp.database.update.promise(DatabaseId, "list", ['real_time="' + recordTime + '"', 'real_list_data="' + real_list_dataStr + '"'], 'starttime = "' + StartTimestampTable + '"');
  //   } catch {
  //     // console.log('updateDataBase', real_list_data);
  //   }
  // }
}

const  SendBenchmarkCMD  = async (obj:any) =>
{
  console.log('SendMessage', obj);
  let obj_str = JSON.stringify(obj);
  try 
  {
    await gamepp.benchmark.benchMarksendMessage.promise(obj_str);
  } 
  catch 
  {}
}

const setChartsHWINFO = async() =>
{
  var chartDom = document.getElementById("cpuCharts");
  var chartDom2 = document.getElementById("gpuCharts");

  cpu_chart = echarts.init(chartDom);
  gpu_chart = echarts.init(chartDom2);


        cpu_options.value = {
            xAxis:{
                type: 'category',
                data: CPU_AXIS.value,//X轴个数
                show: false,
                boundaryGap: false,
                axisTick: { show: false },
                axisLabel: {
                  show: false,
                },
            },
            tooltip: {
                trigger: 'axis',
                formatter:function(data:any)
                {
                  let AllDataHtml:any = '<div class="AllDataHtml">'
                  for(const item of data)
                  {
                    if(item.value != 'undefined' && item.value != null && item.value != undefined)
                    {
                      AllDataHtml += `${item.seriesName.split('_')[0]} : `+ item.value + item.seriesName.split('_')[1]+ '<br/>';
                    }
                  }
                  AllDataHtml += '</div>'
                  if(data[0].value)
                  {
                    return AllDataHtml
                  }else
                  {

                  }
                },

                backgroundColor: 'rgb(44,44,51,0.8)',
                textStyle: {
                  fontSize: '12',
                  color: 'rgb(195,202,213)'
                },
            },
            yAxis: [
            {
              type: 'log', // 使用对数轴
              logBase: 10, // 对数底数为10
              min: 1, // 设置最小值
              // max: 10000, // 设置最大值
              axisLabel: {
                color: "#21222a"  //刻度线标签颜色
              },
              axisTick: { //y轴刻度线
                show: false
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: ['#333645']
                }
              }
            }
          ],
          grid: {
            left: 0,
            right: 0,
            bottom: 0,
            top: 0
           },
          series: [
           
          ],
      }

      for(const item of cpuInfo.value)
      {
        console.warn('item',item);
        
         if(item.cur !== 'undefined' && item.cur !== null && item.cur !== undefined)
         {
          let dataArr = []//

          for( let i = 0 ;i <40; i++ )
          {
            dataArr.push(null)
          }

          cpu_options.value.series.push(
            {
                data: dataArr,
                type: 'line',
                lineStyle: {
                    color: item.color
                },
                symbol: 'none',
                name:`${item.name}_${item.unit}`,
                choosen:true,
                animation: false,
            }
          )
         
         }
      }

      gpu_options.value = {
            xAxis:{
                type: 'category',
                data: GPU_AXIS.value,//X轴个数
                show: false,
                boundaryGap: false,
                axisTick: { show: false },
                axisLabel: {
                  show: false,
                },
            },
            tooltip: {
                trigger: 'axis',
                formatter:function(data:any)
                {
                  let AllDataHtml:any = '<div class="AllDataHtml">'
                  // console.warn('formatter:',data);
                  for(const item of data)
                  {
                    AllDataHtml += `${item.seriesName.split('_')[0]} : `+ item.value + item.seriesName.split('_')[1]+ '<br/>';
                  }
                  // AllDataHtml =`<span class="rickxixi"> FPS :  ${data[0]['value']}</span>  '<br/>';`
                  AllDataHtml += '</div>'
                  if(data[0].value)
                  {
                    return AllDataHtml
                  }else
                  {

                  }
                },

                backgroundColor: 'rgb(44,44,51,0.8)',
                textStyle: {
                  fontSize: '12',
                  color: 'rgb(195,202,213)'
                },
            },
            yAxis: [
            {
              type: 'log', // 使用对数轴
              logBase: 10, // 对数底数为10
              min: 1, // 设置最小值
              axisLabel: {
                color: "#21222a"  //刻度线标签颜色
              },
              axisTick: { //y轴刻度线
                show: true
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: ['#333645']
                }
              }
            }
          ],
          grid: {
            left: 0,
            right: 0,
            bottom: 0,
            top: 0
           },
          series: [
           
          ],
      }

     
      for(const item of gpuInfo.value)
      {
          let dataArr = []//
          for( let i = 0 ;i <40; i++ )
          {
            dataArr.push(null)
          }

          gpu_options.value.series.push(
            {
                data: dataArr,
                type: 'line',
                lineStyle: {
                    color: item.color
                },
                symbol: 'none',
                name:`${item.name}_${item.unit}`,
                choosen:true,
                animation: false,
            }
          )
      }
      
      cpu_chart.setOption(cpu_options.value);
      gpu_chart.setOption(gpu_options.value);
}

const updateChartsInfo = (name:any,index:any,value:any) =>
{
  // console.log('name',name,'index',index,'value',value);
  
  let seriesIndex = cpu_options.value.series.findIndex((item:any)=>{
    return item.name.split('_')[0] == name
  })
  //更新X轴长度
  // console.log('cpu_options.value',cpu_options.value);
  // console.log('seriesIndex',seriesIndex);
  

  
  if(cpu_options.value.series[seriesIndex].data.length > CPU_AXIS.value.length)
  {
    let cha = cpu_options.value.series[seriesIndex].data.length - CPU_AXIS.value.length
    for(let i = 0;i<cha;i++)
    {
      CPU_AXIS.value.push(' ')
    }
  }
  cpu_options.value.series[seriesIndex].data.push(value)

  let nullIndex = cpu_options.value.series[seriesIndex].data.findIndex((item:any)=>{return item == null })

  if (nullIndex != -1  || cpu_options.value.series[seriesIndex].data.length > 100)//存在null 或者不在测试中
  {
    if(!isTesting.value)
    {
      cpu_options.value.series[seriesIndex].data.shift();//出栈
    }
  }
  //数据采样聚合 

  const optionsCopy = JSON.parse(JSON.stringify(cpu_options.value))
  const choosenSerire = cpu_options.value.series.filter((item:any) =>
  {
    return item.choosen
  }
  )
  
  optionsCopy.series = choosenSerire
  cpu_chart.setOption(choosenSerire);
  // console.log('%c cpu::','color:green',cpu_options.value);

}

const updateChartsInfo2 = (seriesIndex:any,value:any) =>
{
    //更新X轴长度
    if(gpu_options.value.series[seriesIndex].data.length > GPU_AXIS.value.length)
    {
      let cha = gpu_options.value.series[seriesIndex].data.length - GPU_AXIS.value.length
      for(let i = 0;i<cha;i++)
      {
        GPU_AXIS.value.push(' ')
      }
    }
    gpu_options.value.series[seriesIndex].data.push(value)

    let nullIndex = gpu_options.value.series[seriesIndex].data.findIndex((item:any)=>{return item == null })

    if (nullIndex != -1  || gpu_options.value.series[seriesIndex].data.length > 100)//存在null 或者不在测试中
    {
      if(!isTesting.value)
      {
        gpu_options.value.series[seriesIndex].data.shift();//出栈
      }
    }

    const optionsCopy = JSON.parse(JSON.stringify(gpu_options.value))
    const choosenSerire = gpu_options.value.series.filter((item:any) =>
    {
      return item.choosen
    }
    )
    
    optionsCopy.series = choosenSerire
    
    gpu_chart.setOption(choosenSerire);
}

const  BenchMarkMessageProcessNew = async  (value:any)  =>
{
  if (value === '') return;
  let data = JSON.parse(value);
  console.log('%cBenchMark Message Process::','color:red',data)
  if(data.type === 'BENCHMARK' && data.state === 1)
  {
    if (data.code !== 0 || data.error !== 0) 
    {
      //产生报错 停止CPU稳定性测试
       cpu_stable_info.value['error'] = true

       PASS.value[coreCount.value][cpu_stable_info.value['curCMD']] = 'ERROR'
      //保存报错信息
      await SendBenchmarkCMD(msg_cpu_stop)
    }
    else
    {
        console.log('%c current Stage::','color:green',data.stage);
        
        CpuThreadsStage.value++
        const modValue = CpuThreadsStage.value % 10; // 对 10 取模
        const divValue = Math.floor(CpuThreadsStage.value / 10); // 对 10 进行整除取整

        if (modValue >= 0 && modValue <= 4) {
          coreCount.value = divValue % 2; // 0-4 对应 0 或 2，取决于当前周期
        } else if (modValue > 4 && modValue <= 9) {
          coreCount.value = (divValue + 1) % 2; // 5-9 对应 1 或 3，取决于当前周期
        }
        console.warn('测试阶段数目::',CpuThreadsStage.value);
        
        console.log('%c 测试项目::','color:orange',coreCount.value,CpuThreadsStage.value,CmdListX.value[CpuThreadsStage.value]);
        
        //当前模式
        cpu_stable_info.value['curCMD'] = CmdListX.value[CpuThreadsStage.value]
        let obj:any = {};
        obj['type'] = 'BENCHMARK'
        obj['cmd'] = CmdListX.value[CpuThreadsStage.value].replace('单核','').replace('多核','')
        obj['threads'] = coreCount.value;
        obj['priority'] = 0;
        console.log('%c Send CPU STABLE NEXT++:','color:blue',obj);
        
        await SendBenchmarkCMD(obj);
        console.warn('coreCount',coreCount.value);
        console.warn('cpu_stable_info.value',cpu_stable_info.value['curCMD']);
        
        PASS.value[data['threads']][data['stage']] = 'PASS'
        console.warn('PASS.value',PASS.value);

    }
  }

  if (data.type === "CM_STRESS_ERROR_CALLBACK")  //内存报错回调函数
    {
    let Arr = ['SimpleTest 0 MB','SimpleTest 1 MB','SimpleTest 2 MB','SimpleTest 4 MB','SimpleTest 16 MB','SimpleTest 32 MB','SimpleTest 64 MB','RefreshTable','MirrorMove 2MB']
    let keyArr = Object.keys(data)
    // console.log('%cKEYS','color:green',keyArr);
    for(const item of keyArr)
    {
      mem_stable_info.value[item] = data[item]
    }
    mem_stable_info.value.curTask =   Arr[data['mem_current_task']]
    // console.log('%cKEYS','color:orange',mem_stable_info.value);
    TestInfo.value['mem_error'] = data.mem_error
    TestInfo.value['mem_data'] = data
  } 
  
}

async function setting(index:any)
{
  if(index == 0)
  { // 最小化
    await gamepp.webapp.windows.minimize.promise('pressureTest');
  }
  else if (index == 3)
  {
    await gamepp.webapp.windows.close.promise('pressureTest')
  }
  console.warn(index);
}

const logChange = async () =>
{
  console.log('%c ModelLsit','pink',ModelList.value);
}

const openHistoryX = async() =>{
  if(isTesting.value)
  {
    ElMessage({
                message: '当前正在测试中',
                type: 'warning',
                grouping:true
        }) 
  }
  else
  {
    openHistory.value = true
  }
}
</script>

<template>
  <div class="PressureTest">
    <div class="Nav">
      <div class="GPPInfo" style="width: 1100px;-webkit-app-region: drag;">
        <img src="../../assets/img/Public/logo_gpp.png" alt="">
        <span class="slogan">压力测试</span>
        <span class="slogan">V{{moVersion}}</span>
      </div>
      <RightTopIcons
              close-icon
              minimize-icon
              @close="setting(3)"
              @minimize="setting(0)"
              hover-color="#22232e"
          />
    </div>
    <div class="TestBox" v-show="!openHistory">
      <div class="modeChoose">
        <p class="shing p_1">测试项</p>
        <div class="mode_choose" @click.native.stop="chooseMode(0)">
         <div class="moveLine" v-show="ModelList[0].status == 1"></div>
         <div class="line">
          <!-- 未测试 -->
           <div v-show="!isTesting" :class="[ModelList[0].choosen?'chunk':'unchunk']"></div>
           <div v-show="isTesting"  class="chunk" :class="{'color1':ModelList[0].status ==  2,'color2':ModelList[0].status ==  1,'color3':ModelList[0].status ==  0}"></div>
           <div class="chunk1" style="width: 14px;height: 14px;position: absolute;right: 5px;top: 22px;z-index: 10000;"></div>
           <div class="text">CPU温度测试</div>
            
           <el-checkbox style="margin-left: 40px;" v-show="!isTesting" v-model="ModelList[0].choosen"  size="large" />
           <div v-show="isTesting" style="width: 50px;text-align: right;">
             <span v-show="ModelList[0].status == 1" style="color: #409EFF;">正在测试</span>
             <span v-show="ModelList[0].status == 0 && ModelList[0].choosen"><span style="color: #FBAC14;" class="iconfont icon-wait"></span></span>
             <span v-show="ModelList[0].status == 2 "><span style="color:#35D57D;" class="iconfont icon-complete"></span></span>
           </div>
          
           <!-- 开始测试后 -->
         </div>
         <div class="waitTest" v-show="isTesting && ModelList[0].status == 0 ">
              <span>{{ModelList[0].choosen?'等待测试':'未参与测试'}}</span>
          </div>
          <div class="Test_Info" v-show="isTesting && ModelList[0].status !== 0">
              <div><span class="disc">此阶段CPU最高温度：</span><span>{{ cpu_temp_info.max }} ℃</span></div>
              <div><span class="disc">达到最高温度用时：</span><span>{{ cpu_temp_info.cost }} s</span></div>
              <div><span class="disc">温度传感器事件报告次数：</span><span>{{ cpu_temp_info.cpulimit }} 次</span></div>
              <div><span class="disc">最低频率：</span><span>{{ cpu_temp_info.minClock }}MHz</span></div>
          </div> 
         <div class="discrib" v-show="!isTesting">
          在安装系统或更换散热器后
          检验散热能力是否足够
         </div>
         <div class="settings"v-show="!isTesting">
          <span>测试时长：</span>  
          <el-input-number
               @click.stop
              :controls="false"
              :step="1"
              style="width: 84px;height: 28px;"
              :min="1"
              :max="999"
              v-model="ModelList[0].min"
              @change="logChange()"
          ></el-input-number>
          <p>分钟</p>
         </div>
        </div>
        <div class="mode_choose" @click.native.stop="chooseMode(1)">
          <div class="moveLine" v-show="ModelList[1].status == 1"></div>
          <div class="line">

            <div v-show="!isTesting" :class="[ModelList[1].choosen?'chunk':'unchunk']"></div>
           <div v-show="isTesting"  class="chunk" :class="{'color1':ModelList[1].status ==  2,'color2':ModelList[1].status ==  1,'color3':ModelList[1].status ==  0}"></div>

           <div class="chunk1" style="width: 14px;height: 14px;position: absolute;right: 5px;top: 22px;z-index: 10000;"></div>
           <div class="text">CPU稳定性测试</div>

           <el-checkbox style="margin-left: 40px;;" v-show="!isTesting" v-model="ModelList[1].choosen"  size="large" />
           <div v-show="isTesting" style="width: 50px;text-align: right;">
             <span v-show="ModelList[1].status == 1" style="color: #409EFF;">正在测试</span>
             <span v-show="ModelList[1].status == 0 && ModelList[1].choosen"><span style="color: #FBAC14;" class="iconfont icon-wait"></span></span>
             <span v-show="ModelList[1].status == 2 "><span style="color:#35D57D;" class="iconfont icon-complete"></span></span>
           </div>
         </div>
         <div class="waitTest" v-show="isTesting && ModelList[1].status == 0 ">
          <span>{{ModelList[1].choosen?'等待测试':'未参与测试'}}</span>
           </div>
           <div class="Test_Info" v-show="isTesting && ModelList[1].status !== 0">
              <div><span class="disc">此阶段CPU最高温度：</span><span>{{ cpu_stable_info.max }} ℃</span></div>
              <div><span class="disc">达到最高温度用时：</span><span>{{ cpu_stable_info.cost }} s</span></div>
              <div><span class="disc">温度传感器事件报告次数：</span><span>{{ cpu_stable_info.cpulimit }} 次</span></div>
              <div><span class="disc">{{cpu_stable_info.error?'CPU错误：':'当前阶段：'}}</span><span>{{ cpu_stable_info.curCMD }}</span></div>
          </div> 
          <div class="discrib" v-show="!isTesting ">
            安装新系统、超频后验证稳定性，或系统经常发生崩溃、蓝屏、死机问题
          </div>
        <div class="settings" v-show="!isTesting">
          <span>测试时长：</span>  
          <el-input-number
              @click.stop
              :controls="false"
              :step="1"
              style="width: 84px;height: 28px;"
              :min="1"
              :max="999"
              v-model="ModelList[1].round"
              @change="logChange()"
          ></el-input-number>
          <p>轮次</p>
         </div>
        </div>
        <div class="mode_choose" @click.native.stop="chooseMode(2)">
          <div class="moveLine" v-show="ModelList[2].status == 1"></div>
            <div class="line">
              <div v-show="!isTesting" :class="[ModelList[2].choosen?'chunk':'unchunk']"></div>
           <div v-show="isTesting"  class="chunk" :class="{'color1':ModelList[2].status ==  2,'color2':ModelList[2].status ==  1,'color3':ModelList[2].status ==  0}"></div>


            <div class="text">内存稳定性测试</div>
            <div class="chunk1" style="width: 14px;height: 14px;position: absolute;right: 5px;top: 22px;z-index: 10000;"></div>
            <el-checkbox style="margin-left: 40px;" v-show="!isTesting" v-model="ModelList[2].choosen"  size="large" />

            <div v-show="isTesting" style="width: 50px;text-align: right;">
              <span v-show="ModelList[2].status == 1"style="color: #409EFF;">正在测试</span>
              <span v-show="ModelList[2].status == 0 && ModelList[2].choosen"><span style="color: #FBAC14;" class="iconfont icon-wait"></span></span>
              <span v-show="ModelList[2].status == 2 "><span style="color:#35D57D;" class="iconfont icon-complete"></span></span>
            </div>
          </div>
          <div class="waitTest" v-show="isTesting && ModelList[2].status == 0 ">
            <span>{{ModelList[2].choosen?'等待测试':'未参与测试'}}</span>
           </div>
           <div class="Test_Info" v-show="isTesting && ModelList[2].status !== 0">
              <div><span class="disc">测试内容：</span><span>{{ mem_stable_info.curTask }} </span></div>
              <div><span class="disc">内存报错：</span><span>{{ mem_stable_info.mem_create_error }} 次</span></div>
              <div><span class="disc">CPU报错：</span><span>{{ mem_stable_info.cpu_error }} 次</span></div>
          </div> 

          <div class="discrib" v-show="!isTesting">
            测试通过读写操作检查RAM是否稳定，防止数据错误或系统崩溃
          </div>
          <div class="settings" v-show="!isTesting">
            <span>测试时长：</span>  
            <el-input-number
                @click.stop
                :controls="false"
                :step="1"
                 style="width: 84px;height: 28px;"
                :min="1"
                :max="999"
                v-model="ModelList[2].round"
                @change="logChange()"
            ></el-input-number>
            <p>轮次</p>
          </div>
        </div>
        <div class="mode_choose" @click.native.stop="chooseMode(3)">
          <div class="moveLine" v-show="ModelList[3].status == 1"></div>
          <div class="line">

            <div v-show="!isTesting" :class="[ModelList[3].choosen?'chunk':'unchunk']"></div>
            <div v-show="isTesting"  class="chunk" :class="{'color1':ModelList[3].status ==  2,'color2':ModelList[3].status ==  1,'color3':ModelList[3].status ==  0}"></div>

            <div class="chunk1" style="width: 14px;height: 14px;position: absolute;right: 5px;top: 22px;z-index: 10000;"></div>
           <div class="text">CPU&GPU 温度测试</div>

           <el-checkbox  style="margin-left: 40px;" v-show="!isTesting" v-model="ModelList[3].choosen"  size="large" />

           <div v-show="isTesting" style="width: 50px;text-align: right;">
             <span v-show="ModelList[3].status == 1"style="color: #409EFF;">正在测试</span>
             <span v-show="ModelList[3].status == 0 && ModelList[3].choosen"><span style="color: #FBAC14;" class="iconfont icon-wait"></span></span>
             <span v-show="ModelList[3].status == 2 "><span style="color:#35D57D;" class="iconfont icon-complete"></span></span>
           </div>

           </div>
           <div class="waitTest" v-show="isTesting && ModelList[3].status == 0 ">
            <span>{{ModelList[3].choosen?'等待测试':'未参与测试'}}</span>
           </div>
           <div class="Test_Info" v-show="isTesting && ModelList[3].status !== 0">
              <div><span class="disc">此阶段CPU最高温度：</span><span>{{double_info.max}}℃</span></div>
              <div><span class="disc">此阶段GPU最高温度：</span><span>{{double_info.maxgpu}}℃</span></div>
              <div><span class="disc">GPU达到最高温度用时：</span><span>{{double_info.cost}} s</span></div>
              <div><span class="disc">温度传感器事件报告次数：</span><span>{{double_info.cpulimit}}次</span></div>
              <div><span class="disc">GPU最低频率：</span><span>{{double_info.minClock}}MHz</span></div>
          </div> 
          <div class="discrib" v-show="!isTesting">
            同时压满CPU和GPU，检查整体散热能力，适合高负载场景如游戏或渲染
          </div>
          <div class="settings" v-show="!isTesting">
          <span>测试时长：</span>  
          <el-input-number
              @click.stop
              :controls="false"
              :step="1"
               style="width: 84px;height: 28px;"
              :min="1"
              :max="999"
              v-model="ModelList[3].round"
              @change="logChange()"
          ></el-input-number>
          <p>轮次</p>
         </div>
        </div>
      </div>

      <div class="versionBox">
        <div class="tips">
          <p class="shing">硬件实时监测数据</p>
          <span>小提示:小提示：在测试过程中点击左侧测试项可仅查看此项的阶段数据</span>
        </div>
        <div class="cpuBox thebox">
          <div class="left">
             <div class="cpu">
                <span class="iconfont icon-CPU" style="color: white; font-size: 24px; margin-right: 5px;"></span>
                <span>{{ bg_sensor_data['cpu']['name'] }}</span>
             </div>
             <div class="infoLine" style="font-size: 12px;color: #777777;">
                <div class="L_1"></div>
                <div class="L_2">当前</div>
                <div class="L_3">最大</div>
             </div>
             <div class="infoLine" v-for="(item,index) in cpuInfo" v-show="item.cur != 'undefined' && item.cur != null" style="font-size: 12px;color: #FFFFFF;margin-top: 3px;">
                <span class="L_1" style="color: #777777;">{{ item.name }}</span>
                <div  class="L_2">{{ item.cur }}<span style="margin-left: 3px;">{{ item.unit }}</span></div>
                <div  class="L_3">{{ item.max }}<span style="margin-left: 3px;">{{ item.unit }}</span></div>
             </div>
              <div class="infoLine" v-for="(item,index) in limit_List_intel" v-show="item.type == cpuType" style="font-size: 12px;color: #FFFFFF;margin-top: 3px;">
                <span class="L_1" style="color: #777777;">{{ item.name }}</span>
                <div  class="L_2" v-show = "item.type == 'intel'">{{ item.value == 0?'否':'是' }}<span style="margin-left: 3px;" ></span></div>
                <div  class="L_3" v-show = "item.type == 'intel'">{{ item.value == 0?'否':'是' }}<span style="margin-left: 3px;" ></span></div>
                <div  class="L_2" v-show = "item.type == 'amd'" >{{ item.value }}%<span style="margin-left: 3px;" ></span></div>
                <div  class="L_3" v-show = "item.type == 'amd'" >{{ item.max }}%<span style="margin-left: 3px;" ></span></div>
             </div>
          </div>
          <div class="right">
            <div style="display: flex;align-items: center;margin-top: 10px;">
              <span class="des">核心：</span><span style="margin-right: 10px;">{{cpuBase['core']  }}</span>
              <span class="des">线程：</span><span style="margin-right: 10px;">{{cpuBase['process']  }}</span>
              <span class="des">Tj,Max：</span><span style="margin-right: 10px;">{{cpuBase['TjMax']  }}</span>
            </div>
            <div style="width: 100%;height: 30px;display: flex;align-items: center;font-size: 12px;"> 
            <div class="options" v-for="(item,index) in cpuInfo" v-show="item.cur != 'undefined' && item.cur != null" style="display: flex;align-items: center;font-size: 12px;">
                <el-checkbox v-model="cpuInfo[index].choosen" @change="selectCharts(cpuInfo[index].name)" size="large" />
                <div :style="{color:item.color,marginLeft:'5px'}">{{ item.name }}</div>
            </div>
           </div>
              <div class="charts"  id="cpuCharts">

              </div>
          </div>
        </div>
        <div class="gpuBox thebox">
          <div class="left">
            <div class="cpu">
                <span class="iconfont icon-GPU" style="color: white; font-size: 24px; margin-right: 5px;"></span>
                <span>{{ bg_sensor_data['gpu']['name'] }}</span>
             </div>
             <div class="infoLine" style="font-size: 12px;color: #777777;margin-top: 10px;">
                <div class="L_1"></div>
                <div class="L_2">当前</div>
                <div class="L_3">最大</div>
             </div>
             <div class="infoLine" v-for="(item,index) in gpuInfo"  style="font-size: 12px;color: #FFFFFF;margin-top: 5px;">
                <span class="L_1" style="color: #777777;">{{ item.name }}</span>
                <div  class="L_2">{{ item.cur }}<span style="margin-left: 3px;">{{ item.unit }}</span></div>
                <div  class="L_3">{{ item.max }}<span style="margin-left: 3px;">{{ item.unit }}</span></div>
             </div>
          </div>
          <div class="right">
            <div style="display: flex;align-items: center;margin-top: 10px;">
              <span class="des">驱动版本：</span><span style="margin-right: 10px;">{{gpuBase['drive']  }}</span>
              <span class="des">显存大小：</span><span style="margin-right: 10px;">{{gpuBase['memSize']  }}</span>
            </div>
            <div style="width: 100%;height: 30px;display: flex;align-items: center;font-size: 12px;"> 
              <div class="options" v-for="(item,index) in gpuInfo" style="display: flex;align-items: center;font-size: 12px;">
                <el-checkbox  v-model="gpuInfo[index].choosen"  size="large" />
                <div :style="{color:item.color,marginLeft:'5px'}">{{ item.name }}</div>
              </div>
            </div>
            
              <div class="charts" id="gpuCharts">
                
              </div>
          </div> 
        </div>
        <div class="memoryBox">
          <div class="memory" style="display: flex;">
               <span class="iconfont icon-Dram" style="color: white; font-size: 24px; margin-right: 5px;"></span>
                <!-- <span>{{ bg_sensor_data['memory']['name'] }}</span> -->
                <p>内存：</p>
                <p>通道：</p>
                <span>{{  bg_sensor_data['memory']['channel'] }}</span>
                <p>频率：</p>
                <span>{{  bg_sensor_data['memory']['clock'] }}MHZ</span>
                <p>大小：</p>
                <span>{{  bg_sensor_data['memory']['size'] }}</span>
                <p>时序：</p>
                <span>{{ bg_sensor_data['memory']['tcas']}}-{{bg_sensor_data['memory']['trcd']}}-{{bg_sensor_data['memory']['trp']}}-{{bg_sensor_data['memory']['tras'] }}</span>
                <!-- <span>代数</span> -->
                <!-- tCAS-tRCD-tRP-tRAS -->
                <!-- <span>{{  bg_sensor_data['memory']['channel'] }}</span> -->
          </div> 
          <div class="memoryOut" v-show="isDDR5">
               <div class="memoryStatus" v-for="(item,index) in memoryInfo" v-show="item.value != null">
                <!-- <div class="memoryStatus" v-for="(item,index) in memoryInfo"> -->
                <span style="color:#999999;">{{bg_sensor_data['memory']['name']}}{{ item.name }}{{ item.key }}:</span>
                <span style="margin-left: 10px;">温度</span><span>{{ item.value }}°C</span>
          </div>
          </div>
          <div class="noSensor" style="width: 1030px;height: 80px;display: flex;align-items: center;justify-content: center;" v-show="!isDDR5">
            <span style="font-size: 18px;font-weight:700;color:#999999;">无温度传感器</span>
          </div>
        </div>
        <div class="cpuLimitBox">
            <div class="LimitLeft" style="font-size: 12px;">
               <p style="margin:10px 0 0 20px;color: #F14343;">温度传感器事件报告（与散热器和机箱风道有关）</p>
               <p style="color:#999999;margin:50px 0 0 50px" v-show="performanceArr.length == 0 && !isTesting">在开始压力测试后，我们将持续跟踪温度传感器事件报告，并及时显示事件原因</p>
               <p style="color:#999999;margin:50px 0 0 50px" v-show="performanceArr.length == 0 && isTesting">当前暂未出现温度传感器事件报告</p>
               <div class="limitcontainer scroll" v-show="performanceArr.length > 0" style="margin-left: 20px;height:100px;display: flex;flex-wrap: wrap;">
                <div class="limitbox" v-for="(item,index) in performanceArr" >
                  <div class="line" style="color:white">
                    <span style="margin-left: 10px;">{{ item.time }}</span><span style="margin-left: 10px;"> 记录</span>{{ item.chang}}项
                    <!-- <span style="color: #999999;margin-left: 10px;">频率：</span><span>{{item.cpuclock}}Mhz</span> -->
                    <span style="color: #999999;margin-left:10px;">CPU功耗：</span><span>{{ item.cputdp }}W</span>
                  </div>
                  <div class="limit_content">
                      <div class="limit_box" style="margin-left:10px;border-radius: 6px;margin-top: 10px;" v-for="(itemx,index) in item.reason">
                      {{ itemx }}
                    </div>
                   
                  </div>
               </div>
              </div>
              </div>
            <div class="LimitRight">
               <div class="history" @click="openHistoryX()">
                <span>历史测试记录</span>
              </div>
               <div class="starTest" @click="isTesting?stopPressure():startPressure()" :style="{backgroundColor:isTesting?'#A64343':'#336AB5'}">
                 <span >{{isTesting?'结束测试':'开始测试'}}</span>
               </div>
            </div>
        </div>
      </div>

    </div>
      <!-- <div id="echartsBox"  style="width: 1200px;height: 200px;">
            
      </div> -->
    <HistoryBox v-if="openHistory" @child-click="openHistory = false"> 

    </HistoryBox>
     
  </div>
</template>

<style scoped lang="scss">
  .PressureTest{
    overflow: hidden;
    box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.3);
    margin-left: 5px;
    margin-top: 5px;
    width: 1280px;
    height: 900px;
    user-select: none;
    margin-left:10px;
    margin-top: 10px;
    border-radius: 6px;
    user-select: none;
    background: #22232E;
    display: flex;
    flex-direction: column;
    color: white;
    .TestBox{
      display: flex;
    }
    .shing{
      font-family: Source Han Sans;
      font-size: 14px;
      font-weight: bold;
      line-height: normal;
      letter-spacing: 0em;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #409EFF;
      text-shadow: 0px 6px 10px rgba(64, 158, 255, 0.6);
    }
    
    .modeChoose{
      width:220px;
      height: 837px;
      border-radius: 6px;
      margin: 10px 10px;
      background-color: #2B2C37;
      .p_1{
        margin-left: 10px;
      }
      .mode_choose{
        position: relative;
        display: flex;
        flex-direction: column;
        width: 200px;
        height: 165px;
        background-color: #262731;
        margin-top: 10px;
        margin-left: 10px;
        .moveLine{
            position: absolute;
            left: 0px;
            top: 0px;
            width: 200px;
            height: 3px;
            border-radius: 4px;
            opacity: 1;
            animation: testing 5s linear infinite;
            /* 自动布局 */
            display: flex;
            background: linear-gradient(270deg, #409EFF 0%, rgba(64, 158, 255, 0) 100%);
        }
        @keyframes testing 
        {
            0% {
              width: 0px;
            }

            100% {
              width: 200px;
            }
        }
        .line{
          display: flex;
          width: 100%;
          align-items: center;
          font-size: 12px;
          color: white;
          margin-top: 10px;
        }
        .des{
          color: #999999;
        }
        .chunk{
          width:4px;
          height: 12px;
          background: #409EFF;
          box-shadow: 2px 0px 6px 0px rgba(64, 158, 255, 0.8);
          margin-right: 10px;
        }
        .unchunk{
          width:4px;
          height: 12px;
          background: #999999;
          box-shadow: 2px 0px 6px 0px rgba(153, 153, 153, 0.8);
          margin-right: 10px;
        }
        .color1{
          background-color: #35D57D !important;
        }
        .color2{
          background-color: transparent !important;
          box-shadow: none !important;
        }
        .color3{
          background-color:#FBAC14!important;
          box-shadow: none !important;
        }
        .waitTest{
          width:100%;
          height: 130px;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #666666;
          font-size: 14px;
          font-weight: 700;
        }

        .text{
          width: 125px;
        }
        .discrib{
          margin-left:10px;
          width: 90%;
          color: #666666;
          font-size: 12px;
          height: 70px;
        }
        .Test_Info{
          font-size: 12px;
          display: flex;
          flex-direction: column;
          div{
            margin-top:10px;
            margin-left: 15px;
          }
          .disc{
            color: #999999;
          }
        }
      }
      .settings{
        margin-left: 10px;
        display: flex;
        align-items: center;
        span{
          font-size: 12px;
          color: #666666;
          margin-right: 14px;
        }
        p{
          margin-left:6px;
          font-size:12px;
          color: white;
        }

      }
    }

    .versionBox{
      width: 1030px;
      .tips{
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 20px;
        margin-top: 20px;
        span{
          color: #999999;
          font-size: 12px;
        }
      }
      .thebox{
        border-radius: 6px;
        width: 1030px;
        height:265px;
        background-color:#2B2C37;
        margin-top: 10px;
        display: flex;
        .left{
          width: 265px;
          height: 265px;
          .cpu{
            margin-top: 5px;
            margin-left: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
          }
          .infoLine{
            display: flex;
            align-items: center;
           .L_1{
            margin-left:10px;
            text-align: left;
            width: 85px;
           }
           .L_2{
            display: flex;
            justify-content: center;
            width: 80px;
           }
           .L_3{
            display: flex;
            justify-content: center;
            width: 80px;
           }
          }
        }
        .right{
          display: flex;
          flex-direction: column;
          width: 750px;
          height: 265px;
          .options{
            margin-top: 10px;
            display: flex;
            margin-right: 10px;
          }
          .charts{
            margin-top: 10px;
            width: 750px;
            height: 180px;
            background-color: #22232E;
          }
        }
      }
    }
    .memoryBox
    {
      width: 1030px;
      height: 100px;
      display: flex;
      flex-direction: column;
      background-color:#2B2C37;
      border-radius: 6px;
      margin-top: 10px;
      .memory{
         font-size: 12px;
         margin-left: 20px;
         display: flex;
         align-items: center;
         margin-top: 10px;
        p{
          color: #999999;
          margin: 0 2px 0 5px;
        }
        span{
          color: #FFFFFF;
        }
      }
      .memoryOut{
        width: 1000px;
        height: 80px;
        display: flex;
        flex-wrap: wrap;
        font-size: 12px;
        color: #FFFFFF;
      }
      .memoryStatus{
        width: 450px;
        margin-left: 20px;
        height: 30px;
        display: flex;
        align-items: center;
      
      }
    }

    .cpuLimitBox
    {
      margin-top: 10px;
      width: 1030px;
      height: 100px;
      display: flex;
      border-radius: 6px;
      .LimitLeft{
        width: 850px;
        height: 140px;
        display: flex;
        flex-direction: column;
        background-color:#2B2C37;
        .limitcontainer{
          overflow: auto;
        }
        .limitbox{
          margin-top: 10px;
          margin-left: 10px;
          display: flex;
          flex-direction: column;
          background-color:#262731;
          border-radius: 6px;
          color: rgb(166, 67, 67);
          border-radius: 4px;
          width: 100%;
          padding: 10px 0;
          .line{
            width: 100%;
            display: flex;
            align-items: center;
            height: 24px;
          }
          .limit_content{
            height: 100%;
            width: 100%;
            margin-top: 5px;
            display: flex;
            flex-wrap: wrap;
            .limit_box{
              background-color:#F14343;
              color: white;
              height: 20px;
              padding: 3px 3px;
            }
          }
        }
      }
      .scroll::-webkit-scrollbar {
      width: 5px;
      transition: 0.25s;
      }

      .scroll::-webkit-scrollbar-thumb {
        background: #71738C;
        border-radius: 3px;
      }

      .scroll::-webkit-scrollbar-thumb:hover {
        background-color: #71738C;
      }

      .scroll::-webkit-scrollbar-track {
        background-color: #2D2E39;
        width: 2px;
      }

      .LimitRight
      {
        display: flex;
        flex-direction: column;
        margin-left: 10px;
        height: 140px;
        .history
        {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 170px;
          height: 40px;
          background-color:#3E4050;
          border-radius: 4px;
          cursor: pointer;
        }
        .history:hover{
          opacity: 0.8;
        }
        .starTest
        {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 10px;
          width: 170px;
          height: 90px;
          border-radius: 4px;
          font-size: 18px;
          cursor: pointer;
        }
        .starTest:hover{
          opacity: 0.8;
        }
      }
    }
  }
  .Nav{
        width: 1280px;
        height: 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #2B2C37;
        .GPPInfo{
          display: flex;
          align-items: center;
          img{
            width: 14px;
            height: 14px;
            margin: 0 10px;
          }
          .slogan{
            font-size: 14px;
            color: white;
            margin: 0 5px;
          }
        }
      }
      .echarts-tooltip {
        top: -50% !important;
        margin-top: -45px;
        border: 0 !important;
        background: #373947 !important;
        opacity: 0.95!important;
      }
</style>
<style lang="scss">
.modeChoose{
  .el-input{
          --el-input-border-color:#191A1D !important;
          --el-input-hover-border:#191A1D !important;
          --el-input-border:#191A1D !important;
          --el-input-bg-color:#191A1D !important;
          --el-input-transparent-border:transparent !important;
        }
        .el-input__inner{
          color:white !important;
        }
  .el-input__wrapper{
    border-radius: 1px !important;
  }
}

</style>
