{"xml":"<?xml version=\"1.0\"?>\n<!DOCTYPE HWINFO SYSTEM \"HWiNFO32log.dtd\">\n<HWINFO>\n\n<COMPUTER>\n\t<NodeName>WIN-20241005PEJ</NodeName>\n\t\t<ScanTime>03.01.2025 16:34</ScanTime>\n\t\t<Property>\n\t\t\t<Entry>Computer Name</Entry>\n\t\t\t<Description>WIN-20241005PEJ</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Operating System</Entry>\n\t\t\t<Description>Microsoft Windows 11 Professional (x64) Build 22631.4169 (23H2)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>UEFI Boot</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Secure Boot</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hypervisor-protected Code Integrity (HVCI)</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n<SubNodes>\n\n<CPU>\n\t<NodeName>Central Processor(s)</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Number Of Processor Packages (Physical)</Entry>\n\t\t\t<Description>1</Description>\n\t\t\t<PropertyType>CPU_COUNT</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Number Of Processor Cores</Entry>\n\t\t\t<Description>6</Description>\n\t\t\t<PropertyType>CPU_COUNT</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Number Of Logical Processors</Entry>\n\t\t\t<Description>12</Description>\n\t\t</Property>\n<SubNode>\n\t<NodeName>AMD Ryzen 5 5600</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Processor Name</Entry>\n\t\t\t<Description>AMD Ryzen 5 5600</Description>\n\t\t\t<PropertyType>CPU_NAME</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Processor Frequency</Entry>\n\t\t\t<Description>4700.0 MHz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Processor Frequency [MHz]</Entry>\n\t\t\t<Description>4700</Description>\n\t\t\t<PropertyType>CPU_SPEED</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CPU ID</Entry>\n\t\t\t<Description>00A20F12</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Extended CPU ID</Entry>\n\t\t\t<Description>00A20F12</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CPU Brand Name</Entry>\n\t\t\t<Description>AMD Ryzen 5 5600 6-Core Processor</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CPU Vendor</Entry>\n\t\t\t<Description>AuthenticAMD</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CPU Stepping</Entry>\n\t\t\t<Description>VMR-B2</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CPU Code Name</Entry>\n\t\t\t<Description>Vermeer</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CPU Technology</Entry>\n\t\t\t<Description>7 nm</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CPU OPN</Entry>\n\t\t\t<Description>100-000000927</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CPU Thermal Design Power (TDP)</Entry>\n\t\t\t<Description>65.0 W</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CPU Thermal Design Current (TDC)</Entry>\n\t\t\t<Description>Fused: 60.0 A, No Limit</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CPU Electrical Design Current (EDC)</Entry>\n\t\t\t<Description>Fused: 90.0 A, No Limit</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CPU Package Power Tracking (PPT)</Entry>\n\t\t\t<Description>Fused: 76.0 W, No Limit</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CPU Max. Junction Temperature (Tj,max)</Entry>\n\t\t\t<Description>95 °C</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CPU PBO Scalar (Reliability Reduction)</Entry>\n\t\t\t<Description>0x (OC-Mode)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CPU Thermal Trip Limit</Entry>\n\t\t\t<Description>115.0 °C</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CPU HTC Temperature Limit</Entry>\n\t\t\t<Description>115.5 °C</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CPU Platform</Entry>\n\t\t\t<Description>AM4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Microcode Update Revision</Entry>\n\t\t\t<Description>A201210</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>SMU Firmware Revision</Entry>\n\t\t\t<Description>56.76.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Core Performance Order</Entry>\n\t\t\t<Description>2, 1, 6, 4, 3, 5</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Core Performance Order (CPPC)</Entry>\n\t\t\t<Description>1, 2, 6, 3, 4, 5</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>cLDO VDDP</Entry>\n\t\t\t<Description>0.9976 V</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>cLDO VDDG IOD</Entry>\n\t\t\t<Description>0.9976 V</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>cLDO VDDG CCD</Entry>\n\t\t\t<Description>0.9976 V</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Number of CPU Cores</Entry>\n\t\t\t<Description>6</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Number of Logical CPUs</Entry>\n\t\t\t<Description>12</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CPU Minimum</Entry>\n\t\t\t<Description>550.0 MHz = 5.50 x 100.0 MHz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CPU Base</Entry>\n\t\t\t<Description>4700.0 MHz = 47.00 x 100.0 MHz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CPU Boost Max (Fmax)</Entry>\n\t\t\t<Description>4450.0 MHz = 44.50 x 100.0 MHz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Precision Boost Clock Limit</Entry>\n\t\t\t<Description>4450.0 MHz = 44.50 x 100.0 MHz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CPU High Temperature Clock Limit</Entry>\n\t\t\t<Description>4200 MHz &gt;= 92 °C</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CPU Automatic Overclocking Offset</Entry>\n\t\t\t<Description>0 MHz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CPU Current</Entry>\n\t\t\t<Description>4691.5 MHz = 47.00 x 99.8 MHz @ 1.2250 V</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CPU Bus Type</Entry>\n\t\t\t<Description>UMI</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L1 Cache</Entry>\n\t\t\t<Description>Instruction: 6 x 32 KBytes, Data: 6 x 32 KBytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L2 Cache</Entry>\n\t\t\t<Description>Integrated: 6 x 512 KBytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L3 Cache</Entry>\n\t\t\t<Description>32 MBytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Instruction TLB</Entry>\n\t\t\t<Description>Fully associative, 64 entries</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Data TLB</Entry>\n\t\t\t<Description>Fully associative, 64 entries</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>FPU on Chip</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Enhanced Virtual-86 Mode</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Breakpoints</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Page Size Extensions</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Time Stamp Counter</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Pentium-style Model Specific Registers</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Physical Address Extension</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Machine Check Exception</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CMPXCHG8B Instruction</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>APIC On Chip / PGE (AMD)</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast System Call</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Type Range Registers</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Page Global Feature</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Machine Check Architecture</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CMOV Instruction</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Page Attribute Table</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>36-bit Page Size Extensions</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Processor Number</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CLFLUSH Instruction</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Debug Trace and EMON Store</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Internal ACPI Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>MMX Technology</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast FP Save/Restore (IA MMX-2)</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Streaming SIMD Extensions</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Streaming SIMD Extensions 2</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Self-Snoop</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Multi-Threading Capable</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Automatic Clock Control</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>IA-64 Processor</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Signal Break on FERR</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Streaming SIMD Extensions 3</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCLMULQDQ Instruction Support</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>MONITOR/MWAIT Support</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Supplemental Streaming SIMD Extensions 3</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>FMA Extension</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CMPXCHG16B Support</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Streaming SIMD Extensions 4.1</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Streaming SIMD Extensions 4.2</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>x2APIC</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>POPCNT Instruction</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AES Cryptography Support</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>XSAVE/XRSTOR/XSETBV/XGETBV Instructions</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>XGETBV/XSETBV OS Enabled</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AVX Support</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Half-Precision Convert (CVT16)</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>FPU on Chip</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Enhanced Virtual-86 Mode</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Breakpoints</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Page Size Extensions</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Time Stamp Counter</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AMD-style Model Specific Registers</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Machine Check Exception</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CMPXCHG8B Instruction</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>APIC On Chip</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>SYSCALL and SYSRET Instructions</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Type Range Registers</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Page Global Feature</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Machine Check Architecture</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CMOV Instruction</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Page Attribute Table</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>36-bit Page Size Extensions</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Multi-Processing / Brand feature</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>No Execute</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>MMX Technology</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>MMX+ Extensions</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast FP Save/Restore</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast FP Save/Restore Optimizations</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>1 GB large page support</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>RDTSCP Instruction</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>x86-64 Long Mode</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>3DNow! Technology Extensions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>3DNow! Technology</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bit Manipulation Instructions Set 1</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bit Manipulation Instructions Set 2</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Advanced Vector Extensions 2 (AVX2)</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Advanced Vector Extensions 512 (AVX-512) Foundation</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AVX-512 Prefetch Instructions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AVX-512 Exponential and Reciprocal Instructions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AVX-512 Conflict Detection Instructions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AVX-512 Doubleword and Quadword Instructions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AVX-512 Byte and Word Instructions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AVX-512 Vector Length Extensions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AVX-512 52-bit Integer FMA Instructions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Secure Hash Algorithm (SHA) Extensions</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Software Guard Extensions (SGX) Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Supervisor Mode Execution Protection (SMEP)</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Supervisor Mode Access Prevention (SMAP)</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware Lock Elision (HLE)</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Restricted Transactional Memory (RTM)</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Protection Extensions (MPX)</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Read/Write FS/GS Base Instructions</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Enhanced Performance String Instruction</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>INVPCID Instruction</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>RDSEED Instruction</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Multi-precision Add Carry Instructions (ADX)</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCOMMIT Instructions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CLFLUSHOPT Instructions</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CLWB Instructions</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>TSC_THREAD_OFFSET</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Platform Quality of Service Monitoring (PQM)</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Platform Quality of Service Enforcement (PQE)</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>FPU Data Pointer updated only on x87 Exceptions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Deprecated FPU CS and FPU DS</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Intel Processor Trace</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PREFETCHWT1 Instruction</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AVX-512 Vector Bit Manipulation Instructions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AVX-512 Vector Bit Manipulation Instructions 2</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AVX-512 Galois Fields New Instructions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AVX-512 Vector AES</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AVX-512 Vector Neural Network Instructions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AVX-512 Bit Algorithms</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AVX-512 Carry-Less Multiplication Quadword (VPCLMULQDQ)</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AVX-512 Vector POPCNT (VPOPCNTD/VPOPCNTQ)</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>User-Mode Instruction Prevention</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Protection Keys for User-mode Pages</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>OS Enabled Protection Keys</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Wait and Pause Enhancements (WAITPKG)</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Total Memory Encryption</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Key Locker</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>57-bit Linear Addresses, 5-level Paging</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Read Processor ID</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>OS Bus-Lock Detection</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Cache Line Demote</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>MOVDIRI: Direct Stores</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>MOVDIR64B: Direct Stores</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>ENQCMD: Enqueue Stores</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>SGX Launch Configuration</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Protection Keys for Supervisor-Mode Pages</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Control-Flow Enforcement Technology (CET) Shadow Stack</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Attestation Services for Intel SGX</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AVX-512 4 x Vector Neural Network Instructions Word Variable Precision</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AVX-512 4 x Fused Multiply Accumulation Packed Single Precision</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Short REP MOV</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>User Interrupts</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AVX-512 VP2INTERSECT Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AVX-512 FP16</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>MD_CLEAR Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>IA32_MCU_OPT_CTRL MSR Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Restricted Transactional Memory (RTM) Always Abort</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Restricted Transactional Memory (RTM) Force Abort</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>SERIALIZE</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hybrid Processor</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>TSX Suspend Load Address Tracking</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Platform Configuration (PCONFIG)</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Architectural LBRs</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Indirect Branch Restricted Speculation (IBRS), Indirect Branch Predictor Barrier (IBPB)</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Single Thread Indirect Branch Predictors (STIBP)</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L1D_FLUSH Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>IA32_ARCH_CAPABILITIES MSR</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>IA32_CORE_CAPABILITIES MSR</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Speculative Store Bypass Disable (SSBD)</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Control-Flow Enforcement Technology (CET) Indirect Branch Tracking</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Advanced Matrix Extensions (AMX) Tile Architecture</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Advanced Matrix Extensions (AMX) bfloat16 Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Advanced Matrix Extensions (AMX) 8-bit Integer Operations</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>SHA512 Instructions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>SM3 Instructions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>SM4 Instructions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Advanced Matrix Extensions (AMX) FP16 Instructions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AVX (VEX-encoded) Vector Neural Network Instructions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AVX-512 BFLOAT16 Instructions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Zero-Length MOVSB</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Short STOSB</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Short CMPSB, SCASB</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>History Reset</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Linear Address Masking</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Linear Address Space Separation</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>RAO-INT Instructions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CMPccXADD Instructions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Flexible Return and Event Delivery (FRED)</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>LKGS Instruction</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>WRMSRNS Instruction</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>NMI-source Reporting</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AVX-IFMA Instructions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>RD/WR MSRLIST Instructions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>INVD Execution Prevention After BIOS-Done</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Move Read-Shared Value (MOVRS)</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Protected Processor Inventory Number (IA32_PPIN) Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PBNDKB Instruction</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>X86S (Legacy-reduced OS ISA)</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>64-bit SIPI</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>RDMSR/WRMSR Immediate Forms</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AVX-VNNI-INT8 Instructions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AVX-VNNI-INT16 Instructions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AVX-NE-CONVERT Instructions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PREFETCHIT0/1 Instructions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>URDMSR/UWRMSR Instructions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AMX-COMPLEX Instructions</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CET Supervisor Shadow-Stack</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>UIRET Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Advanced Vector Extensions 10 (AVX10)</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Advanced Performance Extensions (APX) Foundation</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Not Exhibiting MXCSR Configuration Dependent Timing (MCDT)</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>UC-Lock Disable Feature</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>LAHF/SAHF Long Mode Support</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Core Multi-Processing Legacy Mode</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Secure Virtual Machine</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Extended APIC Register Space</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>LOCK MOV CR0 Support</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Advanced Bit Manipulation</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>SSE4A Support</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Misaligned SSE Mode</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PREFETCH(W) Support</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>OS Visible Work-around Support</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Instruction Based Sampling</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>XOP Instruction Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>SKINIT, STGI, and DEV Support</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Watchdog Timer Support</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>TBM0 Instruction Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Lightweight Profiling Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>FMA4 Instruction Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Translation Cache Extension</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>NodeId Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Trailing Bit Manipulation</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Topology Extensions</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Core Performance Counter Extensions</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>NB Performance Counter Extensions</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Streaming Performance Monitor Architecture</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Data Breakpoint Extension</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Performance Time-Stamp Counter</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L2I Performance Counter Extensions</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>MWAITX/MONITORX Support</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Secure Memory Encryption</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Secure Encrypted Virtualization</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Core Performance Boost</Entry>\n\t\t\t<Description>Supported, Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Physical Address Size</Entry>\n\t\t\t<Description>48-bit (256 TBytes)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Virtual Address Size</Entry>\n\t\t\t<Description>48-bit (256 TBytes)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Range 0-80000000 (0MB-2048MB) Type</Entry>\n\t\t\t<Description>Write Back (WB)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Range 80000000-C0000000 (2048MB-3072MB) Type</Entry>\n\t\t\t<Description>Write Back (WB)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Range BCD10000-BCD20000 (3021MB-3021MB) Type</Entry>\n\t\t\t<Description>Uncacheable (UC)</Description>\n\t\t</Property>\n</SubNode>\n</CPU>\n<MOBO>\n\t<NodeName>Motherboard</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Computer Brand Name</Entry>\n\t\t\t<Description>Unknown or Noname</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Motherboard Model</Entry>\n\t\t\t<Description>ASRock B450M-HDV R4.0</Description>\n\t\t\t<PropertyType>SYSTEM_MAINBOARD</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Motherboard Chipset</Entry>\n\t\t\t<Description>AMD B450 (Low-Power Promontory PROM26.A)</Description>\n\t\t\t<PropertyType>SYSTEM_CHIPSET</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Motherboard Slots</Entry>\n\t\t\t<Description>4xPCI Express x1, 1xPCI Express x2, 1xPCI Express x4, 1xPCI Express x8, 1xPCI Express x16</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Express Version Supported</Entry>\n\t\t\t<Description>v3.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>USB Version Supported</Entry>\n\t\t\t<Description>v3.1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>BIOS Manufacturer</Entry>\n\t\t\t<Description>American Megatrends</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>BIOS Date</Entry>\n\t\t\t<Description>08/21/2024</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>BIOS Version</Entry>\n\t\t\t<Description>P10.31</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>UEFI BIOS</Entry>\n\t\t\t<Description>Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Super-IO/LPC Chip</Entry>\n\t\t\t<Description>Nuvoton NCT6793D/NCT5563D</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Trusted Platform Module (TPM) Chip</Entry>\n\t\t\t<Description>Hardware TPM, version 2.0</Description>\n\t\t</Property>\n<SubNode>\n\t<NodeName>ACPI Devices</NodeName>\n<SubNode>\n\t<NodeName>AMD GPIO Controller</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD GPIO Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>IRQ</Entry>\n\t\t\t<Description>7</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>FED81500</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>IRQ</Entry>\n\t\t\t<Description>7</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD GPIO Controller</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD GPIO Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>FEC30000 - FEC30FFF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>FEC30000 - FEC30FFF</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>Trusted Platform Module 2.0</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>Trusted Platform Module 2.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>BCB42000 - BCB45FFF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>BCB42000 - BCB45FFF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>BCB46000 - BCB49FFF</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>Programmable interrupt controller</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>Programmable interrupt controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0020 - 0021</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>IRQ</Entry>\n\t\t\t<Description>65792</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0020 - 0021</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>00A0 - 00A1</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>System timer</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>System timer</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0040 - 0043</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0040 - 0043</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>IRQ</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>High precision event timer</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>High precision event timer</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>IRQ</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>FED00000</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>IRQ</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>IRQ</Entry>\n\t\t\t<Description>8</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>Direct memory access controller</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>Direct memory access controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0089 - 008B</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DMA</Entry>\n\t\t\t<Description>4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0000 - 000F</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0081 - 0083</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0087</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0089 - 008B</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>008F</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>00C0 - 00DF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DMA</Entry>\n\t\t\t<Description>4</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>通信端口</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>通信端口</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>03F8 - 03FF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>03F8 - 03FF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>IRQ</Entry>\n\t\t\t<Description>4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>03F8 - 03FF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>IRQ</Entry>\n\t\t\t<Description>4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>02F8 - 02FF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>IRQ</Entry>\n\t\t\t<Description>3</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>03E8 - 03EF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>IRQ</Entry>\n\t\t\t<Description>4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>02E8 - 02EF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>IRQ</Entry>\n\t\t\t<Description>3</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>System speaker</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>System speaker</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0061</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0061</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>PCI Express Root Complex</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>PCI Express Root Complex</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0000 - FFFFFFFF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>03B0 - 03DF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>000A0000 - 0009FFFF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>C0000000 - FEC2FFFF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0000 - 03AF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>03E0 - 0CF7</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>03B0 - 03DF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0D00 - FFFF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>000A0000 - 000BFFFF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>000C0000 - 000DFFFF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>C0000000 - FEC2FFFF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>FEE00000 - FFFFFFFF</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>System CMOS/real time clock</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>System CMOS/real time clock</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0070 - 0071</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0070 - 0071</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>System board</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>System board</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>F0000000 - F7FFFFFF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>F0000000 - F7FFFFFF</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>Motherboard resources</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>Motherboard resources</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0280 - 028F</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0280 - 028F</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0290 - 029F</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>02A0 - 02AF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>02B0 - 02BF</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>Motherboard resources</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>Motherboard resources</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>FEB80000 - FEBFFFFF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>FEB80000 - FEBFFFFF</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>Motherboard resources</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>Motherboard resources</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>FD200000 - FD2FFFFF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>FD200000 - FD2FFFFF</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>Motherboard resources</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>Motherboard resources</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0010 - 001F</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0067 - 006F</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0088</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>00B1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>04D6</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0C52</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0000 - 0C6E</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0800 - 089F</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0910 - 091F</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>00000000 - 0000008F</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>IRQ</Entry>\n\t\t\t<Description>1114369</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0010 - 001F</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0022 - 003F</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0063</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0065</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0067 - 006F</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0072 - 007F</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0080</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0084 - 0086</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0088</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>008C - 008E</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0090 - 009F</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>00A2 - 00BF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>00B1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>00E0 - 00EF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>04D0 - 04D1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>040B</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>04D6</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0C00 - 0C01</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0C14</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0C50 - 0C51</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0C52</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0C6C</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0C6F</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0CD8 - 0CDF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0800 - 089F</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0B00 - 0B0F</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0B20 - 0B3F</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0900 - 090F</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Port</Entry>\n\t\t\t<Description>0910 - 091F</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>FEC00000 - FEC00FFF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>FEC01000 - FEC01FFF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>FEDC0000</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>FEE00000</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>FED80000 - FED8FFFF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>FEC10000 - FEC10FFF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Location</Entry>\n\t\t\t<Description>FF000000</Description>\n\t\t</Property>\n</SubNode>\n</SubNode>\n<SubNode>\n\t<NodeName>SMBIOS DMI</NodeName>\n<SubNode>\n\t<NodeName>BIOS</NodeName>\n\t\t<Property>\n\t\t\t<Entry>BIOS Vendor</Entry>\n\t\t\t<Description>American Megatrends Inc.</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>BIOS Version</Entry>\n\t\t\t<Description>P10.31</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>BIOS Release Date</Entry>\n\t\t\t<Description>08/21/2024</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>BIOS Start Segment</Entry>\n\t\t\t<Description>F000</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>BIOS Size</Entry>\n\t\t\t<Description>16 MBytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>System BIOS Version</Entry>\n\t\t\t<Description>5.17</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>ISA Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>MCA Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>EISA Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Support</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PC Card (PCMCIA) Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Plug-and-Play Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>APM Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Flash BIOS</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>BIOS Shadow</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>VL-VESA Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>ESCD Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Boot from CD</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Selectable Boot</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>BIOS ROM Socketed</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Boot from PC Card</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>EDD Support</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>NEC PC-98 Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>ACPI Support</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>USB Legacy Support</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AGP Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I2O Boot Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>LS-120 Boot Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>ATAPI ZIP Drive Boot Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>IEE1394 Boot Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Smart Battery Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>BIOS Boot Specification Support</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Function key-initiated Network Service Boot Support</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Targeted Content Distribution Support</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>UEFI Specification Support</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Virtual Machine</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>System</NodeName>\n\t\t<Property>\n\t\t\t<Entry>System Manufacturer</Entry>\n\t\t\t<Description>To Be Filled By O.E.M.</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Product Name</Entry>\n\t\t\t<Description>B450M-HDV R4.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Product Version</Entry>\n\t\t\t<Description>To Be Filled By O.E.M.</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Product Serial Number</Entry>\n\t\t\t<Description>To Be Filled By O.E.M.</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>UUID</Entry>\n\t\t\t<Description>{F659A1A8-**************-000000000000}</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>SKU Number</Entry>\n\t\t\t<Description>To Be Filled By O.E.M.</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Family</Entry>\n\t\t\t<Description>To Be Filled By O.E.M.</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>Mainboard</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Mainboard Manufacturer</Entry>\n\t\t\t<Description>ASRock</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Mainboard Name</Entry>\n\t\t\t<Description>B450M-HDV R4.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Mainboard Version</Entry>\n\t\t\t<Description></Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Mainboard Serial Number</Entry>\n\t\t\t<Description>BR80G2001900281</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Asset Tag</Entry>\n\t\t\t<Description>                      </Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location in chassis</Entry>\n\t\t\t<Description>                      </Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>System Enclosure</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Manufacturer</Entry>\n\t\t\t<Description>To Be Filled By O.E.M.</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Case Type</Entry>\n\t\t\t<Description>Desktop</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Version</Entry>\n\t\t\t<Description>To Be Filled By O.E.M.</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Serial Number</Entry>\n\t\t\t<Description>To Be Filled By O.E.M.</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Asset Tag Number</Entry>\n\t\t\t<Description>To Be Filled By O.E.M.</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>OEM Strings</NodeName>\n\t\t<Property>\n\t\t\t<Entry></Entry>\n\t\t\t<Description>To Be Filled By O.E.M.</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>System Boot Information</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Boot Status</Entry>\n\t\t\t<Description>No error occurred</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>Additional Information</NodeName>\n\t\t<Property>\n\t\t\t<Entry></Entry>\n\t\t\t<Description>MORDOR</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>Processor Additional Information</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Firmware Component Name</Entry>\n\t\t\t<Description></Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Firmware Manufacturer</Entry>\n\t\t\t<Description></Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Firmware Version</Entry>\n\t\t\t<Description></Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Firmware Release Date</Entry>\n\t\t\t<Description></Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>TPM</NodeName>\n\t\t<Property>\n\t\t\t<Entry>TPM Specification Version</Entry>\n\t\t\t<Description>2.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>TPM Vendor</Entry>\n\t\t\t<Description>AMD</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>TPM Description</Entry>\n\t\t\t<Description>AMD</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>L1 - Cache</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Socket Designation</Entry>\n\t\t\t<Description>L1 - Cache</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Cache State</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Cache Location</Entry>\n\t\t\t<Description>Internal</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Cache Type</Entry>\n\t\t\t<Description>L1 Unified</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Cache Scheme</Entry>\n\t\t\t<Description>Write-Back</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Supported SRAM Type</Entry>\n\t\t\t<Description>Pipeline Burst</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current SRAM Type</Entry>\n\t\t\t<Description>Pipeline Burst</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Cache Speed</Entry>\n\t\t\t<Description>1 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Error Correction Type</Entry>\n\t\t\t<Description>Multi-bit ECC</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Cache Size</Entry>\n\t\t\t<Description>384 KBytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Installed Cache Size</Entry>\n\t\t\t<Description>384 KBytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Cache Associativity</Entry>\n\t\t\t<Description>8-way Set-Associative</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>L2 - Cache</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Socket Designation</Entry>\n\t\t\t<Description>L2 - Cache</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Cache State</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Cache Location</Entry>\n\t\t\t<Description>Internal</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Cache Type</Entry>\n\t\t\t<Description>L2 Unified</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Cache Scheme</Entry>\n\t\t\t<Description>Write-Back</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Supported SRAM Type</Entry>\n\t\t\t<Description>Pipeline Burst</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current SRAM Type</Entry>\n\t\t\t<Description>Pipeline Burst</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Cache Speed</Entry>\n\t\t\t<Description>1 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Error Correction Type</Entry>\n\t\t\t<Description>Multi-bit ECC</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Cache Size</Entry>\n\t\t\t<Description>3072 KBytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Installed Cache Size</Entry>\n\t\t\t<Description>3072 KBytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Cache Associativity</Entry>\n\t\t\t<Description>8-way Set-Associative</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>L3 - Cache</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Socket Designation</Entry>\n\t\t\t<Description>L3 - Cache</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Cache State</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Cache Location</Entry>\n\t\t\t<Description>Internal</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Cache Type</Entry>\n\t\t\t<Description>L3 Unified</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Cache Scheme</Entry>\n\t\t\t<Description>Write-Back</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Supported SRAM Type</Entry>\n\t\t\t<Description>Pipeline Burst</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current SRAM Type</Entry>\n\t\t\t<Description>Pipeline Burst</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Cache Speed</Entry>\n\t\t\t<Description>1 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Error Correction Type</Entry>\n\t\t\t<Description>Multi-bit ECC</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Cache Size</Entry>\n\t\t\t<Description>32768 KBytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Installed Cache Size</Entry>\n\t\t\t<Description>32768 KBytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Cache Associativity</Entry>\n\t\t\t<Description>16-way Set-Associative</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>Processor</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Processor Manufacturer</Entry>\n\t\t\t<Description>Advanced Micro Devices, Inc.</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Processor Version</Entry>\n\t\t\t<Description>AMD Ryzen 5 5600 6-Core Processor              </Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>External Clock</Entry>\n\t\t\t<Description>100 MHz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Clock Supported</Entry>\n\t\t\t<Description>4450 MHz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Clock</Entry>\n\t\t\t<Description>4700 MHz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CPU Socket</Entry>\n\t\t\t<Description>Populated</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CPU Status</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Processor Type</Entry>\n\t\t\t<Description>Central Processor</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Processor Voltage</Entry>\n\t\t\t<Description>1.3 V</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Processor Upgrade</Entry>\n\t\t\t<Description>Socket AM4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Socket Designation</Entry>\n\t\t\t<Description>AM4</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>Memory Devices</NodeName>\n<SubNode>\n\t<NodeName>32-bit Memory Error Information</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>Physical Memory Array</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Array Location</Entry>\n\t\t\t<Description>System board</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Array Use</Entry>\n\t\t\t<Description>System memory</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Error Detecting Method</Entry>\n\t\t\t<Description>None</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Capacity</Entry>\n\t\t\t<Description>128 GBytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Devices</Entry>\n\t\t\t<Description>2</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>Memory Array Mapped Address</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Starting Address</Entry>\n\t\t\t<Description>00000000</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Ending Address</Entry>\n\t\t\t<Description>002FFFFF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Partition Width</Entry>\n\t\t\t<Description>2</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>Memory Array Mapped Address</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Starting Address</Entry>\n\t\t\t<Description>00400000</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Ending Address</Entry>\n\t\t\t<Description>020FFFFF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Partition Width</Entry>\n\t\t\t<Description>2</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>32-bit Memory Error Information</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>Memory Device</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Total Width</Entry>\n\t\t\t<Description>64 bits</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Data Width</Entry>\n\t\t\t<Description>64 bits</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Size</Entry>\n\t\t\t<Description>16384 MBytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Form Factor</Entry>\n\t\t\t<Description>DIMM</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Locator</Entry>\n\t\t\t<Description>DIMM 0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bank Locator</Entry>\n\t\t\t<Description>P0 CHANNEL A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Type</Entry>\n\t\t\t<Description>DDR4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Type Detail</Entry>\n\t\t\t<Description>Synchronous</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Speed</Entry>\n\t\t\t<Description>3600 MHz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Manufacturer</Entry>\n\t\t\t<Description>Unknown</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Serial Number</Entry>\n\t\t\t<Description>000001BB</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Part Number</Entry>\n\t\t\t<Description>TLD416G32A40</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Asset Tag</Entry>\n\t\t\t<Description></Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>Memory Device Mapped Address</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Starting Address</Entry>\n\t\t\t<Description>00000000</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Ending Address</Entry>\n\t\t\t<Description>01FFFFFF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Partition Row Position</Entry>\n\t\t\t<Description>Unknown</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interleave Position</Entry>\n\t\t\t<Description>Unknown</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interleave Data Depth</Entry>\n\t\t\t<Description>Unknown</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>32-bit Memory Error Information</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>Memory Device</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Total Width</Entry>\n\t\t\t<Description>64 bits</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Data Width</Entry>\n\t\t\t<Description>64 bits</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Size</Entry>\n\t\t\t<Description>16384 MBytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Form Factor</Entry>\n\t\t\t<Description>DIMM</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Locator</Entry>\n\t\t\t<Description>DIMM 0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bank Locator</Entry>\n\t\t\t<Description>P0 CHANNEL B</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Type</Entry>\n\t\t\t<Description>DDR4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Type Detail</Entry>\n\t\t\t<Description>Synchronous</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Speed</Entry>\n\t\t\t<Description>3600 MHz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Manufacturer</Entry>\n\t\t\t<Description>Unknown</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Serial Number</Entry>\n\t\t\t<Description>0403CCC7</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Part Number</Entry>\n\t\t\t<Description>TLD416G32A40</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Asset Tag</Entry>\n\t\t\t<Description></Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>Memory Device Mapped Address</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Starting Address</Entry>\n\t\t\t<Description>00000000</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Ending Address</Entry>\n\t\t\t<Description>01FFFFFF</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Partition Row Position</Entry>\n\t\t\t<Description>Unknown</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interleave Position</Entry>\n\t\t\t<Description>Unknown</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interleave Data Depth</Entry>\n\t\t\t<Description>Unknown</Description>\n\t\t</Property>\n</SubNode>\n</SubNode>\n<SubNode>\n\t<NodeName>System Slots</NodeName>\n<SubNode>\n\t<NodeName>PCIE1</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Slot Designation</Entry>\n\t\t\t<Description>PCIE1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Type</Entry>\n\t\t\t<Description>PCI Express</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Usage</Entry>\n\t\t\t<Description>In use</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Data Bus Width</Entry>\n\t\t\t<Description>1x / x1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Length</Entry>\n\t\t\t<Description>Short</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Base Bus:Device:Function Number</Entry>\n\t\t\t<Description>0:0:0</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>PCIE2</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Slot Designation</Entry>\n\t\t\t<Description>PCIE2</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Type</Entry>\n\t\t\t<Description>PCI Express</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Usage</Entry>\n\t\t\t<Description>In use</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Data Bus Width</Entry>\n\t\t\t<Description>16x / x16</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Length</Entry>\n\t\t\t<Description>Long</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Base Bus:Device:Function Number</Entry>\n\t\t\t<Description>0:0:0</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>PCIE3_M2_1</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Slot Designation</Entry>\n\t\t\t<Description>PCIE3_M2_1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Type</Entry>\n\t\t\t<Description>PCI Express</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Usage</Entry>\n\t\t\t<Description>In use</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Data Bus Width</Entry>\n\t\t\t<Description>4x / x4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Length</Entry>\n\t\t\t<Description>Short</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Base Bus:Device:Function Number</Entry>\n\t\t\t<Description>0:0:0</Description>\n\t\t</Property>\n</SubNode>\n</SubNode>\n</SubNode>\n</MOBO>\n<MEMORY>\n\t<NodeName>Memory</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Total Memory Size</Entry>\n\t\t\t<Description>32 GBytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Total Memory Size [MB]</Entry>\n\t\t\t<Description>32768</Description>\n\t\t\t<PropertyType>SYSTEM_MEMORY</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Supported Memory Clock</Entry>\n\t\t\t<Description>Unlimited</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Memory Clock</Entry>\n\t\t\t<Description>1796.8 MHz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Timing (tCAS-tRCD-tRP-tRAS)</Entry>\n\t\t\t<Description>20-19-19-43</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Channels Supported</Entry>\n\t\t\t<Description>2</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Channels Active</Entry>\n\t\t\t<Description>2</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Command Rate (CR)</Entry>\n\t\t\t<Description>1T</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Read to Read Delay (tRDRD_SC) Same Chipselect</Entry>\n\t\t\t<Description>1T</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Read to Read Delay (tRDRD_SG/TrdrdScL) Same Bank Group</Entry>\n\t\t\t<Description>5T</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Read to Read Delay (tRDRD_SD) Same DIMM</Entry>\n\t\t\t<Description>4T</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Read to Read Delay (tRDRD_DD) Different DIMM</Entry>\n\t\t\t<Description>4T</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Write to Write Delay (tWRWR_SC) Same Chipselect</Entry>\n\t\t\t<Description>1T</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Write to Write Delay (tWRWR_SG/TwrwrScL) Same Bank Group</Entry>\n\t\t\t<Description>5T</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Write to Write Delay (tWRWR_SD) Same DIMM</Entry>\n\t\t\t<Description>6T</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Write to Write Delay (tWRWR_DD) Different DIMM</Entry>\n\t\t\t<Description>6T</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Read to Write Delay (tRDWR_SC) Same Chipselect</Entry>\n\t\t\t<Description>10T</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Read to Write Delay (tRDWR_SG/TrdwrScL) Same Bank Group</Entry>\n\t\t\t<Description>10T</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Read to Write Delay (tRDWR_DG/TrdwrScDlr) Different Bank Group</Entry>\n\t\t\t<Description>10T</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Read to Write Delay (tRDWR_SD) Same DIMM</Entry>\n\t\t\t<Description>10T</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Read to Write Delay (tRDWR_DD) Different DIMM</Entry>\n\t\t\t<Description>10T</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Write to Read Delay (tWRRD_SC) Same Chipselect</Entry>\n\t\t\t<Description>1T</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Write to Read Delay (tWRRD_SG/TwrrdScL) Same Bank Group</Entry>\n\t\t\t<Description>1T</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Write to Read Delay (tWRRD_SD) Same DIMM</Entry>\n\t\t\t<Description>1T</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Write to Read Delay (tWRRD_DD) Different DIMM</Entry>\n\t\t\t<Description>1T</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Read to Precharge Delay (tRTP)</Entry>\n\t\t\t<Description>14T</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Write to Precharge Delay (tWTP)</Entry>\n\t\t\t<Description>37T</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Write Recovery Time (tWR)</Entry>\n\t\t\t<Description>26T</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>RAS# to RAS# Delay (tRRD_L)</Entry>\n\t\t\t<Description>9T</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>RAS# to RAS# Delay (tRRD_S)</Entry>\n\t\t\t<Description>6T</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Row Cycle Time (tRC)</Entry>\n\t\t\t<Description>84T</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Refresh Cycle Time (tRFC)</Entry>\n\t\t\t<Description>630T</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Four Activate Window (tFAW)</Entry>\n\t\t\t<Description>38T</Description>\n\t\t</Property>\n<SubNode>\n\t<NodeName>Row: 0 [P0 CHANNEL A/DIMM 0] - 16 GB PC4-25600 DDR4 SDRAM Teclast TLD416G32A40</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Module Number</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Size</Entry>\n\t\t\t<Description>16 GBytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Type</Entry>\n\t\t\t<Description>DDR4 SDRAM</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Type</Entry>\n\t\t\t<Description>Unbuffered DIMM (UDIMM)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Speed</Entry>\n\t\t\t<Description>1600.0 MHz (DDR4-3200 / PC4-25600)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Manufacturer</Entry>\n\t\t\t<Description>Teclast</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Part Number</Entry>\n\t\t\t<Description>TLD416G32A40</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Revision</Entry>\n\t\t\t<Description>0.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Serial Number</Entry>\n\t\t\t<Description>3137404928 (000001BB)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Manufacturing Date</Entry>\n\t\t\t<Description>Year: 2022, Week: 40</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Manufacturing Location</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>SDRAM Manufacturer</Entry>\n\t\t\t<Description>Micron</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Error Check/Correction</Entry>\n\t\t\t<Description>None</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Row Address Bits</Entry>\n\t\t\t<Description>17</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Column Address Bits</Entry>\n\t\t\t<Description>10</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Density</Entry>\n\t\t\t<Description>16384 Mb</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Number Of Ranks</Entry>\n\t\t\t<Description>1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Number Of Bank Groups</Entry>\n\t\t\t<Description>4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Width</Entry>\n\t\t\t<Description>8 bits</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Width</Entry>\n\t\t\t<Description>64 bits</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Die Count</Entry>\n\t\t\t<Description>1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Nominal Voltage (VDD)</Entry>\n\t\t\t<Description>1.2 V</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum SDRAM Cycle Time (tCKAVGmin)</Entry>\n\t\t\t<Description>0.62500 ns (1600 MHz)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum SDRAM Cycle Time (tCKAVGmax)</Entry>\n\t\t\t<Description>1.60000 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CAS# Latencies Supported</Entry>\n\t\t\t<Description>10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum CAS# Latency Time (tAAmin)</Entry>\n\t\t\t<Description>13.750 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum RAS# to CAS# Delay (tRCDmin)</Entry>\n\t\t\t<Description>13.750 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Row Precharge Time (tRPmin)</Entry>\n\t\t\t<Description>13.750 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Active to Precharge Time (tRASmin)</Entry>\n\t\t\t<Description>32.000 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Supported Module Timing at 1600.0 MHz</Entry>\n\t\t\t<Description>22-22-22-52</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Supported Module Timing at 1466.7 MHz</Entry>\n\t\t\t<Description>21-21-21-47</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Supported Module Timing at 1333.3 MHz</Entry>\n\t\t\t<Description>19-19-19-43</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Supported Module Timing at 1200.0 MHz</Entry>\n\t\t\t<Description>17-17-17-39</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Supported Module Timing at 1066.7 MHz</Entry>\n\t\t\t<Description>15-15-15-35</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Supported Module Timing at 933.3 MHz</Entry>\n\t\t\t<Description>13-13-13-30</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Supported Module Timing at 800.0 MHz</Entry>\n\t\t\t<Description>11-11-11-26</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Supported Module Timing at 666.7 MHz</Entry>\n\t\t\t<Description>10-10-10-22</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Active to Active/Refresh Time (tRCmin)</Entry>\n\t\t\t<Description>45.750 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Refresh Recovery Time Delay (tRFC1min)</Entry>\n\t\t\t<Description>550.000 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Refresh Recovery Time Delay (tRFC2min)</Entry>\n\t\t\t<Description>350.000 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Refresh Recovery Time Delay (tRFC4min)</Entry>\n\t\t\t<Description>260.000 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Four Activate Window Delay Time (tFAWmin)</Entry>\n\t\t\t<Description>21.000 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Active to Active Delay Time - Different Bank Group (tRRD_Smin)</Entry>\n\t\t\t<Description>2.500 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Active to Active Delay Time - Same Bank Group (tRRD_Lmin)</Entry>\n\t\t\t<Description>4.900 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum CAS to CAS Delay Time - Same Bank Group (tCCD_Lmin)</Entry>\n\t\t\t<Description>5.000 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Temperature Sensor (TSOD)</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Nominal Height</Entry>\n\t\t\t<Description>31 - 32 mm</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Maximum Thickness (Front)</Entry>\n\t\t\t<Description>1 - 2 mm</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Maximum Thickness (Back)</Entry>\n\t\t\t<Description>1 - 2 mm</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Address Mapping from Edge Connector to DRAM</Entry>\n\t\t\t<Description>Standard</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>Row: 1 [P0 CHANNEL B/DIMM 0] - 16 GB PC4-25600 DDR4 SDRAM Teclast TLD416G32A40</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Module Number</Entry>\n\t\t\t<Description>1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Size</Entry>\n\t\t\t<Description>16 GBytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Type</Entry>\n\t\t\t<Description>DDR4 SDRAM</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Type</Entry>\n\t\t\t<Description>Unbuffered DIMM (UDIMM)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Speed</Entry>\n\t\t\t<Description>1600.0 MHz (DDR4-3200 / PC4-25600)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Manufacturer</Entry>\n\t\t\t<Description>Teclast</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Part Number</Entry>\n\t\t\t<Description>TLD416G32A40</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Revision</Entry>\n\t\t\t<Description>0.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Serial Number</Entry>\n\t\t\t<Description>3352036100 (0403CCC7)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Manufacturing Date</Entry>\n\t\t\t<Description>Year: 2020, Week: 36</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Manufacturing Location</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>SDRAM Manufacturer</Entry>\n\t\t\t<Description>SpecTek</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Error Check/Correction</Entry>\n\t\t\t<Description>None</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Row Address Bits</Entry>\n\t\t\t<Description>17</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Column Address Bits</Entry>\n\t\t\t<Description>10</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Density</Entry>\n\t\t\t<Description>16384 Mb</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Number Of Ranks</Entry>\n\t\t\t<Description>1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Number Of Bank Groups</Entry>\n\t\t\t<Description>4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Width</Entry>\n\t\t\t<Description>8 bits</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Width</Entry>\n\t\t\t<Description>64 bits</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Die Count</Entry>\n\t\t\t<Description>1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Nominal Voltage (VDD)</Entry>\n\t\t\t<Description>1.2 V</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum SDRAM Cycle Time (tCKAVGmin)</Entry>\n\t\t\t<Description>0.75000 ns (1333 MHz)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum SDRAM Cycle Time (tCKAVGmax)</Entry>\n\t\t\t<Description>1.60000 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CAS# Latencies Supported</Entry>\n\t\t\t<Description>10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum CAS# Latency Time (tAAmin)</Entry>\n\t\t\t<Description>14.250 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum RAS# to CAS# Delay (tRCDmin)</Entry>\n\t\t\t<Description>14.250 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Row Precharge Time (tRPmin)</Entry>\n\t\t\t<Description>14.250 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Active to Precharge Time (tRASmin)</Entry>\n\t\t\t<Description>32.000 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Supported Module Timing at 1333.3 MHz</Entry>\n\t\t\t<Description>19-19-19-43</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Supported Module Timing at 1200.0 MHz</Entry>\n\t\t\t<Description>18-18-18-39</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Supported Module Timing at 1066.7 MHz</Entry>\n\t\t\t<Description>16-16-16-35</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Supported Module Timing at 933.3 MHz</Entry>\n\t\t\t<Description>14-14-14-30</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Supported Module Timing at 800.0 MHz</Entry>\n\t\t\t<Description>12-12-12-26</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Supported Module Timing at 666.7 MHz</Entry>\n\t\t\t<Description>10-10-10-22</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Active to Active/Refresh Time (tRCmin)</Entry>\n\t\t\t<Description>46.250 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Refresh Recovery Time Delay (tRFC1min)</Entry>\n\t\t\t<Description>350.000 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Refresh Recovery Time Delay (tRFC2min)</Entry>\n\t\t\t<Description>260.000 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Refresh Recovery Time Delay (tRFC4min)</Entry>\n\t\t\t<Description>160.000 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Four Activate Window Delay Time (tFAWmin)</Entry>\n\t\t\t<Description>21.000 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Active to Active Delay Time - Different Bank Group (tRRD_Smin)</Entry>\n\t\t\t<Description>3.000 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Active to Active Delay Time - Same Bank Group (tRRD_Lmin)</Entry>\n\t\t\t<Description>4.900 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum CAS to CAS Delay Time - Same Bank Group (tCCD_Lmin)</Entry>\n\t\t\t<Description>5.000 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Temperature Sensor (TSOD)</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Nominal Height</Entry>\n\t\t\t<Description>31 - 32 mm</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Maximum Thickness (Front)</Entry>\n\t\t\t<Description>1 - 2 mm</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module Maximum Thickness (Back)</Entry>\n\t\t\t<Description>1 - 2 mm</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Address Mapping from Edge Connector to DRAM</Entry>\n\t\t\t<Description>Standard</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>XMP Revision</Entry>\n\t\t\t<Description>2.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Module VDD Voltage Level</Entry>\n\t\t\t<Description>1.35 V</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum SDRAM Cycle Time (tCKAVGmin)</Entry>\n\t\t\t<Description>0.62500 ns (1600 MHz)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CAS# Latencies Supported</Entry>\n\t\t\t<Description>11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum CAS# Latency Time (tAAmin)</Entry>\n\t\t\t<Description>11.750 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum RAS# to CAS# Delay (tRCDmin)</Entry>\n\t\t\t<Description>11.750 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Row Precharge Time (tRPmin)</Entry>\n\t\t\t<Description>11.750 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Active to Precharge Time (tRASmin)</Entry>\n\t\t\t<Description>26.625 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Supported Module Timing at 1600.0 MHz</Entry>\n\t\t\t<Description>19-19-19-43</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Supported Module Timing at 1466.7 MHz</Entry>\n\t\t\t<Description>18-18-18-40</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Supported Module Timing at 1333.3 MHz</Entry>\n\t\t\t<Description>16-16-16-36</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Supported Module Timing at 1200.0 MHz</Entry>\n\t\t\t<Description>15-15-15-32</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Supported Module Timing at 1066.7 MHz</Entry>\n\t\t\t<Description>13-13-13-29</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Supported Module Timing at 933.3 MHz</Entry>\n\t\t\t<Description>11-11-11-25</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Active to Active/Refresh Time (tRCmin)</Entry>\n\t\t\t<Description>38.500 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Refresh Recovery Time Delay (tRFC1min)</Entry>\n\t\t\t<Description>350.000 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Refresh Recovery Time Delay (tRFC2min)</Entry>\n\t\t\t<Description>260.000 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Refresh Recovery Time Delay (tRFC4min)</Entry>\n\t\t\t<Description>160.000 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Four Activate Window Delay Time (tFAWmin)</Entry>\n\t\t\t<Description>28.000 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Active to Active Delay Time - Different Bank Group (tRRD_Smin)</Entry>\n\t\t\t<Description>4.250 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Minimum Active to Active Delay Time - Same Bank Group (tRRD_Lmin)</Entry>\n\t\t\t<Description>6.150 ns</Description>\n\t\t</Property>\n</SubNode>\n</MEMORY>\n<BUS>\n\t<NodeName>Bus</NodeName>\n<SubNode>\n\t<NodeName>PCI Bus #0</NodeName>\n<SubNode>\n\t<NodeName>AMD Family 17h/19h - Root Complex</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - Root Complex</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - Root Complex</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>Host-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>0:0:0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1480&amp;SUBSYS_14801022&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI standard host CPU bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1480&amp;SUBSYS_14801022&amp;REV_00\\3&amp;11583659&amp;0&amp;00</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0000)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD Family 17h/19h - PCIe Dummy Host Bridge</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - PCIe Dummy Host Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - PCIe Dummy Host Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>Host-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>0:1:0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1482&amp;SUBSYS_00000000&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI standard host CPU bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1482&amp;SUBSYS_00000000&amp;REV_00\\3&amp;11583659&amp;0&amp;08</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0100)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD Family 17h/19h - PCIe GPP Bridge[7:0]</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - PCIe GPP Bridge[7:0]</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - PCIe GPP Bridge[7:0]</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>PCI-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>0:1:1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1483&amp;SUBSYS_00000000&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Version</Entry>\n\t\t\t<Description>3.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Width</Entry>\n\t\t\t<Description>x4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Width</Entry>\n\t\t\t<Description>x4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Speed</Entry>\n\t\t\t<Description>8.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Speed</Entry>\n\t\t\t<Description>8.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device/Port Type</Entry>\n\t\t\t<Description>Root Port of PCI Express Root Complex</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Implemented</Entry>\n\t\t\t<Description>Yes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hot-Plug</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hot-Plug Surprise</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Power Limit</Entry>\n\t\t\t<Description>75.000 W</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Emergency Power Reduction</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Support</Entry>\n\t\t\t<Description>L1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Status</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L0s Exit Latency</Entry>\n\t\t\t<Description>256 - 512 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L1 Exit Latency</Entry>\n\t\t\t<Description>32 - 64 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size Supported</Entry>\n\t\t\t<Description>512 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size</Entry>\n\t\t\t<Description>512 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR Support</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI Express Root Port</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.3672</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1483&amp;SUBSYS_12341022&amp;REV_00\\3&amp;11583659&amp;0&amp;09</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0101)</Description>\n\t\t</Property>\n<SubNode>\n\t<NodeName>PCI Express x4 Bus #1</NodeName>\n<SubNode>\n\t<NodeName>RealTek Semiconductor RTS5763DL PCIe 3.0 x4 NVMe 1.3 SSD Controller</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>RealTek Semiconductor RTS5763DL PCIe 3.0 x4 NVMe 1.3 SSD Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>RealTek Semiconductor RTS5763DL PCIe 3.0 x4 NVMe 1.3 SSD Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>NVMe Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>1:0:0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_10EC&amp;DEV_5765&amp;SUBSYS_576510EC&amp;REV_01</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Version</Entry>\n\t\t\t<Description>3.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Width</Entry>\n\t\t\t<Description>x4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Width</Entry>\n\t\t\t<Description>x4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Speed</Entry>\n\t\t\t<Description>8.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Speed</Entry>\n\t\t\t<Description>8.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device/Port Type</Entry>\n\t\t\t<Description>PCI Express Endpoint</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Implemented</Entry>\n\t\t\t<Description>No</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Emergency Power Reduction</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Support</Entry>\n\t\t\t<Description>L1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Status</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L0s Exit Latency</Entry>\n\t\t\t<Description>&gt;4 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L1 Exit Latency</Entry>\n\t\t\t<Description>32 - 64 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size Supported</Entry>\n\t\t\t<Description>512 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size</Entry>\n\t\t\t<Description>512 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR Support</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>INTA#</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Base Address 0</Entry>\n\t\t\t<Description>FC700000</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Base Address 5</Entry>\n\t\t\t<Description>FC704000</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>Standard NVM Express Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.4111</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_10EC&amp;DEV_5765&amp;SUBSYS_576510EC&amp;REV_01\\00000001004CE00000</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0101)#PCI(0000)</Description>\n\t\t</Property>\n</SubNode>\n</SubNode>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD Family 17h/19h - PCIe GPP Bridge[7:0]</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - PCIe GPP Bridge[7:0]</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - PCIe GPP Bridge[7:0]</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>PCI-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>0:1:3</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1483&amp;SUBSYS_00000000&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Version</Entry>\n\t\t\t<Description>3.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Width</Entry>\n\t\t\t<Description>x8</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Width</Entry>\n\t\t\t<Description>x4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Speed</Entry>\n\t\t\t<Description>8.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Speed</Entry>\n\t\t\t<Description>8.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device/Port Type</Entry>\n\t\t\t<Description>Root Port of PCI Express Root Complex</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Implemented</Entry>\n\t\t\t<Description>Yes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hot-Plug</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hot-Plug Surprise</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Power Limit</Entry>\n\t\t\t<Description>75.000 W</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Emergency Power Reduction</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Support</Entry>\n\t\t\t<Description>L1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Status</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L0s Exit Latency</Entry>\n\t\t\t<Description>256 - 512 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L1 Exit Latency</Entry>\n\t\t\t<Description>32 - 64 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size Supported</Entry>\n\t\t\t<Description>512 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size</Entry>\n\t\t\t<Description>128 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR Support</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI Express Root Port</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.3672</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1483&amp;SUBSYS_12341022&amp;REV_00\\3&amp;11583659&amp;0&amp;0B</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0103)</Description>\n\t\t</Property>\n<SubNode>\n\t<NodeName>PCI Express x8 Bus #2</NodeName>\n<SubNode>\n\t<NodeName>AMD 400-Series Chipset - USB Controller F</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD 400-Series Chipset - USB Controller F</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD 400-Series Chipset - USB Controller F</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>USB xHCI Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>2:0:0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_43D5&amp;SUBSYS_11421B21&amp;REV_01</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Version</Entry>\n\t\t\t<Description>3.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Width</Entry>\n\t\t\t<Description>x4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Width</Entry>\n\t\t\t<Description>x4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Speed</Entry>\n\t\t\t<Description>8.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Speed</Entry>\n\t\t\t<Description>8.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device/Port Type</Entry>\n\t\t\t<Description>Legacy PCI Express Endpoint</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Implemented</Entry>\n\t\t\t<Description>No</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Emergency Power Reduction</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Support</Entry>\n\t\t\t<Description>L0s and L1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Status</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L0s Exit Latency</Entry>\n\t\t\t<Description>1 - 2 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L1 Exit Latency</Entry>\n\t\t\t<Description>16 - 32 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size Supported</Entry>\n\t\t\t<Description>512 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size</Entry>\n\t\t\t<Description>128 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR Support</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>INTA#</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Base Address 0</Entry>\n\t\t\t<Description>FC6A0000</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>USB Version Supported</Entry>\n\t\t\t<Description>3.1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>USB xHCI Compliant Host Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.3672</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-May-2024</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_43D5&amp;SUBSYS_11421B21&amp;REV_01\\4&amp;2C18E2E3&amp;0&amp;000B</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0103)#PCI(0000)</Description>\n\t\t</Property>\n<SubNode>\n\t<NodeName>USB Root Hub</NodeName>\n<SubNode>\n\t<NodeName>[Port1] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port2] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port3] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port4] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port5] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port6] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port7] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port8] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port9] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port10] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port11] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port12] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port13] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port14] : No Device Connected</NodeName>\n</SubNode>\n</SubNode>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD 400-Series Chipset - SATA Controller</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD 400-Series Chipset - SATA Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD 400-Series Chipset - SATA Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>SATA AHCI Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>2:0:1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_43C8&amp;SUBSYS_10621B21&amp;REV_01</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Version</Entry>\n\t\t\t<Description>3.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Width</Entry>\n\t\t\t<Description>x4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Width</Entry>\n\t\t\t<Description>x4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Speed</Entry>\n\t\t\t<Description>8.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Speed</Entry>\n\t\t\t<Description>8.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device/Port Type</Entry>\n\t\t\t<Description>Legacy PCI Express Endpoint</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Implemented</Entry>\n\t\t\t<Description>No</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Emergency Power Reduction</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Support</Entry>\n\t\t\t<Description>L0s and L1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Status</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L0s Exit Latency</Entry>\n\t\t\t<Description>1 - 2 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L1 Exit Latency</Entry>\n\t\t\t<Description>16 - 32 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size Supported</Entry>\n\t\t\t<Description>512 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size</Entry>\n\t\t\t<Description>128 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR Support</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>INTB#</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Base Address 5</Entry>\n\t\t\t<Description>FC680000</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interface Speed Supported</Entry>\n\t\t\t<Description>Gen3 6.0 Gbps</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Number Of Ports</Entry>\n\t\t\t<Description>8</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>External SATA Support</Entry>\n\t\t\t<Description>Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Aggressive Link Power Management</Entry>\n\t\t\t<Description>Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Staggered Spin-up</Entry>\n\t\t\t<Description>Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Mechanical Presence Switch</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Command Queue Acceleration</Entry>\n\t\t\t<Description>Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>64-bit Addressing</Entry>\n\t\t\t<Description>Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AHCI Status</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>AHCI Version</Entry>\n\t\t\t<Description>1.31</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Ports Implemented</Entry>\n\t\t\t<Description>0, 1, 4, 5</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Port Status</Entry>\n\t\t\t<Description>No Device Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>External SATA Port</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hot Plug</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Port Status</Entry>\n\t\t\t<Description>No Device Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>External SATA Port</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hot Plug</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Port Status</Entry>\n\t\t\t<Description>Device Present, Phy communication established</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Interface Speed</Entry>\n\t\t\t<Description>Gen3 6.0 Gbps</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>External SATA Port</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hot Plug</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Type</Entry>\n\t\t\t<Description>SATA</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Port Status</Entry>\n\t\t\t<Description>No Device Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>External SATA Port</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hot Plug</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>Standard SATA AHCI Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.2506</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_43C8&amp;SUBSYS_10621B21&amp;REV_01\\4&amp;2C18E2E3&amp;0&amp;010B</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0103)#PCI(0001)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD 400-Series Chipset - PCIe Function</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD 400-Series Chipset - PCIe Function</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD 400-Series Chipset - PCIe Function</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>PCI-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>2:0:2</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_43C6&amp;SUBSYS_00000000&amp;REV_01</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Version</Entry>\n\t\t\t<Description>3.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Width</Entry>\n\t\t\t<Description>x4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Width</Entry>\n\t\t\t<Description>x4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Speed</Entry>\n\t\t\t<Description>8.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Speed</Entry>\n\t\t\t<Description>8.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device/Port Type</Entry>\n\t\t\t<Description>Upstream Port of PCI Express Switch</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Implemented</Entry>\n\t\t\t<Description>No</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Emergency Power Reduction</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Support</Entry>\n\t\t\t<Description>L0s and L1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Status</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L0s Exit Latency</Entry>\n\t\t\t<Description>1 - 2 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L1 Exit Latency</Entry>\n\t\t\t<Description>16 - 32 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size Supported</Entry>\n\t\t\t<Description>512 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size</Entry>\n\t\t\t<Description>128 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR Support</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>IRQ5</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>INTC#</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI Express Upstream Switch Port</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.3672</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_43C6&amp;SUBSYS_02011B21&amp;REV_01\\4&amp;2C18E2E3&amp;0&amp;020B</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0103)#PCI(0002)</Description>\n\t\t</Property>\n<SubNode>\n\t<NodeName>PCI Express x4 Bus #3</NodeName>\n<SubNode>\n\t<NodeName>AMD 400-Series Chipset - PCIe Downstream Function</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD 400-Series Chipset - PCIe Downstream Function</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD 400-Series Chipset - PCIe Downstream Function</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>PCI-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>3:0:0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_43C7&amp;SUBSYS_00000000&amp;REV_01</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Version</Entry>\n\t\t\t<Description>2.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Width</Entry>\n\t\t\t<Description>x1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Width</Entry>\n\t\t\t<Description>Not negotiated</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Speed</Entry>\n\t\t\t<Description>5.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Speed</Entry>\n\t\t\t<Description>2.5 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device/Port Type</Entry>\n\t\t\t<Description>Downstream Port of PCI Express Switch</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Implemented</Entry>\n\t\t\t<Description>Yes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hot-Plug</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hot-Plug Surprise</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Power Limit</Entry>\n\t\t\t<Description>26.000 W</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Emergency Power Reduction</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Support</Entry>\n\t\t\t<Description>L0s and L1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Status</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L0s Exit Latency</Entry>\n\t\t\t<Description>&gt;4 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L1 Exit Latency</Entry>\n\t\t\t<Description>32 - 64 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size Supported</Entry>\n\t\t\t<Description>512 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size</Entry>\n\t\t\t<Description>128 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR Support</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>IRQ11</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>INTA#</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI Express Downstream Switch Port</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.3672</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_43C7&amp;SUBSYS_33061B21&amp;REV_01\\5&amp;20AB19AA&amp;0&amp;00020B</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0103)#PCI(0002)#PCI(0000)</Description>\n\t\t</Property>\n<SubNode>\n\t<NodeName>PCI Express x1 Bus #4</NodeName>\n</SubNode>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD 400-Series Chipset - PCIe Downstream Function</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD 400-Series Chipset - PCIe Downstream Function</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD 400-Series Chipset - PCIe Downstream Function</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>PCI-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>3:1:0</Description>\n\t\t</Propeòty>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_43C7&amp;SUBSYS_00000000&amp;REV_01</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Version</Entry>\n\t\t\t<Description>2.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Width</Entry>\n\t\t\t<Description>x1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Width</Entry>\n\t\t\t<Description>x1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Speed</Entry>\n\t\t\t<Description>5.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Speed</Entry>\n\t\t\t<Description>2.5 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device/Port Type</Entry>\n\t\t\t<Description>Downstream Port of PCI Express Switch</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Implemented</Entry>\n\t\t\t<Description>Yes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hot-Plug</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hot-Plug Surprise</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Power Limit</Entry>\n\t\t\t<Description>26.000 W</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Emergency Power Reduction</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Support</Entry>\n\t\t\t<Description>L0s and L1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Status</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L0s Exit Latency</Entry>\n\t\t\t<Description>&gt;4 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L1 Exit Latency</Entry>\n\t\t\t<Description>32 - 64 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size Supported</Entry>\n\t\t\t<Description>512 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size</Entry>\n\t\t\t<Description>128 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR Support</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>IRQ10</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>INTA#</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI Express Downstream Switch Port</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.3672</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_43C7&amp;SUBSYS_33061B21&amp;REV_01\\5&amp;20AB19AA&amp;0&amp;08020B</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0103)#PCI(0002)#PCI(0100)</Description>\n\t\t</Property>\n<SubNode>\n\t<NodeName>PCI Express x1 Bus #5</NodeName>\n</SubNode>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD 400-Series Chipset - PCIe Downstream Function</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD 400-Series Chipset - PCIe Downstream Function</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD 400-Series Chipset - PCIe Downstream Function</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>PCI-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>3:4:0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_43C7&amp;SUBSYS_00000000&amp;REV_01</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Version</Entry>\n\t\t\t<Description>2.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Width</Entry>\n\t\t\t<Description>x2</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Width</Entry>\n\t\t\t<Description>Not negotiated</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Speed</Entry>\n\t\t\t<Description>5.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Speed</Entry>\n\t\t\t<Description>2.5 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device/Port Type</Entry>\n\t\t\t<Description>Downstream Port of PCI Express Switch</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Implemented</Entry>\n\t\t\t<Description>Yes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hot-Plug</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hot-Plug Surprise</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Power Limit</Entry>\n\t\t\t<Description>26.000 W</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Emergency Power Reduction</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Support</Entry>\n\t\t\t<Description>L0s and L1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Status</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L0s Exit Latency</Entry>\n\t\t\t<Description>&gt;4 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L1 Exit Latency</Entry>\n\t\t\t<Description>32 - 64 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size Supported</Entry>\n\t\t\t<Description>512 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size</Entry>\n\t\t\t<Description>128 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR Support</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>IRQ11</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>INTA#</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI Express Downstream Switch Port</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.3672</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_43C7&amp;SUBSYS_33061B21&amp;REV_01\\5&amp;20AB19AA&amp;0&amp;20020B</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0103)#PCI(0002)#PCI(0400)</Description>\n\t\t</Property>\n<SubNode>\n\t<NodeName>PCI Express x2 Bus #6</NodeName>\n</SubNode>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD 400-Series Chipset - PCIe Downstream Function</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD 400-Series Chipset - PCIe Downstream Function</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD 400-Series Chipset - PCIe Downstream Function</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>PCI-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>3:6:0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_43C7&amp;SUBSYS_00000000&amp;REV_01</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Version</Entry>\n\t\t\t<Description>2.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Width</Entry>\n\t\t\t<Description>x1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Width</Entry>\n\t\t\t<Description>x1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Speed</Entry>\n\t\t\t<Description>5.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Speed</Entry>\n\t\t\t<Description>2.5 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device/Port Type</Entry>\n\t\t\t<Description>Downstream Port of PCI Express Switch</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Implemented</Entry>\n\t\t\t<Description>Yes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hot-Plug</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hot-Plug Surprise</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Power Limit</Entry>\n\t\t\t<Description>26.000 W</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Emergency Power Reduction</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Support</Entry>\n\t\t\t<Description>L0s and L1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Status</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L0s Exit Latency</Entry>\n\t\t\t<Description>&gt;4 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L1 Exit Latency</Entry>\n\t\t\t<Description>32 - 64 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size Supported</Entry>\n\t\t\t<Description>512 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size</Entry>\n\t\t\t<Description>128 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR Support</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>IRQ5</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>INTA#</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI Express Downstream Switch Port</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.3672</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_43C7&amp;SUBSYS_33061B21&amp;REV_01\\5&amp;20AB19AA&amp;0&amp;30020B</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0103)#PCI(0002)#PCI(0600)</Description>\n\t\t</Property>\n<SubNode>\n\t<NodeName>PCI Express x1 Bus #7</NodeName>\n</SubNode>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD 400-Series Chipset - PCIe Downstream Function</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD 400-Series Chipset - PCIe Downstream Function</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD 400-Series Chipset - PCIe Downstream Function</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>PCI-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>3:7:0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_43C7&amp;SUBSYS_00000000&amp;REV_01</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Version</Entry>\n\t\t\t<Description>2.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Width</Entry>\n\t\t\t<Description>x1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Width</Entry>\n\t\t\t<Description>x1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Speed</Entry>\n\t\t\t<Description>5.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Speed</Entry>\n\t\t\t<Description>2.5 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device/Port Type</Entry>\n\t\t\t<Description>Downstream Port of PCI Express Switch</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Implemented</Entry>\n\t\t\t<Description>Yes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hot-Plug</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hot-Plug Surprise</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Power Limit</Entry>\n\t\t\t<Description>26.000 W</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Emergency Power Reduction</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Support</Entry>\n\t\t\t<Description>L0s and L1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Status</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L0s Exit Latency</Entry>\n\t\t\t<Description>&gt;4 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L1 Exit Latency</Entry>\n\t\t\t<Description>32 - 64 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size Supported</Entry>\n\t\t\t<Description>512 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size</Entry>\n\t\t\t<Description>128 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR Support</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>IRQ11</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>INTA#</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI Express Downstream Switch Port</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.3672</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_43C7&amp;SUBSYS_33061B21&amp;REV_01\\5&amp;20AB19AA&amp;0&amp;38020B</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0103)#PCI(0002)#PCI(0700)</Description>\n\t\t</Property>\n<SubNode>\n\t<NodeName>PCI Express x1 Bus #8</NodeName>\n<SubNode>\n\t<NodeName>RealTek Semiconductor RTL8168/8111 PCI-E Gigabit Ethernet NIC</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>RealTek Semiconductor RTL8168/8111 PCI-E Gigabit Ethernet NIC</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>RealTek Semiconductor RTL8168/8111 PCI-E Gigabit Ethernet NIC</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>Ethernet Adapter</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>15</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>8:0:0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_10EC&amp;DEV_8168&amp;SUBSYS_81681849&amp;REV_15</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Version</Entry>\n\t\t\t<Description>1.1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Width</Entry>\n\t\t\t<Description>x1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Width</Entry>\n\t\t\t<Description>x1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Speed</Entry>\n\t\t\t<Description>2.5 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Speed</Entry>\n\t\t\t<Description>2.5 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device/Port Type</Entry>\n\t\t\t<Description>PCI Express Endpoint</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Implemented</Entry>\n\t\t\t<Description>No</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Emergency Power Reduction</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Support</Entry>\n\t\t\t<Description>L0s and L1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Status</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L0s Exit Latency</Entry>\n\t\t\t<Description>&gt;4 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L1 Exit Latency</Entry>\n\t\t\t<Description>32 - 64 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size Supported</Entry>\n\t\t\t<Description>128 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size</Entry>\n\t\t\t<Description>128 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR Support</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>INTA#</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Base Address 0</Entry>\n\t\t\t<Description>F000</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Base Address 2</Entry>\n\t\t\t<Description>FC504000</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Base Address 4</Entry>\n\t\t\t<Description>FC500000</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Realtek</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>Realtek PCIe GbE Family Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Realtek</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.68.813.2023</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>15-Aug-2023</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_10EC&amp;DEV_8168&amp;SUBSYS_81681849&amp;REV_15\\2906F659A1A8000000</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0103)#PCI(0002)#PCI(0700)#PCI(0000)</Description>\n\t\t</Property>\n</SubNode>\n</SubNode>\n</SubNode>\n</SubNode>\n</SubNode>\n</SubNode>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD Family 17h/19h - PCIe Dummy Host Bridge</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - PCIe Dummy Host Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - PCIe Dummy Host Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>Host-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>0:2:0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1482&amp;SUBSYS_00000000&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI standard host CPU bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1482&amp;SUBSYS_00000000&amp;REV_00\\3&amp;11583659&amp;0&amp;10</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0200)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD Family 17h/19h - PCIe Dummy Host Bridge</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - PCIe Dummy Host Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - PCIe Dummy Host Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>Host-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>0:3:0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1482&amp;SUBSYS_00000000&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI standard host CPU bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1482&amp;SUBSYS_00000000&amp;REV_00\\3&amp;11583659&amp;0&amp;18</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0300)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD Family 17h/19h - PCIe GPP Bridge[7:0]</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - PCIe GPP Bridge[7:0]</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - PCIe GPP Bridge[7:0]</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>PCI-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>0:3:1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1483&amp;SUBSYS_00000000&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Version</Entry>\n\t\t\t<Description>3.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Width</Entry>\n\t\t\t<Description>x16</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Width</Entry>\n\t\t\t<Description>x8</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Speed</Entry>\n\t\t\t<Description>8.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Speed</Entry>\n\t\t\t<Description>8.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device/Port Type</Entry>\n\t\t\t<Description>Root Port of PCI Express Root Complex</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Implemented</Entry>\n\t\t\t<Description>Yes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hot-Plug</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hot-Plug Surprise</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Power Limit</Entry>\n\t\t\t<Description>75.000 W</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Emergency Power Reduction</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Support</Entry>\n\t\t\t<Description>L1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Status</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L0s Exit Latency</Entry>\n\t\t\t<Description>256 - 512 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L1 Exit Latency</Entry>\n\t\t\t<Description>32 - 64 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size Supported</Entry>\n\t\t\t<Description>512 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size</Entry>\n\t\t\t<Description>256 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR Support</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI Express Root Port</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.3672</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1483&amp;SUBSYS_12341022&amp;REV_00\\3&amp;11583659&amp;0&amp;19</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0301)</Description>\n\t\t</Property>\n<SubNode>\n\t<NodeName>PCI Express x16 Bus #9</NodeName>\n<SubNode>\n\t<NodeName>GALAX RTX 3060 Ti BOOMSTAR LHR</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>GALAX RTX 3060 Ti BOOMSTAR LHR</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>NVIDIA GeForce RTX 3060 Ti (GA104-202 LHR)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>VGA Compatible Adapter</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>A1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>9:0:0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_10DE&amp;DEV_2489&amp;SUBSYS_153C1B4C&amp;REV_A1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Version</Entry>\n\t\t\t<Description>4.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Width</Entry>\n\t\t\t<Description>x16</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Width</Entry>\n\t\t\t<Description>x8</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Speed</Entry>\n\t\t\t<Description>16.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Speed</Entry>\n\t\t\t<Description>8.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device/Port Type</Entry>\n\t\t\t<Description>Legacy PCI Express Endpoint</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Implemented</Entry>\n\t\t\t<Description>No</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Emergency Power Reduction</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Support</Entry>\n\t\t\t<Description>L0s and L1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Status</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L0s Exit Latency</Entry>\n\t\t\t<Description>512 ns - 1 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L1 Exit Latency</Entry>\n\t\t\t<Description>2 - 4 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size Supported</Entry>\n\t\t\t<Description>256 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size</Entry>\n\t\t\t<Description>256 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR Support</Entry>\n\t\t\t<Description>Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR0 Supported Size</Entry>\n\t\t\t<Description>16 MB</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR0 Current Size</Entry>\n\t\t\t<Description>16 MB</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR1 Supported Size</Entry>\n\t\t\t<Description>64 MB, 128 MB, 256 MB, 512 MB, 1 GB, 2 GB, 4 GB, 8 GB</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR1 Current Size</Entry>\n\t\t\t<Description>256 MB</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR2 Supported Size</Entry>\n\t\t\t<Description>32 MB</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR2 Current Size</Entry>\n\t\t\t<Description>32 MB</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>INTA#</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Base Address 0</Entry>\n\t\t\t<Description>FB000000</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Base Address 1</Entry>\n\t\t\t<Description>D0000000</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Base Address 3</Entry>\n\t\t\t<Description>E0000000</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>I/O Base Address 5</Entry>\n\t\t\t<Description>E000</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>NVIDIA</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>NVIDIA GeForce RTX 3060 Ti</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>NVIDIA</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>31.0.15.2225 (GeForce 522.25)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>06-Oct-2022</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DCH/UWD Driver</Entry>\n\t\t\t<Description>Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_10DE&amp;DEV_2489&amp;SUBSYS_153C1B4C&amp;REV_A1\\4&amp;1FC990D7&amp;0&amp;0019</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0301)#PCI(0000)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>NVIDIA GA104 - High Definition Audio Controller</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>NVIDIA GA104 - High Definition Audio Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>NVIDIA GA104 - High Definition Audio Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>High Definition Audio</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>A1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>9:0:1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_10DE&amp;DEV_228B&amp;SUBSYS_153C1B4C&amp;REV_A1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Version</Entry>\n\t\t\t<Description>4.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Width</Entry>\n\t\t\t<Description>x16</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Width</Entry>\n\t\t\t<Description>x8</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Speed</Entry>\n\t\t\t<Description>16.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Speed</Entry>\n\t\t\t<Description>8.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device/Port Type</Entry>\n\t\t\t<Description>PCI Express Endpoint</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Implemented</Entry>\n\t\t\t<Description>No</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Emergency Power Reduction</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Support</Entry>\n\t\t\t<Description>L0s and L1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Status</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L0s Exit Latency</Entry>\n\t\t\t<Description>512 ns - 1 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L1 Exit Latency</Entry>\n\t\t\t<Description>2 - 4 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size Supported</Entry>\n\t\t\t<Description>256 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size</Entry>\n\t\t\t<Description>256 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR Support</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>IRQ55</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>INTB#</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Base Address 0</Entry>\n\t\t\t<Description>FC080000</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>High Definition Audio Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.3810</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>18-Jun-2024</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_10DE&amp;DEV_228B&amp;SUBSYS_153C1B4C&amp;REV_A1\\4&amp;1FC990D7&amp;0&amp;0119</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0301)#PCI(0001)</Description>\n\t\t</Property>\n</SubNode>\n</SubNode>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD Family 17h/19h - PCIe Dummy Host Bridge</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - PCIe Dummy Host Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - PCIe Dummy Host Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>Host-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>0:4:0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1482&amp;SUBSYS_00000000&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI standard host CPU bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1482&amp;SUBSYS_00000000&amp;REV_00\\3&amp;11583659&amp;0&amp;20</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0400)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD Family 17h/19h - PCIe Dummy Host Bridge</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - PCIe Dummy Host Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - PCIe Dummy Host Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>Host-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>0:5:0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1482&amp;SUBSYS_00000000&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI standard host CPU bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1482&amp;SUBSYS_00000000&amp;REV_00\\3&amp;11583659&amp;0&amp;28</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0500)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD Family 17h/19h - PCIe Dummy Host Bridge</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - PCIe Dummy Host Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - PCIe Dummy Host Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>Host-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>0:7:0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1482&amp;SUBSYS_00000000&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI standard host CPU bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1482&amp;SUBSYS_00000000&amp;REV_00\\3&amp;11583659&amp;0&amp;38</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0700)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD Family 17h/19h - Internal PCIe GPP Bridge 0 to bus[E:B]</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - Internal PCIe GPP Bridge 0 to bus[E:B]</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - Internal PCIe GPP Bridge 0 to bus[E:B]</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>PCI-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>0:7:1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1484&amp;SUBSYS_00000000&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Version</Entry>\n\t\t\t<Description>4.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Width</Entry>\n\t\t\t<Description>x16</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Width</Entry>\n\t\t\t<Description>x16</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Speed</Entry>\n\t\t\t<Description>16.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Speed</Entry>\n\t\t\t<Description>16.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device/Port Type</Entry>\n\t\t\t<Description>Root Port of PCI Express Root Complex</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Implemented</Entry>\n\t\t\t<Description>No</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Emergency Power Reduction</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Support</Entry>\n\t\t\t<Description>L0s and L1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Status</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L0s Exit Latency</Entry>\n\t\t\t<Description>&lt; 64 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L1 Exit Latency</Entry>\n\t\t\t<Description>&lt; 1 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size Supported</Entry>\n\t\t\t<Description>512 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size</Entry>\n\t\t\t<Description>256 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR Support</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>INTA#</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI Express Root Port</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.3672</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1484&amp;SUBSYS_14841022&amp;REV_00\\3&amp;11583659&amp;0&amp;39</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0701)</Description>\n\t\t</Property>\n<SubNode>\n\t<NodeName>PCI Express x16 Bus #10</NodeName>\n<SubNode>\n\t<NodeName>AMD Family 17h/19h - PCIe Dummy Function</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - PCIe Dummy Function</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - PCIe Dummy Function</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>Non-Essential Instrumentation Function</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>10:0:0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_148A&amp;SUBSYS_148A1022&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Version</Entry>\n\t\t\t<Description>4.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Width</Entry>\n\t\t\t<Description>x16</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Width</Entry>\n\t\t\t<Description>x16</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Speed</Entry>\n\t\t\t<Description>16.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Speed</Entry>\n\t\t\t<Description>16.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device/Port Type</Entry>\n\t\t\t<Description>PCI Express Endpoint</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Implemented</Entry>\n\t\t\t<Description>No</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Emergency Power Reduction</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Support</Entry>\n\t\t\t<Description>L0s and L1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Status</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L0s Exit Latency</Entry>\n\t\t\t<Description>&lt; 64 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L1 Exit Latency</Entry>\n\t\t\t<Description>&lt; 1 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size Supported</Entry>\n\t\t\t<Description>256 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size</Entry>\n\t\t\t<Description>256 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR Support</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Advanced Micro Devices</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>AMD PCI</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Advanced Micro Devices</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>********</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>26-Mar-2024</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_148A&amp;SUBSYS_148A1022&amp;REV_00\\4&amp;D573D7&amp;0&amp;0039</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0701)#PCI(0000)</Description>\n\t\t</Property>\n</SubNode>\n</SubNode>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD Family 17h/19h - PCIe Dummy Host Bridge</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - PCIe Dummy Host Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - PCIe Dummy Host Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>Host-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>0:8:0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1482&amp;SUBSYS_00000000&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI standard host CPU bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1482&amp;SUBSYS_00000000&amp;REV_00\\3&amp;11583659&amp;0&amp;40</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0800)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD Family 17h/19h - Internal PCIe GPP Bridge 0 to bus[E:B]</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - Internal PCIe GPP Bridge 0 to bus[E:B]</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - Internal PCIe GPP Bridge 0 to bus[E:B]</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>PCI-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>0:8:1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1484&amp;SUBSYS_00000000&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Version</Entry>\n\t\t\t<Description>4.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Width</Entry>\n\t\t\t<Description>x16</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Width</Entry>\n\t\t\t<Description>x16</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Speed</Entry>\n\t\t\t<Description>16.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Speed</Entry>\n\t\t\t<Description>16.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device/Port Type</Entry>\n\t\t\t<Description>Root Port of PCI Express Root Complex</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Implemented</Entry>\n\t\t\t<Description>No</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Emergency Power Reduction</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Support</Entry>\n\t\t\t<Description>L0s and L1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Status</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L0s Exit Latency</Entry>\n\t\t\t<Description>&lt; 64 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L1 Exit Latency</Entry>\n\t\t\t<Description>&lt; 1 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size Supported</Entry>\n\t\t\t<Description>512 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size</Entry>\n\t\t\t<Description>256 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR Support</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>INTA#</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI Express Root Port</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.3672</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1484&amp;SUBSYS_14841022&amp;REV_00\\3&amp;11583659&amp;0&amp;41</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0801)</Description>\n\t\t</Property>\n<SubNode>\n\t<NodeName>PCI Express x16 Bus #11</NodeName>\n<SubNode>\n\t<NodeName>AMD Family 17h/19h - SSP</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - SSP</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - SSP</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>Non-Essential Instrumentation Function</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>11:0:0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1485&amp;SUBSYS_14851022&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Version</Entry>\n\t\t\t<Description>4.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Width</Entry>\n\t\t\t<Description>x16</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Width</Entry>\n\t\t\t<Description>x16</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Speed</Entry>\n\t\t\t<Description>16.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Speed</Entry>\n\t\t\t<Description>16.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device/Port Type</Entry>\n\t\t\t<Description>PCI Express Endpoint</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Implemented</Entry>\n\t\t\t<Description>No</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Emergency Power Reduction</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Support</Entry>\n\t\t\t<Description>L0s and L1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Status</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L0s Exit Latency</Entry>\n\t\t\t<Description>&lt; 64 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L1 Exit Latency</Entry>\n\t\t\t<Description>&lt; 1 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size Supported</Entry>\n\t\t\t<Description>256 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size</Entry>\n\t\t\t<Description>256 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR Support</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Advanced Micro Devices</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>AMD PCI</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Advanced Micro Devices</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>********</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>26-Mar-2024</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1485&amp;SUBSYS_14851022&amp;REV_00\\4&amp;1FDE7688&amp;0&amp;0041</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0801)#PCI(0000)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD Family 17h/19h - Cryptographic Coprocessor PSPCPP</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - Cryptographic Coprocessor PSPCPP</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - Cryptographic Coprocessor PSPCPP</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>Other Encryption/Decryption</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>11:0:1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1486&amp;SUBSYS_14861022&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Version</Entry>\n\t\t\t<Description>4.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Width</Entry>\n\t\t\t<Description>x16</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Width</Entry>\n\t\t\t<Description>x16</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Speed</Entry>\n\t\t\t<Description>16.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Speed</Entry>\n\t\t\t<Description>16.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device/Port Type</Entry>\n\t\t\t<Description>PCI Express Endpoint</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Implemented</Entry>\n\t\t\t<Description>No</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Emergency Power Reduction</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Support</Entry>\n\t\t\t<Description>L0s and L1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Status</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L0s Exit Latency</Entry>\n\t\t\t<Description>&lt; 64 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L1 Exit Latency</Entry>\n\t\t\t<Description>&lt; 1 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size Supported</Entry>\n\t\t\t<Description>256 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size</Entry>\n\t\t\t<Description>256 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR Support</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>INTA#</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Base Address 2</Entry>\n\t\t\t<Description>FC400000</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Base Address 5</Entry>\n\t\t\t<Description>FC3FE000</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Advanced Micro Devices Inc.</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>AMD PSP 11.0 Device</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Advanced Micro Devices Inc.</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>********</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>31-Jul-2023</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1486&amp;SUBSYS_14861022&amp;REV_00\\4&amp;1FDE7688&amp;0&amp;0141</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0801)#PCI(0001)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD Matisse/Vermeer - USB3 XHCI Controller</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Matisse/Vermeer - USB3 XHCI Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Matisse/Vermeer - USB3 XHCI Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>USB xHCI Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>11:0:3</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_149C&amp;SUBSYS_FFFF1849&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Version</Entry>\n\t\t\t<Description>4.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Width</Entry>\n\t\t\t<Description>x16</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Width</Entry>\n\t\t\t<Description>x16</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Speed</Entry>\n\t\t\t<Description>16.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Speed</Entry>\n\t\t\t<Description>16.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device/Port Type</Entry>\n\t\t\t<Description>PCI Express Endpoint</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Implemented</Entry>\n\t\t\t<Description>No</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Emergency Power Reduction</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Support</Entry>\n\t\t\t<Description>L0s and L1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Status</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L0s Exit Latency</Entry>\n\t\t\t<Description>&lt; 64 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L1 Exit Latency</Entry>\n\t\t\t<Description>&lt; 1 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size Supported</Entry>\n\t\t\t<Description>256 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size</Entry>\n\t\t\t<Description>256 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR Support</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>INTC#</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Base Address 0</Entry>\n\t\t\t<Description>FC200000</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>USB Version Supported</Entry>\n\t\t\t<Description>3.1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>USB xHCI Compliant Host Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.3672</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-May-2024</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_149C&amp;SUBSYS_FFFF1849&amp;REV_00\\4&amp;1FDE7688&amp;0&amp;0341</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0801)#PCI(0003)</Description>\n\t\t</Property>\n<SubNode>\n\t<NodeName>USB Root Hub</NodeName>\n<SubNode>\n\t<NodeName>[Port1] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port2] : HP, PID=0B92</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Manufacturer</Entry>\n\t\t\t<Description>HP, Inc</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Product Name</Entry>\n\t\t\t<Description>HyperX Virtual Surround Sound</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Serial Number</Entry>\n\t\t\t<Description>00000000</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>USB Version Supported</Entry>\n\t\t\t<Description>2.00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>USB Device Speed</Entry>\n\t\t\t<Description>USB 1.1 Full-speed</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>USB Composite Device</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>USB\\VID_03F0&amp;PID_0B92</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>USB Composite Device</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.3672</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>USB\\VID_03F0&amp;PID_0B92\\00000000</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0801)#PCI(0003)#USBROOT(0)#USB(2)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port3] : Logitech, PID=C547</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Manufacturer</Entry>\n\t\t\t<Description>Logitech</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Product Name</Entry>\n\t\t\t<Description>USB Receiver</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Serial Number</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>USB Version Supported</Entry>\n\t\t\t<Description>2.00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>USB Device Speed</Entry>\n\t\t\t<Description>USB 1.1 Full-speed</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>LIGHTSPEED Receiver</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>USB\\VID_046D&amp;PID_C547</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Logitech</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>LIGHTSPEED Receiver</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Logitech</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>1.1.62.4202</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>16-Sep-2024</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>USB\\VID_046D&amp;PID_C547\\6&amp;25A4B41B&amp;0&amp;3</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0801)#PCI(0003)#USBROOT(0)#USB(3)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port4] : Logitech, PID=C339</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Manufacturer</Entry>\n\t\t\t<Description>Logitech</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Product Name</Entry>\n\t\t\t<Description>PRO X Gaming Keyboard</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Serial Number</Entry>\n\t\t\t<Description>0D6539783634</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>USB Version Supported</Entry>\n\t\t\t<Description>2.00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>USB Device Speed</Entry>\n\t\t\t<Description>USB 1.1 Full-speed</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PRO</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>USB\\VID_046D&amp;PID_C339</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Logitech</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PRO</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Logitech</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>1.1.62.4202</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>16-Sep-2024</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>USB\\VID_046D&amp;PID_C339\\0D6539783634</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0801)#PCI(0003)#USBROOT(0)#USB(4)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port5] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port6] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port7] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port8] : No Device Connected</NodeName>\n</SubNode>\n</SubNode>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD Family 17h/19h - HD Audio Controller</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - HD Audio Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Family 17h/19h - HD Audio Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>High Definition Audio</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>11:0:4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1487&amp;SUBSYS_38971849&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Version</Entry>\n\t\t\t<Description>4.0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Width</Entry>\n\t\t\t<Description>x16</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Width</Entry>\n\t\t\t<Description>x16</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Speed</Entry>\n\t\t\t<Description>16.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Speed</Entry>\n\t\t\t<Description>16.0 GT/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device/Port Type</Entry>\n\t\t\t<Description>PCI Express Endpoint</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Slot Implemented</Entry>\n\t\t\t<Description>No</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Emergency Power Reduction</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Support</Entry>\n\t\t\t<Description>L0s and L1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active State Power Management (ASPM) Status</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L0s Exit Latency</Entry>\n\t\t\t<Description>&lt; 64 ns</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>L1 Exit Latency</Entry>\n\t\t\t<Description>&lt; 1 us</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size Supported</Entry>\n\t\t\t<Description>256 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Payload Size</Entry>\n\t\t\t<Description>256 bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR Support</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>IRQ36</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>INTD#</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Memory Base Address 0</Entry>\n\t\t\t<Description>FC3F0000</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>High Definition Audio Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.3810</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>18-Jun-2024</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1487&amp;SUBSYS_38971849&amp;REV_00\\4&amp;1FDE7688&amp;0&amp;0441</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0801)#PCI(0004)</Description>\n\t\t</Property>\n</SubNode>\n</SubNode>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD RV/RN/Bixby FCH - SMBus and ACPI Controller</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD RV/RN/Bixby FCH - SMBus and ACPI Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD RV/RN/Bixby FCH - SMBus and ACPI Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>SMBus (System Management Bus)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>61</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>0:20:0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_790B&amp;SUBSYS_FFFF1849&amp;REV_61</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Advanced Micro Devices, Inc</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>AMD SMBUS</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Advanced Micro Devices, Inc</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>********</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>25-Jun-2024</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_790B&amp;SUBSYS_FFFF1849&amp;REV_61\\3&amp;11583659&amp;0&amp;A0</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(1400)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD Promontory/Bixby FCH - LPC Bridge</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Promontory/Bixby FCH - LPC Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Promontory/Bixby FCH - LPC Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>PCI-to-ISA Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>51</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>0:20:3</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_790E&amp;SUBSYS_FFFF1849&amp;REV_51</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI standard ISA bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_790E&amp;SUBSYS_FFFF1849&amp;REV_51\\3&amp;11583659&amp;0&amp;A3</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(1403)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD Matisse/Vermeer - Data Fabric: Function 0</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Matisse/Vermeer - Data Fabric: Function 0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Matisse/Vermeer - Data Fabric: Function 0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>Host-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>0:24:0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1440&amp;SUBSYS_00000000&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI standard host CPU bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1440&amp;SUBSYS_00000000&amp;REV_00\\3&amp;11583659&amp;0&amp;C0</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(1800)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD Matisse/Vermeer - Data Fabric: Function 1</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Matisse/Vermeer - Data Fabric: Function 1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Matisse/Vermeer - Data Fabric: Function 1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>Host-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>0:24:1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1441&amp;SUBSYS_00000000&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI standard host CPU bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1441&amp;SUBSYS_00000000&amp;REV_00\\3&amp;11583659&amp;0&amp;C1</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(1801)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD Matisse/Vermeer - Data Fabric: Function 2</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Matisse/Vermeer - Data Fabric: Function 2</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Matisse/Vermeer - Data Fabric: Function 2</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>Host-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>0:24:2</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1442&amp;SUBSYS_00000000&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI standard host CPU bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1442&amp;SUBSYS_00000000&amp;REV_00\\3&amp;11583659&amp;0&amp;C2</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(1802)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD Matisse/Vermeer - Data Fabric: Function 3</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Matisse/Vermeer - Data Fabric: Function 3</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Matisse/Vermeer - Data Fabric: Function 3</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>Host-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>0:24:3</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1443&amp;SUBSYS_00000000&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI standard host CPU bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1443&amp;SUBSYS_00000000&amp;REV_00\\3&amp;11583659&amp;0&amp;C3</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(1803)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD Matisse/Vermeer - Data Fabric: Function 4</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Matisse/Vermeer - Data Fabric: Function 4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Matisse/Vermeer - Data Fabric: Function 4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>Host-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>0:24:4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1444&amp;SUBSYS_00000000&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI standard host CPU bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1444&amp;SUBSYS_00000000&amp;REV_00\\3&amp;11583659&amp;0&amp;C4</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(1804)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD Matisse/Vermeer - Data Fabric: Function 5</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Matisse/Vermeer - Data Fabric: Function 5</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Matisse/Vermeer - Data Fabric: Function 5</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>Host-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>0:24:5</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1445&amp;SUBSYS_00000000&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI standard host CPU bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1445&amp;SUBSYS_00000000&amp;REV_00\\3&amp;11583659&amp;0&amp;C5</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(1805)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD Matisse/Vermeer - Data Fabric: Function 6</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Matisse/Vermeer - Data Fabric: Function 6</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Matisse/Vermeer - Data Fabric: Function 6</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>Host-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>0:24:6</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1446&amp;SUBSYS_00000000&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI standard host CPU bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1446&amp;SUBSYS_00000000&amp;REV_00\\3&amp;11583659&amp;0&amp;C6</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(1806)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD Matisse/Vermeer - Data Fabric: Function 7</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Name</Entry>\n\t\t\t<Description>AMD Matisse/Vermeer - Data Fabric: Function 7</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Original Device Name</Entry>\n\t\t\t<Description>AMD Matisse/Vermeer - Data Fabric: Function 7</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Class</Entry>\n\t\t\t<Description>Host-to-PCI Bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Revision ID</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Address (Bus:Device:Function) Number</Entry>\n\t\t\t<Description>0:24:7</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Latency Timer</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1447&amp;SUBSYS_00000000&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Line</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Interrupt Pin</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Bus Mastering</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Running At 66 MHz</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fast Back-to-Back Transactions</Entry>\n\t\t\t<Description>Not Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PCI standard host CPU bridge</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1447&amp;SUBSYS_00000000&amp;REV_00\\3&amp;11583659&amp;0&amp;C7</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(1807)</Description>\n\t\t</Property>\n</SubNode>\n</SubNode>\n</BUS>\n<VIDEO>\n\t<NodeName>Video Adapter</NodeName>\n<SubNode>\n\t<NodeName>NVIDIA GeForce RTX 3060 Ti</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Video Chipset</Entry>\n\t\t\t<Description>NVIDIA GeForce RTX 3060 Ti</Description>\n\t\t\t<PropertyType>GFXCARD_CHIPSET</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Video Chipset Codename</Entry>\n\t\t\t<Description>GA104-202 (LHR)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Video Memory</Entry>\n\t\t\t<Description>8191 MBytes of GDDR6 SDRAM [Hynix]</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Video Card</Entry>\n\t\t\t<Description>GALAX RTX 3060 Ti BOOMSTAR LHR</Description>\n\t\t\t<PropertyType>GFXCARD</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Video Bus</Entry>\n\t\t\t<Description>PCIe v4.0 x16 (16.0 GT/s) @ x8 (8.0 GT/s)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>GPU Type</Entry>\n\t\t\t<Description>Discrete</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Video RAMDAC</Entry>\n\t\t\t<Description>Integrated RAMDAC</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Video BIOS Version</Entry>\n\t\t\t<Description>94.04.6b.00.81 [UEFI]</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Video Chipset Revision</Entry>\n\t\t\t<Description>A1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Graphics Processor Clock</Entry>\n\t\t\t<Description>1920.0 MHz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Video Unit Clock</Entry>\n\t\t\t<Description>1695.0 MHz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Graphics Memory Clock</Entry>\n\t\t\t<Description>1750.2 MHz (Effective 14002.0 MHz)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Graphics Memory Bus Width</Entry>\n\t\t\t<Description>256-bit</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Number Of ROPs</Entry>\n\t\t\t<Description>80</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Number Of Unified Shaders</Entry>\n\t\t\t<Description>4864</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Number Of Ray Tracing Cores</Entry>\n\t\t\t<Description>38</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Number Of Tensor Cores</Entry>\n\t\t\t<Description>152</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Number Of TMUs (Texture Mapping Units)</Entry>\n\t\t\t<Description>152</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>ASIC Manufacturer</Entry>\n\t\t\t<Description>Samsung</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>ASIC Serial Number</Entry>\n\t\t\t<Description>402955830724</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>NVIDIA SLI Status</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR (ReBAR) Support</Entry>\n\t\t\t<Description>Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Resizable BAR (ReBAR) State</Entry>\n\t\t\t<Description>Disabled (256 MB)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_10DE&amp;DEV_2489&amp;SUBSYS_153C1B4C&amp;REV_A1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>PCI Location (Bus:Dev:Fnc)</Entry>\n\t\t\t<Description>9:00:0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>NVIDIA</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>NVIDIA GeForce RTX 3060 Ti</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>NVIDIA</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>31.0.15.2225 (GeForce 522.25)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>06-Oct-2022</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DCH/UWD Driver</Entry>\n\t\t\t<Description>Capable</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_10DE&amp;DEV_2489&amp;SUBSYS_153C1B4C&amp;REV_A1\\4&amp;1FC990D7&amp;0&amp;0019</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0301)#PCI(0000)</Description>\n\t\t</Property>\n</SubNode>\n</VIDEO>\n<MONITOR>\n\t<NodeName>Monitor</NodeName>\n<SubNode>\n\t<NodeName>Acer [Unknown Model: ACR0704]</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Monitor Name</Entry>\n\t\t\t<Description>Acer [Unknown Model: ACR0704]</Description>\n\t\t\t<PropertyType>MONITOR</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Monitor Name (Manuf)</Entry>\n\t\t\t<Description>VG240Y P     </Description>\n\t\t\t<PropertyType>MONITOR</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Serial Number</Entry>\n\t\t\t<Description>2489404574</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Date Of Manufacture</Entry>\n\t\t\t<Description>Week: 46, Year: 2019</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Monitor Hardware ID</Entry>\n\t\t\t<Description>Monitor\\ACR0704</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Max. Vertical Size</Entry>\n\t\t\t<Description>30 cm</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Max. Horizontal Size</Entry>\n\t\t\t<Description>53 cm</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Horizontal Frequency</Entry>\n\t\t\t<Description>180 - 180 kHz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Vertical Frequency</Entry>\n\t\t\t<Description>48 - 144 Hz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Pixel Clock</Entry>\n\t\t\t<Description>340 MHz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Input Signal</Entry>\n\t\t\t<Description>Digital</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Color Bit Depth</Entry>\n\t\t\t<Description>10 Bits per Primary Color</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Digital Video Interface Standard Supported</Entry>\n\t\t\t<Description>DisplayPort</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Gamma Factor</Entry>\n\t\t\t<Description>2.20</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Standby</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Suspend</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Active Off</Entry>\n\t\t\t<Description>Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Standard Colour Space (sRGB) Default</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Preferred Timing Mode</Entry>\n\t\t\t<Description>Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Default GTF (Continuous Frequency)</Entry>\n\t\t\t<Description>Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DFP 1.x Compatible</Entry>\n\t\t\t<Description>Yes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>1152 x 864</Entry>\n\t\t\t<Description>75 Hz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>1280 x 960</Entry>\n\t\t\t<Description>60 Hz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>1280 x 1024</Entry>\n\t\t\t<Description>60 Hz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>1280 x 720</Entry>\n\t\t\t<Description>60 Hz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>1280 x 800</Entry>\n\t\t\t<Description>60 Hz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>1440 x 900</Entry>\n\t\t\t<Description>60 Hz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>1680 x 1050</Entry>\n\t\t\t<Description>60 Hz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>1920 x 1080</Entry>\n\t\t\t<Description>60 Hz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>1920 x 1080</Entry>\n\t\t\t<Description>527 x 296 mm, Pixel Clock 332.88 MHz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>1920 x 1080</Entry>\n\t\t\t<Description>527 x 296 mm, Pixel Clock 148.50 MHz</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>Generic PnP Monitor</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.3672</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>DISPLAY\\ACR0704\\5&amp;1B38C7B4&amp;0&amp;UID37125</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n</SubNode>\n</MONITOR>\n<DRIVES>\n\t<NodeName>Drives</NodeName>\n<SubNode>\n\t<NodeName>ATA Drives</NodeName>\n<SubNode>\n\t<NodeName>X12 SSD 256GB</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Drive Controller</Entry>\n\t\t\t<Description>Serial ATA 6Gb/s @ 6Gb/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Host Controller</Entry>\n\t\t\t<Description>AMD 400-Series Chipset - SATA Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Drive Model</Entry>\n\t\t\t<Description>X12 SSD 256GB</Description>\n\t\t\t<PropertyType>HDD</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Drive Firmware Revision</Entry>\n\t\t\t<Description>W0710A0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Drive Serial Number</Entry>\n\t\t\t<Description>DF6CPK5D0K9Q15Z5TY82</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Drive Capacity</Entry>\n\t\t\t<Description>244,198 MBytes (256 GB)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Drive Capacity [MB]</Entry>\n\t\t\t<Description>244198</Description>\n\t\t\t<PropertyType>HDD_SIZE</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Media Rotation Rate</Entry>\n\t\t\t<Description>SSD Drive (Non-rotating)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Nominal Form Factor</Entry>\n\t\t\t<Description>2.5&quot;</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>ATA Major Version Supported</Entry>\n\t\t\t<Description>ATA/ATAPI-5, ATA/ATAPI-6, ATA/ATAPI-7, ATA8-ACS, ACS-2</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>ATA Minor Version Supported</Entry>\n\t\t\t<Description>ACS-2 Revision 3</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>ATA Transport Version Supported</Entry>\n\t\t\t<Description>SATA 3.2</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Drive Letter(s)</Entry>\n\t\t\t<Description>E:</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Number of Cylinders</Entry>\n\t\t\t<Description>16383</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Number of Heads</Entry>\n\t\t\t<Description>16</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Sectors Per Track</Entry>\n\t\t\t<Description>63</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Number of Sectors</Entry>\n\t\t\t<Description>16514064</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Total 48-bit LBA Sectors</Entry>\n\t\t\t<Description>500118192</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Logical Sector Size</Entry>\n\t\t\t<Description>512 Bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Cache Buffer Size</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Sectors Per Interrupt</Entry>\n\t\t\t<Description>Total: 1, Active: 1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Max. PIO Transfer Mode</Entry>\n\t\t\t<Description>4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Multiword DMA Mode</Entry>\n\t\t\t<Description>Total: 2, Active: -</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Singleword DMA Mode</Entry>\n\t\t\t<Description>Total: -, Active: -</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Ultra-DMA Mode</Entry>\n\t\t\t<Description>Total: 6 (ATA-133), Active: 6 (ATA-133)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Max. Multiword DMA Transfer Rate</Entry>\n\t\t\t<Description>16.7 MBytes/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Max. PIO with IORDY Transfer Rate</Entry>\n\t\t\t<Description>16.7 MBytes/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Max. PIO w/o IORDY Transfer Rate</Entry>\n\t\t\t<Description>16.7 MBytes/s</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Native Command Queuing</Entry>\n\t\t\t<Description>Supported, Max. Depth: 32</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>TRIM Command</Entry>\n\t\t\t<Description>Supported (Indeterminate Read After TRIM)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Fixed Drive</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Removable Drive</Entry>\n\t\t\t<Description>Not Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Magnetic Storage</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>LBA Mode</Entry>\n\t\t\t<Description>Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DMA Mode</Entry>\n\t\t\t<Description>Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>IORDY</Entry>\n\t\t\t<Description>Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>IORDY Disableable</Entry>\n\t\t\t<Description>Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Write Cache</Entry>\n\t\t\t<Description>Present, Active</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>S.M.A.R.T. Feature</Entry>\n\t\t\t<Description>Present, Active</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Security Feature</Entry>\n\t\t\t<Description>Present, Inactive</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Removable Media Feature</Entry>\n\t\t\t<Description>Not Present, Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Power Management</Entry>\n\t\t\t<Description>Present, Active</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Advanced Power Management</Entry>\n\t\t\t<Description>Present, Active</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Packet Interface</Entry>\n\t\t\t<Description>Not Present, Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Look-Ahead Buffer</Entry>\n\t\t\t<Description>Present, Active</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Host Protected Area</Entry>\n\t\t\t<Description>Present, Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Power-Up In Standby</Entry>\n\t\t\t<Description>Not Supported, Inactive</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Automatic Acoustic Management</Entry>\n\t\t\t<Description>Not Supported, Inactive</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>48-bit LBA</Entry>\n\t\t\t<Description>Supported, Active</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Host-Initiated Link Power Management (HIPM)</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device-Initiated Link Power Management (DIPM)</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>In-Order Data Delivery</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware Feature Control</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Software Settings Preservation</Entry>\n\t\t\t<Description>Supported, Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>NCQ Autosense</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Link Power State Device Sleep</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hybrid Information Feature</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Rebuild Assist</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Power Disable</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Zoned Device ATA Command Set</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>All Write Cache Non-Volatile</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Extended Number of User Addressable Sectors</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>CFast Specification</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>NCQ Priority Information</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Host Automatic Partial to Slumber Transitions</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Automatic Partial to Slumber Transitions</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>NCQ Streaming</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>NCQ Queue Management Command</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DevSleep to Reduced Power State</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Out Of Band Management Interface</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Extended Power Conditions Feature</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Sense Data Reporting Feature</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Free-Fall Control Feature</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Write-Read-Verify Feature</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Security Feature</Entry>\n\t\t\t<Description>Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Security Status</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Security Locked</Entry>\n\t\t\t<Description>Disabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Security Frozen</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Enhanced Security Erase</Entry>\n\t\t\t<Description>Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Sanitize Feature</Entry>\n\t\t\t<Description>Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Sanitize Device - Crypto Scramble</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Sanitize Device - Overwrite</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Sanitize Device - Block Erase</Entry>\n\t\t\t<Description>Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Sanitize Device - Antifreeze Lock</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Encrypts All User Data</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Trusted Computing</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[01] Raw Read Error Rate</Entry>\n\t\t\t<Description>100/50, Worst: 100</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[05] Reallocated Sector Count</Entry>\n\t\t\t<Description>100/50, Worst: 100</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[09] Power-on Hours/Cycle Count</Entry>\n\t\t\t<Description>100/50, Worst: 100 (674 hours / 28.1 days)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[0C] Power Cycle Count</Entry>\n\t\t\t<Description>100/50, Worst: 100 (Data = 149, 0)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[A0] Uncorrectable Sector Count When Read/Write</Entry>\n\t\t\t<Description>100/50, Worst: 100</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[A1] Number of Valid Spare Blocks</Entry>\n\t\t\t<Description>100/50, Worst: 100 (Data = 100, 0)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[A3] Number of Initial Invalid Blocks</Entry>\n\t\t\t<Description>100/50, Worst: 100 (Data = 20, 0)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[A4] Total Erase Count</Entry>\n\t\t\t<Description>100/50, Worst: 100 (Data = 62752, 0)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[A5] Maximum Erase Count</Entry>\n\t\t\t<Description>100/50, Worst: 100 (Data = 91, 0)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[A6] Minimum Erase Count</Entry>\n\t\t\t<Description>100/50, Worst: 100 (Data = 6, 0)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[A7] Average Erase Count</Entry>\n\t\t\t<Description>100/50, Worst: 100 (Data = 22, 0)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[A8] Maximum Erase Count of Spec</Entry>\n\t\t\t<Description>100/50, Worst: 100 (Data = 5050, 0)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[A9] Remaining Life</Entry>\n\t\t\t<Description>100/50, Worst: 100 (Data = 100, 0)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[AF] Program Fail Count In Worst Die</Entry>\n\t\t\t<Description>100/50, Worst: 100</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[B0] Erase Fail Count In Worst Die</Entry>\n\t\t\t<Description>100/50, Worst: 100</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[B1] Total Wear Level Count</Entry>\n\t\t\t<Description>100/50, Worst: 100</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[B2] Runtime Invalid Block Count</Entry>\n\t\t\t<Description>100/50, Worst: 100</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[B5] Program Fail Count (Total)</Entry>\n\t\t\t<Description>100/50, Worst: 100</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[B6] Erase Fail Count (Total)</Entry>\n\t\t\t<Description>100/50, Worst: 100</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[C0] Power-Off Retract Count</Entry>\n\t\t\t<Description>100/50, Worst: 100 (Data = 30, 0)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[C2] Temperature</Entry>\n\t\t\t<Description>100/50, Worst: 100 (45.0 °C)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[C3] Hardware ECC Recovered</Entry>\n\t\t\t<Description>100/50, Worst: 100</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[C4] Reallocation Event Count</Entry>\n\t\t\t<Description>100/50, Worst: 100</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[C5] Current Pending Sector Count</Entry>\n\t\t\t<Description>100/50, Worst: 100</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[C6] Off-Line Uncorrectable Sector Count</Entry>\n\t\t\t<Description>100/50, Worst: 100</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[C7] SATA CRC Error Count</Entry>\n\t\t\t<Description>100/50, Worst: 100</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[E8] Available Reserved Space</Entry>\n\t\t\t<Description>100/50, Worst: 100 (Data = 100, 0)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[F1] Total Host Writes</Entry>\n\t\t\t<Description>100/50, Worst: 100 (Data = 47776, 0)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[F2] Total Host Reads</Entry>\n\t\t\t<Description>100/50, Worst: 100 (Data = 62823, 0)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>[F5] Flash Write Sector Count</Entry>\n\t\t\t<Description>100/50, Worst: 100 (Data = 37601, 0)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Drive Remaining Life</Entry>\n\t\t\t<Description>100%</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Lifetime Power-On Resets</Entry>\n\t\t\t<Description>149</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Power-on Hours</Entry>\n\t\t\t<Description>674</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Logical Sectors Written</Entry>\n\t\t\t<Description>3131048910</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Logical Sectors Read</Entry>\n\t\t\t<Description>4117204329</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Number of Write Commands</Entry>\n\t\t\t<Description>603</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Number of Read Commands</Entry>\n\t\t\t<Description>47781</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Used Endurance Indicator</Entry>\n\t\t\t<Description>0%</Description>\n\t\t</Property>\n</SubNode>\n</SubNode>\n<SubNode>\n\t<NodeName>NVMe Drives</NodeName>\n<SubNode>\n\t<NodeName>SSD 256GB</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Drive Controller</Entry>\n\t\t\t<Description>NVMe (PCIe x4 8.0 GT/s @ x4 8.0 GT/s)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Host Controller</Entry>\n\t\t\t<Description>RealTek Semiconductor RTS5763DL PCIe 3.0 x4 NVMe 1.3 SSD Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Drive Model</Entry>\n\t\t\t<Description>SSD 256GB</Description>\n\t\t\t<PropertyType>HDD</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Drive Serial Number</Entry>\n\t\t\t<Description>202210180239</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Drive Firmware Revision</Entry>\n\t\t\t<Description>VC2S038B</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>NVMe Version Supported</Entry>\n\t\t\t<Description>v1.4</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Drive Capacity</Entry>\n\t\t\t<Description>244,198 MBytes (256 GB)</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Drive Capacity [MB]</Entry>\n\t\t\t<Description>244198</Description>\n\t\t\t<PropertyType>HDD_SIZE</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>SCSI Address</Entry>\n\t\t\t<Description>1:0:0:0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Drive Letter(s)</Entry>\n\t\t\t<Description>C:, D:</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Volatile Write Cache</Entry>\n\t\t\t<Description>Present</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Compare Command</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Write Uncorrectable Command</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Dataset Management</Entry>\n\t\t\t<Description>Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Write Zeroes</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Save field set to a non-zero value</Entry>\n\t\t\t<Description>Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Reservations</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Timestamp</Entry>\n\t\t\t<Description>Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Autonomous Power State Transitions</Entry>\n\t\t\t<Description>Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Host Memory Buffer (HMB) Capability</Entry>\n\t\t\t<Description>Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Host Memory Buffer (HMB) Minimum Size</Entry>\n\t\t\t<Description>32 MBytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Host Memory Buffer (HMB) Preferred Size</Entry>\n\t\t\t<Description>64 MBytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Host Memory Buffer (HMB) Status</Entry>\n\t\t\t<Description>Enabled</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Host Memory Buffer (HMB) Enabled Size</Entry>\n\t\t\t<Description>64 MBytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>NVM Command Set</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Key Value (KV) Command Set</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Zoned Namespace (ZNS) Command Set</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Subsystem Local Memory Command Set</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Computational Programs Namespace Command Set</Entry>\n\t\t\t<Description>Not Supported</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Available Space Below Threshold</Entry>\n\t\t\t<Description>OK</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Temperature Exceeded Critical Threshold</Entry>\n\t\t\t<Description>OK</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Reliablity Degraded</Entry>\n\t\t\t<Description>OK</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Media In Read Only Mode</Entry>\n\t\t\t<Description>OK</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Volatile Memory Backup Device Failed</Entry>\n\t\t\t<Description>OK</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Drive Temperature</Entry>\n\t\t\t<Description>40 °C</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Warning Temperature Threshold</Entry>\n\t\t\t<Description>100 °C</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Critical Temperature Threshold</Entry>\n\t\t\t<Description>110 °C</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Time Above Warning Temperature Threshold</Entry>\n\t\t\t<Description>0 minutes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Time Above Critical Temperature Threshold</Entry>\n\t\t\t<Description>0 minutes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Spare Capacity Available</Entry>\n\t\t\t<Description>100%</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Device Health</Entry>\n\t\t\t<Description>93%</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Power Cycles</Entry>\n\t\t\t<Description>1369</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Power On Hours</Entry>\n\t\t\t<Description>5877 hours</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Unsafe Shutdowns</Entry>\n\t\t\t<Description>130</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Media Errors</Entry>\n\t\t\t<Description>0</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Total Host Reads</Entry>\n\t\t\t<Description>9.24 TBytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Total Host Writes</Entry>\n\t\t\t<Description>24.70 TBytes</Description>\n\t\t</Property>\n</SubNode>\n</SubNode>\n</DRIVES>\n<SOUND>\n\t<NodeName>Audio</NodeName>\n<SubNode>\n\t<NodeName>NVIDIA GA104 - High Definition Audio Controller</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Audio Adapter</Entry>\n\t\t\t<Description>NVIDIA GA104 - High Definition Audio Controller</Description>\n\t\t\t<PropertyType>AUDIO_CARD</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Audio Controller Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_10DE&amp;DEV_228B&amp;SUBSYS_153C1B4C&amp;REV_A1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>High Definition Audio Codec</Entry>\n\t\t\t<Description>nVidia HDMI/DP</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Audio Codec Hardware ID</Entry>\n\t\t\t<Description>HDAUDIO\\FUNC_01&amp;VEN_10DE&amp;DEV_009E&amp;SUBSYS_1B4C153C&amp;REV_1001</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>NVIDIA Corporation</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>NVIDIA High Definition Audio</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>NVIDIA Corporation</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>*********</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>18-Jul-2022</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>HDAUDIO\\FUNC_01&amp;VEN_10DE&amp;DEV_009E&amp;SUBSYS_1B4C153C&amp;REV_1001\\5&amp;386E4A01&amp;0&amp;0001</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD Family 17h/19h - HD Audio Controller</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Audio Adapter</Entry>\n\t\t\t<Description>AMD Family 17h/19h - HD Audio Controller</Description>\n\t\t\t<PropertyType>AUDIO_CARD</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Audio Controller Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_1022&amp;DEV_1487&amp;SUBSYS_38971849&amp;REV_00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>High Definition Audio Codec</Entry>\n\t\t\t<Description>RealTek ALC897</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Audio Codec Hardware ID</Entry>\n\t\t\t<Description>HDAUDIO\\FUNC_01&amp;VEN_10EC&amp;DEV_0897&amp;SUBSYS_18493897&amp;REV_1004</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Realtek Semiconductor Corp.</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>Realtek High Definition Audio</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Realtek Semiconductor Corp.</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>6.0.9239.1</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>16-Sep-2021</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>HDAUDIO\\FUNC_01&amp;VEN_10EC&amp;DEV_0897&amp;SUBSYS_18493897&amp;REV_1004\\5&amp;386ABF67&amp;0&amp;0001</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n</SubNode>\n</SOUND>\n<NETWORK>\n\t<NodeName>Network</NodeName>\n<SubNode>\n\t<NodeName>RealTek Semiconductor RTL8168/8111 PCI-E Gigabit Ethernet NIC</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Network Card</Entry>\n\t\t\t<Description>RealTek Semiconductor RTL8168/8111 PCI-E Gigabit Ethernet NIC</Description>\n\t\t\t<PropertyType>NETCARD</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Vendor Description</Entry>\n\t\t\t<Description>Realtek PCIe GbE Family Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>MAC Address</Entry>\n\t\t\t<Description>A8-A1-59-F6-06-29</Description>\n\t\t\t<PropertyType>NETCARD_MAC</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Maximum Link Speed</Entry>\n\t\t\t<Description>1 Gbps</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Current Link Speed</Entry>\n\t\t\t<Description>1 Gbps</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Transmit Buffer Size</Entry>\n\t\t\t<Description>193792 Bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Receive Buffer Size</Entry>\n\t\t\t<Description>775168 Bytes</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>PCI\\VEN_10EC&amp;DEV_8168&amp;SUBSYS_81681849&amp;REV_15</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Realtek</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>Realtek PCIe GbE Family Controller</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Realtek</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.68.813.2023</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>15-Aug-2023</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>PCI\\VEN_10EC&amp;DEV_8168&amp;SUBSYS_81681849&amp;REV_15\\2906F659A1A8000000</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0103)#PCI(0002)#PCI(0700)#PCI(0000)</Description>\n\t\t</Property>\n</SubNode>\n</NETWORK>\n<PORTS>\n\t<NodeName>Ports</NodeName>\n<SubNode>\n\t<NodeName>COM</NodeName>\n<SubNode>\n\t<NodeName>COM1</NodeName>\n</SubNode>\n</SubNode>\n<SubNode>\n\t<NodeName>USB</NodeName>\n<SubNode>\n\t<NodeName>AMD USB 3.10 可扩展主机控制器 - 1.10 (Microsoft)</NodeName>\n<SubNode>\n\t<NodeName>Root Hub</NodeName>\n<SubNode>\n\t<NodeName>[Port1] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port2] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port3] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port4] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port5] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port6] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port7] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port8] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port9] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port10] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port11] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port12] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port13] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port14] : No Device Connected</NodeName>\n</SubNode>\n</SubNode>\n</SubNode>\n<SubNode>\n\t<NodeName>AMD USB 3.10 可扩展主机控制器 - 1.10 (Microsoft)</NodeName>\n<SubNode>\n\t<NodeName>Root Hub</NodeName>\n<SubNode>\n\t<NodeName>[Port1] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port2] : HP, PID=0B92</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Manufacturer</Entry>\n\t\t\t<Description>HP, Inc</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Product Name</Entry>\n\t\t\t<Description>HyperX Virtual Surround Sound</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Serial Number</Entry>\n\t\t\t<Description>00000000</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>USB Version Supported</Entry>\n\t\t\t<Description>2.00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>USB Device Speed</Entry>\n\t\t\t<Description>USB 1.1 Full-speed</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>USB Composite Device</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>USB\\VID_03F0&amp;PID_0B92</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>USB Composite Device</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Microsoft</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>10.0.22621.3672</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>21-Jun-2006</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>USB\\VID_03F0&amp;PID_0B92\\00000000</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0801)#PCI(0003)#USBROOT(0)#USB(2)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port3] : Logitech, PID=C547</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Manufacturer</Entry>\n\t\t\t<Description>Logitech</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Product Name</Entry>\n\t\t\t<Description>USB Receiver</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Serial Number</Entry>\n\t\t\t<Description>N/A</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>USB Version Supported</Entry>\n\t\t\t<Description>2.00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>USB Device Speed</Entry>\n\t\t\t<Description>USB 1.1 Full-speed</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>LIGHTSPEED Receiver</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>USB\\VID_046D&amp;PID_C547</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Logitech</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>LIGHTSPEED Receiver</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Logitech</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>1.1.62.4202</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>16-Sep-2024</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>USB\\VID_046D&amp;PID_C547\\6&amp;25A4B41B&amp;0&amp;3</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0801)#PCI(0003)#USBROOT(0)#USB(3)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port4] : Logitech, PID=C339</NodeName>\n\t\t<Property>\n\t\t\t<Entry>Device Manufacturer</Entry>\n\t\t\t<Description>Logitech</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Product Name</Entry>\n\t\t\t<Description>PRO X Gaming Keyboard</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Serial Number</Entry>\n\t\t\t<Description>0D6539783634</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>USB Version Supported</Entry>\n\t\t\t<Description>2.00</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>USB Device Speed</Entry>\n\t\t\t<Description>USB 1.1 Full-speed</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PRO</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Hardware ID</Entry>\n\t\t\t<Description>USB\\VID_046D&amp;PID_C339</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Manufacturer</Entry>\n\t\t\t<Description>Logitech</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Description</Entry>\n\t\t\t<Description>PRO</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Provider</Entry>\n\t\t\t<Description>Logitech</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Version</Entry>\n\t\t\t<Description>1.1.62.4202</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Driver Date</Entry>\n\t\t\t<Description>16-Sep-2024</Description>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>DeviceInstanceId</Entry>\n\t\t\t<Description>USB\\VID_046D&amp;PID_C339\\0D6539783634</Description>\n\t\t\t<PropertyType>DeviceInstanceId</PropertyType>\n\t\t</Property>\n\t\t<Property>\n\t\t\t<Entry>Location Paths</Entry>\n\t\t\t<Description>PCIROOT(0)#PCI(0801)#PCI(0003)#USBROOT(0)#USB(4)</Description>\n\t\t</Property>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port5] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port6] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port7] : No Device Connected</NodeName>\n</SubNode>\n<SubNode>\n\t<NodeName>[Port8] : No Device Connected</NodeName>\n</SubNode>\n</SubNode>\n</SubNode>\n</SubNode>\n</PORTS>\n</SubNodes>\n</COMPUTER>\n</HWINFO>