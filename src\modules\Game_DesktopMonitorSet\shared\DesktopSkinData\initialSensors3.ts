
import { Sensor } from '../../../Game_DMComponent/sharedTypes';
import img_people from '../../../../assets/img/monitoring/img3/fg_img_people_01.gif';
import img_people2 from '../../../../assets/img/monitoring/img3/fg_img_people_02.gif';
import fgcp_img from '../../../../assets/img/monitoring/img3/fgcp_img_01.gif';
import fgcp_img2 from '../../../../assets/img/monitoring/img3/fgcp_img_02.gif';
import fgcp_img3 from '../../../../assets/img/monitoring/img3/fgcp_img_01.png';
import fgcp_img4 from '../../../../assets/img/monitoring/img3/fgcp_img_02.png';
import fgcp_img_bg from '../../../../assets/img/monitoring/img3/fgcp_img_bg_01.png';
import fgcp_img_bg2 from '../../../../assets/img/monitoring/img3/fgcp_img_bg_02.png';
import fgcp_img_bg3 from '../../../../assets/img/monitoring/img3/fgcp_img_bg_03.png';
import fgcp_img_bg4 from '../../../../assets/img/monitoring/img3/fgcp_img_bg_04.png';
import fgcp_img_bg5 from '../../../../assets/img/monitoring/img3/fgcp_img_bg_05.png';
import fgcp_img_bg6 from '../../../../assets/img/monitoring/img3/fgcp_img_bg_061.png';
import icon_fan from '../../../../assets/img/monitoring/img3/fgcp_icon_fan.png';
import lightning from '../../../../assets/img/monitoring/img3/fgcp_icon_lightning.png';
import temperature from '../../../../assets/img/monitoring/img3/fgcp_icon_temperature.png';
import net_upload from '../../../../assets/img/monitoring/img3/ic_uploading.png';
import net_download from '../../../../assets/img/monitoring/img3/ic_downloading.png';

export const initialSensors: Sensor[] = [
  {
    id: 1,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: net_upload,
    transformShow: false,
    style: {
      zIndex: 58,
      fontSize: 24,
      top:38,
      left: 20,
      width: 15,
      height: 12,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 2,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'network',
    parameters: 'upload',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'KB/s',
    unitshow: false,
    style: {
      zIndex: 57,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 24,
      color: 'rgba(255,255,255,1)',
      top: 34,
      left: 46,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 3,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'KB/s',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 56,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 16,
      color: 'rgba(255,255,255,1)',
      top: 38,
      left: 80,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 4,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: net_download,
    transformShow: false,
    style: {
      zIndex: 55,
      fontSize: 24,
      top: 38,
      left: 195,
      width: 15,
      height: 12,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 5,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'network',
    parameters: 'download',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'KB/s',
    unitshow: false,
    style: {
      zIndex: 54,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 24,
      color: 'rgba(255,255,255,1)',
      top: 32,
      left: 220,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 6,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'KB/s',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 53,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 16,
      color: 'rgba(255,255,255,1)',
      top: 36,
      left: 260,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 7,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'DRAM',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 52,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 19,
      color: 'rgba(42, 143, 88,1)',
      top: 65,
      left: 20,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 8,
    nums: 8,
    type: '进度条',
    type2: 'progress',
    remark: '/',
    sensor: 'DRAM Usage',
    parameters: 'd_usage',
    belong: '',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    // class: 'ProgressShape1',
    unit: '%',
    Switchcolorsshow: true,
    style: {
      zIndex: 51,
      width: 180,
      height: 5,
      borderRadius: 0,
      backgroundColor:'rgba(67,107,148,1)',
      ProgressColor:'rgba(255, 255, 255, 1)',
      ProgressColor2:'#FF0000',
      Interval:80,
      Judgmentitem:0,
      top: 75,
      left: 82,
    },
  },
  {
    id: 9,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'DRAM Usage',
    parameters: 'd_usage',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: '%',
    unitshow: true,
    style: {
      zIndex: 50,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 19,
      color: 'rgba(42, 143, 88,1)',
      top: 65,
      left: 275,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 10,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'VRAM',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 49,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 19,
      color: 'rgba(42, 143, 88,1)',
      top: 93,
      left: 20,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 11,
    nums: 8,
    type: '进度条',
    type2: 'progress',
    remark: '/',
    sensor: 'VRAM Usage',
    parameters: 'mem_usage',
    belong: '',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    // class: 'ProgressShape1',
    unit: '%',
    Switchcolorsshow: true,
    style: {
      zIndex: 48,
      width: 180,
      height: 5,
      borderRadius: 0,
      backgroundColor:'rgba(67,107,148,1)',
      ProgressColor:'rgba(255, 255, 255, 1)',
      ProgressColor2:'#FF0000',
      Interval:80,
      Judgmentitem:0,
      top: 102,
      left: 82,
    },
  },
  {
    id: 12,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'VRAM Usage',
    parameters: 'mem_usage',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: '%',
    unitshow: true,
    style: {
      zIndex: 47,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 19,
      color: 'rgba(42, 143, 88,1)',
      top: 92,
      left: 275,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },

  {
    id: 13,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: 'CPU Temp',
    parameters: 'temp',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: img_people,
    transformShow: false,
    class: 'imgShape1',
    style: {
      zIndex: 46,
      fontSize: 24,
      top: 123,
      left: 20,
      width: 123,
      height: 204,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id:14,
    nums: 6,
    type: '时间',
    type2: 'time',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    timeFormat: 1,
    timeRule: 1,
    timeType: 1,
    style: {
      zIndex: 45,
      fontSize: 35,
      fontFamily: 'AeeAndCui',
      top: 215,
      left: 150,
      color: 'rgba(247, 238, 151,1)',
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id:15,
    nums: 6,
    type: '时间',
    type2: 'time',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    timeFormat: 2,
    timeRule: 1,
    timeType: 1,
    style: {
      zIndex: 44,
      fontSize: 18,
      fontFamily: 'AeeAndCui',
      top: 258,
      left: 140,
      color: 'rgba(247, 238, 151,1)',
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id:16,
    nums: 6,
    type: '时间',
    type2: 'time',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    timeFormat: 3,
    timeRule: 0,
    timeType: 2,
    style: {
      zIndex: 43,
      fontSize: 18,
      fontFamily: 'AeeAndCui',
      top: 258,
      left: 225,
      color: 'rgba(247, 238, 151,1)',
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 17,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: temperature,
    transformShow: false,
    style: {
      zIndex: 42,
      fontSize: 24,
      top: 55,
      left: 360,
      width: 20,
      height: 25,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 18,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'CPU Temp',
    parameters: 'temp',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: '°C',
    unitshow: false,
    style: {
      zIndex: 41,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 30,
      color: 'rgba(0,0,0,1)',
      top: 56,
      left: 395,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 19,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: '°C',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 40,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 16,
      color: 'rgba(0,0,0,1)',
      top: 63,
      left:440,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 20,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: fgcp_img3,
    transformShow: false,
    style: {
      zIndex: 39,
      fontSize: 24,
      top: 36,
      left: 341,
      width: 135,
      height: 58,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },

  {
    id: 21,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: lightning,
    transformShow: false,
    style: {
      zIndex: 38,
      fontSize: 24,
      top: 56,
      left: 504,
      width: 16,
      height: 22,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 22,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'CPU Power',
    parameters: 'power',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'W',
    unitshow: false,
    style: {
      zIndex: 37,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 30,
      color: 'rgba(0,0,0,1)',
      top: 56,
      left: 536,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 23,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'W',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 36,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 16,
      color: 'rgba(0,0,0,1)',
      top: 65,
      left: 589,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 24,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: fgcp_img4,
    transformShow: false,
    style: {
      zIndex: 35,
      fontSize: 24,
      top: 40,
      left: 488,
      width: 135,
      height: 58,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 25,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'CPU',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 34,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 26,
      color: 'rgba(0,0,0,1)',
      top: 152,
      left: 373,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 26,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'CPU Usage',
    parameters: 'usage',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: '%',
    unitshow: false,
    style: {
      zIndex: 33,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 52,
      color: 'rgba(23, 114, 51, 1)',
      top: 136,
      left: 470,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 27,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: '%',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 32,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 24,
      color: 'rgba(23, 114, 51, 1)',
      top: 156,
      left: 515,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 28,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: 'CPU Fan',
    parameters: 'fan',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: icon_fan,
    transformShow: true,
    style: {
      zIndex: 31,
      fontSize: 12,
      top: 208,
      left: 388,
      width: 24,
      height: 24,
      Interval: 2000,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 29,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'CPU Fan',
    parameters: 'fan',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'RPM',
    unitshow: false,
    style: {
      zIndex: 30,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 28,
      color: 'rgba(0,0,0,1)',
      top: 207,
      left: 488,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 30,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'RPM',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 29,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 18,
      color: 'rgba(0,0,0,1)',
      top: 213,
      left: 556,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },

  {
    id: 31,
    nums: 10,
    type: '折线图',
    type2: 'chart',
    remark: '/',
    sensor: 'CPU Clock',
    parameters: 'clock',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 28,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 18,
      // color: 'rgba(0,0,0,1)',
      gradientColors1:'rgba(77,158, 103,1)',
      gradientColors2: 'rgba(77, 158, 103,1)',
      areaColors1: 'rgba(186, 0, 255, 0)',
      areaColors2: 'rgba(186, 0, 255, 0)',
      top: 241,
      left: 380,
      width:100,
      height: 30,
    },
  },

  {
    id: 32,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'CPU Clock',
    parameters: 'clock',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'MHz',
    unitshow: false,
    style: {
      zIndex: 27,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 28,
      color: 'rgba(0,0,0,1)',
      top: 245,
      left: 488,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 33,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'MHz',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 26,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 18,
      color: 'rgba(0,0,0,1)',
      top: 252,
      left: 556,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 34,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: fgcp_img,
    transformShow: false,
    style: {
      zIndex: 25,
      top: 111,
      left: 348,
      width: 280,
      height: 220,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 35,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'GPU',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 24,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 26,
      color: 'rgba(0,0,0,1)',
      top: 104,
      left: 706,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 36,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'GPU Usage',
    parameters: 'total_usage',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: '%',
    unitshow: false,
    style: {
      zIndex: 23,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 52,
      color: 'rgba(23, 114, 51, 1)',
      top: 138,
      left: 699,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 37,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: '%',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 22,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 24,
      color: 'rgba(23, 114, 51, 1)',
      top: 154,
      left: 754,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 38,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: 'GPU Fan',
    parameters: 'fan',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: icon_fan,
    transformShow: true,
    style: {
      zIndex: 21,
      fontSize: 12,
      top: 198,
      left: 719,
      width: 24,
      height: 24,
      Interval: 2000,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 39,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'GPU Fan',
    parameters: 'fan',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'RPM',
    unitshow: false,
    style: {
      zIndex: 20,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 28,
      color: 'rgba(0,0,0,1)',
      top: 225,
      left: 688,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 40,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'RPM',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 19,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 18,
      color: 'rgba(0,0,0,1)',
      top: 231,
      left: 750,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },


  {
    id: 41,
    nums: 10,
    type: '折线图',
    type2: 'chart',
    remark: '/',
    sensor: 'GPU Clock',
    parameters: 'clock',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 18,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 18,
      // color: 'rgba(0,0,0,1)',
      gradientColors1:'rgba(77, 158, 103,1)',
      gradientColors2: 'rgba(77, 158, 103,1)',
      areaColors1: 'rgba(186, 0, 255, 0)',
      areaColors2: 'rgba(186, 0, 255, 0)',
      top: 254,
      left: 672,
      width:120,
      height: 30,
    },
  },
  {
    id: 42,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'GPU Clock',
    parameters: 'clock',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'MHz',
    unitshow: false,
    style: {
      zIndex: 17,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 28,
      color: 'rgba(0,0,0,1)',
      top: 284,
      left: 688,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 43,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'MHz',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 16,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 18,
      color: 'rgba(0,0,0,1)',
      top: 291,
      left: 750,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },

  {
    id: 44,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: fgcp_img2,
    transformShow: false,
    style: {
      zIndex: 15,
      top: 30,
      left: 645,
      width: 170,
      height: 300,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },

  {
    id: 45,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: temperature,
    transformShow: false,
    style: {
      zIndex: 14,
      fontSize: 24,
      top: 218,
      left: 835,
      width: 16,
      height: 22,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 46,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'GPU Temp',
    parameters: 'temp',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: '°C',
    unitshow: false,
    style: {
      zIndex: 13,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 26,
      color: 'rgba(0,0,0,1)',
      top: 218,
      left: 864,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 47,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: '°C',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 12,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 14,
      color: 'rgba(0,0,0,1)',
      top: 225,
      left: 904,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 48,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: fgcp_img3,
    transformShow: false,
    style: {
      zIndex: 11,
      fontSize: 24,
      top: 205,
      left: 820,
      width: 115,
      height: 45,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 49,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: lightning,
    transformShow: false,
    style: {
      zIndex: 10,
      fontSize: 24,
      top: 293,
      left: 835,
      width:15,
      height: 20,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 50,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'GPU Power',
    parameters: 'power',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'W',
    unitshow: false,
    style: {
      zIndex: 9,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 26,
      color: 'rgba(0,0,0,1)',
      top: 291,
      left: 864,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 51,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'W',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 8,
      fontFamily: 'AeeAndCui',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 14,
      color: 'rgba(0,0,0,1)',
      top: 297,
      left: 904,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 52,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: fgcp_img4,
    transformShow: false,
    style: {
      zIndex: 7,
      fontSize: 24,
      top: 278,
      left: 820,
      width: 115,
      height: 45,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },

  {
    id: 53,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: fgcp_img_bg,
    transformShow: false,
    class: 'animation',
    style: {
      zIndex: 6,
      fontSize: 24,
      top: 11,
      left: 64,
      width: 74,
      height: 74,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },

  {
    id: 54,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: fgcp_img_bg2,
    transformShow: false,
    class: 'animation',
    style: {
      zIndex: 5,
      fontSize: 24,
      top: 13,
      left: 426,
      width: 150,
      height: 150,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 55,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: fgcp_img_bg6,
    transformShow: false,
    class: 'animation',
    style: {
      zIndex: 4,
      fontSize: 24,
      top: 311,
      left: 283,
      width: 138,
      height: 138,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 56,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: fgcp_img_bg3,
    transformShow: false,
    class: 'animation',
    style: {
      zIndex: 3,
      fontSize: 24,
      top: -10,
      left: 710,
      width: 89,
      height: 89,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 57,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: fgcp_img_bg4,
    transformShow: false,
    class: 'animation',
    style: {
      zIndex: 2,
      fontSize: 24,
      top: 25,
      left: 840,
      width: 45,
      height: 45,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 58,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: fgcp_img_bg5,
    transformShow: false,
    class: 'animation',
    style: {
      zIndex: 1,
      fontSize: 24,
      top: 165,
      left: 860,
      width: 88,
      height: 88,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
];
