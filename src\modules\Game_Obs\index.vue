<template>
    <div class="obs_layout">
        <window-header :window-name="windowName" :maximizeIcon="false">
            <span class="windowName">{{$t('video.videoRecord')}}</span>
            <span>V{{version}}</span>
        </window-header>
        <div class="obs_main scroll-y">
            <main-switch></main-switch>
            <shortcut-box />
            <setting-box />
            <scheme-box />
            <video-replay-box />
        </div>
    </div>
</template>

<script setup lang="ts">
import {ref, onBeforeMount, watch} from 'vue';
import {gamepp} from 'gamepp';
import WindowHeader from "@/components/mainCom/windowHeader.vue";
import MainSwitch from "./components/MainSwitch.vue";
import ShortcutBox from "./components/ShortcutBox.vue";
import SettingBox from "./components/SettingBox.vue";
import SchemeBox from "./components/SchemeBox.vue";
import VideoReplayBox from "@/modules/Game_Obs/components/VideoReplayBox.vue";
import {obsStore} from "@/modules/Game_Obs/stores";
import { useLanguage } from '@/uitls/useLanguage';
useLanguage()
const zoomValue = ref<number>(1)
const version = ref("")
const windowName = "videoRecord"
const store = obsStore()
async function getVersion() {
    try {
        const versionObj: any = await gamepp.package.getversion.promise("GameObs")
        if (Object.prototype.toString.call(versionObj) === '[object Object]' && 'version' in versionObj) {
            version.value = versionObj.version;
        } else {
            version.value = gamepp.getPlatformVersion.sync()
        }

    } catch (e) {
        console.log(e)
    }
}

watch(zoomValue, (newValue) => {
    document.body.style.zoom = newValue+''
},{immediate:true})

onBeforeMount(async () => {
    getVersion()
    try {
        const zoomWithSystem = gamepp.setting.getInteger.sync(313) // 是否点了跟随系统缩放开关
        if (zoomWithSystem === 1) {
            zoomValue.value = await gamepp.display.getScaleFromWindowInMonitor.promise();
            gamepp.webapp.windows.resize.sync(windowName, Math.floor(1092 * zoomValue.value), Math.floor(572 * zoomValue.value))
        }
        gamepp.display.onDisplayMetricsChanged.addEventListener(async (scaleFactor: number) => {
            const zoomWithSystem = gamepp.setting.getInteger.sync(313)
            if (zoomWithSystem === 1) {
                zoomValue.value = scaleFactor
                gamepp.webapp.windows.resize.sync(windowName, Math.floor(1092 * zoomValue.value), Math.floor(572 * zoomValue.value))
            }
        })
    } catch {

    }
    gamepp.setting.onConfigChanged.addEventListener(async (type, id, value) => {
        if (id === 406 && value) {
            store.state.main_switch = true;
            GameRunningStartRecording();
        }
    });
})
//游戏运行时开启录像
async function GameRunningStartRecording() {
    if (gamepp.isDesktopMode.sync() === false) {
        let Object = {"action":""};
        Object['action'] = 'game_running_start_recording';
        setTimeout(async () => {
            await gamepp.webapp.sendInternalAppEvent.promise('background', Object);
        }, 2000);
    }
}

</script>

<style scoped lang="scss">
.obs_layout {
    width: 1080px;
    height: 560px;
    position: relative;
    border-radius: 4px;
    background: #22232E;
    margin-left: 6px;
    margin-top: 6px;
    box-shadow:0 1px 6px rgba(0,0,0,.6);
    overflow: hidden;

    .windowName {
        margin-right: 10px;
    }
}

.obs_main {
    margin: 10px auto 10px auto;
    position: relative;
    width: 1080px;
    height: 520px;
    overflow-y: overlay;
    color: #fff;
    font-size: 12px;
    padding: 0 17px 20px 17px;

    .obs_box {
        background: #2B2C37;
        position: absolute;
        transition: all 0.3s;
        box-sizing: border-box;
    }
}
</style>
<style lang="scss">
.scroll-y {
    overflow-y: auto;

    &::-webkit-scrollbar {
        width: 5px;
    }

    &::-webkit-scrollbar-track {
        background-color: #21222a;
    }

    &::-webkit-scrollbar-thumb {
        background-color: #71738c;
        border-radius: 2px;
    }
}
.select-family {
    background: #313242;
    border: none;
    .el-select-dropdown__item.is-hovering {
        background-color: rgb(256,256,256,0.3);
        color: white;
    }
    .el-select-dropdown__list {
        background: #313242;
    }
    .el-select-dropdown__item {
        color: white;
    }
    .el-select-dropdown__item.is-selected {
        color: white;
    }
    .el-popper__arrow:before,.el-popper__arrow:after {
        background: #313242 !important;
        border-color: #313242 !important;
    }
}

.el-select {
    .el-select__wrapper{
        background-color: #343646;
        border-color: transparent;
        box-shadow: none;
    }
    .el-select__wrapper.is-hovering{
        box-shadow: none;
    }
}
</style>
