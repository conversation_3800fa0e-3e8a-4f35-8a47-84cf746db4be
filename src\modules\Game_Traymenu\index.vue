<template>
  <div class="Set-up" :style="traymenuStyle">
    <ul class="Setting-bar">
      <li @click="GPP_ShowMainApp">
        <img src="../../assets/icon/Public/ic_opengamepp.png" alt=""/>
        <span class="tit">{{ $t('Setting.openMainUI') }}</span>
      </li>
      <li @click="GPP_JumpSetting">
        <span class="iconfont icon-option"></span>
        <span>{{ $t('Setting.setting') }}</span>
      </li>
      <li @click="GPP_Feedback">
        <span class="iconfont icon-feedback"></span>
        <span>{{ $t('Setting.feedback') }}</span>
      </li>
      <li @click="GPP_OpenURL('https://gamepp.com/support.html')">
        <span class="iconfont icon-quest"></span>
        <span>{{ $t('Setting.help') }}</span>
      </li>
      <li @click="GPP_ExitApp">
        <span class="iconfont icon-quit"></span>
        <span>{{ $t('messages.exit') }}</span>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { ref,computed,onBeforeMount,onMounted} from 'vue';
import { gamepp } from 'gamepp';

const zoomValue = ref<number>(1)

onBeforeMount(async() =>
{
  try
  {
    const zoomWithSystem = gamepp.setting.getInteger.sync(313) // 是否点了跟随系统缩放开关
    if (zoomWithSystem === 1) {
      zoomValue.value = await gamepp.display.getScaleFromWindowInMonitor.promise();
      gamepp.webapp.windows.resize.sync('traymenu',Math.floor(225 * zoomValue.value),Math.floor(188 * zoomValue.value))
    }
    gamepp.display.onDisplayMetricsChanged.addEventListener( async (scaleFactor:number)=>{
      const zoomWithSystem = gamepp.setting.getInteger.sync(313)
      if (zoomWithSystem === 1) {
        zoomValue.value = scaleFactor
        gamepp.webapp.windows.resize.sync('traymenu',Math.floor(225 * zoomValue.value),Math.floor(188 * zoomValue.value))
      }
    })
  }
  catch{

  }
})

const traymenuStyle = computed(() => {
  return {
    zoom:`${zoomValue.value}`
  }
})

function GPP_Feedback() {
  GPP_ShowMainApp();
  setTimeout(()=>{
    gamepp.webapp.sendInternalAppEvent.promise('desktop',{'action':"showFeedback"})
  },200)
}

function GPP_ShowMainApp() {
  gamepp.webapp.windows.show.sync("desktop");
}

function GPP_JumpSetting() {
  gamepp.webapp.windows.show.sync("gamepp_config");
}

function GPP_OpenURL(url:string) {
  try {
    gamepp.shell.openExternal(url);
  } catch (error) {
    window.open(url)
  }
}
async function GPP_ExitApp () {
  let Obj = {action:'ExitGamePP'};
  await gamepp.webapp.sendInternalAppEvent.promise('background', Obj);//发送到Background关闭
}

async function GPP_JumpAboutUs() {
  let is_show = await gamepp.webapp.windows.show.promise('desktop');
  if (is_show) {
    const Object = {'action':"JumpAboutUs"};
    await gamepp.webapp.sendInternalAppEvent.promise("desktop", Object);
  }
}
</script>

<style scoped>
.Set-up{border-radius: 3px;box-shadow: 0 0 6px rgba(0, 0, 0, .6);margin-top: 6px;margin-left: 6px;}
.Set-up{width:210px;height:210px;background:#343647}
.Setting-bar li{width:100%;height:30px;margin-bottom:3px;cursor:pointer;display: flex;align-items: center;}
.Setting-bar img{width:14px;height: 14px;margin-left:12px}
.Setting-bar .iconfont{font-size: 14px;margin-left:12px;color: #3579d5;}
.Setting-bar span{font-size:14px;color:#3579D5;line-height:30px;margin-left:13px}
.Setting-bar li:hover span{color:#FFFFFF;}
.Setting-bar li:hover{background:#3E4050;}
.Setting-bar li:hover .iconfont{color: #ffffff;}
.Setting-bar li:active{background:#3E4050}
.Set-up .tit{font-size:14px;color:#3579D5;margin-left:13px}
.Setting-bar{padding-top:9px}
.openg img{margin-top:-2px;margin-left:11px}
</style>
