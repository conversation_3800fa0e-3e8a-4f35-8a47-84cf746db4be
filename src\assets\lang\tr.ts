const tr = {
  "update": {
    "Game": "Game",
    "PP": "PP",
    "upgradingInProgress": "<PERSON>üncelleniyor",
    "theModuleIsBeingUpdated": "Modül güncelleniyor",
    "dataIsBeingUpdated": "Veriler güncelleniyor...",
    "checkingUpdate": "Güncellemeler kontrol ediliyor",
    "checkingUpgrade": "Güncellemeler kontrol ediliyor",
    "loadingProgramComponent": "Program bileşenleri yükleniyor...",
    "loadingHotkeyModules": "Sıcak tuş bileşeni yükleniyor",
    "loadingGPPModules": "GamePP bileşenleri yükleniyor",
    "loadingBlackWhiteList": "Siyah liste/beyaz liste yükleniyor",
    "loadingGameSetting": "Oyun ayar parametreleri yükleniyor...",
    "loadingUserAbout": "Kullanıcı kimlik doğrulama verileri yükleniyor",
    "loadingGameBenchmark": "Oyun puanı yükleniyor",
    "loadingHardwareInfo": "Donanım Bilgisi Bileşeni Yükleneiyor",
    "loadingDBModules": "Veritabanı modülü yükleniyor...",
    "loadingIGCModules": "IGC modülü yükleniyor",
    "loadingFTPModules": "FTP desteği modülü yükleniyor",
    "loadingDialogModules": "İletişim kutusu modülünü yükleniyor",
    "loadingDataStatisticsModules": "İstatistik modülü yükleniyor",
    "loadingSysModules": "Sistem bileşenleri yükleniyor",
    "loadingGameOptimization": "Oyun optimizasyonu yükleniyor",
    "loadingGameAcceleration": "Oyun hızlandırması yükleniyor",
    "loadingScreenshot": "Yükleme kaydedilmiş ekran görüntüsü",
    "loadingVideoComponent": "Video sıkıştırma bileşeni yükleniyor",
    "loadingFileFix": "Dosya onarımı yükleniyor",
    "loadingGameAI": "Oyun AI kalitesi yükleniyor",
    "loadingNVAPIModules": "NVAPI modülü yükleniyor",
    "loadingAMDADLModules": "AMDADL modülü yükleniyor",
    "loadingModules": "Modül yükleniyor"
  },
  "messages": {
    "append": "Ekle",
    "confirm": "Onayla",
    "cancel": "İptal et",
    "default": "Varsayılan",
    "quickSelect": "Hızlı Seçim",
    "onoffingame": "Oyun içi izlemeyi aktif/pasif yap:",
    "changeKey": "Klavye kısayolunu değiştirmek için tıklayın",
    "clear": "Temizle",
    "hotkeyOccupied": "Kısayol zaten kullanımda. Lütfen yeni birini ayarlayın！",
    "minimize": "En Küçük",
    "exit": "Çıkış",
    "export": "Dışa Aktar",
    "import": "İçe Aktar",
    "screenshot": "Ekran görüntüsü",
    "showHideWindow": "Pencereyi Göster/Gizle",
    "ingameControlPanel": "Oyun içinde kontrol paneli",
    "openOrCloseGameInSettings": "Oyun içi ayarlar panelini geçiş yap",
    "openOrCloseGameInSettings2": "Bu kısayolu basarak etkinleştirin",
    "openOrCloseGameInSettings3": "Oyun İçi İzlemeyi Etkinleştir/Devre Dışı Bırak",
    "openOrCloseGameInSettings4": "Oyun filtresini etkinleştir/devre dışı bırak",
    "startManualRecord": "El ile İstatistik Kaydı Başlat/Durdur",
    "performanceStatisticsMark": "İşaretleyici Performans İstatistikleri",
    "EnableAIfilter": "AI Filtresi'nin etkinleştirilebilmesi için bu kısayol tuşuna basılması gerekir",
    "Start_stop": "Başlat/Duraklat Manuel İstatistik Kaydı",
    "pressureTest": "Stres testi",
    "moduleNotInstalled": "Fonksiyon modülü yüklenmedi",
    "installingPressureTest": "Basınç testi modülü yükleniyor...",
    "importFailed": "İçe aktarma başarısız oldu",
    "gamepp": "Oyun PP",
    "copyToClipboard": "Panoya kopyalandı"
  },
  "home": {
    "homeTitle": "Ana Sayfa",
    "hardwareInfo": "Donanım Bilgisi",
    "functionIntroduction": "Özellikler",
    "fixedToNav": "Navigasyon çubuğuna sabitle",
    "cancelFixedToNav": "Navigasyon çubuğundan pini çıkart",
    "hardwareInfoLoading": "Donanım bilgisi yükleniyor...",
    "performanceStatistics": "Performans İstatistikleri",
    "updateNow": "Şimdi Güncelle",
    "recentRun": "Son Etkinlik",
    "resolution": "Çözünürlük:",
    "duration": "Süre:",
    "gameFilter": "Oyun Filtresi",
    "gameFilterHasAccompany": "Oyun Filtresi Etkinleştirildi",
    "gameFilterHasAccompany2": "Kullanıcılar, Cyberpunk, APEX ve Hogwarts Legacy gibi oyunları oynar.",
    "currentList": "Mevcut listedeki izlenen öğeler",
    "moreFunction": "Benchmark, stres testi, masaüstü izleme ve diğer özellikler geliştirme aşamasında.",
    "newVersion": "Yeni sürüm mevcut !",
    "discoverUpdate": "Güncelleme bulundu！",
    "downloading": "İndirme",
    "retry": "Yeniden denemek",
    "erhaAI": "2HaAI",
    "recordingmodule": "Bu özellik kayıt modülünden bağımlıdır",
    "superPower": "Ultra Modu",
    "autoRecord": "Oyun içindeki öldürme anlarını otomatik olarak kaydedin ve öne çıkan klipleri kolayca kaydedin",
    "externalDevice": "Dinamik Çevre Işığı",
    "linkage": "Oyunlardaki kill sahnelerini tetikleyin ve bağlı çevre birimleri üzerinden görüntüleyin",
    "AI": "Yapay Zeka Performans Testi",
    "test": "GPU ile AI modellerini test edin ve GPU'nun AI performans puanını görüntüleyin",
    "supportedGames": "Desteklenen Oyunlar",
    "games": "Naraka: Bladepoint, Delta Force, FragPunk, League of Legends, PUBG, Valorant, The Finals, APEX Legends, COD20",
    "videoRecording": "Video Kaydı",
    "videoRecording2": "OBS tabanlı video kaydetme işlevi, farklı kalite ve akışkanlık gereksinimlerini karşılamak için video bit hızı ve kare hızı (FPS) ayarlamayı destekler; ayrıca \"Anında Yeniden Oynatma\" özelliğini de destekler, kısayol tuşuna bastığınızda her an öne çıkan anları kaydedebilirsiniz!",
    "addOne": "Ücretsiz olarak al",
    "gamePlatform": "Oyun platformu",
    "goShop": "Mağaza Sayfasına Git",
    "receiveDeadline": "Eylem Sonrası Talep Süresi",
    "2Ai": "2 Kıkırt AI",
    "questionDesc": "Sorun Açıklaması",
    "inputYourQuestion": "Buraya geri bildirimde bulunmak istediğiniz önerileri veya yorumları yazınız。",
    "uploadLimit": "En fazla 3 yerel resim, JPG/PNG/BMP formatında yükleyin",
    "email": "E-posta",
    "contactWay": "İletişim bilgileri",
    "qqNumber": "QQ Numarası (isteğe bağlı)",
    "submit": "Gönder"
  },
  "hardwareInfo": {
    "hardwareOverview": "Donanım Genel Bakış",
    "copyAllHardwareInfo": "Tüm donanım bilgilerini kopyala",
    "processor": "İşlemci",
    "coreCount": "Çekirdek :",
    "threadCount": "Thread Sayısı:",
    "currentFrequency": "Mevcut Frekans:",
    "currentVoltage": "Mevcut voltaj:",
    "copy": "Kopyala",
    "releaseDate": "Yayın tarihi",
    "codeName": "Kod adı",
    "thermalDesignPower": "Isı Tasarım Gücü",
    "maxTemperature": "Maksimum Sıcaklık",
    "graphicsCard": "Grafik kartı",
    "brand": "Marka:",
    "streamProcessors": "Akış İşlemcisi:",
    "Videomemory": "Video RAM：",
    "busSpeed": "Veri Yolu Hızı",
    "driverInfo": "Sürücü Bilgisi",
    "driverInstallDate": "Sürücü Kurulum Tarihi",
    "hardwareID": "Donanım Kimliği",
    "motherboard": "Anakart",
    "chipGroup": "Chipset:",
    "BIOSDate": "BIOS Tarihi",
    "BIOSVersion": "BIOS Sürümü",
    "PCIESlots": "PCIe yuvası",
    "PCIEVersion": "Desteklenen PCIe Sürümü",
    "memory": "Bellek",
    "memoryBarCount": "Miktar:",
    "totalSize": "Boyut:",
    "channelCount": "Kanal:",
    "Specificmodel": "Belirli Model",
    "Pellet": "Parçacık Jeneratörü",
    "memoryBarEquivalentFrequency": "Etkin Bellek Frekansı:",
    "hardDisk": "Sabit disk",
    "hardDiskCount": "Sabit disk sayısı:",
    "actualCapacity": "Gerçek Kapasite",
    "type": "Tür",
    "powerOnTime": "Açma süresi",
    "powerOnCount": "Güç Döngüleri",
    "SSDRemainingLife": "SSD Kalan Ömür",
    "partitionInfo": "Bölüm Bilgisi",
    "hardDiskController": "Sabit disk kontrolcüsü",
    "driverNumber": "Sürücü Numarası",
    "display": "Ekran",
    "refreshRate": "Yenileme Hızı:",
    "screenSize": "Ekran boyutu:",
    "inches": "inç",
    "productionDate": "Üretim Tarihi",
    "supportRefreshRate": "Yenileme Hızı Desteği",
    "screenLongAndShort": "Ekran Boyutları",
    "systemInfo": "Sistem Bilgisi",
    "version": "Sürüm",
    "systemInstallDate": "Sistem Kurulum Tarihi",
    "systemBootTime": "Mevcut Başlangıç Zamanı",
    "systemRunTime": "Çalışma zamanı",
    "Poccupied": "P Kullanımı",
    "Eoccupied": "E Kullanılıyor",
    "occupied": "Kapalı",
    "temperature": "Sıcaklık",
    "Pfrequency": "İşlemci Frekansı",
    "Efrequency": "E-Sıklık",
    "thermalPower": "Isı enerjisi",
    "frequency": "Sıklık",
    "current": "Mevcut",
    "noData": "Veri yok",
    "loadHwinfo_SDK": "Hwinfo_SDK.dll yüklenemedi, donanım/sensör verileri okunamadı.",
    "loadHwinfo_SDK_reason": "Bu sorunun olası nedenleri:",
    "reason": "Neden",
    "BlockIntercept": "Antivirüs yazılımı tarafından engellendi, örneğin: 2345 Antivirus Software (2345 Active Defense Süreci, McAfee Active Defense Süreci)",
    "solution": "Çözüm:",
    "solution1": "İlgili süreçleri kapatıp kaldırdıktan sonra GamePP'yi yeniden başlatın",
    "solution2": "İlgili cihazlar çıkarıldıktan sonra GamePP'yi yeniden başlatın",
    "RestartGamePP": "Kontrolcüyü kesebilirsiniz, cihazdan yanıt gelene kadar bekleyin, ardından GamePP'yi yeniden başlatın",
    "HWINFOcannotrun": "Hwinfo düzgün çalışamıyor",
    "downloadHWINFO": "Hwinfo'yu İndir",
    "openHWINFO": "Hwinfo başlatıldıktan sonra, RUN'a tıklamak programı normal şekilde başlatır mı?",
    "hardwareDriverProblem": "Donanım sürücü sorunları",
    "checkHardwareManager": "Donanım yöneticisini kontrol ederek anakart ve grafik kartı sürücülerinin düzgün şekilde yüklü olduğundan emin olun",
    "systemProblem": "Sistem sorunları, örneğin: Baofeng veya Xiaoma gibi aktivasyon araçlarının kullanımı sürücülerin yüklenmesini engelleyebilir ve Windows 7 sistem yamaları otomatik olarak yüklenemez",
    "reinstallSystem": "Sistemi yeniden yükleyerek etkinleştirin. WIN7'yi indirip yükleyin: ********* güncelleme yaması",
    "Windows7": "Windows 7: SHA-256 Yaması Kurulumu, Windows 10: Dijital Sertifika Kullanarak Etkinleştirme, Windows 11 Önizleme Sürümü: Bellek Integritesi'ni Devre Dışı Bırak",
    "ViolenceActivator": "Eğer Xiaoma gibi kaba kuvvet aktifleştirme araçları kullanıldıysa, sistemini tamir edin veya yeniden yükleyin",
    "MultipleGraphicsCardDrivers": "Bilgisayarda farklı markalı grafik kartı sürücüleri yüklüdür, örneğin AMD ve Nvidia sürücüleri aynı anda yüklü olabilir.",
    "UninstallUnused": "Gereksiz grafik sürücülerini kaldırdıktan sonra bilgisayarı yeniden başlatın",
    "OfficialQgroup": "Yukarıdaki nedenlerden hiçbiri uygulanmamaktadır. Lütfen resmi QQ grubumuza katının: 908287288 (Grup 5) çözüm için.",
    "ExportHardwareData": "Donanım Verilerini Dışa Aktar",
    "D3D": "D3D Kullanımı",
    "Total": "Toplam kullanım",
    "VRAM": "VRAM Kullanımı",
    "VRAMFrequency": "VRAM Frekansı",
    "SensorData": "Sensör Verileri",
    "CannotGetSensorData": "Bilgisayar sensörü verileri alınamadı",
    "LoadingHardwareInfo": "Donanım bilgileri yükleniyor…",
    "ScanTime": "Son tarama:",
    "Rescan": "Yeniden tara",
    "Screenshot": "Ekran görüntüsü",
    "configCopyed": "Yapılandırma bilgileri panoya kopyalandı.",
    "LegalRisks": "Potansiyel yasal riskler tespit edildi",
    "brandLegalRisks": "Marka gösterimi potansiyel hukuki riskler içeriyor olabilir",
    "professionalVersion": "Profesyonel Sürüm",
    "professionalWorkstationVersion": "Profesyonel Workstation Sürümü",
    "familyEdition": "Ev Sürümü",
    "educationEdition": "Eğitim Sürümü",
    "enterpriseEdition": "Enterprise Sürümü",
    "flagshipEdition": "Premium Sürümü",
    "familyPremiumEdition": "Aile Premium Sürümü",
    "familyStandardEdition": "Aile Standart Sürümü",
    "primaryVersion": "Temel Sürüm",
    "bit": "bit",
    "tempWall": "Sıcaklık Duvarı",
    "error": "Hata",
    "screenshotSuccess": "Ekran görüntüsü başarıyla kaydedildi",
    "atLeastOneData": "En az 1 veri girişi korunmalıdır",
    "atMostSixData": "En fazla 6 veri girişi ekleyin",
    "screenNotActivated": "Etkinleştirilmemiş"
  },
  "psc": {
    "processCoreAssign": "İşlemci Çekirdek Ataması",
    "CoreAssign": "Çekirdek Ataması：",
    "groupName": "Grup Adı:",
    "notGameProcess": "Oyun dışı süreçler",
    "unNamedProcess": "İsimsiz Grup",
    "Group2": "Grup",
    "selectTheCore": "Çekirdeği Seçin",
    "controls": "İşlem",
    "tips": "İpucu",
    "search": "Ara",
    "shiftOut": "Geri dön",
    "ppValue": "PP Değeri",
    "ppDesc": "PP değeri geçmiş donanım kaynak tüketimini yansıtır. Daha yüksek değerler, daha fazla donanım kaynağı kullanımını gösterir.",
    "littletips": "İpucu: Süreci tutun ve sol taraftaki gruba sürükleyin。",
    "warning1": "Gruplar arasında çekirdek iş parçacıkları seçmek performansı etkileyebilir. Aynı gruptan çekirdeklerin kullanılması önerilir.",
    "warning2": "Bu grup adını boş bırakmak istediğinize emin misiniz?",
    "warning3": "Silindikten sonra çekirdek atama etkisi geçersiz olacak. Bu grubu silmek istediğinizden emin misiniz?",
    "allprocess": "Tüm Süreçler",
    "pleaseCheckProcess": "Lütfen süreci seçin",
    "dataSaveDesktop": "Veriler masaüstüne kaydedildi.",
    "createAGroup": "Grup oluştur",
    "delGroup": "Grubu sil",
    "Group": "Grup",
    "editGroup": "Grubu Düzenle",
    "groupinfo": "Grup Bilgisi",
    "moveOutGrouping": "Gruptan kaldır",
    "createANewGroup": "Yeni Grup Oluştur",
    "unallocatedCore": "Atanmamış Çekirdek",
    "inactiveProcess": "Deaktif işlem",
    "importGroupingScheme": "Gruplama Profili'ni İçe Aktar",
    "derivedPacketScheme": "Grup Ayarlarını Dışa Aktar",
    "addNowProcess": "Geçerli çalışan süreci ekle",
    "displaySystemProcess": "Sistem süreçlerini göster",
    "max64": "Maksimum seçim 64 thread'dir",
    "processName": "İşlem Adı",
    "chooseCurProcess": "Mevcut süreci seçin",
    "selectNoProcess": "Hiçbir işlem seçilmedi",
    "coreCount": "Çekirdekler",
    "threadCount": "Threads",
    "process": "İşlem",
    "plzInputProcessName": "İşlem adını el ile eklemek için girin",
    "has_allocation": "İş Parçacığı Atama Şemaları Olan Süreçler",
    "not_made": "Hiçbir işlemi çekmeye atamadınız",
    "startUse": "Optimizasyonu Etkinleştir",
    "stopUse": "İyileştirmeyi Devre Dışı Bırak",
    "threadAllocation": "İş parçacığı tahsisi",
    "configProcess": "İşlem Ayarları",
    "selectThread": "İş parçacığı seç",
    "hyperthreadingState": "Hyper-Threading Durumu",
    "open": "Aktif",
    "notYetUnlocked": "Devre Dışı",
    "nonhyperthreading": "Hyper-Threading Olmadan",
    "intervalSelection": "Aralık Seçimi",
    "invertSelection": "Seçimi Tersine Çevir",
    "description": "Oyun işlemlerini belirlenen CPU çekirdeklerinde kilitleyerek arka plandaki programların etkisini zekice izole edin. FPS tavanını etkili bir şekilde artırır ve oyun FPS'yi stabil hale getirir! Oyun sırasında aniden ortaya çıkan gecikmeleri ve frame drops'leri azaltır, çok çekirdekli işlemcilerin tam performansını serbest bırakarak oyun süresince yüksek frame rate'yi garanti eder!",
    "importSuccess": "İçe aktarma başarılı",
    "importFailed": "İçe aktarım başarısız"
  },
  "InGameMonitor": {
    "onoffingame": "Oyun İçinde İzlemeyi Aç/Kapat:",
    "InGameMonitor": "Oyun İçinde İzleme",
    "CustomMode": "Özel Mod",
    "Developing": "Geliştirme aşamasında...",
    "NewMonitor": "İzleme Öğesi Ekle",
    "Data": "Parametreler",
    "Des": "Not",
    "Function": "Fonksiyon",
    "Editor": "Düzenle",
    "Top": "En üste sabitle",
    "Delete": "Sil",
    "Use": "Kullanmak",
    "DragToSet": "Panel çağrıldıktan sonra ayarlamak için çekerek",
    "MonitorItem": "İzleme Ögesi",
    "addMonitorItem": "İzleme Öğesi Ekle",
    "hide": "Gizle",
    "show": "Göster",
    "generalstyle": "Genel Ayarlar",
    "restoredefault": "Varsayılan Ayarları Geri Yükle",
    "arrangement": "Düzen",
    "horizontal": "Yatay",
    "vertical": "Dikey",
    "monitorposition": "İzleme Yeri",
    "canquickselectposition": "Hızlıca sol haritadaki bir konumu seçin。",
    "curposition": "Mevcut Konum:",
    "background": "Ardın",
    "backgroundcolor": "Arka plan rengi:",
    "font": "Yazı tipi",
    "fontStyle": "Yazı tipi stilleri",
    "fontsize": "Yazı Tipi Boyutu：",
    "fontcolor": "Yazı tipi rengi:",
    "style": "Stil:",
    "style2": "Stil",
    "performance": "Performans",
    "refreshTime": "Yenileme süresi:",
    "goGeneralSetting": "Genel Ayarlara Git",
    "selectMonitorItem": "İzleme Öğesini Seçin",
    "selectedSensor": "Seçilen sensör:",
    "showTitle": "Başlığı Göster",
    "hideTitle": "Başlığı Gizle",
    "showStyle": "Görüntüleme Modu:",
    "remarkSize": "Not boyutu:",
    "remarkColor": "Not rengi:",
    "parameterSize": "Parametre boyutu :",
    "parameterColor": "Parametre Rengi :",
    "lineChart": "Çizgi Grafiği",
    "lineColor": "Çizgi Rengi:",
    "lineThickness": "Çizgi Kalınlığı：",
    "areaHeight": "Bölge Yüksekliği:",
    "sort": "Sırala",
    "displacement": "Deplasman:",
    "up": "Yukarı Taşı",
    "down": "Aşağıya Hareket Ettir",
    "bold": "Kalın",
    "stroke": "Çerçeve",
    "text": "Yazı",
    "textLine": "Metin + Çizgi Grafiği",
    "custom": "Özelleştirilmiş",
    "upperLeft": "Üst sol",
    "upper": "Orta-Üst",
    "upperRight": "Üst sağ",
    "Left": "Sol Merkez",
    "middle": "Merkez",
    "Right": "Sağ Merkez",
    "lowerLeft": "Alt sol",
    "lower": "Orta-Düşük",
    "lowerRight": "Alt sağ",
    "notSupport": "Çevre birimleri fare tıklamasıyla görüntüleme/gizleme özelliğini desteklememektedir",
    "notSupportRate": "Dönüş oranı tıklamayla değiştirilemez",
    "notFindSensor": "Algılayıcı bulunamadı. Değiştirmek için tıklayın。",
    "monitoring": "İzleme",
    "condition": "Şart",
    "bigger": "Daha büyük",
    "smaller": "Daha az",
    "biggerThan": "Eşik aşınmış",
    "biggerThanthreshold": "Eşik yüzdesinden büyük",
    "smallerThan": "Eşik değerinin altındayken",
    "smallerThanthreshold": "Seviye yüzdesinden az",
    "biggerPercent": "Mevcut değerin yüzde azalması",
    "smallerPercent": "Mevcut Değer Yüzdesi Artışı",
    "replay": "Anında Yeniden Oynatma Fonksiyonu",
    "screenshot": "Ekran Görüntüsü Özelliği",
    "text1": "Bir sensör değeri varsa",
    "text2": " ve ",
    "text3": "Bekleme süresi",
    "text4": "Belirlenen saniye içinde daha yüksek bir değer görünmezse anında tetiklenir",
    "text5": "Her tekrar tetiklemesinden sonra eşiği tetikleme anındaki değere güncelleyerek sık tekrarlamaları azaltın",
    "text6": "Yinelenen oyunu tetiklemek için kullanılan mevcut eşiği gösterir",
    "text7": "Algılayıcı değerlerini göster",
    "text8": "Başlangıç eşiği aşıldı",
    "text9": "İlk Eşik Değeri Altında",
    "text10": "İlk Eşik Sayısı",
    "initThreshold": "Başlangıç Eşiği",
    "curThreshold": "Mevcut eşik değeri:",
    "curThreshold2": "Mevcut eşiik",
    "resetCurThreshold": "Mevcut eşik değeri sıfırla",
    "action": "Özellik Etkinleştir",
    "times": "Kez",
    "percentage": "Yüzde",
    "uninstallobs": "Kayıt modülü indirilmedi",
    "install": "İndir",
    "performanceAndAudioMode": "Performans ve Ses Uyum Modu",
    "isSaving": "Kaydediliyor",
    "video_replay": "Anında Yeniden Oynatma",
    "saved": "Kaydedildi",
    "loadQualitysScheme": "Grafik profili yükle",
    "notSet": "Yapılandırılmadı",
    "mirrorEnable": "Filtre etkinleştirildi",
    "canBeTurnedOff": "Geri dön",
    "mirrorClosed": "Oyun filtresi devre dışı bırakıldı",
    "closed": "Kapatıldı",
    "openMirror": "Filtreyi aç",
    "wonderfulScenes": "Öne Çıkanlar",
    "VulkanModeHaveProblem": "Vulkan modunda uyum sorunları vardır",
    "suggestDxMode": "Dx moduna geçmeniz önerilir",
    "functionNotSupported": "Bu özellik desteklenmiyor",
    "NotSupported": "Desteklenmiyor",
    "gppManualRecording": "GamePP, el ile kayıt",
    "perfRecordsHaveBeenSaved": "Performans verileri kaydedildi",
    "redoClickF8": "Kaydı devam ettirmek için F8 tuşuna tekrar basın.",
    "startIngameMonitor": "Oyun içinde izleme işlevini etkinleştirmektedir",
    "inGameMarkSuccess": "Oyunda işaretlemeyi başarıyla tamamladınız",
    "recordingFailed": "Kayıt başarısız",
    "recordingHasNotDownload": "Kayıt özelliği henüz indirilmemiştir",
    "hotkeyDetected": "Fonksiyon kısa yolu çakışması tespit edildi",
    "plzEditIt": "Lütfen yazılım içinde değişiklikleri yaptıktan sonra kullanın",
    "onePercentLowFrame": "1% Düşük kare",
    "pointOnePercentLowFrame": "0.1% Düşük kare",
    "frameGenerationTime": "Çerçeve Oluşturma Zamanı",
    "curTime": "Mevcut Zaman",
    "runTime": "Çalışma süresi",
    "cpuTemp": "CPU Sıcaklığı",
    "cpuUsage": "CPU Kullanımı",
    "cpuFreq": "CPU Frekansı",
    "cpuPower": "CPU Termal Gücü",
    "gpuTemp": "GPU Sıcaklığı",
    "gpuUsage": "GPU Kullanımı",
    "gpuPower": "GPU Isısal Güç",
    "gpuFreq": "GPU Saat Hızı",
    "memUsage": "Bellek kullanımı"
  },
  "LoginArea": {
    "login": "Giriş Yap",
    "loginOut": "Çıkış yap",
    "vipExpire": "Süresi Doldu",
    "remaining": "Kalan",
    "day": "Gökyüzü",
    "openVip": "Üyelik Etkinleştir",
    "vipPrivileges": "Üye Avantajları",
    "rechargeRenewal": "Yeniden Doldur & Yenile",
    "Exclusivefilter": "Filtre",
    "configCloudSync": "Bulut Senkronizasyonunu Ayarla",
    "comingSoon": "Yakında Mevcut Olacak"
  },
  "GameMirror": {
    "filterStatus": "Filtre Durumu",
    "filterPlan": "Filtre Preset",
    "filterShortcut": "Filtre Kısa Yollar",
    "openCloseFilter": "Filtreyi Etkinleştir/Devre Dışı Bırak:",
    "effectDemo": "Etki Gösterimi",
    "demoConfig": "Demo Ayarları",
    "AiFilter": "AI filtre etkileri oyun içindeki etkilere bağlıdır",
    "AiFilterFAQ": "AI Filtreleriyle Yaygın Sorunlar",
    "gamePPAiFilter": "AI Filtresi GamePP",
    "gamePPAiFilterVip": "GamePP VIP özel AI süzgeci, oyun senaryolarına göre süzgeç parametrelerini dinamik olarak ayarlayarak görsel efektleri optimize eder ve oyun deneyimini geliştirir.",
    "AiMingliangTips": "AI Parlaklık: Oyun ekranı çok karanlık olduğunda kullanmanız önerilir.",
    "AiBrightTips": "AI Vibrant: Oyun görselleri çok karanlık görünürken kullanılması önerilir.",
    "AiDarkTips": "AI Dimming: Oyun görselleri çok canlı olduğunda kullanım için önerilir",
    "AiBalanceTips": "AI Denge: Çoğu oyun senaryosu için uygundur.",
    "AiTips": "İpucu: Oyun içinde AI filtresini kullanmak için kısayol tuşuna basmanız gerekir.",
    "AiFilterUse": "Lütfen oyun içinde kullanın",
    "AiFilterAdjust": "AI Filtresi için Kısayol Ayarı",
    "Bright": "Parlak",
    "Soft": "Yazılım",
    "Highlight": "Vurgula",
    "Film": "Video",
    "Benq": "BenQ",
    "AntiGlare": "Anti-Glare",
    "HighSaturation": "Yüksek doygunluk",
    "Brightness": "Vivid",
    "Day": "Gün",
    "Night": "Gece",
    "Nature": "Doğal",
    "smooth": "İnce",
    "elegant": "Sıkı",
    "warm": "Sıcak Ton",
    "clear": "Temizle",
    "sharp": "Netlik",
    "vivid": "Dinamik",
    "beauty": "Vivid",
    "highDefinition": "HD",
    "AiMingliang": "AI Parlaklık",
    "AiBright": "AI Canlı",
    "AiDark": "AI Karanlıklaştırmak",
    "AiBalance": "AI Dengeleme",
    "BrightTips": "Vivid filtresi, klasik, eylem veya macera oyunlarına uygundur ve oyun görsellerini daha dinamik ve ilgi çekici hale getirmek için renk doygunluğunu artırır。",
    "liangTips": "Oyun ekranı çok karanlık olduğunda filtrelerin kullanılması önerilir。",
    "anTips": "Oyun ekranı çok karanlık olduğunda bu süzgecin kullanımı önerilir。",
    "jianyiTips": "Oyun görselleri çok canlıysa süzgeç kullanmanız önerilir。",
    "shiTips": "Filtre, çoğu oyun senaryosu için uygundur。",
    "shi2Tips": "Filtre, klasik, eylem ya da macera oyunlarına uygundur ve oyunun görsellerini daha canlı ve etkileyici hale getirmek için renk doygunluğunu artırır。",
    "ruiTips": "Yumuşak filtre renkleri ve yumuşak aydınlatma etkileri, hayalperest, sıcak veya özlem dolu sahneleri betimlemek için uygundur。",
    "qingTips": "Parlak ton, yüksek kontrast, net detaylar. Bol ışıkla canlı sahneler için uygundur.",
    "xianTips": "Daha yüksek kontrast ve parlaklık ayarları, koyu sahnelerde net detaylar sunar ve bozulmadan, parlak sahnelerde rahat görüntüleme sağlar.",
    "dianTips": "Ekran parlaklığını ve renklerini orta düzeyde artırarak mümkün olduğu kadar sinematik görsel kaliteyi elde edin",
    "benTips": "Tamamen beyaz oyun ortamlarında göz yorgunluğunu önlemek için beyaz ışık etkilerini azaltır",
    "fangTips": "Açık dünya ve macera oyunları için optimize edilmiştir, parlaklık ve kontrastı artırarak daha net görseller sunar.",
    "jiaoTips": "RPG ve simülasyon oyunları için uygundur, dengeli tonlar, görsel gerçekçilik artırılmıştır",
    "jieTips": "Zengin hikayeli ve duygusal incelemeleri olan oyunlar için optimize edilmiştir, ayrıntıları ve yumuşaklığı artırarak daha ince görseller elde edilir。",
    "jingTips": "Aksiyon ve rekabetçi oyunlar için optimize edilmiştir, netlik ve kontrastı artırarak daha net görseller sunar.",
    "xiuTips": "İyileşme ve casual oyunlar için optimize edilmiştir, sıcak tonları ve yumuşaklığı artırarak daha sıcak bir atmosfer yaratır.",
    "qihuanTips": "Zengin fantazi unsurları ve canlı renkler içeren sahneler için uygundur. Renk doygunluğunu artırarak güçlü bir görsel etki yaratır",
    "shengTips": "Renkleri ve detayları artırarak sahnenin canlılığını ve gerçekçiliğini vurgulayın。",
    "sheTips": "FPS, bulmaca veya macera oyunları için optimize edilmiştir, detayları ve kontrastı artırarak oyun dünyasının gerçekçiliğini geliştirir.",
    "she2Tips": "Ateşli, yarışma veya dövüş oyunları için uygundur, yüksek tanımlı detayları ve dinamik performansı vurgular, oyun deneyiminin yoğunluğunu ve görsel etkilerini artırır.",
    "an2Tips": "Koyu ortamlardaki sahne netliğini artırır, karanlık veya gece senaryoları için uygundur。",
    "wenTips": "Sanatsal, macera veya dinlenme oyunları için uygundur, yumuşak renk tonları ve ışık-gölge efektleri yaratarak sahnenin zarif ve sıcak bir havasını artırır。",
    "jing2Tips": "Yarışma, müzik ritmi veya gece şehri senaryo oyunları için uygundur, canlı renkleri ve aydınlatma etkilerini vurgular。",
    "jing3Tips": "Yarışmacı, eylem veya fantasy türündeki oyunlar için optimize edilmiştir, renk kontrastını artırarak görselleri daha canlı ve dinamik hale getirir。",
    "onlyVipCanUse": "Bu filtre yalnızca VIP kullanıcılar içindir"
  },
  "GameRebound": {
    "noGame": "Oyun kaydı yok",
    "noGameRecord": "Henüz oyun kaydı yok! Hemen bir oturum başlatın!",
    "gameDuration": "Bugünkü Oyun Süresi:",
    "gameElectricity": "Günlük Elektrik Tüketimi",
    "degree": "Derece",
    "gameCo2": "Bugünkü CO₂ Emisyonları :",
    "gram": "Tuş",
    "manualRecord": "Elamanlı Kayıt",
    "recordDuration": "Kayıt süresi:",
    "details": "Ayrıntılar",
    "average": "Ortalama",
    "minimum": "Minimum",
    "maximum": "Maksimum",
    "occupancyRate": "Kullanım",
    "voltage": "Gerilim",
    "powerConsumption": "Enerji Tüketimi",
    "start": "Başlat：",
    "end": "Bitiş",
    "Gametime": "Oyun süresi :",
    "Compactdata": "Veri Optimizasyonu",
    "FullData": "Tüm veriler",
    "PerformanceAnalysis": "Performans Analizi",
    "PerformanceAnalysis2": "Olay Raporu",
    "HardwareStatus": "Donanım Durumu",
    "totalPower": "Toplam Enerji Tüketimi",
    "TotalEmissions": "Toplam Emisyon",
    "PSS": "Not: Grafikteki veriler ortalama değerleri temsil eder.",
    "FrameGenerationTime": "Frame Üretim Süresi",
    "GameResolution": "Oyun Çözünürlüğü",
    "FrameGenerationTimeTips": "Bu veri noktası anormal olarak yüksek ve istatistiklerden çıkarıldı",
    "FrameGenerationTimeTips2": "Bu veri noktası anormal olarak düşük olduğu için istatistiklere dahil edilmedi",
    "noData": "Mevcut veri yok",
    "ProcessorOccupancy": "CPU Kullanımı",
    "ProcessorFrequency": "İşlemci Frekansı",
    "ProcessorTemperature": "İşlemci Sıcaklığı",
    "ProcessorHeatPower": "İşlemci TDP",
    "GraphicsCardOccupancy": "GPU Kullanımı D3D",
    "GraphicsCardOccupancyTotal": "Toplam GPU Kullanımı",
    "GraphicsCardFrequency": "GPU Frekansı",
    "GraphicsCardTemperature": "GPU Sıcaklığı",
    "GraphicsCardCoreTemperature": "GPU Çekirdek Isı Noktası Sıcaklığı",
    "GraphicsCardHeatPower": "GPU Isı Gücü",
    "GraphicsCardMemoryTemperature": "GPU Bellek Sıcaklığı",
    "MemoryOccupancy": "Bellek Kullanımı",
    "MemoryTemperature": "Bellek Sıcaklığı",
    "MemoryPageFaults": "Bellek Sayfalaması Kesintisi",
    "Duration": "Süre",
    "Time": "Zaman",
    "StartStatistics": "İstatistikleri Başlat",
    "Mark": "Etiket",
    "EndStatistics": "İstatistikleri Bitir",
    "LineChart": "Çizgi Grafiği",
    "AddPointInGame_m1": "Oyun içinde basın",
    "AddPointInGame_m2": "Bir işaret noktası eklenebilir",
    "LeftMouse": "Sol tıkla göster/gizle arasında geçiş yapmak için, sağ tıkla rengi değiştirmek için",
    "DeleteThisLine": "Bu polilineyi sil",
    "AddCurve": "Eğri ekle",
    "AllCurvesAreHidden": "Tüm eğriler gizlendi",
    "ThereAreSamplingData": "Toplam örnek veri:",
    "Items": "Giriş",
    "StatisticsData": "İstatistikler",
    "electricity": "Enerji Kullanımı",
    "carbonEmission": "Karbon Emisyonları",
    "carbonEmissionTips": "Karbondioksit Emisyonları (kg) = Elektrik Tüketimi (kWh) × 0,785",
    "D3D": "D3D Kullanımı:",
    "TOTAL": "Toplam Kullanım Süresi:",
    "Process": "İşlem:",
    "L3Cache": "L3 Önbellek:",
    "OriginalFrequency": "Orijinal frekans:",
    "MaximumBoostFrequency": "Maksimum Turbo Boost:",
    "DriverVersion": "Sürücü sürümü :",
    "GraphicsCardMemoryBrand": "VRAM Markası:",
    "Bitwidth": "Veri Yolu Genişliği",
    "System": "Sistem:",
    "Screen": "Ekran",
    "Interface": "Arayüz:",
    "Channel": "Kanal:",
    "Timing": "Sıra: ",
    "Capacity": "Kapasite:",
    "Generation": "cebir",
    "AddPoint_m1": "Oyun içinde bastır",
    "AddPoint_m2": "İşaret noktası ekle",
    "Hidden": "Gizli",
    "Totalsampling": "Toplam Örnekleme Verileri:",
    "edition": "Sürücü Sürümü:",
    "MainHardDisk": "Ana sabit disk",
    "SetAsStartTime": "Başlangıç Zamanı Olarak Ayarla",
    "SetAsEndTime": "Bitiş saati olarak ayarla",
    "WindowWillBe": "İş Verimliliği İstatistikleri Penceresi burada yer alacak",
    "After": "Kapatıldıktan sonra",
    "NoLongerPopUpThisGame": "Bu oyun artık açılır pencereleri göstermeyecektir",
    "HideTemperatureReason": "Sıcaklık nedenini gizle",
    "HideTemperatureReason2": "Olay Raporunu Gizle",
    "HideOtherReason": "Diğer Sebepleri Gizle",
    "CPUanalysis": "CPU Performansı Analizi",
    "TemperatureCause": "Sıcaklık Nedeni",
    "tempSensorEvent": "Sıcaklık Sensörü Olayı",
    "NoTemperatureLimitation": "Sıcaklık nedeniyle CPU performansı azaltma tespit edilmedi. Soğutma sisteminiz bu oyunun gereksinimlerini tamamen karşılayabilecek kapasitededir.",
    "NoTemperatureLimitation2": "Sıcaklık sensörleri olayı yok, soğutma sisteminiz bu oyunun gereksinimlerini tamamen karşılayabilir.",
    "performanceis": "Seçildi",
    "Inside": "İç,",
    "TheStatisticsTimeOf": "İstatistiksel dönem ilgili tetikleme koşullarını karşılar. En sık tetikleme nedeni",
    "limited": "Sıcaklık Nedeniyle Performans Sınırlamalarına Ait Toplam Zamanın Yüzdesi",
    "SpecificReasons": "Sıcaklıkla ilgili nedenlerde belirli nedenler ve oranları:",
    "OptimizationSuggestion": "Optimizasyon Önerileri :",
    "CPUtemperature": "CPU sıcaklığı çok yüksektir. Lütfen CPU soğutma ortamını kontrol edin/iyileştirin。",
    "CPUoverheat": "CPU, ana kartın güç kaynağı nedeniyle aşırı ısınıyor. Ana kartla ilgili ayarları kontrol edin veya soğutma ortamını iyileştirin。",
    "OtherReasons": "Diğer nedenler",
    "NoPowerSupplyLimitation": "CPU performansı güç kaynağı/güç tüketimiyle sınırlı değildir. BIOS güç tüketimi ayarlarınız bu oyunun gereksinimlerini karşılamaktadır。",
    "PowerSupplyLimitation": "Elektrik kaynağı/enerji tüketimi sınırlamaları nedeniyle",
    "SpecificReasonsInOtherReasons": "Belirli bir neden ve diğer nedenler arasında oranı:",
    "PleaseCheckTheMainboard": "Anakartın güç teslim durumunu kontrol edin veya BIOS güç ayarlarını ayarlayarak diğer faktörlerden kaynaklanan CPU performansı sınırlamalarını çözün",
    "CPUcoretemperature": "Çekirdek sıcaklığı Tj,Max seviyesine ulaştı ve sınırlanmıştır",
    "CPUCriticalTemperature": "CPU Sıcaklığı Kritik",
    "CPUCircuitTemperature": "CPU Paketi/Ring Veri Yolu Tj,Max'a ulaşılarak sınırlanıyor",
    "CPUCircuitCriticalTemperature": "CPU paketi/anakart halkası kritik sıcaklık seviyesine ulaştı",
    "CPUtemperatureoverheating": "CPU aşırı ısınması algılandı, bu da sıcaklıkları düşürmek ve donanım arızalarını önlemek için otomatik frekans azaltmasını tetikleyecektir.",
    "CPUoverheatingtriggered": "İşlemci, soğutma mekanizmasını aktif hale getirerek voltaj/çalışma frekansını ayarlayacak ve enerji tüketimini ve sıcaklığı azaltacaktır.",
    "CPUPowerSupplyOverheating": "CPU, ana kartı besleme sisteminin ciddi şekilde aşırı ısınması nedeniyle sınırlıdır",
    "CPUPowerSupplyLimitation": "Ana kartı güç kaynağından aşırı ısınma nedeniyle CPU performansı sınırlıdır",
    "CPUMaximumPowerLimitation": "Çekirdek, maksimum güç tüketimi sınırı tarafından sınırlanmaktadır",
    "CPUCircuitPowerLimitation": "CPU Paketi/Ring Bus güç limitine ulaştı",
    "CPUElectricalDesignLimitation": "Elektrik tasarım sınırlamalarını tetikle (ICCmax akım duvarı, PL4 zirve güç duvarı, SVID voltaj sınırlandırması vb. dahil)",
    "CPULongTermPowerLimitation": "CPU'nun Uzun Vadeli Güç Sınırı Aşıldı",
    "CPULongTermPowerinstantaneous": "CPU'nun anlık güç tüketimi limitine ulaştı",
    "CPUPowerLimitation": "CPU Turbo Frekansı Degradasyon Mekanizması, genellikle BIOS veya özel yazılımlar tarafından kısıtlanır",
    "CPUPowerWallLimitation": "CPU Güç Sınırı",
    "CPUcurrentwalllimit": "CPU Mevcut Duvar Sınırı",
    "AiAgent": "GamePP Aracı (AI Aracı)",
    "AgentDesc": "Anasayfaya geri dön",
    "fnBeta": "Bu özellik şu anda davetiye temelli test aşamasındadır. GamePP hesabınız henüz test erişimine sahip değildir。",
    "getAIReport": "AI raporu elde et",
    "waitingAi": "Raporun oluşturulması tamamlanana kadar bekleniyor",
    "no15mins": "Oyun süresi 15 dakikadan azdır, geçerli bir AI raporu oluşturulamaz。",
    "timeout": "Sunucu isteği zaman aşımına uğradı",
    "agentId": "Ajan ID'si:",
    "reDo": "Raporu yeniden oluştur",
    "text2": "GamePP Ajanı: Çevrimiçi AI Analiz Raporu, aşağıdaki içerik AI tarafından oluşturulmuş olup sadece bilgilendirme amaçlıdır.",
    "amdAiagentTitle": "GamePP Ajan: AMD Ryzen AI Analiz Raporu, aşağıdaki içerik AI tarafından üretilmiştir ve sadece referans amaçlıdır.",
    "noCurData": "Mevcut veriler yok",
    "dataScreening": "Veri Filtreleme",
    "dataScreeningDescription": "Bu özellik, harita yüklemesi, lobi bekletme gibi etkisiz oyun zaman aralıklarının veri istatistiklerini dışlamayı amaçlamaktadır. 0, dışlama yapmayacağını gösterir.",
    "excessivelyHighParameter": "Aşırı parametreler",
    "tooLowParameter": "Aşırı düşük parametreler",
    "theMaximumValueIs": "Maksimum değer",
    "theMinimumValueIs": "Minimum değerdir",
    "exclude": "Hariç tut",
    "dataStatisticsAtThatTime": "Bu andaki veri istatistikleri",
    "itHasBeenGenerated": "Oluşturma tamamlandı,",
    "clickToView": "Tıklayarak görüntüle",
    "onlineAnalysis": "Çevrimiçi Analiz",
    "amdRyzenAILocalAnalysis": "AMD Ryzen AI Yerel Analiz",
    "useTerms": "Kullanım Koşulları:",
    "term1": "1. Ryzen AI Max Prosesörü veya Ryzen Al 300 Serisi Prosesörü",
    "term2": "2.AMD NPU Sürücü Sürümü",
    "term3": "3. Entegre grafik etkinleştirmek",
    "conformsTo": "Uygun",
    "notInLineWith": "Geçersiz",
    "theVersionIsTooLow": "Sürüm çok eski",
    "canNotUseAmdNpu": "Yapılandırmanız gereksinimleri karşılamamaktadır. AMD NPU varlık analizi kullanılamamaktadır。",
    "unusable": "Kullanılamıyor",
    "downloadTheFile": "Dosya indir",
    "downloadSource": "İndirme kaynağı:",
    "fileSize": "Dosya boyutu: yaklaşık 8,34 GB",
    "cancelDownload": "İndirme iptal et",
    "filePath": "Dosya yolu",
    "generateAReport": "Rapor oluştur",
    "fileMissing": "Dosya eksik. Yeniden indirme gerekli.",
    "downloading": "İndirme devam ediyor...",
    "theModelConfigurationLoadingFailed": "Model yapılandırması yüklenemedi",
    "theModelDirectoryDoesNotExist": "Model dizini mevcut değil",
    "thereIsAMistakeInReasoning": "Tümdengelim hatası",
    "theInputExceedsTheModelLimit": "Giriş model limitini aşıyor",
    "selectModelNotSupport": "Seçilen indirme modeli desteklenmemektedir",
    "delDirFail": "Mevcut model dizinini silme başarısız oldu",
    "failedCreateModelDir": "Model dizini oluşturma başarısız oldu",
    "modelNotBeenFullyDownload": "Model tamamen indirilmedi",
    "agentIsThinking": "Jiajia Ajandı düşünüyor",
    "reasoningModelFile": "Çıkarım modeli dosyası",
    "modelReasoningTool": "Model Çıkarım Aracı"
  },
  "SelectSensor": {
    "DefaultSensor": "Varsayılan Algılayıcı",
    "Change": "Değiştir",
    "FanSpeed": "Fan Hızı",
    "MainGraphicsCard": "Birincil GPU",
    "SetAsMainGraphicsCard": "Birincil GPU olarak ayarla",
    "GPUTemperature": "GPU Sıcaklığı",
    "GPUHeatPower": "GPU Isı Gücü",
    "GPUTemperatureD3D": "GPU D3D Kullanımı",
    "GPUTemperatureTOTAL": "GPU Toplam Kullanımı",
    "GPUTemperatureCore": "GPU Çekirdek Isı Noktası Sıcaklığı",
    "MotherboardTemperature": "Anakart sıcaklığı",
    "MyAttention": "Favorilerim",
    "All": "Tüm",
    "Unit": "Birim:",
    "NoAttention": "Takip edilmeyen sensörler",
    "AttentionSensor": "İzlenen Sensörler (Beta)",
    "GoToAttention": "Focus Moduna Geç",
    "CancelAttention": "Takibi Bırak",
    "noThisSensor": "Bir sensör yok",
    "deviceAbout": "Çevre Birimleri İlgili",
    "deviceBattery": "Periferik Pil",
    "testFunction": "Test Özelliği",
    "mouseEventRate": "Ankete alma hızı",
    "relatedWithinTheGame": "Oyunla İlgili",
    "winAbout": "Sistem",
    "trackDevicesBattery": "Çevresel Cihaz Pil Seviyesini Takip Et",
    "ingameRealtimeMouseRate": "Oyun sırasında gerçek zamanlı farenin sorgulama hızı",
    "notfoundDevice": "Desteklenen cihaz bulunamadı",
    "deviceBatteryNeedMythcool": "Pil durumu gösterimi destekleyen cihaz listesi: (Myth.Cool ile kullanılması gerekir)",
    "vkm1mouse": "Valkyrie M1 Fare",
    "vkm2mouse": "Valkyrie M2 Fare",
    "vk99keyboard": "Valkyrie 99 Manyetik Mil Klavyesi",
    "logitechProWireless": "Logitech Pro Wireless",
    "logitechProXSUPERLIGHT": "Logitech PRO X SUPERLIGHT",
    "logitechProXSUPERLIGHT2": "Logitech PRO",
    "logitechPro2LIGHTSPEED": "Logitech PRO 2 LIGHTSPEED",
    "steelSeriesArctisGameBuds": "SteelSeries Arctis GameBuds",
    "steelSeriesArctis7PPlus": "SteelSeries Arctis 7P Plus",
    "razerV3": "Razer Valkyrie V3 Profesyonel Sürüm",
    "razerV2": "Razer Viper V2 Profesyonel Sürüm",
    "wireless": "Kablosuz",
    "logitechNeedGhub": "Logitech cihaz modelini alamazsa GHUB'ı indirmeniz gerekir",
    "chargingInProgress": "Şarj",
    "inHibernation": "Uyku modunda"
  },
  "video": {
    "videoRecord": "Video Kaydı",
    "recordVideo": "Kaydedilen Video",
    "scheme": "Profil",
    "suggestScheme": "Tavsiye Edilen Plan",
    "text1": "Bu yapılandırma ile 1 dakikalık video boyutu yaklaşık olarak",
    "text2": "Bu özellik ek sistem kaynakları kullanır",
    "low": "Düşük",
    "mid": "Ana Sayfa",
    "high": "Yüksek",
    "1080p": "Yerli",
    "RecordingFPS": "FPS Kaydet",
    "bitRate": "Video Bit Hızı",
    "videoResolution": "Video Çözünürlüğü",
    "startStopRecord": "Kaydı Başlat/Durdur",
    "instantReplay": "Anında Yeniden Oynatma",
    "instantReplayTime": "Anında Oynatma Süresi",
    "showIngame": "Oyun İçindeki Kontrol Panelini Aç",
    "CaptureMode": "Yakalama Yöntemi",
    "gameWindow": "Oyun Penceresi",
    "desktopWindow": "Masaüstü Penceresi",
    "fileSavePath": "Dosya depolama yolu",
    "selectVideoSavePath": "Kayıt Kaydetme Yolunu Seçin",
    "diskFreeSpace": "Sabit diskte boş alan:",
    "edit": "Değiştir",
    "open": "Aç",
    "displayMouse": "Fare imleci göster",
    "recordMicrophone": "Mikrofonu Kaydet",
    "gameGraphics": "Orijinal Oyun Görünümü"
  },
  "Setting": {
    "common": "Genel",
    "personal": "Kişiselleştirme",
    "messageNotification": "Bildirimler",
    "sensorReading": "Sensör Okumaları",
    "OLEDscreen": "OLED Yanık Önleme",
    "performanceStatistics": "Performans İstatistikleri",
    "shortcut": "Klavye Kısayolu",
    "ingameSetting": "Oyun Ayarlarını Kaydet",
    "other": "Diğer",
    "otherSettings": "Diğer Ayarlar",
    "GeneralSetting": "Genel Ayarlar",
    "softwareVersion": "Yazılım Sürümü",
    "checkForUpdates": "Güncellemeleri kontrol et",
    "updateNow": "Şimdi Güncelle",
    "currentVersion": "Mevcut Sürüm",
    "latestVersion": "En son sürüm",
    "isLatestVersion": "Mevcut sürüm zaten en son sürümdür.",
    "functionModuleUpdate": "Fonksiyonel Modül Güncellemesi",
    "alwaysUpdateModules": "Yüklü tüm işlevsel modüller güncel tutulmalıdır",
    "lang": "Dil",
    "bootstrap": "Otomatik Başlat",
    "powerOn_m1": "Başlangıç",
    "powerOn_m2": "Saniye sonra otomatik başlatma",
    "defaultDelay": "Varsayılan: 40 saniye",
    "followSystemScale": "Sistem Ölçeklendirmesini Takip Et",
    "privacySettings": "Gizlilik Ayarları",
    "JoinGamePPPlan": "GamePP Kullanıcı Deneyimi Geliştirme Programına Katılın",
    "personalizedSetting": "Kişiselleştirme",
    "restoreDefault": "Varsayılan ayarları geri yükle",
    "color": "Renk",
    "picture": "Görüntü",
    "video": "Video",
    "browse": "Tarayıcı",
    "clear": "Temizle",
    "mp4VideoOrPNGImagesCanBeUploaded": "MP4 videoları veya PNG görüntüleri yükleyin",
    "transparency": "Şeffaflık",
    "backgroundColor": "Arka Plan Rengi",
    "textFont": "Ana Metin Yazı Tipi",
    "message": "Mesaj",
    "enableInGameNotifications": "Oyun içindeki bildirimleri etkinleştir",
    "messagePosition": "Oyunda Gösterim Konumu",
    "leftTop": "Sol üst köşe",
    "leftCenter": "Sol Orta",
    "leftBottom": "Sol Alt Köşe",
    "rightTop": "üst sağ köşe",
    "rightCenter": "Sağ Merkez",
    "rightBottom": "sağ alt köşe",
    "noticeContent": "Bildirim İçeriği",
    "gameInjection": "Oyun Enjeksiyonu",
    "ingameShow": "Oyun içinde göster",
    "inGameMonitoring": "Oyun İçinde İzleme",
    "gameFilter": "Oyun Filtresi",
    "start": "Başlat",
    "endMarkStatistics": "Son İşaretleyici İstatistikleri",
    "readHwinfoFail": "HWINFO donanım bilgileri alınamadı!",
    "dataSaveDesktop": "Veriler kopyalama hattına ve masaüstü dosyasına kaydedildi.",
    "TheSensorCacheCleared": "Duyarlılık verileri temizlendi",
    "defaultSensor": "Varsayılan Sensör",
    "setSensor": "Algılayıcı Seç",
    "refreshTime": "Veri Yenileme Zamanı",
    "recommend": "Varsayılan",
    "sensorMsg": "Zaman aralığı ne kadar kısa olursa, performans tüketimi o kadar yüksektir. Lütfen dikkatli seçin。",
    "exportData": "Veri Dışa Aktar",
    "exportHwData": "Donanım bilgisi verilerini dışa aktar",
    "sensorError": "Duyarlılık Okuma Anormal",
    "clearCache": "Önbelleği Temizle",
    "littleTips": "İpucu: Oyun başlatıldıktan 2 dakika sonra performans raporu oluşturulacak",
    "disableAutoShow": "Performans istatistikleri penceresinin otomatik açılmasını devre dışı bırak",
    "AutoClosePopUpWindow_m1": "Performans istatistikleri penceresi belirlenen süre sonunda otomatik olarak kapanır:",
    "AutoClosePopUpWindow_m2": "saniye",
    "abnormalShutdownReport": "Anormal Kapanma Raporu",
    "showWeaAndAddress": "Hava durumu ve konum bilgilerini göster",
    "autoScreenShots": "İşaretlenirken oyun ekranını otomatik olarak yakala",
    "keepRecent": "Son kayıtların korunacağı miktar:",
    "noLimit": "Sınırsız",
    "enableInGameSettingsSaving": "Oyun içi ayarların kaydedilmesini etkinleştir",
    "debugMode": "Hata Ayıklama Modu",
    "enableDisableDebugMode": "Hata ayıklama modunu etkinleştir/devre dışı bırak",
    "audioCompatibilityMode": "Ses Uyum Modu",
    "quickClose": "Hızlı kapatma",
    "closeTheGameQuickly": "Oyun sürecini hızlıca kapat",
    "cancel": "İptal",
    "confirm": "Onayla",
    "MoveInterval_m1": "Masaüstü ve oyun içi izleme biraz hareket edecek:",
    "MoveInterval_m2": "dakika",
    "text3": "Oyun kapatıldıktan sonra performans raporu penceresi açılmayacak, yalnızca geçmiş kayıtlar korunur",
    "text5": "Beklenmedik bir kapatma sonrasında otomatik olarak bir rapor oluşturulacak. Bu özelliği etkinleştirmek ek sistem kaynaklarını tüketecektir。",
    "text6": "Kısayol fonksiyonlarını kullanmak, diğer oyun kısayollarıyla çakışabilir. Lütfen dikkatlice ayarlayın。",
    "text7": "Klavye kısayolunu 'Hiçbiri' olarak ayarlayın, lütfen Backspace tuşunu kullanın",
    "text8": "Oyun oynarken işlem isimlerine dayanarak filtreler, oyun içi izleme ve diğer işlevsel durumları koru",
    "text9": "Etkinleştirilirse çalışma zamanı verisi sürekli olarak kaydedilecektir; devre dışı bırakma günlük dosyalarını siler (Kapalı tutulması önerilir)",
    "text10": "Etkinleştirildikten sonra ana kart sensörleri GamePP tarafından neden olan ses sorunlarını çözmek için erişilemez hale gelecektir。",
    "text11": "Mevcut oyunu hızlıca kapatmak için Alt+F4 tuşuna ard arda iki kez basın",
    "text12": "Devam etmek istiyor musunuz? Bu mod GamePP'nin yeniden başlatılmasını gerektirir.",
    "openMainUI": "Uygulamayı Göster",
    "setting": "Ayarlar",
    "feedback": "Geribildirim",
    "help": "Yardım",
    "sensorReadingSetting": "Bilgisayar Sensör Okuma Ayarları",
    "searchlanguage": "Arama Dili"
  },
  "GamePlusOne": {
    "year": "Yıl",
    "month": "Ay",
    "day": "gün",
    "success": "Başarı",
    "fail": "Başarısız",
    "will": "Mevcut",
    "missedGame": "Oyunu alma fırsatını kaçırdınız",
    "text1": "Miktar, yak. ￥",
    "text2": "Toplam Talep Edilen Oyunlar",
    "text3": "Sürüm",
    "gamevalue": "Oyun Değeri",
    "gamevalue1": "Talep",
    "total": "Toplam Talep Edilen",
    "text4": "Oyunlar, toplam kaydedildi",
    "text6": "Ürün, Değer",
    "Platformaccountmanagement": "Platform Hesabı Yönetimi",
    "Missed1": "İtiraf edilmemiş",
    "Received2": "（Başarıyla alındı）",
    "Receivedsoon2": "(Şu anda kullanılabilir)",
    "Receivedsoon": "Kullanılabilir",
    "Missed": "Eksik Toplama",
    "Received": "Başarıyla alındı",
    "Getaccount": "Hesap talep et",
    "Worth": "Değer",
    "Auto": "Otomatik",
    "Manual": "El",
    "Pleasechoose": "Lütfen bir oyun seçin",
    "Receive": "Şimdi talep edin",
    "Selected": "Seçildi",
    "text5": "Oyunlar",
    "Automatic": "Otomatik talep ediliyor...",
    "Collecting": "Talep Ediliyor...",
    "ReceiveTimes": "Aylık Talep Sayısı",
    "Thefirst": "#",
    "Week": "Hafta",
    "weekstotal": "Toplam 53 hafta",
    "Return": "Anasayfa",
    "Solutionto": "Hesap Bağlantısı Başarısızlık Çözümü",
    "accounts": "Bağlı Hesap Sayısı",
    "Addaccount": "Hesap Ekle",
    "Clearcache": "Önbelleği Temizle",
    "Bindtime": "Bağlama zamanı",
    "Status": "Durum",
    "Normal": "Normal Mod",
    "Invalid": "Geçersiz",
    "text7": "Oyunlar, toplam olarak size kaydedildi",
    "Yuan": "Yuan",
    "untie": "Bağlantıyı Kes",
    "disable": "Devre Dışı Bırak",
    "enable": "Etkinleştir",
    "gamePlatform": "Oyun platformu",
    "goStorePage": "Mağaza Sayfasına Git",
    "receiveEnd": "Son Tarihten Sonra",
    "loginPlatformAccount": "Oturum açılan platform hesabı",
    "waitReceive": "Bekleyen Talep",
    "receiveSuccess": "Başarı",
    "accountInvalid": "Hesap Süresi Doldu",
    "alreadyOwn": "Zaten Sahip Olundu",
    "networkError": "Ağ Anomalisi",
    "noGame": "Oyun Çekirdeği Yok",
    "manualReceiveInterrupt": "Elde Edim Manuelsi Kesilmesi",
    "receiving": "Talep ediliyor",
    "agree": "GamePP ücretsiz alma programına katılmayı kabul ediyorum.",
    "again": "Yeniden al"
  },
  "shutdownTimer": {
    "timedShutdown": "Programlanmış Kapatma",
    "currentTime": "Mevcut saat:",
    "setCountdown": "Sayım Ayarla",
    "shutdownInSeconds": "X saniye sonra kapat",
    "shutdownIn": "Kapatma Sonrası",
    "goingToBe": "olacak",
    "executionPlan": "İşlem Planı",
    "startTheClock": "Zamanlayıcıyı Başlat",
    "stopTheClock": "Planı İptal Et",
    "isShuttingDown": "Planlı kapanma planı yürütülüyor:",
    "noplan": "Mevcut kapatma planı yok",
    "hour": "Saat",
    "min": "Dakika",
    "sec": "Saniye",
    "ms": "Milisaniye",
    "year": "Yıl",
    "month": "Ay",
    "day": "Gün",
    "hours": "Saat"
  },
  "screenshotpage": {
    "screenshot": "Ekran görüntüsü",
    "screenshotFormat": "Oyun ekran görüntülerinin yakalanması için özel olarak tasarlanmıştır, JPG/PNG/BMP formatlarında kaydetmeyi destekler, oyun ekranlarını hızlı bir şekilde yakalayabilir ve yüksek çözünürlüklü, kalite kaybı olmadan çıktı sağlar",
    "Turnon": "Otomatik ekran görüntüsü etkinleştir, her",
    "seconds": "Saniye",
    "takeScreenshot": "Otomatik ekran görüntüsü yap",
    "screenshotSettings": "Bu ayar, oyun içinde etkinleştirilirse geçersizdir",
    "saveGameFilterAndMonitoring": "'Oyun Filtresi' ve 'Oyun İçinde Takip' etkilerini ekran görüntüsüne kaydet",
    "disableScreenshotSound": "Ekran görüntüsü ses bildirimini devre dışı bırak",
    "imageFormat": "Görüntü Formatı",
    "recommended": "Öner",
    "viewingdetails": "Görüntü kalite detaylarını korur, boyut orta, detayları görüntülemek için uygundur",
    "saveSpace": "Ayarlanabilir görüntü kalitesi, minimum dosya boyutu, alan tasarrufu",
    "ultraQuality": "Ultra net, sıkıştırılmamış görseller büyük dosya boyutuyla. Yüksek kaliteli oyun kayıtlarını öncelikli tutan oyuncular için önerilir.",
    "fileSavePath": "Dosya Kaydetme Yolu",
    "hardDiskSpace": "Kullanılabilir disk alanı:",
    "minutes": "Dakika"
  },
  "DesktopMonitoring": {
    "desktopMonitoring": "Masaüstü İzleme",
    "SomeSensors": "Bazı sensörler izleme için önerilmiştir. Silinebilir veya eklenebilir",
    "AddComponent": "Yeni bir bileşen ekle",
    "Type": "Tür",
    "Remarks": "Not",
    "AssociatedSensor": "Bir sensör bağla",
    "Operation": "İşlem",
    "Return": "Geri",
    "TimeSelection": "Zaman seçimi:",
    "Format": "Biçim:",
    "Rule": "Kural：",
    "Coordinate": "Koordinatlar:",
    "CustomTextContent": "Özel Metin İçeriği:",
    "SystemTime": "Sistem Saati",
    "China": "Çin",
    "Britain": "Birleşik Krallık",
    "America": "Amerika Birleşik Devletleri",
    "Russia": "Rusya",
    "France": "Fransa",
    "DateAndTime": "Tarih ve saat",
    "Time": "Zaman",
    "Date": "Tarih",
    "Week": "Haftanın günü",
    "DateAndTimeAndWeek": "Tarih+Saat+Haftanın Günü",
    "TimeAndWeek": "Saat+Haftanın Günü",
    "Hour12": "12 Saat Formatı",
    "Hour24": "24 saat formatı",
    "SelectSensor": "Bir sensör seçin:",
    "AssociatedSensor1": "Birleştirici Sensör:",
    "SensorUnit": "Sensör birimi：",
    "Second": "Saniye:",
    "Corner": "Yuvarlatılmış köşeler:",
    "BackgroundColor": "Arka plan rengi:",
    "ProgressColor": "İlerleme Rengi:",
    "Font": "Yazı tipi：",
    "SelectFont": "Yazı tipi seç",
    "FontSize": "Yazı Tipi Boyutu:",
    "Color": "Renk：",
    "Style": "Stil:",
    "Bold": "Kalın",
    "Italic": "İtalik",
    "Shadow": "Gölge",
    "ShadowPosition": "Gölge Konumu：",
    "ShadowEffect": "Gölge Etkileri：",
    "Blur": "Bulanıklık",
    "ShadowColor": "Gölge Rengi：",
    "SelectFromLocalFiles": "Yerel dosyaları seçin:",
    "UploadImageVideo": "Resim/Videoları Yükle",
    "UploadSVGFile": "SVG dosyasını yükle",
    "Width": "Genişlik:",
    "Height": "Yüksek: ",
    "Effect": "Etki:",
    "Rotation": "Döndürme:",
    "WhenTheSensorValue": "Sensor değeri daha büyükse",
    "conditions": "Şartlar karşılanmadığında (dönüştürme yapılmaz, sensör değeri 0 olduğunda)",
    "Clockwise": "Saat yönünde",
    "Counterclockwise": "Aktif yön: Saat yönünün tersi",
    "QuickRotation": "Hızlı döndürme",
    "SlowRotation": "Yavaş Döndürme",
    "StopRotation": "Döndürmeyi durdur",
    "StrokeColor": "Kenar rengi：",
    "Path": "Yol",
    "Color1": "Renk",
    "ChangeColor": "Renk değiştir",
    "When": "Ne zaman",
    "SensorValue": "Algılayıcı değeri daha büyük veya eşit",
    "SensorValue1": "Bir sensör değeri küçük veya eşitse",
    "SensorValue2": "Sensör değeri eşittir",
    "MonitoringSettings": "Gözetim Ayarları",
    "RestoreDefault": "Varsayılana Döndür",
    "Monitor": "Monitör",
    "AreaSize": "Bölge boyutu",
    "Background": "Arka plan",
    "ImageVideo": "Görseller/Video",
    "PureColor": "Tek renk",
    "Select": "Seç",
    "ImageVideoDisplayMode": "Görüntü/Video Görünüm Modu",
    "Transparency": "Şeffaflık",
    "DisplayPosition": "Konumu Göster",
    "Stretch": "Germe",
    "Fill": "Doldur",
    "Adapt": "Uyumlamak",
    "SelectThePosition": "Kareye tıklayarak konumu hızlıca seçebilirsiniz",
    "CurrentPosition": "Mevcut konum:",
    "DragLock": "Sürüklemeyi Kilit",
    "LockMonitoringPosition": "Gözetim pozisyonu kilitlendi (Kilitlendikten sonra gözetim sürüklenemez)",
    "Unlockinterior": "İç öğelerin sürükleme izni ver",
    "Font1": "Yazı Tipi",
    "GameSettings": "Oyun Ayarları",
    "CloseDesktopMonitor": "Oyun çalışırken masaüstü izlemeyi otomatik olarak devre dışı bırakın.",
    "OLED": "OLED Ekran Yandırma Koruyucu",
    "Display": "Göster",
    "PleaseEnterContent": "İçerik girin",
    "NextStep": "İleri",
    "Add": "Ekle",
    "StylesForYou": "Bazı izleme stilleri önerdik. Seçip uygulayabilirsiniz. Gelecekte daha fazla stil eklenir.",
    "EditPlan": "Profil Düzenle",
    "MonitoringStylePlan": "Gözetim Stili Planı",
    "AddDesktopMonitoring": "Masaüstü izleme ekle",
    "TextLabel": "Metin Etiketi",
    "ImageVideo1": "Resimler, Videolar",
    "SensorGraphics": "Sensor Grafiği",
    "SensorData": "Sensör Verileri",
    "CustomText": "Özel Metin",
    "DateTime": "Tarih ve saat",
    "Image": "Görüntü",
    "Video": "Video",
    "SVG": "SVG",
    "ProgressBar": "İlerleme çubuğu",
    "Graphics": "Grafik",
    "UploadImage": "Resim yükle",
    "UploadVideo": "Video yükle",
    "RealTimeMonitoring": "CPU/GPU sıcaklıkları ve kullanımını anlık olarak izleyin, sürükleyip bırakarak düzeni özgürce ayarlayın, kişiselleştirilmiş stil ayarları ile performansı ve masaüstü estetiğini yönetin",
    "Chart": "Grafik",
    "Zigzagcolor": "Çizgi rengi (Başlangıç)",
    "Zigzagcolor1": "Çizgi Rengi (Bitiş Noktası)",
    "Zigzagcolor2": "Çizgi Grafiği Alanı Rengi (Başlangıç)",
    "Zigzagcolor3": "Çizgi Grafiği Alan Rengi (Bitiş)",
    "CustomMonitoring": "Özelleştirilmiş izleme"
  }
}
//messageEnd 
 export default tr 