
import { createApp } from 'vue'
import { createPinia } from 'pinia'

import 'element-plus/dist/index.css'
import './Game_Setting_RTL.scss'
// import './assets/theme.scss'
// import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import '../../assets/iconfont/iconfont.css'
import '../../assets/iconfont/iconfont.js'
import App from './setting.vue'

//国际化
import i18n from '../../assets/lang'
const pinia = createPinia()

const app = createApp(App)

app.use(createPinia())
// app.use(ElementPlus)
app.use(pinia)
app.use(i18n)
app.mount('#app')
