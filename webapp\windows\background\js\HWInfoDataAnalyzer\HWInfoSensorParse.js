if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
    // 在 Node.js 环境下，使用 module.exports 导出
    module.exports = {
          ProcessHWMonitorSensor
    };
}

function CreateCPUSensor () {
    var data = {}
    data.Name = null
    data.Load = 0
    data.LoadP = 0
    data.LoadE = 0
    data.TotalUtility = 0// 任务管理器报告利用率
    data.TotalUsage = 0 // 整个CPU封装的平均利用率
    data.TotalUsageP = 0
    data.TotalUsageE = 0
    data.TotalUtilityP = 0
    data.TotalUtilityE = 0
    data.LoadArr = []
    data.LoadPEArr = [{ TotalUsageP: 0, TotalUsageE: 0 }, { TotalUtilityP: 0, TotalUtilityE: 0 }]
    data.Temp = []
    data.Temps = []
    data.Clock = 0
    data.ClockP = 0
    data.ClockE = 0
    data.Voltage = 0
    data.VoltageP = 0
    data.VoltageE = 0
    data.VoltageArr = []
    data.Ratio = 0
    data.BusClock = 0
    data.TDP = []
    data.Thread = {}
    data.EffectiveClock = {}
    data.Core = { Clock: {}, Ratio: {}, VID: {}, Count: 0, Load: {} }
    data.PLR = [] // PERFORMANCE LIMIT REASONS性能限制
    data.Thermal = 0 // AMD温度墙实际百分比

    data.NPUClock = null
    data.NPUUsage = null
    return data
}


  function CreateGPUSensor () {
    var data = {}
    data.Name = null
    data.Type = null
    data.Load = []
    data.Temp = []
    data.Temps = []
    data.Clock = 0
    data.Voltage = 0
    data.TDP = []
    data.Fan = []
    data.Fans = []
    data.VRAM = 0
    data.VRAMUsage = 0
    data.VRAMUsageSize = 0
    data.VRAMClock = 0
    data.VRAMTemp = 0
    data.HotSpotTemp = 0
    data.PLR = [] // PERFORMANCE LIMIT性能限制
    data.CoreThrottling = [] // 核心降频
    data.VRAMThrottling = [] // 显存降频
    data.ThermalHotspot = 0 // HOTSPOT THERMAL性能限制
    data.ThermalMemory = 0 // HOTSPOT THERMAL性能限制
    data.ReliabilityVoltage = 0
    data.MaxOperatingVoltage = 0
    data.VRGFXThermal = 0
    data.VRSOCThermal = 0
    return data
}

function CreateDRAMSensor () {
    var data = {}
    data.Clock = 0
    data.CR = 0
    data.GearMode = 0
    data.PhysicalAvailable = 0
    data.PhysicalLoad = 0
    data.PhysicalUsed = 0
    data.TCAS = 0
    data.TRAS = 0
    data.TRCD = 0
    data.TRP = 0
    data.Temp = []
    data.VirtualAvailable = 0
    data.VirtualCommitted = 0
    data.VirtuallLoad = 0
    data.Voltage = 0
    return data
}

function CreateNetworkSensor () {
    var data = {}
    data.DLRate = 0
    data.ULRate = 0
    data.TotalDL = 0
    data.TotalUL = 0
    return data
}

function CreateDriveSensor () {
    var data = {}
    data.Temp = []
    data.ReadRate = 0
    data.WriteRate = 0
    data.ReadActivity = 0
    data.WriteActivity = 0
    data.TotalActivity = 0
    return data
}

function sum (arr) {
    return eval(arr.join('+'))
}

function ProcessHWMonitorSensor (sensorStr) {
    if (sensorStr === '') return
    var CPU = {}
    var GPU = {}
    var NETWORK = CreateNetworkSensor()
    var DRIVE = {}
    var DRIVES = []
    var DRAM = CreateDRAMSensor()
    var MOTHERBOARD = {
        Temp: [], Temps: [], Temperatures: [], Fan: [], Vcore: null, Fan1: 0, Fan2: 0, Fan3: 0, Fan4: 0, Fan5: 0, Fan6: 0
    }
    var WHEA_Total_Errors = 0, Battery = { ChargeLevel: 0, Voltage: 0, WearLevel: 0 }

    var SensorInfos = JSON.parse(sensorStr);
    var AllMainKeys = Object.keys(SensorInfos)

    // console.log(SensorInfos);

    // 判断是否有核显
    var GPUIDX_MIN = 1
    for (let i = 0; i < AllMainKeys.length; i++) {
        const mainKey = AllMainKeys[i]
        var UpperMainKey = mainKey.toUpperCase()
        if (UpperMainKey.match(new RegExp('^GPU.*$'))) {
            // GPU
            const GPUIDX = parseInt(UpperMainKey.substring(strIdx, strIdx + 1))
            if (GPUIDX < GPUIDX_MIN) {
                GPUIDX_MIN = GPUIDX
            }
        }
    }

    // 循环数据
    for (let i = 0; i < AllMainKeys.length; i++) {
        var mainKey = AllMainKeys[i]
        var upperMainKey = mainKey.toUpperCase()
        var itemValue = SensorInfos[mainKey]
        if (upperMainKey.match(new RegExp('^CPU.*$'))) {
            // CPU
            var strIdx = upperMainKey.indexOf('#') + 1
            var CPUIDX = upperMainKey.substring(strIdx, strIdx + 1)
            if (GPUIDX_MIN === 1) {
                // 有核显
                const GPUIDX = 0
                let GPUData
                if (CPU.hasOwnProperty(GPUIDX)) {
                    // 取出来
                    GPUData = GPU[GPUIDX]
                } else {
                    // 创建新的
                    GPUData = CreateGPUSensor()
                }
                const IsArrBool1 = Array.isArray(itemValue)
                if (itemValue && IsArrBool1) {
                    GPUData.Type = 'Integrated'
                    itemValue.forEach(function (itemDetail) {
                        var infoKey = Object.keys(itemDetail)[0]
                        var itemInfo = itemDetail[infoKey]
                        var infoType = itemInfo.type.toUpperCase()
                        var infoValue = itemInfo.value
                        var upperInfoKey = infoKey.toUpperCase()
                        if (upperInfoKey.match(new RegExp('^GPU.*$'))) {
                            if (infoType === 'TEMPERATURE') {
                                // 温度
                                if (upperInfoKey.indexOf('GT CORES') !== -1) {
                                    const temp = { 'GT CORES': Number(parseFloat(infoValue).toFixed(0)) }
                                    GPUData.Temp.push(temp)
                                }
                            } else if (infoType === 'POWER') {
                                // TDP
                                const tdp = {}
                                tdp[upperInfoKey] = Number(parseFloat(infoValue).toFixed(0))
                                GPUData.TDP.push(tdp)
                            } else if (infoType === 'USAGE') {
                                // Usage
                                if (upperInfoKey.indexOf('D3D USAGE') !== -1) {
                                    // D3D USAGE
                                    GPUData.Load.push({ D3D: Number(parseFloat(infoValue).toFixed(0)) })
                                }
                                if (upperInfoKey.indexOf('GT USAGE') !== -1) {
                                    // GT USAGE TOTAL
                                    GPUData.Load.push({ TOTAL: Number(parseFloat(infoValue).toFixed(0)) })
                                }
                            } else if (infoType === 'CLOCK') {
                                // 频率
                                if (upperInfoKey.indexOf('GPU CLOCK') !== -1 && upperInfoKey.indexOf('EFFECTIVE') === -1) {
                                    GPUData.Clock = Number(parseFloat(infoValue).toFixed(0))
                                }
                            } else if (infoType === 'OTHER') {
                                // 显存占用
                                if (upperInfoKey.indexOf('MEMORY DYNAMIC') !== -1) {
                                    GPUData.VRAM = Number(parseFloat(infoValue).toFixed(0))
                                }
                            } else if (infoType === 'VOLTAGE') {
                                // 核显电压
                                if (upperInfoKey.indexOf('IGPU VID') !== -1) {
                                    GPUData.Voltage = parseFloat(infoValue).toFixed(3)
                                }
                            }
                        } else if (upperInfoKey.indexOf('GT') !== -1) {
                            if (infoType === 'TEMPERATURE') {
                                // 温度
                                if (upperInfoKey.indexOf('GT CORES') !== -1) {
                                    const temp = { 'GT CORES': Number(parseFloat(infoValue).toFixed(0)) }
                                    GPUData.Temp.push(temp)
                                }
                            } else if (infoType === 'POWER') {
                                // TDP
                                if (upperInfoKey.indexOf('POWER') !== -1) {
                                    if (parseFloat(infoValue) !== 0) {
                                        const tdp = { 'GT TDP': Number(parseFloat(infoValue).toFixed(0)) }
                                        GPUData.TDP.push(tdp)
                                    }
                                }
                            }
                        }
                    })
                }
                GPU[GPUIDX] = GPUData
            }

            var CPUData
            if (CPU.hasOwnProperty(CPUIDX)) {
                // 取出来
                CPUData = CPU[CPUIDX]
            } else {
                // 创建新的
                CPUData = CreateCPUSensor()
            }
            const IsArrBool1 = Array.isArray(itemValue)
            if (itemValue && IsArrBool1) {
                const CoreClocks = []
                const PCoreClocks = []
                const ECoreClocks = []
                const CoreRatios = []
                const PCoreRatios = []
                const ECoreRatios = []
                const CoreVoltages = []
                const PCoreVoltages = []
                const ECoreVoltages = []
                const CoreLoadP = []
                const CoreLoadE = []
                const CoreUtilityP = []
                const CoreUtilityE = []
                CPUData.Name = (mainKey.split(':')[1]).replace(/(^\s*)|(\s*$)/g, '')
                // console.log(upperMainKey)
                if (upperMainKey.includes('PERFORMANCE LIMIT REASONS')) {
                    let NeedPLR = ['IA: PROCHOT', 'IA: Thermal Event', 'IA: VR Thermal Alert', 'IA: VR TDC', 'IA: Electrical Design Point/Other (ICCmax,PL4,SVID,DDR RAPL)', 'IA: Package-Level RAPL/PBM PL1', 'IA: VmaxStress']
                    itemValue.forEach(function (itemDetail) {
                        let infoKey = Object.keys(itemDetail)[0]
                        let itemInfo = itemDetail[infoKey]
                        let infoValue = itemInfo.value
                        if (NeedPLR.indexOf(infoKey) !== -1) {
                            let CPUPLR_Obj = {}
                            CPUPLR_Obj[infoKey] = parseInt(infoValue)
                            CPUData['PLR'].push(CPUPLR_Obj)
                        }
                    })
                } else if (upperMainKey.includes('DTS')) {
                    itemValue.forEach(function (itemDetail) {
                        let infoKey = Object.keys(itemDetail)[0]
                        let itemInfo = itemDetail[infoKey]
                        let infoValue = itemInfo.value
                        if (infoKey.includes('Thermal Throttling') || infoKey === 'Package/Ring Critical Temperature' || infoKey === 'Package/Ring Thermal Throttling') {
                            let CPUDTS_Obj = {}
                            CPUDTS_Obj[infoKey] = parseInt(infoValue)
                            CPUData['PLR'].push(CPUDTS_Obj)
                        }
                    })
                } else if (upperMainKey.includes('ENHANCED')) {
                    let NeedENHANCED = ['Thermal Limit', 'CPU PPT Limit', 'CPU TDC Limit', 'CPU EDC Limit', 'Thermal Throttling (HTC)', 'Thermal Throttling (PROCHOT CPU)', 'Thermal Throttling (PROCHOT EXT)']
                    itemValue.forEach(function (itemDetail) {
                        let infoKey = Object.keys(itemDetail)[0]
                        let itemInfo = itemDetail[infoKey]
                        let infoValue = itemInfo.value
                        if (NeedENHANCED.indexOf(infoKey) !== -1) {
                            let CPUPLR_Obj = {}
                            CPUPLR_Obj[infoKey] = parseInt(infoValue)
                            CPUData['PLR'].push(CPUPLR_Obj)
                        }
                        if (infoKey === 'Thermal Limit') {
                            //AMD温度墙实际百分比
                            if (itemInfo.type === 'usage') {
                                CPUData['Thermal'] = parseInt(itemInfo.value)
                            }
                        }
                    })
                }

                itemValue.forEach(function (itemDetail) {
                    const infoKey = Object.keys(itemDetail)[0]
                    const itemInfo = itemDetail[infoKey]
                    const infoType = itemInfo.type.toUpperCase()
                    const infoValue = itemInfo.value
                    const upperInfoKey = infoKey.toUpperCase()
                    if (infoType === 'CLOCK') {
                        // 频率
                        if (
                          upperInfoKey.match(new RegExp('^CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]CLOCK[.\n]*')) ||
                          upperInfoKey.match(new RegExp('^P-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]CLOCK [(]PERF #1[)][.\n]*')) ||
                          upperInfoKey.match(new RegExp('^E-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]CLOCK [(]PERF #1[)][.\n]*')) ||
                          upperInfoKey.match(new RegExp('^P-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]CLOCK[.\n]*')) ||
                          upperInfoKey.match(new RegExp('^E-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]CLOCK[.\n]*'))
                        ) {
                            const clock = Number(parseFloat(infoValue).toFixed(0))
                            const infoKeyItems = upperInfoKey.split(' ')
                            CPUData.Core.Clock[infoKeyItems[1]] = clock
                            CoreClocks.push(clock)
                        }

                        if (upperInfoKey.match(new RegExp('^P-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]CLOCK [(]PERF #1[)][.\n]*')) || upperInfoKey.match(new RegExp('^P-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]CLOCK[.\n]*'))) {
                            const clock = Number(parseFloat(infoValue).toFixed(0))
                            const infoKeyItems = upperInfoKey.split(' ')
                            CPUData.Core.Clock[infoKeyItems[1]] = clock
                            PCoreClocks.push(clock)
                        }

                        if (upperInfoKey.match(new RegExp('^E-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]CLOCK [(]PERF #1[)][.\n]*')) || upperInfoKey.match(new RegExp('^E-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]CLOCK[.\n]*'))) {
                            const clock = Number(parseFloat(infoValue).toFixed(0))
                            const infoKeyItems = upperInfoKey.split(' ')
                            CPUData.Core.Clock[infoKeyItems[1]] = clock
                            ECoreClocks.push(clock)
                        }
                        // 频率
                        if (upperInfoKey === 'BUS CLOCK') {
                            CPUData.BusClock = (Number(infoValue))
                        }
                        //有效频率
                        if (
                          upperInfoKey.match(new RegExp('^CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]T[0-9][ \f\r\t\n]EFFECTIVE CLOCK$'))
                        ) {
                            // 超线程技术
                            const clock = Number(parseFloat(infoValue).toFixed(0))
                            const infoKeyItems = upperInfoKey.split(' ')
                            const EffectiveClock = CPUData.EffectiveClock
                            if (EffectiveClock.hasOwnProperty(infoKeyItems[1])) {
                                EffectiveClock[infoKeyItems[1]][infoKeyItems[2]] = clock
                            } else {
                                var Detail = {}
                                Detail[infoKeyItems[2]] = clock
                                CPUData.EffectiveClock[infoKeyItems[1]] = Detail
                            }
                        }

                        //NPU频率
                        if (upperInfoKey === 'IPU CLOCK' || upperInfoKey === 'NPU CLOCK') {
                            console.log(1)
                            CPUData.NPUClock = Number(parseFloat(infoValue).toFixed(0))
                        }
                    } else if (infoType === 'VOLTAGE') {
                        // 电压
                        if (
                          upperInfoKey.match(new RegExp('^CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]VID$')) ||
                          upperInfoKey.match(new RegExp('^P-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]VID$')) ||
                          upperInfoKey.match(new RegExp('^E-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]VID$'))) {
                            const vid = Number(parseFloat(infoValue).toFixed(3))
                            const infoKeyItems = upperInfoKey.split(' ')
                            CPUData.Core.VID[infoKeyItems[1]] = vid
                            CoreVoltages.push(vid)
                        }

                        if (upperInfoKey.match(new RegExp('^P-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]VID$'))) {
                            const vid = Number(parseFloat(infoValue).toFixed(3))
                            const infoKeyItems = upperInfoKey.split(' ')
                            CPUData.Core.VID[infoKeyItems[1]] = vid
                            PCoreVoltages.push(vid)
                        }

                        if (upperInfoKey.match(new RegExp('^E-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]VID$'))) {
                            const vid = Number(parseFloat(infoValue).toFixed(3))
                            const infoKeyItems = upperInfoKey.split(' ')
                            CPUData.Core.VID[infoKeyItems[1]] = vid
                            ECoreVoltages.push(Number(vid))
                        }
                    } else if (infoType === 'USAGE') {
                        // 占用
                        if (upperInfoKey === 'TOTAL CPU USAGE') {
                            const usage = Number(parseFloat(infoValue).toFixed(0))
                            CPUData.Load = usage
                            CPUData.TotalUsage = usage
                            CPUData.LoadArr.push({ TotalUsage: usage })
                        } else if (upperInfoKey === 'TOTAL CPU UTILITY') {
                            const usage = Number(parseFloat(infoValue).toFixed(0))
                            const usageV = usage <= 100 ? usage : 100
                            CPUData.TotalUtility = usageV
                            CPUData.LoadArr.push({ TotalUtility: usageV })
                        } else if (
                          upperInfoKey.match(new RegExp('^CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]T[0-9][ \f\r\t\n]USAGE$')) ||
                          upperInfoKey.match(new RegExp('^P-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]T[0-9][ \f\r\t\n]USAGE$')) ||
                          upperInfoKey.match(new RegExp('^E-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]T[0-9][ \f\r\t\n]USAGE$'))
                        ) {
                            // 超线程技术
                            const usage = Number(parseFloat(infoValue).toFixed(0))
                            const infoKeyItems = upperInfoKey.split(' ')
                            const Thread = CPUData.Thread
                            if (Thread.hasOwnProperty(infoKeyItems[1])) {
                                Thread[infoKeyItems[1]][infoKeyItems[2]] = usage
                            } else {
                                var Detail = {}
                                Detail[infoKeyItems[2]] = usage
                                CPUData.Thread[infoKeyItems[1]] = Detail
                            }

                            if (upperInfoKey.match(new RegExp('^P-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]T[0-9][ \f\r\t\n]USAGE$'))) {
                                CoreLoadP.push(usage)
                            }

                            if (upperInfoKey.match(new RegExp('^E-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]T[0-9][ \f\r\t\n]USAGE$'))) {
                                CoreLoadE.push(parseFloat(infoValue))
                            }
                        } else if (upperInfoKey.match(new RegExp('^P-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]T[0-9][ \f\r\t\n]UTILITY$'))) {
                            const usage = Number(parseFloat(infoValue).toFixed(0))
                            CoreUtilityP.push(usage)
                        } else if (upperInfoKey.match(new RegExp('^E-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]T[0-9][ \f\r\t\n]UTILITY$'))) {
                            const usage = Number(parseFloat(infoValue).toFixed(0))
                            CoreUtilityE.push(usage)
                        } else if (upperInfoKey.match(new RegExp('^CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]USAGE$'))) {
                            // 没有超线程技术
                            const usage = Number(parseFloat(infoValue).toFixed(0))
                            const infoKeyItems = upperInfoKey.split(' ')
                            const Thread = CPUData.Thread
                            if (Thread.hasOwnProperty(infoKeyItems[1])) {
                                Thread[infoKeyItems[1]].T0 = usage
                            } else {
                                const Detail = {}
                                Detail.T0 = usage
                                CPUData.Thread[infoKeyItems[1]] = Detail
                            }
                        } else if (upperInfoKey.match(new RegExp('^P-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]USAGE$'))) {
                            CoreLoadP.push(parseFloat(infoValue))
                        } else if (upperInfoKey.match(new RegExp('^P-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]UTILITY$'))) {
                            CoreUtilityP.push(parseFloat(infoValue))
                        } else if (upperInfoKey.match(new RegExp('^E-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]USAGE$'))) {
                            CoreLoadE.push(parseFloat(infoValue))
                        } else if (upperInfoKey.match(new RegExp('^E-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]UTILITY$'))) {
                            CoreUtilityE.push(parseFloat(infoValue))
                        } else if (upperInfoKey === 'IPU USAGE' || upperInfoKey === 'NPU USAGE') {
                            //NPU占用
                            CPUData.NPUUsage = Number(parseFloat(infoValue).toFixed(0))
                        }
                    } else if (infoType === 'TEMPERATURE') {
                        // 温度
                        if (upperInfoKey.indexOf('PACKAGE') !== -1 || upperInfoKey.indexOf('IA CORES') !== -1) {
                            // INTEL
                            const Detail = {}
                            Detail[infoKey] = Number(parseFloat(infoValue).toFixed(0))
                            CPUData.Temp.push(Detail)
                            CPUData.Temps.push(Number(parseFloat(infoValue).toFixed(0)))
                        } else if (
                          upperInfoKey.indexOf('DIE (AVERAGE)') !== -1 ||
                          upperInfoKey.indexOf('CCD') !== -1 ||
                          upperInfoKey.indexOf('SOC') !== -1 ||
                          upperInfoKey.indexOf('TCTL/TDIE') !== -1 ||
                          upperInfoKey.indexOf('TCTL') !== -1 ||
                          upperInfoKey.indexOf('TDIE') !== -1 ||
                          upperInfoKey.match(new RegExp('^CORE[0-9]'))
                        ) {
                            // AMD
                            const Detail = {}
                            Detail[infoKey] = Number(parseFloat(infoValue).toFixed(0))
                            CPUData.Temp.push(Detail)
                            CPUData.Temps.push(Number(parseFloat(infoValue).toFixed(0)))
                            if (upperInfoKey.indexOf('DIE (AVERAGE)')) {
                                CPUData.DieAverage = Number(parseFloat(infoValue).toFixed(0))
                            }

                        } else if (upperInfoKey.match(new RegExp('^CORE[ \f\r\t\n][0-9]')) || upperInfoKey.match(new RegExp('^P-CORE[ \f\r\t\n][0-9]')) || upperInfoKey.match(new RegExp('^E-CORE[ \f\r\t\n][0-9]'))) {
                            if (infoType === 'TEMPERATURE') {
                                if (upperInfoKey.indexOf('DISTANCE') === -1) {
                                    const Detail = {}
                                    Detail[infoKey] = Number(parseFloat(infoValue).toFixed(0))
                                    CPUData.Temp.push(Detail)
                                    CPUData.Temps.push(Number(parseFloat(infoValue).toFixed(0)))
                                }
                            }
                        }
                    } else if (infoType === 'POWER') {
                        // TDP
                        if (upperInfoKey.indexOf('PACKAGE POWER') !== -1 ||
                            upperInfoKey.indexOf('SOC') !== -1 ||
                            upperInfoKey.indexOf('IA CORES POWER') !== -1) {
                            const Detail = {}
                            Detail[infoKey] = Number(parseFloat(infoValue).toFixed(0))
                            CPUData.TDP.push(Detail)
                        }

                        if (upperInfoKey === 'CPU PACKAGE POWER') {
                            CPUData.PackagePower = (Number(parseFloat(infoValue).toFixed(0)))
                        }
                    } else if (infoType === 'OTHER') {
                        // 倍频 Ratio
                        if (
                          upperInfoKey.match(new RegExp('^CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]RATIO[.\n]*')) ||
                          upperInfoKey.match(new RegExp('^P-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]RATIO [(]PERF #1[)][.\n]*')) ||
                          upperInfoKey.match(new RegExp('^E-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]RATIO [(]PERF #1[)][.\n]*')) ||
                          upperInfoKey.match(new RegExp('^P-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]RATIO[.\n]*')) ||
                          upperInfoKey.match(new RegExp('^E-CORE[ \f\r\t\n][0-9]+[ \f\r\t\n]RATIO[.\n]*'))
                        ) {
                            const ratio = Number(parseFloat(infoValue).toFixed(0))
                            const infoKeyItems = upperInfoKey.split(' ')
                            CPUData.Core.Ratio[infoKeyItems[1]] = ratio
                            CoreRatios.push(ratio)
                        }
                    }
                })
                if (CoreClocks.length > 0) {
                    CPUData.Core.Count = CoreClocks.length
                    CPUData.Clock = Number((sum(CoreClocks) / CoreClocks.length).toFixed(0))
                }

                if (PCoreClocks.length > 0) {
                    CPUData.ClockP = Number((sum(PCoreClocks) / PCoreClocks.length).toFixed(0))
                }

                if (ECoreClocks.length > 0) {
                    CPUData.ClockE = Number((sum(ECoreClocks) / ECoreClocks.length).toFixed(0))
                }

                if (CoreVoltages.length > 0) {
                    let value = Number((sum(CoreVoltages) / CoreVoltages.length).toFixed(3))
                    CPUData.Voltage = value
                    CPUData.VoltageArr.push({ 'Voltage': value })
                }

                if (PCoreVoltages.length > 0) {
                    CPUData.VoltageP = Number((sum(PCoreVoltages) / PCoreVoltages.length).toFixed(3))
                }

                if (ECoreVoltages.length > 0) {
                    CPUData.VoltageE = Number((sum(ECoreVoltages) / ECoreVoltages.length).toFixed(3))
                }

                if (CoreLoadP.length > 0) {
                    const value = Number(parseFloat((sum(CoreLoadP) / CoreLoadP.length)).toFixed(1))
                    CPUData.LoadP = value
                    CPUData.TotalUsageP = value
                    CPUData.LoadPEArr[0].TotalUsageP = value
                }

                if (CoreLoadE.length > 0) {
                    const value = Number(parseFloat(sum(CoreLoadE) / CoreLoadE.length).toFixed(1))
                    CPUData.LoadE = value
                    CPUData.TotalUsageE = value
                    CPUData.LoadPEArr[0].TotalUsageE = value
                }

                if (CoreUtilityP.length > 0) {
                    const value = Number(parseFloat((sum(CoreUtilityP) / CoreUtilityP.length)).toFixed(1))
                    CPUData.TotalUtilityP = value
                    CPUData.LoadPEArr[1].TotalUtilityP = value
                }

                if (CoreUtilityE.length > 0) {
                    const value = Number(parseFloat((sum(CoreUtilityE) / CoreUtilityE.length)).toFixed(1))
                    CPUData.TotalUtilityE = value
                    CPUData.LoadPEArr[1].TotalUtilityE = value
                }

                if (CoreRatios.length > 0) {
                    const value = Number(parseFloat((sum(CoreRatios) / CoreRatios.length)).toFixed(1))
                    CPUData.Ratio = value
                }

            }
            CPU[CPUIDX] = CPUData
        } else if (upperMainKey.match(new RegExp('^GPU.*$'))) {
            // GPU
            var strIdx = upperMainKey.indexOf('#') + 1
            const GPUIDX = upperMainKey.substring(strIdx, strIdx + 1)
            let GPUData
            if (GPU.hasOwnProperty(GPUIDX)) {
                // 取出来
                GPUData = GPU[GPUIDX]
            } else {
                // 创建新的
                GPUData = CreateGPUSensor()
            }
            const IsArrBool1 = Array.isArray(itemValue)
            if (itemValue && IsArrBool1) {
                GPUData.Name = (mainKey.split(':')[1]).replace(/(^\s*)|(\s*$)/g, '')
                GPUData.Type = 'Discrete'
                const GPUDataLoadD3D = []
                const GPUDataLoadTOTAL = []
                const GPUDataLoadUTILIZATION = []
                const GPUDataLoadComputingUsage = []
                const NeedThrottling = ['AVG. POWER (PL1)', 'BURST POWER (PL2)', 'CURRENT (PL4)', 'THERMAL', 'POWER SUPPLY', 'SOFTWARE LIMIT', 'HARDWARE LIMIT']
                let hasGpuShaderClock = itemValue.some(sensor => {let key = Object.keys(sensor)[0];return key === "GPU Shader Clock";});
                itemValue.forEach(function (itemDetail) {
                    const infoKey = Object.keys(itemDetail)[0]
                    const itemInfo = itemDetail[infoKey]
                    const infoType = itemInfo.type.toUpperCase()
                    const infoValue = itemInfo.value
                    const upperInfoKey = infoKey.toUpperCase()
                    if (infoType === 'USAGE') {
                        // 显存占用
                        if (upperInfoKey.indexOf('MEMORY USAGE') !== -1) {
                            GPUData.VRAMUsage = Number(parseFloat(infoValue).toFixed(1))
                        }
                        // 占用
                        if (upperInfoKey.indexOf('D3D USAGE') !== -1) {
                            // D3D USAGE
                            // GPUData["Load"].push({ "D3D": parseFloat(infoValue).toFixed(0) });
                            GPUDataLoadD3D.push({ D3D: Number(parseFloat(infoValue).toFixed(0)) })
                        }
                        if (upperInfoKey.indexOf('CORE LOAD') !== -1 || upperInfoKey.indexOf('TOTAL USAGE') !== -1) {
                            // CORE LOAD TOTAL
                            // GPUData["Load"].push({ "TOTAL": parseFloat(infoValue).toFixed(0) });
                            GPUDataLoadTOTAL.push({ TOTAL: Number(parseFloat(infoValue).toFixed(0)) })
                        }
                        if (upperInfoKey.indexOf('GPU UTILIZATION') !== -1) {
                            // CORE Utilization
                            GPUDataLoadUTILIZATION.push({ TOTAL: Number(parseFloat(infoValue).toFixed(0)) })
                        }

                        if (upperInfoKey.indexOf('GPU COMPUTING USAGE') !== -1) {
                            // CORE Utilization
                            GPUDataLoadComputingUsage.push({ 'Computing Usage': Number(parseFloat(infoValue).toFixed(0)) })
                        }

                        GPUData.Load = GPUDataLoadD3D.concat(GPUDataLoadTOTAL).concat(GPUDataLoadUTILIZATION).concat(GPUDataLoadComputingUsage)

                        if (upperInfoKey === 'GPU HOTSPOT THERMAL LIMIT') {
                            GPUData.ThermalHotspot = Number(parseFloat(infoValue).toFixed(0))
                        } else if (upperInfoKey === 'GPU MEMORY THERMAL LIMIT') {
                            GPUData.ThermalMemory = Number(parseFloat(infoValue).toFixed(0))
                        }

                        // AMD GPU 电压限制
                        if (upperInfoKey === 'GPU VR GFX THERMAL LIMIT') {
                            GPUData.VRGFXThermal = Number(parseFloat(infoValue).toFixed(0))
                        }
                        if (upperInfoKey === 'GPU VR SOC THERMAL LIMIT') {
                            GPUData.VRSOCThermal = Number(parseFloat(infoValue).toFixed(0))
                        }

                        if (upperInfoKey.indexOf('MEMORY CONTROLLER LOAD') !== -1) {
                            GPUData.MemoryControllerLoad = Number(parseFloat(infoValue).toFixed(1))
                        }

                        if (upperInfoKey.indexOf('BUS LOAD') !== -1) {
                            GPUData.BusLoad = Number(parseFloat(infoValue).toFixed(1))
                        }

                    } else if (infoType === 'POWER') {
                        // TDP
                        const tdp = {}
                        tdp[infoKey] = Number(parseFloat(infoValue).toFixed(3))
                        GPUData.TDP.push(tdp)

                        if (upperInfoKey === 'GPU POWER') {
                            GPUData.GPUPower = Number(parseFloat(infoValue).toFixed(3))
                        }

                    } else if (infoType === 'CLOCK') {
                        // 频率
                        if (upperInfoKey.indexOf('GPU CLOCK') !== -1 && upperInfoKey.indexOf('EFFECTIVE') === -1) {
                            GPUData.Clock = Number(parseFloat(infoValue).toFixed(0))
                        }
                        if (upperInfoKey === "GPU SHADER CLOCK") {
                            GPUData.Clock = Number(parseFloat(infoValue).toFixed(0))
                        }
                        if (!hasGpuShaderClock && upperInfoKey === 'GPU FRONT END CLOCK') {
                            GPUData.Clock = Number(parseFloat(infoValue).toFixed(0))
                        }
                        // 显存频率
                        if (upperInfoKey.indexOf('MEMORY CLOCK') !== -1) {
                            GPUData.VRAMClock = Number(parseFloat(infoValue).toFixed(1))
                        }
                    } else if (infoType === 'VOLTAGE') {
                        // 电压
                        if (upperInfoKey.indexOf('CORE VOLTAGE') !== -1) {
                            GPUData.Voltage = Number(parseFloat(infoValue).toFixed(3))
                        }
                    } else if (infoType === 'TEMPERATURE') {
                        // 温度
                        const temp = {}
                        temp[upperInfoKey] = Number(parseFloat(infoValue).toFixed(0))
                        GPUData.Temp.push(temp)
                        GPUData.Temps.push(Number(parseFloat(infoValue).toFixed(0)))
                        // 核心热点温度
                        if (upperInfoKey.indexOf('HOT SPOT TEMPERATURE') !== -1) {
                            GPUData.HotSpotTemp = Number(parseFloat(infoValue).toFixed(0))
                        }
                        // 显存温度
                        if (['GPU MEMORY JUNCTION TEMPERATURE', 'GPU MEMORY TEMPERATURE'].includes(upperInfoKey)) {
                            GPUData.VRAMTemp = Number(parseFloat(infoValue).toFixed(0))
                        }
                    } else if (infoType === 'FAN') {
                        // 风扇转速
                        const Detail = {}
                        if (infoKey === 'GPU Fan') {
                            Detail[infoKey + ' (' + itemInfo.type + ')'] = Number(parseFloat(infoValue).toFixed(0))
                        } else {
                            Detail[infoKey] = Number(parseFloat(infoValue).toFixed(0))
                        }
                        GPUData.Fan.push(Detail)
                        GPUData.Fans.push(Number(parseFloat(infoValue).toFixed(0)))
                    } else if (infoType === 'OTHER') {
                        // 风扇转速 专用显存占用
                        if (upperInfoKey.indexOf('FAN') !== -1) {
                            const Detail = {}
                            if (infoKey === 'GPU Fan') {
                                Detail[infoKey + ' (' + itemInfo.type + ')'] = Number(parseFloat(infoValue).toFixed(0))
                            } else {
                                Detail[infoKey] = Number(parseFloat(infoValue).toFixed(0))
                            }
                            GPUData.Fan.push(Detail)
                        } else if (upperInfoKey.indexOf('MEMORY DEDICATED') !== -1) {
                            GPUData.VRAM = Number(parseFloat(infoValue).toFixed(0))
                            if (upperInfoKey.includes('D3D MEMORY DEDICATED')) {
                                // AMD显存占用 MB
                                GPUData.VRAMUsageSize = Number(parseFloat(infoValue).toFixed(0))
                            }
                        } else if (upperInfoKey.indexOf('D3D MEMORY DYNAMIC') !== -1) {
                            GPUData.MemoryDynamic = Number(parseFloat(infoValue).toFixed(0))
                        } else if (upperInfoKey.indexOf('MEMORY ALLOCATED') !== -1) {
                            GPUData.MemoryAllocated = Number(parseFloat(infoValue).toFixed(0))
                        } else if (upperInfoKey.includes('PERFORMANCE LIMIT')) {
                            const key = upperInfoKey.split(' - ')
                            const GPUPLR_Obj = {}
                            GPUPLR_Obj[key[1]] = parseInt(infoValue)
                            GPUData.PLR.push(GPUPLR_Obj)
                            if (upperInfoKey.includes('RELIABILITY VOLTAGE')) {
                                GPUData.ReliabilityVoltage = Number(parseFloat(infoValue))
                            } else if (upperInfoKey.includes('MAX OPERATING VOLTAGE')) {
                                GPUData.MaxOperatingVoltage = Number(parseFloat(infoValue))
                            }
                        } else if (NeedThrottling.includes(upperInfoKey)) {
                            const GPUPLR_Obj = {}
                            GPUPLR_Obj[upperInfoKey] = parseInt(infoValue)
                            if (GPUData.CoreThrottling.length <= 7) {
                                GPUData.CoreThrottling.push(GPUPLR_Obj)
                            } else {
                                GPUData.VRAMThrottling.push(GPUPLR_Obj)
                            }
                        } else if (upperInfoKey.includes('TOTAL GPU POWER')) {
                            GPUData.TotalGPUPower = Number(parseFloat(infoValue).toFixed(0))
                        }
                    }
                })
            }
            GPU[GPUIDX] = GPUData
        } else if (upperMainKey.match(new RegExp('^NETWORK.*$'))) {
            // Network
            if (itemValue) {
                let DLRate = parseFloat(NETWORK.DLRate)
                let ULRate = parseFloat(NETWORK.ULRate)
                let TotalDL = parseFloat(NETWORK.TotalDL)
                let TotalUL = parseFloat(NETWORK.TotalUL)
                const IsArrBool1 = Array.isArray(itemValue)
                if (itemValue && IsArrBool1) {
                    itemValue.forEach(function (itemDetail) {
                        var infoKey = Object.keys(itemDetail)[0]
                        var itemInfo = itemDetail[infoKey]
                        var infoType = itemInfo.type.toUpperCase()
                        var infoValue = itemInfo.value
                        var upperInfoKey = infoKey.toUpperCase()
                        if (infoType === 'OTHER') {
                            if (upperInfoKey === 'CURRENT DL RATE') {
                                DLRate += parseFloat(infoValue)
                            }
                            if (upperInfoKey === 'CURRENT UP RATE') {
                                ULRate += parseFloat(infoValue)
                            }
                            if (upperInfoKey === 'TOTAL DL') {
                                TotalDL += parseFloat(infoValue)
                            }
                            if (upperInfoKey === 'TOTAL UP') {
                                TotalUL += parseFloat(infoValue)
                            }
                        }
                    })
                }

                NETWORK.DLRate = DLRate
                NETWORK.ULRate = ULRate
                NETWORK.TotalDL = TotalDL
                NETWORK.TotalUL = TotalUL
            }
        } else if (upperMainKey.match(new RegExp('^DRIVE.*$')) && !(upperMainKey.includes('VIRTUAL'))) {
            // Drive
            const upperMainKeys = upperMainKey.split(':')
            let DriveName = upperMainKeys[1]
            if (DriveName !== '  ()') {
                DriveName = DriveName.substring(1, DriveName.length)
                var DriveData = CreateDriveSensor()
                if (DRIVE.hasOwnProperty(DriveName)) {
                    DriveData = DRIVE[DriveName]
                }
                const IsArrBool1 = Array.isArray(itemValue)
                if (itemValue && IsArrBool1) {
                    itemValue.forEach(function (itemDetail) {
                        const infoKey = Object.keys(itemDetail)[0]
                        const itemInfo = itemDetail[infoKey]
                        const infoType = itemInfo.type.toUpperCase()
                        const infoValue = itemInfo.value
                        const upperInfoKey = infoKey.toUpperCase()
                        if (infoType === 'USAGE') {
                            if (upperInfoKey === 'READ ACTIVITY') {
                                // READ
                                DriveData.ReadActivity = Number(parseFloat(infoValue).toFixed(0))
                            } else if (upperInfoKey === 'WRITE ACTIVITY') {
                                // WRITE
                                DriveData.WriteActivity = Number(parseFloat(infoValue).toFixed(0))
                            } else if (upperInfoKey === 'TOTAL ACTIVITY') {
                                // TOTAL
                                DriveData.TotalActivity = Number(parseFloat(infoValue).toFixed(0))
                            }
                        } else if (infoType === 'OTHER') {
                            if (upperInfoKey === 'READ RATE') {
                                DriveData.ReadRate = Number(parseFloat(infoValue).toFixed(3))
                            } else if (upperInfoKey === 'WRITE RATE') {
                                DriveData.WriteRate = Number(parseFloat(infoValue).toFixed(3))
                            }
                        }
                    })
                }
                DRIVE[DriveName] = DriveData
            }
        } else if (upperMainKey.match(new RegExp('^S.M.A.R.T..*$'))) {
            // Drive SMART
            const upperMainKeys = upperMainKey.split(':')
            let DriveName = upperMainKeys[1]
            DriveName = DriveName.substring(1, DriveName.length)

            let DriveData = CreateDriveSensor()
            if (DRIVE.hasOwnProperty(DriveName)) {
                DriveData = DRIVE[DriveName]
            }
            const IsArrBool1 = Array.isArray(itemValue)
            if (itemValue && IsArrBool1) {
                const DRIVETemp = []
                itemValue.forEach(function (itemDetail) {
                    const infoKey = Object.keys(itemDetail)[0]
                    const itemInfo = itemDetail[infoKey]
                    const infoType = itemInfo.type.toUpperCase()
                    const infoValue = itemInfo.value
                    if (infoType === 'TEMPERATURE') {
                        DRIVETemp.push(Number(parseFloat(infoValue).toFixed(0)))
                        DriveData.Temp = DRIVETemp
                    }
                })
            }
            DRIVE[DriveName] = DriveData
            DRIVES.push(DriveData)
        } else if (upperMainKey.match(new RegExp('^MEMORY.*$'))) {

            // DRAM
            const IsArrBool1 = Array.isArray(itemValue)
            if (itemValue && IsArrBool1) {
                itemValue.forEach(function (itemDetail) {
                    const infoKey = Object.keys(itemDetail)[0]
                    const itemInfo = itemDetail[infoKey]
                    const infoType = itemInfo.type.toUpperCase()
                    const infoValue = itemInfo.value
                    const upperInfoKey = infoKey.toUpperCase()
                    if (infoType === 'OTHER') {
                        if (upperInfoKey === 'TCAS') {
                            DRAM.TCAS = Number(parseFloat(infoValue).toFixed(0))
                        } else if (upperInfoKey === 'TRCD') {
                            DRAM.TRCD = Number(parseFloat(infoValue).toFixed(0))
                        } else if (upperInfoKey === 'TRP') {
                            DRAM.TRP = Number(parseFloat(infoValue).toFixed(0))
                        } else if (upperInfoKey === 'TRAS') {
                            DRAM.TRAS = Number(parseFloat(infoValue).toFixed(0))
                        } else if (upperInfoKey === 'COMMAND RATE' || upperInfoKey === 'COMMAND RATE (CR)') {
                            DRAM.CR = Number(parseFloat(infoValue).toFixed(0))
                        } else if (upperInfoKey === 'GEAR MODE') {
                            DRAM.GearMode = Number(parseFloat(infoValue).toFixed(0))
                        }
                    } else if (infoType === 'CLOCK') {
                        if (upperInfoKey === 'MEMORY CLOCK') {
                            DRAM.Clock = parseInt(parseFloat(infoValue).toFixed(0)) * 2
                        }
                    }
                })
            }
        } else if (upperMainKey.includes('DIMM')) {
            // DRAM  old---upperMainKey.match(new RegExp("^DIMM.*$"))
            if (itemValue) {
                itemValue.forEach(function (itemDetail) {
                    const infoKey = Object.keys(itemDetail)[0]
                    const itemInfo = itemDetail[infoKey]
                    const infoType = itemInfo.type.toUpperCase()
                    const infoValue = itemInfo.value
                    if (infoType === 'TEMPERATURE') {
                        DRAM.Temp.push(Number(parseFloat(infoValue).toFixed(0)))
                    }
                })
            }
        } else if (upperMainKey.match(new RegExp('^SYSTEM.*$'))) {
            // DRAM
            const IsArrBool1 = Array.isArray(itemValue)
            if (itemValue && IsArrBool1) {
                itemValue.forEach(function (itemDetail) {
                    const infoKey = Object.keys(itemDetail)[0]
                    const itemInfo = itemDetail[infoKey]
                    const infoType = itemInfo.type.toUpperCase()
                    const infoValue = itemInfo.value
                    const upperInfoKey = infoKey.toUpperCase()
                    if (infoType === 'OTHER') {
                        if (upperInfoKey === 'PHYSICAL MEMORY AVAILABLE') {
                            DRAM.PhysicalAvailable = Number(parseFloat(infoValue).toFixed(0)) // 物理可用
                        } else if (upperInfoKey === 'PHYSICAL MEMORY USED') {
                            DRAM.PhysicalUsed = Number(parseFloat(infoValue).toFixed(0))// 已使用物理内存
                        } else if (upperInfoKey === 'PHYSICAL MEMORY LOAD') {
                            DRAM.PhysicalLoad = Number(parseInt(infoValue).toFixed(0))// 物理内存占用
                        } else if (upperInfoKey === 'VIRTUAL MEMORY COMMITTED') {
                            DRAM.VirtualCommitted = Number(parseFloat(infoValue).toFixed(0)) // 已提交虚拟内存
                        } else if (upperInfoKey === 'VIRTUAL MEMORY AVAILABLE') {
                            DRAM.VirtualAvailable = Number(parseFloat(infoValue).toFixed(0))// 可用虚拟内存
                        } else if (upperInfoKey === 'VIRTUAL MEMORY LOAD') {
                            DRAM.VirtuallLoad = Number(parseInt(infoValue).toFixed(0))// 虚拟内存占用
                        }
                    }
                })
            }
        } else if (upperMainKey.match(new RegExp('^WINDOWS HARDWARE ERRORS.*$'))) {
            WHEA_Total_Errors = parseInt(itemValue[0]['Total Errors'].value)
        } else if (upperMainKey.match(new RegExp('^BATTERY.*$'))) {
            if (itemValue) {
                itemValue.forEach(function (itemDetail) {
                    let infoKey = Object.keys(itemDetail)[0]
                    let itemInfo = itemDetail[infoKey]
                    if (infoKey === 'Charge Level') {
                        Battery['ChargeLevel'] = parseInt(itemInfo.value)
                    } else if (infoKey === 'Battery Voltage') {
                        Battery['Voltage'] = parseInt(itemInfo.value)
                    } else if (infoKey === 'Wear Level') {
                        Battery['WearLevel'] = parseInt(itemInfo.value)
                    }
                })
            }
        } else {
            // Motherboard
            const IsArrBool1 = Array.isArray(itemValue)
            if (itemValue && IsArrBool1) {
                itemValue.forEach(function (itemDetail) {
                    const infoKey = Object.keys(itemDetail)[0]
                    const itemInfo = itemDetail[infoKey]
                    const infoType = itemInfo.type.toUpperCase()
                    const infoValue = itemInfo.value
                    const upperInfoKey = infoKey.toUpperCase()
                    if (infoType === 'TEMPERATURE') {
                        const Detail = {}
                        if (Number(infoValue) < 100) {
                            Detail[infoKey] = Number(parseFloat(infoValue).toFixed(0))
                            MOTHERBOARD.Temp.push(Detail)
                            MOTHERBOARD.Temps.push(Number(parseFloat(infoValue).toFixed(0)))
                            if (upperInfoKey.match(new RegExp('^TEMPERATURE[ \f\r\t\n][0-9]$'))) {
                                MOTHERBOARD.Temperatures.push(Number(parseFloat(infoValue).toFixed(0)))
                            }
                        }
                    } else if (infoType === 'FAN') {
                        const Detail = {}
                        Detail[infoKey] = Number(parseFloat(infoValue).toFixed(0))
                        MOTHERBOARD.Fan.push(Detail)
                        if (infoKey !== 'Fan') {
                            MOTHERBOARD[infoKey] = Number(parseFloat(infoValue).toFixed(0))
                        }
                    } else if (infoKey === 'Vcore') {
                        // MOTHERBOARD["Vcore"] = Number(infoValue);
                        MOTHERBOARD.Vcore = Number(parseFloat(infoValue).toFixed(3))
                    } else if (infoKey === 'DRAM' || infoKey === 'IMC VDD') {
                        DRAM.Voltage = Number(parseFloat(infoValue).toFixed(3))
                    } else if (infoType === 'POWER') {
                        MOTHERBOARD.CPUCorePower = Number(parseFloat(infoValue).toFixed())
                    } else if (infoType === 'VOLTAGE') {
                        MOTHERBOARD[infoKey.replace('.', '')] = Number(parseFloat(infoValue).toFixed(3))
                    }
                })
            }
        }
    }
    if (CPUData) {
        if (MOTHERBOARD['Vcore']) {
            CPUData.VoltageArr.push({ 'Vcore': MOTHERBOARD['Vcore'] })
        }
        if (MOTHERBOARD['CPUCorePower']) {
            CPUData.TDP.push({ 'CPU Core Power': MOTHERBOARD['CPUCorePower'] })
        }
        for (let i = 0; i < CPUData.Core.Count; i++) {
            const coreId = i.toString();
            if (CPUData.Thread.hasOwnProperty(coreId)) {
                const T0 = CPUData.Thread[coreId].T0;
                const T1 = CPUData.Thread[coreId].hasOwnProperty('T1') ? CPUData.Thread[coreId].T1 : 0;
                CPUData.Core.Load[coreId] = Math.round((T0 + T1) / 2);
            }
        }
    }
    let currentDate = new Date()
    let year = currentDate.getFullYear().toString()
    let month = (currentDate.getMonth() + 1).toString().padStart(2, '0')
    let day = currentDate.getDate().toString().padStart(2, '0')
    let dateString = `${year}/${month}/${day}`

    let hours = currentDate.getHours()
    let minutes = currentDate.getMinutes().toString().padStart(2, '0')
    let seconds = currentDate.getSeconds()

    let timeString = hours + ':' + minutes + ':' + seconds
    let timeString1 = hours + ':' + minutes

    return {
        CPU        : CPU,
        GPU        : GPU,
        NETWORK    : NETWORK,
        DRIVE      : DRIVE,
        DRIVES     : DRIVES,
        DRAM       : DRAM,
        MOTHERBOARD: MOTHERBOARD,
        WHEA       : WHEA_Total_Errors,
        Battery    : Battery,
        DATE       : dateString,
        TIME       : timeString,
        TIMENS     : timeString1
    }
}
