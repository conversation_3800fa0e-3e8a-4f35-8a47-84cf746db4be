<!-- 游戏加加更新启动页 -->
<script setup lang="ts">
import type { CalendarDateType, CalendarInstance } from 'element-plus'
import { Minus, Close } from "@element-plus/icons-vue";
import {onMounted , reactive ,ref ,computed} from "vue";
import { defineEmits } from 'vue'
import {LoadModuleStepInterface} from "@/modules/Game_Update/types/Interface";
import { getGameList , getHistoryList , postReceiveGameInfo ,getTime} from "../utils/receiveGame";
import { ElMessage } from "element-plus";
import RightTopIcons from "@/components/mainCom/RightTopIcons.vue";

let AccountList  = ref([])
let init = ref(true)
let steamList:any = ref([])
let epicList:any = ref([])
let isVIP:any = ref(false)
const emit = defineEmits<{
  (event: 'updateParentValue'): void;
  (event: 'updateAccountInfo'): void;
  (event: 'openshade'): void;
  (event: 'closeshade'): void;
  (event: 'deleteAccountInfo'): void;
  (event: 'openplan'): void;
}
>();
// @ts-ignore
const gamepp = window.gamepp as any;
onMounted (async() => 
{
  //喜加一
   await INIT()
   init.value = false
   gamepp.webapp.onInternalAppEvent.addEventListener(async (value:any) => {
    console.log('value::',value);
    
    if(value.action === 'AgreePlan')
    {
      loginEvent('epic',-1,false)
    }
  })
})


const INIT = async() =>
{
  //初始化账号信息
  await initCount()
}

const initCount = async() =>
{
  if (localStorage.getItem('account') === null || JSON.parse(localStorage.getItem('account') as any).length !== 6) 
  {
    localStorage.setItem('account', "[{\"persist\":\"persist:B6976E9166D9F496497780A0540833B5\",\"platform\":\"epic\",\"bindDate\":\"\",\"platform_name\":\"\",\"active\":false,\"loading\":false,\"loginStatus\":false,\"idle\":true,\"localIndex\":0},{\"persist\":\"persist:750E5870F1ED4F21574CFC35DC6CAF05\",\"platform\":\"epic\",\"bindDate\":\"\",\"platform_name\":\"\",\"active\":false,\"loading\":false,\"loginStatus\":false,\"idle\":true,\"localIndex\":1},{\"persist\":\"persist:8F0F22FCFD202C017FA9E4F3244C074B\",\"platform\":\"epic\",\"bindDate\":\"\",\"platform_name\":\"\",\"active\":false,\"loading\":false,\"loginStatus\":false,\"idle\":true,\"localIndex\":2},{\"persist\":\"persist:6C18E7EED1BC08121CA763FD41A1FE5D\",\"platform\":\"steam\",\"bindDate\":\"\",\"platform_name\":\"\",\"active\":false,\"loading\":false,\"loginStatus\":false,\"idle\":true,\"localIndex\":3},{\"persist\":\"persist:1875FC35367415A2A6E81A1E66270668\",\"platform\":\"steam\",\"bindDate\":\"\",\"platform_name\":\"\",\"active\":false,\"loading\":false,\"loginStatus\":false,\"idle\":true,\"localIndex\":4},{\"persist\":\"persist:********************************\",\"platform\":\"steam\",\"bindDate\":\"\",\"platform_name\":\"\",\"active\":false,\"loading\":false,\"loginStatus\":false,\"idle\":true,\"localIndex\":5}]")
  }
  isVIP.value = await gamepp.user.isVIP.promise();
  await FilterAccount()
}

const loginEventEx = () =>
{
  emit("openplan");
}

const loginEvent = async(platform:string, index:number, reLogin:boolean) =>
{
  emit("openshade");
  console.warn('click');
  if(!gamepp.user.getToken.sync())
  {
    ElMessage({
        message: '请登录游戏加加',
        type: 'warning',
        grouping:true
      });
      console.warn('请登录游戏加加');
      emit("closeshade");
      return
  }
  console.warn('return not effective');
  let count = 1
  if(isVIP.value)
  {
    count =3
  }
  
  if([platform + 'List'].length > count && !reLogin)
  {
    ElMessage({
        message: '绑定账号已达上限',
        type: 'warning',
        grouping:true
      });
      emit("closeshade");
      return
  }
  const localAccountList = JSON.parse(localStorage.getItem('account') as any)

  let idlePersist
  let idleLocalIndex
  if (index === -1) 
  {
    for (const item of localAccountList) {
      if (item.platform === platform && item.idle) 
      {
        idlePersist = item.persist
        idleLocalIndex = item.localIndex
      }
    }
  } 
  else 
  {
    idlePersist = localAccountList[index].persist
    idleLocalIndex = index
  }
  //await gamepp.freegame.createLoginWindow.promise('epic', 'persist:750E5870F1ED4F21574CFC35DC6CAF05')
  const res = await gamepp.freegame.createLoginWindow.promise(platform, idlePersist)
  console.log('登陆返回结果', res)
      if (res.result) 
        {
          if (platform === 'steam' && !res.twocheck) 
          {
          ElMessage({
            message: '添加账号失败，账号未开启二次验证',
            type: 'warning',
            grouping:true
          })
          gamepp.freegame.signOut.promise(localAccountList[idleLocalIndex].platform, localAccountList[idleLocalIndex].persist)
          emit("closeshade");
          return
          }
        }
        if(!res.result) 
        {
          emit("closeshade"); 
          return
        }
        console.log('localAccountList',localAccountList);
        console.log('idleLocalIndex',idleLocalIndex);
        //处理登录成功信息
        localAccountList[idleLocalIndex].platform_name = res.user
        localAccountList[idleLocalIndex].bindDate = new Date().getFullYear() + '/' + (new Date().getMonth() + 1) + '/' + new Date().getDate()
        localAccountList[idleLocalIndex].idle = false
        localAccountList[idleLocalIndex].active = true
        localAccountList[idleLocalIndex].loginStatus = true
        localAccountList[idleLocalIndex].loading = false    
        //
        console.warn('处理登录账号信息');

        localStorage.setItem('account', JSON.stringify(localAccountList))
        emit("updateAccountInfo");
        //处理平台账号信息
        await FilterAccount()
        emit("closeshade");
}

const removeEvent = async (index:number) =>
{
      const localAccount = JSON.parse(localStorage.getItem('account') as any)
      localAccount[index].active = false
      localAccount[index].loading = false
      localAccount[index].loginStatus = false
      localAccount[index].idle = true
      localAccount[index].platform_name = ''
      localAccount[index].bindDate = ''
      localStorage.setItem('account', JSON.stringify(localAccount))
      emit("deleteAccountInfo");
      await FilterAccount()
      await gamepp.freegame.signOut.promise(localAccount[index].platform, localAccount[index].persist)
     
}

 const  disableEvent = async(platform:string, index:number, localIndex:number) =>
 {
      console.log('platform',platform,index,localIndex);
      console.log('epicList.value',epicList.value);
      let bStatus = 0
      
      if(platform == 'epic')
      {
        epicList.value[index].active = !epicList.value[index].active
        if(epicList.value[index].active)
        {
          bStatus = 1 
        }
      }
      else
      {
        steamList.value[index].active = !steamList.value[index].active
        if(steamList.value[index].active)
        {
          bStatus = 1
        }
      }
      // [platform + 'List'].value[index].active = false
      const localAccount = JSON.parse(localStorage.getItem('account') as any)
      localAccount[localIndex].active = !localAccount[localIndex].active
      localStorage.setItem('account', JSON.stringify(localAccount))
      ElMessage({
        message: bStatus == 0?'账号已禁用领取':'账号已启用领取',
        type: 'warning',
        grouping:true
      })
      const obj ={action:'updateAcoount',index:localIndex}
      await gamepp.webapp.sendInternalAppEvent.promise('FreeGame', obj)
  }

const getAccountList = () =>
{
    const accountList = JSON.parse(localStorage.getItem('account') as any)
      if (accountList.every((v:any, i:any) => { return v.idle })) {
        return []
      } else {
        const list = accountList.filter((v:any, i:any) => { return !v.idle })
        return list
      }
}
//添加账号
const  handleClick = () =>
    {
      emit("updateParentValue");
    }
//账号清算
const FilterAccount = async() =>
{
    const accountList = getAccountList()
    const historyList = await getHistoryList()
    steamList.value = []
    epicList.value = []
    accountList.forEach((account:any, i:number) => {
        let gameNum = 0
        let price = 0
        historyList.forEach((v:any) => {
            if (v.platform_name === account.platform_name && v.platform === account.platform) {
                if (+v.status === 200) {
                 gameNum += 1
                }
                if (v.original_price !== 'Free' && +v.status === 200) {
                 const num = +v.original_price.replace('¥', '').replace('HK$', '')
                 price += num
                }
            }
            })
       
        if (account.platform === 'steam') 
        {
          steamList.value.push({
            platform_name: account.platform_name,
            platform: account.platform,
            loginStatus: account.loginStatus,
            loading: account.loading,
            active: account.active,
            gameNum: gameNum,
            price: price.toFixed(2),
            bindDate: account.bindDate,
            index: account.localIndex,
            persist: account.persist
          })
        } 
        else if (account.platform === 'epic') 
        {
          epicList.value.push({
            platform_name: account.platform_name,
            platform: account.platform,
            loginStatus: account.loginStatus,
            loading: account.loading,
            active: account.active,
            gameNum: gameNum,
            price: price.toFixed(2),
            bindDate: account.bindDate,
            index: account.localIndex,
            persist: account.persist
          })
        }
      })
}
const clearCache  = () =>
{

}

</script>

<template>
  <div class="Account" v-loading="init" element-loading-background="#2d2e39">
    <div class="Account_Nav">
      <div class="return" @click.stop="handleClick">{{ $t('GamePlusOne.Return') }}</div>
      <span class="resolution" v-show="false">{{ $t('GamePlusOne.Solutionto') }}</span>
    </div>
    <div class="Box">
      <div class="lineI">
        <div class="left">
          <img class="platformIcon" src="../assets/icon/icon_epic.png" alt="">
          <span class="Platformname" style="margin-right: 33px;width: 50px;">Epic</span>
          <span>{{ $t('GamePlusOne.accounts') }}{{ epicList.length }}/{{ isVIP?3:1 }}</span>
          <img class="vip" v-show="isVIP" src="../assets/img/bq_VIP.png" alt="">
        </div>
        <div class="right">
          <div class="loginBtn" style="margin-right:10px;" @click.stop="loginEventEx()">
            <img src="../assets/icon/icon_add_account.png" alt="">
            <span >{{ $t('GamePlusOne.Addaccount') }}</span>
          </div>
          <div class="loginBtn">
            <img src="../assets/icon/icon_remove_account.png" alt="">
            <span @click.stop="clearCache()">{{ $t('GamePlusOne.Clearcache') }}</span>
          </div>
        </div>
      </div>
      <div class="Item" v-for="(item,index) in epicList">
        <span class="AccountName">{{item.platform_name}}</span>
          <span class="BindDate">{{ $t('GamePlusOne.Bindtime') }}：{{item.bindDate}}</span>
          <div class="status">{{ $t('GamePlusOne.Status') }}：<span :style="{color:item.loginStatus?'#35D57D':'#F14343'}">{{ item.loginStatus? $t('GamePlusOne.Normal') : $t('GamePlusOne.Invalid')  }}</span></div>
          <div class="total">{{ $t('GamePlusOne.total') }}<span style="color:#35D57D;">{{ item.gameNum }}</span>{{ $t('GamePlusOne.text7') }}<span style="color:#35D57D;">{{ item.price }}</span>{{ $t('GamePlusOne.Yuan') }}</div>
          <div class="do" @click="removeEvent(item.index)">{{ $t('GamePlusOne.untie') }}</div>
          <div class="do" @click="disableEvent('epic',index,item.index)">{{item.active?$t('GamePlusOne.disable') : $t('GamePlusOne.enable')}}</div>
      </div>
    </div>
    <!-- <div class="Box">
      <div class="lineI">
        <div class="left">
          <img class="platformIcon" src="../assets/icon/icon_steam.png" alt="">
          <span style="margin-right: 33px;width: 50px;">Steam</span>
          <span>绑定账号数量{{ steamList.length }}/3</span>
          <img class="vip" src="../assets/img/bq_VIP.png" alt="">
        </div>
        <div class="right">
          <div style="margin-right:10px;">
            <img src="../assets/icon/icon_add_account.png" alt="">
            <span @click.stop="loginEvent('steam',-1,false)">添加账号</span>
          </div>
          <div>
            <img src="../assets/icon/icon_remove_account.png" alt="">
            <span>清除缓存</span>
          </div>
        </div>
      </div>
      <div class="Item" v-for="(item,index) in steamList">

      </div>
    </div> -->
     
  </div>
  
   
</template>
<style lang="scss">
.Account{
  width: 932px;
  display: flex;
  flex-direction: column;
}
.Account_Nav
{
  font-size: 14px;
  width:100%;
  height: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #3E7FE1;
  .return{
    cursor: pointer;
  }
  .resolution{
    cursor: pointer;
  }
  .return:hover{
     color: white;
  }
  .resolution:hover{
    color: white;
  }
}

.Box{
  background-color:#2B2C37;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  padding: 14px 22px;
  margin-top: 20px;
  .lineI{
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .vip{
    width: 22px;
    height: 9px;
    margin-left: 10px;
  }
  .left{
    color: white;
    display: flex;
    align-items: center;
  }
  .right{
    display: flex;
    color:#999999;
    div{
      cursor: pointer;
    }
    div:hover{
      color: white;
    }
    img{
      margin-right: 4px;
      width: 14px;
      height: 14px;
    }
  }
}
 .platformIcon
 {
   width: 26px;
   height: 26px;
   margin-right: 13px;
 }

 .Item
 {
   width: 100%;
   display: flex;
   justify-content: space-between;
   align-items: center;
   margin-top: 10px;
   color: #999999;
   span{
    display: inline-block; /* 或 block，根据需要选择 */
    white-space: nowrap; /* 防止内容换行 */
    overflow: hidden; /* 隐藏超出部分 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */
   }
   .AccountName{
    width: 130px;
   }
   .bindDate{
    width: 200px;
   }
   .status{
    display: flex;
    align-items: center;
    justify-content: center;
   }
   .total{
    margin-left:30px;
    width: 300px;
    display: flex;
    align-items: center;
   }
   .do{
    width: 65px;
    height: 26px;
    cursor: pointer;
    border-radius: 4px;
    color: #999999;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: underline;
   }
   .do:hover{
    color:#FFFFFF
   }

 }
</style>
