<template>
  <div class="HardWareError-container">
    <el-collapse v-model="activeCollapse_error" @change="emitChangeFn">
      <el-collapse-item :title="$t('hardwareInfo.hardwareOverview')"  name="1" class="as">
        <template #title>
          <div class="hardware-all-l2-title flex-items-center">
            <span class="iconfont icon-hardware" style="color: #3579D5;font-size: 24px;margin-right: 5px;"></span>
            <span style="color: #ffffff">{{ $t("hardwareInfo.hardwareOverview") }}</span>

            <span class="ml-auto"></span>
          </div>
        </template>
        <div>
          <h1 class="errorTitle">
            <el-icon :size="14" color="#BF4040">
              <Warning />
            </el-icon>
            <span>{{ $t("hardwareInfo.loadHwinfo_SDK") }}</span>
          </h1>
          <h2 class="errReason">{{ $t("hardwareInfo.loadHwinfo_SDK_reason") }}</h2>
          <ul class="errReasonList">
            <li>
              <section class="problem">
                <span>{{ $t("hardwareInfo.reason") }}1: </span>
                <text>{{ $t("hardwareInfo.BlockIntercept") }}</text>
              </section>
              <section class="solution">
                <span>{{ $t("hardwareInfo.solution") }}</span>
                <text>{{ $t("hardwareInfo.solution1") }}</text>
              </section>
            </li>
            <li>
              <section class="problem">
                <span>{{ $t("hardwareInfo.reason") }}2: </span>
                <text>{{ $t("hardwareInfo.solution2") }} </text>
              </section>
              <section class="solution">
                <span>{{ $t("hardwareInfo.solution") }}</span>
                <text>{{ $t("hardwareInfo.RestartGamePP") }}</text>
              </section>
            </li>
            <li>
              <section class="problem">
                <span>{{ $t("hardwareInfo.reason") }}3: </span>
                <text>{{ $t("hardwareInfo.HWINFOcannotrun") }}</text>
              </section>
              <section class="solution">
                <span>{{ $t("hardwareInfo.solution") }}</span>
                <text><a href="https://www.hwinfo.com/" target="_blank">{{ $t("hardwareInfo.downloadHWINFO") }}</a> {{ $t("hardwareInfo.openHWINFO") }}</text>
              </section>
            </li>
            <li>
              <section class="problem">
                <span>{{ $t("hardwareInfo.reason") }}4: </span>
                <text>{{ $t("hardwareInfo.hardwareDriverProblem") }}</text>
              </section>
              <section class="solution">
                <span>{{ $t("hardwareInfo.solution") }}</span>
                <text>{{ $t("hardwareInfo.checkHardwareManager") }}</text>
              </section>
            </li>
            <li>
              <section class="problem">
                <span>{{ $t("hardwareInfo.reason") }}5: </span>
                <text>{{ $t("hardwareInfo.systemProblem") }}</text>
              </section>
              <section class="solution">
                <span>{{ $t("hardwareInfo.solution") }}</span>
                <text>{{ $t("hardwareInfo.reinstallSystem") }}</text>
              </section>
            </li>
            <li>
              <section class="problem">
                <span>{{ $t("hardwareInfo.reason") }}6: </span>
                <text>{{ $t("hardwareInfo.Windows7") }}</text>
              </section>
              <section class="solution">
                <span>{{ $t("hardwareInfo.solution") }}</span>
                <text>{{ $t("hardwareInfo.ViolenceActivator") }}</text>
              </section>
            </li>
            <li>
              <section class="problem">
                <span>{{ $t("hardwareInfo.reason") }}7: </span>
                <text>{{ $t("hardwareInfo.MultipleGraphicsCardDrivers") }} </text>
              </section>
              <section class="solution">
                <span>{{ $t("hardwareInfo.solution") }}</span>
                <text>{{ $t("hardwareInfo.UninstallUnused") }}</text>
              </section>
            </li>
          </ul>
          <span style="color: #fff;margin: 20px 0;font-size: 12px;">{{ $t("hardwareInfo.OfficialQgroup") }}</span>

          <div class="bottom_btn">
            <el-button style="width: 100px;" @click="exportBaseXml">{{ $t("hardwareInfo.ExportHardwareData") }}</el-button>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup lang="ts">
import {defineProps, onMounted, ref} from "vue";
import {Warning} from "@element-plus/icons-vue";

const activeCollapse_error = ref(["1"])
const props = defineProps({
  changeMenuInfo: {
    type: Function,
    required: true
  }
})

onMounted(() => {
  setTimeout(()=>{
    emitChangeFn();
  },200)
  listenHardwareChangeMsg()
})
function listenHardwareChangeMsg() {
  const hw = new BroadcastChannel('hw')
  hw.onmessage = (e:any)=>{
    if (e.data.action && e.data.action == 'change') {
      emitChangeFn();
    }
  }
}
const emitChangeFn = () => {
  const h = activeCollapse_error.value.length > 0 ? 733 : 72
  props.changeMenuInfo(0, h);
}

async function exportBaseXml () {
  let str = await gamepp.hardware.getBaseXmlInfo.promise();
  if (str.length <= 10) {
    ElMessage.error('HWINFO数据读取失败,请检查常见问题!');
    return false
  }
  await navigator.clipboard.writeText(str);
  ElMessage('导出数据已保存到粘贴板!');
}
</script>

<style scoped lang="scss">
.HardWareError-container {
  width: 100%;
  background: rgba(45 ,46 ,57, 0.8);
  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  padding: 0 25px 0 20px;
  font-size: 12px;

  .hardware-all-l2-title {
    width: 100%;
  }

  .errorTitle {
    margin-bottom: 20px;
    font-weight: bold;
    font-size: 12px;
    color: #BF4040;
    line-height: 20px;
    display: flex;
    align-items: center;
    span {
      margin-left: 10px;
    }
  }

  .errReason {
    font-weight: 400;
    font-size: 12px;
    color: #777777;
    line-height: 20px;
    margin-bottom: 18px;
  }

  .errReasonList {
    display: flex;
    flex-flow: column nowrap;

    li {
      margin-bottom: 25px;
    }

    .problem,.solution {
      display: flex;
      flex-flow: row nowrap;
      font-size: 12px;
      span {
        flex-shrink: 0;
        width: 71px;
      }
    }
    .problem {
      color: #ffffff;
    }
    .solution {
      color: #777777;
    }
  }

  .bottom_btn {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    margin-bottom: 10px;
  }
}

.child-space-right-10 {
  & > * {
    margin-right: 10px;
  }
}

.child-space-right-5 {
  & > * {
    margin-right: 5px;
  }
}

.color777 {
  color: #777777;
}
</style>

<style>
.HardWareError-container{
    .el-button {
        border: 2px solid #3579D5;
        color: #3579D5;
        background: transparent;
        &:hover {
            background-color: #3579D5;
            border-color: #3579D5;
            color: #ffffff;
        }
    }
}
</style>
