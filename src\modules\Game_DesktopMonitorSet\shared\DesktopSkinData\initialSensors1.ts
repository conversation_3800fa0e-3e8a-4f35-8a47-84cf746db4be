
import { Sensor } from '../../../Game_DMComponent/sharedTypes';
import cpu_temp from '../../../../assets/img/monitoring/img1/ic_3dmonitoring_cpu_temp.png';
import cpu_fan from '../../../../assets/img/monitoring/img1/ic_3dmonitoring_cpu_fan.png';
import cpu_clock from '../../../../assets/img/monitoring/img1/ic_3dmonitoring_cpu_clock.png';
import cpu_base from '../../../../assets/img/monitoring/img1/img_3dmonitoring_cpu_base.png';
import cpu_power from '../../../../assets/img/monitoring/img1/ic_3dmonitoring_cpu_power.png';
import cpu_breathing_light from '../../../../assets/img/monitoring/img1/img_3dmonitoring_cpu_breathing_light.png';
import cpu_pattern from '../../../../assets/img/monitoring/img1/img_3dmonitoring_cpu_pattern.png';
import gpu_temp from '../../../../assets/img/monitoring/img1/ic_3dmonitoring_gpu_temp.png';
import gpu_fan from '../../../../assets/img/monitoring/img1/ic_3dmonitoring_gpu_fan.png';
import gpu_clock from '../../../../assets/img/monitoring/img1/ic_3dmonitoring_gpu_clock.png';
import gpu_base from '../../../../assets/img/monitoring/img1/img_3dmonitoring_gpu_base.png';
import gpu_power from '../../../../assets/img/monitoring/img1/ic_3dmonitoring_gpu_power.png';
import gpu_breathing_light from '../../../../assets/img/monitoring/img1/img_3dmonitoring_gpu_breathing_light.png';
import gpu_pattern from '../../../../assets/img/monitoring/img1/img_3dmonitoring_gpu_pattern.png';
import vram_breathing_light from '../../../../assets/img/monitoring/img1/img_3dmonitoring_vram_breathing_light.png';
import vram_pattern from '../../../../assets/img/monitoring/img1/img_3dmonitoring_dram_pattern.png';
import net_upload from '../../../../assets/img/monitoring/img1/ic_3dmonitoring_net_upload.png';

export const initialSensors: Sensor[] = [
  {
    id: 1,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'GAMEPP',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 61,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 28,
      color: 'rgba(73,231,231,1)',
      top: 5,
      left: 117,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 2,
    nums: 6,
    type: '时间',
    type2: 'time',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    timeFormat: 0,
    timeRule: 1,
    timeType: 1,
    style: {
      zIndex: 60,
      fontSize: 28,
      fontFamily: 'QuartzRegular',
      top: 5,
      left: 222,
      color: 'rgba(73,231,231,1)',
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 3,
    nums: 5,
    type: 'SVG',
    type2: 'svg',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unitshow: true,
    processedSvg: "<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" viewBox=\"0 0 459  24\">\n    <path fill-rule=\"evenodd\" fill=\"rgb(105, 255, 255)\" stroke=\"rgb(105, 255, 255)\" d=\"M-0.000,22.1000 L454.000,22.1000 L454.000,24.000 L-0.000,24.000 L-0.000,22.1000 Z\"/>\n    <path fill-rule=\"evenodd\" opacity=\"0.8\" fill=\"rgb(105, 255, 255)\" d=\"M-0.000,16.1000 L92.986,16.1000 L92.986,18.000 L-0.000,18.000 L-0.000,16.1000 Z\"/>\n    <path fill-rule=\"evenodd\" opacity=\"0.6\" fill=\"rgb(105, 255, 255)\" d=\"M-0.000,10.1000 L92.986,10.1000 L92.986,12.000 L-0.000,12.000 L-0.000,10.1000 Z\"/>\n    <path fill-rule=\"evenodd\" opacity=\"0.4\" fill=\"rgb(105, 255, 255)\" d=\"M-0.000,4.1000 L92.986,4.1000 L92.986,6.000 L-0.000,6.000 L-0.000,4.1000 Z\"/>\n    <path fill-rule=\"evenodd\" opacity=\"0.2\" fill=\"rgb(105, 255, 255)\" d=\"M-0.000,-0.000 L92.986,-0.000 L92.986,1.000 L-0.000,1.000 L-0.000,-0.000 Z\"/>\n</svg>",
    style: {
      zIndex: 59,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      strokeColor: "rgba(255, 255, 255, 1)",
      pathFills: ["rgb(105, 255, 255)", "rgb(105, 255, 255)", "rgb(105, 255, 255)", "rgb(105, 255, 255)", "rgb(105, 255, 255)"],
      fontSize: 12,
      color: '#ffffff',
      width: 454,
      height: 24,
      top: 14,
      left: 8,
    },
  },
  {
    id: 4,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'CPU',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 58,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 36,
      color: 'rgba(73,231,231,1)',
      top: 51,
      left: 18,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 5,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: cpu_temp,
    transformShow: false,
    style: {
      zIndex: 57,
      fontSize: 12,
      top: 97,
      left: 20,
      width: 20,
      height: 9,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 6,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'TEMP',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 56,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 14,
      color: 'rgba(73,231,231,1)',
      top: 93,
      left: 46,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 7,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'CPU Temp',
    parameters: 'temp',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: '°C',
    unitshow: true,
    style: {
      zIndex: 55,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(73,231,231,1)',
      top: 108,
      left: 20,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 8,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: 'CPU Fan',
    parameters: 'fan',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: cpu_fan,
    transformShow: true,
    style: {
      zIndex: 54,
      fontSize: 12,
      top: 136,
      left: 20,
      width: 14,
      height: 14,
      Interval: 2000,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 9,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'FAN',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 53,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 14,
      color: 'rgba(73,231,231,1)',
      top: 135,
      left: 44,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 10,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'CPU Fan',
    parameters: 'fan',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'RPM',
    unitshow: true,
    style: {
      zIndex: 52,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(73,231,231,1)',
      top: 153,
      left: 20,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 11,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: cpu_clock,
    transformShow: false,
    style: {
      zIndex: 51,
      fontSize: 12,
      top: 182,
      left: 20,
      width: 15,
      height: 8,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 12,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'CLOCK',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 50,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 14,
      color: 'rgba(73,231,231,1)',
      top: 176,
      left: 46,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 13,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'CPU Clock',
    parameters: 'clock',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'MHz',
    unitshow: true,
    style: {
      zIndex: 49,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(73,231,231,1)',
      top: 194,
      left: 20,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 14,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'CPU Usage',
    parameters: 'usage',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: '%',
    unitshow: false,
    style: {
      zIndex: 48,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 130,
      color: 'rgba(73,231,231,1)',
      top: 23,
      left: 166,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 15,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: '%',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 47,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 24,
      color: 'rgba(73,231,231,1)',
      top: 56,
      left: 300,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 16,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: cpu_base,
    transformShow: false,
    style: {
      zIndex: 46,
      fontSize: 12,
      top: 147,
      left: 98,
      width: 276,
      height: 74,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 17,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: cpu_power,
    transformShow: false,
    style: {
      zIndex: 45,
      fontSize: 12,
      top: 167,
      left: 392,
      width: 8,
      height: 16,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 18,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'POWER',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 44,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 14,
      color: 'rgba(73,231,231,1)',
      top: 167,
      left: 408,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 19,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'CPU Power',
    parameters: 'power',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'W',
    unitshow: true,
    style: {
      zIndex: 43,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(73,231,231,1)',
      top: 188,
      left: 392,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 20,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    class: 'breath',
    mediaSrc: cpu_breathing_light,
    transformShow: false,
    style: {
      zIndex: 42,
      fontSize: 12,
      top: 51,
      left: 15,
      width: 440,
      height: 163,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 21,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: cpu_pattern,
    transformShow: false,
    style: {
      zIndex: 41,
      fontSize: 12,
      top: 39,
      left: 0,
      width: 468,
      height: 190,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 22,
    nums: 5,
    type: 'SVG',
    type2: 'svg',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unitshow: true,
    processedSvg: "<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"477px\" height=\"190px\" viewBox=\"-1 -7 467 190\">\n            <path fill-rule=\"evenodd\" id=\"borderPath\" stroke=\"rgb(118, 248, 255)\" stroke-width=\"1px\" stroke-linecap=\"butt\" stroke-linejoin=\"miter\" opacity=\"0.8\" fill=\"rgba(255, 255, 255, 0)\" d=\"M426.824,176.500 L0.500,176.500 L0.500,0.500 L454.500,0.500 L454.500,145.927 L426.824,176.500 Z\"/>\n            <path fill-rule=\"evenodd\" fill=\"rgb(72, 217, 217)\" d=\"M455.430,147.500 C455.306,147.834 455.133,148.155 454.888,148.441 L429.718,177.883 C428.709,179.062 427.013,179.290 425.733,178.500 L384.500,178.500 L384.500,173.500 L425.288,173.500 L450.165,144.403 C450.534,143.971 450.1000,143.678 451.500,143.503 L451.500,107.500 L456.500,107.500 L456.500,147.500 L455.430,147.500 Z\"/>\n            <defs>\n                <radialGradient id=\"circleGradient\" cx=\"50%\" cy=\"50%\" r=\"50%\" fx=\"50%\" fy=\"50%\">\n                    <stop offset=\"0%\" style=\"stop-color:#87FFFF;\"/>\n                    <stop offset=\"100%\" style=\"stop-color:rgba(135, 255, 255,.1)\"/>\n                </radialGradient>\n            </defs>\n            <circle cx=\"0\" cy=\"0\" r=\"6\" fill=\"url(#circleGradient)\" stroke=\"rgba(135, 255, 255,.3)\" stroke-width=\"1\">\n                <animateMotion dur=\"5s\" repeatCount=\"indefinite\">\n                <mpath xlink:href=\"#borderPath\"/>\n                </animateMotion>\n            </circle>\n        </svg>",
    style: {
      zIndex: 40,
      fontSize: 12,
      width: 477,
      height: 190,
      top: 37,
      left: 0,
      strokeColor: "rgb(118, 248, 255)",
      pathFills: ["rgba(255, 255, 255, 0)", "rgb(72, 217, 217)"],
    },
  },


  {
    id: 23,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'GPU',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 39,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 36,
      color: 'rgba(251,172,20,1)',
      top: 239,
      left: 18,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 24,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: gpu_temp,
    transformShow: false,
    style: {
      zIndex: 38,
      fontSize: 12,
      top: 286,
      left: 20,
      width: 20,
      height: 9,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 25,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'TEMP',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 37,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 14,
      color: 'rgba(251,172,20,1)',
      top: 281,
      left: 46,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 26,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'GPU Temp',
    parameters: 'temp',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: '°C',
    unitshow: true,
    style: {
      zIndex: 36,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(251,172,20,1)',
      top: 298,
      left: 20,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 27,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: 'GPU Fan',
    parameters: 'fan',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: gpu_fan,
    transformShow: true,
    style: {
      zIndex: 35,
      fontSize: 12,
      top: 324,
      left: 20,
      width: 14,
      height: 14,
      Interval: 1600,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 28,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'FAN',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 34,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 14,
      color: 'rgba(251,172,20,1)',
      top: 323,
      left: 46,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 29,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'GPU Fan',
    parameters: 'fan',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'RPM',
    unitshow: true,
    style: {
      zIndex: 33,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(251,172,20,1)',
      top: 341,
      left: 20,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 30,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: gpu_clock,
    transformShow: false,
    style: {
      zIndex: 32,
      fontSize: 12,
      top: 369,
      left: 20,
      width: 15,
      height: 8,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 31,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'GLOCK',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 31,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 14,
      color: 'rgba(251,172,20,1)',
      top: 364,
      left: 46,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 32,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'GPU Clock',
    parameters: 'clock',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'MHz',
    unitshow: true,
    style: {
      zIndex: 30,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(251,172,20,1)',
      top: 380,
      left: 20,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 33,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'GPU Usage',
    parameters: 'total_usage',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: '%',
    unitshow: false,
    style: {
      zIndex: 29,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 130,
      color: 'rgba(251,172,20,1)',
      top: 215,
      left: 166,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 34,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: '%',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 28,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 24,
      color: 'rgba(251,172,20,1)',
      top: 246,
      left: 300,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 35,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: gpu_base,
    transformShow: false,
    style: {
      zIndex: 27,
      fontSize: 12,
      top: 335,
      left: 98,
      width: 276,
      height: 74,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 36,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: gpu_power,
    transformShow: false,
    style: {
      zIndex: 26,
      fontSize: 12,
      top: 355,
      left: 392,
      width: 8,
      height: 16,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 37,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'POWER',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 25,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 14,
      color: 'rgba(251,172,20,1)',
      top: 355,
      left: 406,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 38,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'GPU Power',
    parameters: 'power',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'W',
    unitshow: true,
    style: {
      zIndex: 24,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 12,
      color: 'rgba(251,172,20,1)',
      top: 376,
      left: 392,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 39,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    class: 'breath',
    mediaSrc: gpu_breathing_light,
    transformShow: false,
    style: {
      zIndex: 23,
      fontSize: 12,
      top: 239,
      left: 15,
      width: 440,
      height: 163,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 40,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: gpu_pattern,
    transformShow: false,
    style: {
      zIndex: 22,
      fontSize: 12,
      top: 228,
      left: 0,
      width: 468,
      height: 190,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 41,
    nums: 5,
    type: 'SVG',
    type2: 'svg',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unitshow: true,
    processedSvg: "<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\"  viewBox=\"-1 -7 467 190\">\n<path fill-rule=\"evenodd\" id=\"borderPath2\" stroke=\"rgb(251, 172, 20)\" stroke-width=\"1px\" stroke-linecap=\"butt\" stroke-linejoin=\"miter\" opacity=\"0.8\" fill=\"rgba(255, 255, 255, 0)\" d=\"M427.052,176.500 L0.500,176.500 L0.500,0.500 L454.500,0.500 L454.500,146.135 L427.052,176.500 Z\"/>\n<path fill-rule=\"evenodd\" fill=\"rgb(251, 172, 20)\" d=\"M455.430,147.500 C455.306,147.834 455.133,148.155 454.888,148.441 L429.718,177.883 C428.709,179.062 427.013,179.290 425.733,178.500 L384.500,178.500 L384.500,173.500 L425.288,173.500 L450.165,144.403 C450.534,143.971 450.1000,143.678 451.500,143.503 L451.500,107.500 L456.500,107.500 L456.500,147.500 L455.430,147.500 Z\"/>\n\n<defs>\n    <radialGradient id=\"circleGradient2\" cx=\"50%\" cy=\"50%\" r=\"50%\" fx=\"50%\" fy=\"50%\">\n        <stop offset=\"0%\" style=\"stop-color:#FBAC14;\"/>\n        <stop offset=\"100%\" style=\"stop-color:rgba(251, 172, 20,.1)\"/>\n    </radialGradient>\n</defs>\n<circle cx=\"0\" cy=\"0\" r=\"6\" fill=\"url(#circleGradient2)\" stroke=\"rgba(251, 172, 20,.3)\" stroke-width=\"1\">\n    <animateMotion dur=\"7s\" repeatCount=\"indefinite\">\n    <mpath xlink:href=\"#borderPath2\"/>\n    </animateMotion>\n</circle>\n</svg>",
    style: {
      zIndex: 21,
      fontSize: 12,
      width: 477,
      height: 190,
      top: 225,
      left: 0,
      strokeColor: "rgb(118, 248, 255)",
      pathFills: ["rgba(255, 255, 255, 0)", "rgb(251, 172, 20)"]
    },
  },
  {
    id: 42,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'DRAM',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 20,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 36,
      color: 'rgba(251,172,20,1)',
      top: 427,
      left: 20,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 43,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'DRAM Usage',
    parameters: 'usage_mb',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'GB',
    unitshow: true,
    style: {
      zIndex: 19,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 24,
      color: 'rgba(251,172,20,1)',
      top: 434,
      left: 290,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },

  {
    id: 44,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 18,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 24,
      color: 'rgba(251,172,20,1)',
      top: 434,
      left: 352,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },

  {
    id: 45,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'DRAM Total',
    parameters: 'size',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'GB',
    unitshow: true,
    style: {
      zIndex: 17,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 24,
      color: 'rgba(251,172,20,1)',
      top: 434,
      left: 376,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },

  {
    id: 46,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    class: 'breath3',
    mediaSrc: vram_breathing_light,
    transformShow: false,
    style: {
      zIndex: 16,
      fontSize: 12,
      top: 427,
      left: 13,
      width: 440,
      height: 44,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 47,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: vram_pattern,
    transformShow: false,
    style: {
      zIndex: 15,
      fontSize: 12,
      top: 423,
      left: 0,
      width: 454,
      height: 53,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 48,
    nums: 5,
    type: 'SVG',
    type2: 'svg',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unitshow: true,
    processedSvg: "<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"477px\" height=\"67px\" viewBox=\"-1 -6 467 67\">\n<path fill-rule=\"evenodd\" id=\"borderPath3\" stroke=\"rgb(251, 172, 20)\" stroke-width=\"1px\" stroke-linecap=\"butt\" stroke-linejoin=\"miter\" opacity=\"0.8\" fill=\"rgba(255, 255, 255, 0)\" d=\"M443.475,53.500 L0.500,53.500 L0.500,0.500 L454.500,0.500 L454.500,41.304 L443.475,53.500 Z\"/>\n<defs>\n    <radialGradient id=\"circleGradient2\" cx=\"50%\" cy=\"50%\" r=\"50%\" fx=\"50%\" fy=\"50%\">\n        <stop offset=\"0%\" style=\"stop-color:#FBAC14;\"/>\n        <stop offset=\"100%\" style=\"stop-color:rgba(251, 172, 20,.1)\"/>\n    </radialGradient>\n</defs>\n<circle cx=\"0\" cy=\"0\" r=\"6\" fill=\"url(#circleGradient2)\" stroke=\"rgba(251, 172, 20,.3)\" stroke-width=\"1\">\n    <animateMotion dur=\"7s\" repeatCount=\"indefinite\">\n    <mpath xlink:href=\"#borderPath3\"/>\n    </animateMotion>\n</circle>\n</svg>",
    style: {
      zIndex: 14,
      fontSize: 12,
      width: 477,
      height: 67,
      top: 416,
      left: 0,
      strokeColor: "rgb(251, 172, 20)",
      pathFills: ["rgba(255, 255, 255, 0)"]
    },
  },
  {
    id: 49,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'VRAM',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 13,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 36,
      color: 'rgba(251,172,20,1)',
      top: 491,
      left: 20,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 50,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'VRAM Usage mb',
    parameters: 'mem_usage_mb',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'GB',
    unitshow: true,
    style: {
      zIndex: 12,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 24,
      color: 'rgba(251,172,20,1)',
      top: 497,
      left: 290,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 51,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 11,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 24,
      color: 'rgba(251,172,20,1)',
      top: 497,
      left: 352,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 52,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'VRAM Total',
    parameters: 'mem_size',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'GB',
    unitshow: true,
    style: {
      zIndex: 10,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 24,
      color: 'rgba(251,172,20,1)',
      top: 497,
      left: 375,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 53,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    class: 'breath3',
    mediaSrc: vram_breathing_light,
    transformShow: false,
    style: {
      zIndex: 9,
      fontSize: 12,
      top: 491,
      left: 13,
      width: 440,
      height: 44,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 54,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: vram_pattern,
    transformShow: false,
    style: {
      zIndex: 8,
      fontSize: 12,
      top: 486,
      left: 0,
      width: 454,
      height: 53,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 55,
    nums: 5,
    type: 'SVG',
    type2: 'svg',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unitshow: true,
    processedSvg: "<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"477px\" height=\"67px\" viewBox=\"-1 -6 467 67\">\n<path fill-rule=\"evenodd\" id=\"borderPath3\" stroke=\"rgb(251, 172, 20)\" stroke-width=\"1px\" stroke-linecap=\"butt\" stroke-linejoin=\"miter\" opacity=\"0.8\" fill=\"rgba(255, 255, 255, 0)\" d=\"M443.475,53.500 L0.500,53.500 L0.500,0.500 L454.500,0.500 L454.500,41.304 L443.475,53.500 Z\"/>\n<defs>\n    <radialGradient id=\"circleGradient2\" cx=\"50%\" cy=\"50%\" r=\"50%\" fx=\"50%\" fy=\"50%\">\n        <stop offset=\"0%\" style=\"stop-color:#FBAC14;\"/>\n        <stop offset=\"100%\" style=\"stop-color:rgba(251, 172, 20,.1)\"/>\n    </radialGradient>\n</defs>\n<circle cx=\"0\" cy=\"0\" r=\"6\" fill=\"url(#circleGradient2)\" stroke=\"rgba(251, 172, 20,.3)\" stroke-width=\"1\">\n    <animateMotion dur=\"3s\" repeatCount=\"indefinite\">\n    <mpath xlink:href=\"#borderPath3\"/>\n    </animateMotion>\n</circle>\n</svg>",
    style: {
      zIndex: 7,
      fontSize: 12,
      width: 477,
      height: 67,
      top: 480,
      left: 0,
      strokeColor: "rgb(251, 172, 20)",
      pathFills: ["rgba(255, 255, 255, 0)"]
    },
  },
  {
    id: 56,
    nums: 1,
    type: '自定义文字',
    type2: 'text',
    remark: 'NET',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 6,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 24,
      color: 'rgba(251,172,20,1)',
      top: 554,
      left: 110,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)'
      },
    },
  },
  {
    id: 57,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    mediaSrc: net_upload,
    transformShow: false,
    style: {
      zIndex: 5,
      fontSize: 12,
      top: 560,
      left: 225,
      width: 11,
      height: 18,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 58,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'network',
    parameters: 'upload',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'KB/s',
    unitshow: true,
    style: {
      zIndex: 4,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 18,
      color: 'rgba(251,172,20,1)',
      top: 558,
      left: 245,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 59,
    nums: 3,
    type: '图片',
    type2: 'img',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    class: 'rotate180',
    mediaSrc: net_upload,
    transformShow: false,
    style: {
      zIndex: 3,
      fontSize: 12,
      top: 561,
      left: 355,
      width: 11,
      height: 18,
      Interval: 0,
      animation: {
        names: 0,
        names2: 1,
        duration:0, 
        duration2: 1,
      }
    },
  },
  {
    id: 60,
    nums: 2,
    type: '传感器',
    type2: 'sensor',
    remark: '/',
    sensor: 'network',
    parameters: 'download',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unit: 'KB/s',
    unitshow: true,
    style: {
      zIndex: 2,
      fontFamily: 'QuartzRegular',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: 18,
      color: 'rgba(251,172,20,1)',
      top: 558,
      left: 375,
      shadow: {
        enabled: false,
        x: 0,
        y: 1,
        blur: 6,
        color: 'rgba(255,255,255,1)',
      },
    },
  },
  {
    id: 61,
    nums: 7,
    type: '动画',
    type2: 'animation',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    style: {
      zIndex: 1,
      fontSize: 12,
      top: 582,
      left: 108,
      width: 352,
      height: 20,
      color: '#FBAC14',
      seconds: 2,
    },
  },
  {
    id: 62,
    nums: 5,
    type: 'SVG',
    type2: 'svg',
    remark: '/',
    sensor: '/',
    page: 1,
    group: false,
    showDetails: true,
    hideDetails: false,
    settop: false,
    showdelete: false,
    enter: true,
    unitshow: true,
    processedSvg: "<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" viewBox=\"-1 -7 459 62\">\n<path fill-rule=\"evenodd\" id=\"borderPath4\" fill=\"rgb(251, 172, 20)\" d=\"M-0.000,1.000 L443.000,1.000 L443.000,-0.000 L-0.000,-0.000 L-0.000,1.000 Z\"/>\n<path fill-rule=\"evenodd\" opacity=\"0.8\" fill=\"rgb(251, 172, 20)\" d=\"M-0.000,7.000 L92.986,7.000 L92.986,5.1000 L-0.000,5.1000 L-0.000,7.000 Z\"/>\n<path fill-rule=\"evenodd\" opacity=\"0.6\" fill=\"rgb(251, 172, 20)\" d=\"M-0.000,13.000 L92.986,13.000 L92.986,11.1000 L-0.000,11.1000 L-0.000,13.000 Z\"/>\n<path fill-rule=\"evenodd\" opacity=\"0.4\" fill=\"rgb(251, 172, 20)\" d=\"M-0.000,19.000 L92.986,19.000 L92.986,17.1000 L-0.000,17.1000 L-0.000,19.000 Z\"/>\n<path fill-rule=\"evenodd\" opacity=\"0.2\" fill=\"rgb(251, 172, 20)\" d=\"M-0.000,25.000 L92.986,25.000 L92.986,23.1000 L-0.000,23.1000 L-0.000,25.000 Z\"/>\n<path fill-rule=\"evenodd\" opacity=\"0.8\" fill=\"rgb(251, 172, 20)\" d=\"M-0.000,32.000 L92.986,32.000 L92.986,30.1000 L-0.000,30.1000 L-0.000,32.000 Z\"/>\n<path fill-rule=\"evenodd\" opacity=\"0.6\" fill=\"rgb(251, 172, 20)\" d=\"M-0.000,38.000 L92.986,38.000 L92.986,36.1000 L-0.000,36.1000 L-0.000,38.000 Z\"/>\n<path fill-rule=\"evenodd\" opacity=\"0.4\" fill=\"rgb(251, 172, 20)\" d=\"M-0.000,44.000 L92.986,44.000 L92.986,42.1000 L-0.000,42.1000 L-0.000,44.000 Z\"/>\n<path fill-rule=\"evenodd\" opacity=\"0.2\" fill=\"rgb(251, 172, 20)\" d=\"M-0.000,50.000 L92.986,50.000 L92.986,48.1000 L-0.000,48.1000 L-0.000,50.000 Z\"/>\n<path fill-rule=\"evenodd\" id=\"borderPath4\" fill=\"rgb(251, 172, 20)\" d=\"M-0.000,57.000 L443.000,57.000 L443.000,55.1000 L-0.000,55.1000 L-0.000,57.000 Z\"/>\n</svg>",
    style: {
      zIndex: 0,
      fontSize: 12,
      width: 460,
      height: 64,
      top: 542,
      left: 5,
      strokeColor: "rgb(251, 172, 20)",
      pathFills: ["rgb(251, 172, 20)", "rgb(251, 172, 20)", "rgb(251, 172, 20)", "rgb(251, 172, 20)", "rgb(251, 172, 20)", "rgb(251, 172, 20)", "rgb(251, 172, 20)", "rgb(251, 172, 20)", "rgb(251, 172, 20)"]
    },
  },
];
