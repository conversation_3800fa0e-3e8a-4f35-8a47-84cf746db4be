(function(e){function n(n){for(var r,u,c=n[0],i=n[1],s=n[2],f=0,p=[];f<c.length;f++)u=c[f],Object.prototype.hasOwnProperty.call(o,u)&&o[u]&&p.push(o[u][0]),o[u]=0;for(r in i)Object.prototype.hasOwnProperty.call(i,r)&&(e[r]=i[r]);l&&l(n);while(p.length)p.shift()();return a.push.apply(a,s||[]),t()}function t(){for(var e,n=0;n<a.length;n++){for(var t=a[n],r=!0,c=1;c<t.length;c++){var i=t[c];0!==o[i]&&(r=!1)}r&&(a.splice(n--,1),e=u(u.s=t[0]))}return e}var r={},o={index:0},a=[];function u(n){if(r[n])return r[n].exports;var t=r[n]={i:n,l:!1,exports:{}};return e[n].call(t.exports,t,t.exports,u),t.l=!0,t.exports}u.m=e,u.c=r,u.d=function(e,n,t){u.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:t})},u.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},u.t=function(e,n){if(1&n&&(e=u(e)),8&n)return e;if(4&n&&"object"===typeof e&&e&&e.__esModule)return e;var t=Object.create(null);if(u.r(t),Object.defineProperty(t,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var r in e)u.d(t,r,function(n){return e[n]}.bind(null,r));return t},u.n=function(e){var n=e&&e.__esModule?function(){return e["default"]}:function(){return e};return u.d(n,"a",n),n},u.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},u.p="";var c=window["webpackJsonp"]=window["webpackJsonp"]||[],i=c.push.bind(c);c.push=n,c=c.slice();for(var s=0;s<c.length;s++)n(c[s]);var l=i;a.push([0,"chunk-vendors","chunk-common"]),t()})({0:function(e,n,t){e.exports=t("47b5")},"152c":function(e,n,t){"use strict";var r=t("8bbb"),o=t.n(r);o.a},"47b5":function(e,n,t){"use strict";t.r(n);t("0fae");var r=t("9e2f"),o=t.n(r),a=(t("e260"),t("e6cf"),t("cca6"),t("a79d"),t("2b0e")),u=function(){var e=this,n=e.$createElement,t=e._self._c||n;return t("div",{staticClass:"index"},[t("div",{staticClass:"container"},[t("router-view")],1)])},c=[],i=(t("96cf"),t("1da1")),s={name:"index",components:{},data:function(){return{}},methods:{},created:function(){var e=this;return Object(i["a"])(regeneratorRuntime.mark((function n(){var t;return regeneratorRuntime.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,gamepp.setting.getString.promise(242);case 2:t=n.sent,console.log("Lan",t),n.t0=t,n.next="CN"===n.t0?7:"EN"===n.t0?10:13;break;case 7:return console.log("RushHouer:CN"),e.$i18n.locale="zh_CN",n.abrupt("break",14);case 10:return e.$i18n.locale="en_US",console.log("RushHouer:EN"),n.abrupt("break",14);case 13:return n.abrupt("break",14);case 14:case"end":return n.stop()}}),n)})))()},mounted:function(){}},l=s,f=(t("152c"),t("2877")),p=Object(f["a"])(l,u,c,!1,null,"060f283a",null),d=p.exports,b=t("a18c"),h=t("4360"),v=t("bc3a"),g=t.n(v),m=t("a925");a["default"].config.productionTip=!1,a["default"].prototype.$axios=g.a,a["default"].use(m["a"]),a["default"].use(o.a);var y=new m["a"]({locale:"zh_CN",messages:{zh_CN:t("103b"),en_US:t("c60b")},silentTranslationWarn:!0});new a["default"]({router:b["a"],store:h["a"],i18n:y,render:function(e){return e(d)}}).$mount("#Index")},"8bbb":function(e,n,t){}});