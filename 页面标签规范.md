# 语义化标签使用规范
语义化标签    《保持一致的标签使用习惯》
使用合适的语义化标签，如`<main>` , `<header>`, `<nav>`, `<section>`, `<article>`, `<aside>`, `<footer> ` , `<details>` , `<summary>`等。
## main
`<main>` 表示文档或应用程序的主要内容

```html
<template>
  <main>
    <section class="home">
      <!-- 首页内容 -->
    </section>
    <section class="about">
      <!-- 关于我们内容 -->
    </section>
  </main>
</template>
```

## header
`<header>` 表示页面或页面区域的页眉。
```html
<template>
  <header>
    <h1>标题</h1>
  </header>
</template>
```
## nav
`<nav>` 表示导航链接的容器。
```html
<template>
  <nav>
    <ul>
      <li><a href="#home">首页</a></li>
      <li><a href="#about">关于我们</a></li>
      <li><a href="#services">服务</a></li>
    </ul>
  </nav>
</template>
```
## section
`<section>` 表示文档中的一个独立的区段。
```html
<template>
  <section class="about">
    <h2>关于我们</h2>
    <p>这里是关于我们公司的介绍。</p>
  </section>
</template>
```
## article
`<article>` 表示独立的自包含内容，如博客文章、新闻文章等。
```html
<template>
  <article>
    <header>  //头部页眉 用header规范 
      <h2>文章标题</h2>
    </header>
    <p>文章内容...</p>
  </article>
</template>
```
## aside
`<aside>` 表示与页面内容稍微相关的侧边栏内容，如广告、链接列表。
```html
<template>
  <aside>
    <h3>相关链接</h3>
    <ul>
      <li><a href="#">链接1</a></li>
      <li><a href="#">链接2</a></li>
    </ul>
  </aside>
</template>
```
## details  , summary
`<details>`  定义额外信息的容器。
`<summary>`  定义 `<details>` 元素的可见标题。 

<strong style="color:red">`<details>`必须包含一个 `<summary>` 元素作为其第一个子元素。</strong >

`<details>` 和 `<summary>`用于创建一个可以展开和收起的内容区域,方便查看或隐藏信息。
```html
  <details>
      <summary>什么是CSS？</summary>
      <p>层叠样式表</p>
      <p>它可以用来设置字体样式、颜色、间距、布局等。</p>
  </details>
```

## 表单和输入
使用 `<form>` 元素定义表单，包含 action 和 method 属性。使用 `<label>` 元素与 `<input>` 元素相关联，提高可访问性。使用 `<button>` 或 `<input type="submit">` 提交表单。

```html
<form action="/submit-form" method="post">
  <!-- 表单内容 -->
</form>
```
## 列表
使用  `<ul> ` 定义无序列表， `<ol> ` 定义有序列表。使用  `<li> ` 元素表示列表项。
```html
<ul>
  <li>列表项1</li>
  <li>列表项2</li>
</ul>
```

## 链接
使用 `<a>` 元素定义链接，包含 href 属性。
```html
<a href="https://12312312.com">链接文本</a>
```

## 图片和媒体
使用 `<img>` 元素加载图片，包含 src, alt 属性。使用 `<figure>` 和 `<figcaption>` 定义图像及其标题。
```html
<img src="image.jpg" alt="描述">
<figure>
  <img src="image.jpg" alt="描述">
  <figcaption>图像标题</figcaption>
</figure>
```

## 表格
使用 `<table>` 定义表格。使用 `<thead>`, `<tbody>`, `<tfoot>` 定义表格的头部、主体和脚注。使用 `<tr>` 定义行，`<th>` 定义表头单元格，`<td>` 定义单元格。
```html
<table>
  <thead>
    <!-- 表头内容 -->
  </thead>
  <tbody>
    <!-- 表体内容 -->
  </tbody>
  <tfoot>
    <!-- 表脚内容 -->
  </tfoot>
</table>
```

## div
`<div>` 是一个块级元素，用于为内容创建一个容器，用于布局和样式化。
```html
<div>
  <!-- 内容 -->
</div>
```

## h2
`<h2>` 定义 HTML 中的二级标题。
```html
<h2>章节标题</h2>
```
## span
`<span>` 标签是一个内联元素，用于对文档中的一小段内容进行分组和应用样式。 
```html
<span>特定文本</span>
```

## p
`<p>` 标签定义一个段落。 块级元素
```html
<p>这是一个段落。</p>
```
## em
`<em>` 表示强调。
```html
<em>强调文本</em>
```

## i
`<i>` 表示斜体文本。没有强调意
```html
<i>斜体文本</i>
```
## strong
`<strong>`：表示语气更为强烈的强调
```html
<p>所有人员 <strong>立即撤离</strong> 该区域。</p>
```

## 避免滥用和过度嵌套
避免使用 `<div>` 和 `<span>` 来模拟不存在的布局或样式。

避免过度嵌套，这可能影响页面性能和可读性、过度依赖这些标签来创建布局或模拟样式，可能会导致 HTML 结构混乱和难以维护。

示例:

滥用:
```html
<div style="text-align: center;">这是一段居中的文本。</div>
<div class="button">点击我</div>
```
改进:

```html
<p class>这是一段居中的文本。</p>
<button>点击我</button>
```

### 过度嵌套: 深层次的不必要嵌套，尤其是当多个嵌套层级可以简化时。

过度嵌套
```html
<div>
  <div>
    <div>
      <div>
        <p>这是一个段落。</p>
      </div>
    </div>
  </div>
</div>
```

改进: 简化嵌套
```html
<div>
  <p>这是一个段落。</p>
</div>
```

## css布局

利用 CSS Grid 和 Flexbox 布局技术来创建复杂的布局结构，而不需要依赖额外的 `<div>` 容器。

在需要添加装饰性元素时，可以使用 CSS 伪元素 ::before 和 ::after 来实现，而不是添加额外的 `<span>`。

## css 定位滥用

滥用定位布局可能会导致页面布局混乱、难以维护和性能问题

滥用position: absolute;
```html
<style>
.box {
  position: absolute;
  top: 20px;
  left: 20px;
  width: 100px;
  height: 100px;
}
</style>

/* 如果.box没有明确的相对定位的父元素，它将相对于文档的<html>或<body>元素定位 */
```
改进:
```html
/* 给父元素设置相对定位 */
<style>
  .parent {
    position: relative;
  }

  .box {
    position: absolute;
    top: 20px;
    left: 20px;
    width: 100px;
    height: 100px;
  }
</>  

<section class='parent'>
  <div class='box'></div>
</section>

/* 它将相对于父元素parent元素定位 */
```

过度使用position: fixed;
```html
<style>
  .box {
    position: fixed;
    top: 0;
    left: 0;
    width: 100px;
    height: 100px;
  }

   .box2 {
    position: fixed;
    top: 0;
    left: 0;
    width: 200px;
    height: 100px;
  }
</style>
/* 如果页面中有多个固定定位的元素，它们可能会互相重叠，导致布局混乱。*/
```

<strong>页面请加上重置css</strong>

简单示例
```html
<template>
  <div class="container">
    <header>
      <h1>{{ pageTitle }}</h1>
    </header>
    <main>
      <section class="content">
        <div v-if="loading" class="loading">Loading...</div>
        <div v-else>
          <article v-for="item in posts" :key="item.id" class="post">
            <h2>{{ item.title }}</h2>
            <p>{{ item.body }}</p>
          </article>
        </div>
      </section>
    </main>
    <footer>
      <p> 2024</p>
    </footer>
  </div>
</template>
```







