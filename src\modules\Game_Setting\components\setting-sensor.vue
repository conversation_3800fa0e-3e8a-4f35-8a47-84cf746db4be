<template>
  <!--传感器读数设置-->
  <div id="cgq"></div>
  <el-collapse-item :title="$t('Setting.sensorReadingSetting')" name="5">
    <div class="setting-item">
      <section class="left-box">
        <p class="setting-item-title">{{$t('Setting.defaultSensor')}}</p>
        <el-button  type="primary" @click="GPP_WindowOpen('hardware_setupsensor')">{{$t('Setting.setSensor')}}</el-button>

        <p class="setting-item-title mt-25">{{$t('Setting.refreshTime')}}</p>
        <el-select style="width: 180px;" v-model="store.state.refreshTime" @change="setRefreshTime">
          <el-option label="100ms" :value="100"></el-option>
          <el-option label="300ms" :value="300"></el-option>
          <el-option label="500ms" :value="500"></el-option>
          <el-option :label="`1000ms(${$t('Setting.recommend')})`" :value="1000"></el-option>
          <el-option label="2000ms" :value="2000"></el-option>
          <el-option label="3000ms" :value="3000"></el-option>
          <el-option label="5000ms" :value="5000"></el-option>
        </el-select>
        <p class="setting-item-title" style="margin-top: 15px;">{{$t('Setting.sensorMsg')}}</p>
      </section>
      <section class="right-box">
        <p class="setting-item-title">{{$t('Setting.exportData')}}</p>
        <el-button type="primary" @click="ExportHWiNFOData('hardware')">{{$t('Setting.exportHwData')}}</el-button>

        <p class="setting-item-title mt-25">{{$t('Setting.sensorError')}}</p>
        <el-button type="primary" @click="ClearCache">{{$t('Setting.clearCache')}}</el-button>
      </section>
    </div>
  </el-collapse-item>
</template>

<script setup lang="ts">
import {gameppBaseSetting} from '@/modules/Game_Home/stores/index'
import {useScroll} from '../hooks/useScroll'
import {GPP_SendStatics} from "@/uitls/sendstatics"
import { useI18n } from 'vue-i18n';
const { t } = useI18n();


useScroll('cgq');
const store = gameppBaseSetting();

const setRefreshTime = (val: number) => {
  if (val === 100) {
    GPP_SendStatics(100751)
  }else if(val === 300) {
    GPP_SendStatics(100752)
  }else if (val === 500) {
    GPP_SendStatics(100753)
  }else if (val === 1000) {
    GPP_SendStatics(100754)
  }else if (val === 2000) {
    GPP_SendStatics(100755)
  }else if (val === 3000) {
    GPP_SendStatics(100756)
  }else if (val === 5000) {
    GPP_SendStatics(100757)
  }
  store.actions.setRefreshTime(val);
}

function GPP_WindowOpen(name:string) {
  try{
    if (name === 'hardware_setupsensor') {
      GPP_SendStatics(100745)
    }
    gamepp.webapp.windows.show.promise(name)
  }catch (e) {
    console.warn(e)
  }
}
function warningMsg(msg:string) {
  ElMessage({
    message: h('p', { style: 'line-height: 1; font-size: 14px' }, [
      h('span', null, msg),
    ]),
    duration: 3000,
    type: "warning"
  })
}
function successMsg(msg:string,duration=3000) {
  ElMessage({
    message: h('p', { style: 'line-height: 1; font-size: 14px' }, [
      h('span', null, msg),
    ]),
    duration: duration,
    type: "success"
  })
}
//导出数据
async function ExportHWiNFOData (type:any) {
  GPP_SendStatics(100758)
  let str = '';
  let XmlInfo = await gamepp.hardware.getBaseXmlInfo.promise();
  let SensorInfo = await gamepp.hardware.getSensorInfo.promise();
  if (XmlInfo.length <= 10) {
    warningMsg(t('Setting.readHwinfoFail'))
    return false
  }
  if (SensorInfo.length <= 10) {
    warningMsg(t('Setting.readHwinfoFail'))
    return false
  }
  let Display = '显示器数据(getDisplayCardInfo): ' + "\n" + JSON.stringify(await gamepp.hardware.getDisplayCardInfo.promise()) + "\n"//显示器数据
  let MainDisk = '主硬盘数据(getMainDiskInfo): ' + "\n" + JSON.stringify(await gamepp.hardware.getMainDiskInfo.promise()) + "\n"//硬盘数据
  let DiskInfo = '硬盘数据(getDiskInfo): ' + "\n" + JSON.stringify(await gamepp.getDiskInfo.promise()) + "\n"//硬盘数据
  let chooseSensor_list_v1Str = '选择的传感器(chooseSensor_list_v1): ' + "\n" + window.localStorage.getItem('chooseSensor_list_v1') + "\n";
  let bg_sensor_data = '解析的传感器(bg_sensor_data): ' + "\n" + window.localStorage.getItem('bg_sensor_data') + "\n";
  let Version = '版本号: ' + (await gamepp.getPlatformVersion.promise())

  str += XmlInfo + "\n" + SensorInfo  + "\n" + Display + "\n" + MainDisk + "\n" + DiskInfo + "\n" + chooseSensor_list_v1Str + "\n" + bg_sensor_data + "\n" + Version;
  try {
    await navigator.clipboard.writeText(str);
  }catch (e) {
    console.log(e)
  }
  // console.log(str)
  let HwNameStr = window.localStorage.getItem('cpu_gpu_Name')
  let FileName = ''
  if (HwNameStr) {
    let HwName = JSON.parse(HwNameStr)
    FileName += (HwName['cpu'] + ' && ' + HwName['gpu'])
  }
  let AppDataDir = await gamepp.getAppDataDir.promise()
  let save_path = AppDataDir.replace('AppData', 'Desktop').split('\\').slice(0, 4).join('\\') + '\\' + FileName;

  //
  console.log('保存文件路径:' + save_path + '.txt')
  try {
    await gamepp.saveFileToDisk.promise(save_path + '.txt', str)
  } catch (err){
    console.log(err)
  }
  successMsg(t('Setting.dataSaveDesktop'))
}
function ClearCache () {
  GPP_SendStatics(100759)
  window.localStorage.removeItem('chooseSensor_list_v1')
  successMsg(t('Setting.TheSensorCacheCleared'),3000)
}

</script>

<style scoped lang="scss">
#cgq {
  margin-top: 20px;
}
</style>
<style>
.sensor_msg .el-icon {
    --color: inherit;
    align-items: center;
    display: inline-flex;
    height: 0.2rem;
    justify-content: center;
    line-height: 0.2rem;
    position: relative;
    width: 0.2rem;
    fill: currentColor;
    //color: var(--color);
    font-size: inherit;
}
.sensor_msg p {
    color: #67c23a;
}
</style>
