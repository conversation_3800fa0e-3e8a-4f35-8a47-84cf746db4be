<script setup lang="ts">
import {computed, defineProps, inject, onMounted, reactive, ref, watch} from 'vue'
import {Star, StarFilled, QuestionFilled} from "@element-plus/icons-vue";
import {bg_list,totalSeconds,MathArrayAvg2,MathArrayMax2,MathArrayMin2,getHoursMinutesSeconds,displayHMSTime,inputFormatter,FormatTime} from "./someScripts"
import * as echarts from "echarts";
import Shortcut from "@/components/eleCom/shortcut.vue";
import RightClickMenu from "@/modules/Game_ReboundDetail/components/RightClickMenu.vue";
import ColorPickerCover from "@/modules/Game_ReboundDetail/components/ColorPickerCover.vue";
import {useReboundDetailStore,dropdown_menu} from "@/modules/Game_ReboundDetail/stores/reboundDetail";
import {storeToRefs} from "pinia";
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
// @ts-ignore
const gamepp = window.gamepp

const $store = useReboundDetailStore()
const {startTime,endTime,endTimeOriginal,gameTime} = storeToRefs($store)
const {handleInputTime,handlePointCommand} = $store
const zoomValue = inject('zoomV')
const props = defineProps({
  recentGameInfo: {
    type: Object,
    required: true
  },
  hardwaerinfo: {
    type: Object,
    required: true
  },
  powerData: {
    type: Object,
    required: true
  }
})
const elColorPickers = ref<Array<any>>([])
const loading = ref(false)
const charts_content_order = ref(2)
const statistics_data_order = ref(2)
const hardware_preview_order = ref(2)
const driver_index = ref(0)
const disk_info_list = ref<Array<any>>([])
let CPUEstimatePower: any
let GameTimeH
let GPUEstimatePower: any
let cpupower1 = ref(0)
let cpupower2 = ref(0)
let cpupower3 = ref(0)
let gpupower1 = ref(0)
let gpupower2 = ref(0)
let gpupower3 = ref(0)
let mempower1 = ref(0)
let mempower2 = ref(0)
let mempower3 = ref(0)

const echarts_Max = ref(0)
const echarts_Avg = ref(0)
const echarts_Min = ref(0)
let echartsInstance: any = null
let echartsShowIndexStart = 0;
let echartsShowIndexEnd = -1;
const showDataList = ref([
  {name: 'fps', value: 'FPS', color: '#2FD274',show: true,value2:""},
  {name: 'fps1', value: 'FPS 1% low', color: '#355ED5',show: true,value2:""},
  {name: 'fps01', value: 'FPS 0.1% Low', color: '#FF7E00',show: true,value2:""},
]) // 现在显示的什么数据
const curDisplayDataCount = ref(0);
const dropdownImgUrl = ref('')
const dropdown_point_img = ref();
const dropdown_point_index = ref(-1);
const dropdown_menu_toshow = computed(() => {
  return JSON.parse(JSON.stringify(dropdown_menu)).filter((item:any) => {
    if (!showDataList.value.find(item2 => item.name === item2.name)) {
      const checkName = item.name
      const PM_GetDetailsARR = props.powerData.full;
      let DetailArr;
      if (PM_GetDetailsARR[checkName]) {
        if (checkName === 'memory' || checkName === 'fps' || checkName === 'cpupower' || checkName === 'frametime') {
          DetailArr = PM_GetDetailsARR[checkName]['detail'];
        }
        else if (checkName === 'gpuload' || checkName === 'gpuload1' || checkName === 'gputemperature' || checkName === 'gpumemoryload' || checkName === 'gpuclock' || checkName === 'gpupower')
        {
          if (PM_GetDetailsARR[checkName].length === 2) {
            if (PM_GetDetailsARR[checkName][0]['avg'] <= 0) {
              DetailArr = PM_GetDetailsARR[checkName][1]['detail'];
            } else {
              if (PM_GetDetailsARR.gpushadres !== undefined) {
                if (PM_GetDetailsARR.gpushadres[0] < PM_GetDetailsARR.gpushadres[1])
                {
                  DetailArr = PM_GetDetailsARR[checkName][1]['detail'];
                }
                else
                {
                  DetailArr = PM_GetDetailsARR[checkName][0]['detail'];
                }
              }
              else
              {
                DetailArr = PM_GetDetailsARR[checkName][1]['detail'];
              }
            }
          }
          else
          {
            DetailArr = PM_GetDetailsARR[checkName][0]['detail'];
          }
        }
        else
        {
          DetailArr = PM_GetDetailsARR[checkName]['detail'];
        }
      }else{
        DetailArr = []
      }
      return DetailArr.length !== 0;
    } else {
      return false
    }
  })
})
const line_item = computed(() =>
{
  if (props.powerData && props.powerData.points) {
    let map: any = {}
    const totalLen = props.powerData.points.length
    for (let i = 0; i < totalLen; i++) {
      const item = props.powerData.points[i]
      if (item !== null) {
        const dur = Math.ceil(Number((gameTime.value / totalLen) * i))
        const hms = getHoursMinutesSeconds(dur)
        map[i] = {
          imgurl: item,
          time: {
            ...hms
          },
          left: `${i / totalLen * 800}px`,
        }
      }
    }
    return map
  }
  return {}
})

const disk_info = computed(() =>
{
  const hwinfo = JSON.parse(gamepp.hardware.getBaseJsonInfo.sync())
  if (hwinfo.DRIVES && hwinfo.DRIVES.SubNode && hwinfo.DRIVES.SubNode[driver_index.value]) {
    const disk_data = hwinfo.DRIVES.SubNode[driver_index.value]
    disk_info_list.value = hwinfo.DRIVES.SubNode
    return disk_data
  } else {
    return false
  }
})

// (结束时间-开始时间差) / gameTime * 800
const line_width = computed(() => {
  const end = totalSeconds(endTime.value)
  const start = totalSeconds(startTime.value)
  return (end - start) / gameTime.value * 800 + 'px'
})
const line_left = computed(() => {
  const start = totalSeconds(startTime.value)
  return start / gameTime.value * 800 + 'px'
})

const selectedDDR5Memory = ref('')
const selectedDDR5MemoryTempList = computed(()=>{
  if (props.powerData.full && props.powerData.full['ddr5temperature']) {
    let findIndex = props.hardwaerinfo['memory_list_all'].findIndex((item:string)=>item === selectedDDR5Memory.value)
    if (findIndex === -1) findIndex = 0
    const _arr = props.powerData.full['ddr5temperature'].map((item:Array<any>)=>{
      return Number(item[findIndex])
    })
    return _arr
  }else{
    return []
  }
})

const selectedDDR5MemoryVoltageList = computed(()=>{
  if (props.powerData.full && props.powerData.full['ddr5voltage']) {
    let findIndex = props.hardwaerinfo['memory_list_all'].findIndex((item:string)=>item === selectedDDR5Memory.value)
    if (findIndex === -1) findIndex = 0
    const _arr = props.powerData.full['ddr5voltage'].map((item:Array<any>)=>{
      return Number(item[findIndex])
    })
    return _arr
  }else{
    return []
  }
})

onMounted(() => {
  loadShowDataItem();
  loadOrders();
  initEcharts();
  checkDDR5Memory();
  const bc = new BroadcastChannel('fps_limit')
  bc.onmessage = ()=>{
    echartsSetOption()
  }
})
function FormatSeconds(value: any, second: any) {
    let theTime: number = parseInt(value);// 秒
    let theTime1: number = 0;// 分
    let theTime2: number = 0;// 小时
    if (theTime > 60) {
        theTime1 = parseInt(String(theTime / 60));
        theTime = parseInt(String(theTime % 60));
        if (theTime1 > 60) {
            theTime2 = parseInt(String(theTime1 / 60));
            theTime1 = parseInt(String(theTime1 % 60));
        }
    }
    let result = "";
    if (second) {
        result = "" + parseInt(String(theTime)) + t('shutdownTimer.sec');
    }
    if (theTime1 > 0) {
        result = "" + parseInt(String(theTime1)) + t('screenshotpage.minutes') + result;
    }
    if (theTime2 > 0) {
        result = "" + parseInt(String(theTime2)) + t('shutdownTimer.hours') + result;
    }
    return result;
}
let checkDDR5MemoryCount = 0;
function checkDDR5Memory() {
  checkDDR5MemoryCount++
  if (props.hardwaerinfo['memory_list_all'] && props.hardwaerinfo['memory_list_all'][0]) {
    selectedDDR5Memory.value = props.hardwaerinfo['memory_list_all'][0]
  }else{
    if (checkDDR5MemoryCount === 10) return
    setTimeout(checkDDR5Memory,1500)
  }
}
function loadOrders() {
  const localData = window.localStorage.getItem('reboundDetailOrders')
  if (localData) {
    const localDataObj = JSON.parse(localData)
    charts_content_order.value = localDataObj.charts_content_order
    statistics_data_order.value = localDataObj.statistics_data_order
    hardware_preview_order.value = localDataObj.hardware_preview_order
  }
}
function changeOrder(num:number, type: string) {
  switch (type) {
    case 'charts_content_order':
      charts_content_order.value = num
      break
    case 'statistics_data_order':
      statistics_data_order.value = num
      break
    case 'hardware_preview_order':
      hardware_preview_order.value = num
      break
  }
  window.localStorage.setItem('reboundDetailOrders', JSON.stringify({
    charts_content_order: charts_content_order.value,
    statistics_data_order: statistics_data_order.value,
    hardware_preview_order: hardware_preview_order.value
  }))
}

function handlePointCommandWithLoading(e: any, type: string) {
  const result = handlePointCommand(e,type)
  if (result) openLoading()
}

const handleDropdownPointMouseIn = (event: MouseEvent, imgurl: string, type:string, index:number) => {
  setTimeout(() => {
    dropdown_point_index.value = index
    if (imgurl) {
      const {clientY} = event;
      dropdownImgUrl.value = imgurl
      if (type === 'start') {
        dropdown_point_img.value.style.left = 211 + 'px'
      } else {
        dropdown_point_img.value.style.left = 650 + 'px'
      }
      dropdown_point_img.value.style.top = clientY - 80 + 'px'
      dropdown_point_img.value.style.display = 'block'
    }
  })
}

const handleDropdownPointMouseLeave = () => {
  dropdown_point_img.value.style.display = 'none'
  dropdownImgUrl.value = ''
  dropdown_point_index.value = -1
}
let try_count = 0

const initEcharts = () => {
  echartsInstance = echarts.init(document.getElementById('full-data-charts'))
  echartsSetOption()
}

function colorWithOpacity(color: string, opacity: number): string {
  let rgbaColor = '';

  if (/^#([0-9A-F]{3}){1,2}$/i.test(color)) { // 检查是否为 16 进制颜色
    color = color.startsWith('#') ? color.slice(1) : color;
    const shortHex = color.length === 3;
    const r = parseInt((shortHex ? color[0] + color[0] : color.slice(0, 2)), 16);
    const g = parseInt((shortHex ? color[1] + color[1] : color.slice(2, 4)), 16);
    const b = parseInt((shortHex ? color[2] + color[2] : color.slice(4, 6)), 16);
    rgbaColor = `rgba(${r},${g},${b},${opacity})`;
  } else if (/^(rgb|rgba)\s*\(\s*\d+\s*,\s*\d+\s*,\s*\d+(,\s*[\d.]+\s*)?\)$/.test(color)) { // 检查是否为 RGB 或 RGBA
    const rgbaMatch = color.match(/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)(,\s*([\d.]+)\s*)?\)/);
    if (rgbaMatch) {
      const r = parseInt(rgbaMatch[1], 10);
      const g = parseInt(rgbaMatch[2], 10);
      const b = parseInt(rgbaMatch[3], 10);
      const a = rgbaMatch[5] ? parseFloat(rgbaMatch[5]) : opacity;
      rgbaColor = `rgba(${r},${g},${b},${a})`;
    }
  } else {
    return color;
  }

  return rgbaColor;
}

const echartsSetOption = () => {
  if (props.recentGameInfo.starttime && props.powerData.fps && props.powerData.full && props.powerData.full.fps) {
    let checkName = showDataList.value[0].name;
    let checkValue = showDataList.value[0].value;
    const PM_GetDetailsARR = props.powerData.full;
    let DetailArr;
    if (PM_GetDetailsARR[checkName]) {
      if (checkName === 'memory' || checkName === 'fps' || checkName === 'cpupower' || checkName === 'frametime' || checkName === 'frametime') {
        DetailArr = PM_GetDetailsARR[checkName]['detail'];
      } else if (checkName === 'gpuload' || checkName === 'gpuload1' || checkName === 'gputemperature' || checkName === 'gpumemoryload' || checkName === 'gpuclock' || checkName === 'gpupower') {
        if (PM_GetDetailsARR[checkName].length === 2) {
          if (PM_GetDetailsARR[checkName][0]['avg'] <= 0) {
            DetailArr = PM_GetDetailsARR[checkName][1]['detail'];
          } else {
            if (PM_GetDetailsARR.gpushadres !== undefined) {
              if (PM_GetDetailsARR.gpushadres[0] < PM_GetDetailsARR.gpushadres[1]) {
                DetailArr = PM_GetDetailsARR[checkName][1]['detail'];
              } else {
                DetailArr = PM_GetDetailsARR[checkName][0]['detail'];
              }
            } else {
              DetailArr = PM_GetDetailsARR[checkName][1]['detail'];
            }
          }
        } else {
          DetailArr = PM_GetDetailsARR[checkName][0]['detail'];
        }
      } else {
        DetailArr = PM_GetDetailsARR[checkName]['detail'];
      }
    }
    if (DetailArr !== null && DetailArr !== undefined) {
      if (startTime.value.h != 0 || startTime.value.m != 0 || startTime.value.s != 0) {
        echartsShowIndexStart = Math.ceil(totalSeconds(startTime.value) / gameTime.value * DetailArr.length)
      }
      if (startTime.value.h == 0 && startTime.value.m == 0 && startTime.value.s == 0) {
        echartsShowIndexStart = 0
      }
      if (totalSeconds(endTime.value) < totalSeconds(endTimeOriginal.value)) {
        echartsShowIndexEnd = Math.ceil(totalSeconds(endTime.value) / gameTime.value * DetailArr.length)
      } else {
        echartsShowIndexEnd = -1
      }
      DELoadGraph(DetailArr, checkValue, checkName, PM_GetDetailsARR.fps1['detail'], PM_GetDetailsARR.fps01['detail']);
    } else {

    }
  } else {
    setTimeout(() => {
      echartsSetOption()
    }, 100)
  }
}
function isObject (obj:any) {
  return obj && typeof obj === 'object' && !Array.isArray(obj)
}
//加载折线图
function DELoadGraph(DetailArr: any, checkValue: string, CheckName: string, fps1 = [], fps01 = []) {
  if (DetailArr.length === 0) {
    // @ts-ignore
    ElMessage(t('GameRebound.noCurData'));
  }
  let maxN = Math.max.apply(null, DetailArr);
  let minN = Math.min.apply(null, DetailArr);
  let avgN = AVGNum(DetailArr);

  echarts_Max.value = maxN
  echarts_Min.value = minN
  echarts_Avg.value = avgN

  let color1 = showDataList.value[0].color;
  let color2 = colorWithOpacity(showDataList.value[0].color, 0.4);
  let DataUnit = ''
  if (CheckName === 'cpuload' || CheckName === 'gpuload' || CheckName === 'memory') {
    DataUnit = ' %';
  } else if (CheckName === 'cputemperature' || CheckName === 'gputemperature') {
    DataUnit = ' ℃';
  } else if (CheckName === 'cpupower' || CheckName === 'gpupower') {
    DataUnit = ' W';
  } else {
    DataUnit = '';
  }
  const errsSet = new Set($store.powerData.errs)
  let filterFn = (value: number, index: number)=>{
    if (errsSet.has(index)) {
      return false;
    }
    if (echartsShowIndexStart > 0 && index < echartsShowIndexStart) {
      return false
    }
    if (echartsShowIndexEnd != -1 && index > echartsShowIndexEnd) {
      return false
    }
    return true;
  }
  let DisplayArrData = DetailArr.filter(filterFn);
  let displayFormatterObj = JSON.parse(JSON.stringify($store.powerData.full))

  let _gpuarr = ['gpuload','gpuload1','gputemperature','gpumemoryload','gpuclock','gpupower','gpuvoltage']
  Object.keys(displayFormatterObj).forEach((k)=>{
    if (isObject(displayFormatterObj[k]) && Array.isArray(displayFormatterObj[k]['detail'])) {
      displayFormatterObj[k]['detail'] = displayFormatterObj[k]['detail'].filter(filterFn)
    }else if (Array.isArray(displayFormatterObj[k]) && k !== 'errs' && k !== 'errs_reason') {
      if (_gpuarr.includes(k)) {
        displayFormatterObj[k] = displayFormatterObj[k].map(item=>{
          item.detail = item.detail.filter(filterFn)
          return item;
        })
      }else{
        displayFormatterObj[k] = displayFormatterObj[k].filter(filterFn)
      }
    }
  });
  const MaxNum_fps = props.powerData.fps?.max;
  if (Array.isArray(DisplayArrData)) {
    const MaxNum = Math.max(...DisplayArrData)
    if (CheckName.includes("fps"))
    {
      DisplayArrData = DisplayArrData.map((v: number) => v / MaxNum_fps * 100)
    }
    else
    {
      DisplayArrData = DisplayArrData.map((v: number) => v / MaxNum * 100)
    }
  }
  let OnePoint = '';
  for (let i = 0; i < (DisplayArrData.length) - 1; i++) {
    OnePoint += ',';
  }
  let FormartXAxisData = OnePoint.split(',');
  curDisplayDataCount.value = DisplayArrData.length
  $store.curDisplayDataCount = DisplayArrData.length
  let option: any = null;
  option = {
    title: {},
    tooltip: {
      trigger: 'axis',
      formatter: function (data: any) {
        if ((window as any).isScreenshot === true) return ''
        const PM_GetDetailsARR = displayFormatterObj
        if (PM_GetDetailsARR.gpuload !== null && PM_GetDetailsARR.cpuload !== null) {
          let ArrSub = data[0].dataIndex;
          let TotalGameTiem: any = gameTime.value + '';
          let StartTime = props.recentGameInfo.starttime;
          let TotalPoint = DetailArr.length;
          if (!rightClickOptions.show) {
            rightClickOptions.ArrSub = ArrSub
            rightClickOptions.dataIndex = data[0].dataIndex
            rightClickOptions.duration = PM_GetDetailsARR.duration[ArrSub];
          }
          let AllDataHtml = '<div class="AllDataHtml">'

          let findItem = showDataList.value.find(v => v.name === 'fps')
          if (findItem) {
            AllDataHtml += `<span style="color: ${findItem.color}">FPS : </span>` + (PM_GetDetailsARR.fps['detail'][ArrSub] ? PM_GetDetailsARR.fps['detail'][ArrSub]: t('GameRebound.noData')) + '<br/>';
          }else{
            AllDataHtml += '<span style="color: #777">FPS : <span>' + (PM_GetDetailsARR.fps['detail'][ArrSub]?PM_GetDetailsARR.fps['detail'][ArrSub]:t('GameRebound.noData')) + '<br/>';
          }

          if (PM_GetDetailsARR.frametime['detail'] !== null) {
            findItem = showDataList.value.find(v => v.name === 'frametime')
            if (findItem) {
              AllDataHtml += `<span style="color: ${findItem.color}">${t('GameRebound.FrameGenerationTime')} : </span>` + (PM_GetDetailsARR.frametime['detail'][ArrSub]?(PM_GetDetailsARR.frametime['detail'][ArrSub]+'ms'):t('GameRebound.noData')) + '<br/>';
            }else{
              AllDataHtml += `<span style="color: #777">${t('GameRebound.FrameGenerationTime')}  : </span>` + (PM_GetDetailsARR.frametime['detail'][ArrSub]?(PM_GetDetailsARR.frametime['detail'][ArrSub]+'ms'):t('GameRebound.noData')) + '<br/>';
            }
          }

          if (PM_GetDetailsARR.fps1['detail'] !== null) {
            findItem = showDataList.value.find(v => v.name === 'fps1')
            if (findItem) {
              AllDataHtml += `<span style="color: ${findItem.color}">FPS 1% Low : </span>` + (PM_GetDetailsARR.fps1['detail'][ArrSub]?PM_GetDetailsARR.fps1['detail'][ArrSub]:t('GameRebound.noData')) + '<br/>';
            }else{
              AllDataHtml += '<span style="color: #777">FPS 1% Low : </span>' + (PM_GetDetailsARR.fps1['detail'][ArrSub]?PM_GetDetailsARR.fps1['detail'][ArrSub]:t('GameRebound.noData')) + '<br/>';
            }
          }

          if (PM_GetDetailsARR.fps01['detail'] !== null) {
            findItem = showDataList.value.find(v => v.name === 'fps01')
            if (findItem) {
              AllDataHtml += `<span style="color: ${findItem.color}">FPS 0.1% Low : </span>` + (PM_GetDetailsARR.fps01['detail'][ArrSub]?PM_GetDetailsARR.fps01['detail'][ArrSub]:t('GameRebound.noData'))+ '<br/>';
            } else {
              AllDataHtml += '<span style="color: #777">FPS 0.1% Low : </span>' + (PM_GetDetailsARR.fps01['detail'][ArrSub]?PM_GetDetailsARR.fps01['detail'][ArrSub]:t('GameRebound.noData'))+ '<br/>';
            }
          }
          findItem = showDataList.value.find(v => v.name === 'cpuload')
          if (findItem) {
            AllDataHtml += `<span style="color: ${findItem.color}">${t('GameRebound.ProcessorOccupancy')}` + ' : </span>' + PM_GetDetailsARR.cpuload['detail'][ArrSub] + '%' + '<br/>';
          } else {
            AllDataHtml += `<span style="color: #777">${t('GameRebound.ProcessorOccupancy')}` + ' : </span>' + PM_GetDetailsARR.cpuload['detail'][ArrSub] + '%' + '<br/>';
          }

          if (PM_GetDetailsARR.cpuloadP && PM_GetDetailsARR.cpuloadP.avg !== 0) {
            findItem = showDataList.value.find(v => v.name === 'cpuloadP')
            if (findItem) {
              AllDataHtml += `<span style="color: ${findItem.color}">${t('GameRebound.ProcessorOccupancy')}-P` + ' : </span>' + PM_GetDetailsARR.cpuloadP['detail'][ArrSub] + '%' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777">${t('GameRebound.ProcessorOccupancy')}-P` + ' : </span>' + PM_GetDetailsARR.cpuloadP['detail'][ArrSub] + '%' + '<br/>';
            }
          }
          if (PM_GetDetailsARR.cpuloadE && PM_GetDetailsARR.cpuloadE.avg !== 0) {
            findItem = showDataList.value.find(v => v.name === 'cpuloadE')
            if (findItem) {
              AllDataHtml += `<span style="color: ${findItem.color}">${t('GameRebound.ProcessorOccupancy')}-E` + ' : </span>' + PM_GetDetailsARR.cpuloadE['detail'][ArrSub] + '%' + '<br/>';
            }else{
              AllDataHtml += `<span style="color: #777">${t('GameRebound.ProcessorOccupancy')}-E` + ' : </span>' + PM_GetDetailsARR.cpuloadE['detail'][ArrSub] + '%' + '<br/>';
            }
          }

          findItem = showDataList.value.find(v => v.name === 'cpuclock')
          if (findItem) {
            AllDataHtml += `<span style="color: ${findItem.color}">${t('GameRebound.ProcessorFrequency')}` + ' : </span>' + PM_GetDetailsARR.cpuclock['detail'][ArrSub] + 'MHz' + '<br/>';
          } else {
            AllDataHtml += `<span style="color: #777">${t('GameRebound.ProcessorFrequency')}` + ' : </span>' + PM_GetDetailsARR.cpuclock['detail'][ArrSub] + 'MHz' + '<br/>';
          }
          if (PM_GetDetailsARR.cpuclockP && PM_GetDetailsARR.cpuclockP.avg !== 0) {
            findItem = showDataList.value.find(v => v.name === 'cpuclockP')
            if (findItem) {
              AllDataHtml += `<span style="color: ${findItem.color}">${t('GameRebound.ProcessorFrequency')}-P` + ' : </span>' + PM_GetDetailsARR.cpuclockP['detail'][ArrSub] + 'MHz' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777">${t('GameRebound.ProcessorFrequency')}-P` + ' : </span>' + PM_GetDetailsARR.cpuclockP['detail'][ArrSub] + 'MHz' + '<br/>';
            }
          }
          if (PM_GetDetailsARR.cpuclockE && PM_GetDetailsARR.cpuclockE.avg !== 0) {
            findItem = showDataList.value.find(v => v.name === 'cpuclockE')
            if (findItem) {
              AllDataHtml += `<span style="color: ${findItem.color}">${t('GameRebound.ProcessorFrequency')}-E` + ' : </span>' + PM_GetDetailsARR.cpuclockE['detail'][ArrSub] + 'MHz' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777">${t('GameRebound.ProcessorFrequency')}-E` + ' : </span>' + PM_GetDetailsARR.cpuclockE['detail'][ArrSub] + 'MHz' + '<br/>';
            }
          }
          findItem = showDataList.value.find(v => v.name === 'cputemperature')
          if (findItem) {
            AllDataHtml += `<span style="color: ${findItem.color}">${t('GameRebound.ProcessorTemperature')}` + ' : </span>' + PM_GetDetailsARR.cputemperature['detail'][ArrSub] + '℃' + '<br/>';
          } else {
            AllDataHtml += `<span style="color: #777">${t('GameRebound.ProcessorTemperature')}` + ' : </span>' + PM_GetDetailsARR.cputemperature['detail'][ArrSub] + '℃' + '<br/>';
          }
          findItem = showDataList.value.find(v => v.name === 'cpupower')
          if (findItem) {
            AllDataHtml += `<span style="color: ${findItem.color}">${t('GameRebound.ProcessorHeatPower')}` + ' : </span>' + PM_GetDetailsARR.cpupower['detail'][ArrSub] + 'W' + '<br/>';
          } else {
            AllDataHtml += `<span style="color: #777">${t('GameRebound.ProcessorHeatPower')}` + ' : </span>' + PM_GetDetailsARR.cpupower['detail'][ArrSub] + 'W' + '<br/>';
          }

          let GpuArrlocation = 0;
          if (PM_GetDetailsARR.gpuload.length > 1) {
            if (PM_GetDetailsARR.gpushadres && (PM_GetDetailsARR.gpushadres).length === 2) {
              if (PM_GetDetailsARR.gpushadres[0] < PM_GetDetailsARR.gpushadres[1]) {
                GpuArrlocation = 1;
              }
            }
          }

          if (PM_GetDetailsARR.gpuload[GpuArrlocation]['detail'] !== null) {
            findItem = showDataList.value.find(v => v.name === 'gpuload')
            if (findItem)
            {
              AllDataHtml += `<span style="color: ${findItem.color}">${t('GameRebound.GraphicsCardOccupancy')}` + ' : </span>' + PM_GetDetailsARR.gpuload[GpuArrlocation]['detail'][ArrSub] + '%' + '<br/>';
            }
            else
            {
              AllDataHtml += `<span style="color: #777">${t('GameRebound.GraphicsCardOccupancy')}` + ' : </span>' + PM_GetDetailsARR.gpuload[GpuArrlocation]['detail'][ArrSub] + '%' + '<br/>';
            }
          }

          if (PM_GetDetailsARR.gpuload1 && PM_GetDetailsARR.gpuload1[GpuArrlocation]['detail'] !== null) {
            findItem = showDataList.value.find(v => v.name === 'gpuload1')
            if (findItem)
            {
              AllDataHtml += `<span style="color: ${findItem.color}">${t('GameRebound.GraphicsCardOccupancyTotal')}` + ' : </span>' + PM_GetDetailsARR.gpuload1[GpuArrlocation]['detail'][ArrSub] + '%' + '<br/>';
            }
            else
            {
              AllDataHtml += `<span style="color: #777">${t('GameRebound.GraphicsCardOccupancyTotal')}` + ' : </span>' + PM_GetDetailsARR.gpuload1[GpuArrlocation]['detail'][ArrSub] + '%' + '<br/>';
            }
          }

          findItem = showDataList.value.find(v => v.name === 'gpuclock')
          if (findItem)
          {
            AllDataHtml += `<span style="color: ${findItem.color}">${t('GameRebound.GraphicsCardFrequency')}` + ' : </span>' + PM_GetDetailsARR.gpuclock[GpuArrlocation]['detail'][ArrSub] + 'MHz' + '<br/>';
          }
          else
          {
            AllDataHtml += `<span style="color: #777">${t('GameRebound.GraphicsCardFrequency')}` + ' : </span>' + PM_GetDetailsARR.gpuclock[GpuArrlocation]['detail'][ArrSub] + 'MHz' + '<br/>';
          }

          if (PM_GetDetailsARR.gputemperature && PM_GetDetailsARR.gputemperature[GpuArrlocation]['detail'] !== null) {
            findItem = showDataList.value.find(v => v.name === 'gputemperature')
            if (findItem) {
              AllDataHtml += `<span style="color: ${findItem.color}">${t('GameRebound.GraphicsCardTemperature')}` + ' : </span>' + PM_GetDetailsARR.gputemperature[GpuArrlocation]['detail'][ArrSub] + '℃' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777">${t('GameRebound.GraphicsCardTemperature')}` + ' : </span>' + PM_GetDetailsARR.gputemperature[GpuArrlocation]['detail'][ArrSub] + '℃' + '<br/>';
            }
          }

          if (PM_GetDetailsARR.gpuhotspottemp && PM_GetDetailsARR.gpuhotspottemp['detail'] !== null) {
            findItem = showDataList.value.find(v => v.name === 'gpuhotspottemp')
            if (findItem) {
              AllDataHtml += `<span style="color: ${findItem.color}">${t('GameRebound.GraphicsCardCoreTemperature')}` + ' : </span>' + PM_GetDetailsARR.gpuhotspottemp['detail'][ArrSub] + '℃' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777">${t('GameRebound.GraphicsCardCoreTemperature')}` + ' : </span>' + PM_GetDetailsARR.gpuhotspottemp['detail'][ArrSub] + '℃' + '<br/>';
            }
          }
          if (PM_GetDetailsARR.gpupower && PM_GetDetailsARR.gpupower[GpuArrlocation] && PM_GetDetailsARR.gpupower[GpuArrlocation]['detail'] !== null) {
            findItem = showDataList.value.find(v => v.name === 'gpupower')
            if (findItem) {
              AllDataHtml += `<span style="color: ${findItem.color}">${t('GameRebound.GraphicsCardHeatPower')}` + ' : </span>' + PM_GetDetailsARR.gpupower[GpuArrlocation]['detail'][ArrSub] + 'W' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777">${t('GameRebound.GraphicsCardHeatPower')}` + ' : </span>' + PM_GetDetailsARR.gpupower[GpuArrlocation]['detail'][ArrSub] + 'W' + '<br/>';
            }
          }

          if (PM_GetDetailsARR.gpumemoryload && PM_GetDetailsARR.gpumemoryload[GpuArrlocation]['detail'] !== null) {
            findItem = showDataList.value.find(v => v.name === 'gpumemoryload')
            if (findItem) {
              AllDataHtml += `<span style="color: ${findItem.color}">${t('hardwareInfo.VRAM')}` + ' : </span>' + PM_GetDetailsARR.gpumemoryload[GpuArrlocation]['detail'][ArrSub] + '%' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777">${t('hardwareInfo.VRAM')}` + ' : </span>' + PM_GetDetailsARR.gpumemoryload[GpuArrlocation]['detail'][ArrSub] + '%' + '<br/>';
            }
          }

          if (PM_GetDetailsARR.gpumemorytemp && PM_GetDetailsARR.gpumemorytemp['detail'] !== null && PM_GetDetailsARR.gpumemorytemp['detail'][ArrSub]) {
            findItem = showDataList.value.find(v => v.name === 'gpumemorytemp')
            if (findItem) {
              AllDataHtml += `<span style="color: ${findItem.color}">${t('GameRebound.GraphicsCardMemoryTemperature')}` + ' : </span>' + PM_GetDetailsARR.gpumemorytemp['detail'][ArrSub] + '℃' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777">${t('GameRebound.GraphicsCardMemoryTemperature')}` + ' : </span>' + PM_GetDetailsARR.gpumemorytemp['detail'][ArrSub] + '℃' + '<br/>';
            }
          }

          if (PM_GetDetailsARR.gpumemoryclock && PM_GetDetailsARR.gpumemoryclock['detail'] !== null) {
            findItem = showDataList.value.find(v => v.name === 'gpumemoryclock')
            if (findItem) {
              AllDataHtml += `<span style="color: ${findItem.color}">${t('hardwareInfo.VRAMFrequency')}` + ' : </span>' + PM_GetDetailsARR.gpumemoryclock['detail'][ArrSub] + 'MHz' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777">${t('hardwareInfo.VRAMFrequency')}` + ' : </span>' + PM_GetDetailsARR.gpumemoryclock['detail'][ArrSub] + 'MHz' + '<br/>';
            }
          }

          if (PM_GetDetailsARR.memory && PM_GetDetailsARR.memory['detail'] !== null) {
            findItem = showDataList.value.find(v => v.name === 'memory')
            if (findItem) {
              AllDataHtml += `<span style="color: ${findItem.color}">${t('GameRebound.MemoryOccupancy')}` + ' : </span>' + PM_GetDetailsARR.memory['detail'][ArrSub] + '%' + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777">${t('GameRebound.MemoryOccupancy')}` + ' : </span>' + PM_GetDetailsARR.memory['detail'][ArrSub] + '%' + '<br/>';
            }
          }

          if (PM_GetDetailsARR.memorytemperature && PM_GetDetailsARR.memorytemperature['detail'] !== null) {
            findItem = showDataList.value.find(v => v.name === 'memorytemperature')
            if (findItem) {
              AllDataHtml += `<span style="color: ${findItem.color}">${t('GameRebound.MemoryTemperature')}` + ' : </span>' + PM_GetDetailsARR.memorytemperature['detail'][ArrSub] + '℃' + '<br/>';
            } else {
              AllDataHtml +=`<span style="color: #777">${t('GameRebound.MemoryTemperature')}` + ' : </span>' + PM_GetDetailsARR.memorytemperature['detail'][ArrSub] + '℃' + '<br/>';
            }
          }
          if (PM_GetDetailsARR.pageFaultDelta && PM_GetDetailsARR.pageFaultDelta['detail'] !== null) {
            findItem = showDataList.value.find(v => v.name === 'pageFaultDelta')
            if (findItem) {
              AllDataHtml += `<span style="color: ${findItem.color}">${t('GameRebound.MemoryPageFaults')}` + ' : </span>' + PM_GetDetailsARR.pageFaultDelta['detail'][ArrSub]  + '<br/>';
            } else {
              AllDataHtml += `<span style="color: #777777">${t('GameRebound.MemoryPageFaults')}` + ' : </span>' + PM_GetDetailsARR.pageFaultDelta['detail'][ArrSub]  + '<br/>';
            }
          }

          let duration = PM_GetDetailsARR.duration[ArrSub]
          AllDataHtml += `<span style="color: #777">${t('GameRebound.Duration')}` + ' : </span>' + FormatSeconds(duration, true) + '<br/>';
          AllDataHtml += `<span style="color: #777">${t('GameRebound.Time')}` + ' : </span>' + FormatTime(Number((StartTime + duration))) + '<br/>' + '</div>';
          // 降频截图 打开既有
          // if(1>0){//判断加数组该点是否存在降频图片
          //     AllDataHtml += '降频截图：'+ '</br>'
          //     AllDataHtml +="<img style='width:140px;height:80px;margin-top:5px;margin-bottom:10px;cursor:pointer;' class='Frequencypic' onclick='Openthispic()'>";
          // }

          return AllDataHtml;
        } else {
          return null;
        }

      },
      confine: true,
      backgroundColor: 'rgb(44,44,51,0.8)',
      textStyle: {
        fontSize: '12',
        color: 'rgb(195,202,213)'
      },
      className: 'full-data-echarts-tooltip'
    },
    grid: {
      left: 0,
      right: 0,
      bottom: 0,
      top: 0
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: FormartXAxisData,
        // axisLine: {
        //   lineStyle: {
        //     type: 'solid',
        //     color: '#333645',//左边线的颜色
        //     width: '1'//坐标线的宽度
        //   }
        // },
        axisTick: { //x轴刻度线
          show: false
        },
        axisLabel: {
          textStyle: {
            color: '#333645'//坐标值得具体的颜色
          }
        },
        splitArea: {
          show: false,
          areaStyle: {
            color: [
              'rgba(37,39,47,.5)',
              'rgba(100,100,100,.5)'
            ]
          }
        },
        splitLine: {
          show: true,
          //  改变轴线颜色
          lineStyle: {
            // 使用深浅的间隔色
            color: ['#333645']
          }
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        min: 0,
        // min: minN - 1,
        // max: maxN + 1,
        dataMax: "",
        axisLabel: {
          // formatter: '{value} °C',
          color: "#21222a"  //刻度线标签颜色
        },
        axisTick: { //y轴刻度线
          show: false
        },
        splitLine: {
          show: true,
          //  改变轴线颜色
          lineStyle: {
            // 使用深浅的间隔色
            color: ['#333645']
          }
        }
      }
    ],

    series: [
      {
        name: checkValue,
        type: 'line',
        symbol: 'circle',  //取消折点圆圈
        symbolSize: '2',
        stack: '总量',
        areaStyle: {normal: {}},
        data: DisplayArrData,
        cursor: 'default',
        itemStyle: {
          normal: {
//                        label : {show: true},
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                // 0% 处的颜色
                offset: 0, color: color1
              },
              {
                // 100% 处的颜色
                offset: 1, color: color2
              }
            ], false),
            borderColor: '#0089E9',
            opacity: 0,
            borderWidth: 1,
            lineStyle: {
              color: color1
            }

          },
          lineStyle: {
            // 使用深浅的间隔色
            color: ['#333645']
          },
          emphasis: {
            // borderColor:'red'
            color: '#39404d',
            borderColor: '#0089E9',
            opacity: 1,
          }
        },
      }
    ],
  };
  if (!showDataList.value[0].show) {
    option.series[0].data = []
  }
  if (showDataList.value.length >= 2) { // 其他折线

    const PM_GetDetailsARR = props.powerData.full;
    let tempArr = null;
    for (let i = 1; i < showDataList.value.length; i++) {
      const v = showDataList.value[i]
      if (!v.show) continue;
      option.series[0].areaStyle = null
      const checkName = v.name;
      if (checkName === 'memory' || checkName === 'fps' || checkName === 'cpupower' || checkName === 'frametime' || checkName === 'frametime') {
        tempArr = PM_GetDetailsARR[v.name]['detail'];
      } else if (checkName === 'gpuload' || checkName === 'gpuload1' || checkName === 'gputemperature' || checkName === 'gpumemoryload' || checkName === 'gpuclock' || checkName === 'gpupower') {
        if (PM_GetDetailsARR[checkName].length === 2) {
          if (PM_GetDetailsARR[checkName][0]['avg'] <= 0) {
            tempArr = PM_GetDetailsARR[checkName][1]['detail'];
          } else
          {
            if (PM_GetDetailsARR.gpushadres !== undefined) {
              if (PM_GetDetailsARR.gpushadres[0] < PM_GetDetailsARR.gpushadres[1]) {
                tempArr = PM_GetDetailsARR[checkName][1]['detail'];
              } else {
                tempArr = PM_GetDetailsARR[checkName][0]['detail'];
              }
            } else {
              tempArr = PM_GetDetailsARR[checkName][1]['detail'];
            }
          }
        } else {
          tempArr = PM_GetDetailsARR[checkName][0]['detail'];
        }
      } else {
        tempArr = PM_GetDetailsARR[checkName]['detail'];
      }
      if (tempArr !== null) {
        tempArr = tempArr.filter(filterFn);

        if (Array.isArray(tempArr)) {
          if (checkName.includes("fps"))
          {
            tempArr = tempArr.map((v: number) => v / MaxNum_fps * 100)
          }
          else
          {
            const MaxNum = Math.max(...tempArr)
            tempArr = tempArr.map((v: number) => v / MaxNum * 100)
          }
        }
        option.series.push({
          name: checkName,
          type: 'line',
          symbol: 'circle',  //取消折点圆圈
          symbolSize: '2',
          stack: checkName,
          data: tempArr,
          cursor: 'default',
          lineStyle: {
            color: v.color,
          },
          itemStyle: {
            normal: {
              color: v.color,
              opacity: 0,
            },
          },
        } as any)
      }
    }
  }
  if (line_item.value && Object.keys(line_item.value).length > 0) { // point的竖线
    Object.keys(line_item.value).forEach(function (key: any) {
      if (Number(key) > echartsShowIndexStart && (Number(key) < echartsShowIndexEnd || echartsShowIndexEnd == -1)) {
        const obj = {
          xAxis: Number(key) - echartsShowIndexStart,
          lineStyle: {
            type: 'solid',
            color: '#0089E9',
            lineWidth: 2
          },
          label: {
            show: false
          }
        }
        if ('markLine' in option.series[0]) {
          option.series[0].markLine.data.push(obj)
        } else {
          option.series[0].markLine = {
            symbol: ['none', 'none'],
            label: {show: false},
            data: [obj]
          }
        }
      }
    })
  }
  if (option && typeof option === "object") {
    echartsInstance.setOption(option,true);
  }
}

const handleInputTimeAndLoading = (val: string, type: 'h'|'m'|'s', type2: string) => {
  handleInputTime(val,type,type2)
  openLoading()
};
let loadingTimer:any = null;
let echartsSetOptionTimer:any = null;
function openLoading () {
  if (loadingTimer !== null) clearTimeout(loadingTimer)
  loading.value = true;
  loadingTimer = setTimeout(()=>{
    loading.value = false;
  },1000)
  if (echartsSetOptionTimer !== null) clearTimeout(echartsSetOptionTimer)
  echartsSetOptionTimer = setTimeout(()=>{
    echartsSetOption()
  },200)
}


function AVGNum(arr: Array<any>) {
  let sum = 0
  for (let value of arr) {
    sum += value;
  }

  return ~~((sum / arr.length * 100) / 100);
}
const chartsContentIsBottom = computed(()=>{
  return charts_content_order.value === 2 && statistics_data_order.value === 1 && hardware_preview_order.value === 1;
})

const totalPowerTooltip = computed(() => {
  return (cpupower1.value + gpupower1.value + mempower1.value).toFixed(4) + '· ' +
    (cpupower2.value + gpupower2.value + mempower2.value).toFixed(4) + ' ≈ ' +
    (cpupower3.value + gpupower3.value + mempower3.value).toFixed(4) + '度'
})

function totalPower(powerData: any) {
  return (showCpuPower(powerData) + showGpuPower(powerData) + getMemoryPower(props.recentGameInfo)).toFixed(5)
}

function showCpuPower(powerData: any) {
  let cpuload = powerData.cpuload?.avg || 0;
  if (cpuload <= 25) {
    CPUEstimatePower = (powerData.cpupower['avg'] * 1.5) * 1.25;
  } else if (cpuload > 25 && cpuload <= 50) {
    CPUEstimatePower = (powerData.cpupower['avg'] * 1.5) * 1.2;
  } else if (cpuload > 50 && cpuload <= 75) {
    CPUEstimatePower = (powerData.cpupower['avg'] * 1.5) * 1.15;
  } else if (cpuload > 75 && cpuload <= 100) {
    CPUEstimatePower = (powerData.cpupower['avg'] * 1.5) * 1.05;
  }
  GameTimeH = (((props.recentGameInfo.endtime - props.recentGameInfo.starttime) / 3600));
  cpupower1.value = parseFloat((CPUEstimatePower / 1000).toFixed(5))
  cpupower2.value = parseFloat(GameTimeH.toFixed(5));
  cpupower3.value = parseFloat(((CPUEstimatePower / 1000) * GameTimeH).toFixed(4));
  return parseFloat(((CPUEstimatePower / 1000) * GameTimeH).toFixed(4));
}

function showGpuPower(powerData: any) {
  let gpuload = powerData.gpuload.avg;
  if (gpuload <= 25) {
    GPUEstimatePower = powerData.gpupower['avg'] * 1.5;
  } else if (gpuload > 25 && gpuload <= 50) {
    GPUEstimatePower = powerData.gpupower['avg'] * 1.35;
  } else if (gpuload > 50 && gpuload <= 75) {
    GPUEstimatePower = powerData.gpupower['avg'] * 1.25;
  } else if (gpuload > 75 && gpuload <= 100) {
    GPUEstimatePower = powerData.gpupower['avg'] * 1.15;
  }
  GameTimeH = (((props.recentGameInfo.endtime - props.recentGameInfo.starttime) / 3600));

  gpupower1.value = parseFloat((GPUEstimatePower / 1000).toFixed(4))
  gpupower2.value = parseFloat(GameTimeH.toFixed(4))
  gpupower3.value = parseFloat(((GPUEstimatePower / 1000) * GameTimeH).toFixed(4));

  return parseFloat(((GPUEstimatePower / 1000) * GameTimeH).toFixed(4));
}

function getMemoryPower(recentGameInfo: any) {
  try {
    let MemoryPower = 0;
    let data_arr = JSON.parse(gamepp.hardware.getBaseJsonInfo.sync());
    data_arr['MEMORY']['SubNode'].forEach((MemoryKV: any) => {
      let MemorySize = ((MemoryKV.ModuleSize).split(' ')[0]) * 1024;
      if (MemorySize === 2048) {
        MemoryPower += 1.5;
      } else if (MemorySize === 4096) {
        MemoryPower += 3;
      } else if (MemorySize === 8192) {
        MemoryPower += 6;
      } else if (MemorySize === 16384) {
        MemoryPower += 12;
      } else if (MemorySize === 32768) {
        MemoryPower += 18;
      }
    })
    GameTimeH = (((recentGameInfo.endtime - recentGameInfo.starttime) / 3600));
    mempower1.value = parseFloat((MemoryPower / 1000).toFixed(4))
    mempower2.value = parseFloat(GameTimeH.toFixed(4))
    mempower3.value = parseFloat(((MemoryPower / 1000) * GameTimeH).toFixed(4))
    return parseFloat(((MemoryPower / 1000) * GameTimeH).toFixed(4))
  } catch (e) {
    return 0;
  }
}

function percentBg(num: number) {
  if (num <= 30) {
    return bg_list['--greenbg'];
  } else if (num <= 60) {
    return bg_list['--bluebg'];
  } else if (num <= 80) {
    return bg_list['--yellowbg'];
  } else {
    return bg_list['--redbg'];
  }
}

const handleCommand = (i: number) => {
  driver_index.value = i;
}

function handleChangeShowDataItem(e: any, i: number) {
  if (e.name && e.name === 'delete') {
    if (showDataList.value.length === 1) {
      // @ts-ignore
      ElMessage.warning(t('hardwareInfo.atLeastOneData'))
      return;
    }
    showDataList.value.splice(i, 1)
  } else {
    showDataList.value[i].name = e.name
    showDataList.value[i].value = e.value
    if (e.value2) {
        showDataList.value[i].value2 = e.value2
    }
  }
  localStorage.setItem('reboundDetailFullDataShowList', JSON.stringify(showDataList.value));
  echartsSetOption()
}

function addShowDataItem() {
  if (showDataList.value.length >= 6) {
    // @ts-ignore
    ElMessage.warning(t('hardwareInfo.atMostSixData'))
    return
  } else {
    showDataList.value.push(dropdown_menu_toshow.value[0] as any)
    // 随机一个颜色
    showDataList.value[showDataList.value.length - 1].color = getRandomColor()
    showDataList.value[showDataList.value.length - 1].show = true
    // 存储到本地
    localStorage.setItem('reboundDetailFullDataShowList', JSON.stringify(showDataList.value));
    echartsSetOption()
  }
}
function getRandomColor() {
    // 生成一个0到16777215之间的随机数
    let color = Math.floor(Math.random() * 16777215).toString(16);
    // 确保颜色值长度为6位，不足6位时前面补0
    while (color.length < 6) {
        color = '0' + color;
    }
    return '#' + color;
}

function loadShowDataItem() {
  try {
      if (!window.localStorage.getItem('reboundDetailFullDataShowListV2Status')) {
          window.localStorage.removeItem('reboundDetailFullDataShowList')
          window.localStorage.setItem('reboundDetailFullDataShowListV2Status','1')
      }
    const localData = window.localStorage.getItem('reboundDetailFullDataShowList')
    if (localData) {
      const localDataObj = JSON.parse(localData)
      showDataList.value = localDataObj
      for (let i = 0; i < showDataList.value.length; i++) {
        if (!showDataList.value[i].hasOwnProperty('show')){
          showDataList.value[i].show = true;
        }
      }
    }else{
        if ($store.isManual) {
            showDataList.value = [
                {name: "cpuload", value: 'GameRebound.ProcessorOccupancy', color: "#2FD274", show: true,value2:""},
                {name: "cpuclock", value: 'GameRebound.ProcessorOccupancy', color: "#355ED5", show: true,value2:""},
                {name: "cputemperature", value: 'GameRebound.ProcessorTemperature', color: "#FF7E00", show: true,value2:""}
            ]
        }
    }
  } catch (e) {

  }
}

function handleColorChange(index: number) {
  if (!showDataList.value[index].show) handleColorPickerClick(index);
  localStorage.setItem('reboundDetailFullDataShowList', JSON.stringify(showDataList.value));
  echartsSetOption()
}

function computedFullDataAvg(dataList:Array<number>) {
  let startIndex = 0
  let endIndex = 0
  if (startTime.value.h != 0 || startTime.value.m != 0 || startTime.value.s != 0) {
    startIndex = Math.ceil(totalSeconds(startTime.value) / gameTime.value * dataList.length)
  }
  if (startTime.value.h == 0 && startTime.value.m == 0 && startTime.value.s == 0) {
    startIndex = 0
  }
  if (totalSeconds(endTime.value) < totalSeconds(endTimeOriginal.value)) {
    endIndex = Math.ceil(totalSeconds(endTime.value) / gameTime.value * dataList.length)
  } else {
    endIndex = dataList.length
  }
  try {
    return MathArrayAvg2(dataList.slice(startIndex, endIndex),startIndex,props.powerData.errs)
  }catch (e) {
    return 0
  }
}
function computedFullDataMax(dataList:Array<number>) {
  let startIndex = 0
  let endIndex = 0
  if (startTime.value.h != 0 || startTime.value.m != 0 || startTime.value.s != 0) {
    startIndex = Math.ceil(totalSeconds(startTime.value) / gameTime.value * dataList.length)
  }
  if (startTime.value.h == 0 && startTime.value.m == 0 && startTime.value.s == 0) {
    startIndex = 0
  }
  if (totalSeconds(endTime.value) < totalSeconds(endTimeOriginal.value)) {
    endIndex = Math.ceil(totalSeconds(endTime.value) / gameTime.value * dataList.length)
  } else {
    endIndex = dataList.length
  }
  try {
    return MathArrayMax2(dataList.slice(startIndex, endIndex),startIndex,props.powerData.errs)
  }catch (e) {
    return 0
  }
}
function computedFullDataMin(dataList:Array<number>) {
  let startIndex = 0
  let endIndex = 0
  if (startTime.value.h != 0 || startTime.value.m != 0 || startTime.value.s != 0) {
    startIndex = Math.ceil(totalSeconds(startTime.value) / gameTime.value * dataList.length)
  }
  if (startTime.value.h == 0 && startTime.value.m == 0 && startTime.value.s == 0) {
    startIndex = 0
  }
  if (totalSeconds(endTime.value) < totalSeconds(endTimeOriginal.value)) {
    endIndex = Math.ceil(totalSeconds(endTime.value) / gameTime.value * dataList.length)
  } else {
    endIndex = dataList.length
  }
  try {
    return MathArrayMin2(dataList.slice(startIndex, endIndex),startIndex,props.powerData.errs)
  }catch (e) {
    return 0
  }
}
const rcm = ref()
const rightClickOptions = reactive({ // 一些开了右键菜单后需要的数据
  ArrSub: -1,
  dataIndex: -1,
  duration: 0,
  show: false,
  screenX: 0,
  screenY: 0,
  clientX: 0,
  clientY: 0,
})
function handleEchartsContextMenu(e:MouseEvent) {
  if (showDataList.value.findIndex(item=>item.show) === -1) {
    return
  }
  const {clientX, clientY, screenX, screenY} = e;
  const zoomLevel = zoomValue.value
  if (rcm.value.openMenu) {
    rightClickOptions.clientX = clientX/zoomLevel
    rightClickOptions.clientY = clientY/zoomLevel
    rightClickOptions.screenX = screenX/zoomLevel
    rightClickOptions.screenY = screenY/zoomLevel
    rightClickOptions.show = true
    rcm.value.openMenu(clientX, clientY, screenX, screenY)
  }
}

function handleRightClickMenuCommand(n:number) {
  const duration = Number(rightClickOptions.duration.toFixed(0))
  const hms = getHoursMinutesSeconds(duration)
  if (n === 1) { // 设置成开始时间
    startTime.value.h = hms.h
    startTime.value.m = hms.m
    startTime.value.s = hms.s
  } else { // 设置成结束时间
    endTime.value.h = hms.h
    endTime.value.m = hms.m
    endTime.value.s = hms.s
  }
  openLoading ()
  echartsSetOption()
}

function handleRightClickMenuClose() {
  rightClickOptions.show = false
}

function handleColorPickerContextmenu(index:number) { // color picker右键事件
  const curColor = showDataList.value[index].color.toUpperCase()
  console.log(elColorPickers.value)
  console.log(elColorPickers.value[index].color.value,curColor)
  if (elColorPickers.value[index] && elColorPickers.value[index].color.value.toUpperCase() === curColor) {
    elColorPickers.value[index].show();
  }else{
    for (let i = 0; i < elColorPickers.value.length; i++) {
      if (elColorPickers.value[i].color.value.toUpperCase() === curColor) {
        elColorPickers.value[i].show()
      }
    }
  }
}

function handleColorPickerClick(index:number) {
  showDataList.value[index].show = !showDataList.value[index].show
  localStorage.setItem('reboundDetailFullDataShowList', JSON.stringify(showDataList.value));
  echartsSetOption()
}
</script>

<template>
  <div class="full-data-container">
    <div class="charts-dur">
      <el-dropdown trigger="click" max-height="200" @command="(e)=>handlePointCommandWithLoading(e,'start')">
        <div class="start-time">
          <el-input v-model="startTime.h" :formatter="inputFormatter" size="small"
                    @change="(e)=>handleInputTimeAndLoading(e,'h','start')"/>
          <span>:</span>
          <el-input v-model="startTime.m" :formatter="inputFormatter" size="small"
                    @change="(e)=>handleInputTimeAndLoading(e,'m','start')"
          />
          <span>:</span>
          <el-input v-model="startTime.s" :formatter="inputFormatter" size="small"
                    @change="(e)=>handleInputTimeAndLoading(e,'s','start')"
          />
          <span class="iconfont icon-hideshow"></span>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item :command="{time: {h:0,m:0,s:0}}">
              <div class="dropdown_point">
                <span>{{ $t('GameRebound.StartStatistics') }}</span>
                <span>00 : 00 : 00</span>
              </div>
            </el-dropdown-item>
            <el-dropdown-item
                v-for="(item, index) in Object.keys(line_item)"
                :key="item"
                :command="line_item[item]"
            >
              <div class="dropdown_point" @mouseenter="handleDropdownPointMouseIn($event, line_item[item].imgurl,'start',index)"
                   @mouseleave="handleDropdownPointMouseLeave">
                <span>{{ $t('GameRebound.Mark') }}{{ index + 1 }}</span>
                <span>{{ displayHMSTime(line_item[item].time) }}</span>
              </div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <div class="dur-process">
        <div class="line" :style="{width: line_width,left: line_left}"></div>
        <div
            v-for="(item,index) in Object.keys(line_item)"
            class="line-item"
            :class="{'is-active': (index === dropdown_point_index)}"
            :style="{left: line_item[item].left}"
            :key="index+'dpl'"
        >
        </div>
      </div>
      <el-dropdown trigger="click" max-height="200" @command="(e)=>handlePointCommandWithLoading(e,'end')">
        <div class="end-time">
          <el-input v-model="endTime.h" :formatter="inputFormatter" size="small"
                    @change="(e)=>handleInputTimeAndLoading(e,'h','end')"
          />
          <span>:</span>
          <el-input v-model="endTime.m" :formatter="inputFormatter" size="small"
                    @change="(e)=>handleInputTimeAndLoading(e,'m','end')"
          />
          <span>:</span>
          <el-input v-model="endTime.s" :formatter="inputFormatter" size="small"
                    @change="(e)=>handleInputTimeAndLoading(e,'s','end')"
          />
          <span class="iconfont icon-hideshow"></span>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item :command="{time: endTimeOriginal}">
              <div class="dropdown_point">
                <span>{{ $t('GameRebound.EndStatistics') }}</span>
                <span>{{ displayHMSTime(endTimeOriginal) }}</span>
              </div>
            </el-dropdown-item>
            <el-dropdown-item
                v-for="(item, index) in Object.keys(line_item)"
                :key="item"
                :command="line_item[item]"
            >
              <div class="dropdown_point" @mouseenter="handleDropdownPointMouseIn($event, line_item[item].imgurl,'end',index)"
                   @mouseleave="handleDropdownPointMouseLeave">
                <span>{{ $t('GameRebound.Mark') }}{{ index + 1 }}</span>
                <span>{{ displayHMSTime(line_item[item].time) }}</span>
              </div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <div class="hwinfoAndCharts scroll">
      <div class="charts-content" :class="{'chartsContentIsBottom':chartsContentIsBottom}" :style="{order: charts_content_order}">
        <div class="top-info">
          <el-icon color="#ffffff" class="no-inherit" @click="changeOrder(1,'charts_content_order')" v-show="charts_content_order === 2">
            <Star />
          </el-icon>
          <el-icon color="#ffb000" class="no-inherit" @click="changeOrder(2,'charts_content_order')" v-show="charts_content_order !== 2">
            <StarFilled />
          </el-icon>
          <span style="margin-left: 10px;">{{ $t('GameRebound.LineChart') }}</span>

            <div class="data-screening" style="margin-left: auto;" @click="$store.openFpsLimitDialog">
                <span class="iconfont icon-parameter"></span>
                <span>{{$t('GameRebound.dataScreening')}}</span>
            </div>
        </div>
        <div class="charts-box">
          <div class="shortcut-info">
            <span>{{ $t('GameRebound.AddPointInGame_m1') }}</span>
            <div>
              <shortcut :id="527" bg-color="#22232E" style="color: #ffffff;border: 1px solid #777;border-radius: 4px;"/>
            </div>
            <span>{{ $t('GameRebound.AddPointInGame_m2') }}</span>
          </div>

          <ul>
            <li v-for="(item,index) in showDataList" :key="item.name">
              <el-tooltip effect="light" :content="$t('GameRebound.LeftMouse')" placement="top">
                <div @contextmenu.prevent.stop="handleColorPickerContextmenu(index)" @click="handleColorPickerClick(index)">
                  <ColorPickerCover :color="item.show?item.color:'#999999'"/>
                  <el-color-picker
                      @change="handleColorChange(index)"
                      v-model="showDataList[index].color"
                      ref="elColorPickers"
                      size="small"
                      :key="'elColorPickers'+index"
                      popper-class="no-clear-icon"
                  />
                </div>
              </el-tooltip>
              <el-dropdown @command="(e)=>handleChangeShowDataItem(e,index)" trigger="click" max-height="200">
                <div class="show-data-item" :class="{'show-data-item-hide': !item.show}">
                  <span :style="{color: item.show?item.color:'#999999',textAlign:item.show?'':'center'}" style="width: 85px;">{{ item.show?$t(item.value):$t('GameRebound.Hidden') }}</span>
                  <span class="iconfont icon-hideshow"></span>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                        :command="{name: 'delete',index: index}"
                    ><span class="delete-show-data-item">{{ $t('GameRebound.DeleteThisLine') }}</span>
                    </el-dropdown-item>
                    <el-dropdown-item
                        v-for="(item2, index2) in dropdown_menu_toshow"
                        :key="item2.name"
                        :command="item2"
                    ><span>{{ $t(item2.value) + (item2.value2? item2.value2:'') }}</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </li>
            <li class="addzxline" @click="addShowDataItem" v-show="showDataList.length <= 5">
              <div class="add-icon">+</div>
              <span>{{ $t('GameRebound.AddCurve') }}</span>
            </li>
          </ul>
        </div>
        <div class="noone-show" v-show="showDataList.findIndex(item=>item.show) === -1">{{ $t('GameRebound.AllCurvesAreHidden') }}</div>
        <div id="full-data-charts" style="width: 1200px;height: 200px;" @contextmenu="handleEchartsContextMenu"></div>
        <div class="charts-left-bottom">
          <div class="charts-left-bottom-box">
            <span>{{displayHMSTime(startTime)}}</span>
            <text>{{ $t('GameRebound.ThereAreSamplingData') }}</text>
            <span>{{curDisplayDataCount}}{{ $t('GameRebound.Items') }}</span>
          </div>
          <div class="charts-left-bottom-box" style="margin-left: auto;">
            <span>{{displayHMSTime(endTime)}}</span>
          </div>
        </div>
        <teleport to="body">
          <div class="right-click-markline" v-show="rightClickOptions.show" :style="{left: rightClickOptions.clientX+'px'}"></div>
        </teleport>
      </div>
      <div class="statistics-data" :style="{order: statistics_data_order}" v-loading="loading">
        <div class="top-info">
          <el-icon color="#ffffff" class="no-inherit" @click="changeOrder(1,'statistics_data_order')" v-show="statistics_data_order === 2">
            <Star />
          </el-icon>
          <el-icon color="#ffb000" class="no-inherit" @click="changeOrder(2,'statistics_data_order')" v-show="statistics_data_order !== 2">
            <StarFilled />
          </el-icon>
          <span style="margin-left: 10px;">{{ $t('GameRebound.StatisticsData') }}</span>

          <div class="top-info-right">
            <span>{{ $t('GameRebound.electricity') }}<</span>
            <span class="power-color" v-if="props.powerData">≈{{ totalPower(props.powerData) }}</span>
            <span>{{ $t('GameRebound.degree') }}</span>
            <el-tooltip effect="light" :content="totalPowerTooltip">
              <el-icon color="#CABB3D" class="no-inherit">
                <QuestionFilled/>
              </el-icon>
            </el-tooltip>

            <span>{{ $t('GameRebound.carbonEmission') }}</span>
            <span class="co2-color">≈{{ parseFloat(((Number(totalPower(props.powerData)) * 0.785) * 1000).toFixed(1)) }}</span>
            <span>{{ $t('GameRebound.gram') }}</span>
            <el-tooltip effect="light" :content="$t('GameRebound.carbonEmissionTips')">
              <el-icon color="#CABB3D" class="no-inherit">
                <QuestionFilled/>
              </el-icon>
            </el-tooltip>
          </div>
        </div>
        <div class="statistics">
          <div class="left">
            <div class="fps-about">
              <div class="FPS">
                <div class="desc">FPS</div>
                <h1 v-if="props.powerData.fps">{{ computedFullDataAvg(props.powerData.fps.detail).toFixed(0) }}</h1>
              </div>
              <ul>
                <li>
                  <p>{{ $t('GameRebound.FrameGenerationTime') }}</p>
                  <span v-if="props.powerData.frametime">{{ computedFullDataAvg(props.powerData.frametime.detail).toFixed(2) }} ms</span>
                </li>
                <li>
                  <p>1% Low FPS</p>
                  <span v-if="props.powerData.fps1">{{ computedFullDataAvg(props.powerData.fps1.detail).toFixed(0) }}</span>
                </li>
                <li>
                  <p>0.1% Low FPS</p>
                  <span v-if="props.powerData.fps01">{{ computedFullDataAvg(props.powerData.fps01.detail).toFixed(0) }}</span>
                </li>
              </ul>
            </div>

            <div class="gpu-about" v-if="props.powerData">
              <div class="gpu-item" style="margin-bottom: 25px;">
                <span class="first">GPU:</span>
                <span v-if="props.hardwaerinfo.GPU">{{ props.hardwaerinfo.GPU }}</span>
              </div>
              <div class="gpu-item" v-if="props.powerData.gpuclock">
                <span class="first">{{ $t('hardwareInfo.frequency') }}：</span>
                <div class="avg">
                  <span>Avg:</span>
                  <span>{{ computedFullDataAvg(props.powerData.gpuclock.detail).toFixed(0) }} MHz</span>
                </div>
                <div class="max">
                  <span>Max:</span>
                  <span>{{ computedFullDataMax(props.powerData.gpuclock.detail).toFixed(0) }} MHz</span>
                </div>
                <div class="min">
                  <span>Min:</span>
                  <span>{{ computedFullDataMin(props.powerData.gpuclock.detail).toFixed(0) }} MHz</span>
                </div>
              </div>
              <div class="gpu-item" v-if="props.powerData.gpuload">
                <span class="first">{{ $t('GameRebound.D3D') }}</span>
                <div class="avg">
                  <span>Avg:</span>
                  <span class="bg-percent"
                        :style="{'--width': computedFullDataAvg(props.powerData.gpuload.detail) + 'px','--bg':percentBg(computedFullDataAvg(props.powerData.gpuload.detail))}">{{
                      computedFullDataAvg(props.powerData.gpuload.detail).toFixed(0)
                    }} %</span>
                </div>
                <div class="max">
                  <span>Max:</span>
                  <span class="bg-percent"
                        :style="{'--width': computedFullDataMax(props.powerData.gpuload.detail) + 'px','--bg':percentBg(computedFullDataMax(props.powerData.gpuload.detail))}">{{
                      computedFullDataMax(props.powerData.gpuload.detail).toFixed(0)
                    }} %</span>
                </div>
                <div class="min">
                  <span>Min:</span>
                  <span class="bg-percent"
                        :style="{'--width': computedFullDataMin(props.powerData.gpuload.detail) + 'px','--bg':percentBg(computedFullDataMin(props.powerData.gpuload.detail))}">{{
                      computedFullDataMin(props.powerData.gpuload.detail).toFixed(0)
                    }} %</span>
                </div>
              </div>
              <div class="gpu-item" v-if="props.powerData.gpuload1">
                <span class="first">{{ $t('GameRebound.TOTAL') }}</span>
                <div class="avg">
                  <span>Avg:</span>
                  <span class="bg-percent"
                        :style="{'--width': computedFullDataAvg(props.powerData.gpuload1.detail) + 'px','--bg':percentBg(computedFullDataAvg(props.powerData.gpuload1.detail))}">{{
                      computedFullDataAvg(props.powerData.gpuload1.detail).toFixed(0)
                    }} %</span>
                </div>
                <div class="max">
                  <span>Max:</span>
                  <span class="bg-percent"
                        :style="{'--width': computedFullDataMax(props.powerData.gpuload.detail) + 'px','--bg':percentBg(computedFullDataMax(props.powerData.gpuload1.detail))}">{{
                      computedFullDataMax(props.powerData.gpuload1.detail).toFixed(0)
                    }} %</span>
                </div>
                <div class="min">
                  <span>Min:</span>
                  <span class="bg-percent"
                        :style="{'--width': computedFullDataMin(props.powerData.gpuload1.detail) + 'px','--bg':percentBg(computedFullDataMin(props.powerData.gpuload1.detail))}">{{
                      computedFullDataMin(props.powerData.gpuload1.detail).toFixed(0)
                    }} %</span>
                </div>
              </div>
              <div class="gpu-item" v-if="props.powerData.gputemperature">
                <span class="first">{{ $t('hardwareInfo.temperature') }}：</span>
                <div class="avg">
                  <span>Avg:</span>
                  <span class="bg-percent"
                        :style="{'--width': computedFullDataAvg(props.powerData.gputemperature.detail) + 'px','--bg':percentBg(computedFullDataAvg(props.powerData.gputemperature.detail))}">{{
                      computedFullDataAvg(props.powerData.gputemperature.detail).toFixed(0)
                    }} ℃</span>
                </div>
                <div class="max">
                  <span>Max:</span>
                  <span class="bg-percent"
                        :style="{'--width': computedFullDataMax(props.powerData.gputemperature.detail) + 'px','--bg':percentBg(computedFullDataMax(props.powerData.gputemperature.detail))}">{{
                      computedFullDataMax(props.powerData.gputemperature.detail).toFixed(0)
                    }} ℃</span>
                </div>
                <div class="min">
                  <span>Min:</span>
                  <span class="bg-percent"
                        :style="{'--width': computedFullDataMin(props.powerData.gputemperature.detail) + 'px','--bg':percentBg(computedFullDataMin(props.powerData.gputemperature.detail))}">{{
                      computedFullDataMin(props.powerData.gputemperature.detail).toFixed(0)
                    }} ℃</span>
                </div>
              </div>
              <div class="gpu-item" v-if="props.powerData.gpuvoltage">
                <span class="first">{{ $t('GameRebound.voltage') }}：</span>
                <div class="avg">
                  <span>Avg:</span>
                  <span>{{ computedFullDataAvg(props.powerData.gpuvoltage.detail).toFixed(3) }} V</span>
                </div>
                <div class="max">
                  <span>Max:</span>
                  <span>{{ computedFullDataMax(props.powerData.gpuvoltage.detail).toFixed(3) }} V</span>
                </div>
                <div class="min">
                  <span>Min:</span>
                  <span>{{ computedFullDataMin(props.powerData.gpuvoltage.detail).toFixed(3) }} V</span>
                </div>
              </div>
              <div class="gpu-item" v-if="props.powerData.gpupower">
                <span class="first">TDP:</span>
                <div class="avg">
                  <span>Avg:</span>
                  <span>{{ computedFullDataAvg(props.powerData.gpupower.detail).toFixed(0) }} W</span>
                </div>
                <div class="max">
                  <span>Max:</span>
                  <span>{{ computedFullDataMax(props.powerData.gpupower.detail).toFixed(0) }} W</span>
                </div>
                <div class="min">
                  <span>Min:</span>
                  <span>{{ computedFullDataMin(props.powerData.gpupower.detail).toFixed(0) }} W</span>
                </div>
              </div>
              <div class="gpu-item" v-if="props.powerData.gpumemoryload">
                <span class="first">{{ $t('hardwareInfo.VRAM') }}：</span>
                <div class="avg">
                  <span>Avg:</span>
                  <span class="bg-percent"
                        :style="{'--width': computedFullDataAvg(props.powerData.gpumemoryload.detail) + 'px','--bg':percentBg(computedFullDataAvg(props.powerData.gpumemoryload.detail))}">{{
                      computedFullDataAvg(props.powerData.gpumemoryload.detail).toFixed(0)
                    }} %</span>
                </div>
                <div class="max">
                  <span>Max:</span>
                  <span class="bg-percent"
                        :style="{'--width': computedFullDataMax(props.powerData.gpumemoryload.detail) + 'px','--bg':percentBg(computedFullDataMax(props.powerData.gpumemoryload.detail))}">{{
                      computedFullDataMax(props.powerData.gpumemoryload.detail).toFixed(0)
                    }} %</span>
                </div>
                <div class="min">
                  <span>Min:</span>
                  <span class="bg-percent"
                        :style="{'--width': computedFullDataMin(props.powerData.gpumemoryload.detail) + 'px','--bg':percentBg(computedFullDataMin(props.powerData.gpumemoryload.detail))}">{{
                      computedFullDataMin(props.powerData.gpumemoryload.detail).toFixed(0)
                    }} %</span>
                </div>
              </div>
              <div class="gpu-item"
                   v-if="props.powerData.full.gpumemorytemp && props.powerData.full.gpumemorytemp[0] && props.powerData.full.gpumemorytemp[0].avg !== 0">
                <span class="first">{{ $t('GameRebound.GraphicsCardMemoryTemperature') }}：</span>
                <div class="avg">
                  <span>Avg:</span>
                  <span class="bg-percent"
                        :style="{'--width': computedFullDataAvg(props.powerData.gpumemorytemp.detail) + 'px','--bg':percentBg(computedFullDataAvg(props.powerData.gpumemorytemp.detail))}">{{
                      computedFullDataAvg(props.powerData.gpumemorytemp.detail).toFixed(0)
                    }} ℃</span>
                </div>
                <div class="max">
                  <span>Max:</span>
                  <span class="bg-percent"
                        :style="{'--width': computedFullDataMax(props.powerData.gpumemorytemp.detail) + 'px','--bg':percentBg(computedFullDataMax(props.powerData.gpumemorytemp.detail))}">{{
                      computedFullDataMax(props.powerData.gpumemorytemp.detail).toFixed(0)
                    }} ℃</span>
                </div>
                <div class="min">
                  <span>Min:</span>
                  <span class="bg-percent"
                        :style="{'--width': computedFullDataMin(props.powerData.gpumemorytemp.detail) + 'px','--bg':percentBg(computedFullDataMin(props.powerData.gpumemorytemp.detail))}">{{
                      computedFullDataMin(props.powerData.gpumemorytemp.detail).toFixed(0)
                    }} ℃</span>
                </div>
              </div>
            </div>

            <div class="disk-about">
              <div class="disk-desc">
                <span class="w70">{{ $t('hardwareInfo.hardDisk') }}</span>
                <span style="margin-right:5px;">{{ props.hardwaerinfo.gameDiskName }}</span>
                <span> {{ $t('hardwareInfo.actualCapacity') }}{{ props.hardwaerinfo.Drive_size }}</span>
                <span> {{ $t('hardwareInfo.type') }}{{ props.hardwaerinfo.drivesType }}</span>
              </div>

              <div class="disk-item" v-if="props.powerData.disk_temp">
                <span class="w70">{{ $t('hardwareInfo.temperature') }}</span>
                <div class="avg">
                  <span>Avg:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataAvg(props.powerData.disk_temp.detail) + 'px','--bg':percentBg(computedFullDataAvg(props.powerData.disk_temp.detail))}">{{
                      computedFullDataAvg(props.powerData.disk_temp.detail).toFixed(0)
                    }}℃</span>
                </div>

                <div class="max">
                  <span>Max:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataMax(props.powerData.disk_temp.detail) + 'px','--bg':percentBg(computedFullDataMax(props.powerData.disk_temp.detail))}">{{
                      computedFullDataMax(props.powerData.disk_temp.detail).toFixed(0)
                    }}℃</span>
                </div>

                <div class="min">
                  <span>Min:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataMin(props.powerData.disk_temp.detail) + 'px','--bg':percentBg(computedFullDataMin(props.powerData.disk_temp.detail))}">{{
                      computedFullDataMin(props.powerData.disk_temp.detail).toFixed(0)
                    }}℃</span>
                </div>
              </div>

            </div>
          </div>

          <div class="right">
            <div class="cpu-about">
              <div class="item" style="margin-bottom: 20px;">
                <span>CPU:</span>
                <span>{{ props.hardwaerinfo.ProcessorName }}</span>
              </div>

              <div class="item" v-if="props.powerData.full && props.powerData.full.cpuclockP && props.powerData.full.cpuclockP.avg !== 0">
                <span>P {{ $t('hardwareInfo.frequency') }}：</span>

                <div class="avg">
                  <span>Avg:</span>
                  <span>{{ computedFullDataAvg(props.powerData.full.cpuclockP.detail).toFixed(0) }} MHz</span>
                </div>
                <div class="max">
                  <span>Max:</span>
                  <span>{{ computedFullDataMax(props.powerData.full.cpuclockP.detail).toFixed(0) }} MHz</span>
                </div>
                <div class="min">
                  <span>Min:</span>
                  <span>{{ computedFullDataMin(props.powerData.full.cpuclockP.detail).toFixed(0) }} MHz</span>
                </div>
              </div>
              <div class="item" v-if="props.powerData.full && props.powerData.full.cpuclockE && props.powerData.full.cpuclockE.avg !== 0">
                <span>E {{ $t('hardwareInfo.frequency') }}：</span>

                <div class="avg">
                  <span>Avg:</span>
                  <span>{{ computedFullDataAvg(props.powerData.full.cpuclockE.detail).toFixed(0) }} MHz</span>
                </div>
                <div class="max">
                  <span>Max:</span>
                  <span>{{ computedFullDataMax(props.powerData.full.cpuclockE.detail).toFixed(0) }} MHz</span>
                </div>
                <div class="min">
                  <span>Min:</span>
                  <span>{{ computedFullDataMin(props.powerData.full.cpuclockE.detail).toFixed(0) }} MHz</span>
                </div>
              </div>
              <div class="item" v-if="props.powerData.full && props.powerData.full.cpuclockP && props.powerData.full.cpuclockP.avg === 0">
                <span>{{ $t('hardwareInfo.frequency') }}：</span>

                <div class="avg">
                  <span>Avg:</span>
                  <span>{{ computedFullDataAvg(props.powerData.full.cpuclock.detail).toFixed(0) }} MHz</span>
                </div>
                <div class="max">
                  <span>Max:</span>
                  <span>{{ computedFullDataMax(props.powerData.full.cpuclock.detail).toFixed(0) }} MHz</span>
                </div>
                <div class="min">
                  <span>Min:</span>
                  <span>{{ computedFullDataMin(props.powerData.full.cpuclock.detail).toFixed(0) }} MHz</span>
                </div>
              </div>

              <div class="item" v-if="props.powerData.full && props.powerData.full.cpuloadP && props.powerData.full.cpuloadP.avg !== 0">
                <span>P {{ $t('hardwareInfo.occupied') }}：</span>

                <div class="avg">
                  <span>Avg:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataAvg(props.powerData.full.cpuloadP.detail)+'px','--bg':percentBg(computedFullDataAvg(props.powerData.full.cpuloadP.detail))}">{{
                      computedFullDataAvg(props.powerData.full.cpuloadP.detail).toFixed(1)
                    }} %</span>
                </div>
                <div class="max">
                  <span>Max:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataMax(props.powerData.full.cpuloadP.detail)+'px','--bg':percentBg(computedFullDataMax(props.powerData.full.cpuloadP.detail))}">{{
                      computedFullDataMax(props.powerData.full.cpuloadP.detail).toFixed(1)
                    }} %</span>
                </div>
                <div class="min">
                  <span>Min:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataMin(props.powerData.full.cpuloadP.detail)+'px','--bg':percentBg(computedFullDataMin(props.powerData.full.cpuloadP.detail))}">{{
                      computedFullDataMin(props.powerData.full.cpuloadP.detail).toFixed(1)
                    }} %</span>
                </div>
              </div>
              <div class="item" v-if="props.powerData.full && props.powerData.full.cpuloadE && props.powerData.full.cpuloadE.avg !== 0">
                <span>E {{ $t('hardwareInfo.occupied') }}：</span>

                <div class="avg">
                  <span>Avg:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataAvg(props.powerData.full.cpuloadE.detail)+'px','--bg':percentBg(computedFullDataAvg(props.powerData.full.cpuloadE.detail))}">{{
                      computedFullDataAvg(props.powerData.full.cpuloadE.detail).toFixed(0)
                    }} %</span>
                </div>
                <div class="max">
                  <span>Max:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataMax(props.powerData.full.cpuloadE.detail)+'px','--bg':percentBg(computedFullDataMax(props.powerData.full.cpuloadE.detail))}">{{
                      computedFullDataMax(props.powerData.full.cpuloadE.detail).toFixed(0)
                    }} %</span>
                </div>
                <div class="min">
                  <span>Min:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataMin(props.powerData.full.cpuloadE.detail)+'px','--bg':percentBg(computedFullDataMin(props.powerData.full.cpuloadE.detail))}">{{
                      computedFullDataMin(props.powerData.full.cpuloadE.detail).toFixed(0)
                    }} %</span>
                </div>
              </div>
              <div class="item" v-if="props.powerData.full && props.powerData.full.cpuloadE && props.powerData.full.cpuloadE.avg === 0">
                <span>{{ $t('hardwareInfo.occupied') }}：</span>

                <div class="avg">
                  <span>Avg:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataAvg(props.powerData.full.cpuload.detail)+'px','--bg':percentBg(computedFullDataAvg(props.powerData.full.cpuload.detail))}">{{
                      computedFullDataAvg(props.powerData.full.cpuload.detail).toFixed(0)
                    }} %</span>
                </div>
                <div class="max">
                  <span>Max:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataMax(props.powerData.full.cpuload.detail)+'px','--bg':percentBg(computedFullDataMax(props.powerData.full.cpuload.detail))}">{{
                      computedFullDataMax(props.powerData.full.cpuload.detail).toFixed(0)
                    }} %</span>
                </div>
                <div class="min">
                  <span>Min:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataMin(props.powerData.full.cpuload.detail)+'px','--bg':percentBg(computedFullDataMin(props.powerData.full.cpuload.detail))}">{{
                      computedFullDataMin(props.powerData.full.cpuload.detail).toFixed(0)
                    }} %</span>
                </div>
              </div>

              <div class="item" v-if="props.powerData.full && props.powerData.full.cputemperature">
                <span>{{ $t('hardwareInfo.temperature') }}：</span>

                <div class="avg">
                  <span>Avg:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataAvg(props.powerData.full.cputemperature.detail)+'px','--bg':percentBg(computedFullDataAvg(props.powerData.full.cputemperature.detail))}">{{
                      computedFullDataAvg(props.powerData.full.cputemperature.detail).toFixed(0)
                    }} ℃</span>
                </div>
                <div class="max">
                  <span>Max:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataMax(props.powerData.full.cputemperature.detail)+'px','--bg':percentBg(computedFullDataMax(props.powerData.full.cputemperature.detail))}">{{
                      computedFullDataMax(props.powerData.full.cputemperature.detail).toFixed(0)
                    }} ℃</span>
                </div>
                <div class="min">
                  <span>Min:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataMin(props.powerData.full.cputemperature.detail)+'px','--bg':percentBg(computedFullDataMin(props.powerData.full.cputemperature.detail))}">{{
                      computedFullDataMin(props.powerData.full.cputemperature.detail).toFixed(0)
                    }} ℃</span>
                </div>
              </div>

              <div class="item" v-if="props.powerData.full && props.powerData.full.cpupower">
                <span>{{ $t('GameRebound.voltage') }}：</span>

                <div class="avg">
                  <span>Avg:</span>
                  <span>{{ computedFullDataAvg(props.powerData.full.cpuvoltage.detail).toFixed(3) }} V</span>
                </div>
                <div class="max">
                  <span>Max:</span>
                  <span>{{ computedFullDataMax(props.powerData.full.cpuvoltage.detail).toFixed(3) }} V</span>
                </div>
                <div class="min">
                  <span>Min:</span>
                  <span>{{ computedFullDataMin(props.powerData.full.cpuvoltage.detail).toFixed(3) }} V</span>
                </div>
              </div>

              <div class="item" v-if="props.powerData.full">
                <span>TDP:</span>

                <div class="avg">
                  <span>Avg:</span>
                  <span>{{ computedFullDataAvg(props.powerData.full.cpupower.detail).toFixed(0) }} W</span>
                </div>
                <div class="max">
                  <span>Max:</span>
                  <span>{{ computedFullDataMax(props.powerData.full.cpupower.detail).toFixed(0) }} W</span>
                </div>
                <div class="min">
                  <span>Min:</span>
                  <span>{{ computedFullDataMin(props.powerData.full.cpupower.detail).toFixed(0) }} W</span>
                </div>
              </div>
            </div>

            <div class="memory-about" v-if="props.powerData.ddr5voltage && props.powerData.ddr5voltage.length > 0">
              <div class="item" style="margin-bottom: 20px;">
                <span>{{ $t('hardwareInfo.memory') }}：</span>
                <el-select v-model="selectedDDR5Memory">
                  <el-option
                    v-for="item in props.hardwaerinfo['memory_list_all']"
                    :key="item"
                    :label="item"
                    :value="item"
                  />
                </el-select>
              </div>

              <div class="item">
                <span>{{ $t('hardwareInfo.occupied') }}：</span>
                <div class="avg">
                  <span>Avg:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataAvg(props.powerData.memory.detail)+'px','--bg':percentBg(computedFullDataAvg(props.powerData.memory.detail))}">{{
                      computedFullDataAvg(props.powerData.memory.detail).toFixed(0)
                    }} %</span>
                </div>
                <div class="max">
                  <span>Max:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataMax(props.powerData.memory.detail)+'px','--bg':percentBg(computedFullDataMax(props.powerData.memory.detail))}">{{
                      computedFullDataMax(props.powerData.memory.detail).toFixed(0)
                    }} %</span>
                </div>
                <div class="min">
                  <span>Min:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataMin(props.powerData.memory.detail)+'px','--bg':percentBg(computedFullDataMin(props.powerData.memory.detail))}">{{
                      computedFullDataMin(props.powerData.memory.detail).toFixed(0)
                    }} %</span>
                </div>
              </div>

              <div class="item" v-if="selectedDDR5MemoryTempList.length > 0">
                <span>{{ $t('hardwareInfo.temperature') }}：</span>
                <div class="avg">
                  <span>Avg:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataAvg(selectedDDR5MemoryTempList)+'px','--bg':percentBg(computedFullDataAvg(selectedDDR5MemoryTempList))}">{{
                      computedFullDataAvg(selectedDDR5MemoryTempList).toFixed(0)
                    }} ℃</span>
                </div>
                <div class="max">
                  <span>Max:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataMax(selectedDDR5MemoryTempList)+'px','--bg':percentBg(computedFullDataMax(selectedDDR5MemoryTempList))}">{{
                      computedFullDataMax(selectedDDR5MemoryTempList).toFixed(0)
                    }} ℃</span>
                </div>
                <div class="min">
                  <span>Min:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataMin(selectedDDR5MemoryTempList)+'px','--bg':percentBg(computedFullDataMin(selectedDDR5MemoryTempList))}">{{
                      computedFullDataMin(selectedDDR5MemoryTempList).toFixed(0)
                    }} ℃</span>
                </div>
              </div>

              <div class="item" v-if="selectedDDR5MemoryVoltageList.length > 0">
                <span>{{ $t('GameRebound.voltage') }}：</span>
                <div class="avg">
                  <span>Avg:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataAvg(selectedDDR5MemoryVoltageList)+'px','--bg':percentBg(computedFullDataAvg(selectedDDR5MemoryVoltageList))}">{{
                      computedFullDataAvg(selectedDDR5MemoryVoltageList).toFixed(3)
                    }} V</span>
                </div>
                <div class="max">
                  <span>Max:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataMax(selectedDDR5MemoryVoltageList)+'px','--bg':percentBg(computedFullDataMax(selectedDDR5MemoryVoltageList))}">{{
                      computedFullDataMax(selectedDDR5MemoryVoltageList).toFixed(3)
                    }} V</span>
                </div>
                <div class="min">
                  <span>Min:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataMin(selectedDDR5MemoryVoltageList)+'px','--bg':percentBg(computedFullDataMin(selectedDDR5MemoryVoltageList))}">{{
                      computedFullDataMin(selectedDDR5MemoryVoltageList).toFixed(3)
                    }} V</span>
                </div>
              </div>
            </div>

            <div class="memory-about" v-else>
              <div class="item" style="margin-bottom: 20px;">
                <span>{{ $t('hardwareInfo.memory') }}：</span>
                <span v-if="props.hardwaerinfo.memory_list1[0]">{{ props.hardwaerinfo.memory_list1[0] }}</span>
              </div>

              <div class="item">
                <span>{{ $t('hardwareInfo.occupied') }}：</span>
                <div class="avg">
                  <span>Avg:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataAvg(props.powerData.memory.detail)+'px','--bg':percentBg(computedFullDataAvg(props.powerData.memory.detail))}">{{
                      computedFullDataAvg(props.powerData.memory.detail).toFixed(0)
                    }} %</span>
                </div>
                <div class="max">
                  <span>Max:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataMax(props.powerData.memory.detail)+'px','--bg':percentBg(computedFullDataMax(props.powerData.memory.detail))}">{{
                      computedFullDataMax(props.powerData.memory.detail).toFixed(0)
                    }} %</span>
                </div>
                <div class="min">
                  <span>Min:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataMin(props.powerData.memory.detail)+'px','--bg':percentBg(computedFullDataMin(props.powerData.memory.detail))}">{{
                      computedFullDataMin(props.powerData.memory.detail).toFixed(0)
                    }} %</span>
                </div>
              </div>

              <div class="item" v-if="props.powerData.memorytemperature && Array.isArray(props.powerData.memorytemperature.detail) && props.powerData.memorytemperature.detail.length > 0">
                <span>{{ $t('hardwareInfo.temperature') }}：</span>
                <div class="avg">
                  <span>Avg:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataAvg(props.powerData.memorytemperature.detail)+'px','--bg':percentBg(computedFullDataAvg(props.powerData.memorytemperature.detail))}">{{
                      computedFullDataAvg(props.powerData.memorytemperature.detail).toFixed(0)
                    }} ℃</span>
                </div>
                <div class="max">
                  <span>Max:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataMax(props.powerData.memorytemperature.detail)+'px','--bg':percentBg(computedFullDataMax(props.powerData.memorytemperature.detail))}">{{
                      computedFullDataMax(props.powerData.memorytemperature.detail).toFixed(0)
                    }} ℃</span>
                </div>
                <div class="min">
                  <span>Min:</span>
                  <span class="bg-percent"
                        :style="{'--width':computedFullDataMin(props.powerData.memorytemperature.detail)+'px','--bg':percentBg(computedFullDataMin(props.powerData.memorytemperature.detail))}">{{
                      computedFullDataMin(props.powerData.memorytemperature.detail).toFixed(0)
                    }} ℃</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="hardware-preview" :style="{order: hardware_preview_order}">
        <div class="top-info">
          <el-icon color="#ffffff" class="no-inherit" @click="changeOrder(1,'hardware_preview_order')" v-show="hardware_preview_order === 2">
            <Star />
          </el-icon>
          <el-icon color="#ffb000" class="no-inherit" @click="changeOrder(2,'hardware_preview_order')" v-show="hardware_preview_order !== 2">
            <StarFilled />
          </el-icon>
          <span style="margin-left: 10px;">{{ $t('hardwareInfo.hardwareOverview') }}</span>
        </div>
        <div class="hwinfo">
          <div class="left">
            <ul class="cpu-info border-box">
              <li>
                <p>
                  <span class="iconfont icon-CPU"></span>
                  <span>  CPU</span>
                </p>
                <span>{{ props.hardwaerinfo.ProcessorName }}</span>
              </li>
              <li>
                <div class="helf-box">
                  <p>{{ $t('hardwareInfo.coreCount') }}</p>
                  <span>{{ props.hardwaerinfo.NumberofCPUCores }} {{ props.hardwaerinfo.CPUCores }}</span>
                </div>
                <div class="helf-box">
                  <p>{{ $t('hardwareInfo.threadCount') }}</p>
                  <span>{{ props.hardwaerinfo.NumberofLogicalCPUs }} {{ props.hardwaerinfo.LogicalCPUs }}</span>
                </div>
              </li>
              <li>
                <div class="helf-box">
                  <p>{{ $t('GameRebound.Process') }}</p>
                  <span>{{ props.hardwaerinfo.CPUTechnology }}</span>
                </div>
                <div class="helf-box">
                  <p>{{ $t('GameRebound.L3Cache') }}</p>
                  <span>{{ props.hardwaerinfo.L3Cache }}</span>
                </div>
              </li>
              <li>
                <div class="helf-box">
                  <p>{{ $t('GameRebound.OriginalFrequency') }}</p>
                  <span>{{ props.hardwaerinfo.OriginalProcessorFrequency }}</span>
                </div>
                <div class="helf-box">
                  <p>{{ $t('GameRebound.MaximumBoostFrequency') }}</p>
                  <span>{{ props.hardwaerinfo.CPUTurboMax }}</span>
                </div>
              </li>
            </ul>
            <ul class="gpu-info border-box">
              <li>
                <p>
                  <span class="iconfont icon-GPU"></span>
                  <span>  GPU</span>
                </p>
                <span>{{ props.hardwaerinfo.GPU }}</span>
              </li>
              <li>
                <div class="helf-box">
                  <p>{{ $t('hardwareInfo.brand') }}</p>
                  <span>{{ props.hardwaerinfo.GPUType.toUpperCase() }}</span>
                </div>
                <div class="helf-box">
                  <p>{{ $t('GameRebound.DriverVersion') }}</p>
                  <el-tooltip v-if="props.hardwaerinfo.GPU_DriverVersion.length > 33" effect="light" placement="top" :content="props.hardwaerinfo.GPU_DriverVersion">
                    <span>{{ props.hardwaerinfo.GPU_DriverVersion }}</span>
                  </el-tooltip>
                  <span v-if="props.hardwaerinfo.GPU_DriverVersion.length <= 33">{{ props.hardwaerinfo.GPU_DriverVersion }}</span>
                </div>
              </li>
              <li>
                <div class="helf-box">
                  <p>{{ $t('hardwareInfo.Videomemory') }}</p>
                  <span>{{ props.hardwaerinfo.GPU_VideoMemor }}</span>
                </div>
                <div class="helf-box">
                  <p>{{ $t('GameRebound.GraphicsCardMemoryBrand') }}</p>
                  <span>{{ props.hardwaerinfo.VideoMemoryBrand }}</span>
                </div>
              </li>
              <li>
                <div class="helf-box">
                  <p>{{ $t('GameRebound.Bitwidth') }}</p>
                  <span>{{ props.hardwaerinfo.GPU_GraphicsMemoryBusWidth }}</span>
                </div>
                <div class="helf-box">
                  <p>{{ $t('hardwareInfo.streamProcessors') }}</p>
                  <span>{{ props.hardwaerinfo.GPU_NumberOfUnifiedShaders }}</span>
                </div>
              </li>
            </ul>
            <ul class="system-info border-box">
              <li>
                <p>
                  <span class="iconfont icon-nav_toolbox"></span>
                  <span>{{ $t('GameRebound.System') }}</span>
                </p>
                <span>{{ props.hardwaerinfo.SystemName }}</span>
              </li>
            </ul>
            <ul class="system-info border-box" style="margin-bottom: 0;">
              <li>
                <p>
                  <span class="iconfont icon-board"></span>
                  <span>{{ $t('hardwareInfo.motherboard') }}</span>
                </p>
                <span>{{ props.hardwaerinfo.MainboardName }}</span>
                <p class="p" style="width: auto;margin-left: auto;">{{ $t('hardwareInfo.BIOSVersion') }}：</p>
                <span class="span" style="margin-left: 5px;">{{ props.hardwaerinfo.BIOSVersion }}</span>
              </li>
            </ul>
          </div>
          <div class="right">
            <ul class="monitor-info border-box">
              <li>
                <p>
                  <span class="iconfont icon-Monitor"></span>
                  <span>{{ $t('GameRebound.Screen') }} </span>
                </p>
                <span>{{ props.hardwaerinfo.MonitorNameStr }}</span>
                <p class="p" style="width: auto;margin-left: auto;">{{ $t('home.resolution') }}</p>
                <span class="span" style="margin-left: 5px;">{{ props.hardwaerinfo.resolutiop }}</span>
                <p class="p2" style="width: auto;margin-left: 20px;">{{ $t('hardwareInfo.refreshRate') }}</p>
                <span class="span2" style="margin-left: 5px;">{{ props.hardwaerinfo.refresh_rate }}</span>
              </li>
            </ul>
            <el-dropdown trigger="click" @command="handleCommand">
              <ul class="disk-info border-box">
                <li>
                  <p>
                    <span class="iconfont icon-Harddisk"></span>
                    <span>{{ $t('hardwareInfo.hardDisk') }}</span>
                  </p>
                  <div class="disk-info-name" v-show="disk_info">
                    <span>{{ disk_info.DriveModel }}</span>
                    <div style="margin-top: 8px;">
                      <span class="disk-interface">{{ $t('GameRebound.Interface') }}</span><span>{{ disk_info.DriveController }}</span>
                    </div>
                  </div>

                  <span class="iconfont icon-hideshow" style="margin-left: auto;"></span>
                </li>
              </ul>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                      v-for="(item,index) in disk_info_list"
                      :key="item.DriveModel"
                      :command="index"
                  >
                    {{ item.DriveModel }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <ul class="dram-info border-box"
                :style="{height: props.hardwaerinfo.memory_list1.length < 2 ? '220px' : '340px'}">
              <li>
                <p>
                  <span class="iconfont icon-Dram"></span>
                  <span>{{ $t('hardwareInfo.memory') }} </span>
                </p>
              </li>
              <div class="dram-list scroll">
                <li v-for="(item,index) in props.hardwaerinfo.memory_list1" :key="item">
                  <div>
                    <span class="span_rtl" style="width: auto;margin-right: 10px">{{ item }} </span>
                    <span> x {{
                        props.hardwaerinfo.memory_list2[index] ? props.hardwaerinfo.memory_list2[index] : '1'
                      }}</span>
                  </div>
                  <div>
                    <p>{{ $t('GameRebound.Channel') }}</p>
                    <span>{{ props.hardwaerinfo.Memory_channels_active }}</span>
                    <p>{{ $t('GameRebound.Timing') }}</p>
                    <span>{{ props.hardwaerinfo.Memory_CurrentTiming }}</span>
                  </div>
                  <div>
                    <p>{{ $t('GameRebound.Capacity') }}</p>
                    <span>{{ props.hardwaerinfo.Memory_size }}</span>
                    <p v-if="props.hardwaerinfo.memory_list3[index]">{{ $t('GameRebound.Generation') }}</p>
                    <span v-if="props.hardwaerinfo.memory_list3[index]">{{ props.hardwaerinfo.memory_list3[index]['MemoryType'] }}</span>
                  </div>
                  <div>
                    <p>{{ $t('hardwareInfo.frequency') }}：</p>
                    <span>{{ props.hardwaerinfo.Memory_Clock }}</span>
                  </div>
                </li>
              </div>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <div class="dropdown_point_img" ref="dropdown_point_img">
      <span class="img-name">
        {{dropdownImgUrl.split('\\')[dropdownImgUrl.split('\\').length-1]}}
      </span>
      <img :src="dropdownImgUrl" alt="">
    </div>
  </div>

  <RightClickMenu ref="rcm" @command="handleRightClickMenuCommand" @close="handleRightClickMenuClose"/>
</template>

<style scoped lang="scss">
#full-data-charts {
    zoom: calc(1 / var(--zoomV--));
    transform: scale(var(--zoomV--));
    transform-origin: 0 0;
    &:hover {
        z-index: 99999;
    }
}
.full-data-container {
  width: 100%;
  padding: 10px 0 10px 20px;
  background: #22232E;
  .charts-dur {
    :deep(.el-input__wrapper) {
      padding: 0;
    }

    :deep(.el-input-number--small) {
      width: 40px;
      line-height: 20px;
      font-size: 12px;
      text-align: center;
    }

    :deep(.el-input__inner) {
      text-align: center;
    }
  }
  .hwinfoAndCharts {
    height: calc((100vh - 135px) / var(--zoomV--));
    overflow-y: auto;
    display: flex;
    flex-flow: column nowrap;


    .charts-content {
      height: 310px;
      border-radius: 4px;
      background: #2B2C37;
      margin: 10px 18px 0 0;
      flex-shrink: 0;
      padding: 20px;
      position: relative;

      .noone-show {
        position: absolute;
        font-size: 20px;
        text-shadow: 1px 4px 6px rgba(0, 0, 0, .5);
        color: #999;
        left: 50%;
        bottom: 160px;
        transform: translateX(-50%);
        z-index: 9999;
      }

      .charts-left-bottom {
        position: absolute;
        left: 26px;
        bottom: 16px;
        display: flex;
        flex-flow: row nowrap;
        font-weight: 400;
        font-size: 12px;
        color: #777777;
        line-height: 20px;
        gap: 5px;
        width: 1180px;
        pointer-events: none;
        z-index:9999;
        span {
          color: #ffffff;
        }

        .charts-left-bottom-box {
          background: rgba(0, 0, 0, 0.6);
          border-radius: 4px;
          padding: 2px 9px;
          gap: 5px;
          display: flex;
        }
      }

      .charts-box {
        display: flex;
        flex-flow: row nowrap;
        margin-bottom: 17px;

        .shortcut-info {
          color: #777777;
          font-size: 12px;
          display: flex;
          flex-flow: row nowrap;
          align-items: center;
          gap: 10px;
        }

        :deep(.el-color-picker--small) {
          opacity: 0;
          pointer-events: none;
        }
        :deep(.el-tooltip__trigger) {
          height: 14px;
        }

        ul {
          display: flex;
          flex-flow: row wrap;
          align-items: center;
          margin-left: auto;
            gap: 10px;

          li {
            display: flex;
            flex-flow: row wrap;
            align-items: center;

            .show-data-item {
              height: 20px;
              background: #22232E;
              box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
              border-radius: 4px;
              border: 1px solid #777777;
              cursor: pointer;
              display: flex;
              flex-flow: row nowrap;
              align-items: center;
              justify-content: space-between;
              padding: 0 5px;
              font-size: 12px;

              span {
                color: #ffffff;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
              }
            }
            .show-data-item-hide {
              border: 1px dashed #999999;
              color: #999999;

              .icon-hideshow {
                display: none;
              }
            }

            &.addzxline {
              justify-content: space-around;
              color: #3579D5;
                padding: 0 5px;
              height: 20px;
              background: #22232E;
              box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.3);
              border-radius: 4px;
              border: 1px solid #777777;
              cursor: pointer;
              display: flex;
              flex-flow: row nowrap;
              align-items: center;
              justify-content: space-around;
                gap:3px;

              .add-icon {
                width: 14px;
                height: 14px;
                line-height: 10px;
                text-align: center;
                border-radius: 2px;
                border: 1px solid #3579d5;
              }
            }
          }
        }

        :deep(.el-color-picker__trigger) {
          border: 0;
        }

        :deep(.el-color-picker__color) {
          border: 0;
          border-radius: 0;
        }

        :deep(.el-color-picker--small .el-color-picker__trigger) {
          width: 18px;
          height: 18px;
        }

        :deep(.el-color-picker--small) {
          height: 18px;
        }

        :deep(.el-color-picker .el-color-picker__icon) {
          display: none;
        }
      }
    }

    .statistics-data {
      padding: 20px;
      margin-right: 18px;
      margin-top: 10px;
      background: #2B2C37;
      border-radius: 4px;

      .statistics {
        display: flex;
        flex-flow: row wrap;
        white-space: nowrap;
        justify-content: space-between;

        .left, .right {
          width: calc(50% - 5px);
          display: flex;
          flex-flow: column wrap;
          white-space: nowrap;
        }

        .fps-about {
          height: 110px;
          width: 595px;
          display: flex;
          flex-flow: row nowrap;
          justify-content: space-between;

          .FPS {
            width: 290px;
            height: 108px;
            background: #22232E;
            border-radius: 4px;
            border: 1px solid #727281;
            position: relative;

            &:hover {
              border-color: #3579D5;
            }

            .desc {
              position: absolute;
              top: 20px;
              left: 19px;
            }

            h1 {
              font-weight: bold;
              font-size: 24px;
              color: #2FD274;
              line-height: 108px;
              text-align: center;
            }
          }

          ul {
            display: flex;
            flex-flow: column nowrap;
            justify-content: space-between;
            padding-bottom: 3px;

            li {
              &:hover {
                border-color: #3579D5;
              }

              width: 295px;
              height: 30px;
              background: #22232E;
              border-radius: 4px;
              border: 1px solid #727281;
              display: flex;
              flex-flow: row nowrap;
              align-items: center;
              justify-content: space-between;
              padding: 0 19px;
              color: #777777;
              font-size: 12px;

              span {
                color: #2FD274;
                font-weight: bold;
                font-size: 16px;
              }
            }
          }
        }

        .gpu-about {
          width: 595px;
          height: 280px;
          background: #22232E;
          box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.3);
          border-radius: 4px;
          border: 1px solid #727281;
          padding: 16px 24px;
          margin-top: 10px;
          color: #777777;
          font-size: 12px;
          display: flex;
          flex-flow: column nowrap;
          justify-content: space-between;

          &:hover {
            border-color: #3579D5;
          }

          .gpu-item {
            display: flex;
            margin-bottom: 10px;
            flex-flow: row nowrap;
            align-items: center;
            line-height: 20px;

            span.first {
              // width: 65px;
              min-width: 95px;
              display: inline-block;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }

            .avg, .min, .max {
              display: flex;
              align-items: center;
              width: 155px;
              height: 20px;
              gap: 5px;

              span:first-child {
                display: inline-block;
                width: 36px;
              }

              span:last-child {
                color: white;
              }
            }
          }
        }

        .disk-about {
          width: 595px;
          background: #242530;
          border-radius: 4px;
          border: 1px solid #727281;
          padding: 18px;
          margin-top: 10px;
          color: #777777;

          &:hover {
            border-color: #3579D5;
          }

          .disk-desc {
            margin-bottom: 20px;
          }

          .disk-item {
            display: flex;
            margin-bottom: 10px;
            flex-flow: row nowrap;
            align-items: center;
            line-height: 20px;

            .avg, .min, .max {
              display: flex;
              align-items: center;
              width: 155px;
              height: 20px;
              gap: 5px;

              span:first-child {
                display: inline-block;
                width: 36px;
              }

              span:last-child {
                color: white;
              }
            }
          }

          .w70 {
            width: 70px;
            display: inline-block;
          }
        }

        .cpu-about, .memory-about {
          width: 595px;
          background: #242530;
          border-radius: 4px;
          border: 1px solid #727281;
          padding: 20px;
          color: #777777;

          &:hover {
            border-color: #3579D5;
          }

          .item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            line-height: 20px;

            span:first-child {
              // width: 65px;
              display: inline-block;
              min-width: 80px;
              display: inline-block;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }

            .avg, .min, .max {
              display: flex;
              align-items: center;
              width: 155px;
              height: 20px;
              gap: 5px;

              span:first-child {
                display: inline-block;
                width: 36px;
              }

              span:last-child {
                color: white;
              }
            }
          }
        }

        .memory-about {
          margin-top: 10px;
        }
      }
    }

    .hardware-preview {
      padding: 20px;
      margin-right: 18px;
      margin-top: 10px;
      background: #2B2C37;
      border-radius: 4px;

      .hwinfo {
        display: flex;
        flex-flow: row wrap;
        white-space: nowrap;
        justify-content: space-between;

        .left, .right {
          width: calc(50% - 5px);
        }

        .border-box {
          width: 595px;
          background: #22232E;
          box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.3);
          border-radius: 4px;
          border: 1px solid #727281;
          padding: 20px;

          li {
            display: flex;
            flex-flow: row nowrap;
            align-items: center;
            margin-bottom: 15px;

            p {
              // width: 70px;
              min-width: 70px;
              margin-right: 5px;
              color: #777777;
            }

            div.helf-box {
              width: 50%;
              display: flex;
              flex-shrink: 0;
              span {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }
        }

        .cpu-info {
          height: 160px;
          margin-bottom: 10px;
        }

        .gpu-info {
          height: 150px;
          margin-bottom: 10px;
        }

        .system-info, .monitor-info {
          height: 30px;
          padding-top: 5px;
          margin-bottom: 10px;
        }

        .disk-info {
          font-size: 12px;
          cursor: pointer;
          margin-bottom: 10px;

          li {
            margin-bottom: 0;
          }

          .disk-info-name {
            display: flex;
            flex-flow: column nowrap;
            color: #ffffff;

              .disk-interface {
                  color: #777777;
                  margin-right: 5px;
              }
          }
        }

        .dram-info {
          height: 340px;

          .dram-list {
            height: calc(100% - 25px);
            overflow-y: auto;
            padding-right: 0;
            width: 565px;

            li {
              display: flex;
              flex-flow: column nowrap;
              align-items: flex-start;
              justify-content: space-between;
              width: 555px;
              height: 130px;
              background: #2F3140;
              box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
              border-radius: 4px;
              padding: 14px 20px;

              & > div {
                display: flex;
                flex-flow: row nowrap;

                p {
                  width: 60px;
                  display: inline-block;
                }

                span {
                  width: 105px;
                  display: inline-block;
                }
              }


            }
          }
        }
      }
    }

    .top-info {
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      margin-bottom: 10px;

      .icon-star {
        margin-right: 7px;
      }

      .top-info-right {
        margin-left: auto;
        display: flex;
        flex-flow: row nowrap;
        align-items: center;
        gap: 5px;

        .power-color {
          color: #FFC600;
        }

        .co2-color {
          color: #FF7E00;
        }

        .gray-color {
          color: #777777;
        }
      }
    }
  }

  .bg-percent {
    --bg: linear-gradient(90deg, rgba(47, 210, 116, 0) 0%, rgba(47, 210, 116, 0.4) 100%);
    --width: 100px;
    transition: all .5s linear;
    width: 100px;
    height: 20px;
    border-radius: 4px;
    display: inline-block;
    position: relative;
    overflow: hidden;
    padding-left: 10px;
    z-index: 10;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      background: var(--bg);
      width: var(--width);
      height: 20px;
      border-radius: 4px;
      z-index: -1;
    }
  }
}
.right-click-markline {
  width: 1px;
  height: 198px;
  background-color: #fff;
  position: absolute;
  top: 217px;
  left: 0;
}
.delete-show-data-item {
  color: #BF4040;
}

.no-inherit {
  cursor: pointer;
}

.dropdown_point_img {
  display: none;
  position: absolute;
  background-color: #373946;
  padding: 10px;

  .img-name {
    position: absolute;
    top: 13px;
    left: 50%;
    transform: translateX(-50%);
    display: inline-block;
    width: 361px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #ffffff;
  }

  img {
    width: 400px;
    height: 225px;
  }
}
</style>
<style>
.full-data-echarts-tooltip {
  margin-top: -30px;
  border: 0 !important;
  background: #373947 !important;
  opacity: 0.9!important;
}
.chartsContentIsBottom .full-data-echarts-tooltip {
  margin-top: -100px;
  top: -100% !important;
}
</style>
