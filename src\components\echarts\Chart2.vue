<!-- eCharts折线图 -->
<template>
    <div class="outer" style="width: 270px; height:210px;background-color:rgba(34, 35, 46, 1)">
      <div id="main" style="width: 250px; height: 150px"></div>
    </div>
</template>
  <script setup lang="ts">
  import { ref, onMounted,onUnmounted} from 'vue';
  import echarts from "../../uitls/echarts";
  type Props = {
    xAxisNum?:any[];//x轴的个数
    yAxisValue?:number[];//y轴的值
    Linedirection?:number;//线条渐变方向
    LineColorStart?:string;//线条起始颜色 rgba 可以改变透明度
    LineColorEnd?:string;//线条终止颜色
    yAxisMax?:any;//y轴刻度线最大值
    // width: number;
    // height: number;
  };
    const { xAxisNum , yAxisValue ,Linedirection = 0 , LineColorStart = 'rgba(53, 121, 213, 0.2)', 
            LineColorEnd = 'rgba(53, 121, 213, 1)',  yAxisMax = 'dataMax'} = defineProps<Props>();

    onMounted(() => {
        setEchartsOption()
    });
    function setEchartsOption(){ //配置Echarts
        var chartDom = document.getElementById("main")!;
        var myChart = echarts.init(chartDom);
        var option = {
            xAxis: {
                type: 'category',
                data: xAxisNum,//X轴个数
                show: false,
                boundaryGap: false,
                axisTick: { show: false },
                axisLabel: {
                  show: false,
                },
            },
            tooltip: {
                // formatter: '{b0}: {c0}<br />{b1}: {c1}',
                type:'line',
                trigger: 'axis',
                axisPointer:{
                    label:{
                        show:false
                    }
                }
            },
            yAxis: {
                type: 'value',
                name:'5分钟前', 
                nameLocation:'start',
                max:yAxisMax,
                axisTick: { show: false },
                axisLine: { show: false },
                // show: false,
                splitLine: {
                    interval:4,
                    lineStyle: {
                    color:'rgba(69, 70, 85, 1)',
                    type: [5, 3],
                    },
                },
            },
          grid: { left: 45, right: 0, top: 20, bottom:25},
          series: [
            {
                data: yAxisValue,
                type: 'line',
                lineStyle: {
                    color: new echarts.graphic.LinearGradient(0, Linedirection, 1-Linedirection, 0, [
                    { offset: 0, color: LineColorStart }, // 起始颜色，红色，透明度0.8
                    { offset: 1, color: LineColorEnd } // 结束颜色，蓝色，透明度0.8
                    ])
                },
                symbol: 'none',
            }
          ]
      }
      myChart.setOption(option);
    }
   
  </script>
  
  <style lang="scss" scoped>

    .outer{
      margin:20px 0 0 20px;
      border-radius: 4px;
      display: flex;
      align-items: center
    }

</style>

  