<template>
  <!--性能统计-->
  <div id="xn"></div>
  <el-collapse-item :title="$t('home.performanceStatistics')" name="7">
    <div class="setting-item">
      <section class="left-box">
        <div class="box">
          <el-checkbox
              @change="setInteger(296)"
              v-model="store.state.performanceStatistics['296']"><span style="font-size: 12px;">{{$t('Setting.performanceStatistics')}}</span></el-checkbox>
          <text>{{$t('Setting.littleTips')}}</text>
        </div>
        <div class="box">
          <el-checkbox
              @change="setInteger(297)"
              v-model="store.state.performanceStatistics['297']"><span style="font-size: 12px;">{{$t('Setting.disableAutoShow')}}</span></el-checkbox>
          <text>{{$t('Setting.text3')}}</text>
        </div>
        <div class="flex-row-center">
          <el-checkbox v-model="store.state.performanceStatisticsAutoClose" @change="handleStatisticsAutoClose">
            <span style="font-size: 12px;">{{$t('Setting.AutoClosePopUpWindow_m1')}}</span>
          </el-checkbox>
          <el-input-number
              :controls="false"
              :step="1"
              style="width: 55px;min-width: 45px;"
              :min="1"
              :max="999"
              v-model="store.state.performanceStatisticsAutoCloseTime"
          ></el-input-number>
          <span style="margin: 0 5px;">{{$t('Setting.AutoClosePopUpWindow_m2')}}</span>
        </div>
      </section>
      <section class="right-box">
        <!--<div class="box">-->
        <!--  <el-checkbox-->
        <!--      @change="setInteger(479)"-->
        <!--      v-model="store.state.performanceStatistics['479']"><span style="font-size: 12px;">{{$t('Setting.abnormalShutdownReport')}}</span></el-checkbox>-->
        <!--  <text>{{$t('Setting.text5')}}</text>-->
        <!--</div>-->


          <el-checkbox
              @change="setInteger(521)"
              v-model="store.state.performanceStatistics['521']"><span style="font-size: 12px;">{{$t('Setting.showWeaAndAddress')}}</span>
          </el-checkbox>
          <div style="margin-bottom: 30px;"></div>

          <el-checkbox
              @change="changeIngamePointAutoScreenshot"
              v-model="ingamePointAutoScreenshot"><span style="font-size: 12px;">{{$t('Setting.autoScreenShots')}}</span>
          </el-checkbox>
        <div style="margin-bottom: 30px;"></div>

        <text style="margin-right: 5px;color: white;">{{$t('Setting.keepRecent')}}</text>
        <el-select v-model="store.state.performanceStatistics['300']" style="width: 150px;" @change="setInteger(300)">
          <el-option :label="10" :value="10"/>
          <el-option :label="30" :value="30"/>
          <el-option :label="90" :value="90"/>
          <el-option :label="180" :value="180"/>
          <el-option :label="$t('Setting.noLimit')" :value="10000"/>
        </el-select>
<!--        <el-input-number-->
<!--            v-model="store.state.performanceStatistics['300']"-->
<!--            @change="setInteger(300)"-->
<!--            :controls="false"-->
<!--            :step="1"-->
<!--            style="width: 55px;"-->
<!--            :min="1"-->
<!--            :max="999"-->
<!--        ></el-input-number>-->
<!--        <text style="margin:0 5px">条数据</text>-->


<!--        <div class="flex-row-center" style="margin: 20px 0;">-->
<!--          <text>数据存储位置：</text>-->
<!--          <div class="savePlace">{{store.state.CM_STATISTICS_SAVE_ADDRESS}}</div>-->
<!--          <el-button style="width: 60px;" type="primary" @click="GPP_ShowOpenDialog('数据存储位置',526)">浏览</el-button>-->
<!--        </div>-->

<!--        <text style="display: none;">当前存储数据：</text>-->
<!--        <span style="display: none;">999 GB</span>-->
      </section>
    </div>
  </el-collapse-item>
</template>

<script setup lang="ts">
import {gameppBaseSetting} from '@/modules/Game_Home/stores/index'
import {useScroll} from '../hooks/useScroll'
import {onMounted, ref} from "vue";
import {GPP_SendStatics} from "@/uitls/sendstatics"
useScroll('xn');
const store = gameppBaseSetting();
const ingamePointAutoScreenshot = ref(true)
// @ts-ignore
const gamepp = window.gamepp;

onMounted(()=>{
  readLocal()
})
function handleStatisticsAutoClose(e:boolean) {
  if (e) {
    GPP_SendStatics(100764)
  }else{
    GPP_SendStatics(100765)
  }
}

function readLocal() {
  const localData = window.localStorage.getItem('ingamePointAutoScreenshot')
  if (localData) {
    ingamePointAutoScreenshot.value = JSON.parse(localData)
  }
}

function changeIngamePointAutoScreenshot(v:any) {
  if (v) {
    GPP_SendStatics(100770)
  }else{
    GPP_SendStatics(100771)
  }
  window.localStorage.setItem('ingamePointAutoScreenshot',JSON.stringify(v))
}

function setInteger(id:number) {
  if (id === 297) {
    if (store.state.performanceStatistics['297']){
      GPP_SendStatics(100762)
    }else{
      GPP_SendStatics(100763)
    }
  }
  if (id === 479) {
    if (store.state.performanceStatistics['479']){
      GPP_SendStatics(100766)
    }else{
      GPP_SendStatics(100767)
    }
  }
  if (id === 521) {
    if (store.state.performanceStatistics['521']){
      GPP_SendStatics(100768)
    }else{
      GPP_SendStatics(100769)
    }
  }
  if (id === 300) {
    gamepp.setting.setInteger.promise(id,store.state.performanceStatistics[id])
  }else{
    gamepp.setting.setInteger.promise(id,(store.state.performanceStatistics[id] ? 1 : 0))
  }
}

//文件路径选择
async function GPP_ShowOpenDialog(title:string,id:number){
  let oldPath = gamepp.setting.getString.sync(526)
  if (oldPath === '') {
    let AppDataDir = await gamepp.getAppDataDir.promise();
    oldPath = AppDataDir + '\\common\\Data'
  }
  gamepp.dialog.showOpenDialog.async((value:any) => {
    console.log(value)
    ShowOpenDialogCallback(value[0],id,oldPath)
  }, {title: title,properties:["openDirectory"]});
}
function warningMsg(msg:string) {
  ElMessage({
    message: h('p', { style: 'line-height: 1; font-size: 14px' }, [
      h('span', null, msg),
    ]),
    duration: 3000,
    type: "warning"
  })
}
function ShowOpenDialogCallback(Path:any,id:number,oldPath:string)  {
  if (!Path['canceled']) {
    let regex = /^[a-zA-Z]:(([a-zA-Z]*)||([a-zA-Z]*\\))*/;
    let array = regex.exec(Path['filePaths'][0]);
    if (array === null) {warningMsg('错误！');return;}
    //无法写入
    // if ((Path['filePaths'][0]).indexOf('C:\\Program Files (x86)') === 0 || (Path['filePaths'][0]).indexOf('C:\\Program Files') === 0) {AlertLayuiMsg('无法选择当前目录');return false;}
    console.log(id, Path['filePaths'][0],oldPath)
    moveFile(oldPath,Path['filePaths'][0])
    gamepp.setting.setString.promise(id, Path['filePaths'][0]);
    store.state.CM_STATISTICS_SAVE_ADDRESS = Path['filePaths'][0]
  }
}

function moveFile(oldPath:string,newPath:string)  {
  gamepp.webapp.sendInternalAppEvent.promise('background', {
    action: 'moveDataBase',
    oldPath,
    newPath
  });
}
</script>

<style scoped lang="scss">
#xn {
  margin-top: 20px;
}

.box {
  display: flex;
  flex-direction: column;
  margin-bottom: 50px;
}

.savePlace {
  width: 260px;
  height: 30px;
  background: #343647;
  border-radius: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: .12rem;
  line-height: 30px;
  padding: 0 10px;
}
</style>
