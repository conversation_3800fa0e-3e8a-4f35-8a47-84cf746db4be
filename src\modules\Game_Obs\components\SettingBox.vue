<script setup lang="ts">
import {GPP_GetInteger, GPP_WriteInteger,GPP_CheckedSendData,SetVideoQualityParams} from "../stores/index"
import {reactive} from "vue";

const data = reactive({
    checkbox105: false,
    checkbox106: false,
    checkbox405: false,
    select280: 1,
    filePath:"",
})

onMounted(async () => {
    data.checkbox105 = await GPP_GetInteger(105) === 1;
    data.checkbox106 = await GPP_GetInteger(106) === 1;
    data.checkbox405 = await GPP_GetInteger(405) === 1;
    data.filePath = await gamepp.setting.getString.promise(6);
    data.select280 = await GPP_GetInteger(280);
})
async function onChangeCheckbox(id:number,value:boolean) {
    const ivalue = value ? 1 : 0;
    if (id === 105) {
        await GPP_CheckedSendData(105, value);
        let is_DesktopMode = await gamepp.isDesktopMode.promise();
        if (is_DesktopMode === false) {
            let is_open_cursor = gamepp.setting.getInteger.sync(105) !== 0;
            let Obj = {};
            Obj['type'] = "CURSOR";
            Obj['capture'] = is_open_cursor;
            console.log(Obj);
            await gamepp.obs.sendMessage.promise(JSON.stringify(Obj));
        }
    }else if (id === 106) {
        await GPP_CheckedSendData(106, value);
        let is_DesktopMode = await gamepp.isDesktopMode.promise();
        if (ivalue === 1) {
            if (is_DesktopMode === false) {
                let is_open_capture_microphone = gamepp.setting.getInteger.sync(106) !== 0;
                let Obj = {};Obj['type'] = "MUTE";Obj['mute'] = !is_open_capture_microphone;Obj['a_type'] = 2;
                await gamepp.obs.sendMessage.promise(JSON.stringify(Obj));
            }
        } else {
            if (is_DesktopMode === false) {
                let is_open_capture_microphone = gamepp.setting.getInteger.sync(106) !== 0;
                let Obj = {};Obj['type'] = "MUTE";Obj['mute'] = !is_open_capture_microphone;Obj['a_type'] = 2;
                await gamepp.obs.sendMessage.promise(JSON.stringify(Obj));
            }
        }
    }else if (id === 405) {
        await GPP_CheckedSendData(405, value);
        let is_DesktopMode = await gamepp.isDesktopMode.promise();
        if (is_DesktopMode === false) {
            let is_open_capture_reshade = gamepp.setting.getInteger.sync(405) === 0;
            let Obj = {};
            Obj['type'] = "OVERLAY";
            Obj['capture'] = is_open_capture_reshade;
            console.log(Obj);
            await gamepp.obs.sendMessage.promise(JSON.stringify(Obj));
        }
    }
}

async function setSelect280 (e:number) {
    await GPP_WriteInteger(280, e);
    await SetVideoQualityParams();
}

function diskLeftSize(VideoPath) {
    return mbToSize(gamepp.readDiskSpace.sync(VideoPath));
}
function mbToSize (mb) {
    if (mb === 0) return '0 MB';
    if (mb < 1) return mb + ' MB';
    const k = 1024;
    const sizes = [' MB', ' GB', ' TB'];
    const i = Math.floor(Math.log(mb) / Math.log(k));
    return (Math.round((mb / Math.pow(k, i)) * 100)) / 100 + sizes[i];
}
//文件路径选择
function GPP_ShowOpenDialog(title,id){
    gamepp.dialog.showOpenDialog.async(value => ShowOpenDialogCallback(value[0],id), {title: title,properties:["openDirectory"]});
}
function ShowOpenDialogCallback(Path,id) {
    if (!Path['canceled']) {
        let regex = /^[a-zA-Z]:(([a-zA-Z]*)||([a-zA-Z]*\\))*/;
        let array = regex.exec(Path['filePaths'][0]);
        if (array === null) {ElMessage.error('错误');return;}
        //无法写入
        // if ((Path['filePaths'][0]).indexOf('C:\\Program Files (x86)') === 0 || (Path['filePaths'][0]).indexOf('C:\\Program Files') === 0) {AlertLayuiMsg('无法选择当前目录');return false;}
        data.filePath = Path['filePaths'][0];//录像保存路径
        gamepp.setting.setString.promise(id, Path['filePaths'][0]);
    }
}

function GPP_OpenVideoPath() {
    const value = data.filePath;
    const szSendData = {};
    szSendData['value'] = value;
    gamepp.shell.openPath(szSendData['value'])
}
</script>

<template>
    <div class="obs-setting-box">
        <span class="obs-setting-box-title">{{$t('Setting.setting')}}</span>
        <ul>
            <li>
                <span>{{$t('video.CaptureMode')}}</span>
                <div class="capture-mode">
                    <el-select popper-class="select-family" v-model="data.select280" @change="setSelect280">
                        <el-option :value="1" :label="$t('video.gameWindow')">{{$t('video.gameWindow')}}</el-option>
                        <el-option :value="2" :label="$t('video.desktopWindow')">{{$t('video.desktopWindow')}}</el-option>
                    </el-select>
                </div>
            </li>
            <li class="flex-start">
                <el-checkbox :title="$t('video.displayMouse')" v-model="data.checkbox105" @change="(e)=>onChangeCheckbox(105,e)" :label="$t('video.displayMouse')" />
                <el-checkbox :title="$t('video.recordMicrophone')" v-model="data.checkbox106" @change="(e)=>onChangeCheckbox(106,e)" :label="$t('video.recordMicrophone')" />
                <el-checkbox :title="$t('video.gameGraphics')" v-model="data.checkbox405" v-show="data.select280 === 1" @change="(e)=>onChangeCheckbox(405,e)" :label="$t('video.gameGraphics')" />
            </li>
            <li class="file-path">
                <p>{{$t('video.fileSavePath')}}</p>
                <section>
                    <div class="path">{{data.filePath}}</div>
                    <el-button type="primary" color="#336AB5" @click="GPP_ShowOpenDialog($t('video.selectVideoSavePath'),6)">{{$t('video.edit')}}</el-button>
                    <el-button type="primary" color="#336AB5" @click="GPP_OpenVideoPath">{{$t('video.open')}}</el-button>
                </section>
                <div class="disk-left-space">
                    <div class="point"></div>
                    <span>{{$t('video.diskFreeSpace')}}{{diskLeftSize(data.filePath)}}</span>
                </div>
            </li>
        </ul>
    </div>
</template>

<style scoped lang="scss">
.obs-setting-box {
    position: absolute;
    left: 17px;
    top: 240px;
    width: 450px;
    height: 240px;
    border-radius: 4px;
    background: #2B2C37;
    box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.3);
    font-size: 12px;
    padding: 10px;

    &-title {
        color: #ffffff;
    }

    ul {
        display: flex;
        flex-flow: column nowrap;
        flex-direction: column;
        height: 195px;
        justify-content: space-between;
        margin-top: 10px;
        li {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #777777;
            :deep(.el-checkbox__label) {
                font-size: 12px;
            }
        }
        li.flex-start {
            justify-content: flex-start;
            .el-checkbox{
                max-width: 117px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }

        li.file-path {
            display: flex;
            flex-flow: column nowrap;
            align-items: start;
            section {
                margin-top: 19px;
                margin-bottom: 10px;
                color: #ffffff;
                display: flex;
                flex-flow: row nowrap;
                align-items: center;
                .path {
                    width: 250px;
                    height: 30px;
                    border-radius: 4px;
                    background: #22232E;
                    margin-right: 12px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    padding: 0 10px;
                    line-height: 30px;
                    color: #ffffff;
                }
            }

            .disk-left-space {
                width: 100%;
                display: flex;
                flex-flow: row nowrap;
                align-items: center;
                justify-content: flex-end;
                .point {
                    width: 6px;
                    height: 6px;
                    background-color: #35D57D;
                    border-radius: 50%;
                    margin-right: 6px;
                }
            }
        }
    }
    .capture-mode {
        width: 156px;
    }
}
</style>
<style>
.path .el-button{
    max-width: 80px;
}
</style>
