import { createApp, h } from 'vue';
import MessageBox from './MessageBox.vue'; // 你的 MessageBox 组件

export const useMessageBox = () => {
    const showMessageBox = (title: string, message: string, onConfirm?: () => void, onCancel?: () => void,cancelText?: string,confirmText?: string) => {
        const container = document.createElement('div');
        document.body.appendChild(container);

        const instance = createApp({
            render() {
                return h(MessageBox, {
                    title,
                    message,
                    onConfirm: () => {
                        onConfirm && onConfirm();
                        instance.unmount();
                        document.body.removeChild(container);
                    },
                    onCancel: () => {
                        onCancel && onCancel();
                        instance.unmount();
                        document.body.removeChild(container);
                    },
                    cancelText,
                    confirmText
                });
            }
        });

        instance.mount(container);
    };

    return {
        showMessageBox
    };
};
