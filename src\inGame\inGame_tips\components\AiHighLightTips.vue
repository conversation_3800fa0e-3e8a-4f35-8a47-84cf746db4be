<script setup lang="ts">
import {onMounted, ref} from "vue";
let auto_rec_count = ref(0);
let saving = ref(false);
let auto_rec = ref(true);
let zoom1 = ref(1)
let timer:any;
onMounted(async ()=>{
    await IsReady()
    setTimeout(async ()=>{
        gamepp.webapp.onInternalAppEvent.addEventListener((msg:any) => {
            console.warn(msg)
            if (msg === "kill") {
                saving.value = true
                auto_rec.value = false
                timer = setTimeout(()=>{
                    saving.value = false
                    auto_rec.value = true
                },10000)
            }

            if (msg === 'SaveRecord') {
                auto_rec_count.value++;
                timer && clearTimeout(timer)
                saving.value = false
                auto_rec.value = true
            }

            if (msg === "SaveRecord_False") {
                timer && clearTimeout(timer)
                saving.value = false
                auto_rec.value = true
            }
        });
        // 获取游戏窗口大小
        const GameWindowWidth = await gamepp.game.getWidth.promise()
        console.warn(GameWindowWidth)

        setTimeout(()=>{
            zoom1.value = GameWindowWidth / 3840
        },3000)

        gamepp.game.ingame.onGameWindowStateChanged.addEventListener(listenGameWindowStateChanged)
    },2000)
})
function listenGameWindowStateChanged(e:any) {
    console.warn(e)
    let GameWindowWidth = e.windowWidth

    // $('.auto_rec').css('zoom', `${GameWindowWidth / 3840}`)
    // $('.saving').css('zoom', `${GameWindowWidth / 3840}`)
    zoom1.value = GameWindowWidth / 3840
}
async function IsReady (time = 100) {
    return new Promise((resolve, reject) => {
        let nRet = 0;
        let isready_time = setInterval(async () => {
            try {nRet = await gamepp.apiinfo.isready.promise();} catch (error) {console.log(error);}
            if (nRet === 1) {
                clearInterval(isready_time)
                resolve(1)
            }
        }, time)

    })
}
</script>

<template>
<div class="ai-high-light-tips">
    <div class="auto_rec" v-show="auto_rec" :style="{zoom: zoom1}">
        <div class="HighLight_img_box">
            <img class="HighLight_img"
                 src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyNpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTQ4IDc5LjE2NDAzNiwgMjAxOS8wOC8xMy0wMTowNjo1NyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIDIxLjAgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjYxMzBERTE1Q0VGMjExRUU5NjREQ0ExQkZDMzMzMENGIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjYxMzBERTE2Q0VGMjExRUU5NjREQ0ExQkZDMzMzMENGIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6NjEzMERFMTNDRUYyMTFFRTk2NERDQTFCRkMzMzMwQ0YiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NjEzMERFMTRDRUYyMTFFRTk2NERDQTFCRkMzMzMwQ0YiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz7ByMH0AAAE0ElEQVR42qRVfUzUdRj//O6F41Xe3z1AVFqLoTNkE3txrbUIsNqaZK2Uo9S2Ys2abipz0zZnufVPpvgSK1dDg+VWDdSV48UNaogOGaggddDBcZBwdxx3x3G/Pt+f97ODYKt4ts/2fXm+z/vzfKXx8XFIkiSwF8DrRBaWRlbie1mWdxOQAgo+5+E7ygGVLYWCZDRxXaLz+/3vC+Eqg6pAMC6RnibOSVar1cyFUZzMzvoRHR2FEJ0e4/fv/2tvhDEGgwHhERGY4LsgcmjoQQqhMC2LjMBPTa2o++FHJCcnKWezs7MQ9yrEmUajUdbizufzkTcZYZGRaL5yBV6vFzINC/DrhAKP+lir1cI5NYVLV5vR/msHkhPiH1qowj09DZ9QotNBItLS0mC+exebCwrQ1tKChMREyH8b5cPg4KCdkAXMZrM843bLPT29cslrb8q19d9Rpl+m2w/uh4Zki8Ui1544IY86HLKg2mPHRLLkZzIzlb3VZpMHKScg0y6SHBRLwGK1IjsrA7t3VOBi42VkGZfDNmFH7uoVCA0xID41FTMOB76srERCeDgqjh9HCt9+de0a3AyXi3caRgKBIpmjQJDfL2PIMoxNGzcgggIOfvIpHs3NRVxcLIyJ8dCTJ4Nh+HjPHsRynU0cPXQI6enp6L93D9pAflTSiEQFQ720O524fvMmrMN/AJ5pyLHJ0CQl4/KRI/imvBzPkWc58W5RETZVVcFssQjrMF/ePzxQaWz8T5QWPQ8DS9ZUvg3mkRHUnT2FAUM4NLx3EonEyzU18NAY1+QktEw65vWPTmhZiET5GUJC8MbWMlzv78NnHx3GK3xb98UpZK5ajZOmbXA6JjHQ0YGVhYWiQ7GQLE1wjc+HaBoPG2jv2jVYSeb6mtPY8tZOGDe/gKoxG5499zVGb/fC7/EuKkPq6uqy821U8BzRh4bC5/HAkJGB4eZmtJSWYhXvDpPnFlFGJbWnT8LFdW/nDWh9XmjDIoSE+Q445iRZXPvYiSNsHENWFjzDw2goKYEkGkxUC6tq3fr1OH+mGltMbyNcVNGatfBpQ+B1OR92dzDmhEgm9MuWYZAK6rdvR0NxMcIoJJqYyc7GIxwFDa2tyGXZfltzBq9W7EQMM74ij0o0OiqZUqIQLHOOgpmZGXhZEU/s2gV/dzcu9fXBS+G/CAVGI1xMpsRGamxsVObPeSa8zLQDcSyenHWPQw4JAyfB4gqES2Hx8bB2duJqTw8ymI/C/fvx4oED6GPC2/ftw28sVx2rSyhJSkrCBSZe9ST7sTy46UXwgJTa2tocNDBSqVm9XgnRUYZGSyGV1dUIz8uDTM/cHCET9CgsJkaZljk5ORhmjvLz8xXPnyx+CRfrL8ByuwdORkES44JJ1ppMpoOMm07ELpQj9xYnokxF71E4uLffuQOP+PXYF6HR0UqeBNlsNhgZtkKGbWx0FO1NP8PL/2Qj91NOB/wPpu+s1NraKn6IGLWuhHtxHGiiM70ul2rJgiQsTyVvKMvabrfj94F+REXHQk8DAz/itBgVZ7n4QH0k/oRJWqSMEIZCXmSUqLyj5BUfkI5jIiklDR72T1BH3xCh+ZAL9jo2qB78V1Jrfh5NElt1gQ5+SvwdXBYEh+t/0hTRzYlgIsx/CTAAmGocYgEMsBwAAAAASUVORK5CYII="/>
            <div class="circle"></div>
        </div>
        <div class="right_box">
            <p>Auto REC</p>
            <span id="auto_rec_text">{{auto_rec_count}}</span>
        </div>

    </div>
    <div class="saving" v-show="saving" :style="{zoom: zoom1}">
        <div class="HighLight_img_box">
            <img class="HighLight_img"
                 src="data:image/png;base64,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"/>
            <img src="../assets/saving.png" alt="" class="saving_img">
        </div>
        <div class="right_box">
            <p>Saving</p>
            <span id="saving_text">+1</span>
        </div>
    </div>
</div>
</template>

<style scoped lang="scss">
.ai-high-light-tips{
    width: 230px;
    height: 112px;
}
.auto_rec,.saving {
    display: flex;
    flex-flow: row nowrap;
    position: absolute;
    /* margin-right: 50px; */
    right: 50px;
    margin-top: 50px;
}

.circle {
    width: 16px;
    height: 16px;
    background: #00fda7;
    position: absolute;
    top: 24px;
    left: 24px;
    border-radius: 50%;
}

.HighLight_img_box {
    position: relative;
    width: 40px;
    margin-bottom: 20px;
    margin-right: 10px;
}

.HighLight_img {
    width: 32px;
    height: 32px;
}

.saving_img {
    animation: 0.5s linear saving_img infinite;
    position: absolute;
    top: 19px;
    left: 19px;
}

.right_box {
    width: 123px;
    display: flex;
    flex-flow: column nowrap;
    align-items: center;
}

.right_box p{
    font-weight: bold;
    font-size: 24px;
    color: #00FFA8;
    line-height: 16px;
    margin-bottom: 8px;
}

.right_box span{
    font-weight: 400;
    font-size: 24px;
    color: #FFFFFF;
    line-height: 16px;
}

@keyframes saving_img {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
</style>
