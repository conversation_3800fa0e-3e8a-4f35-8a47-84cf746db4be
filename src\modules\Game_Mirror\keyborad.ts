
export function keyCodeTransform (inputCode:any) {

    const codeArr = [
  
      { key: '0', keyCode: 48, realCode: 11 },
  
      { key: '1', keyCode: 49, realCode: 2 },
  
      { key: '2', keyCode: 50, realCode: 3 },
  
      { key: '3', keyCode: 51, realCode: 4 },
  
      { key: '4', keyCode: 52, realCode: 5 },
  
      { key: '5', keyCode: 53, realCode: 6 },
  
      { key: '6', keyCode: 54, realCode: 7 },
  
      { key: '7', keyCode: 55, realCode: 8 },
  
      { key: '8', keyCode: 56, realCode: 9 },
  
      { key: '9', keyCode: 57, realCode: 10 },
  
      { key: 'A', keyCode: 65, realCode: 30 },
  
      { key: 'B', keyCode: 66, realCode: 48 },
  
      { key: 'C', keyCode: 67, realCode: 46 },
  
      { key: 'D', keyCode: 68, realCode: 32 },
  
      { key: 'E', keyCode: 69, realCode: 18 },
  
      { key: 'F', keyCode: 70, realCode: 33 },
  
      { key: 'G', keyCode: 71, realCode: 34 },
  
      { key: 'H', keyCode: 72, realCode: 35 },
  
      { key: 'I', keyCode: 73, realCode: 23 },
  
      { key: 'J', keyCode: 74, realCode: 36 },
  
      { key: 'K', keyCode: 75, realCode: 37 },
  
      { key: 'L', keyCode: 76, realCode: 38 },
  
      { key: 'M', keyCode: 77, realCode: 50 },
  
      { key: 'N', keyCode: 78, realCode: 49 },
  
      { key: 'O', keyCode: 79, realCode: 24 },
  
      { key: 'P', keyCode: 80, realCode: 25 },
  
      { key: 'Q', keyCode: 81, realCode: 16 },
  
      { key: 'R', keyCode: 82, realCode: 19 },
  
      { key: 'S', keyCode: 83, realCode: 31 },
  
      { key: 'T', keyCode: 84, realCode: 20 },
  
      { key: 'U', keyCode: 85, realCode: 22 },
  
      { key: 'V', keyCode: 86, realCode: 47 },
  
      { key: 'W', keyCode: 87, realCode: 17 },
  
      { key: 'X', keyCode: 88, realCode: 45 },
  
      { key: 'Y', keyCode: 89, realCode: 21 },
  
      { key: 'Z', keyCode: 90, realCode: 44 },
  
      { key: 'F1', keyCode: 112, realCode: 59 },
  
      { key: 'F2', keyCode: 113, realCode: 60 },
  
      { key: 'F3', keyCode: 114, realCode: 61 },
  
      { key: 'F4', keyCode: 115, realCode: 62 },
  
      { key: 'F5', keyCode: 116, realCode: 63 },
  
      { key: 'F6', keyCode: 117, realCode: 64 },
  
      { key: 'F7', keyCode: 118, realCode: 65 },
  
      { key: 'F8', keyCode: 119, realCode: 66 },
  
      { key: 'F9', keyCode: 120, realCode: 67 },
  
      { key: 'F10', keyCode: 121, realCode: 68 },
  
      { key: 'F11', keyCode: 122, realCode: 87 },
  
      { key: 'F12', keyCode: 123, realCode: 88 },
  
      { key: 'NUM0', keyCode: 96, realCode: 82 },
  
      { key: 'NUM1', keyCode: 97, realCode: 79 },
  
      { key: 'NUM2', keyCode: 98, realCode: 80 },
  
      { key: 'NUM3', keyCode: 99, realCode: 81 },
  
      { key: 'NUM4', keyCode: 100, realCode: 75 },
  
      { key: 'NUM5', keyCode: 101, realCode: 76 },
  
      { key: 'NUM6', keyCode: 102, realCode: 77 },
  
      { key: 'NUM7', keyCode: 103, realCode: 71 },
  
      { key: 'NUM8', keyCode: 104, realCode: 72 },
  
      { key: 'NUM9', keyCode: 105, realCode: 73 },
  
      { key: 'NUM *', keyCode: 106, realCode:  55 },
  
      { key: 'NUM +', keyCode: 107, realCode: 78 },
  
      { key: 'NUM Enter', keyCode: 108, realCode: 3612 },
  
      { key: 'NUM -', keyCode: 109, realCode: 74 },
  
      { key: 'NUM .', keyCode: 110, realCode: 83 },
  
      { key: 'NUM /', keyCode: 111, realCode: 3637 },
  
      { key: 'NUMLOCK', keyCode: 144, realCode: 69 },
  
      { key: '空', keyCode: 8, realCode: -1 }, // BackSpace realCode 14
  
      { key: 'Tab', keyCode: 9, realCode: 15 },
  
      { key: 'Clear', keyCode: 12, realCode: 57420 },
  
      { key: 'Enter', keyCode: 13, realCode: 28 },
  
      { key: 'Shift', keyCode: 16, realCode: 42 || 54 },
  
      { key: 'Ctrl', keyCode: 17, realCode: 29 || 3613 },
  
      { key: 'Alt', keyCode: 18, realCode: 56 || 3640 },
  
      { key: 'Pause', keyCode: 19, realCode: 3653 },
  
      { key: 'CapsLock', keyCode: 20, realCode: 58 },

      { key:'Shift', keyCode: 160},

      { key:'Ctrl' , keyCode: 162},

      { key:'Win' , keyCode: 91},

      { key:'Alt' , keyCode: 164},

      { key:'Del' , keyCode: 46},

      { key:'Print' , keyCode: 44},

      { key:'Scrlk' , keyCode: 145},

      { key: 'Esc', keyCode: 27, realCode: 1 },
  
      { key: 'Space', keyCode: 32, realCode: 57 },
  
      { key: 'PageUp', keyCode: 33, realCode: 3657 },
  
      { key: 'PageDown', keyCode: 34, realCode: 3665 },
  
      { key: 'End', keyCode: 35, realCode: 3663 },
  
      { key: 'Home', keyCode: 36, realCode: 3655 },
  
      { key: 'LeftArrow', keyCode: 37, realCode: 57419 },
  
      { key: 'UpArrow', keyCode: 38, realCode: 57416 },
  
      { key: 'RightArrow', keyCode: 39, realCode: 57421 },
  
      { key: 'DownArrow', keyCode: 40, realCode: 57424 },
  
      { key: 'Select', keyCode: 41, realCode: 57453 },
  
      { key: 'Print', keyCode: 42, realCode: 3639 },
  
      { key: 'Insert', keyCode: 45, realCode: 3666 },
  
      { key: '空', keyCode: 46, realCode: -1 }, // Delete realCode 3667
  
      { key: 'Help', keyCode: 47, realCode: 65397 },
  
      { key: ';', keyCode: 186, realCode: 39 },
  
      { key: '=', keyCode: 187, realCode: 13 },
  
      { key: ',', keyCode: 188, realCode: 51 },
  
      { key: '-', keyCode: 189, realCode: 12 },
  
      { key: '.', keyCode: 190, realCode: 52 },
  
      { key: '/', keyCode: 191, realCode: 53 },
  
      { key: '`', keyCode: 192, realCode: 41 },
  
      { key: '[', keyCode: 219, realCode: 26 },
  
      { key: '\\', keyCode: 220, realCode: 43 },
  
      { key: ']', keyCode: 221, realCode: 27 },
  
      { key: '\'', keyCode: 222, realCode: 40 }
  
    ]
  
    let resData = null
  
    codeArr.forEach((v) => {
  
      if (v.keyCode === inputCode) {
  
        resData = v.key
  
      }
  
    })
  
    return resData
  
  }