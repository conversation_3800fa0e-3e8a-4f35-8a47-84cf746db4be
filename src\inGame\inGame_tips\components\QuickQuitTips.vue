<template>
  <div class="Rebound-hint">
    <div class="Rebound-hint-herder">
      <div class="Game-plus-logo">
        <img src="../../../assets/img/Public/logo.png" alt="" />
      </div>
      <div class="Rebound-hint-close" @click="GPP_CancelExitGame()"></div>
    </div>
    <div class="Rebound-hint-content">
      <div class="performance">
        <p class="generated" set-lan="text:">现在退出游戏吗？</p>
        <p class="Performance-report" set-lan="text:">
          (<span id="SecondVal">{{ SecondVal }}</span><span>秒后自动退出）</span>
        </p>
      </div>
      <div class="Rebound-hint-option">
        <div class="btn-Check">
          <input
            type="button"
            value="取消(Esc)"
            set-lan="val:"
            @click="GPP_CancelExitGame()"
          />
        </div>
        <div class="btn-Check">
          <input
            type="button"
            value="立即退出(Alt+F4)"
            set-lan="val:"
            @click="GPP_ImmediateExitGame()"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref,onMounted } from "vue";

// @ts-ignore
const gamepp = window.gamepp;
let SecondVal = ref(10);
onMounted(async ()=>{
    gamepp.webapp.onInternalAppEvent.addEventListener(value => {
        console.log(value);
        if (value === 0) {
            EXitSetInterval()
        }else if (value === -1){
            GPP_CancelExitGame();
        }
    });
    await gamepp.setting.setBool2.promise('window','ingame_quit_game_tips',true);
})

//立即退出 immediately
async function GPP_ImmediateExitGame() {
    let Obj = {};
    Obj['action'] = 'ImmediateExitGame';
    await gamepp.webapp.sendInternalAppEvent.promise('background', Obj);
    gamepp.webapp.windows.close.sync('ingame_quit_game_tips');
}
//执行倒计时退出
let setInterval1:any = null;
//取消退出
async function GPP_CancelExitGame() {
    setInterval1 && clearInterval(setInterval1);
    let Obj = {};
    Obj['action'] = 'CancelExitGame';
    await gamepp.webapp.sendInternalAppEvent.promise('background', Obj);
    gamepp.webapp.windows.close.sync('ingame_quit_game_tips');
}

function EXitSetInterval() {
    setInterval1 && clearInterval(setInterval1);
    SecondVal.value = 10;
    setInterval1 = setInterval(function () {
        SecondVal.value--;
        if (SecondVal.value === 0) {
            clearInterval(setInterval1);
            GPP_ImmediateExitGame();
        }
    }, 1000)
}
</script>

<style scoped lang="scss">
.Rebound-hint {
  width: 350px;
  height: 240px;
  position: absolute;
}
.performance {
  text-align: center;
  width: 100%;
  padding-bottom: 6px;
  margin-top: 70px;
}
.btn-Check {
  width: 120px;
  margin-right: 45px;
  text-align: center;
}
.btn-Check input {
  font-size: 12px;
}
.generated {
  color: #0089e9;
}
.Performance-report {
  color: #707780;
  font-size: 14px;
  margin-top: 5px;
}
.Rebound-hint-content {
  height: 215px;
}
.Rebound-hint {
  width: 300px;
  height: 200px;
  position: relative;
}
.Rebound-hint-herder {
  width: 100%;
  height: 25px;
  background: #34343b;
}
.Game-plus-logo {
  width: 82px;
  height: 12px;
  margin-left: 12px;
  padding-top: 2px;
  float: left;
}
.Game-plus-logo img {
  width: 100%;
  height: 100%;
}
.Rebound-hint-close {
  margin-right: 8px;
  margin-top: 2px;
  cursor: pointer;
  width: 20px;
  height: 20px;
  float: right;
  background: url("../../../assets/icon/Public/btn_window_close_0.png")
    no-repeat;
  background-size: 100% 100%;
}
.Rebound-hint-close:hover {
  background: url("../../../assets/icon/Public/btn_window_close_1.png")
    no-repeat;
  background-size: 100% 100%;
}
.Rebound-hint-close:active {
  background: url("../../../assets/icon/Public/btn_window_close_2.png")
    no-repeat;
  background-size: 100% 100%;
}
.Rebound-hint-content {
  width: 100%;
  height: 175px;
  background: #21222a;
}
.performance {
  float: left;
  margin-top: 45px;
}
.performance-icon {
  width: 45px;
  height: 51px;
  float: left;
  margin-left: 12px;
}
.performance-icon img {
  width: 100%;
  height: 100%;
}
.Report {
  float: left;
  margin-top: 6px;
  margin-left: 30px;
}
.Performance-report {
  font-size: 14px;
  color: #ffffff;
}
.generated {
  font-size: 16px;
  color: #108ff0;
  font-weight: bold;
}
.Rebound-hint-option {
  float: left;
  margin-top: 46px;
  width: 100%;
}
.option-checkbox {
  float: left;
  margin-left: 12px;
}
.option-checkbox span {
  font-size: 12px;
  color: #909399;
}
.btn-Check {
  width: 120px;
  height: 26px;
  float: right;
  margin-right: 20px;
  margin-top: -3px;
}
.btn-Check input {
  background: #108ff0;
  font-size: 14px;
  color: #ffffff;
  cursor: pointer;
  border-radius: 2px;
  width: 100%;
  height: 100%;
}
.btn-Check input:hover {
  background: #0fb3ff;
}
.btn-Check input:active {
  background: #0884e3;
}
input[type=checkbox],
input[type=radio]{  -webkit-appearance: none;appearance: none; width: 15px;height: 15px;margin: 0;cursor: pointer;vertical-align: bottom;background: rgba(0,0,0,0.1);border: 1px solid #464b4e;
   -webkit-border-radius: 1px;-moz-border-radius: 1px;border-radius: 3px;-webkit-box-sizing: border-box;-moz-box-sizing: border-box;box-sizing: border-box;position: relative;top:-7px;}
input[type=checkbox]:active,
input[type=radio]:active {border-color: #c6c6c6;background: #ebebeb;  }
input[type=checkbox]:checked,
input[type=radio]:checked {background: #11a4f8;}

.DataException{width: 100%;height: 560px;float: left;}
.interfere{text-align: center;margin-top: 70px;}
.DataException p{font-size: 16px;color: #EFEFEF;text-align: center;line-height: 32px;}
.text-label{background: #21222A;border: 1px solid #3C3C43; padding-left: 10px;box-sizing: border-box;color: #BBBBBB;}
.text-list{font-size: 14px;color: #1193F8;float: left;}
.issue{width: 520px;margin: 15px auto 0 auto;height: 170px;text-align: center}
.Report-input{width:110px;height:34px;background:rgba(17,147,248,1);border-radius:2px;font-size:14px;color:rgba(255,255,255,1);cursor: pointer;margin-top: 10px;}
.Report-input:hover{background: #0FB3FF;}
.Report-input:active{background: #0884E3;}
</style>
