<script setup lang="ts">
import {defineProps} from "vue"

const props = defineProps({
  color: {
    type: String,
    default: '#999999',
    required: true,
  },
})
</script>

<template>
  <div class="color-picker-cover">
    <div :style="{backgroundColor: props.color}"></div>
    <div :style="{backgroundColor: props.color}"></div>
    <div :style="{backgroundColor: props.color}"></div>
    <div :style="{backgroundColor: props.color}"></div>
  </div>
</template>

<style scoped lang="scss">
.color-picker-cover {
  width: 14px;
  height: 14px;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  align-content: space-between;
  position: absolute;
  cursor: pointer;

  div {
    width: 6px;
    height: 6px;
    border-radius: 1px;
  }
}
</style>
