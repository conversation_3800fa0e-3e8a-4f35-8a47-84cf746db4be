body[dir=rtl] {
    header{
        padding-left: 0;
    }
    ul.RightTopIcons{
        margin-left: inherit!important;
        margin-right: auto;
    }
    .GameRebound{
       .content{
        margin: 10px 20px 0 0;
        .chooseTime{
            .line{
                margin-left:0;
                margin-right: 10px;
            }
            .item{
                margin-left:0;
                margin-right: 100px;
            }
        }
        .detailBox{
            margin: 10px 20px 0 0;
            .time{
                margin-left: 20px;
                margin-right: 20px;
            }
            .gametime{
                margin-left: 0!important;
                margin-right: 10px;
            }
            .exe_rtl{
                margin-left:0!important;
                margin-right: 20px;
            }
            .exe{
                margin-left: 10px;
                .iconfont{
                    margin-left:0!important;
                    margin-right: 20px;
                }
            }
            .detailitem_rtl{
                margin-left:0!important;
                margin-right: auto;
            }
            .inner{
                margin-left:0!important;
                margin-right: 20px;
                .inner_box{
                    margin-right:0!important;
                    margin-left: 10px;
                }
                .box{
                    margin-left:0;
                    margin-right: 10px;
                }
            }
        }
       }
    }
    .electronic span:nth-child(3){
        margin-left:0!important;
        margin-right: 20px;
    }
    .electronic span:nth-child(6){
        margin-left:0!important;
        margin-right: 20px;
    }
    .GameRebound .container .content .detailBox,.GameRebound .container .content {
        margin: 10px 20px 0 0;
    }
    .GameRebound .container .content .dataTable {
        margin: 20px 20px 0 0;
    }
    .GameRebound .container .content .electronic {
        margin: 25px 20px 0 0;
    }
}
