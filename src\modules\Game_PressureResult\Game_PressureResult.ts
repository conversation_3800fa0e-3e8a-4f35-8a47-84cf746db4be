
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import App from './Game_PressureResult.vue'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
// import './reset.css'
// import './utils/reset.css'
// import './utils/font.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/theme-chalk/dark/css-vars.css'
import '../../assets/iconfont/iconfont.css'
import '../../assets/iconfont/iconfont.js'
// import '../Game_Home/reset.css'
// import 'dayjs/locale/zh-cn'
// import i18n from '../../assets/lang'

const app = createApp(App)
// app.use(createPinia())
// app.use(i18n)
app.mount('#app')
