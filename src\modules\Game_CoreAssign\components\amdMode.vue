<script setup lang="ts">
import {onMounted,watch} from "vue";
import {Core, CPUComplexInfo, processCoreAssignStore} from "../stores";
import {ElMessage} from "element-plus";
import {useI18n} from "vue-i18n";

interface computedCore extends Core {
    innerIndex: number;
}

const { t } = useI18n();
const $store = processCoreAssignStore();

onMounted(() => {
    $store.getGroupCpuSet();
})

const cpuInfo = (CCD: CPUComplexInfo) => {
    const e = CCD
    let threadCount = 0
    e.Cores.forEach(i => {
        threadCount += i.LogicalCores.length
    })
    return {
        l3Cache: Math.floor(e.CacheL3Size / 1048576),// 1048576 = 1MB
        coreCount: e.Cores.length,
        threadCount
    };
}
const largeAndSmallCore = (CCD: CPUComplexInfo) => {
    let largeCores: Array<computedCore> = [];// 大核
    let smallCores: Array<computedCore> = [];// 小核
    let littleCores: Array<computedCore> = [];// 小小核
    let tempArr: Array<number> = [];
    CCD.Cores.forEach((core) => {
        tempArr.push(core.EfficiencyClass);
    })
    tempArr = Array.from(new Set(tempArr)) // 去重
    tempArr.sort((a, b) => b - a)

    CCD.Cores.forEach((core, innerIndex) => {
        if (core.EfficiencyClass === tempArr[0]) {
            largeCores.push({...core, innerIndex})
        } else if (tempArr[1].toString() && core.EfficiencyClass === tempArr[1]) {
            smallCores.push({...core, innerIndex})
        } else if (tempArr[2].toString() && core.EfficiencyClass === tempArr[2]) {
            littleCores.push({...core, innerIndex})
        }
    })

    return {
        largeCores,
        smallCores,
        littleCores
    }
}

const handleAddOrRemoveCpuSet = (arr: Array<any>,CCD: CPUComplexInfo) => {
    if ($store.curGroupCpuSet.length >= 64) {
        ElMessage.warning(t('psc.max64'))
        return
    }
    let bool = false;
    $store.curGroupCpuSet.forEach((item) => {
        if (arr.includes(item)) {
            bool = true;
        }
    })
    if (bool) {
        $store.removeCpuSet(arr);
    } else {
        // 检查是否选择了不同的分组
        if ($store.curGroupCpuSet.length > 0 && $store.cpuComplexInfo) {
            let chooseGroup = CCD.Group
            let findItem = $store.cpuComplexInfo.find((item) => {
                return item.Cores.find((core) => {
                    return core.LogicalCores.find((logicalCore) => {
                        return $store.curGroupCpuSet.includes(logicalCore.CPUSetId)
                    })
                })
            })
            if (findItem && chooseGroup !== findItem.Group) {
                ElMessage.warning(t('psc.warning1'))
            }
        }
        $store.addCpuSet(arr);
    }
}
</script>

<template>
  <div class="container-amd">
    <div class="wrap">
        <template v-for="(CCD,index) in $store.cpuComplexInfo">
            <div class="CCD-box"
                 :key="'CCD'+index"
                 v-if="CCD.Cores.length > 0"
            >
                <div class="cpu-info">
                    <span>CCD {{ CCD.Cores[0] ? CCD.Cores[0].Die : '' }} CCX {{ CCD.ComplexId }}</span>
                    <span>L3 Cache</span>
                    <span>{{ cpuInfo(CCD).l3Cache }} MB</span>
                    <span>{{$t('psc.coreCount')}}:</span>
                    <span>{{ cpuInfo(CCD).coreCount }}</span>
                    <span>{{$t('psc.threadCount')}}:</span>
                    <span>{{ cpuInfo(CCD).threadCount }}</span>
                    <span v-if="CCD.hasOwnProperty('Group')">{{$t('psc.Group2')}}:{{CCD.Group}}</span>
                </div>

                <div v-if="largeAndSmallCore(CCD).largeCores.length > 0">
                    <div class="PEcore">
                        <span>{{ largeAndSmallCore(CCD).smallCores.length > 0 ? 'P Core' : 'Core' }}</span>
                    </div>
                    <div class="pCoreList">
                        <div class="box" v-for="(item,index) in largeAndSmallCore(CCD).largeCores" :key="'P'+index">
                            <div v-if="item.LogicalCores.length == 2">
                                <div
                                    class="core"
                                    :class="{'active': $store.isActiveCpuSet([item.LogicalCores[0].CPUSetId, item.LogicalCores[1].CPUSetId])}"
                                    @click="handleAddOrRemoveCpuSet([item.LogicalCores[0].CPUSetId, item.LogicalCores[1].CPUSetId],CCD)"
                                >
                                    <span>Core {{ item.Core }}</span>
                                </div>
                                <div
                                    class="t0"
                                    :class="{'active': $store.isActiveCpuSet([item.LogicalCores[0].CPUSetId])}"
                                    @click="handleAddOrRemoveCpuSet([item.LogicalCores[0].CPUSetId],CCD)"
                                >
                                    <span>T0</span>
                                </div>
                                <div
                                    class="t1"
                                    :class="{'active': $store.isActiveCpuSet([item.LogicalCores[1].CPUSetId])}"
                                    @click="handleAddOrRemoveCpuSet([item.LogicalCores[1].CPUSetId],CCD)"
                                >
                                    <span>T1</span>
                                </div>
                            </div>
                            <div
                                v-else
                                class="noThread"
                                :class="{'active': $store.isActiveCpuSet([item.LogicalCores[0].CPUSetId])}"
                                @click="handleAddOrRemoveCpuSet([item.LogicalCores[0].CPUSetId],CCD)"
                            >
                                <span>Core {{ item.Core }}</span>
                            </div>
                        </div>
                    </div>

                </div>

                <div v-if="largeAndSmallCore(CCD).smallCores.length > 0">
                    <div class="PEcore">
                        <span>E Core</span>
                    </div>
                    <div class="pCoreList">
                        <div class="box" v-for="(item,index) in largeAndSmallCore(CCD).smallCores" :key="'E'+index">
                            <div v-if="item.LogicalCores.length == 2">
                                <div
                                    class="core"
                                    :class="{'active': $store.isActiveCpuSet([item.LogicalCores[0].CPUSetId, item.LogicalCores[1].CPUSetId])}"
                                    @click="handleAddOrRemoveCpuSet([item.LogicalCores[0].CPUSetId, item.LogicalCores[1].CPUSetId],CCD)"
                                >
                                    <span>Core {{ item.Core }}</span>
                                </div>
                                <div
                                    class="t0"
                                    :class="{'active': $store.isActiveCpuSet([item.LogicalCores[0].CPUSetId])}"
                                    @click="handleAddOrRemoveCpuSet([item.LogicalCores[0].CPUSetId],CCD)"
                                >
                                    <span>T0</span>
                                </div>
                                <div
                                    class="t1"
                                    :class="{'active': $store.isActiveCpuSet([item.LogicalCores[1].CPUSetId])}"
                                    @click="handleAddOrRemoveCpuSet([item.LogicalCores[1].CPUSetId],CCD)"
                                >
                                    <span>T1</span>
                                </div>
                            </div>
                            <div
                                v-else
                                class="noThread"
                                :class="{'active': $store.isActiveCpuSet([item.LogicalCores[0].CPUSetId])}"
                                @click="handleAddOrRemoveCpuSet([item.LogicalCores[0].CPUSetId],CCD)"
                            >
                                <span>Core {{ item.Core }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>
  </div>
</template>

<style scoped lang="scss">
.container-amd {
  width: 100%;
  display: flex;
  flex-flow: column nowrap;
  flex-shrink: 0;
  flex: 1;

  .wrap {
    width: 100%;
    display: flex;
    flex-shrink: 0;
    flex-flow: row wrap;
    justify-content: flex-start;
    align-items: flex-start;
  }

  .CCD-box {
    width: 435px;
    min-height: 100px;
    background: #22232E;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 10px;
    margin-right: 10px;
    flex: 0 0 auto;

    .cpu-info {
      margin-bottom: 20px;

      span {
        margin-right: 10px;
      }
    }

    .PEcore {
      margin-bottom: 15px;
    }

    .pCoreList {
      display: flex;
      flex-flow: row wrap;

      .box {
        width: 82px;
        height: 62px;
        margin-bottom: 10px;
        margin-right: 10px;
      }

      .noThread {
        width: 82px;
        height: 62px;
        background: #2B2C37;
        border-radius: 4px;
        border: 2px solid transparent;
        text-align: center;
        line-height: 62px;

        &:hover {
          background: #343647;
        }

        &.active {
          background: #3E4050;
          border: 2px solid #3579D5;
        }
      }

      .core {
        width: 82px;
        height: 20px;
        background: #2B2C37;
        margin-bottom: 2px;
        text-align: center;
        line-height: 20px;
        cursor: pointer;
        border: 2px solid transparent;

        &.active {
          border: 2px solid #3579D5;
          border-radius: 4px;
          background: #3E4050;
        }

        &:hover {
          background: #3E4050;
        }
      }

      .t0, .t1 {
        cursor: pointer;
        width: 40px;
        height: 40px;
        background: #2B2C37;
        display: inline-block;
        text-align: center;
        line-height: 40px;
        border: 2px solid transparent;

        &.active {
          border: 2px solid #3579D5;
          border-radius: 4px;
          background: #3E4050;
        }

        &:hover {
          background: #3E4050;
        }
      }

      .t1 {
        margin-left: 2px;
      }
    }
  }
}
</style>
