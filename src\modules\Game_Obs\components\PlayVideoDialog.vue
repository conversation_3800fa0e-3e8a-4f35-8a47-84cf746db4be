<script setup lang="ts">
import {defineProps,defineEmits} from 'vue';
const props = defineProps({
    videoUrl: {
        type: String,
        default: ''
    },
    visible: {
        type: Boolean,
        default: false
    }
})
const emits = defineEmits(['close'])

function close() {
    emits('close')
}
</script>

<template>
    <div class="play-video-dialog" v-show="visible">
        <div class="close" @click="close">
            <span class="iconfont icon-Close"></span>
        </div>
        <video :src="videoUrl" controls class="video_box" muted autoplay></video>
    </div>
</template>

<style scoped lang="scss">
.play-video-dialog {
    position: fixed;
    top: 40px;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 99999;

    .close {
        width: 20px;
        height: 20px;
        position: absolute;
        top: 20px;
        right: 20px;
        cursor: pointer;
        color: #fff;
        font-size: 20px;
        line-height: 20px;
        text-align: center;
    }

    .video_box {
        width: 900px;
        height: 640px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
}
</style>
