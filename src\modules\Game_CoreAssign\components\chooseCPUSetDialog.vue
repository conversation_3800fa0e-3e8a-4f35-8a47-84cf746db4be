<script setup lang="ts">
import {processCoreAssignStore,Core, CPUComplexInfo, LogicalCore} from "../stores";
import PButton from "./pButton.vue";
import {computed, onBeforeMount} from "vue";
import AmdMode from "./amdMode.vue";
import IntelMode from "./intelMode.vue";
import {useI18n} from "vue-i18n";
interface computedCore extends Core {
    innerIndex: number;
}
const $store = processCoreAssignStore()
const { t } = useI18n();
onBeforeMount(()=>{
    const i = $store.display.process_groups.findIndex((item)=>{
        return item.id == $store.display.activeGroup
    })
    if (i !== -1) {
        $store.curGroupCpuSet = $store.display.process_groups[i].cpuSet
    }else{
        $store.curGroupCpuSet = []
    }
})

const methods = {
    quickChooseCpuSet(type: string,index?:number) {
        $store.disableCpuSet()

        if (type === 'default') {
            $store.curGroupCpuSet = $store.systemCpuSet;
        }else if (type === "spaced") {// 间隔
            let arr: Array<number> = [];
            $store.cpuComplexInfo.forEach((item: CPUComplexInfo) => {
                item.Cores.forEach((core: Core, index: number) => {
                    if (index % 2 === 0) {
                        core.LogicalCores.forEach((v) => {
                            arr.push(v.CPUSetId)
                        })
                    }
                })
            })
            if (arr.length > 64) {
                ElMessage.warning(t('psc.max64'))
                return
            }
            $store.curGroupCpuSet = arr;
        }else if (type === "fx") {// 反选
            let arr_fx: Array<number> = []; // 拿到所有cpuset
            $store.cpuComplexInfo.forEach((item: CPUComplexInfo) => {
                item.Cores.forEach((core: Core) => {
                    core.LogicalCores.forEach((v) => {
                        arr_fx.push(v.CPUSetId)
                    })
                })
            })
            $store.curGroupCpuSet.forEach((item: number) => {
                let i = arr_fx.findIndex((CPUSetId: number) => CPUSetId === item)
                if (i !== -1) {
                    arr_fx.splice(i, 1)
                }
            })
            if (arr_fx.length > 64) {
                ElMessage.warning(t('psc.max64'))
                return
            }
            $store.curGroupCpuSet = arr_fx;
        }else if(type === "noThread") { // 非超线程
            let arr_noThread: Array<number> = [];
            $store.cpuComplexInfo.forEach((item: CPUComplexInfo) => {
                item.Cores.forEach((core: Core) => {
                    arr_noThread.push(core.LogicalCores[0].CPUSetId)
                })
            })
            if (arr_noThread.length > 64) {
                ElMessage.warning(t('psc.max64'))
                return
            }
            $store.curGroupCpuSet = arr_noThread;
        }else if (type === "CCD") {
            let arr_CCD: Array<number> = [];
            $store.cpuComplexInfo[index!].Cores.forEach((core: Core) => {
                core.LogicalCores.forEach((item: LogicalCore) => {
                    arr_CCD.push(item.CPUSetId)
                })
            })
            $store.curGroupCpuSet = arr_CCD;
        }else if (type === 'PCore') { // 选大核
            let arr_PCore: Array<number> = [];
            $store.cpuComplexInfo.forEach((item: CPUComplexInfo) => {
                methods.largeAndSmallCore(item).largeCores.forEach((item2)=>{
                    item2.LogicalCores.forEach((LogicalCore)=>{
                        arr_PCore.push(LogicalCore.CPUSetId)
                    })
                })
            })
            $store.curGroupCpuSet = arr_PCore;
        }else if (type === 'ECore') { // 选小核
            let arr_ECore: Array<number> = [];
            $store.cpuComplexInfo.forEach((item: CPUComplexInfo) => {
                methods.largeAndSmallCore(item).smallCores.forEach((item2)=>{
                    item2.LogicalCores.forEach((LogicalCore)=>{
                        arr_ECore.push(LogicalCore.CPUSetId)
                    })
                })
            })
            $store.curGroupCpuSet = arr_ECore;
        }
        // 限制选择的CPUSet不超过64
        if ($store.curGroupCpuSet.length > 64) {
            // 第64个后的全部砍掉
            $store.curGroupCpuSet = $store.curGroupCpuSet.slice(0, 64)
        }
        // 检查是不是选择了不同组的
        let tempSet = new Set()
        $store.curGroupCpuSet.forEach((setId)=>{
            $store.cpuComplexInfo.find((item)=>{
                return item.Cores.find((core)=>{
                    return core.LogicalCores.find((LogicalCore)=>{
                        tempSet.add(item.Group)
                        return LogicalCore.CPUSetId === setId
                    })
                })
            })
        })
        if (tempSet.size > 1 && type !== 'CCD') {
            ElMessage.warning(t('psc.warning1'))
        }
    },
    largeAndSmallCore (CCD: CPUComplexInfo) {
        let largeCores: Array<computedCore> = [];// 大核
        let smallCores: Array<computedCore> = [];// 小核
        let littleCores: Array<computedCore> = [];// 小小核
        let tempArr: Array<number> = [];
        CCD.Cores.forEach((core) => {
            tempArr.push(core.EfficiencyClass);
        })
        tempArr = Array.from(new Set(tempArr)) // 去重
        tempArr.sort((a, b) => b - a)

        CCD.Cores.forEach((core, innerIndex) => {
            if (core.EfficiencyClass === tempArr[0]) {
                largeCores.push({...core, innerIndex})
            } else if (tempArr[1].toString() && core.EfficiencyClass === tempArr[1]) {
                smallCores.push({...core, innerIndex})
            } else if (tempArr[2].toString() && core.EfficiencyClass === tempArr[2]) {
                littleCores.push({...core, innerIndex})
            }
        })

        return {
            largeCores,
            smallCores,
            littleCores
        }
    },
    closeDialog() {
        $store.display.showChooseCPUSetDialog = false
    },
    confirm() {
        const i = $store.display.process_groups.findIndex((item)=>{
            return item.id == $store.display.activeGroup
        })
        if (i !== -1) {
            $store.display.process_groups[i].cpuSet = $store.curGroupCpuSet
            $store.saveProcessGroup()
        }
        $store.disableCpuSet()
        $store.display.showChooseCPUSetDialog = false
    }
}
const haveECore = computed(() => {
    let s = false
    $store.cpuComplexInfo.forEach((CCD)=>{
        if (methods.largeAndSmallCore(CCD).smallCores.length > 0) s = true
    })
    return s;
})
const ccdOrCcx = computed(()=>{
    let tempSet = new Set()
    $store.cpuComplexInfo.forEach((item)=>{
        item.Cores.forEach((core)=>{
            tempSet.add(core.Die)
        })
    })
    if (tempSet.size < $store.cpuComplexInfo.length) {
        return 'CCX'
    }else{
        return 'CCD'
    }
})
</script>

<template>
    <div class="chooseCPUSetDialog">
        <section>
            <header>
                <span class="iconfont icon-threads"></span>
                <span>{{$t('psc.threadAllocation')}}</span>
                <span class="iconfont icon-Close" @click="methods.closeDialog"></span>
            </header>

            <div class="scroll-box scroll-y">
                <div class="flex-items-center mb-20">
                    <span class="color777">CPU:</span>
                    <span class="cpu-name">{{ $store.display.cpuName }} {{$store.display.cpuLength}}</span>

                    <span class="color777 cores">{{$t('psc.coreCount')}}:</span>
                    <span class="color777">{{ $store.cpuCores }}</span>
                    <span class="color777 thread">{{$t('psc.threadCount')}}:</span>
                    <span class="color777">{{ $store.cpuThreads }}</span>

                    <span class="color777" style="margin-left: 20px;">{{$t('psc.hyperthreadingState')}}：</span>
                    <span style="color: #3579D5;" v-if="$store.cpuThreads > $store.cpuCores">{{$t('psc.open')}}</span>
                    <span style="color: #BF4040;" v-else>{{$t('psc.notYetUnlocked')}}</span>
                </div>

                <div class="quickSelect mb-15">
                    <span>{{$t('messages.quickSelect')}}：</span>

                    <p-button @click="methods.quickChooseCpuSet('noThread')">{{$t('psc.nonhyperthreading')}}</p-button>
                    <p-button @click="methods.quickChooseCpuSet('spaced')">{{$t('psc.intervalSelection')}}</p-button>
                    <p-button @click="methods.quickChooseCpuSet('fx')">{{$t('psc.invertSelection')}}</p-button>
                    <p-button @click="methods.quickChooseCpuSet('default')">{{$t('messages.default')}}</p-button>
                    <p-button
                        @click="methods.quickChooseCpuSet('PCore')"
                        v-if="haveECore"
                    >P Core</p-button>
                    <p-button
                        @click="methods.quickChooseCpuSet('ECore')"
                        v-if="haveECore"
                    >E Core</p-button>
                    <template v-for="(item,index) in $store.cpuComplexInfo">
                        <p-button
                            @click="methods.quickChooseCpuSet('CCD',index)"
                            v-if="$store.display.cpuName != '' && !$store.isIntel && item.Cores.length > 0"
                        >{{ccdOrCcx}}{{index}}</p-button>
                    </template>
                </div>

                <intel-mode v-if="$store.display.cpuName != '' && $store.isIntel"/>

                <amd-mode v-if="$store.display.cpuName != '' && !$store.isIntel"/>
            </div>

            <div class="bottom">
                <el-button type="primary" color="#3E4050" @click="methods.closeDialog">{{$t('cancel')}}</el-button>
                <el-button type="primary" color="#336AB5" @click="methods.confirm">{{$t('messages.confirm')}}</el-button>
            </div>
        </section>
    </div>
</template>

<style scoped lang="scss">
.chooseCPUSetDialog {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #000000C7;
  z-index: 2000;
  font-size: 12px;
  color: #ffffff;

  section {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background-color: #2b2c37;
    border-radius: 6px;
    padding: 15px 15px 15px 18px;

    width: 980px;
    height: 500px;

    header {
      display: flex;
      align-items: center;
      margin-bottom: 30px;

      .icon-threads {
        color: #3579d5;
        margin-right: 10px;
        font-size: 20px;
      }

      .icon-Close {
        margin-left: auto;
        color: #999999;
        cursor: pointer;
      }
    }

    .scroll-box {
      height: 363px;
      .quickSelect {
        display: flex;
        align-items: center;
        flex-flow: row wrap;
        gap: 10px;
        :deep(.el-button+.el-button) {
          margin-left: 0;
        }
      }
    }

    .bottom {
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-top: 10px;
    }
  }
}


.flex-items-center {
  display: flex;
  align-items: center;
}
.mb-20 {
  margin-bottom: 20px;
}

.mb-15 {
  margin-bottom: 15px;
}
.color777 {
  color: #777777;
}
.cpu-name {
  margin: 0 10px;
}

.cores {
  margin-left: 40px;
  margin-right: 10px;
}

.thread {
  margin-left: 20px;
  margin-right: 10px;
}
</style>
