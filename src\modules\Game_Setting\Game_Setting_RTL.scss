body[dir=rtl] {
    .setting-content {
        .el-collapse-item__header {
            padding-left: 0;
            padding-right: 20px;
        }

        .flex-row-center {
            .el-button.el-button--primary {
                margin-left: 20px;
                margin-right: 0 !important;
            }
        }

        .el-checkbox__label {
            padding-left: 0;
            padding-right: 8px;
        }

        .el-checkbox {
            margin-right: 0;
        }

        .setting-item .right-box {
            border-left: 0;
            border-right: 2px solid var(--setting-item-border-color);
            padding-left: 0;
            padding-right: 20px;
        }

        .el-collapse-item__arrow {
            margin: 0 auto 0 8px;
        }

        .gxh_title {
            padding-left: 15px;
            padding-right: 0;

            .restoreDefaultBtn {
                margin-left: 0;
                margin-right: auto;
            }
        }
        .setting-item {
            .el-radio{
                margin-right: 0;
                margin-left: 20px;
            }
            .el-radio__input{
                margin-left: 10px;
            }
            .el-color-picker__trigger{
                margin-left: 0px;
                margin-right: 10px;
            }
            .ingame{
                ul{
                    li{
                        margin-left: 0px;
                        margin-right: 10px;
                    }
                }
            }
            text{
                margin-left: 5px;
                margin-right: 0 !important;
            }
            .gxh_title{
                span{
                    margin-left: inherit!important;
                }
            }
        }

        .left-box .dir {
            padding: 0 10px 0 2px;
            .clear-icon {
                right: unset;
                left: 0;
                transform: translateX(calc(-100% - 10px));
            }
        }
    }
}
