<script setup lang="ts">
import {computed, onMounted, ref,reactive, watch} from 'vue'
import {useReboundDetailStore} from "@/modules/Game_ReboundDetail/stores/reboundDetail";
import {storeToRefs} from "pinia";
import {totalSeconds} from "@/modules/Game_ReboundDetail/components/someScripts";
import {useI18n} from "vue-i18n";

const {t} = useI18n()
const noPerformance = ref(true); // 有没有因温度原因性能受限的
const noPerformance_other = ref(true); // 有没有因其他原因性能受限的
const desc_list = reactive([
    'GameRebound.CPUcoretemperature',
    'GameRebound.CPUCriticalTemperature',
    'GameRebound.CPUCircuitTemperature',
    'GameRebound.CPUCircuitCriticalTemperature',
    'GameRebound.CPUtemperatureoverheating',
    'GameRebound.CPUoverheatingtriggered',
    'GameRebound.CPUPowerSupplyOverheating',
    'GameRebound.CPUPowerSupplyLimitation',
    'GameRebound.CPUcoretemperature',
    'GameRebound.CPUPowerSupplyLimitation',
    'GameRebound.CPUCriticalTemperature',
    'GameRebound.CPUtemperatureoverheating',
    'GameRebound.CPUPowerSupplyOverheating',
    'GameRebound.CPUMaximumPowerLimitation',
    'GameRebound.CPUCircuitPowerLimitation',
    'GameRebound.CPUElectricalDesignLimitation',
    'GameRebound.CPULongTermPowerLimitation',
    'GameRebound.CPULongTermPowerinstantaneous',
    'GameRebound.CPUPowerLimitation',
    'GameRebound.CPUPowerWallLimitation',
    'GameRebound.CPUcurrentwalllimit',
])
const temp_performance_limit_list = reactive([
  {"name":"Core Thermal Throttling","value":0,"tags":["core","thermal","throttling"],desc_index:0},
  {"name":"Core Critical Temperature","value":0,"tags":["core","critical","temperature"],desc_index:1},
  {"name":"Package/Ring Thermal Throttling","value":0,"tags":["package/ring","thermal","throttling"],desc_index:2},
  {"name":"Package/Ring Critical Temperature","value":0,"tags":["package/ring","critical","temperature"],desc_index:3},
  {"name":"IA：PROCHOT","value":0,"tags":["ia","prochot"],desc_index:4},
  {"name":"IA：Thermal Event","value":0,"tags":["ia","thermal","event"],desc_index:5},
  {"name":"IA: VR Thermal Alert","value":0,"tags":["ia","vr","thermal","alert"],desc_index:6},
  {"name":"IA: VR TDC","value":0,"tags":["ia","vr","tdc"],desc_index:7},
  {"name":"Thermal Limit","value":0,"tags":["thermal","limit"],desc_index:8},
  {"name":"CPU TDC Limit","value":0,"tags":["cpu","tdc","limit"],desc_index:9},
  {"name":"Thermal Throttling HTC","value":0,"tags":["thermal","throttling","htc"],desc_index:10},
  {"name":"PROCHOT CPU","value":0,"tags":["prochot","cpu"],desc_index:11},
  {"name":"PROCHOT EXT","value":0,"tags":["prochot","ext"],desc_index:12}
])
const temp_performance_limit_map = reactive<any>({})
const limit_list_toshow = computed(() => {
  const _arr: Array<any> = Object.keys(temp_performance_limit_map).map((key: string) => {
    return {name: key, value: temp_performance_limit_map[key].v,desc_index:temp_performance_limit_map[key].desc_index, percent: 0}
  }).filter(o => o.value > 0)
  _arr.sort((a: any, b: any) => b.value - a.value)
  let _sum = 0
  _arr.forEach(({value}) => {
    _sum += value
  })
  for (let i = 0; i < _arr.length; i++) {
    const v = _arr[i].value
    _arr[i]['percent'] = Math.round(v / _sum * 100)
    _arr[i]['percent'] === 0 && (_arr[i]['percent'] = 1)// 防止出现0%的情况
  }
  return _arr;
})
const isShowMainboradAbout = computed(()=>{
  const findIndex = limit_list_toshow.value.findIndex(item=>{
    return item.desc_index === 6  || item.desc_index === 7  || item.desc_index === 9 || item.desc_index === 12
  })
  return findIndex !== -1
})
const other_performance_limit_map = reactive<any>({})
const limit_list_toshow2 = computed(() => {
  const _arr: Array<any> = Object.keys(other_performance_limit_map).map((key: string) => {
    return {name: key, value: other_performance_limit_map[key].v,desc_index:other_performance_limit_map[key].desc_index, percent: 0}
  }).filter(o => o.value > 0)
  _arr.sort((a: any, b: any) => b.value - a.value)
  let _sum = 0
  _arr.forEach(({value}) => {
    _sum += value
  })
  for (let i = 0; i < _arr.length; i++) {
    const v = _arr[i].value
    _arr[i]['percent'] = Math.round(v / _sum * 100)
  }
  return _arr;
})
const chooseTime = computed(()=>{
  const s = totalSeconds(endTime.value) -  totalSeconds(startTime.value)
  return FormatSeconds(s,true)
})
function FormatSeconds(value: any, second: any) {
    let theTime: number = parseInt(value);// 秒
    let theTime1: number = 0;// 分
    let theTime2: number = 0;// 小时
    if (theTime > 60) {
        theTime1 = parseInt(String(theTime / 60));
        theTime = parseInt(String(theTime % 60));
        if (theTime1 > 60) {
            theTime2 = parseInt(String(theTime1 / 60));
            theTime1 = parseInt(String(theTime1 % 60));
        }
    }
    let result = "";
    if (second) {
        result = "" + parseInt(String(theTime)) + t('shutdownTimer.sec');
    }
    if (theTime1 > 0) {
        result = "" + parseInt(String(theTime1)) + t('screenshotpage.minutes') + result;
    }
    if (theTime2 > 0) {
        result = "" + parseInt(String(theTime2)) + t('shutdownTimer.hours') + result;
    }
    return result;
}
const $store = useReboundDetailStore();
const {startTime, endTime, endTimeOriginal, gameTime} = storeToRefs($store)

const all_time_limit_percent = computed(()=>{
  const total = $store.curDisplayDataCount
  const limit_count1 = Array.from($store.CPA.limit_data_temp).filter(o=>o >= startTime.value && o <= endTime.value).length
  const limit_count2 = $store.CPA.limit_data_other.size
  const r1 = Math.round(limit_count1 / total * 100)
  const r2 = Math.round(limit_count2 / total * 100)
  return [(r1=== 0?0.1:r1),(r2 === 0?0.1:r2)]
})
function checkPerformance() {
  let startIndex = 0
  let endIndex = 0
  if ($store.powerData.cpuclock && $store.powerData.cpuclock.performance) {
    const arr = JSON.parse(JSON.stringify($store.powerData.cpuclock.performance))
    if (startTime.value.h != 0 || startTime.value.m != 0 || startTime.value.s != 0) {
      startIndex = Math.ceil(totalSeconds(startTime.value) / gameTime.value * arr.length)
    }
    if (startTime.value.h == 0 && startTime.value.m == 0 && startTime.value.s == 0) {
      startIndex = 0
    }
    if (totalSeconds(endTime.value) < totalSeconds(endTimeOriginal.value)) {
      endIndex = Math.ceil(totalSeconds(endTime.value) / gameTime.value * arr.length)
    } else {
      endIndex = arr.length
    }
    const errsSet = new Set($store.powerData.errs)
    let filterFn = (value: number, index: number)=>{
      if (errsSet.has(index)) {
        return false;
      }
      if (startIndex > 0 && index < startIndex) {
        return false
      }
      if (index > endIndex) {
        return false
      }
      return true;
    }
    const curArr = arr.filter(filterFn)
    for (let i = 0; i < temp_performance_limit_list.length; i++) {
      temp_performance_limit_list[i].value = 0
    }
    $store.CPA.limit_data_temp.clear()
    $store.CPA.limit_data_other.clear()
    noPerformance.value = true;
    noPerformance_other.value = true;
    for (let i = 0; i < curArr.length; i++) {
      if (curArr[i] !== '') {
        try {
          const performances = JSON.parse(curArr[i])
          performances.forEach((item: any) => {
            Object.keys(item).forEach((reason: string) => {
              checkPerformanceReason(reason.toLowerCase(), i + startIndex,reason)
            })
          })
        } catch (e) {
          console.log(e)
        }
      }
    }
  }
}

// 检查什么原因导致的性能受限
function checkPerformanceReason(reason: string, index: number,reason_origin:string) {
  for (let i = 0; i < temp_performance_limit_list.length; i++) {
    const item: any = temp_performance_limit_list[i]
    const _arr = item.tags
    let _bool = true;
    _arr.forEach((v: string) => {
      if (!reason.includes(v)) {
        _bool = false
      }
    })
    if (_bool) {
      noPerformance.value = false
      temp_performance_limit_list[i].value++
      if (!temp_performance_limit_map.hasOwnProperty(reason_origin)) {
        temp_performance_limit_map[reason_origin] = {
          v:0,
          desc_index: item.desc_index
        }
      }
      temp_performance_limit_map[reason_origin].v++
      $store.CPA.limit_data_temp.add(index)
      break;
    }
  }
}

onMounted(() => {
  checkPerformance()
  const bc = new BroadcastChannel('fps_limit')
  bc.onmessage = ()=>{
    checkPerformance()
  }
})

watch([startTime, endTime], () => {
  checkPerformance()
},{deep:true})
</script>

<template>
  <div class="container">
    <p>{{ $t('GameRebound.CPUanalysis') }}</p>
    <div class="content scroll">
      <section style="margin-bottom: 10px;">
        <p class="temp-title">{{ $t('GameRebound.tempSensorEvent') }}</p>
        <span v-if="noPerformance">{{ $t('GameRebound.NoTemperatureLimitation2') }}</span>
        <ul v-if="!noPerformance">
          <li>
            <span>{{ $t('GameRebound.performanceis') }}&nbsp{{ chooseTime }}&nbsp{{ $t('GameRebound.Inside') }}&nbsp<span class="percent-num">{{all_time_limit_percent[0]}}%</span>
            {{ $t('GameRebound.TheStatisticsTimeOf') }}</span>
            <span class="why">{{ limit_list_toshow[0]['name'] }}</span>
            <span>{{ $t('GameRebound.limited') }} <span class="percent-num">{{ limit_list_toshow[0]['percent'] }}%</span></span>
          </li>
          <li>
            <p>{{ $t('GameRebound.SpecificReasons') }}</p>
            <div class="reason_list">
              <el-tooltip v-for="item in limit_list_toshow" :content="$t(desc_list[item.desc_index])" effect="light" placement="top">
                <div class="list-item">
                  <span class="reason-desc">{{ item.name }}</span>
                  <span class="reason-percent">{{ item.percent }}%</span>
                </div>
              </el-tooltip>
            </div>
          </li>
          <li>
            <p>{{ $t('GameRebound.OptimizationSuggestion') }}</p>

            <span>{{ $t('GameRebound.CPUtemperature') }}  <span v-show="isShowMainboradAbout">{{ $t('GameRebound.CPUoverheat') }} </span></span>
          </li>
        </ul>
      </section>
    </div>
  </div>
</template>

<style scoped lang="scss">
.container {
  width: 1220px;
  height: calc(100vh - 496px);

  p.title {
    font-weight: 400;
    font-size: 12px;
    color: #ffffff;
    margin-bottom: 14px;
  }

  .content {
    margin-top: 15px;
    height: calc(100vh - 527px);
    overflow: auto;

    section {
      width: 1200px;
      background: #22232E;
      padding: 25px 20px;

      ul {
        display: flex;
        flex-flow: column nowrap;
        gap: 15px;

        li {
          color: #ffffff;
          display: flex;
          flex-flow: row nowrap;
          align-items: center;
          flex-wrap: wrap;
          font-size: 12px;
          gap: 10px;

          p {
            color: #777777;
          }

          .percent-num {
            font-size: 14px;
            font-weight: bold;
          }

          .why {
            color: #BF4040;
            display: inline-block;
            font-weight: bold;
          }

          .reason_list {
            display: flex;
            flex-flow: row wrap;
            gap: 10px;

            .list-item {
              height: 20px;
              line-height: 20px;
              background: #BF4040;
              border-radius: 2px;
              padding: 0 8px;

              .reason-desc {
                margin-right: 10px;
              }
            }
          }
        }
      }
    }

    .temp-title, .other-title {
      font-weight: 400;
      font-size: 12px;
      color: #BF4040;
      margin-bottom: 10px;
    }

    p.other-title {
      color: #D27F2E;
    }
  }
}
</style>
