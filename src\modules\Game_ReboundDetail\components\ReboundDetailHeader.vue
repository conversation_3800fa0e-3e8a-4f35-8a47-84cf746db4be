<script setup lang="ts">
import {onMounted, reactive, ref, watch} from 'vue';
import {gameppBaseSetting} from "@/modules/Game_Home/stores";
import * as XLSX from "xlsx";
import {FormatTimestamp} from "@/uitls/GameppTools";
import RightTopIcons from "@/components/mainCom/RightTopIcons.vue";
import {useReboundDetailStore} from "@/modules/Game_ReboundDetail/stores/reboundDetail";
import {useI18n} from "vue-i18n";

// @ts-ignore
const gamepp = window.gamepp
const {t} = useI18n()
let version = ref('')
let setting_store = gameppBaseSetting()
let closeWindowCount = ref(15)
let showCloseWindowCount = ref(false)
let noMoreShow = ref(false)
let isFullscreen = ref(false)
const show_dev_menu = ref(false)
const $store = useReboundDetailStore()
const props = defineProps({
  processName: {
    type: String,
    default: ''
  },
  databaseTable: {
    type: String,
    default: ''
  },
  activeTab: {
    type: Number,
    default: ''
  }
})

onMounted(() => {
  addLis()
  getVersion()
  getProcessNoMoreShow()
  gamepp.display.onDisplayMetricsChanged.addEventListener( async (scaleFactor:number)=>{
    const zoomWithSystem = gamepp.setting.getInteger.sync(313)
    if (zoomWithSystem === 1) {
      isFullscreen.value = false
    }
  })
})

watch(() => setting_store.state.performanceStatisticsAutoClose, () => {
  isNeedCloseWindow()
}, {
  immediate: true
})

function addLis() {
    document.addEventListener('keydown', (e) => {
        // 按下ctrl + s
        if (e.ctrlKey && e.keyCode === 83) {
            show_dev_menu.value = !show_dev_menu.value
        }
    })
}
const gpu_memory_size_type = (data: any) => {
    const VideoMemory = (data.VideoMemory).split(' ')
    let n = 1024
    if (data.VideoMemory.includes('MBytes')) {
        n = 1024
    } else if (data.VideoMemory.includes('KBytes')) {
        n = 1024*1024
    } else if (data.VideoMemory.includes('GBytes')) {
        n = 1
    }
    return Math.ceil(((VideoMemory[0] / n))) + 'GB'
}
const desc_list = [
    '核心温度达到Tj,Max而受到限制',
    'CPU达到临界温度',
    'CPU封装/环形总线达到Tj,Max而受到限制',
    'CPU封装/环形总线达到临界温度',
    'CPU温度过热，触发后CPU会主动降频以降低温度，防止硬件故障',
    '因过热触发散热机制，CPU会相应调整电压和频率以降低功耗和温度',
    'CPU因主板供电严重过热而所受到限制',
    'CPU因主板供电过热而受到限制',
    '核心温度达到Tj,Max而受到限制',
    'CPU因主板供电过热而受到限制',
    'CPU达到临界温度',
    'CPU温度过热，触发后CPU会主动降频以降低温度，防止硬件故障',
    'CPU因主板供电严重过热而所受到限制',
    '核心受到最大功耗限制',
    'CPU封装/环形总线达到功耗限制',
    '触发电气设计限制（含ICCmax电流墙、PL4峰值功耗墙、SVID电压限制等)',
    'CPU的长时功耗达到限制',
    'CPU的瞬时功耗达到限制',
    'CPU睿频衰减机制，一般由BIOS或特定软件限制',
    'CPU功耗墙限制',
    'CPU电流墙限制',
]
const performance_limit_list = ([
    // {"name":"Core Thermal Throttling","value":0,"tags":["core","thermal","throttling"],desc_index:0},
    {"name":"Core Critical Temperature","value":0,"tags":["core","critical","temperature"],desc_index:1},
    {"name":"Package/Ring Thermal Throttling","value":0,"tags":["package/ring","thermal","throttling"],desc_index:2},
    {"name":"Package/Ring Critical Temperature","value":0,"tags":["package/ring","critical","temperature"],desc_index:3},
    {"name":"IA：PROCHOT","value":0,"tags":["ia","prochot"],desc_index:4},
    {"name":"IA：Thermal Event","value":0,"tags":["ia","thermal","event"],desc_index:5},
    {"name":"IA: VR Thermal Alert","value":0,"tags":["ia","vr","thermal","alert"],desc_index:6},
    {"name":"IA: VR TDC","value":0,"tags":["ia","vr","tdc"],desc_index:7},
    {"name":"Thermal Limit","value":0,"tags":["thermal","limit"],desc_index:8},
    {"name":"CPU TDC Limit","value":0,"tags":["cpu","tdc","limit"],desc_index:9},
    {"name":"Thermal Throttling HTC","value":0,"tags":["thermal","throttling","htc"],desc_index:10},
    {"name":"PROCHOT CPU","value":0,"tags":["prochot","cpu"],desc_index:11},
    {"name":"PROCHOT EXT","value":0,"tags":["prochot","ext"],desc_index:12},
    {"name":"Core Power Limit Exceeded","value":0,"tags":["core","power","limit","exceeded"],desc_index:13},
    {"name":"Package/Ring Power Limit Exceeded","value":0,"tags":["package/ring","power","limit","exceeded"],desc_index:14},
    {"name":"IA: Electrical Design Point/Other (ICCmax,PL4,SVID,DDR RAPL)","value":0,"name2":"IA: Electrical Design Point/Other","tags":["ia","electrical","design","point/other"],desc_index:15},
    {"name":"IA: Package-Level RAPL/PBM PL1","value":0,"tags":["ia","package-level","rapl/pbm","pl1"],desc_index:16},
    {"name":"IA: Package-Level RAPL/PBM PL2,PL3","value":0,"tags":["ia","package-level","rapl/pbm","pl2,pl3"],desc_index:17},
    {"name":"IA: Turbo Attenuation (MCT)","value":0,"tags":["ia","turbo","attenuation","(mct)"],desc_index:18},
    {"name":"CPU PPT Limit","value":0,"tags":["cpu","ppt","limit"],desc_index:19},
    {"name":"CPU EDC Limit","value":0,"tags":["cpu","edc","limit"],desc_index:20}
])
let peCoreArr:any[] = [0]; // 大核，小核，小小核
const LargeAndSmallCore = (ComplexInfo:any)=>{
    let largeCores: Array<any> = [0,0];// 大核
    let smallCores: Array<any> = [0,0];// 小核
    let littleCores: Array<any> = [0,0];// 小小核
    ComplexInfo.Cores.forEach((core:any, innerIndex:any) => {
        if (core.EfficiencyClass === peCoreArr[0]) {
            largeCores[0]++;
            largeCores[1] += core.LogicalCores.length;
        } else if (peCoreArr.length >=2 && core.EfficiencyClass === peCoreArr[1]) {
          smallCores[0]++;
          smallCores[1] += core.LogicalCores.length;
        } else if (peCoreArr.length >=3 && core.EfficiencyClass === peCoreArr[2]) {
          littleCores[0]++;
          littleCores[1] += core.LogicalCores.length;
        }
    })
    return {
        largeCores,smallCores,littleCores
    }
}
function deepClone(params:any) {
  return JSON.parse(JSON.stringify(params))
}
async function saveToDesktop() {
    if (($store.recentGameInfo.endtime - $store.recentGameInfo.starttime) < 60*5) return

    const str = $store.getSendStr();
    // 复制到剪贴板
    try {
        await navigator.clipboard.writeText(str);
    }catch (e) {
        console.log(e)
    }
    // 存到桌面
    const AppDataDir = await gamepp.getAppDataDir.promise()
    const save_path = AppDataDir.replace('AppData', 'Desktop').split('\\').slice(0, 4).join('\\') + '\\' + '游戏数据';
    try {
        await gamepp.saveFileToDisk.promise(save_path + '.txt',str)
    } catch (err){
        console.log(err)
    }

    show_dev_menu.value = false
}

function getDDRVersion(memoryString:string) {
    // 正则表达式匹配 DDR 后面跟着数字（1-9）
    const regex = /DDR(\d+)/i;
    const match = memoryString.match(regex);

    // 如果匹配成功，返回数字部分，否则返回 null
    return match ? 'DDR'+parseInt(match[1], 10) : '';
}

function secondsToMinutes(seconds:number) {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}分钟${secs < 10 ? '0' : ''}${secs}秒`;
}

function calcMin(arr: number[]) {
    let sum = arr[0]
    for (let i = 0; i < arr.length; i++) {
        sum = Math.min(sum, arr[i])
    }
    return sum
}

function calcAvg(arr: number[]) {
    let sum = 0
    for (let i = 0; i < arr.length; i++) {
        sum += arr[i]
    }
    return Math.floor(sum / arr.length)
}

function findValueByKeyAndType (data:Object, key:string, keySubstring:any = false, type) {
    let ToFixed = 0
    if (type.includes('voltage')) {
        ToFixed = 3
    }
    let key1:any = false, key2:any = false;
    if (key === 'Mainboard') {
        const sift = JSON.parse(JSON.stringify(data)) //深拷贝
        const keywords = ['CPU', 'GPU', 'Memory', 'S.M.A.R.T', 'System:', 'Windows Hardware', 'Drive', 'Network', 'DIMM']
        const filteredData = {}
        for (const key1 in sift) {
            let shouldKeep = true
            for (const keyword of keywords) {
                if (data[key1] === null || key1.toLowerCase().includes(keyword.toLowerCase())) {
                    shouldKeep = false
                    break
                }
            }
            if (shouldKeep) {
                filteredData[key1] = sift[key1]
            }
        }
        data = filteredData
        key = Object.keys(filteredData)[0]
        key1 = Object.keys(filteredData)[1]
        key2 = Object.keys(filteredData)[2]
    } else if (key === 'Network') {
        let totalDLRate = 0, totalUPRate = 0;
        for (const key1 in data) {
            if (key1.startsWith("Network")) {
                const networkData = data[key1] || [];
                networkData.forEach(item => {
                    if (item["Current DL rate"]) {
                        totalDLRate += parseFloat(item["Current DL rate"].value);
                    }
                    if (item["Current UP rate"]) {
                        totalUPRate += parseFloat(item["Current UP rate"].value);
                    }
                });
            }
        }
        return [Number(totalDLRate.toFixed(0)), Number(totalUPRate.toFixed(0))];
    }
    //除开主板与NetWork都在此处进行查找
    for (const [parentKey, arr] of Object.entries(data)) {
        // console.warn('ArrArrArr',arr);
        if (data[parentKey] && (parentKey.includes(key) || (key1 && parentKey.includes(key1))|| (key2 && parentKey.includes(key2)))) {
            for (const obj of arr) {
                const [itemKey, itemValue] = Object.entries(obj)[0] as [any,any]
                // const [itemValue,itemKey] = Object.entries(obj)[0]
                if (keySubstring) {
                    if (keySubstring.includes('|')) {
                        const substrings = keySubstring.split('|');
                        for (const substring of substrings) {
                            if (itemKey.includes(substring) && type === (itemValue.type)) {
                                return [itemKey, Number(parseFloat(itemValue.value).toFixed(ToFixed))];
                            }
                        }
                    } else {
                        if (itemKey.includes(keySubstring) && type.includes(itemValue.type)) {
                            return [itemKey, Number(parseFloat((itemValue.value)).toFixed(ToFixed))]
                        }
                    }
                } else {
                    if (type.includes(itemValue.type)) {
                        return [itemKey, Number(parseFloat((itemValue.value)).toFixed(ToFixed))]
                    }
                }
            }
        }
    }
    return ['', '']
}

const ExportPerformanceData = async (index: number) => {
  let AppDataDir = await gamepp.getAppDataDir.promise();
  let DatabaseId = await gamepp.database.open.promise(AppDataDir + '\\common\\Data\\GamePP5.dll');
  let timeTable: any = Number(props.databaseTable);

  let queryField_cpu_temperature_core = await gamepp.database.queryField.promise(DatabaseId, timeTable, 'cpu_temperature_core');
  let queryField = '';
  if (queryField_cpu_temperature_core) {
    queryField = 'performance_gpu,amd_gpu_thermalmemory,amd_gpu_thermalhotspot,amd_cpu_thermal,performance,disk_load,disk_temp,fps01,fps1,pointmark,id,fps,frametime,cpuclock,cpuload,cpupower,cputemperature,cpu_temperature_core,cpuvoltage,gpuclock,gpuload,gpumemoryload,gpupower,gputemperature,memory,memorytemperature';
  } else {
    queryField = 'performance_gpu,amd_gpu_thermalmemory,amd_gpu_thermalhotspot,amd_cpu_thermal,performance,disk_load,disk_temp,fps01,fps1,pointmark,id,fps,frametime,cpuclock,cpuload,cpupower,cputemperature,cpuvoltage,gpuclock,gpuload,gpumemoryload,gpupower,gputemperature,memory,memorytemperature';
  }

  const DetailedData = await gamepp.database.query.promise(DatabaseId, "'" + timeTable + "'", queryField);
  console.warn('DetailedData:', DetailedData);

  let allSheetData = []
  for (let i = 0; i < DetailedData.length; i++) {
    // console.log(DetailedData[i])
    let tempObjBase: any = {}, tempObjCPU: any = {}, tempObjGPU: any = {}, tempObjMemory: any = {};
    tempObjBase['FPS'] = (DetailedData[i].fps);
    tempObjBase['FPS1'] = (DetailedData[i].fps1);
    tempObjBase['FPS01'] = (DetailedData[i].fps01);

    tempObjBase['performance'] = (DetailedData[i].performance);
    tempObjBase['pointmark'] = (DetailedData[i].pointmark);

    tempObjBase['disk_load'] = (DetailedData[i].disk_load);
    tempObjBase['disk_temp'] = (DetailedData[i].disk_temp);
    tempObjBase['performance_gpu'] = (DetailedData[i].performance_gpu);
    tempObjBase['amd_gpu_thermalmemory'] = (DetailedData[i].amd_gpu_thermalmemory);
    tempObjBase['amd_gpu_thermalhotspot'] = (DetailedData[i].amd_gpu_thermalhotspot);
    tempObjBase['amd_cpu_thermal'] = (DetailedData[i].amd_cpu_thermal);


    tempObjBase['FrameTime'] = (DetailedData[i].frametime);
    //---------------------------------CPU-------------------------
    tempObjCPU["CPU Temp [℃]"] = (DetailedData[i].cputemperature);
    tempObjCPU['CPU Power [W]'] = (DetailedData[i].cpupower);
    tempObjCPU['CPU Voltage  [V]'] = (DetailedData[i].cpuvoltage);

    //CPU每个核心频率--cpuclock
    let cpuclockArr = (DetailedData[i].cpuclock).split('|');

    //CPU每个核心占用--cpuload
    let cpuloadArr = (DetailedData[i].cpuload).split('|');

    //CPU每个核心占用--cpu_temperature_core
    let cpu_temperature_coreArr = [];
    if (queryField_cpu_temperature_core) {
      cpu_temperature_coreArr = (DetailedData[i].cpu_temperature_core).split('|');
    }

    for (let j = 0; j < (cpuclockArr.length) - 1; j++) {
      tempObjCPU['CPU Core ' + (j + 1) + ' Load [%]'] = cpuloadArr[j];
      if (queryField_cpu_temperature_core) {
        tempObjCPU['CPU Core ' + (j + 1) + ' Temp [℃]'] = cpu_temperature_coreArr[j];
      }
      tempObjCPU['CPU Core ' + (j + 1) + ' Clock [MHz]'] = cpuclockArr[j];
    }
    //---------------------------------GPU-------------------------
    tempObjGPU['GPU Temp [℃]'] = (DetailedData[i].gputemperature).replace('|', '');
    tempObjGPU['GPU Power [W]'] = (DetailedData[i].gpupower).replace('|', '');
    tempObjGPU['GPU Core Clock [MHz]'] = (DetailedData[i].gpuclock).replace('|', '');
    tempObjGPU['GPU Core Load [%]'] = (DetailedData[i].gpuload).replace('|', '');
    tempObjGPU['GPU Memory Load [%]'] = (DetailedData[i].gpumemoryload).replace('|', '');
    //---------------------------------Memory-------------------------
    tempObjMemory['Memory Temp [℃]'] = (DetailedData[i].memorytemperature);
    tempObjMemory['Memory Load [%]'] = (DetailedData[i].memory);

    //-------------------------------数据推送表------------------------
    allSheetData.push({...tempObjBase, ...tempObjCPU, ...tempObjGPU, ...tempObjMemory})
  }
  /* 创建worksheet */
  let sheetAll = XLSX.utils.json_to_sheet(allSheetData);
  /* 新建空workbook，然后加入worksheet */
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, sheetAll, "HWINFO");
  // /* 生成xlsx文件 */
  let fildName = ((FormatTimestamp(timeTable))) + ' ' + props.processName.replace('.exe', '') + '.csv'
  console.log(fildName)
  XLSX.writeFile(workbook, fildName);//csv xlsx  csv不支持多sheet
}

const screenshot = () => {
  window.isScreenshot = true;
  let str = 'GamePP'
  if (props.processName !== '???') {
    str += props.processName.replace('.exe','exe')
  }
  gamepp.dialog.showSaveDialog.async((value: any) => showSaveDialogCallback(value[0]), {
    title: "GamePP_", defaultPath: str + '.jpg', filters: [{name: 'Jpeg Picture', extensions: ['*.jpg']}]
  });
}
let bounds = {width: 1292, height: 762}

function showSaveDialogCallback(Path: any) {
  console.log(Path)
  if (!Path['canceled']) {
    const regex = /^[a-zA-Z]:(([a-zA-Z]*)||([a-zA-Z]*\\))*/;
    const array = regex.exec(Path['filePath']);
    if (array != null) {
      bounds = gamepp.webapp.windows.getBounds.sync()
      if (props.activeTab === 1) {
        gamepp.webapp.windows.resize.sync('rebound_details_v2', 1292, 1550)
      }else if (props.activeTab === 2) {
        gamepp.webapp.windows.resize.sync('rebound_details_v2', 1292, 870)
      }
      gamepp.webapp.windows.capturePage.async((value: any) => CapturePageCallback(value, Path['filePath']), '', Path['filePath'], 'jpg');
    } else {
      ElMessage.error(t('hardwareInfo.error'));
      window.isScreenshot = false;
    }
  }else{
    window.isScreenshot = false;
  }
}

//保存截图回调
function CapturePageCallback(value: any, filePath: any) {
  window.isScreenshot = false;
  if (props.activeTab === 1 || props.activeTab === 2) {
    gamepp.webapp.windows.resize.sync('rebound_details_v2', bounds.width, bounds.height)
  }
  if (value) {
    try {
      let image = gamepp.nativeImage.createFromPath.sync(filePath);
      gamepp.clipboard.writeImage.sync(image);
    } catch (e) {
      console.log('gamepp.nativeImage.createFromPath', e)
    }
    ElMessage.success(t('hardwareInfo.screenshotSuccess'));
  }
}

async function getVersion() {
  try {
    const versionObj:any = await gamepp.package.getversion.promise("GameRebound")
    if (Object.prototype.toString.call(versionObj) === '[object Object]' && 'version' in versionObj) {
      version.value = versionObj.version;
    }else{
      version.value = gamepp.getPlatformVersion.sync()
    }

  } catch (e) {
    console.log(e)
  }
}

function isNeedCloseWindow() {
  const rebound_details_v2_open_by = window.localStorage.getItem('rebound_details_v2_open_by')
  if (rebound_details_v2_open_by && rebound_details_v2_open_by === 'bg') {
    showCloseWindowCount.value = true
    console.log(setting_store.state.performanceStatisticsAutoClose)
    if (setting_store.state.performanceStatisticsAutoClose) { // 需不需要自动关闭
      if (setting_store.state.performanceStatisticsAutoCloseTime) {
        closeWindowCount.value = setting_store.state.performanceStatisticsAutoCloseTime
      } else {
        closeWindowCount.value = 15
      }
      setInterval(() => {
        closeWindowCount.value -= 1
        if (closeWindowCount.value <= 0) {
          closeWindow()
        }
      }, 1000)
    } else {
      showCloseWindowCount.value = false
    }
  } else {
    showCloseWindowCount.value = false
  }
}

function closeWindow() {
  gamepp.webapp.windows.close.promise('rebound_details_v2')
}

function minimizeWindow() {
  gamepp.webapp.windows.minimize.promise('rebound_details_v2')
}
function maximizeWindow () {
  document.documentElement.style.setProperty('--rebound-w', '100vw');
  document.documentElement.style.setProperty('--rebound-h', '100vh');
  document.documentElement.style.setProperty('--shadow-size', '0px');
  isFullscreen.value = true;
  gamepp.webapp.windows.maximize.promise('rebound_details_v2')
}
function unMaximizeWindow () {
  document.documentElement.style.setProperty('--rebound-w', 'calc(100vw - 12px)');
  document.documentElement.style.setProperty('--rebound-h', 'calc(100vh - 12px)');
  document.documentElement.style.setProperty('--shadow-size', '6px');
  isFullscreen.value = false;
  gamepp.webapp.windows.unmaximize.promise('rebound_details_v2')
}

let gpnms = 0

function getProcessNoMoreShow() {
  gpnms++
  if (gpnms >= 20) return
  if (props.processName !== '???') {
    let ProcessArr: any = localStorage.getItem('ShowReboundProcess');
    if (ProcessArr) {
      ProcessArr = JSON.parse(ProcessArr);
      for (let i = 0; i < ProcessArr.length; i++) {
        if (ProcessArr[i]['name'] === props.processName) {
          noMoreShow.value = ProcessArr[i]['value'] === 1
          break;
        }
      }
    }
  } else {
    setTimeout(getProcessNoMoreShow, 100)
  }
}

function handleNoMoreShowChange() {
  const IsChecked = noMoreShow.value
  const process: string = props.processName as string
  //改LocalStorage
  let Process = JSON.parse(localStorage.getItem('ShowReboundProcess')!);
  let Object = {
    name: '???',
    value: 0
  }
  Object['name'] = process;
  Object['value'] = (IsChecked ? 1 : 0);
  if (Process === null) {
    Process = [];
    Process.push(Object);
  } else {
    if (JSON.stringify(Process).indexOf(process) !== -1) {
      for (let i = 0; i < Process.length; i++) {
        if (Process[i]['name'] === process) {
          Process[i].value = (IsChecked ? 1 : 0);
        }
      }
    } else {
      Process.push(Object);
    }
  }
  const JsonStr = JSON.stringify(Process);
  window.localStorage.setItem('ShowReboundProcess', JsonStr);
}

</script>
<template>
  <div class="header">
    <img src="../../../assets/img/Public/logo.png" alt="">
    <span class="title" style="margin-left: 10px;">{{ $t('home.performanceStatistics') }}</span>
    <span class="Version">V{{ version }}</span>

    <div class="drag-bar"></div>
    <span v-if="showCloseWindowCount">{{ $t('GameRebound.WindowWillBe') }}</span>
    <span v-if="showCloseWindowCount" class="window-close-count"> {{ closeWindowCount }}s </span>
    <span v-if="showCloseWindowCount">{{ $t('GameRebound.After') }}</span>

    <div class="no-more-show">
      <el-checkbox id="no-more-show" v-model="noMoreShow" @change="handleNoMoreShowChange">
        <span>{{ $t('GameRebound.NoLongerPopUpThisGame') }}</span>
      </el-checkbox>
    </div>

    <el-dropdown trigger="click" popper-class="setting-dropdown">
      <div class="item iconfont-box">
        <span class="iconfont icon-parameter"></span>
      </div>
      <template #dropdown>
        <div>
            <el-dropdown-menu :append-to-body="false">
                <el-dropdown-item @click="ExportPerformanceData">
                    <span class="iconfont icon-detail"></span>
                    <span>{{ $t('messages.export') }}</span>
                </el-dropdown-item>
                <el-dropdown-item @click="screenshot">
                    <span class="iconfont icon-Screenshot"></span>
                    <span>{{ $t('messages.screenshot') }}</span>
                </el-dropdown-item>
            </el-dropdown-menu>
        </div>
      </template>
    </el-dropdown>
    <RightTopIcons
        close-icon
        minimize-icon
        maximize-icon
        :item-h="30"
        hover-color="#22232e"
        @close="closeWindow"
        @minimize="minimizeWindow"
        @maximize="maximizeWindow"
        @unmaximize="unMaximizeWindow"
        :is-full-screen="isFullscreen"
    />

      <teleport to="body">
          <div class="dev-menu" v-show="show_dev_menu" @click="saveToDesktop">
              <span>copy to desktop</span>
          </div>
      </teleport>
    <!--<div class="iconfont-box" @click="minimizeWindow"><span class="iconfont icon-minimize" ></span></div>-->
    <!--<div class="iconfont-box" @click="maximizeWindow" v-show="!isFullscreen"><span class="iconfont icon-Maximize" ></span></div>-->
    <!--<div class="iconfont-box" @click="unMaximizeWindow" v-show="isFullscreen"><span class="iconfont icon-normalize"></span></div>-->
    <!--<div class="iconfont-box" @click="closeWindow"><span class="iconfont icon-Close"></span></div>-->
  </div>
</template>

<style lang="scss" scoped>
.dev-menu {
    position: fixed;
    left: 20px;
    top: 20px;
    z-index: 99999;
    width: 140px;
    height: 32px;
    background-color: #333;
    color: #fff;
    text-align: center;
    line-height: 32px;
    cursor: pointer;
}
.header {
  width: 100%;
  height: 30px;
  position: relative;
  background: #2D2E39;
  display: flex;
  align-items: center;
  flex-flow: row nowrap;
  color: #777777;
  font-size: 12px;
  overflow: hidden;
  padding-left: 12px;
  flex-shrink: 0;
  -webkit-app-region: drag;

  :deep(.el-dropdown) {
    -webkit-app-region: no-drag;
  }

  .Version {
    color: #ffffff;
    margin-right: auto;
    -webkit-app-region: no-drag;
    cursor: pointer;
    margin-left: 10px;
  }

  .window-close-count {
    color: #ffffff;
    font-weight: bold;
  }

  .no-more-show {
    display: flex;
    align-items: center;
    flex-flow: row nowrap;
    color: #ffffff;
    margin-left: 10px;
    margin-right: 20px;
    -webkit-app-region: no-drag;

    input[type=checkbox] {
      cursor: pointer;
      margin-right: 5px;
    }
  }

  .iconfont-box {
    width: 40px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    -webkit-app-region: no-drag;
    cursor: pointer;

    &:hover {
      background-color: #22232e;
      .iconfont {
        color: #ffffff;
      }
    }
  }
  .iconfont {
    color: #777;
    font-size: 14px;
    text-align: center;
  }
}
</style>
<style lang="scss">
.setting-dropdown {
  //width: 120px;
  box-shadow: #343647 !important;
  border-radius: 4px;

  .el-dropdown-menu {
    background: #343647;
    color: #3579D5;
  }

  .el-dropdown-menu__item {
    color: #3579D5;
    gap: 10px;
  }

  &.el-dropdown__popper.el-popper {
    background: #343647;
    border: 0;
  }

  .el-dropdown-menu__item:not(.is-disabled):focus {
    background: #3E4050;
    color: #FFFFFF;
  }

  .el-icon {
    margin-right: 10px;
  }
}
</style>
