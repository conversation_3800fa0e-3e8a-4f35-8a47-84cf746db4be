<template>
    <div class="monitor-set">
        <section class="left-section">
            <p class="title">{{ $t('DesktopMonitoring.SomeSensors') }}</p>
            <div class="screen_content">
                <div class="list-title">
                    <div class="titledes"  v-for="title in titles" :key="title.text" :class="title.class" >
                        {{ $t(title.text) }}
                    </div>
                </div>
                <div class="add-monitor hover" @click="showSensor">
                    <el-icon class="el-icon-plus custom-icon"><Plus /></el-icon>
                    <p class="add-monitor-text" >{{ $t('DesktopMonitoring.AddComponent') }}</p>
                </div>

                <ul class="Sensor_data_list scroll"  ref="scrollContainer" >
                    <li v-for="(sensor, index) in sensors" :key="sensor.id"
                        @mouseenter="handleMouseEnter(sensor)"
                        @mouseleave="handleMouseLeave(sensor)"
                        @click="handleSelect(index,sensor)"
                         @mousedown="startDragli($event, index)"
                        :class="{ 'highlighted': hoveredId === sensor.id ,
                        'dragging': isDragging && dragStartIndex === index,
                        'selected': selected === index
                        }"
                        :style="{ zIndex: sensor.style.zIndex }"
                    >
                        <div class="type wide">{{ currentLanguage === 'CN' ? sensor.type : sensor.type2 }}</div>
                        <div class="remark wide">{{ sensor.remark }}</div>
                        <div class="sensor wide Extrawidth">{{ sensor.sensor }}</div>
                        <!-- <div class="page wide">{{ sensor.page }}</div> -->
                        <div class="operation wide" v-show="sensorGroups[sensor.id] || !sensor.showDetails">
                            <el-tooltip class="item" effect="dark" :content="$t('InGameMonitor.show')" placement="top" :delay="0" :duration="0"  :hide-after="0" :enterable="false" popper-class="custom_tooltip">
                                <el-icon class="el-icon-view custom-icon" @click.stop="changeView(index)" v-show="sensor.showDetails && sensorGroups[sensor.id]"><View /></el-icon>
                            </el-tooltip>
                            <el-tooltip class="item" effect="dark" :content="$t('InGameMonitor.hide')" placement="top" :delay="0" :duration="0"  :hide-after="0" :enterable="false" popper-class="custom_tooltip">
                                <el-icon class="el-icon-view custom-icon"  @click.stop="changeView(index)" v-show="!sensor.showDetails"><Hide /></el-icon>
                            </el-tooltip>
                            <el-tooltip class="item" effect="dark" :content="$t('InGameMonitor.Top')" placement="top" :delay="0" :duration="0"  :hide-after="0" :enterable="false" popper-class="custom_tooltip">
                                <el-icon class="el-icon-upload2 custom-icon" v-show="!sensor.showdelete && sensorGroups[sensor.id] && index !== 0" @click.stop="uploadSensor(sensor)"><Upload /></el-icon>
                            </el-tooltip>
                            <el-tooltip class="item" effect="dark" :content="$t('InGameMonitor.Delete')" placement="top" :delay="0" :duration="0"  :hide-after="0" :enterable="false" popper-class="custom_tooltip">
                                <el-icon class="el-icon-delete custom-icon" v-show="!sensor.showdelete && sensorGroups[sensor.id]" @click.stop="deleteSensor(index)"><Delete /></el-icon>
                            </el-tooltip>
                        </div>
                    </li>
                </ul>
            </div>
        </section>
        <!-- 设置项 -->
        <section class="Settings scroll" v-show="!showSettings">
            <div class="goback hover" @click="goback">
                <el-icon><ArrowLeftBold/></el-icon>
                <p class="goback-text">{{ $t('DesktopMonitoring.Return') }}</p>
            </div>
            <div class="setting_content">
                <div v-if="currentSensor.type2 === 'time' " style="margin-top: 18px;">
                    <div class="datatime">
                        <p>{{ $t('DesktopMonitoring.TimeSelection') }}</p>
                        <div class="itemdata">
                            <el-button v-for="(item, index) in buttons"  :key="`button-${index}`"   :class="{ active: activeIndex === index }"
                            @click="updateTimeType(index)"
                            >
                            {{ $t(item) }}
                            </el-button>
                        </div>
                    </div>
                    <div class="datatime">
                    <p>{{ $t('DesktopMonitoring.Format') }}</p>
                    <div class="itemdata">
                        <el-button v-for="(item, index) in buttons2"  :key="`button2-${index}`" :class="{ active: activeIndex2 === index }"
                        @click="updateTimeFormat(index)"
                        >
                        {{ $t(item) }}
                        </el-button>
                    </div>
                    </div>
                    <div class="datatime">
                    <p>{{ $t('DesktopMonitoring.Rule') }}</p>
                    <div class="itemdata">
                        <el-button v-for="(item, index) in buttons3"  :key="`button3-${index}`"  :class="{ active: activeIndex3 === index }"
                        @click="updateTimeRule(index)"
                        >
                        {{ $t(item) }}
                        </el-button>
                    </div>
                    </div>
                </div>

                <div class="Set_item">
                    <p>{{ $t('DesktopMonitoring.Coordinate') }}</p>
                    <div class="itemdata">
                    <div class="displacement">
                        <span>X</span>
                        <el-input type="number"  @input="updateStyle('left', leftdata)" v-model.number="leftdata" class="Inputcontent"></el-input>
                    </div>
                    <div class="displacement">
                        <span>Y</span>
                        <el-input type="number" @input="updateStyle('top', topdata)" v-model.number="topdata" class="Inputcontent"></el-input>
                    </div>
                    </div>
                </div>

                <div class="Set_item" v-if="currentSensor.type2 === 'text'">
                    <p>{{ $t('DesktopMonitoring.CustomTextContent') }}</p>
                    <div class="itemdata">
                    <div class="displacement">
                        <el-input class="single-des" @input="changeText()" v-model="singleDes" />
                    </div>
                    </div>
                </div>

                <div class="Set_item"  v-if="['sensor', 'progress','graphic','img','chart'].includes(currentSensor.type2)">
                    <!-- <p>{{ currentSensor.type2 === 'sensor' ? '选择传感器：' : '关联传感器：' }}</p> -->
                    <p>{{ currentSensor.type2 === 'sensor' ? $t('DesktopMonitoring.SelectSensor') : $t('DesktopMonitoring.AssociatedSensor1') }}</p>
                    <div class="itemdata">
                    <div class="displacement">
                        <el-button  style="width: auto;padding: 0 30px;"
                        @click="ChangeSensor()" class="Sensor-button">
                        {{ singleName }}
                        </el-button>
                    </div>
                    </div>
                </div>

                <div class="Set_item" v-if="currentSensor.type2 === 'sensor' ">
                    <p>{{ $t('DesktopMonitoring.SensorUnit') }}</p>
                    <div class="itemdata">
                    <div class="displacement">
                        <el-checkbox label="bold" v-model="selectedUnit"  @change="unitChange">{{ $t('DesktopMonitoring.Display') }}</el-checkbox>
                    </div>
                    </div>
                </div>

                <div class="Set_item" v-if="currentSensor.type2 === 'animation' ">
                    <p>{{ $t('DesktopMonitoring.Second') }}</p>
                    <div class="itemdata">
                    <div class="displacement">
                        <el-input type="number" v-model.number="seconds" class="Inputcontent"  @input="updateStyle('seconds', seconds)"></el-input>
                    </div>
                    </div>
                </div>
                
               
                <div class="Set_item" v-if="currentSensor.type2 === 'progress' ">
                    <p>{{ $t('DesktopMonitoring.Corner') }}</p>
                    <div class="itemdata">
                    <div class="displacement">
                        <el-input type="number" v-model.number="borderRadius" class="Inputcontent"  @input="updateStyle('borderRadius', borderRadius)"></el-input>
                    </div>
                    </div>
                </div>
                <div class="Set_item"  v-if="['progress', 'graphic'].includes(currentSensor.type2)">
                    <p>{{ $t('DesktopMonitoring.BackgroundColor') }}</p>
                    <div class="itemdata">
                        <div class="displacement">
                        <el-input v-model="backgcolor" class="Colorbox"></el-input>
                        <el-color-picker v-model="backgcolor" show-alpha :predefine="predefineColors"  @change="updateStyle('backgroundColor', backgcolor)" ></el-color-picker>
                        </div>
                    </div>
                </div>
                <div class="Set_item" v-if="currentSensor.type2 === 'progress' ">
                    <p>{{ $t('DesktopMonitoring.ProgressColor') }}</p>
                    <div class="itemdata">
                        <div class="displacement">
                        <el-input v-model="ProgressColor" class="Colorbox"></el-input>
                        <el-color-picker v-model="ProgressColor" show-alpha :predefine="predefineColors"  @change="updateStyle('ProgressColor', ProgressColor)" ></el-color-picker>
                        </div>
                    </div>
                </div>

                <div v-if="currentSensor.type2 === 'chart' ">
                    <div class="Set_item">
                        <p>{{ $t('DesktopMonitoring.Zigzagcolor') }}</p>
                        <div class="itemdata">
                            <div class="displacement">
                            <el-input v-model="gradientColors1" class="Colorbox"></el-input>
                            <el-color-picker v-model="gradientColors1" show-alpha :predefine="predefineColors"  @change="updateStyle('gradientColors1', gradientColors1)" ></el-color-picker>
                            </div>
                        </div>
                    </div>
                    <div class="Set_item">
                        <p>{{ $t('DesktopMonitoring.Zigzagcolor1') }}</p>
                        <div class="itemdata">
                            <div class="displacement">
                            <el-input v-model="gradientColors2" class="Colorbox"></el-input>
                            <el-color-picker v-model="gradientColors2" show-alpha :predefine="predefineColors"  @change="updateStyle('gradientColors2', gradientColors2)" ></el-color-picker>
                            </div>
                        </div>
                    </div>
                    <div class="Set_item">
                        <p>{{ $t('DesktopMonitoring.Zigzagcolor2') }}</p>
                        <div class="itemdata">
                            <div class="displacement">
                            <el-input v-model="areaColors1" class="Colorbox"></el-input>
                            <el-color-picker v-model="areaColors1" show-alpha :predefine="predefineColors"  @change="updateStyle('areaColors1', areaColors1)" ></el-color-picker>
                            </div>
                        </div>
                    </div>
                    <div class="Set_item">
                        <p>{{ $t('DesktopMonitoring.Zigzagcolor3') }}</p>
                        <div class="itemdata">
                            <div class="displacement">
                            <el-input v-model="areaColors2" class="Colorbox"></el-input>
                            <el-color-picker v-model="areaColors2" show-alpha :predefine="predefineColors"  @change="updateStyle('areaColors2', areaColors2)" ></el-color-picker>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="!['img', 'video', 'svg','progress','graphic','chart'].includes(currentSensor.type2)">
                    <div class="Set_item"v-if="!['animation', 'graphic','chart'].includes(currentSensor.type2)">
                        <p>{{ $t('DesktopMonitoring.Font') }}</p>
                        <div class="itemdata">
                            <div class="displacement font_box">
                            <el-select v-model="selectedFont" :placeholder="$t('DesktopMonitoring.SelectFont')" class="font-select" @change="updateStyle('fontFamily', selectedFont)" :popper-append-to-body="false">
                                <el-option
                                    v-for="item in fontValues"
                                    :key="item"
                                    :label="item"
                                    :value="item"
                                >
                                    <!-- {{ item.fontFamily }} -->
                                </el-option>
                            </el-select>
                            </div>
                        </div>
                    </div>
                    <div class="Set_item" v-if="!['animation', 'graphic','chart'].includes(currentSensor.type2)">
                        <p>{{ $t('DesktopMonitoring.FontSize') }}</p>
                        <div class="itemdata">
                            <div class="displacement">
                            <el-input class="Inputsize" @input="updateStyle('fontSize', singleSize)" v-model="singleSize" />
                            <strong>px</strong>
                            </div>
                        </div>
                    </div>
                    <div class="Set_item" v-if="!['chart'].includes(currentSensor.type2)">
                        <p>{{ $t('DesktopMonitoring.Color') }}</p>
                        <div class="itemdata">
                            <div class="displacement">
                            <el-input v-model="Fontcolor" class="Colorbox"></el-input>
                            <el-color-picker v-model="Fontcolor" show-alpha :predefine="predefineColors"  @change="updateStyle('color', Fontcolor)" ></el-color-picker>
                            </div>
                        </div>
                    </div>
                    <div class="Set_item" v-if="!['animation', 'graphic','chart'].includes(currentSensor.type2)">
                        <p>{{ $t('DesktopMonitoring.Style') }}</p>
                        <div class="itemdata">
                            <div class="displacement">
                            <el-checkbox-group v-model="selectedStyles">
                                <el-checkbox label="bold" @change="toggleStyle('fontWeight', 'bold')">{{ $t('DesktopMonitoring.Bold') }}</el-checkbox>
                                <el-checkbox label="italic" @change="toggleStyle('fontStyle', 'normal')">{{ $t('DesktopMonitoring.Italic') }}</el-checkbox>
                                <el-checkbox label="shadow" @change="toggleShadow">{{ $t('DesktopMonitoring.Shadow') }}</el-checkbox>
                            </el-checkbox-group>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="Set_item" v-if="selectedStyles.includes('shadow')">
                    <p>{{ $t('DesktopMonitoring.ShadowPosition') }}</p>
                    <div class="itemdata">
                    <div class="displacement">
                        <span>X</span>
                        <el-input v-model="shadowX" class="Inputcontent"  @input="updateShadow('x', shadowX)"></el-input>
                    </div>
                    <div class="displacement">
                        <span>Y</span>
                        <el-input v-model="shadowY" class="Inputcontent"  @input="updateShadow('y', shadowY)"></el-input>
                    </div>
                    </div>
                </div>
                <div class="Set_item" v-if="selectedStyles.includes('shadow')">
                    <p>{{ $t('DesktopMonitoring.ShadowEffect') }}</p>
                    <div class="itemdata">
                    <div class="displacement">
                        <span>{{ $t('DesktopMonitoring.Blur') }}</span>
                        <el-input v-model="shadowBlur" class="Inputcontent"  @input="updateShadow('blur', shadowBlur)"></el-input>
                    </div>
                    <!-- <div class="displacement">
                        <span>扩展</span>
                        <el-input v-model="shadowSpread" class="Inputcontent"  @input="updateShadow('spread', shadowSpread)"></el-input>
                    </div> -->
                    </div>
                </div>
                <div class="Set_item" v-if="selectedStyles.includes('shadow')">
                    <p>{{ $t('DesktopMonitoring.ShadowColor') }}</p>
                    <div class="itemdata">
                    <div class="displacement">
                        <el-input v-model="shadowColor" class="Colorbox"></el-input>
                        <el-color-picker v-model="shadowColor" show-alpha :predefine="predefineColors" @change="updateShadow('color', shadowColor)"></el-color-picker>
                    </div>
                    </div>
                </div>

                <!-- 类型图片/视频的共同设置 -->
                <div class="picturevideo" v-if="['img', 'video', 'svg', 'graphic', 'progress','chart'].includes(currentSensor.type2)">
                    <div class="Set_item" v-if="currentSensor.type2 === 'img' || currentSensor.type2 === 'video'">
                    <p>{{ $t('DesktopMonitoring.SelectFromLocalFiles') }}</p>
                    <div class="itemdata" >
                        <div class="displacement">

                        <input type="file" ref="fileInput"  accept="image/png, video/mp4, image/gif" 
                            hidden
                            @change="handleFileUpload"
                        >
                        <el-button
                            class="Sensor-button"
                            @click="fileInput?.click()"
                        >
                           {{ $t('DesktopMonitoring.UploadImageVideo') }}
                        </el-button>
                        </div>
                    </div>
                    </div>
                    <div class="Set_item" v-if="currentSensor.type2 === 'svg'">
                        <p>{{ $t('DesktopMonitoring.SelectFromLocalFiles') }}</p>
                        <input
                            type="file"
                            ref="svgUpload" accept=".svg"
                            hidden
                            @change="handleSvgUpload">
                        <el-button
                            type="primary"
                            @click="svgUpload?.click()">
                            {{ $t('DesktopMonitoring.UploadSVGFile') }}
                        </el-button>
                    </div>

                    <div class="Set_item">
                        <p>{{ $t('DesktopMonitoring.Width') }}</p>
                        <div class="itemdata">
                            <div class="displacement">
                            <el-input  type="number" class="Inputsize" @input="updateStyle('width', singleSizewide)" v-model.number="singleSizewide" />
                            <strong>px</strong>
                            </div>
                        </div>
                    </div>
                    <div class="Set_item">
                        <p>{{ $t('DesktopMonitoring.Height') }}</p>
                        <div class="itemdata">
                            <div class="displacement">
                            <el-input  type="number" class="Inputsize" @input="updateStyle('height', singleSizehigh)" v-model.number="singleSizehigh" />
                            <strong>px</strong>
                            </div>
                        </div>
                    </div>

                    <div class="Set_item" v-if="['img',].includes(currentSensor.type2)">
                        <p>{{ $t('DesktopMonitoring.Effect') }}</p>
                        <div class="itemdata">
                            <div class="displacement">
                            <!-- <el-checkbox-group v-model="selectedStyles"> -->
                                <el-checkbox label="RotationChange" v-model="selectedRotation"  @change='ChangeRotation'>{{ $t('DesktopMonitoring.Rotation') }}</el-checkbox>
                            <!-- </el-checkbox-group> -->
                            </div>
                        </div>
                    </div>

                    <div class="Set_item2" v-show="currentSensor.transformShow">
                        <span>{{ $t('DesktopMonitoring.WhenTheSensorValue') }}</span>
                        <el-input type="number"  @input="updateStyle('Interval', RotationData)" v-model.number="RotationData" class="Inputcontent2"></el-input>
                        <el-select v-model="Animationname" :placeholder="$t('DesktopMonitoring.Select')" @change="updateAnimation('names', Animationname)" style="width: 200px">
                            <el-option
                            v-for="(item, index) in rotationOptions"
                            :key="index"
                            :label="$t(item.name)"
                            :value="index"
                            />
                        </el-select>
                        <el-select v-model="AnimationSeppd" :placeholder="$t('DesktopMonitoring.Select')" @change="updateAnimation('duration', AnimationSeppd)" style="width: 200px">
                            <el-option
                            v-for="(item, index)  in SpeedOptions"
                            :key="index"
                            :label="$t(item.value)"
                            :value="index"
                            />
                        </el-select>

                    </div>

                    <div class="Set_item2" v-show="currentSensor.transformShow">
                        <span>{{ $t('DesktopMonitoring.conditions') }}</span>
                        <el-select v-model="Animationname2" :placeholder="$t('DesktopMonitoring.Select')" @change="updateAnimation('names', Animationname2)" style="width: 200px">
                            <el-option
                            v-for="(item, index)  in rotationOptions2"
                            :key="index"
                            :label="$t(item.name)"
                            :value="index"
                            />
                        </el-select>
                        <el-select v-model="AnimationSeppd2" :placeholder="$t('DesktopMonitoring.Select')" @change="updateAnimation('duration', AnimationSeppd2)" style="width: 200px">
                            <el-option
                            v-for="(item, index)  in SpeedOptions2"
                            :key="index"
                            :label="$t(item.value)"
                            :value="index"
                            />
                        </el-select>

                    </div>


                    <template v-if="currentSensor.type2 === 'svg'">
                    <div class="Set_item">
                        <p>{{ $t('DesktopMonitoring.StrokeColor') }}</p>
                        <div class="itemdata">
                        <div class="displacement">
                            <el-input v-model="currentSensor.style.strokeColor" class="Colorbox"></el-input>
                            <el-color-picker
                            v-model="currentSensor.style.strokeColor"
                            @change="updateStrokeColor"
                            show-alpha>
                            </el-color-picker>
                        </div>
                        </div>
                    </div>

                    <!-- 动态路径颜色控件 -->
                    <div v-for="(fill, index) in currentSensor.style.pathFills"
                        :key="fill"
                        class="Set_item">
                        <p>{{ $t('DesktopMonitoring.Path') }} {{ index + 1 }} {{ $t('DesktopMonitoring.Color1') }}:</p>
                        <div class="itemdata">
                        <div class="displacement">
                            <el-input v-model="currentSensor.style.pathFills[index]" class="Colorbox"></el-input>
                            <el-color-picker
                            v-model="currentSensor.style.pathFills[index]"
                            @change="(color: string) => updatePathFill(index, color)"
                            show-alpha>
                            </el-color-picker>
                        </div>
                        </div>
                    </div>
                    </template>
                </div>

                <div class="Set_item" v-if="['progress','graphic'].includes(currentSensor.type2)">
                    <p>{{ $t('DesktopMonitoring.Effect') }}</p>
                    <div class="itemdata">
                        <div class="displacement">
                        <!-- <el-checkbox-group v-model="selectedStyles"> -->
                            <el-checkbox label="colorChange" v-model="selectedcolor"  @change='Changecolor'>{{ $t('DesktopMonitoring.ChangeColor') }}</el-checkbox>
                        <!-- </el-checkbox-group> -->
                        </div>
                    </div>
                </div>

                <div class="Set_item2" v-show=" currentSensor.Switchcolorsshow">
                    <span>{{ $t('DesktopMonitoring.When') }}</span>
                    <el-select v-model="Numerical" :placeholder="$t('DesktopMonitoring.Select')" @change="updateStyle('Judgmentitem', Numerical)" style="width: 200px">
                        <el-option
                        v-for="(item, index) in options"
                        :key="index"
                        :label="$t(item.value)"
                        :value="index"
                        />
                    </el-select>
                    <el-input type="number"  @input="updateStyle('Interval', IntervalData)" v-model.number="IntervalData" class="Inputcontent2"></el-input>
                    <el-input v-model="ProgressColor2" class="Colorbox Colorbox2"></el-input>
                    <el-color-picker v-model="ProgressColor2" show-alpha :predefine="predefineColors"  @change="updateStyle('ProgressColor2', ProgressColor2)" ></el-color-picker>
                </div>

            </div>
        </section>

        <section class="right-section scroll" v-show="showSettings">
            <!--桌面监控窗口相关的设置-->
            <div class="window-settings">
                <div class="settings-title">
                    <p class="flex-items-center">
                        <el-icon size="20">
                            <Setting/>
                        </el-icon>
                        <span style="margin-left: 10px">{{ $t('DesktopMonitoring.MonitoringSettings') }}</span>
                    </p>
                    <el-button color="#336AB5" class="Settingbutton" @click="reset">{{ $t('DesktopMonitoring.RestoreDefault') }}</el-button>
                </div>
                <div class="settings-content">
                    <section style="width: 380px;">
                        <!--显示器-->
                        <p class="tit">{{ $t('DesktopMonitoring.Monitor') }}</p>
                        <div style="width: 240px;margin-bottom: 20px;">
                            <el-select @change="changeScreen" v-model="window_settings.position.screen">
                                <el-option
                                    v-for="item in data.screen_list"
                                    :key="item.MonitorModal"
                                    :label="item.MonitorModal"
                                    :value="item.MonitorModal">
                                </el-option>
                            </el-select>
                        </div>
                        <!--区域大小-->
                        <p class="tit" style="margin-top: 10px;">{{ $t('DesktopMonitoring.AreaSize') }}</p>
                        <div class="input-box">
                            <span style="color: #999">{{ $t('DesktopMonitoring.Width') }}</span>
                            <el-input-number v-model="window_settings.size.w" :controls="false"
                                             :min="20"></el-input-number>
                            <span>px</span>
                        </div>
                        <div class="input-box" style="margin-bottom: 20px;">
                            <span style="color: #999">{{ $t('DesktopMonitoring.Height') }}</span>
                            <el-input-number v-model="window_settings.size.h" :controls="false"
                                             :min="20"></el-input-number>
                            <span>px</span>
                        </div>
                        <!--背景-->
                        <p class="tit">{{ $t('DesktopMonitoring.Background') }}</p>
                        <el-radio-group v-model="window_settings.background.type">
                            <el-radio value="img/video">{{ $t('DesktopMonitoring.ImageVideo') }}</el-radio>
                            <el-radio value="color">{{ $t('DesktopMonitoring.PureColor') }}</el-radio>
                        </el-radio-group>
                        <!--颜色旋转器-->
                        <div v-show="window_settings.background.type === 'color'">
                            <el-color-picker v-model="window_settings.background.color" show-alpha></el-color-picker>
                        </div>
                        <template v-if="window_settings.background.type === 'img/video'">
                            <!--选图片/视频-->
                            <div class="input-box-file" v-show="window_settings.background.type === 'img/video'">
                                <el-input v-model="bgImgOrVideo" disabled></el-input>
                                <el-button color="#336AB5" class="Settingbutton"  @click="openChooseFileDialog">{{ $t('DesktopMonitoring.Select') }}</el-button>
                            </div>

                            <p class="tit">{{ $t('DesktopMonitoring.ImageVideoDisplayMode') }}</p>
                            <el-radio-group v-model="window_settings.background.img_video_display_type">
                                <el-radio value="拉伸">{{ $t('DesktopMonitoring.Stretch') }}</el-radio>
                                <el-radio value="填充">{{ $t('DesktopMonitoring.Fill') }}</el-radio>
                                <el-radio value="适应">{{ $t('DesktopMonitoring.Adapt') }}</el-radio>
                            </el-radio-group>

                            <p class="tit">{{ $t('DesktopMonitoring.Transparency') }}</p>
                            <div style="width: 300px;">
                                <el-slider
                                    v-model="window_settings.background.opacity"
                                    :marks="marks"
                                    :step="0.01"
                                    :min="0"
                                    :max="1"
                                    :format-tooltip="(value: number) => {return `${(value * 100).toFixed(0)}`}"
                                />
                            </div>
                        </template>
                    </section>
                    <section>
                        <p class="tit">{{ $t('DesktopMonitoring.DisplayPosition') }}</p>
                        <div class="set-position">
                            <div class="nineSquareGrid">
                                <div :class="{'active':window_settings.position.position === 'upperLeft'}"
                                     @click="setNineSquareGrid('upperLeft')"></div>
                                <div :class="{'active':window_settings.position.position === 'upper'}"
                                     @click="setNineSquareGrid('upper')"></div>
                                <div :class="{'active':window_settings.position.position === 'upperRight'}"
                                     @click="setNineSquareGrid('upperRight')"></div>
                                <div :class="{'active':window_settings.position.position === 'left'}"
                                     @click="setNineSquareGrid('left')"></div>
                                <div :class="{'active':window_settings.position.position === 'middle'}"
                                     @click="setNineSquareGrid('middle')"></div>
                                <div :class="{'active':window_settings.position.position === 'right'}"
                                     @click="setNineSquareGrid('right')"></div>
                                <div :class="{'active':window_settings.position.position === 'lowerLeft'}"
                                     @click="setNineSquareGrid('lowerLeft')"></div>
                                <div :class="{'active':window_settings.position.position === 'lower'}"
                                     @click="setNineSquareGrid('lower')"></div>
                                <div :class="{'active':window_settings.position.position === 'lowerRight'}"
                                     @click="setNineSquareGrid('lowerRight')"></div>
                            </div>
                            <div style="color: #999999;max-width: 240px;">
                                <p>{{ $t('DesktopMonitoring.SelectThePosition') }}</p>
                                <!-- <p>当前位置：<span>{{ $t(`${window_settings.position.position}`) }}</span></p> -->
                                <p>{{ $t('DesktopMonitoring.CurrentPosition') }}<span>{{ 
                                    {upperLeft:$t('InGameMonitor.upperLeft'), upper:$t('InGameMonitor.upper'), upperRight:$t('InGameMonitor.upperRight'), left:$t('InGameMonitor.Left'),  middle:$t('InGameMonitor.middle'), right:$t('InGameMonitor.Right'), lowerLeft:$t('InGameMonitor.lowerLeft'), lower:$t('InGameMonitor.lower'), lowerRight:$t('InGameMonitor.lowerRight')}
                                    [window_settings.position.position] 
                                    }}</span></p>
                            </div>
                        </div>

                        <p class="tit">{{ $t('DesktopMonitoring.DragLock') }}</p>
                        <div class="set-drag">
                            <div class="lock" @click="unlock"
                                 v-show="data.isLock === 1"></div>
                            <div class="unlock" @click="setLock"
                                 v-show="data.isLock !== 1"></div>
                            <span class="maxspan">{{ $t('DesktopMonitoring.LockMonitoringPosition') }}</span>
                        </div>
                        <div style="margin-bottom: 10px;margin-left: 30px;">
                            <el-checkbox
                                v-show="data.isLock !== 1"
                                v-model="data.inner_fixed"
                                :label="$t('DesktopMonitoring.Unlockinterior')"
                                style="color: #fff;"
                                @change="changeInnerFixed"
                            >
                            </el-checkbox>
                        </div>

                        <p class="tit">{{ $t('DesktopMonitoring.Font1') }}</p>
                        <div style="width: 240px;margin-bottom: 20px;">
                            <el-select v-model="window_settings.font.font_family" @change="changeFontFamily(window_settings.font.font_family)">
                                <el-option
                                    v-for="item in data.font_list"
                                    :key="item"
                                    :label="item"
                                    :value="item"
                                    :style="{fontFamily: item}"
                                >
                                </el-option>
                            </el-select>
                        </div>

                        <p class="tit">{{ $t('DesktopMonitoring.GameSettings') }}</p>
                        <div>
                            <el-checkbox
                                v-model="data.auto_close"
                                :label="$t('DesktopMonitoring.CloseDesktopMonitor')"
                                style="color: #fff;"
                                @change="GPP_WriteInteger(310,data.auto_close ? 1 : 0)"
                            >
                            </el-checkbox>
                        </div>

                        <div class="oled_itme">
                            <p class="tit">{{ $t('DesktopMonitoring.OLED') }}</p>
                            <el-button @click="showOLEDSetting" class="Settingbutton" type="primary" size="small">{{ $t("InGameMonitor.goGeneralSetting") }}</el-button>
                        </div>
                    </section>
                </div>
            </div>
        </section>
    </div>
</template>

<script setup lang="ts">
import {GPP_GetInteger, GPP_WriteInteger} from "../utils/public";
import {defalutDatas} from "../utils/monitor_config";
import {Setting} from '@element-plus/icons-vue'
import idb from '@/uitls/indexedDB'
import {computed, onMounted, reactive, ref, toRaw, watch ,onBeforeMount} from "vue";
import {Plus,View,Hide,Upload,Delete,ArrowLeftBold} from "@element-plus/icons-vue";
import {gamepp} from 'gamepp'
import {ElMessage} from "element-plus";
import { Sensor } from '../../Game_DMComponent/sharedTypes';
import { throttle,debounce } from 'lodash-es';
import useSensorData from '../shared/useSensorData'
import { storeToRefs } from 'pinia'
const store = useSensorData()
const {sensors} = storeToRefs(store)
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const props = defineProps({
    zoomValue: {
        type: Number,
        default: 1
    },
});

const showSettings = ref(true)
const currentSensor = ref<Sensor>({
  id: 0,
  nums: 1,
  page: 1,
  type: '',
  type2: 'time',
  remark: '',
  sensor: '',
  parameters: '',
  belong: '',
  unit: '',
  unitshow: true,
  mediaSrc: '',
  processedSvg: '',
  group: false,
  showDetails: true,
  hideDetails: false,
  settop: false,
  showdelete: false,
  enter: false,
  style: {
    zIndex: 1,
    fontSize: 12,
    color: '#ffffff',
    top: 0,
    left: 0,
    width: 0,
    height: 0,
    fontFamily: 'Microsoft YaHei',
    fontWeight: 'normal',
    fontStyle: 'normal',
    strokeColor: 'rgba(255, 255, 255, 1)',
    pathFills: [],
    shadow: {
      enabled: false,
      x: 0,
      y: 1,
      blur: 6,
      color: 'rgba(0,0,0,0.1)'
    }
  },
  timeType: 0,
  timeFormat: 0,
  timeRule: 0
})
const buttons = ref<string[]>(['DesktopMonitoring.SystemTime', 'DesktopMonitoring.China', 'DesktopMonitoring.America', 'DesktopMonitoring.Russia', 'DesktopMonitoring.Britain','DesktopMonitoring.France'])
const activeIndex = ref<number>(0)
const buttons2 = ref<string[]>(['DesktopMonitoring.DateAndTime', 'DesktopMonitoring.Time', 'DesktopMonitoring.Date', 'DesktopMonitoring.Week', 'DesktopMonitoring.DateAndTimeAndWeek','DesktopMonitoring.TimeAndWeek'])
const activeIndex2 = ref<number>(0)
const buttons3 = ref<string[]>(['DesktopMonitoring.Hour12', 'DesktopMonitoring.Hour24'])
const activeIndex3 = ref<number>(0)
const topdata = ref<number>(0)
const leftdata = ref<number>(0)
const seconds = ref<number>(0)
const borderRadius = ref<number>(0)
const singleName = ref<string>('')
const selectedUnit = ref<boolean>(true)
const singleDes = ref<string>('')
const selectedFont = ref<string>('QuartzRegular')
const fontValues = ref<string[]>([])
const singleSize = ref<string>('12')
const Fontcolor = ref<string>('rgba(255, 255, 255, 1)')
const backgcolor = ref<string>('rgba(255, 255, 255, 0)')
const ProgressColor = ref<string>('rgba(0, 255, 198, 1)')
const selectedStyles = ref<string[]>([])
const shadowX = ref<number>(0)
const shadowY = ref<number>(1)
const shadowBlur = ref<number>(6)
const shadowColor = ref<string>('rgba(255, 255, 255, 1)')
const predefineColors = ref<string[]>([
  '#ff4500',
  '#ff8c00',
  '#ffd700',
  '#90ee90',
  '#00ced1',
  '#1e90ff',
  '#c71585',
  'rgba(255, 69, 0, 0.68)',
  'rgb(255, 120, 0)',
  'hsv(51, 100, 98)',
  'hsva(120, 40, 94, 0.5)',
  'hsl(181, 100%, 37%)',
  'hsla(209, 100%, 56%, 0.73)',
  '#c7158577'
])
const singleSizewide = ref<number>(0)
const singleSizehigh = ref<number>(0)
const selectedcolor = ref<boolean>(false)
const IntervalData = ref<number>(50)
const selectedRotation = ref<boolean>(false)
const RotationData = ref<number>(3000)

const Animationname = ref<number>(0)
const Animationname2 = ref<number>(1)
const AnimationSeppd = ref<number>(0)
const AnimationSeppd2 = ref<number>(1)

const ProgressColor2 = ref<string>('rgba(218, 72, 17, 1)')
const Numerical = ref<number>(0)
const options = ref([
  {
    value: 'DesktopMonitoring.SensorValue',
  },
  {
    value: 'DesktopMonitoring.SensorValue1',
  },
  {
    value: 'DesktopMonitoring.SensorValue2',
  },
])
const rotationOptions = ref([
  { name: 'DesktopMonitoring.Clockwise',},
  {name: 'DesktopMonitoring.Counterclockwise', }
])
const SpeedOptions = ref([
  {value: 'DesktopMonitoring.QuickRotation',},
  { value: 'DesktopMonitoring.SlowRotation',},
  { value: 'DesktopMonitoring.StopRotation',},
])
const rotationOptions2 = ref([
  { name: 'DesktopMonitoring.Clockwise',},
  {name: 'DesktopMonitoring.Counterclockwise', }
])
const SpeedOptions2 = ref([
  {value: 'DesktopMonitoring.QuickRotation',},
  { value: 'DesktopMonitoring.SlowRotation',},
  { value: 'DesktopMonitoring.StopRotation',},
])
const activeId = ref(1)
const window_settings = reactive({
    background: {
        type: "color",
        color: "#000000",
        img: "",
        video: "",
        img_video_display_type: "填充", // 拉伸/填充/适应
        opacity: 1,
        img_video_name:"",
    },
    size: {
        w: 600,
        h: 600,
    },
    font: {
        font_family: "Microsoft Yahei",
    },
    position: {
        screen: "",  // 屏幕
        position: "upperRight",  // 位置
        x: -1,
        y: -1,
    }
})

const gradientColors1 = ref<string>('rgba(77 158 103,1)')
const gradientColors2 = ref<string>('rgba(77 158 103,1)')
const areaColors1 = ref<string>('rgba(186, 0, 255, 0.2)')
const areaColors2 = ref<string>('rgba(186, 0, 255, 0.8)')

watch(window_settings, () => {
    idb.setItem(`custom_monitor_window_setting_${activeId.value}`, toRaw(window_settings))
},{deep: true})
const data = reactive({
    screen_list: [] as any[],
    font_list: [] as any[],
    isLock: 1,
    auto_close: false,
    inner_fixed:false
})
const marks = reactive({
    0: "0",
    0.1: "10",
    0.2: "20",
    0.3: "30",
    0.4: "40",
    0.5: "50",
    0.6: "60",
    0.7: "70",
    0.8: "80",
    0.9: "90",
    1: "100",
})
// const sensors = ref([
// ]);
const scrollContainer = ref<HTMLElement | null>(null);
const isDragging = ref(false);
const dragStartIndex = ref(-1);
const dragOverIndex = ref(-1);
const hoveredId = ref<number | null>(null);
const selectedId = ref<number | null>(null);
const selected = ref<number | null>(null);
const titles = ref ([
    { text: 'DesktopMonitoring.Type', class: '' },
    { text: 'DesktopMonitoring.Remarks', class: '' },
    { text: 'DesktopMonitoring.AssociatedSensor', class: 'Extrawidth' },
    { text: 'DesktopMonitoring.Operation', class: '' }
])
const fileInput = ref<HTMLInputElement>()
const svgUpload = ref<HTMLInputElement>()
const sensorGroups = ref<{ [key: number]: boolean }>({});
const bgImgOrVideo = computed(() => {
    return window_settings.background.img_video_name
})
let handle:any = null;
const currentLanguage = ref('CN');
// let activeId = ref<number | null>(null);
let TimeStands = ref<string | null>(null);
onMounted(async () => {
    window.addEventListener('storage', handleStorageChange)
    loadFontFamilies();
    const savedId = localStorage.getItem('activeMonitorId');
    if (savedId) {
        // store.initializeSensors(Number(savedId));
        const storedData = localStorage.getItem(`sensorSettings_${savedId}`);
        sensors.value = storedData ? JSON.parse(storedData) : [];
    }else{
        store.initializeSensors(1)
        localStorage.setItem('activeMonitorId', '1')
    }
    updateZIndices(); // 初始化时更新zIndex
    window.queryLocalFonts().then((res: any[]) => {
        const s = new Set()
        for (const item of res) {
            if (s.has(item.family)) continue;
            data.font_list.push(item.family)
            s.add(item.family)
        }
        const customFonts = ['QuartzRegular', 'Hybriddd' ,'AeeAndCui'];
        customFonts.forEach(font => {
            if (!s.has(font)) {
                data.font_list.push(font);
                s.add(font);
            }
        });
    })
    data.isLock = await GPP_GetInteger(311);
    data.auto_close = await GPP_GetInteger(310) === 1;
    checkLocal()
    processingData()
    try {
    gamepp.webapp.onInternalAppEvent.addEventListener(async (value:any) => {
        console.log(value,value.type,'桌面监控')
        activeId.value = Number(localStorage.getItem('activeMonitorId'));
        if(value.action === 'Change'){
            console.log('Change')
            console.log(value,value.action,'传感器')
            singleName.value = value.value.name
            currentSensor.value.sensor = value.value.name
            currentSensor.value.enter = false
            currentSensor.value.belong = value.value.all.name
            currentSensor.value.unit = value.value.unit
            // localStorage.setItem(`sensorSettings_${activeId.value}`, JSON.stringify(sensors.value));
        }
        if (value.type === 'sensors') {
            console.log('sensors')
            isDragging.value = false;
            sensors.value = value.data;
            localStorage.setItem(`sensorSettings_${activeId.value}`, JSON.stringify(sensors.value));
        }
        if(value.type === 'hoveredId'){
            hoveredId.value = value.data;
        }
        if(value.type === 'selectedId'){
            let index = value.index
            let data = value.sensor
            TimeStands.value = value.TimeStands
            handleSelect(index, data)
            // SingleSetting(index)
        }
    });
  } catch (error) {
    console.error(error); // It's a good practice to log the error
  }

  const savedLanguage = localStorage.getItem('language');
  if (savedLanguage) {
      currentLanguage.value = savedLanguage;
  }

  if (gamepp.webapp.windows.isValid.sync('desktop_monitor')) {
    handle = await gamepp.dialog.showdeskMonitor.promise();
  }
    const inner_fixed = localStorage.getItem('inner_fixed')
    if(inner_fixed === 'true'){
        data.isLock = 0
        data.inner_fixed = true
        if (handle && gamepp.webapp.windows.isValid.sync('desktop_monitor')) {
            await gamepp.moveDestopWindow.promise(handle);
        }
    }else{
        data.inner_fixed = false
        data.isLock = 1
    }
})
onBeforeMount(() => {
    window.removeEventListener('storage', handleStorageChange)
})
function  showOLEDSetting() {
  window.localStorage.setItem('setting_anchorPoint','#OLED')
  gamepp.webapp.windows.show.sync('gamepp_config', false)
}
function changeScreen() {
    window_settings.position.position = 'upperRight'
    window_settings.position.x = -1
    window_settings.position.y = -1
}
const handleStorageChange = (event:any)=> {
    if (event.key === 'sensorDragData') {
        const dragData = JSON.parse(event.newValue || '{}');
        if (dragData.id) {
            // 找到对应的传感器并更新其 top 和 left
            const sensorIndex = sensors.value.findIndex((s: { id: any; }) => s.id === dragData.id);
            if (sensorIndex !== -1) {
                sensors.value[sensorIndex].style.top = dragData.top;
                sensors.value[sensorIndex].style.left = dragData.left;
                topdata.value =  dragData.top;
                leftdata.value = dragData.left;
            }
        }
    }
    if(event.key === 'language') {
        currentLanguage.value = event.newValue || 'CN';
    }
    // activeId.value = Number(localStorage.getItem('activeMonitorId'));
    // if(event.key === `sensorSettings_${activeId.value}`) {
    //     const storedData = event.newValue;
    //     sensors.value = storedData ? JSON.parse(storedData) : [];
    // }
}
const handleMouseEnter = throttle((sensor: { group: boolean;id: number}) => {
    // sensor.group = true;
    sensorGroups.value[sensor.id] = true;
    hoveredId.value = sensor.id;
// requestAnimationFrame(() => {
    const obj = {
        type: 'hoveredId',
        data: sensor.id,
    }
    if (gamepp.webapp.windows.isValid.sync('desktop_monitor')) {
        gamepp.webapp.sendInternalAppEvent.promise('desktop_monitor', obj);
    }
    // })
},100)
const handleMouseLeave =throttle((sensor: { group: boolean; id: number}) => {
    // sensor.group = false
    sensorGroups.value[sensor.id] = false;
    Object.keys(sensorGroups.value).forEach(key => {
     sensorGroups.value[parseInt(key)] = false;
    });
    hoveredId.value = null;
    // requestAnimationFrame(() => {
    if (gamepp.webapp.windows.isValid.sync('desktop_monitor')) {
        gamepp.webapp.sendInternalAppEvent.promise('desktop_monitor', 'hoveredIdfalse');
    }
    // })
},100)

const saveSensors = () => {
  activeId.value = Number(localStorage.getItem('activeMonitorId'));
  localStorage.setItem(`sensorSettings_${activeId.value}`, JSON.stringify(sensors.value));
};

const startDragli = (event: MouseEvent, index: number) => {
    console.log(props.zoomValue,'props.zoomValue')
  isDragging.value = true;
  dragStartIndex.value = index;
//   const draggedSensorId = sensors[index].id;

  document.addEventListener('mousemove', handleDragli);
  document.addEventListener('mouseup', stopDragli);
};

const handleDragli = (event: MouseEvent) => {
  if (!isDragging.value) return;
  const zoomFactor = props.zoomValue;
  const container = scrollContainer.value!
  const containerRect = container.getBoundingClientRect();
  const containerHeight = containerRect.height * zoomFactor;
  const currentY = event.clientY - containerRect.top* zoomFactor;

  // 边界检测
  if (
    event.clientY < (containerRect.top * props.zoomValue) ||
    event.clientY > (containerRect.bottom * props.zoomValue) ||
    event.clientX < (containerRect.left * props.zoomValue) ||
    event.clientX > (containerRect.right * props.zoomValue)
  ) {
    stopDragli();
    return;
  }

   // 自动滚动容器逻辑
    const scrollThreshold = 50 * zoomFactor;
    const scrollAmount = 10 * zoomFactor
    if (currentY < scrollThreshold) {
        // 向上滚动
        container.scrollTop = Math.max(-10,  Math.round(container.scrollTop * zoomFactor) - scrollAmount);
        console.log(container.scrollTop,'container.scrollTop')
    } else if (currentY > containerHeight - scrollThreshold) {
        // 向下滚动
        const maxScrollTop =  Math.round(container.scrollHeight * zoomFactor) - Math.round(container.clientHeight * zoomFactor);;
        container.scrollTop = Math.min(maxScrollTop,  Math.round(container.scrollTop * zoomFactor) + scrollAmount);
        console.log(container.scrollTop,'container.scrollTop',maxScrollTop)
    }
    const newIndex = Math.max(0, Math.min(sensors.value.length - 1,  Math.floor(
        (currentY +  Math.round(container.scrollTop * zoomFactor)) / (32 * zoomFactor)
    ))); 
    dragOverIndex.value = newIndex;

    if (dragStartIndex.value !== dragOverIndex.value) {
        const draggedItem = sensors.value.splice(dragStartIndex.value, 1)[0];
        sensors.value.splice(dragOverIndex.value, 0, draggedItem);
        dragStartIndex.value = dragOverIndex.value;
        updateZIndices();
    }
};

const stopDragli = () => {
  if (!isDragging.value) return;
  isDragging.value = false;
  dragOverIndex.value = -1;
//   dragStartIndex.value = -1;
  document.removeEventListener('mousemove', handleDragli);
  document.removeEventListener('mouseup', stopDragli);
  saveSensors();
};

const updateZIndices = () => {
  sensors.value.forEach((sensor: { style: { zIndex: number; }; }, index: number) => {
    sensor.style.zIndex = sensors.value.length - index;
  });
  saveSensors();
};

const changeView = (index: number) => {
  const currentList = sensors.value;
  const visibleCount = currentList.filter((item: { showDetails: any; }) => item.showDetails).length;

  if (currentList[index].showDetails && visibleCount <= 1) {
    ElMessage({
      message: '至少保留一个可见传感器',
      type: 'warning',
    });
    return;
  }

  currentList[index].showDetails = !currentList[index].showDetails;
  currentList[index].hideDetails = !currentList[index].hideDetails;
  saveSensors();
};

const uploadSensor = (sensor: any) => {
  const maxZIndex = Math.max(...sensors.value.map((s: { style: { zIndex: any; }; }) => s.style.zIndex));
  sensor.style.zIndex = maxZIndex + 1;
  sensors.value.sort((a: { style: { zIndex: number; }; }, b: { style: { zIndex: number; }; }) => b.style.zIndex - a.style.zIndex);
  updateZIndices();
};

const deleteSensor = (index: number) => {
    sensors.value.splice(index, 1);
    updateZIndices();
    goback()
};

const showSensor= () => {
    gamepp.webapp.windows.show.sync('new_component',false)
    goback()
}

const handleSelect = async(index: any,sensor: any) => {
    selected.value = index;
    // selectedId.value = selectedId.value === sensor.id ? null : sensor.id;
    selectedId.value = sensor.id
    SingleSetting(index)
    console.log(selectedId.value,'selectedId')
    data.isLock = 0;
    data.inner_fixed = true;
    // changeInnerFixed()
    // if (gamepp.webapp.windows.isValid.sync('desktop_monitor')) {
    //     gamepp.webapp.sendInternalAppEvent.promise('desktop_monitor', 'setInnerFixed');
    // }

    const obj = {
        type: 'selectedId',
        data: selectedId.value,
        Zindex:999,
        // Zindexold: sensor.style.zIndex,
    }
    if (gamepp.webapp.windows.isValid.sync('desktop_monitor')) {
        gamepp.webapp.sendInternalAppEvent.promise('desktop_monitor', obj);
        gamepp.webapp.sendInternalAppEvent.promise('desktop_monitor', 'setInnerFixed');
    }

    const MonitorLock = await gamepp.getDesktopMonitorLockState.promise(handle)
    if (MonitorLock) {
        await gamepp.moveDestopWindow.promise(handle);
    }
    // await gamepp.moveDestopWindow.promise(handle);
    localStorage.setItem('inner_fixed',  JSON.stringify(data.inner_fixed));
}
const SingleSetting = (index: string | number) => {
  showSettings.value = false;
  currentSensor.value = sensors.value[index];
//   currentSensor.value = { ...sensors.value[index] };
  console.log(currentSensor.value,'currentSensor',sensors.value[index])
  // 将时间类型转换为对应的按钮索引
//   activeIndex.value = buttons.value.indexOf(currentSensor.value.timeType)??'' ;
//   activeIndex2.value = buttons2.value.indexOf(currentSensor.value.timeFormat) || 0;
//   activeIndex3.value = buttons3.value.indexOf(currentSensor.value.timeRule) || 0;

    activeIndex.value = currentSensor.value.timeType ?? 0;
    activeIndex2.value = currentSensor.value.timeFormat ?? 0;
    activeIndex3.value =currentSensor.value.timeRule ?? 0;
    
    console.log(sensors.value[index].type2,'1111111111111111111111112222',TimeStands.value)
    // if(sensors.value[index].type2 === 'time' && TimeStands.value !=='notAllowed'){
    //     console.log('时间')
    //     updateTimeType(activeIndex.value);
    //     updateTimeFormat(activeIndex2.value);
    //     updateTimeRule(activeIndex3.value);
    // }

  singleName.value = currentSensor.value.sensor;
  const selectedFonts = currentSensor.value.style.fontFamily ?? '';
  selectedFont.value = selectedFonts;
//   singleSize.value = currentSensor.value.style.fontSize;
  singleSize.value = currentSensor.value.style.fontSize ? currentSensor.value.style.fontSize.toString() : '12';
  Fontcolor.value = currentSensor.value.style.color ?? '';
  backgcolor.value = currentSensor.value.style.backgroundColor ?? '';
  ProgressColor.value = currentSensor.value.style.ProgressColor ?? '';
  topdata.value = currentSensor.value.style.top;
  leftdata.value = currentSensor.value.style.left;
  seconds.value = currentSensor.value.style.seconds ?? 0;
  borderRadius.value = currentSensor.value.style.borderRadius ?? 0;
  singleSizewide.value = currentSensor.value.style.width?? 0;
  singleSizehigh.value = currentSensor.value.style.height?? 0;
  singleDes.value = currentSensor.value.remark;
  gradientColors1.value = currentSensor.value.style.gradientColors1 ?? '';
  gradientColors2.value = currentSensor.value.style.gradientColors2 ?? '';
  areaColors1.value = currentSensor.value.style.areaColors1 ?? '';
  areaColors2.value = currentSensor.value.style.areaColors2 ?? '';

  if (currentSensor.value.style.shadow) {
    shadowX.value = currentSensor.value.style.shadow.x || 0;
    shadowY.value = currentSensor.value.style.shadow.y || 1;
    shadowBlur.value = currentSensor.value.style.shadow.blur || 6;
    // shadowSpread.value = currentSensor.value.style.shadow.spread;
    shadowColor.value = currentSensor.value.style.shadow.color|| 'rgba(255, 255, 255, 1)';
  }

  selectedStyles.value = [];
  if (currentSensor.value.style.fontWeight === 'bold') {
    selectedStyles.value.push('bold');
  }
  if (currentSensor.value.style.fontStyle === 'italic') {
    selectedStyles.value.push('italic');
  }
  if (currentSensor.value.style.shadow?.enabled) {
    selectedStyles.value.push('shadow');
  }

  selectedUnit.value = currentSensor.value.unitshow?? true;
  selectedcolor.value = currentSensor.value.Switchcolorsshow?? false;
  IntervalData.value = currentSensor.value.style.Interval?? 0;
  ProgressColor2.value = currentSensor.value.style.ProgressColor2 ?? '';
  Numerical.value = currentSensor.value.style.Judgmentitem ?? 0;

  selectedRotation.value = currentSensor.value.transformShow ?? false;
  RotationData.value = currentSensor.value.style.Interval ?? 0;
  Animationname.value = currentSensor.value.style.animation?.names ?? 0;
  AnimationSeppd.value = currentSensor.value.style.animation?.duration ?? 0;
  Animationname2.value = currentSensor.value.style.animation?.names2 ?? 0;
  AnimationSeppd2.value = currentSensor.value.style.animation?.duration2 ?? 0;

};

const goback = () => {
    showSettings.value = true
    selected.value = null;
    if (gamepp.webapp.windows.isValid.sync('desktop_monitor')) {
        gamepp.webapp.sendInternalAppEvent.promise('desktop_monitor', 'selectedIdfalse');
    }
}

// 更新时间类型
function updateTimeType(index: number) {
  activeIndex.value = index
  currentSensor.value.timeType = index
  saveSensors();
}

// 更新时间格式
function updateTimeFormat(index: number) {
  activeIndex2.value = index
  currentSensor.value.timeFormat = index
  saveSensors();
}

// 更新时间规则
function updateTimeRule(index: number) {
  activeIndex3.value = index
  currentSensor.value.timeRule = index
  saveSensors();
}


const updateStyle = (property:any, value: any) => {
  currentSensor.value.style[property] = value
  console.log(sensors.value,'sensors.value',value)
  saveSensors();
}
const changeFontFamily = (fontFamily: string) => {
    sensors.value.forEach((sensor: any) => {
        sensor.style.fontFamily = fontFamily
    })
    saveSensors();
}
const changeText = () => {
  currentSensor.value.remark = singleDes.value
  saveSensors();
}

const ChangeSensor = () => {
    window.localStorage.setItem('SensorType', 'Change');
    if(gamepp.webapp.windows.isValid.sync("Sensorchoose")){
        gamepp.webapp.windows.close.sync('Sensorchoose')
        setTimeout(() => {
            gamepp.webapp.windows.show.sync('Sensorchoose',false)
        }, 300);
    }
    gamepp.webapp.windows.show.sync('Sensorchoose',false)
    window.localStorage.removeItem('SensorManageType') // {{}}此时传感器操作的种类
    window.localStorage.removeItem('SensorManageData') // 更改此时传感器操作的种类
    window.localStorage.removeItem('SensorManageFilter') // 更改此时传感器操作的种类
    gamepp.webapp.windows.close.sync('hardware_setupsensor')
}

const unitChange = () => {
  currentSensor.value.unitshow = selectedUnit.value
  saveSensors();
}

const Changecolor = () => {
  currentSensor.value.Switchcolorsshow = selectedcolor.value
  saveSensors();
}

const ChangeRotation = () => {
    currentSensor.value.transformShow = selectedRotation.value
    saveSensors();
}

const toggleStyle = (styleProperty: 'fontStyle' | 'fontWeight', styleValue: string) => {
  const currentValue = currentSensor.value.style[styleProperty] || (styleProperty === 'fontStyle' ? 'normal' : '400')
  let newValue = ''

  if (styleProperty === 'fontStyle') {
    newValue = currentValue === 'italic' ? 'normal' : 'italic'
  } else {
    newValue = currentValue === styleValue ? 'normal' : styleValue
  }
  updateStyle(styleProperty, newValue)

}

const toggleShadow = (checked: boolean) => {
  const hasShadow = checked

  if (hasShadow) {
    currentSensor.value.style.shadow = {
      enabled: true,
      x: shadowX.value,
      y: shadowY.value,
      blur: shadowBlur.value,
      color: shadowColor.value
    }
    if (!selectedStyles.value.includes('shadow')) {
      selectedStyles.value.push('shadow')
    }
  } else {
    if (currentSensor.value.style.shadow) {
      currentSensor.value.style.shadow.enabled = false
    }
    selectedStyles.value = selectedStyles.value.filter(s => s !== 'shadow')
  }
  saveSensors();
}

const updateShadow = (property: string, value: any) => {
  if (!currentSensor.value.style.shadow) {
    currentSensor.value.style.shadow = {}
  }
  currentSensor.value.style.shadow[property as keyof typeof currentSensor.value.style.shadow] = value
  saveSensors();
}

const updateAnimation = (property: string, value: any) => {
  if (!currentSensor.value.style.animation) {
    currentSensor.value.style.animation = {}
  }
  currentSensor.value.style.animation[property as keyof typeof currentSensor.value.style.animation] = value
  saveSensors();
}


// const handleFileUpload = (e: Event) => {
//     const target = e.target as HTMLInputElement
//   const file = target.files?.[0]

//   if (!file) return

//   // 验证文件类型
//   if (!file.type.startsWith('image/png') && !file.type.startsWith('video/mp4') && !file.type.startsWith('image/gif')) {
//     ElMessage.error('只支持 PNG 和 MP4 格式')
//     return
//   }

//   const objectURL = URL.createObjectURL(file)
//   const newSensor = {
//     ...currentSensor.value!,
//     mediaSrc: objectURL,
//     type: file.type.startsWith('image/png') || file.type.startsWith('image/gif') ? '图片' : '视频',
//     type2: file.type.startsWith('image/png') || file.type.startsWith('image/gif') ? 'img' : 'video',
//     nums: file.type.startsWith('image/png') || file.type.startsWith('image/gif') ? 3 : 4,
//   };
//   const index = sensors.value.findIndex((s: { id: number; nums: number; type: string; type2: string; remark: string; sensor: string; parameters?: string | undefined; page: number; group: boolean; showDetails: boolean; hideDetails: boolean; settop: boolean; showdelete: boolean; enter: boolean; unit?: string | undefined; unitshow?: boolean | undefined; mediaSrc?: string | undefined; processedSvg?: string | undefined; style: { [x: string]: any; zIndex: number; fontSize: number; color?: string | undefined; top: number; left: number; width?: number | undefined; height?: number | undefined; fontFamily?: string | undefined; fontStyle?: string | undefined; fontWeight?: string | undefined; strokeColor?: string | undefined; pathFills?: string[] | undefined; shadow?: { [x: string]: any; enabled?: boolean | undefined; x?: number | undefined; y?: number | undefined; blur?: number | undefined; color?: string | undefined; } | undefined; }; timeType?: string | undefined; timeFormat?: string | undefined; timeRule?: string | undefined; class?: string | undefined; }) => s === currentSensor.value)
//   sensors.value[index] = newSensor
//   currentSensor.value = { ...newSensor }
//   saveSensors();
// }

const handleFileUpload = (e: Event) => {
    const target = e.target as HTMLInputElement;
    const file = target.files?.[0];
    if (!file) return;
    // 验证文件类型
    if (!file.type.startsWith('image/png') && !file.type.startsWith('video/mp4') && !file.type.startsWith('image/gif')) {
        ElMessage.error('只支持 PNG、GIF 和 MP4 格式');
        return;
    }

    // 使用 FileReader 将文件转换为 Base64
    const reader = new FileReader();
    reader.onload = (event) => {
        if (event.target?.result) {
            const base64 = event.target.result as string;
            const newSensor = {
                ...currentSensor.value!,
                mediaSrc: base64, // 使用 Base64 格式
                type: file.type.startsWith('image/png') || file.type.startsWith('image/gif') ? '图片' : '视频',
                type2: file.type.startsWith('image/png') || file.type.startsWith('image/gif') ? 'img' : 'video',
                nums: file.type.startsWith('image/png') || file.type.startsWith('image/gif') ? 3 : 4,
            };
            const index = sensors.value.findIndex((s: { id: number; nums: number; type: string; type2: string; remark: string; sensor: string; parameters?: string | undefined; belong?: string | undefined; page: number; group: boolean; showDetails: boolean; hideDetails: boolean; settop: boolean; showdelete: boolean; enter: boolean; unit?: string | undefined; unitshow?: boolean | undefined; mediaSrc?: string | undefined; processedSvg?: string | undefined; style: { [x: string]: any; zIndex: number; fontSize?: number | undefined; color?: string | undefined; top: number; left: number; width?: number | undefined; height?: number | undefined; fontFamily?: string | undefined; fontStyle?: string | undefined; fontWeight?: string | undefined; strokeColor?: string | undefined; pathFills?: string[] | undefined; seconds?: number | undefined; backgroundColor?: string | undefined; backgroundColor2?: string | undefined; borderRadius?: number | undefined; ProgressColor?: string | undefined; ProgressColor2?: string | undefined; Interval?: number | undefined; Judgmentitem?: number | undefined; gradientColors1?: string | undefined; gradientColors2?: string | undefined; areaColors1?: string | undefined; areaColors2?: string | undefined; shadow?: { [x: string]: any; enabled?: boolean | undefined; x?: number | undefined; y?: number | undefined; blur?: number | undefined; color?: string | undefined; } | undefined; animation?: { [x: string]: any; names?: number | undefined; names2?: number | undefined; duration?: number | undefined; duration2?: number | undefined; } | undefined; }; timeType?: number | undefined; timeFormat?: number | undefined; timeRule?: number | undefined; class?: string | undefined; Switchcolorsshow?: boolean | undefined; transformShow?: boolean | undefined; }) => s === currentSensor.value);
            if (index !== -1) {
                sensors.value[index] = newSensor;
                currentSensor.value = { ...newSensor };
                saveSensors();
            }
        }
    };

    reader.onerror = () => {
        ElMessage.error('文件读取失败');
    };

    reader.readAsDataURL(file);
};

const handleSvgUpload = async (e: Event) => {
  const target = e.target as HTMLInputElement
  const file = target.files?.[0]
  if (!file) return
  try {
    // 读取文件内容
    const text = await file.text()
    const parser = new DOMParser()
    const svgDoc = parser.parseFromString(text, "image/svg+xml")
    // 提取路径信息
    const paths = Array.from(svgDoc.querySelectorAll('path'))
    // 更新响应式数据
    currentSensor.value.style.pathFills = paths.map(p =>
      p.getAttribute('fill') || '#000000'
    )
    // 查找描边路径
    const strokePath = paths.find(p => p.hasAttribute('stroke'))
    if (strokePath) {
      currentSensor.value.style.strokeColor =  strokePath.getAttribute('stroke') || 'rgba(255, 255, 255, 1)'
    }
    // 更新 SVG 内容并刷新预览
    currentSensor.value.processedSvg = svgDoc.documentElement.outerHTML
    updateSvgPreview()
  } catch (error) {
    console.error('文件读取失败:', error)
    ElMessage.error('SVG 文件处理失败')
  }
  saveSensors();
}

const updateStrokeColor = (color: string) => {
  if (!currentSensor.value) return
  currentSensor.value.style.strokeColor = color
  updateSvgPreview()
}

const updateSvgPreview = () => {
  if (!currentSensor.value?.processedSvg) return

  const svgDoc = new DOMParser().parseFromString(
    currentSensor.value.processedSvg,
    "image/svg+xml"
  )

  const paths = svgDoc.querySelectorAll('path')
  paths.forEach((path, index) => {
    const fillColor = currentSensor.value!.style.pathFills?.[index] ?? 'none'
    path.setAttribute('fill', fillColor)

    if (currentSensor.value!.style.strokeColor) {
      path.setAttribute('stroke', currentSensor.value!.style.strokeColor)
    }
  })

  currentSensor.value.processedSvg = svgDoc.documentElement.outerHTML
  saveSensors();
}

const updatePathFill = (index: number, color: string) => {
  if (!currentSensor.value) return
  currentSensor.value.style.pathFills[index] = color
  updateSvgPreview()
}

const loadFontFamilies = async () => {
    try {
    // const fontData = gamepp.getFontFamilys();
    // fontValues.value = fontData || fontValues.value;
    // let arr = new Set()
    let arr = new Set(["QuartzRegular","Hybriddd","AeeAndCui"]);
    try {
        const availableFonts = await window.queryLocalFonts();
        for (const fontData of availableFonts) {
        if (fontData.family.toLowerCase().startsWith('st')){
            continue
        }
        arr.add(fontData.family) 
        }
        fontValues.value = Array.from(arr)
    } catch (err) {
        console.error('获取字体失败', err);
    }
    } catch (error) {

    }
};
const changeInnerFixed = async() => {
  data.inner_fixed = !data.inner_fixed;
  if (data.inner_fixed) {
    data.inner_fixed = false;
    await gamepp.displayDestopWindow.promise(handle);
    // GPP_WriteInteger(311,0);
    if (gamepp.webapp.windows.isValid.sync('desktop_monitor')) {
        gamepp.webapp.sendInternalAppEvent.promise('desktop_monitor', 'setInnerFixedcolse');
    }
    localStorage.setItem('inner_fixed', 'false');
  } else {
    data.inner_fixed = true;
    await gamepp.moveDestopWindow.promise(handle);
    // GPP_WriteInteger(311,1);
    if (gamepp.webapp.windows.isValid.sync('desktop_monitor')) {
        gamepp.webapp.sendInternalAppEvent.promise('desktop_monitor', 'setInnerFixed');
    }
    localStorage.setItem('inner_fixed', 'true');

  }
}
// watch(sensors, (newVal) => {
// //   if (!isDragging.value) {
//     console.log('sensors changed监听:', newVal);

//     const cleanData = newVal.map(item => ({
//       ...item,
//       mediaSrc: item.mediaSrc,
//       processedSvg: item.processedSvg,
//       style: {
//         ...item.style,
//         left: Number(item.style.left) || 0,
//         top: Number(item.style.top) || 0,
//         shadow: item.style.shadow ? {
//           ...item.style.shadow,
//           enabled: !!item.style.shadow.enabled
//         } : null
//       }
//     }));
//     localStorage.setItem('sensorSettings', JSON.stringify(cleanData));
// //   }
// }, { deep: true });
let positionX = 0, positionY = 0;
async function unlock() {
    console.log('解锁');
    await gamepp.moveDestopWindow.promise(handle);
    if(data.inner_fixed === false){
        GPP_WriteInteger(311,0);
    }
    data.isLock = 0;
    let PresentBounds = await gamepp.webapp.windows.getBounds.promise('desktop_monitor');
    positionX = PresentBounds['x'];
    positionY = PresentBounds['y'];
}
async function setLock() {
    console.log('锁定');
    if (gamepp.webapp.windows.isValid.sync('desktop_monitor')) {
        gamepp.webapp.sendInternalAppEvent.promise('desktop_monitor', 'setInnerFixed');
    }
    GPP_WriteInteger(311,1);
    data.isLock = 1
    //获取当前监控宽高位置
    let PresentBounds = await gamepp.webapp.windows.getBoundsHasParent.promise('desktop_monitor');
    let {bounds} = PresentBounds;
    if (positionY !== bounds['y'] || positionX !== bounds['x']) {
        window_settings.position.x = bounds['x'];
        window_settings.position.y = bounds['y'];
        window_settings.position.position = "";
        idb.setItem(`custom_monitor_window_setting_${activeId.value}`, toRaw(window_settings))
    }
    //保存监控位置
    GPP_WriteInteger(233, PresentBounds['bounds']['x']);
    GPP_WriteInteger(234, PresentBounds['bounds']['y']);
    GPP_WriteInteger(446, PresentBounds['flag']);
    console.warn('handle',handle);
    await gamepp.displayDestopWindow.promise(handle);
}
function setNineSquareGrid(position: string) {
    window_settings.position.position = position;
    window_settings.position.x = -1;
    window_settings.position.y = -1;
}
async function openChooseFileDialog() {
    const AddFileResult = await gamepp.dialog.showOpenDialog.promise({
        title: '添加文件',
        properties: ["openFile"],
        filters: [{name: 'Files', extensions: ['jpg', 'png','bmp', 'mkv', 'avi', 'mp4','rmvb','flv','webp']}]
    });
    if (!AddFileResult['canceled']) {
        console.log(AddFileResult)
        const file = AddFileResult['filePaths'][0]
        const fileType = file.split(".").pop().toLowerCase();

        if (fileType === "jpg" || fileType === "png" || fileType === "bmp"|| fileType === "webp"){
            window_settings.background.video = "";
            window_settings.background.img = file;
            window_settings.background.img_video_name = file.split("/").pop()
        } else if (fileType === "mkv" || fileType === "avi" || fileType === "mp4" || fileType === "rmvb" || fileType === "flv") {
            window_settings.background.img = "";
            window_settings.background.video = file;
            window_settings.background.img_video_name = file.split("/").pop()
        }
    }
}
function reset() {
    activeId.value = Number(localStorage.getItem('activeMonitorId'));
    window.localStorage.removeItem(`sensorSettings_${activeId.value}`);
    setTimeout(() => {
        // const savedId = localStorage.getItem('activeMonitorId');
        if (activeId.value) {
            store.initializeSensors(activeId.value);
        }else{
            store.initializeSensors(1)
        }
        const family = window.localStorage.getItem(`sensorSettings_${activeId.value}`);
        if(family){
            sensors.value = JSON.parse(family);
            window_settings.font.font_family =  sensors.value[1].style.fontFamily;
            // window.localStorage.setItem(`sensorSettings_${activeId.value}`, JSON.stringify( sensors.value))
        }
    },500)
    const monitor_data = defalutDatas.find((item) => {
        return item.id === activeId.value
    })
    if (monitor_data) {
        Object.assign(window_settings, monitor_data.window_settings)
    }
}
function checkLocal() {
    const localMonitorId = window.localStorage.getItem("activeMonitorId")
    if (localMonitorId) {
        activeId.value = Number(localMonitorId)
    } else {
        activeId.value = 1;
    }
}

function processingData() {
    const monitor_data = defalutDatas.find((item) => {
        return item.id === activeId.value
    })
    if (monitor_data) {
        Object.assign(window_settings, monitor_data.window_settings)
    }
    idb.getItem(`custom_monitor_window_setting_${activeId.value}`).then((res: any[]) => {
        console.log(res)
        if (res) {
            Object.assign(window_settings, res)
        }
        data.screen_list = (gamepp.hardware.getDisplayCardInfo.sync())?.Element ?? [];
        if (window_settings.position.screen === "") {
            for (const d of data.screen_list) {
                if (d.IsPrimaryScreen === 1) {
                    window_settings.position.screen = d.MonitorModal
                    break;
                }
            }
        }
    })
}
</script>

<style scoped lang="scss">
.hover:hover{
  filter: brightness(1.2);
  -webkit-filter: brightness(1.2);
  -webkit-tap-highlight-color: transparent;
}
.selected{background: #354054;}
.dragging{background: #354054;}
.Extrawidth{
    width: 188px!important;
}
.highlighted{ border: 1px dashed #7389B2!important; will-change: border;}
// @font-face{
//     font-family: 'QuartzRegular';
//     src:url('../../../static/font/QuartzRegular.ttf');
// }
.scroll::-webkit-scrollbar {
    width: 5px;
    transition: 0.25s;
}
.scroll::-webkit-scrollbar-thumb {
    background: #71738C;
    border-radius: 3px;
}
.scroll::-webkit-scrollbar-thumb:hover {
    background-color: #71738C;
}
.scroll::-webkit-scrollbar-track {
    background-color: #2D2E39;
    width: 2px;
}
.monitor-set {
    display: grid;
    grid-template-columns: 516px 735px;
    grid-template-rows: 600px;
    grid-column-gap: 10px;
    margin-top: 5px;
    .left-section{
        padding: 10px;
        box-sizing: border-box;
        .title{
            font-size: 12px;
            color: #FFFFFF;
            padding: 0 0 10px 0;
        }
        .screen_content{
            width: 494px;
            height: 550px;
            border-radius: 4px;
            background: #22232E;
            box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
            .list-title{
                width: 100%;
                display: flex;
                align-items: center;
                gap: 2px;
                .titledes{
                    width: 100px;
                    height: 30px;
                    border-radius: 4px;
                    background: #343647;
                    font-size: 12px;
                    color: #fff;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }
            .add-monitor{
                width: 100%;
                border-radius: 4px;
                cursor: pointer;
                height: 30px;
                border: 1px dashed #505050;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-top: 5px;
                .add-monitor-text{
                    font-size: 12px;
                    color: #999;
                    margin-left: 10px;
                }
            }
            .custom-icon{
                font-size: 16px;
            }
            .custom-icon::before {
                color: #999999;
            }


            .Sensor_data_list{
                width: 100%;
                margin-top: 2px;
                overflow-y: auto;
                overflow-x: hidden;
                height: 480px;
                position: relative;
                .custom-icon{
                font-size: 13px;
                padding: 8px 1px 8px 1px;
                &:hover{
                    color: #409EFF;
                }
                }
                .custom-icon-rotate{
                    padding: 1px 8px 1px 8px;
                }
                }
                li{
                    width: 100%;
                    height: 32px;
                    display: flex;
                    // cursor: pointer;
                    border: 1px solid transparent;
                    transition: transform 0.3s ease, box-shadow 0.3s ease;
                    cursor: move;
                    // &:hover{
                    //     border: 1px dashed #7389B2;
                    // }
                    .wide{
                        width: 100px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 12px;
                        color: #fff;
                    }
                    .operation{
                        gap: 10px;
                        i{
                        cursor: pointer;
                        }
                    }
            }
        }
    }
    .left-section, .right-section {
        width: 100%;
        height: 600px;
        background: #2B2C37;
        box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
        border-radius: 4px;
    }

    .right-section {
        padding: 20px;
        .window-settings {
            display: flex;
            flex-flow: column nowrap;

            .settings-title {
                display: flex;
                justify-content: space-between;
                align-items: center;
                height: 32px;
                line-height: 32px;
                color: #409EFF;
                font-size: 14px;
                margin-bottom: 10px;
            }

            .settings-content {
                display: flex;
                flex-flow: row nowrap;
                --el-fill-color-blank: #3E4050;
                --el-border-color: #3E4050;
                --el-border-color-hover: #3E4050;
                --el-color-primary: #3E4050;

                :deep(.el-input__wrapper) {
                    background: #22232E;
                    box-shadow: 0 0 0 1px #22232E inset;
                }

                :deep(.el-radio) {
                    --el-radio-input-bg-color: #fff;
                }

                :deep(.el-radio__input.is-checked .el-radio__inner) {
                    background: #409EFF;
                }

                :deep(.el-radio__input.is-checked+.el-radio__label) {
                    color: #fff;
                    font-size: 12px;
                }

                :deep(.el-slider__bar) {
                    background-color: #409EFF;
                    z-index: 1;
                }

                :deep(.el-radio__label) {
                    font-size: 12px;
                }

                :deep(.el-checkbox__label) {
                    color: #fff;
                    font-size: 12px;
                    max-width: 358px;
                    white-space: normal;
                }

                :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                    background-color: #409EFF;
                    border-color: #409EFF;
                }

                section {
                    p.tit {
                        color: #999999;
                        margin-bottom: 10px;
                    }

                    .input-box, .input-box-file {
                        width: 165px;
                        margin-bottom: 7px;
                        display: flex;
                        align-items: center;
                        gap: 9px;
                    }

                    .input-box-file {
                        width: 320px;
                        margin-bottom: 13px;
                    }

                    .set-position {
                        display: flex;
                        flex-flow: row nowrap;
                        gap: 10px;
                        margin-bottom: 20px;

                        .nineSquareGrid {
                            display: grid;
                            grid-template-columns: repeat(3, 1fr);
                            grid-template-rows: repeat(3, 1fr);
                            gap: 1px;
                            width: 90px;
                            height: 90px;
                            border-radius: 4px;
                            --nineSquareGrid-border-color: #409EFF;
                            border: 1px solid var(--nineSquareGrid-border-color);
                            background: var(--nineSquareGrid-border-color);
                            cursor: pointer;
                            overflow: hidden;

                            & > div {
                                background-color: #2B2C37;
                                &:hover {
                                    padding: 1.5px;
                                    box-sizing: border-box;
                                    background: #1c4468;
                                }
                                &.active {
                                    &::after {
                                        content: "";
                                        background-color: #409EFF;
                                        width: 25px;
                                        height: 25px;
                                        border-radius: 2px;
                                        margin: 1.5px;
                                        display: block;
                                    }
                                }
                            }
                        }
                    }

                    .set-drag {
                        display: flex;
                        margin-bottom: 5px;
                        align-items: center;
                        gap: 10px;
                        .maxspan{
                            max-width: 300px;
                        }
                        .lock {
                            width: 20px;
                            height: 20px;
                            cursor: pointer;
                            background: url("../assets/img/lock.png") no-repeat;

                            &:hover {
                                background: url("../assets/img/lock_hover.png") no-repeat;
                            }
                        }

                        .unlock {
                            width: 20px;
                            height: 20px;
                            cursor: pointer;
                            background: url("../assets/img/unlock.png") no-repeat;

                            &:hover {
                                background: url("../assets/img/unlock_hover.png") no-repeat;
                            }
                        }
                    }
                }
            }
        }
        .oled_itme{
            margin-top: 5px;
        }
    }

    .Settings{
      width: 735px;
      height: 600px;
      border-radius: 4px;
      background: #2B2C37;
      box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
      padding: 20px 20px 10px 20px;
      box-sizing: border-box;
      overflow: auto;
      .goback{
        display: inline-flex;
        align-items: center;
        color: #409EFF;
        font-size: 12px;
        cursor: pointer;
        // margin: 15px 0 0 10px;
      }
      .Set_item{
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 20px;
        p{
          font-size: 12px;
          color: #999999;
        }
        .itemdata{
          display: flex;
          align-items: center;
          gap: 20px;
          .displacement{
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #fff;
            .Colorbox{
              margin-right: 10px;
            }
             span{
              margin-right: 10px;
             }
             strong{
              margin-left: 5px;
              font-weight: normal;
             }
            .Inputcontent{
              width: 70px;
              height: 28px;
              border-radius: 4px;
              background: #171B20;
              border: none;
            }
            .single-des{
              width: 194px;
              height: 28px;
              border-radius: 4px;
            }
          }
        }
      }
    }
    .Set_item2{
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 20px;
    }
    .Inputcontent2{
        width: 70px;
        height: 28px;
        border-radius: 4px;
        background: #171B20;
        border: none;
    }
    .datatime{
        margin-bottom: 15px;
        p{
        font-size: 12px;
        color: #999999;
        }
        .itemdata{
            margin-top: 10px;
            .el-button{
                font-size: 12px;
                background: #354054;
                border: none;
                color: #fff;
                &:hover{
                background: #566B92!important;;
                }
            }
            .active {
                background-color: #566B92;
                color: #fff;
            }
        }
    }
}

.flex-items-center {
    align-items: center;
    display: flex;
}
</style>
<style lang="scss">
.custom_tooltip{
    background: #343647!important;
    border: 1px solid #777777!important;
    color: #ffffff !important;
}
.custom_tooltip  .el-popper__arrow:before{
  background: #343647!important;
  border: 1px solid #777777!important;
}
.monitor-set{
    .displacement {
        .el-input__inner {
            background: #3E4050;
            padding: 0 10px;
            border-radius: 4px;
            color: #fff;
            text-align: center;
        }
        .el-checkbox__label{
            color: #fff;
        }
        .el-checkbox__inner{
            border: 1px solid #fff;
        }
        .el-checkbox__input.is-checked .el-checkbox__inner{
            border: 1px solid #409eff;
        }
        .single-des{
            .el-input__inner {
                background: #171b20;
            }
        }
    }
    .Inputsize{
        .el-input__inner {
            width: 100px;
            height: 28px;
            border-radius: 4px;
            background: #171b20;
            border: none;
            color: #fff;
            text-align: center;
        }
    }
    .Colorbox{
        .el-input__inner{
            border-radius: 4px;
        }
    }
    .Inputcontent,.Inputcontent2 {
        .el-input__inner {
            width: 70px;
            height: 28px;
            border-radius: 4px;
            background: #171b20;
            border: none;
            color: #fff;
            text-align: center;
        }
    }
    .Colorbox2{
        width: 190px;
        .el-input__inner{
            background: #3e4050;
            padding: 0 10px;
            border-radius: 4px;
            color: #fff;
            text-align: center;    
        }
      
    }
    .el-input__wrapper{
        padding: 0;
    }
    .font_box{
        .el-select__wrapper{
            box-shadow: none!important;
            background-color: #3E4050!important;
        }
        .el-select__selection{
            width: 150px;
            background: #354050 !important;
        }
        .el-select__placeholder{
            color: #fff!important;
        }
    }
    .el-button:hover {
        background-color: #566b92 !important;
        color: #fff !important;
    }
   .el-button {
        width: 200px;
        height: 28px;
        border-radius: 4px;
        background: #3E4050;
        color: #fff;
        padding: 0;
        font-weight: 400;
        border: none;
    }
    .datatime{
        .el-button {
            padding: 12px 20px;
            width: auto;
        }
    }
    .picturevideo{
        .el-button {
            width: auto;
            padding: 0 20px;
        }
    }
    .Settingbutton {
        width: auto;
        padding: 0 20px;
        color: #fff;
        background: #3579D5;
        &:hover {
            background: #4a87d9 !important;
        }
    }
}
</style>
