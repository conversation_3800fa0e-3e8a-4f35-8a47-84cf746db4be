const id = {
  "update": {
    "Game": "Game",
    "PP": "PP",
    "upgradingInProgress": "Sedang diperbarui",
    "theModuleIsBeingUpdated": "Mengupdate modul",
    "dataIsBeingUpdated": "Mengupdate data...",
    "checkingUpdate": "Memeriksa pembaruan",
    "checkingUpgrade": "Memeriksa pembaruan",
    "loadingProgramComponent": "Memuat komponen program...",
    "loadingHotkeyModules": "Memuat komponen pintasan",
    "loadingGPPModules": "Mengimpor komponen GamePP",
    "loadingBlackWhiteList": "Memuat daftar hitam dan putih",
    "loadingGameSetting": "Memuat parameter pengaturan game...",
    "loadingUserAbout": "Memuat data otentikasi pengguna",
    "loadingGameBenchmark": "Memuat skor permainan",
    "loadingHardwareInfo": "Memuat komponen informasi perangkat keras",
    "loadingDBModules": "Memuat modul database...",
    "loadingIGCModules": "Memuat modul IGC",
    "loadingFTPModules": "Modul dukungan FTP sedang dimuat",
    "loadingDialogModules": "Memuat modul kotak dialog",
    "loadingDataStatisticsModules": "Modul statistik sedang dimuat",
    "loadingSysModules": "Memuat komponen sistem",
    "loadingGameOptimization": "Memuat optimasi game",
    "loadingGameAcceleration": "Memuat percepatan game",
    "loadingScreenshot": "Memuat tangkapan layar rekaman",
    "loadingVideoComponent": "Memuat komponen kompresi video",
    "loadingFileFix": "Memuat perbaikan file",
    "loadingGameAI": "Memuat kualitas AI game",
    "loadingNVAPIModules": "Memuat modul NVAPI",
    "loadingAMDADLModules": "Memuat modul AMDADL",
    "loadingModules": "Modul sedang dimuat"
  },
  "messages": {
    "append": "Tambahkan",
    "confirm": "Konfirmasi",
    "cancel": "Batal",
    "default": "Bawaan",
    "quickSelect": "Pemilihan Cepat",
    "onoffingame": "Aktifkan/matikan pemantauan dalam game:",
    "changeKey": "Klik untuk mengubah shortcut keyboard",
    "clear": "Hapus",
    "hotkeyOccupied": "Kombinasi tombol sudah digunakan. Silakan tetapkan yang baru！",
    "minimize": "Kecilkan",
    "exit": "Keluar",
    "export": "Ekspor",
    "import": "Impor",
    "screenshot": "Tangkapan layar",
    "showHideWindow": "Tampilkan/Sembunyikan Jendela",
    "ingameControlPanel": "Panel kontrol dalam game",
    "openOrCloseGameInSettings": "Beralih ke panel pengaturan dalam game",
    "openOrCloseGameInSettings2": "Tekan tombol pintas ini untuk mengaktifkannya",
    "openOrCloseGameInSettings3": "Aktifkan/Nonaktifkan pemantauan dalam permainan",
    "openOrCloseGameInSettings4": "Aktifkan/Nonaktifkan Filter Game",
    "startManualRecord": "Mulai/Hentikan Perekaman Statistik Manual",
    "performanceStatisticsMark": "Penanda Statistik Kinerja",
    "EnableAIfilter": "Filter AI memerlukan tombol pintas ini ditekan untuk diaktifkan",
    "Start_stop": "Mulai/Hentikan Rekaman Statistik Manual",
    "pressureTest": "Tes stres",
    "moduleNotInstalled": "Modul fungsional belum terinstal",
    "installingPressureTest": "Menginstal modul uji tekan...",
    "importFailed": "Impor gagal",
    "gamepp": "GamePP",
    "copyToClipboard": "Sudah disalin ke papan klip"
  },
  "home": {
    "homeTitle": "Beranda",
    "hardwareInfo": "Informasi perangkat keras",
    "functionIntroduction": "Fitur",
    "fixedToNav": "Lampirkan ke bilah navigasi",
    "cancelFixedToNav": "Lepaskan dari bilah navigasi",
    "hardwareInfoLoading": "Memuat informasi perangkat keras...",
    "performanceStatistics": "Statistik Kinerja",
    "updateNow": "Perbarui sekarang",
    "recentRun": "Aktivitas Terbaru",
    "resolution": "Resolusi:",
    "duration": "Durasi:",
    "gameFilter": "Filter game",
    "gameFilterHasAccompany": "Filter game telah diaktifkan",
    "gameFilterHasAccompany2": "Pengguna bermain game seperti Cyberpunk, APEX, dan Hogwarts Legacy.",
    "currentList": "Item yang dipantau dalam daftar saat ini",
    "moreFunction": "Benchmark, pengujian stres, pemantauan desktop, dan fitur lainnya sedang dalam pengembangan.",
    "newVersion": "Versi baru tersedia !",
    "discoverUpdate": "Pembaruan ditemukan！",
    "downloading": "Mengunduh",
    "retry": "Coba lagi",
    "erhaAI": "2HaAI",
    "recordingmodule": "Fitur ini bergantung pada modul perekaman",
    "superPower": "Mode Ultra",
    "autoRecord": "Rekam otomatis momen pembunuhan di dalam game dan mudah menyimpan klip sorotan",
    "externalDevice": "Pencahayaan Dinamis Perangkat",
    "linkage": "Picu scene pembunuhan dalam game dan tampilkan melalui perangkat eksternal yang terhubung",
    "AI": "Uji Performa AI",
    "test": "Uji model AI menggunakan GPU dan lihat skor kinerja AI GPU",
    "supportedGames": "Permainan yang Didukung",
    "games": "Naraka: Bladepoint, Delta Force, FragPunk, League of Legends, PUBG, Valorant, The Finals, APEX Legends, COD20",
    "videoRecording": "Rekaman video",
    "videoRecording2": "Fungsi perekaman video berbasis OBS, mendukung penyetelan bitrate dan kecepatan frame (FPS) untuk memenuhi berbagai persyaratan kualitas dan kehalusan; juga mendukung \"Replay Instan\", tekan tombol pintas untuk menyimpan highlight kapan saja!",
    "addOne": "Dapatkan secara gratis",
    "gamePlatform": "Platform permainan",
    "goShop": "Pergi ke Halaman Toko",
    "receiveDeadline": "Batas Waktu Klaim Setelah Aksi",
    "2Ai": "2 Tertawa AI",
    "questionDesc": "Deskripsi masalah",
    "inputYourQuestion": "Silakan masukkan saran atau komentar yang ingin Anda berikan di sini。",
    "uploadLimit": "Unggah hingga 3 gambar lokal dalam format JPG/PNG/BMP",
    "email": "Surel",
    "contactWay": "Informasi kontak",
    "qqNumber": "Nomor QQ (opsional)",
    "submit": "Kirim"
  },
  "hardwareInfo": {
    "hardwareOverview": "Gambaran Umum Perangkat Keras",
    "copyAllHardwareInfo": "Salin semua informasi perangkat keras",
    "processor": "Prosesor",
    "coreCount": "Inti :",
    "threadCount": "Jumlah thread:",
    "currentFrequency": "Frekuensi Saat Ini:",
    "currentVoltage": "Tegangan saat ini:",
    "copy": "Salin",
    "releaseDate": "Tanggal rilis",
    "codeName": "Nama Kode",
    "thermalDesignPower": "Daya Panas Desain",
    "maxTemperature": "Suhu maksimum",
    "graphicsCard": "Kartu grafis",
    "brand": "Merek:",
    "streamProcessors": "Prosesor Streaming:",
    "Videomemory": "Memori video：",
    "busSpeed": "Kecepatan Bus",
    "driverInfo": "Informasi Driver",
    "driverInstallDate": "Tanggal Instalasi Driver",
    "hardwareID": "ID perangkat keras",
    "motherboard": "Papan induk",
    "chipGroup": "Chipset:",
    "BIOSDate": "Tanggal BIOS",
    "BIOSVersion": "Versi BIOS",
    "PCIESlots": "slot PCIe",
    "PCIEVersion": "Versi PCIe yang Didukung",
    "memory": "Memori",
    "memoryBarCount": "Jumlah:",
    "totalSize": "Ukuran:",
    "channelCount": "Saluran:",
    "Specificmodel": "Model spesifik",
    "Pellet": "Generator Partikel",
    "memoryBarEquivalentFrequency": "Frekuensi Memori Efektif:",
    "hardDisk": "Hard disk",
    "hardDiskCount": "Jumlah hard drive:",
    "actualCapacity": "Kapasitas Aktual",
    "type": "Tipe",
    "powerOnTime": "Waktu Menyala",
    "powerOnCount": "Siklus Energi",
    "SSDRemainingLife": "Umur pakai tersisa SSD",
    "partitionInfo": "Informasi Partisi",
    "hardDiskController": "Kontroler hard disk",
    "driverNumber": "Nomor drive",
    "display": "Tampilan",
    "refreshRate": "Tingkat penyegaran:",
    "screenSize": "Ukuran layar:",
    "inches": "inci",
    "productionDate": "Tanggal produksi",
    "supportRefreshRate": "Dukungan Tingkat Perbaruan",
    "screenLongAndShort": "Ukuran Layar",
    "systemInfo": "Informasi Sistem",
    "version": "Versi",
    "systemInstallDate": "Tanggal Instalasi Sistem",
    "systemBootTime": "Waktu Boot Saat Ini",
    "systemRunTime": "Waktu eksekusi",
    "Poccupied": "Penggunaan P",
    "Eoccupied": "E Sedang Digunakan",
    "occupied": "Terpakai",
    "temperature": "Suhu",
    "Pfrequency": "Frekuensi Prosesor",
    "Efrequency": "Frekuensi E",
    "thermalPower": "Energi termal",
    "frequency": "Frekuensi",
    "current": "Saat ini",
    "noData": "Tidak ada data",
    "loadHwinfo_SDK": "Gagal memuat Hwinfo_SDK.dll, tidak dapat membaca data perangkat keras/sensor.",
    "loadHwinfo_SDK_reason": "Penyebab mungkin dari masalah ini:",
    "reason": "Alasan",
    "BlockIntercept": "Diblokir oleh perangkat lunak antivirus, contoh: 2345 Antivirus Software (Proses 2345 Active Defense, Proses Active Defense McAfee)",
    "solution": "Solusi:",
    "solution1": "Setelah menutup dan menghapus proses terkait, mulai ulang GamePP",
    "solution2": "Setelah memutuskan perangkat yang terhubung, mulai ulang GamePP",
    "RestartGamePP": "Lepaskan kontroler, tunggu respons dari perangkat, lalu restart GamePP",
    "HWINFOcannotrun": "Hwinfo tidak dapat dijalankan dengan benar",
    "downloadHWINFO": "Unduh Hwinfo",
    "openHWINFO": "Setelah meluncurkan Hwinfo, apakah mengklik RUN dapat memulai program secara normal?",
    "hardwareDriverProblem": "Masalah driver perangkat keras",
    "checkHardwareManager": "Periksa manajer perangkat keras untuk memastikan driver motherboard dan kartu grafis terinstal dengan benar",
    "systemProblem": "Masalah sistem, misalnya: Menggunakan alat aktivasi seperti Baofeng atau Xiaoma dapat menyebabkan driver gagal dimuat, dan patch sistem Windows 7 tidak dapat diinstal secara otomatis",
    "reinstallSystem": "Pasang ulang sistem untuk mengaktifkan. Unduh dan pasang Windows 7: perbaikan *********",
    "Windows7": "Windows 7: Instalasi Patch SHA-256, Windows 10: Aktifkan Menggunakan Sertifikat Digital, Windows 11 Versi Pratinjau: Nonaktifkan Memory Integrity",
    "ViolenceActivator": "Jika Anda menggunakan alat aktivasi brute-force seperti Xiaoma, tolong perbaiki atau instal ulang sistem",
    "MultipleGraphicsCardDrivers": "Pengemudi kartu grafis dari berbagai merek terinstal di komputer, misalnya driver AMD dan Nvidia terinstal sekaligus.",
    "UninstallUnused": "Mulai ulang komputer setelah menghapus driver grafis yang tidak diperlukan",
    "OfficialQgroup": "Tidak ada dari alasan di atas yang berlaku. Silakan bergabung ke grup QQ resmi kami: 908287288 (Grup 5) untuk penyelesaian.",
    "ExportHardwareData": "Ekspor Data Hardware",
    "D3D": "Penggunaan D3D",
    "Total": "Penggunaan total",
    "VRAM": "Penggunaan VRAM",
    "VRAMFrequency": "Frekuensi VRAM",
    "SensorData": "Data sensor",
    "CannotGetSensorData": "Gagal mengambil data sensor",
    "LoadingHardwareInfo": "Memuat informasi perangkat keras…",
    "ScanTime": "Skan terakhir:",
    "Rescan": "Sken ulang",
    "Screenshot": "Tangkapan layar",
    "configCopyed": "Informasi konfigurasi telah disalin ke papan klip.",
    "LegalRisks": "Risiko hukum potensial terdeteksi",
    "brandLegalRisks": "Penggunaan tampilan merek dapat melibatkan risiko hukum potensial",
    "professionalVersion": "Edisi Profesional",
    "professionalWorkstationVersion": "Edisi Workstation Profesional",
    "familyEdition": "Versi Rumah",
    "educationEdition": "Versi Pendidikan",
    "enterpriseEdition": "Edisi Perusahaan",
    "flagshipEdition": "Edisi Premium",
    "familyPremiumEdition": "Edisi Premium Keluarga",
    "familyStandardEdition": "Versi Standar Rumah Tangga",
    "primaryVersion": "Versi Dasar",
    "bit": "bit",
    "tempWall": "Dinding suhu",
    "error": "Kesalahan",
    "screenshotSuccess": "Tangkapan layar berhasil disimpan",
    "atLeastOneData": "Setidaknya 1 entri data harus dipertahankan",
    "atMostSixData": "Tambahkan maksimal 6 entri data",
    "screenNotActivated": "Belum diaktifkan"
  },
  "psc": {
    "processCoreAssign": "Penugasan inti proses",
    "CoreAssign": "Alokasi inti：",
    "groupName": "Nama grup:",
    "notGameProcess": "Proses Non-Game",
    "unNamedProcess": "Grup Tidak Berjudul",
    "Group2": "Grup",
    "selectTheCore": "Pilih inti",
    "controls": "Operasi",
    "tips": "Prompt",
    "search": "Cari",
    "shiftOut": "Kembali",
    "ppValue": "Nilai PP",
    "ppDesc": "Nilai PP mencerminkan konsumsi sumber daya perangkat keras sebelumnya. Nilai yang lebih tinggi menunjukkan penggunaan sumber daya perangkat keras yang lebih besar.",
    "littletips": "Petunjuk: Tahan dan seret proses ke grup di sisi kiri。",
    "warning1": "Memilih thread inti di antara grup mungkin mempengaruhi kinerja. Disarankan untuk menggunakan inti dari grup yang sama.",
    "warning2": "Apakah Anda yakin ingin membiarkan nama grup kosong?",
    "warning3": "Efek alokasi inti akan menjadi tidak berlaku setelah dihapus. Apakah Anda yakin ingin menghapus grup ini?",
    "allprocess": "Semua proses",
    "pleaseCheckProcess": "Silakan pilih proses",
    "dataSaveDesktop": "Data telah disimpan ke desktop.",
    "createAGroup": "Buat grup",
    "delGroup": "Hapus grup",
    "Group": "Grup",
    "editGroup": "Edit Grup",
    "groupinfo": "Informasi Kelompok",
    "moveOutGrouping": "Hapus dari grup",
    "createANewGroup": "Buat Grup Baru",
    "unallocatedCore": "Inti yang belum ditetapkan",
    "inactiveProcess": "Proses tidak aktif",
    "importGroupingScheme": "Impor Profil Pengelompokan",
    "derivedPacketScheme": "Ekspor Konfigurasi Kelompok",
    "addNowProcess": "Tambahkan proses yang sedang berjalan",
    "displaySystemProcess": "Tampilkan proses sistem",
    "max64": "Pemilihan maksimum adalah 64 thread",
    "processName": "Nama Proses",
    "chooseCurProcess": "Pilih proses saat ini",
    "selectNoProcess": "Tidak ada proses yang dipilih",
    "coreCount": "Inti",
    "threadCount": "Threads",
    "process": "Proses",
    "plzInputProcessName": "Masukkan nama proses untuk ditambahkan secara manual",
    "has_allocation": "Proses dengan Skema Alokasi Thread",
    "not_made": "Anda belum menetapkan proses apa pun ke inti",
    "startUse": "Aktifkan optimisasi",
    "stopUse": "Nonaktifkan optimisasi",
    "threadAllocation": "Alokasi Thread",
    "configProcess": "Konfigurasi Proses",
    "selectThread": "Pilih thread",
    "hyperthreadingState": "Status Hyper-Threading",
    "open": "Diaktifkan",
    "notYetUnlocked": "Dinonaktifkan",
    "nonhyperthreading": "Tanpa Hyper-Threading",
    "intervalSelection": "Pemilihan Interval",
    "invertSelection": "Balikkan Pemilihan",
    "description": "Kunci proses game ke inti CPU yang ditentukan untuk operasi, secara cerdas mengisolasi gangguan dari program latar belakang. Efektif meningkatkan batas atas FPS dan menstabilkan FPS saat bermain game! Mengurangi lag tiba-tiba dan penurunan frame rate, memanfaatkan performa penuh prosesor multi-core untuk memastikan frame rate tinggi dan stabil sepanjang permainan!",
    "importSuccess": "Impor berhasil",
    "importFailed": "Impor gagal"
  },
  "InGameMonitor": {
    "onoffingame": "Aktifkan/Nonaktifkan Pemantauan di Dalam Game:",
    "InGameMonitor": "Pemantauan dalam Game",
    "CustomMode": "Mode Khusus",
    "Developing": "Dalam pengembangan...",
    "NewMonitor": "Tambahkan Item Pemantauan",
    "Data": "Parameter",
    "Des": "Catatan",
    "Function": "Fungsi",
    "Editor": "Edit",
    "Top": "Tetapkan di bagian atas",
    "Delete": "Hapus",
    "Use": "Menggunakan",
    "DragToSet": "Setelah memanggil panel, Anda dapat menyeret untuk mengatur",
    "MonitorItem": "Item Pemantauan",
    "addMonitorItem": "Tambahkan Item Pantau",
    "hide": "Sembunyikan",
    "show": "Tampilkan",
    "generalstyle": "Pengaturan Umum",
    "restoredefault": "Kembalikan Pengaturan Bawaan",
    "arrangement": "Tata Letak",
    "horizontal": "Horizontal",
    "vertical": "Vertikal",
    "monitorposition": "Lokasi Pemantauan",
    "canquickselectposition": "Pilih dengan cepat lokasi pada peta di sebelah kiri。",
    "curposition": "Lokasi saat ini:",
    "background": "Latar Belakang",
    "backgroundcolor": "Warna latar belakang:",
    "font": "Font",
    "fontStyle": "Gaya font",
    "fontsize": "Ukuran Font：",
    "fontcolor": "Warna huruf:",
    "style": "Gaya:",
    "style2": "Gaya",
    "performance": "Kinerja",
    "refreshTime": "Waktu pembaruan:",
    "goGeneralSetting": "Pergi ke Pengaturan Umum",
    "selectMonitorItem": "Pilih Item Pemantauan",
    "selectedSensor": "Sensor yang dipilih:",
    "showTitle": "Tampilkan Judul",
    "hideTitle": "Sembunyikan Judul",
    "showStyle": "Mode Tampilan:",
    "remarkSize": "Ukuran catatan:",
    "remarkColor": "Warna catatan:",
    "parameterSize": "Ukuran parameter :",
    "parameterColor": "Warna Parameter :",
    "lineChart": "Grafik Garis",
    "lineColor": "Warna Garis:",
    "lineThickness": "Ketebalan Garis：",
    "areaHeight": "Tinggi Wilayah:",
    "sort": "Urutkan",
    "displacement": "Perpindahan:",
    "up": "Pindahkan ke Atas",
    "down": "Pindah ke Bawah",
    "bold": "Tebal",
    "stroke": "Outline",
    "text": "Teks",
    "textLine": "Teks + Grafik Garis",
    "custom": "Disesuaikan",
    "upperLeft": "Bagian kiri atas",
    "upper": "Tengah-Atas",
    "upperRight": "Kanan Atas",
    "Left": "Tengah Kiri",
    "middle": "Pusat",
    "Right": "Tengah Kanan",
    "lowerLeft": "Kiri bawah",
    "lower": "Menengah-Rendah",
    "lowerRight": "Bagian bawah kanan",
    "notSupport": "Perangkat peripheral tidak mendukung menampilkan/menyembunyikan melalui klik mouse",
    "notSupportRate": "Tingkat kembali tidak dapat diaktifkan/mati dengan mengklik",
    "notFindSensor": "Sensor tidak ditemukan. Klik untuk memodifikasi。",
    "monitoring": "Pemantauan",
    "condition": "Kondisi",
    "bigger": "Lebih dari",
    "smaller": "Kurang dari",
    "biggerThan": "Melebihi ambang batas",
    "biggerThanthreshold": "Lebih besar dari persentase ambang batas",
    "smallerThan": "Di bawah ambang batas",
    "smallerThanthreshold": "Lebih sedikit dari persentase ambang batas",
    "biggerPercent": "Penurunan Persentase Nilai Saat Ini",
    "smallerPercent": "Persentase Kenaikan Nilai Saat Ini",
    "replay": "Fungsi Putar Ulang Instan",
    "screenshot": "Fungsi tangkapan layar",
    "text1": "Saat nilai sensor",
    "text2": " dan di",
    "text3": "Waktu tunggu",
    "text4": "Jika tidak ada nilai yang lebih tinggi muncul dalam detik yang ditentukan, segera diaktifkan",
    "text5": "Setelah setiap trigger ulang, perbarui ambang batas ke nilai saat trigger untuk mengurangi aktivasi yang sering",
    "text6": "Menampilkan ambang batas saat ini yang digunakan untuk memicu pemutaran ulang",
    "text7": "Tampilkan nilai sensor",
    "text8": "Melebihi ambang batas awal",
    "text9": "Di Bawah Ambang Batas Awal",
    "text10": "Jumlah Ambang Batas Awal",
    "initThreshold": "Ambang Batas Awal",
    "curThreshold": "Batas Saat Ini:",
    "curThreshold2": "Batas saat ini",
    "resetCurThreshold": "Atur ulang ambang batas saat ini",
    "action": "Aktifkan Fitur",
    "times": "Kali",
    "percentage": "Persen",
    "uninstallobs": "Modul rekaman belum diunduh",
    "install": "Unduh",
    "performanceAndAudioMode": "Mode kompatibilitas kinerja dan audio",
    "isSaving": "Menyimpan",
    "video_replay": "Replay Langsung",
    "saved": "Sudah disimpan",
    "loadQualitysScheme": "Muat preset grafis",
    "notSet": "Belum dikonfigurasi",
    "mirrorEnable": "Filter telah diaktifkan",
    "canBeTurnedOff": "Kembali",
    "mirrorClosed": "Filter game telah dinonaktifkan",
    "closed": "Telah ditutup",
    "openMirror": "Aktifkan filter",
    "wonderfulScenes": "Momen Terbaik",
    "VulkanModeHaveProblem": "Mode Vulkan memiliki masalah kompatibilitas",
    "suggestDxMode": "Disarankan untuk beralih ke mode Dx",
    "functionNotSupported": "Fitur tidak didukung",
    "NotSupported": "Tidak didukung",
    "gppManualRecording": "GamePP, perekaman manual",
    "perfRecordsHaveBeenSaved": "Data kinerja telah disimpan",
    "redoClickF8": "Tekan F8 kembali untuk melanjutkan perekaman.",
    "startIngameMonitor": "Mengaktifkan fungsi pemantauan dalam game",
    "inGameMarkSuccess": "Penandatanganan dalam game berhasil dilakukan",
    "recordingFailed": "Perekaman gagal",
    "recordingHasNotDownload": "Fungsi rekaman belum diunduh",
    "hotkeyDetected": "Konflik hotkey fungsi terdeteksi",
    "plzEditIt": "Harap lakukan perubahan di dalam perangkat lunak sebelum menggunakannya",
    "onePercentLowFrame": "1% Rendah frame",
    "pointOnePercentLowFrame": "0.1% FPS Rendah",
    "frameGenerationTime": "Waktu pembuatan frame",
    "curTime": "Waktu saat ini",
    "runTime": "Durasi eksekusi",
    "cpuTemp": "Suhu CPU",
    "cpuUsage": "Penggunaan CPU",
    "cpuFreq": "Frekuensi CPU",
    "cpuPower": "Daya Panas CPU",
    "gpuTemp": "Suhu GPU",
    "gpuUsage": "Penggunaan GPU",
    "gpuPower": "Daya Termal GPU",
    "gpuFreq": "Frekuensi GPU",
    "memUsage": "Penggunaan memori"
  },
  "LoginArea": {
    "login": "Masuk",
    "loginOut": "Keluar",
    "vipExpire": "Kadaluarsa",
    "remaining": "Sisa",
    "day": "Langit",
    "openVip": "Aktifkan Anggota",
    "vipPrivileges": "Keuntungan Anggota",
    "rechargeRenewal": "Isi Ulang & Perbarui",
    "Exclusivefilter": "Filter",
    "configCloudSync": "Konfigurasikan Sinkronisasi Awan",
    "comingSoon": "Segera Tersedia"
  },
  "GameMirror": {
    "filterStatus": "Status filter",
    "filterPlan": "Preset Filter",
    "filterShortcut": "Saringan Jalan Cepat",
    "openCloseFilter": "Aktifkan/Nonaktifkan Filter:",
    "effectDemo": "Demo Efek",
    "demoConfig": "Pengaturan Demo",
    "AiFilter": "Efek filter AI tergantung pada efek in-game",
    "AiFilterFAQ": "Masalah Umum dengan Filter AI",
    "gamePPAiFilter": "Filter AI GamePP",
    "gamePPAiFilterVip": "Filter AI eksklusif VIP GamePP secara dinamis menyesuaikan parameter filter berdasarkan skenario bermain game untuk mengoptimalkan efek visual dan meningkatkan pengalaman bermain game.",
    "AiMingliangTips": "Kecerahan AI: Direkomendasikan untuk digunakan ketika layar game terlalu gelap.",
    "AiBrightTips": "AI Vibrant: Disarankan untuk digunakan ketika tampilan game terlihat terlalu gelap.",
    "AiDarkTips": "AI Dimming: Direkomendasikan untuk digunakan saat visual game terlalu mencolok",
    "AiBalanceTips": "Keseimbangan AI: Cocok untuk sebagian besar skenario permainan.",
    "AiTips": "Tips: Filter AI diaktifkan dengan menekan tombol pintasan dalam game.",
    "AiFilterUse": "Silakan gunakan dalam game",
    "AiFilterAdjust": "Penyetelan Tombol Pintas untuk Filter AI",
    "Bright": "Cerah",
    "Soft": "Perangkat lunak",
    "Highlight": "Tampilkan Utama",
    "Film": "Video",
    "Benq": "BenQ",
    "AntiGlare": "Anti-Silau",
    "HighSaturation": "Kejenuhan tinggi",
    "Brightness": "Vivid",
    "Day": "Hari",
    "Night": "Malam",
    "Nature": "Alami",
    "smooth": "Halus",
    "elegant": "Sederhana",
    "warm": "Nuansa Hangat",
    "clear": "Hapus",
    "sharp": "Ketajaman",
    "vivid": "Dinamis",
    "beauty": "Cerah",
    "highDefinition": "HD",
    "AiMingliang": "Kecerahan AI",
    "AiBright": "AI Vivid",
    "AiDark": "AI Meredupkan",
    "AiBalance": "Keseimbangan AI",
    "BrightTips": "Filter Vivid cocok untuk game casual, action, atau adventure, meningkatkan saturasi warna untuk membuat visual game lebih dinamis dan menarik。",
    "liangTips": "Disarankan menggunakan filter ketika tampilan game terlalu gelap。",
    "anTips": "Filter ini disarankan untuk digunakan ketika layar game terlalu gelap。",
    "jianyiTips": "Disarankan menggunakan filter ketika visual game terlalu cerah。",
    "shiTips": "Filter ini cocok untuk sebagian besar skenario bermain game。",
    "shi2Tips": "Filter ini cocok untuk game casual, action, atau petualangan, meningkatkan saturasi warna untuk membuat visual game lebih hidup dan menarik。",
    "ruiTips": "Warna filter yang halus dan efek pencahayaan lembut sangat ideal untuk menggambarkan adegan berkhayal, hangat, atau bernostalgia。",
    "qingTips": "Warna terang, kontras tinggi, detail tajam. Cocok untuk adegan hidup dengan pencahayaan yang memadai.",
    "xianTips": "Pengaturan kontras dan kecerahan yang lebih tinggi memastikan detail yang jelas pada adegan gelap tanpa distorsi dan pengalaman menonton yang nyaman pada adegan terang.",
    "dianTips": "Meningkatkan kecerahan dan warna layar secara moderat untuk mencapai kualitas visual sinematik sebaik mungkin",
    "benTips": "Mengurangi efek cahaya putih untuk mencegah kelelahan mata di lingkungan permainan putih murni",
    "fangTips": "Dioptimalkan untuk game open-world dan petualangan, meningkatkan kecerahan dan kontras untuk visual yang lebih tajam.",
    "jiaoTips": "cocok untuk permainan role-playing dan simulasi, nada yang seimbang, realisme visual ditingkatkan",
    "jieTips": "Di optimalkan untuk game dengan cerita kaya dan nuansa emosional, meningkatkan detail dan kelembutan untuk mencapai visual yang lebih terperinci。",
    "jingTips": "Dioptimalkan untuk game aksi dan kompetitif, meningkatkan kejelasan dan kontras untuk tampilan yang lebih tajam.",
    "xiuTips": "Dioptimalkan untuk penyembuhan dan game casual, memperkuat warna hangat dan kelembutan, menciptakan atmosfer yang lebih nyaman.",
    "qihuanTips": "Sesuai untuk adegan dengan unsur fantasi yang kaya dan warna yang cerah, meningkatkan saturasi warna untuk menciptakan dampak visual yang kuat",
    "shengTips": "Perkuat warna dan detail untuk menyoroti kecermatan dan realisme adegan。",
    "sheTips": "Didesain untuk permainan FPS, teka-teki, atau petualangan, meningkatkan detail dan kontras untuk memperbaiki realisme dunia permainan.",
    "she2Tips": "Tepat untuk game tembak-tembakan, balapan, atau perkelahian, menyoroti detail berkualitas tinggi dan kinerja dinamis untuk meningkatkan intensitas dan efek visual pengalaman bermain game.",
    "an2Tips": "Meningkatkan kejelasan adegan di lingkungan gelap, cocok untuk adegan gelap atau skenario malam hari。",
    "wenTips": "Sesuai untuk game bernuansa seni, petualangan, atau santai, menciptakan nuansa warna lembut dan efek cahaya-bayangan untuk menambah keanggunan dan suasana hangat pada skenario。",
    "jing2Tips": "Tepat untuk game kompetitif, ritme musik, atau skenario kota malam, menyoroti warna cerah dan efek pencahayaan,",
    "jing3Tips": "Dioptimalkan untuk game kompetitif, aksi, atau fantasi, meningkatkan kontras warna untuk membuat visual lebih hidup dan dinamis。",
    "onlyVipCanUse": "Filter ini hanya tersedia untuk pengguna VIP"
  },
  "GameRebound": {
    "noGame": "Tidak ada catatan permainan",
    "noGameRecord": "Belum ada catatan permainan! Mulailah sesi sekarang!",
    "gameDuration": "Durasi permainan hari ini:",
    "gameElectricity": "Penggunaan Listrik Harian",
    "degree": "Derajat",
    "gameCo2": "Emisi CO₂ hari ini :",
    "gram": "Kunci",
    "manualRecord": "Rekaman manual",
    "recordDuration": "Durasi perekaman:",
    "details": "Rincian",
    "average": "Rata-rata",
    "minimum": "Minimum",
    "maximum": "Maksimal",
    "occupancyRate": "Penggunaan",
    "voltage": "Tegangan",
    "powerConsumption": "Konsumsi energi",
    "start": "Mulai：",
    "end": "Akhir",
    "Gametime": "Durasi permainan :",
    "Compactdata": "Optimisasi Data",
    "FullData": "Data lengkap",
    "PerformanceAnalysis": "Analisis Kinerja",
    "PerformanceAnalysis2": "Laporan Peristiwa",
    "HardwareStatus": "Status Perangkat Keras",
    "totalPower": "Konsumsi Daya Total",
    "TotalEmissions": "Emisi total",
    "PSS": "Catatan: Data di bawah grafik mewakili nilai rata-rata.",
    "FrameGenerationTime": "Waktu pembuatan frame",
    "GameResolution": "Resolusi Game",
    "FrameGenerationTimeTips": "Titik data ini sangat tinggi dan telah dikeluarkan dari statistik",
    "FrameGenerationTimeTips2": "Titik data ini sangat rendah sehingga tidak termasuk dalam statistik",
    "noData": "Tidak ada data yang tersedia saat ini",
    "ProcessorOccupancy": "Penggunaan CPU",
    "ProcessorFrequency": "Frekuensi Prosesor",
    "ProcessorTemperature": "Suhu Prosesor",
    "ProcessorHeatPower": "Daya Desain Termal Prosesor",
    "GraphicsCardOccupancy": "Penggunaan GPU D3D",
    "GraphicsCardOccupancyTotal": "Penggunaan GPU Total",
    "GraphicsCardFrequency": "Frekuensi GPU",
    "GraphicsCardTemperature": "Suhu GPU",
    "GraphicsCardCoreTemperature": "Suhu Titik Panas Core GPU",
    "GraphicsCardHeatPower": "Daya Panas GPU",
    "GraphicsCardMemoryTemperature": "Suhu Memori GPU",
    "MemoryOccupancy": "Penggunaan memori",
    "MemoryTemperature": "Suhu Memori",
    "MemoryPageFaults": "Pengalihan Paging Memori",
    "Duration": "Durasi",
    "Time": "Waktu",
    "StartStatistics": "Mulai Statistik",
    "Mark": "Tag",
    "EndStatistics": "Selesai Statistik",
    "LineChart": "Grafik Garis",
    "AddPointInGame_m1": "Tekan di dalam game",
    "AddPointInGame_m2": "Titik penanda dapat ditambahkan",
    "LeftMouse": "Klik kiri untuk beralih menampilkan/sembunyikan, klik kanan untuk mengubah warna",
    "DeleteThisLine": "Hapus polyline ini",
    "AddCurve": "Tambahkan kurva",
    "AllCurvesAreHidden": "Semua kurva telah disembunyikan",
    "ThereAreSamplingData": "Total data sampel:",
    "Items": "Masuk",
    "StatisticsData": "Statistik",
    "electricity": "Penggunaan Daya",
    "carbonEmission": "Emisi Karbon",
    "carbonEmissionTips": "Emisi karbon dioksida (kg) = Konsumsi listrik (kWh) × 0,785",
    "D3D": "Penggunaan D3D:",
    "TOTAL": "Durasi penggunaan total:",
    "Process": "Proses:",
    "L3Cache": "Cache L3:",
    "OriginalFrequency": "Frekuensi asli:",
    "MaximumBoostFrequency": "Turbo Boost Maksimum:",
    "DriverVersion": "Versi driver :",
    "GraphicsCardMemoryBrand": "Merek VRAM:",
    "Bitwidth": "Lebar Bus",
    "System": "Sistem:",
    "Screen": "Layar",
    "Interface": "Antarmuka:",
    "Channel": "Saluran:",
    "Timing": "Urutan: ",
    "Capacity": "Kapasitas:",
    "Generation": "aljabar",
    "AddPoint_m1": "Tekan di dalam game",
    "AddPoint_m2": "Tambahkan titik penanda",
    "Hidden": "Tersembunyi",
    "Totalsampling": "Total data sampel:",
    "edition": "Versi Driver:",
    "MainHardDisk": "Hard drive utama",
    "SetAsStartTime": "Tetapkan sebagai Waktu Mulai",
    "SetAsEndTime": "Tetapkan sebagai waktu selesai",
    "WindowWillBe": "Jendela Statistik Kinerja akan berada di",
    "After": "Setelah ditutup",
    "NoLongerPopUpThisGame": "Game ini tidak akan muncul lagi",
    "HideTemperatureReason": "Sembunyikan alasan suhu",
    "HideTemperatureReason2": "Sembunyikan Laporan Kejadian",
    "HideOtherReason": "Sembunyikan Alasan Lain",
    "CPUanalysis": "Analisis Kinerja CPU",
    "TemperatureCause": "Penyebab Suhu",
    "tempSensorEvent": "Peristiwa Sensor Suhu",
    "NoTemperatureLimitation": "Tidak ada penurunan kinerja CPU yang disebabkan oleh suhu yang terdeteksi. Sistem pendingin Anda mampu sepenuhnya memenuhi kebutuhan permainan ini.",
    "NoTemperatureLimitation2": "Tidak ada kejadian sensor suhu, sistem pendingin Anda dapat sempurna menangani persyaratan game ini.",
    "performanceis": "Terpilih",
    "Inside": "Internal,",
    "TheStatisticsTimeOf": "Periode statistik memenuhi kondisi pemicu yang relevan. Alasan pemicu yang paling umum adalah",
    "limited": "Persentase waktu total karena batasan kinerja yang disebabkan oleh suhu",
    "SpecificReasons": "Alasan spesifik dan proporsinya dalam penyebab yang terkait suhu:",
    "OptimizationSuggestion": "Saran optimalisasi :",
    "CPUtemperature": "Suhu CPU terlalu panas. Silakan periksa/perbaiki lingkungan pendingin CPU。",
    "CPUoverheat": "CPU terlalu panas karena pasokan daya dari motherboard. Periksa pengaturan terkait motherboard atau tingkatkan lingkungan pendingin。",
    "OtherReasons": "Alasan lain",
    "NoPowerSupplyLimitation": "Kinerja CPU tidak dibatasi oleh pasokan daya/konsumsi daya. Pengaturan konsumsi daya BIOS Anda memadai untuk kebutuhan game ini。",
    "PowerSupplyLimitation": "Karena keterbatasan pasokan daya/penggunaan energi",
    "SpecificReasonsInOtherReasons": "Alasan spesifik dan proporsinya di antara alasan lain:",
    "PleaseCheckTheMainboard": "Periksa status pengiriman daya papan induk atau atur pengaturan daya BIOS untuk mengatasi batasan kinerja CPU yang disebabkan oleh faktor lain",
    "CPUcoretemperature": "Suhu inti telah mencapai Tj,Max dan dibatasi",
    "CPUCriticalTemperature": "Suhu CPU kritis",
    "CPUCircuitTemperature": "CPU Package/Ring Bus dibatasi karena mencapai Tj,Max",
    "CPUCircuitCriticalTemperature": "Paket CPU / bus cincin telah mencapai suhu kritis",
    "CPUtemperatureoverheating": "Pendeteksi panas berlebih CPU, yang akan memicu pengurangan frekuensi otomatis untuk menurunkan suhu dan mencegah kegagalan perangkat keras.",
    "CPUoverheatingtriggered": "CPU akan mengaktifkan mekanisme pendinginannya dan menyesuaikan tegangan/kecepatan untuk mengurangi konsumsi daya dan suhu.",
    "CPUPowerSupplyOverheating": "CPU dibatasi karena kenaikan panas berlebih pada suplai daya papan induk",
    "CPUPowerSupplyLimitation": "Kinerja CPU dibatasi karena pasokan daya motherboard terlalu panas",
    "CPUMaximumPowerLimitation": "Inti dibatasi oleh batas konsumsi daya maksimum",
    "CPUCircuitPowerLimitation": "Paket CPU/Bus Ring telah mencapai batas daya",
    "CPUElectricalDesignLimitation": "Memicu batasan desain listrik (termasuk dinding arus ICCmax, dinding daya puncak PL4, batasan tegangan SVID, dll.)",
    "CPULongTermPowerLimitation": "Batas daya jangka panjang CPU tercapai",
    "CPULongTermPowerinstantaneous": "Konsumsi daya CPU sesaat telah mencapai batas",
    "CPUPowerLimitation": "Mekanisme Penurunan Frekuensi Turbo CPU, biasanya dibatasi oleh BIOS atau perangkat lunak tertentu",
    "CPUPowerWallLimitation": "Batas Daya CPU",
    "CPUcurrentwalllimit": "Batas Dinding CPU Saat Ini",
    "AiAgent": "Agen GamePP (Agen AI)",
    "AgentDesc": "Kembali ke halaman utama",
    "fnBeta": "Fitur ini saat ini berada di fase pengujian undangan. Akun GamePP Anda belum mendapatkan akses pengujian。",
    "getAIReport": "Dapatkan laporan AI",
    "waitingAi": "Menunggu pembuatan laporan selesai",
    "no15mins": "Durasi permainan kurang dari 15 menit, tidak ada laporan AI yang valid tersedia。",
    "timeout": "Permintaan server melebihi batas waktu",
    "agentId": "ID Agen:",
    "reDo": "Regenerasi laporan",
    "text2": "Agen GamePP: Laporan analisis AI online, konten berikut dibuat oleh AI dan hanya sebagai referensi.",
    "amdAiagentTitle": "GamePP Agen: Laporan Analisis AMD Ryzen AI, konten berikut dibuat oleh AI dan hanya untuk referensi.",
    "noCurData": "Tidak ada data saat ini",
    "dataScreening": "Pemfilteran data",
    "dataScreeningDescription": "Fitur ini dirancang untuk mengecualikan statistik data dari periode waktu permainan yang tidak efektif seperti pemuatan peta atau waktu tunggu di lobby. 0 berarti tidak ada pengecualian yang dilakukan.",
    "excessivelyHighParameter": "Parameter terlalu tinggi",
    "tooLowParameter": "Parameter terlalu rendah",
    "theMaximumValueIs": "Nilai maksimum adalah",
    "theMinimumValueIs": "Nilai minimum adalah",
    "exclude": "Mengecualikan",
    "dataStatisticsAtThatTime": "Statistik data pada waktu ini",
    "itHasBeenGenerated": "Pembuatan selesai,",
    "clickToView": "Klik untuk melihat",
    "onlineAnalysis": "Analisis Online",
    "amdRyzenAILocalAnalysis": "AMD Ryzen AI Analisis Lokal",
    "useTerms": "Kondisi Penggunaan:",
    "term1": "1. Prosesor Ryzen AI Max atau Prosesor Seri Ryzen Al 300",
    "term2": "2.Versi Driver AMD NPU",
    "term3": "3. Aktifkan grafik terintegrasi",
    "conformsTo": "Sesuai",
    "notInLineWith": "Tidak valid",
    "theVersionIsTooLow": "Versi terlalu lama",
    "canNotUseAmdNpu": "Konfigurasi Anda tidak memenuhi persyaratan. Analisis entitas AMD NPU tidak dapat digunakan。",
    "unusable": "Tidak tersedia",
    "downloadTheFile": "Unduh file",
    "downloadSource": "Sumber unduhan:",
    "fileSize": "Ukuran file: sekitar 8,34 GB",
    "cancelDownload": "Batalkan unduhan",
    "filePath": "Lokasi file",
    "generateAReport": "Buat Laporan",
    "fileMissing": "File hilang. Perlu diunduh ulang.",
    "downloading": "Mengunduh...",
    "theModelConfigurationLoadingFailed": "Gagal memuat konfigurasi model",
    "theModelDirectoryDoesNotExist": "Direktori model tidak ada",
    "thereIsAMistakeInReasoning": "Kesalahan inferensi",
    "theInputExceedsTheModelLimit": "Masukan melebihi batas model",
    "selectModelNotSupport": "Model unduhan yang dipilih tidak didukung",
    "delDirFail": "Gagal menghapus direktori model yang ada",
    "failedCreateModelDir": "Gagal membuat direktori model",
    "modelNotBeenFullyDownload": "Model belum sepenuhnya diunduh",
    "agentIsThinking": "Agen Jiajia sedang berpikir",
    "reasoningModelFile": "Berkas model untuk inferensi",
    "modelReasoningTool": "Alat Inferensi Model"
  },
  "SelectSensor": {
    "DefaultSensor": "Sensor Bawaan",
    "Change": "Ubah",
    "FanSpeed": "Kecepatan Kipas",
    "MainGraphicsCard": "GPU utama",
    "SetAsMainGraphicsCard": "Tetapkan sebagai GPU utama",
    "GPUTemperature": "Suhu GPU",
    "GPUHeatPower": "Daya Termal GPU",
    "GPUTemperatureD3D": "Penggunaan GPU D3D",
    "GPUTemperatureTOTAL": "Penggunaan GPU Total",
    "GPUTemperatureCore": "Suhu Titik Panas Inti GPU",
    "MotherboardTemperature": "Suhu papan induk",
    "MyAttention": "Favorit Saya",
    "All": "Semua",
    "Unit": "Satuan:",
    "NoAttention": "Sensor yang Tidak Diikuti",
    "AttentionSensor": "Sensor yang Dipantau (Beta)",
    "GoToAttention": "Beralih ke Mode Fokus",
    "CancelAttention": "Batalkan pengikutan",
    "noThisSensor": "Tidak ada sensor",
    "deviceAbout": "Perangkat Peripheral Terkait",
    "deviceBattery": "Baterai Perangkat Luar",
    "testFunction": "Fitur Uji Coba",
    "mouseEventRate": "Frekuensi polling",
    "relatedWithinTheGame": "Terkait Game",
    "winAbout": "Sistem",
    "trackDevicesBattery": "Lacak tingkat baterai perangkat eksternal",
    "ingameRealtimeMouseRate": "Frekuensi polling mouse saat ini yang digunakan secara real-time selama bermain game",
    "notfoundDevice": "Tidak ada perangkat yang didukung ditemukan",
    "deviceBatteryNeedMythcool": "Daftar perangkat yang mendukung tampilan baterai: (memerlukan penggunaan Myth.Cool)",
    "vkm1mouse": "Mouse Valkyrie M1",
    "vkm2mouse": "Valkyrie M2 Mouse",
    "vk99keyboard": "Valkyrie 99 Keyboard Poros Magnet",
    "logitechProWireless": "Logitech Pro Wireless",
    "logitechProXSUPERLIGHT": "Logitech PRO X SUPERLIGHT",
    "logitechProXSUPERLIGHT2": "Logitech PRO",
    "logitechPro2LIGHTSPEED": "Logitech PRO 2 LIGHTSPEED",
    "steelSeriesArctisGameBuds": "SteelSeries Arctis GameBuds",
    "steelSeriesArctis7PPlus": "SteelSeries Arctis 7P Plus",
    "razerV3": "Razer Valkyrie V3 Edisi Profesional",
    "razerV2": "Razer Viper V2 Edisi Profesional",
    "wireless": "Nirkabel",
    "logitechNeedGhub": "Ketika Logitech tidak dapat mengambil model perangkat, Anda perlu mengunduh GHUB",
    "chargingInProgress": "Mengisi",
    "inHibernation": "Dalam mode tidur"
  },
  "video": {
    "videoRecord": "Perekaman Video",
    "recordVideo": "Video Direkam",
    "scheme": "Profil",
    "suggestScheme": "Rencana yang Disarankan",
    "text1": "Dengan konfigurasi ini, ukuran video 1 menit sekitar",
    "text2": "Fitur ini menggunakan sumber daya sistem tambahan",
    "low": "Rendah",
    "mid": "Beranda",
    "high": "Tinggi",
    "1080p": "Natif",
    "RecordingFPS": "Rekam FPS",
    "bitRate": "Bitrate Video",
    "videoResolution": "Resolusi Video",
    "startStopRecord": "Mulai/Hentikan Perekaman",
    "instantReplay": "Replay instan",
    "instantReplayTime": "Durasi Putar Langsung",
    "showIngame": "Buka panel kontrol di dalam game",
    "CaptureMode": "Metode Tangkap",
    "gameWindow": "Jendela Game",
    "desktopWindow": "Jendela Desktop",
    "fileSavePath": "Lokasi penyimpanan file",
    "selectVideoSavePath": "Pilih Jalur Simpan Rekaman",
    "diskFreeSpace": "Ruang kosong di hard disk:",
    "edit": "Ubah",
    "open": "Buka",
    "displayMouse": "Tampilkan kursor mouse",
    "recordMicrophone": "Rekam Mikrofon",
    "gameGraphics": "Tampilan Game Asli"
  },
  "Setting": {
    "common": "Umum",
    "personal": "Pemersonalisan",
    "messageNotification": "Pemberitahuan",
    "sensorReading": "Nilai sensor",
    "OLEDscreen": "Perlindungan Burn-in OLED",
    "performanceStatistics": "Statistik Kinerja",
    "shortcut": "Kombinasi tombol",
    "ingameSetting": "Simpan Pengaturan Game",
    "other": "Lainnya",
    "otherSettings": "Pengaturan Lainnya",
    "GeneralSetting": "Pengaturan Umum",
    "softwareVersion": "Versi perangkat lunak",
    "checkForUpdates": "Periksa pembaruan",
    "updateNow": "Perbarui Sekarang",
    "currentVersion": "Versi Saat Ini",
    "latestVersion": "Versi terbaru",
    "isLatestVersion": "Versi saat ini sudah merupakan versi terbaru.",
    "functionModuleUpdate": "Pembaruan Modul Fungsional",
    "alwaysUpdateModules": "Pastikan semua modul fungsional yang terinstal tetap terkini",
    "lang": "Bahasa",
    "bootstrap": "Mulai otomatis",
    "powerOn_m1": "Startup",
    "powerOn_m2": "Mulai otomatis setelah detik",
    "defaultDelay": "Default: 40 detik",
    "followSystemScale": "Ikuti penyesuaian sistem",
    "privacySettings": "Pengaturan Privasi",
    "JoinGamePPPlan": "Bergabunglah dengan Program Peningkatan Pengalaman Pengguna GamePP",
    "personalizedSetting": "Personalisasi",
    "restoreDefault": "Kembalikan ke pengaturan default",
    "color": "Warna",
    "picture": "Gambar",
    "video": "Video",
    "browse": "Telusuri",
    "clear": "Hapus",
    "mp4VideoOrPNGImagesCanBeUploaded": "Unggah video MP4 atau gambar PNG",
    "transparency": "Transparansi",
    "backgroundColor": "Warna Latar",
    "textFont": "Font Teks Utama",
    "message": "Pesan",
    "enableInGameNotifications": "Aktifkan pemberitahuan di dalam game",
    "messagePosition": "Posisi Tampil dalam Game",
    "leftTop": "Sudut kiri atas",
    "leftCenter": "Kiri Tengah",
    "leftBottom": "Sudut kiri bawah",
    "rightTop": "sudut kanan atas",
    "rightCenter": "Kanan Tengah",
    "rightBottom": "sudut kanan bawah",
    "noticeContent": "Isi Pemberitahuan",
    "gameInjection": "Injeksi Game",
    "ingameShow": "Tampilkan di game",
    "inGameMonitoring": "Pemantauan dalam game",
    "gameFilter": "Penyaring Game",
    "start": "Mulai",
    "endMarkStatistics": "Statistik Marker Akhir",
    "readHwinfoFail": "HWINFO Gagal mengambil informasi perangkat keras!",
    "dataSaveDesktop": "Data telah disimpan ke papan klip dan file desktop.",
    "TheSensorCacheCleared": "Data cache sensor telah dihapus",
    "defaultSensor": "Sensor Bawaan",
    "setSensor": "Pilih sensor",
    "refreshTime": "Waktu Pembaruan Data",
    "recommend": "Bawaan",
    "sensorMsg": "Semakin pendek interval waktu, semakin tinggi konsumsi kinerja. Silakan pilih secara hati-hati。",
    "exportData": "Ekspor data",
    "exportHwData": "Ekspor data informasi perangkat keras",
    "sensorError": "Pembacaan Sensor Abnormal",
    "clearCache": "Hapus Cache",
    "littleTips": "Petunjuk: Laporan kinerja akan dihasilkan 2 menit setelah memulai game",
    "disableAutoShow": "Nonaktifkan pembukaan otomatis jendela statistik kinerja",
    "AutoClosePopUpWindow_m1": "Jendela statistik kinerja akan ditutup secara otomatis setelah waktu yang ditentukan:",
    "AutoClosePopUpWindow_m2": "detik",
    "abnormalShutdownReport": "Laporan Tutup Tidak Normal",
    "showWeaAndAddress": "Tampilkan informasi cuaca dan lokasi",
    "autoScreenShots": "Mangkap layar game secara otomatis saat ditandai",
    "keepRecent": "Jumlah catatan terbaru yang akan disimpan:",
    "noLimit": "Tidak Terbatas",
    "enableInGameSettingsSaving": "Aktifkan penyimpanan pengaturan dalam game",
    "debugMode": "Mode Debug",
    "enableDisableDebugMode": "Aktifkan/Nonaktifkan Mode Debug",
    "audioCompatibilityMode": "Mode Kompatibilitas Audio",
    "quickClose": "Penutupan cepat",
    "closeTheGameQuickly": "Menutup proses game secara cepat",
    "cancel": "Batal",
    "confirm": "Konfirmasi",
    "MoveInterval_m1": "Pemantauan desktop dan dalam game akan sedikit bergerak setelah:",
    "MoveInterval_m2": "menit",
    "text3": "Setelah keluar dari game, tidak ada jendela laporan kinerja yang akan muncul, hanya catatan historis yang disimpan",
    "text5": "Laporan akan secara otomatis dibuat setelah shutdown tak terduga. Mengaktifkan fitur ini akan mengonsumsi sumber daya sistem tambahan。",
    "text6": "Penggunaan fungsi pintas mungkin bertabrakan dengan pintas game lainnya, harap konfigurasikan dengan hati-hati。",
    "text7": "Atur pintasan keyboard ke 'Tidak Ada', mohon gunakan tombol Backspace",
    "text8": "Menjaga filter, pemantauan dalam game, dan status fungsional lainnya selama bermain game berdasarkan nama proses",
    "text9": "Mengaktifkan akan terus mencatat data runtime; menonaktifkan akan menghapus file log (Disarankan untuk tetap dimatikan)",
    "text10": "Setelah diaktifkan, sensor papan induk akan tidak dapat diakses untuk menyelesaikan masalah audio yang disebabkan oleh GamePP。",
    "text11": "Tekan Alt+F4 dua kali berturut-turut untuk keluar dari game saat ini secara cepat",
    "text12": "Apakah Anda ingin melanjutkan? Mode ini memerlukan restart GamePP.",
    "openMainUI": "Tampilkan aplikasi",
    "setting": "Pengaturan",
    "feedback": "Umpan balik",
    "help": "Bantuan",
    "sensorReadingSetting": "Pengaturan Pembacaan Sensor",
    "searchlanguage": "Bahasa Pencarian"
  },
  "GamePlusOne": {
    "year": "Tahun",
    "month": "Bulan",
    "day": "hari",
    "success": "Keberhasilan",
    "fail": "Gagal",
    "will": "Saat ini",
    "missedGame": "Melewatkan kesempatan mendapatkan game",
    "text1": "Jumlah, kira-kira ￥",
    "text2": "Total Game yang Telah Diklaim",
    "text3": "Versi",
    "gamevalue": "Nilai permainan",
    "gamevalue1": "Permintaan",
    "total": "Total Klaim",
    "text4": "Permainan, total disimpan",
    "text6": "Produk, Nilai",
    "Platformaccountmanagement": "Pengelolaan Akun Platform",
    "Missed1": "Belum diambil",
    "Received2": "（Berhasil diterima）",
    "Receivedsoon2": "(Tersedia saat ini)",
    "Receivedsoon": "Tersedia",
    "Missed": "Koleksi yang Terlewat",
    "Received": "Berhasil Diklaim",
    "Getaccount": "Aklaim akun",
    "Worth": "Nilai",
    "Auto": "Otomatis",
    "Manual": "Manual",
    "Pleasechoose": "Silakan pilih game",
    "Receive": "Klaim sekarang",
    "Selected": "Telah dipilih",
    "text5": "Permainan",
    "Automatic": "Pengambilan otomatis...",
    "Collecting": "Mengklaim...",
    "ReceiveTimes": "Jumlah Klaim Bulanan",
    "Thefirst": "#",
    "Week": "Minggu",
    "weekstotal": "Total 53 minggu",
    "Return": "Beranda",
    "Solutionto": "Solusi untuk Kegagalan Pengikatan Akun",
    "accounts": "Jumlah akun yang terikat",
    "Addaccount": "Tambahkan akun",
    "Clearcache": "Hapus cache",
    "Bindtime": "Waktu pengikatan",
    "Status": "Status",
    "Normal": "Mode Normal",
    "Invalid": "Tidak valid",
    "text7": "Permainan, jumlah total yang disimpan",
    "Yuan": "Yuan",
    "untie": "Hapus ikatan",
    "disable": "Nonaktifkan",
    "enable": "Aktifkan",
    "gamePlatform": "Platform game",
    "goStorePage": "Pergi ke Halaman Toko",
    "receiveEnd": "Setelah Batas Waktu",
    "loginPlatformAccount": "Akun Platform yang Telah Masuk",
    "waitReceive": "Klaim yang menunggu",
    "receiveSuccess": "Sukses",
    "accountInvalid": "Akun Kedaluwarsa",
    "alreadyOwn": "Sudah Dimiliki",
    "networkError": "Ketidaknormalan Jaringan",
    "noGame": "Tidak ada Game Core",
    "manualReceiveInterrupt": "Penghentian Manual Pengambilan",
    "receiving": "Klaim sedang berlangsung",
    "agree": "Saya setuju untuk bergabung dengan program klaim gratis GamePP.",
    "again": "Aklami kembali"
  },
  "shutdownTimer": {
    "timedShutdown": "Pematian Terjadwal",
    "currentTime": "Waktu saat ini:",
    "setCountdown": "Atur Penghitungan Mundur",
    "shutdownInSeconds": "Menutup dalam X detik",
    "shutdownIn": "Setelah Dimatikan",
    "goingToBe": "akan menjadi",
    "executionPlan": "Rencana Eksekusi",
    "startTheClock": "Mulai Timer",
    "stopTheClock": "Batalkan Rencana",
    "isShuttingDown": "Menjalankan rencana shutdown terjadwal:",
    "noplan": "Tidak ada rencana shutdown saat ini",
    "hour": "Jam",
    "min": "Menit",
    "sec": "Detik",
    "ms": "Milidetik",
    "year": "Tahun",
    "month": "Bulan",
    "day": "Hari",
    "hours": "Jam"
  },
  "screenshotpage": {
    "screenshot": "Tangkapan layar",
    "screenshotFormat": "Didesain khusus untuk menangkap layar game, mendukung penyimpanan dalam format JPG/PNG/BMP, memungkinkan penangkapan layar game secara cepat, dan memastikan output berkualitas tinggi tanpa kehilangan kualitas",
    "Turnon": "Aktifkan tangkapan layar otomatis, setiap",
    "seconds": "Detik",
    "takeScreenshot": "Lakukan tangkapan layar otomatis",
    "screenshotSettings": "Pengaturan ini tidak valid saat diaktifkan dalam game",
    "saveGameFilterAndMonitoring": "Simpan efek 'Game Filter' dan 'Pemantauan di Dalam Game' ke dalam tangkapan layar",
    "disableScreenshotSound": "Nonaktifkan pemberitahuan suara untuk tangkapan layar",
    "imageFormat": "Format gambar",
    "recommended": "Rekomendasikan",
    "viewingdetails": "Mempertahankan detail kualitas gambar, ukuran moderat, cocok untuk melihat detail",
    "saveSpace": "Kualitas gambar dapat disesuaikan, ukuran file minimal, penghematan ruang",
    "ultraQuality": "Visual ultra jernih tanpa kompresi dengan ukuran file besar. Disarankan untuk pemain yang memprioritaskan penyimpanan game berkualitas tinggi.",
    "fileSavePath": "Jalur Penyimpanan File",
    "hardDiskSpace": "Ruang disk yang tersedia:",
    "minutes": "Menit"
  },
  "DesktopMonitoring": {
    "desktopMonitoring": "Pemantauan Desktop",
    "SomeSensors": "Kami merekomendasikan beberapa sensor untuk pemantauan. Anda dapat menghapus atau menambahkannya",
    "AddComponent": "Tambahkan komponen baru",
    "Type": "Tipe",
    "Remarks": "Catatan",
    "AssociatedSensor": "Hubungkan Sensor",
    "Operation": "Operasi",
    "Return": "Kembali",
    "TimeSelection": "Pemilihan waktu:",
    "Format": "Format：",
    "Rule": "Aturan：",
    "Coordinate": "Koordinat:",
    "CustomTextContent": "Konten teks khusus:",
    "SystemTime": "Waktu Sistem",
    "China": "Tiongkok",
    "Britain": "Britania Raya",
    "America": "Amerika Serikat",
    "Russia": "Rusia",
    "France": "Perancis",
    "DateAndTime": "Tanggal dan waktu",
    "Time": "Waktu",
    "Date": "Tanggal",
    "Week": "Hari dalam seminggu",
    "DateAndTimeAndWeek": "Tanggal+Waktu+Hari dalam seminggu",
    "TimeAndWeek": "Waktu+Hari dalam Minggu",
    "Hour12": "Format 12 jam",
    "Hour24": "format 24 jam",
    "SelectSensor": "Pilih sensor:",
    "AssociatedSensor1": "Hubungkan sensor:",
    "SensorUnit": "Satuan sensor：",
    "Second": "Detik:",
    "Corner": "Sudut tumpul:",
    "BackgroundColor": "Warna latar belakang:",
    "ProgressColor": "Warna Kemajuan:",
    "Font": "Font：",
    "SelectFont": "Pilih font",
    "FontSize": "Ukuran font:",
    "Color": "Warna：",
    "Style": "Gaya:",
    "Bold": "Tebal",
    "Italic": "Miring",
    "Shadow": "Bayangan",
    "ShadowPosition": "Posisi Bayangan：",
    "ShadowEffect": "Efek Bayangan：",
    "Blur": "Buran",
    "ShadowColor": "Warna bayangan：",
    "SelectFromLocalFiles": "Pilih dari file lokal:",
    "UploadImageVideo": "Unggah gambar/video",
    "UploadSVGFile": "Unggah file SVG",
    "Width": "Lebar:",
    "Height": "Tinggi: ",
    "Effect": "Efek:",
    "Rotation": "Rotasi:",
    "WhenTheSensorValue": "Nilai sensor lebih besar dari",
    "conditions": "Ketika kondisi tidak terpenuhi (tidak ada rotasi ketika nilai sensor 0)",
    "Clockwise": "Searah jarum jam",
    "Counterclockwise": "Berlawanan arah jarum jam",
    "QuickRotation": "Rotasi cepat",
    "SlowRotation": "Putar lambat",
    "StopRotation": "Hentikan rotasi",
    "StrokeColor": "Warna garis tepi：",
    "Path": "Jalur",
    "Color1": "Warna",
    "ChangeColor": "Ubah warna",
    "When": "Ketika",
    "SensorValue": "Nilai sensor lebih besar atau sama dengan",
    "SensorValue1": "Nilai sensor kurang dari atau sama dengan",
    "SensorValue2": "Nilai sensor sama",
    "MonitoringSettings": "Pengaturan pemantauan",
    "RestoreDefault": "Kembalikan ke pengaturan default",
    "Monitor": "Monitor",
    "AreaSize": "Ukuran area",
    "Background": "Latar belakang",
    "ImageVideo": "Gambar/Vide",
    "PureColor": "Warna padat",
    "Select": "Pilih",
    "ImageVideoDisplayMode": "Mode Tampilan Gambar/Video",
    "Transparency": "Transparansi",
    "DisplayPosition": "Tampilkan posisi",
    "Stretch": "Mengulur",
    "Fill": "Isi",
    "Adapt": "Menyesuaikan",
    "SelectThePosition": "Klik sel grid untuk memilih lokasi secara cepat",
    "CurrentPosition": "Lokasi saat ini:",
    "DragLock": "Kunci Geser",
    "LockMonitoringPosition": "Posisi monitor terkunci (Setelah dikunci, monitor tidak dapat digeser)",
    "Unlockinterior": "Izinkan seret elemen internal",
    "Font1": "Font",
    "GameSettings": "Pengaturan game",
    "CloseDesktopMonitor": "Nonaktifkan pemantauan desktop secara otomatis saat permainan berjalan.",
    "OLED": "Perlindungan OLED Burn-in",
    "Display": "Tampilkan",
    "PleaseEnterContent": "Masukkan konten",
    "NextStep": "Berikutnya",
    "Add": "Tambah",
    "StylesForYou": "Kami merekomendasikan beberapa gaya pemantauan. Anda dapat memilih dan menerapkannya. Lebih banyak gaya akan ditambahkan di masa depan.",
    "EditPlan": "Edit profil",
    "MonitoringStylePlan": "Skema Gaya Pantauan",
    "AddDesktopMonitoring": "Tambahkan pengawasan desktop",
    "TextLabel": "Label teks",
    "ImageVideo1": "Gambar, Video",
    "SensorGraphics": "Grafik Sensor",
    "SensorData": "Data sensor",
    "CustomText": "Teks Kustom",
    "DateTime": "Tanggal dan waktu",
    "Image": "Gambar",
    "Video": "Video",
    "SVG": "SVG",
    "ProgressBar": "Batang progres",
    "Graphics": "Grafis",
    "UploadImage": "Unggah gambar",
    "UploadVideo": "Unggah video",
    "RealTimeMonitoring": "Pemantauan real-time suhu dan penggunaan CPU/GPU, penyesuaian tata letak dengan drag and drop, pengaturan gaya pribadi – bantu Anda menguasai kinerja dan estetika desktop",
    "Chart": "Grafik",
    "Zigzagcolor": "Warna garis (Awal)",
    "Zigzagcolor1": "Warna Garis (Titik Akhir)",
    "Zigzagcolor2": "Warna area grafik garis (Awal)",
    "Zigzagcolor3": "Warna area grafik garis (akhir)",
    "CustomMonitoring": "Pemantauan Kustom"
  }
}
//messageEnd 
 export default id 