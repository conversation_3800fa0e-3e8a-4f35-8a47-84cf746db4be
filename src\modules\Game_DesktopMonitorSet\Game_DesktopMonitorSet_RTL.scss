body[dir=rtl] {
    header{
        padding-left:0;
    }
    .RightTopIcons{
        margin-right: auto;
        margin-left: inherit!important;
    }
    .mainSwitch{
        .title{
            margin-right: 0;
            margin-left: 10px;
        }
    }
    .mainContent{
        .el-button {
            span{
                margin-left: 0;
                margin-right: 6px;
            }
        }
        .monitor-set{
            .flex-items-center{
                span{
                    margin-left:0!important;
                    margin-right: 10px;
                }
            }
            .el-radio{
                margin-right:0;
                margin-left: 20px;
            }
            .el-radio__label{
                padding-left:0;
                padding-right: 8px;
            }
            .el-checkbox__label{
                padding-left:0;
                padding-right: 8px;
            }
            // .Set_item .itemdata .displacement span{margin-right:0; margin-left: 10px;}
            .Inputcontent{
                margin-right: 10px;
            }
        }
        .displacement {
            strong{
                margin-left: 0;
                margin-right: 5px;
            }
            .Colorbox{
                margin-right:0;
                margin-left: 10px;
            }
            .el-checkbox{
                margin-right: 0;
                margin-left: 30px;
            }
            .el-checkbox:last-of-type{
                margin-right: 0;
                margin-left: 0;
            }
        }
        .el-button+.el-button{
            margin-left: 0px;
            margin-right: 12px;
        }
    }
}