<script setup lang="ts">
import {obsStore,GPP_WriteInteger} from "../stores/index"
import { storeToRefs } from 'pinia'
import {onMounted} from "vue";
const store = obsStore();
const { state } = storeToRefs(store)
const { getSettingsValue } = store

onMounted(()=>{
    getSettingsValue()
})

function setMainSwitch(val:boolean) {
    store.state.main_switch = val
    GPP_WriteInteger(41,val?1:0)
}
</script>

<template>
    <div class="main_switch">
        <span>{{$t('video.videoRecord')}}</span>
        <el-switch tabindex="-1" v-model="state.main_switch" @change="setMainSwitch" active-color="#508DE2"></el-switch>
        <input tabindex="0" style="display: none;">
    </div>

</template>

<style scoped lang="scss">
.main_switch {
    display: flex;
    align-items: center;
    gap: 10px;
    height: 40px;
    line-height: 40px;
    position: absolute;
    top: 0;
    left: 17px;
}
</style>
