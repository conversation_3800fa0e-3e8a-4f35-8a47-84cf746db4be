<script setup lang="ts">
import {defineProps} from "vue";
import {useReboundDetailStore} from "@/modules/Game_ReboundDetail/stores/reboundDetail";

const $store = useReboundDetailStore()

const props = defineProps({
    canUse:{
        type: Boolean
    },
    no15Mins:{
        type: Boolean
    },
})
</script>

<template>
    <div class="online-box">
        <p>{{ $t('GameRebound.AgentDesc') }}</p>
        <!--<p>根据本次游戏的性能统计数据，从各方面进行数据分析</p>-->
        <p v-if="!props.canUse">{{ $t('GameRebound.fnBeta') }}</p>
        <el-button
            style="padding: 0 20px"
            type="primary"
            color="#3578d3"
            size="small"
            :disabled="!props.canUse || props.no15Mins || $store.ai_agent.disableCount > 0 || $store.ai_agent.statusNot200"
            @click="$store.handleAddContent"
        >
            {{ $t('GameRebound.getAIReport') }}
            <span v-show="$store.ai_agent.disableCount > 0"> ({{ $store.ai_agent.disableCount }})</span>
        </el-button>
        <p v-show="$store.ai_agent.isWaiting">{{ $t('GameRebound.waitingAi') }}</p>
        <p v-show="props.no15Mins" class="noFiveMins">{{ $t('GameRebound.no15mins') }}</p>
        <p v-show="$store.ai_agent.try_count == 10 && !$store.ai_agent.errMsg">{{ $t('GameRebound.timeout') }}</p>
        <p v-show="$store.ai_agent.try_count >= 10 && $store.ai_agent.errMsg">{{$store.ai_agent.errMsg}}</p>
    </div>
</template>

<style scoped lang="scss">
.online-box {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 25px;
    flex-direction: column;

    .noFiveMins {
        color: rgb(206, 48, 48);
    }
}
</style>
