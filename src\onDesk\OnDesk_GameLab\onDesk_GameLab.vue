<script setup lang="ts">
import {onMounted, ref, watch, computed} from "vue";
import { gamepp } from "gamepp"
let time = ref('00:00:00')
let num =  ref(0)
let string = ref('')


onMounted(async ()=>{
   getValue()
   initWindow()
})

const initWindow = async () =>
{
  let DisplayBounds = await gamepp.getPrimaryDisplayBounds.promise();
   await gamepp.webapp.windows.setPosition.promise('desktop_lab', Number((DisplayBounds['width']) - 350), 0);
   string.value = await gamepp.setting.getString.promise(468);
   setInterval(() => {
             num.value++
             time.value = FormatSecondsNumber(num.value);
        }, 1000)
        
}

const getValue = () =>
{
  gamepp.webapp.onInternalAppEvent.addEventListener((e) => 
      {
         console.warn('Get Message：',e);
         let msg 
         try 
         {
            msg = e['action']
            console.warn('msg:',msg);
            switch(msg)
            {
               case 'changeHotkey':
               break;
            }
         }
         catch(err){}

      });
}


const FormatSecondsNumber  = (result:number) =>
{
    var h = Math.floor(result / 3600) < 10 ? '0' + Math.floor(result / 3600) : Math.floor(result / 3600);
    var m = Math.floor((result / 60 % 60)) < 10 ? '0' + Math.floor((result / 60 % 60)) : Math.floor((result / 60 % 60));
    var s = Math.floor((result % 60)) < 10 ? '0' + Math.floor((result % 60)) : Math.floor((result % 60));
    return h + ":" + m + ":" + s;
}


</script>

<template>
  <div class="container">
      <img style="width: 25px;height: 25px;margin: 0 20px;" src="../../../src/assets/img/Public/logo_gpp.png" alt="">按下{{ string }}停止记录<span style="margin-left: 15px;">{{ time }}</span>
  </div>
</template>

<style lang="scss" scoped>
.container
{
    user-select: none;
    width: 350px;
    height: 40px;
    background-color:rgba(34, 35, 46, 0.4);
    color: #FFF;
    display: flex;
    align-items: center;
}

</style>
