const pt = {
  "update": {
    "Game": "Game",
    "PP": "PP",
    "upgradingInProgress": "Atualização em andamento",
    "theModuleIsBeingUpdated": "Atualizando módulo",
    "dataIsBeingUpdated": "Atualizando dados...",
    "checkingUpdate": "A verificar atualizações",
    "checkingUpgrade": "Verificando atualizações",
    "loadingProgramComponent": "A carregar componentes do programa...",
    "loadingHotkeyModules": "A carregar componente de atalhos",
    "loadingGPPModules": "A carregar componentes do GamePP",
    "loadingBlackWhiteList": "Lista preta/lista branca está carregando",
    "loadingGameSetting": "A carregar parâmetros de configuração do jogo...",
    "loadingUserAbout": "A carregar dados de autenticação do utilizador",
    "loadingGameBenchmark": "A carregar pontuação do jogo",
    "loadingHardwareInfo": "Carregando componente de informações de hardware",
    "loadingDBModules": "A carregar o módulo da base de dados...",
    "loadingIGCModules": "A carregar o módulo IGC",
    "loadingFTPModules": "A carregar módulo de suporte FTP",
    "loadingDialogModules": "Carregando módulo de caixa de diálogo",
    "loadingDataStatisticsModules": "Carregamento do módulo de estatísticas em andamento",
    "loadingSysModules": "A carregar componentes do sistema",
    "loadingGameOptimization": "Carregando otimização do jogo",
    "loadingGameAcceleration": "Carregando aceleração do jogo",
    "loadingScreenshot": "Carregando captura de tela da gravação",
    "loadingVideoComponent": "Carregando componente de compactação de vídeo",
    "loadingFileFix": "A carregar reparação de ficheiro",
    "loadingGameAI": "A carregar a qualidade da IA do jogo",
    "loadingNVAPIModules": "A carregar o módulo NVAPI",
    "loadingAMDADLModules": "Carregando módulo AMDADL",
    "loadingModules": "A carregar módulo"
  },
  "messages": {
    "append": "Adicionar",
    "confirm": "Confirmar",
    "cancel": "Cancelar",
    "default": "Padrão",
    "quickSelect": "Seleção rápida",
    "onoffingame": "Ativar/Desativar monitoramento no jogo:",
    "changeKey": "Clique para alterar o atalho do teclado",
    "clear": "Esvaziar",
    "hotkeyOccupied": "A tecla de atalho já está em uso, defina uma nova!",
    "minimize": "Minimizar",
    "exit": "Sair",
    "export": "Exportar",
    "import": "Importar",
    "screenshot": "Captura de tela",
    "showHideWindow": "Mostrar/Esconder Janela",
    "ingameControlPanel": "Painel de controle no jogo",
    "openOrCloseGameInSettings": "Ativar/Desativar painel de configurações no jogo",
    "openOrCloseGameInSettings2": "Pressione esta tecla de atalho para ativar",
    "openOrCloseGameInSettings3": "Ativar/Desativar monitoramento no jogo",
    "openOrCloseGameInSettings4": "Ativar/Desativar filtro de jogo",
    "startManualRecord": "Iniciar/Parar Gravação Manual de Estatísticas",
    "performanceStatisticsMark": "Marca de estatísticas de desempenho",
    "EnableAIfilter": "É necessário pressionar esta combinação de teclas para ativar o filtro de IA",
    "Start_stop": "Iniciar/Parar Registro Estatístico Manual",
    "pressureTest": "Teste de stress",
    "moduleNotInstalled": "Módulo funcional não instalado",
    "installingPressureTest": "A instalar o módulo de teste de carga...",
    "importFailed": "Importação falhou",
    "gamepp": "GamePP",
    "copyToClipboard": "Copiado para a área de transferência"
  },
  "home": {
    "homeTitle": "Início",
    "hardwareInfo": "Informações de hardware",
    "functionIntroduction": "Funcionalidades",
    "fixedToNav": "Fixar na barra de navegação",
    "cancelFixedToNav": "Desafixar da barra de navegação",
    "hardwareInfoLoading": "Carregando informações de hardware...",
    "performanceStatistics": "Estatísticas de Desempenho",
    "updateNow": "Atualizar agora",
    "recentRun": "Atividade Recente",
    "resolution": "Resolução:",
    "duration": "Duração:",
    "gameFilter": "Filtro de jogo",
    "gameFilterHasAccompany": "O filtro do jogo está agora ativo",
    "gameFilterHasAccompany2": "Os usuários jogam jogos como Cyberpunk, APEX e Hogwarts Legacy",
    "currentList": "Itens de monitoramento na lista atual",
    "moreFunction": "Benchmark, teste de estresse, monitoramento de desktop e mais recursos estão em desenvolvimento.",
    "newVersion": "Nova versão disponível !",
    "discoverUpdate": "Atualização encontrada!",
    "downloading": "Transferência em andamento",
    "retry": "Tentar novamente",
    "erhaAI": "2HaAI",
    "recordingmodule": "Esta funcionalidade depende do módulo de gravação",
    "superPower": "Modo Ultra",
    "autoRecord": "Registre automaticamente momentos de kill no jogo e salve facilmente os melhores momentos",
    "externalDevice": "Iluminação Dinâmica de Periféricos",
    "linkage": "Acionar cenas de kill nos jogos e exibi-las através de dispositivos periféricos conectados",
    "AI": "Teste de desempenho de IA",
    "test": "Teste modelos de IA com GPU e veja a pontuação de desempenho de IA",
    "supportedGames": "Jogos compatíveis",
    "games": "Naraka: Bladepoint, Delta Force, FragPunk, League of Legends, PUBG, Valorant, The Finals, APEX Legends, COD20",
    "videoRecording": "Gravação de vídeo",
    "videoRecording2": "Função de gravação de vídeo baseada no OBS, suporta ajustar o bitrate do vídeo e a taxa de quadros (FPS) para atender a diferentes necessidades de qualidade e fluidez; também suporta \"Replay Instantâneo\", pressione a tecla de atalho para salvar momentos marcantes a qualquer momento!",
    "addOne": "Obter gratuitamente",
    "gamePlatform": "Plataforma de jogos",
    "goShop": "Ir para a página da loja",
    "receiveDeadline": "Prazo de Reivindicação Posterior",
    "2Ai": "2 Risos AI",
    "questionDesc": "Descrição do problema",
    "inputYourQuestion": "Por favor, insira aqui as suas sugestões ou comentários。",
    "uploadLimit": "Carregar até 3 imagens locais no formato JPG/PNG/BMP",
    "email": "E-mail",
    "contactWay": "Informações de contato",
    "qqNumber": "Número QQ (opcional)",
    "submit": "Submeter"
  },
  "hardwareInfo": {
    "hardwareOverview": "Visão Geral de Hardware",
    "copyAllHardwareInfo": "Copiar todas as informações de hardware",
    "processor": "Processador",
    "coreCount": "Núcleos:",
    "threadCount": "Número de Threads:",
    "currentFrequency": "Frequência Atual:",
    "currentVoltage": "Voltagem atual:",
    "copy": "Copiar",
    "releaseDate": "Data de lançamento",
    "codeName": "Nome de código",
    "thermalDesignPower": "Potência de Design Térmico",
    "maxTemperature": "Temperatura Máxima",
    "graphicsCard": "Placa Gráfica",
    "brand": "Marca:",
    "streamProcessors": "Processador de fluxo:",
    "Videomemory": "Memória de vídeo:",
    "busSpeed": "Velocidade do Barramento",
    "driverInfo": "Informações do driver",
    "driverInstallDate": "Data de Instalação do Driver",
    "hardwareID": "ID de hardware",
    "motherboard": "Placa-mãe",
    "chipGroup": "Conjunto de chips:",
    "BIOSDate": "Data BIOS",
    "BIOSVersion": "Versão BIOS",
    "PCIESlots": "slot PCIe",
    "PCIEVersion": "Versão PCIe suportada",
    "memory": "Memória",
    "memoryBarCount": "Quantidade:",
    "totalSize": "Tamanho:",
    "channelCount": "Canal:",
    "Specificmodel": "Modelo específico",
    "Pellet": "Fabricante de Partículas",
    "memoryBarEquivalentFrequency": "Frequência Efetiva da Memória:",
    "hardDisk": "Disco rígido",
    "hardDiskCount": "Quantidade de discos rígidos:",
    "actualCapacity": "Capacidade Real",
    "type": "Tipo",
    "powerOnTime": "Tempo de ligação",
    "powerOnCount": "Ciclos de Alimentação",
    "SSDRemainingLife": "Vida útil restante do SSD",
    "partitionInfo": "Informação de partição",
    "hardDiskController": "Controlador de Disco Rígido",
    "driverNumber": "Número de Série do Disco",
    "display": "Monitor",
    "refreshRate": "Taxa de atualização:",
    "screenSize": "Tamanho da tela:",
    "inches": "Polegadas",
    "productionDate": "Data de Produção",
    "supportRefreshRate": "Taxa de atualização compatível",
    "screenLongAndShort": "Tamanho da tela",
    "systemInfo": "Informações do Sistema",
    "version": "Versão",
    "systemInstallDate": "Data de Instalação do Sistema",
    "systemBootTime": "Hora de Início Atual",
    "systemRunTime": "Tempo de execução",
    "Poccupied": "P Uso",
    "Eoccupied": "E ocupado",
    "occupied": "Ocupado",
    "temperature": "Temperatura",
    "Pfrequency": "P Frequência",
    "Efrequency": "Frequência E",
    "thermalPower": "Potência Térmica",
    "frequency": "Frequência",
    "current": "Atual",
    "noData": "Sem dados",
    "loadHwinfo_SDK": "Falha ao carregar Hwinfo_SDK.dll, impossível ler os dados de hardware/sensor",
    "loadHwinfo_SDK_reason": "Possíveis causas deste problema:",
    "reason": "Motivo",
    "BlockIntercept": "Bloqueado pelo software antivírus, por exemplo: 2345 Software Antivírus (Processo de Defesa Ativa 2345, Processo de Defesa Ativa McAfee)",
    "solution": "Solução:",
    "solution1": "Após fechar e desinstalar os processos relacionados, reinicie o GamePP",
    "solution2": "Desconecte os dispositivos associados e reinicie o GamePP",
    "RestartGamePP": "Desconecte o controlador, espere a resposta do dispositivo e reinicie o GamePP",
    "HWINFOcannotrun": "Hwinfo não pode ser executado corretamente",
    "downloadHWINFO": "Baixar Hwinfo",
    "openHWINFO": "Após abrir o Hwinfo, clicar em RUN permite abri-lo normalmente?",
    "hardwareDriverProblem": "Problemas do driver de hardware",
    "checkHardwareManager": "Verifique o gerenciador de hardware para garantir que os drivers da placa-mãe e da placa de vídeo estejam instalados corretamente",
    "systemProblem": "Problemas no sistema, por exemplo: Usar ferramentas de ativação como Baofeng ou Xiaoma pode causar falhas no carregamento dos drivers e impedir a instalação automática das atualizações do sistema Windows 7",
    "reinstallSystem": "Reinstale o sistema para ativá-lo, Baixe e instale o WIN7: Atualização *********",
    "Windows7": "Windows 7: Instale o patch SHA-256, Windows 10: Ative usando certificado digital, Windows 11 Versão prévia: Desative a integridade da memória",
    "ViolenceActivator": "Se você utilizou ferramentas de ativação não autorizadas como Xiaoma, por favor repare ou reinstale o sistema",
    "MultipleGraphicsCardDrivers": "Estão instalados controladores de placas gráficas de marcas diferentes no computador, por exemplo os controladores AMD e Nvidia instalados simultaneamente",
    "UninstallUnused": "Reinicie o computador após desinstalar os drivers de placa gráfica desnecessários",
    "OfficialQgroup": "Nenhuma das razões acima se aplica. Junte-se ao nosso grupo oficial do QQ: 908287288 (Grupo 5) para resolver o problema.",
    "ExportHardwareData": "Exportar dados de hardware",
    "D3D": "Uso de D3D",
    "Total": "Uso Total",
    "VRAM": "Utilização de VRAM",
    "VRAMFrequency": "Frequência da VRAM",
    "SensorData": "Dados do sensor",
    "CannotGetSensorData": "Falha ao obter dados do sensor",
    "LoadingHardwareInfo": "Carregando informações de hardware...",
    "ScanTime": "Último scan:",
    "Rescan": "Reescanear",
    "Screenshot": "Screenshot",
    "configCopyed": "Informações de configuração foram copiadas para a área de transferência.",
    "LegalRisks": "Riscos legais potenciais detetados",
    "brandLegalRisks": "A exibição da marca pode envolver riscos legais potenciais",
    "professionalVersion": "Edição Profissional",
    "professionalWorkstationVersion": "Edição Workstation Profissional",
    "familyEdition": "Edição Home",
    "educationEdition": "Edição Educativa",
    "enterpriseEdition": "Edição Enterprise",
    "flagshipEdition": "Edição Premium",
    "familyPremiumEdition": "Edição Familiar Premium",
    "familyStandardEdition": "Edição Padrão para Família",
    "primaryVersion": "Versão Básica",
    "bit": "bit",
    "tempWall": "Parede de temperatura",
    "error": "Erro",
    "screenshotSuccess": "Captura de tela salva com sucesso",
    "atLeastOneData": "Mantenha pelo menos 1 entrada de dados",
    "atMostSixData": "Adicione no máximo 6 entradas de dados",
    "screenNotActivated": "Não ativado"
  },
  "psc": {
    "processCoreAssign": "Alocação de núcleos de processo",
    "CoreAssign": "Alocação de cores:",
    "groupName": "Nome do grupo:",
    "notGameProcess": "Processos Não-Jogo",
    "unNamedProcess": "Grupo sem nome",
    "Group2": "Grupo",
    "selectTheCore": "Selecione o núcleo",
    "controls": "Operação",
    "tips": "Aviso",
    "search": "Pesquisar",
    "shiftOut": "Ejetar",
    "ppValue": "Valor PP",
    "ppDesc": "O valor PP reflete o consumo histórico de recursos de hardware. Quanto maior este valor, maior será o consumo de recursos de hardware.",
    "littletips": "Dica: Mantenha pressionado o processo para arrastá-lo até o grupo à esquerda",
    "warning1": "Selecionar threads de núcleo entre grupos diferentes pode afetar o desempenho. Recomenda-se utilizar núcleos do mesmo grupo.",
    "warning2": "Tem certeza de deixar este nome de grupo vazio?",
    "warning3": "O efeito de alocação do núcleo será inválido após a exclusão. Tem certeza de que deseja excluir este grupo?",
    "allprocess": "Todos os processos",
    "pleaseCheckProcess": "Por favor, selecione o processo",
    "dataSaveDesktop": "Os dados foram guardados no ambiente de trabalho.",
    "createAGroup": "Criar grupo",
    "delGroup": "Excluir grupo",
    "Group": "Grupo",
    "editGroup": "Modificar grupo",
    "groupinfo": "Informações do Grupo",
    "moveOutGrouping": "Remover do grupo",
    "createANewGroup": "Criar novo grupo",
    "unallocatedCore": "Core não atribuído",
    "inactiveProcess": "Processo inativo",
    "importGroupingScheme": "Importar perfil de agrupamento",
    "derivedPacketScheme": "Exportar configuração de grupo",
    "addNowProcess": "Adicionar processo atual em execução",
    "displaySystemProcess": "Exibir processos do sistema",
    "max64": "Máximo de 64 threads selecionáveis",
    "processName": "Nome do processo",
    "chooseCurProcess": "Selecionar processo atual",
    "selectNoProcess": "Nenhum processo selecionado",
    "coreCount": "Núcleos",
    "threadCount": "Threads",
    "process": "Processo",
    "plzInputProcessName": "Digite o nome do processo para adicionar manualmente",
    "has_allocation": "Processos com esquemas de alocação de threads",
    "not_made": "Você não atribuiu núcleos a nenhum processo",
    "startUse": "Ativar Otimização",
    "stopUse": "Desativar Otimização",
    "threadAllocation": "Alocação de Threads",
    "configProcess": "Configuração do processo",
    "selectThread": "Selecionar thread",
    "hyperthreadingState": "Status do Hyper-Threading",
    "open": "Ativado",
    "notYetUnlocked": "Desativado",
    "nonhyperthreading": "Sem Hyper-Threading",
    "intervalSelection": "Seleção de intervalo",
    "invertSelection": "Inverter seleção",
    "description": "Bloqueie os processos do jogo em núcleos CPU específicos, isolando inteligentemente programas em segundo plano. Aumenta efetivamente o limite superior de FPS, estabilizando a taxa de quadros no jogo! Reduz travamentos súbitos e quedas abruptas de frame rate, liberando todo o potencial dos processadores multinúcleos para assegurar uma execução contínua com altas taxas de quadros.",
    "importSuccess": "Importação bem-sucedida",
    "importFailed": "Importação falhou"
  },
  "InGameMonitor": {
    "onoffingame": "Ativar/Desativar Supervisão no Jogo:",
    "InGameMonitor": "Monitoramento no Jogo",
    "CustomMode": "Modo Personalizado",
    "Developing": "Em desenvolvimento...",
    "NewMonitor": "Adicionar item de monitoramento",
    "Data": "Parâmetros",
    "Des": "Nota",
    "Function": "Funcionalidade",
    "Editor": "Editar",
    "Top": "Fixar no topo",
    "Delete": "Eliminar",
    "Use": "Usar",
    "DragToSet": "Após invocar o painel, você pode arrastar para configurar",
    "MonitorItem": "Item de monitoramento",
    "addMonitorItem": "Adicionar elemento de monitoramento",
    "hide": "Ocultar",
    "show": "Mostrar",
    "generalstyle": "Configurações Gerais",
    "restoredefault": "Restaurar configurações padrão",
    "arrangement": "Disposição",
    "horizontal": "Horizontal",
    "vertical": "Vertical",
    "monitorposition": "Localização de Monitoramento",
    "canquickselectposition": "Selecione rapidamente uma localização no mapa à esquerda",
    "curposition": "Localização Atual:",
    "background": "Fundo",
    "backgroundcolor": "Cor de Fundo:",
    "font": "Fonte",
    "fontStyle": "Estilo da fonte",
    "fontsize": "Tamanho da Fonte:",
    "fontcolor": "Cor da Fonte:",
    "style": "Estilo:",
    "style2": "Estilo",
    "performance": "Desempenho",
    "refreshTime": "Tempo de atualização:",
    "goGeneralSetting": "Ir para Configurações gerais",
    "selectMonitorItem": "Selecionar item de monitoramento",
    "selectedSensor": "Sensor selecionado:",
    "showTitle": "Mostrar título",
    "hideTitle": "Ocultar título",
    "showStyle": "Modo de Exibição:",
    "remarkSize": "Tamanho da nota:",
    "remarkColor": "Cor da Nota:",
    "parameterSize": "Tamanho do parâmetro:",
    "parameterColor": "Cor dos Parâmetros:",
    "lineChart": "Gráfico de Linhas",
    "lineColor": "Cor da linha quebrada:",
    "lineThickness": "Espessura da Linha:",
    "areaHeight": "Altura da Região:",
    "sort": "Ordenar",
    "displacement": "Deslocamento:",
    "up": "Mover para cima",
    "down": "Mover para baixo",
    "bold": "Negrito",
    "stroke": "Contorno",
    "text": "Texto",
    "textLine": "Texto + Gráfico de Linha",
    "custom": "Personalizado",
    "upperLeft": "Superior esquerdo",
    "upper": "Centro Superior",
    "upperRight": "Superior direito",
    "Left": "Centro esquerdo",
    "middle": "Centro",
    "Right": "Centro Direito",
    "lowerLeft": "Na parte inferior esquerda",
    "lower": "Médio Inferior",
    "lowerRight": "Inferior direito",
    "notSupport": "O dispositivo periférico não suporta mostrar ou ocultar com um clique",
    "notSupportRate": "A taxa de retorno não permite alternar exibição/ocultação com clique",
    "notFindSensor": "Sensor não encontrado. Clique para modificar.",
    "monitoring": "Monitoramento",
    "condition": "Condição",
    "bigger": "Maior que",
    "smaller": "Menor que",
    "biggerThan": "Excede o limiar",
    "biggerThanthreshold": "Maior que a porcentagem do limiar",
    "smallerThan": "Menor que o limiar",
    "smallerThanthreshold": "Menos de porcentagem do limiar",
    "biggerPercent": "Porcentagem de Diminuição do Valor Atual",
    "smallerPercent": "Percentagem de aumento do valor atual",
    "replay": "Funcionalidade de Replay Instantâneo",
    "screenshot": "Captura de Tela",
    "text1": "Valor do sensor quando",
    "text2": ", e em",
    "text3": "Tempo de espera",
    "text4": "Se não aparecer um valor mais alto nos segundos especificados, ative imediatamente",
    "text5": "Após cada ativação de replay, atualize o limiar para o valor no momento do disparo para reduzir as ativações frequentes",
    "text6": "Mostrar o limiar atual para acionar o replay",
    "text7": "Mostrar valores do sensor",
    "text8": "Exceder o limiar inicial",
    "text9": "Abaixo do Limiar Inicial",
    "text10": "Contagem de Limiar Inicial",
    "initThreshold": "Limiar inicial",
    "curThreshold": "Limiar atual:",
    "curThreshold2": "Limiar Atual",
    "resetCurThreshold": "Redefinir valor de limiar atual",
    "action": "Ativar Função",
    "times": "vezes",
    "percentage": "Porcentagem",
    "uninstallobs": "Módulo de gravação não descarregado",
    "install": "Baixar",
    "performanceAndAudioMode": "Modo de compatibilidade de desempenho e áudio",
    "isSaving": "A guardar",
    "video_replay": "Replay instantâneo",
    "saved": "Salvo",
    "loadQualitysScheme": "Carregar configuração gráfica",
    "notSet": "Não configurado",
    "mirrorEnable": "O filtro está ativado",
    "canBeTurnedOff": "Voltar",
    "mirrorClosed": "Filtro do jogo desativado",
    "closed": "Fechado",
    "openMirror": "Ativar filtro",
    "wonderfulScenes": "Destaque",
    "VulkanModeHaveProblem": "O modo Vulkan tem problemas de compatibilidade",
    "suggestDxMode": "Recomenda-se alternar para o modo Dx",
    "functionNotSupported": "Funcionalidade não suportada",
    "NotSupported": "Não suportado",
    "gppManualRecording": "GamePP, gravação manual",
    "perfRecordsHaveBeenSaved": "Dados de desempenho guardados",
    "redoClickF8": "Pressione F8 novamente para continuar a gravação.",
    "startIngameMonitor": "Activando a função de monitoramento dentro do jogo",
    "inGameMarkSuccess": "Marcação no jogo foi bem-sucedida",
    "recordingFailed": "Gravação falhou",
    "recordingHasNotDownload": "A função de gravação não foi baixada",
    "hotkeyDetected": "Detetado conflito de teclas de função",
    "plzEditIt": "Por favor, faça as alterações no software e depois utilize-o",
    "onePercentLowFrame": "1% Baixo frame",
    "pointOnePercentLowFrame": "0.1% Baixa taxa de quadros",
    "frameGenerationTime": "Tempo de geração de frame",
    "curTime": "Hora atual",
    "runTime": "Duração da execução",
    "cpuTemp": "Temperatura da CPU",
    "cpuUsage": "Uso da CPU",
    "cpuFreq": "Frequência do CPU",
    "cpuPower": "Potência térmica da CPU",
    "gpuTemp": "Temperatura da GPU",
    "gpuUsage": "Utilização da GPU",
    "gpuPower": "Potência térmica GPU",
    "gpuFreq": "Frequência da GPU",
    "memUsage": "Utilização de memória"
  },
  "LoginArea": {
    "login": "Iniciar sessão",
    "loginOut": "Sair da conta",
    "vipExpire": "Vencido",
    "remaining": "Restante",
    "day": "Céu",
    "openVip": "Ativar assinatura",
    "vipPrivileges": "Privilégios de Membro",
    "rechargeRenewal": "Recarregar e Renovar",
    "Exclusivefilter": "Filtro",
    "configCloudSync": "Configuração da sincronização na nuvem",
    "comingSoon": "Em breve"
  },
  "GameMirror": {
    "filterStatus": "Estado do filtro",
    "filterPlan": "Preset de Filtro",
    "filterShortcut": "Atalhos do filtro",
    "openCloseFilter": "Ativar/Desativar Filtro:",
    "effectDemo": "Demonstração de Efeitos",
    "demoConfig": "Configuração de Demonstração",
    "AiFilter": "Os efeitos do filtro de IA estão sujeitos aos efeitos no jogo",
    "AiFilterFAQ": "Problemas comuns com os filtros AI",
    "gamePPAiFilter": "Filtro AI do GamePP",
    "gamePPAiFilterVip": "Filtro de IA exclusivo para VIP do GamePP, ajusta dinamicamente os parâmetros do filtro com base nos cenários de jogo para otimizar os efeitos visuais e melhorar a experiência de jogo.",
    "AiMingliangTips": "Brilho AI: Recomenda-se utilizar quando a tela do jogo estiver muito escura.",
    "AiBrightTips": "AI Vibrante: Recomendado para uso quando a tela do jogo estiver muito escura.",
    "AiDarkTips": "AI Dimming: Recomendado para uso quando os gráficos do jogo estiverem excessivamente vibrantes.",
    "AiBalanceTips": "Equilíbrio de IA: adequado para a maioria dos cenários de jogo",
    "AiTips": "Dicas: O filtro de IA deve ser usado pressionando a tecla de atalho no jogo",
    "AiFilterUse": "Por favor, utilize dentro do jogo",
    "AiFilterAdjust": "Ajuste do filtro AI através da tecla de atalho",
    "Bright": "Vibrante",
    "Soft": "Suave",
    "Highlight": "Destacar",
    "Film": "Filme",
    "Benq": "BenQ",
    "AntiGlare": "Antirreflexo",
    "HighSaturation": "Alta saturação",
    "Brightness": "Vibrante",
    "Day": "Dia",
    "Night": "Noite",
    "Nature": "Natural",
    "smooth": "Detalhado",
    "elegant": "Discreto",
    "warm": "Tons Quentes",
    "clear": "Claro",
    "sharp": "Nitidez",
    "vivid": "Dinâmico",
    "beauty": "Vibrante",
    "highDefinition": "Alta Definição",
    "AiMingliang": "IA Brilhante",
    "AiBright": "IA Vivid",
    "AiDark": "AI Esmaecido",
    "AiBalance": "Equilíbrio de IA",
    "BrightTips": "O filtro vibrante é adequado para jogos casuais, de ação ou de aventura, aumentando a saturação de cor para tornar os gráficos mais dinâmicos e envolventes.",
    "liangTips": "Recomenda-se utilizar filtros quando画面 do jogo estiver muito escura.",
    "anTips": "Recomenda-se utilizar o filtro quando a tela do jogo estiver muito escura.",
    "jianyiTips": "Recomenda-se o uso do filtro quando os gráficos do jogo estiverem muito vibrantes.",
    "shiTips": "O filtro é adequado para a maioria das cenas de jogo.",
    "shi2Tips": "O filtro é adequado para jogos casuais, de ação ou de aventura, melhora a saturação de cores tornando os gráficos mais vívidos e atraentes.",
    "ruiTips": "Cores suaves do filtro, efeitos de iluminação delicados, adequados para cenas oníricas, aconchegantes ou nostálgicas",
    "qingTips": "Tons brilhantes, alto contraste, detalhes claros e nítidos, ideal para mostrar cenas vivas e bem iluminadas.",
    "xianTips": "Configurações de maior contraste e brilho garantem detalhes claros em cenas escuras sem distorção e visualização confortável em cenas brilhantes.",
    "dianTips": "Valquiria: Aumente moderadamente o brilho e as cores da tela para alcançar uma qualidade visual cinematográfica",
    "benTips": "Reduz o efeito da luz branca, tornando as cenas de jogo totalmente brancas menos ofuscantes",
    "fangTips": "Otimizado para jogos de mundo aberto e aventura, melhora a luminosidade e o contraste para visuais mais nítidos",
    "jiaoTips": "Adequado para jogos de RPG e simulação, tons equilibrados, realismo visual aprimorado",
    "jieTips": "Otimizado para jogos com histórias ricas e emoções sutis, melhorando detalhes e suavidade para visuais mais refinados",
    "jingTips": "Otimizado para jogos de ação e competitivos, melhora a clareza e o contraste para imagens mais nítidas",
    "xiuTips": "Otimizado para jogos de cura e casual, realça os tons quentes e suavidade, criando uma atmosfera mais aconchegante",
    "qihuanTips": "Adequado para cenas com elementos fantásticos e cores vibrantes, intensificando a saturação de cor para gerar impacto visual marcante",
    "shengTips": "Intensifique cores e detalhes para destacar a vivacidade e o realismo da cena,",
    "sheTips": "Adequado a jogos de FPS, puzzles ou aventura, melhora os detalhes e o contraste para aumentar a realidade do mundo do jogo.",
    "she2Tips": "Adequado para jogos de tiro, corrida ou luta, realçando detalhes em alta definição e desempenho dinâmico para intensificar a experiência e os efeitos visuais do jogo",
    "an2Tips": "Melhora a clareza das cenas em ambientes escuros, adequado para cenários escuros ou noturnos.",
    "wenTips": "Adequado para jogos artísticos, de aventura ou de relaxamento, cria tons de cores suaves e efeitos de luz e sombra para adicionar elegância e sensação de acolhimento à cena。",
    "jing2Tips": "Adequado para jogos competitivos, jogos de ritmo musical ou cenários urbanos noturnos, destacando cores vibrantes e efeitos de iluminação,",
    "jing3Tips": "Otimizado para jogos de competição, ação ou fantasia, intensifica o contraste de cores, tornando os visuais mais ricos e dinâmicos.",
    "onlyVipCanUse": "Este filtro só está disponível para utilizadores VIP"
  },
  "GameRebound": {
    "noGame": "Nenhum registro de jogo",
    "noGameRecord": "Ainda não há registros de jogos! Comece uma partida agora!",
    "gameDuration": "Duração do jogo hoje:",
    "gameElectricity": "Consumo Elétrico Diário",
    "degree": "Grau",
    "gameCo2": "Emissão de CO₂ do dia:",
    "gram": "Chave",
    "manualRecord": "Registo manual",
    "recordDuration": "Duração da gravação:",
    "details": "Detalhes",
    "average": "Média",
    "minimum": "Valor mínimo",
    "maximum": "Valor Máximo",
    "occupancyRate": "Utilização",
    "voltage": "Tensão",
    "powerConsumption": "Consumo de Energia",
    "start": "Iniciar:",
    "end": "Encerrar",
    "Gametime": "Duração do jogo:",
    "Compactdata": "Otimização de Dados",
    "FullData": "Dados Completos",
    "PerformanceAnalysis": "Análise de desempenho",
    "PerformanceAnalysis2": "Relatório de evento",
    "HardwareStatus": "Estado do hardware",
    "totalPower": "Consumo Elétrico Total",
    "TotalEmissions": "Emissão Total",
    "PSS": "Nota: Os dados do gráfico abaixo representam valores médios",
    "FrameGenerationTime": "Tempo de Geração de Frames",
    "GameResolution": "Resolução do jogo",
    "FrameGenerationTimeTips": "Este ponto de dados é excepcionalmente alto e, por isso, não foi incluído nas estatísticas",
    "FrameGenerationTimeTips2": "Este ponto de dados é anormalmente baixo e, portanto, não está incluído nas estatísticas",
    "noData": "Nenhum",
    "ProcessorOccupancy": "Utilização da CPU",
    "ProcessorFrequency": "Frequência do processador",
    "ProcessorTemperature": "Temperatura do processador",
    "ProcessorHeatPower": "Potência Térmica do Processador",
    "GraphicsCardOccupancy": "Utilização de D3D na placa gráfica",
    "GraphicsCardOccupancyTotal": "Uso total da placa gráfica",
    "GraphicsCardFrequency": "Frequência GPU",
    "GraphicsCardTemperature": "Temperatura da GPU",
    "GraphicsCardCoreTemperature": "Temperatura do Ponto Quente do Núcleo da Placa Gráfica",
    "GraphicsCardHeatPower": "Potência térmica da GPU",
    "GraphicsCardMemoryTemperature": "Temperatura da Memória GPU",
    "MemoryOccupancy": "Uso da Memória",
    "MemoryTemperature": "Temperatura da Memória",
    "MemoryPageFaults": "Interrupção de paginação de memória",
    "Duration": "Duração",
    "Time": "Tempo",
    "StartStatistics": "Iniciar estatísticas",
    "Mark": "Marca",
    "EndStatistics": "Encerrar estatísticas",
    "LineChart": "Gráfico de Linha",
    "AddPointInGame_m1": "Pressione dentro do jogo",
    "AddPointInGame_m2": "Ponto de marcação adicional",
    "LeftMouse": "Clique com o botão esquerdo para alternar exibir/ocultar, clique direito para alterar a cor",
    "DeleteThisLine": "Excluir esta polilinha",
    "AddCurve": "Adicionar curva",
    "AllCurvesAreHidden": "Todos os gráficos de curvas estão ocultos",
    "ThereAreSamplingData": "Dados de amostragem totais:",
    "Items": "Entrada",
    "StatisticsData": "Estatísticas",
    "electricity": "Consumo de energia",
    "carbonEmission": "Emissões de Carbono",
    "carbonEmissionTips": "Emissões de Dióxido de Carbono (kg) = Consumo Elétrico (kWh) × 0.785",
    "D3D": "Utilização de D3D:",
    "TOTAL": "Uso Total:",
    "Process": "Processo:",
    "L3Cache": "Cache L3:",
    "OriginalFrequency": "Frequência original:",
    "MaximumBoostFrequency": "Turbo Boost Máximo:",
    "DriverVersion": "Versão do driver:",
    "GraphicsCardMemoryBrand": "Marca da memória de vídeo:",
    "Bitwidth": "Largura do barramento",
    "System": "Sistema:",
    "Screen": "Tela",
    "Interface": "Interface:",
    "Channel": "Canal:",
    "Timing": "Sequência:",
    "Capacity": "Capacidade:",
    "Generation": "Álgebra",
    "AddPoint_m1": "Pressione no jogo",
    "AddPoint_m2": "Adicionar ponto de marcação",
    "Hidden": "Ocultado",
    "Totalsampling": "Dados Totais de Amostragem:",
    "edition": "Versão do driver:",
    "MainHardDisk": "Disco Rígido Principal",
    "SetAsStartTime": "Definir como hora de início",
    "SetAsEndTime": "Definir como hora de término",
    "WindowWillBe": "A janela de estatísticas de desempenho estará localizada em",
    "After": "Fechar após",
    "NoLongerPopUpThisGame": "Este jogo não será mais exibido",
    "HideTemperatureReason": "Ocultar motivo da temperatura",
    "HideTemperatureReason2": "Ocultar relatório de eventos",
    "HideOtherReason": "Ocultar Outras Razões",
    "CPUanalysis": "Análise de desempenho da CPU",
    "TemperatureCause": "Causa de temperatura",
    "tempSensorEvent": "Evento do Sensor de Temperatura",
    "NoTemperatureLimitation": "Não foi detectado nenhum throttling de CPU devido a temperatura. Seu sistema de refrigeração é totalmente capaz de atender às exigências deste jogo.",
    "NoTemperatureLimitation2": "Não há eventos de sensor de temperatura, o seu sistema de refrigeração pode perfeitamente lidar com os requisitos deste jogo.",
    "performanceis": "No selecionado",
    "Inside": "Interno,",
    "TheStatisticsTimeOf": "O período estatístico atende às condições de ativação correspondentes. A razão com maior frequência de ativação é",
    "limited": "Percentagem do tempo total devido a limitações de desempenho causadas pela temperatura",
    "SpecificReasons": "Causas específicas e sua proporção nas causas relacionadas à temperatura:",
    "OptimizationSuggestion": "Sugestões de otimização:",
    "CPUtemperature": "A temperatura da CPU está superaquecida. Por favor, verifique/melhore o ambiente de resfriamento da CPU.",
    "CPUoverheat": "CPU sobreaquecida devido à alimentação da placa-mãe. Verifique as configurações relacionadas à placa-mãe ou melhore o ambiente de refrigeração.",
    "OtherReasons": "Outras razões",
    "NoPowerSupplyLimitation": "O desempenho da CPU não está limitado por alimentação/consumo de energia. As configurações de consumo de energia do seu BIOS podem perfeitamente suportar os requisitos deste jogo.",
    "PowerSupplyLimitation": "Devido a limitações de alimentação/consumo de energia",
    "SpecificReasonsInOtherReasons": "Causa específica e sua proporção em outras causas:",
    "PleaseCheckTheMainboard": "Verifique o status da alimentação da placa-mãe ou ajuste as configurações de energia do BIOS para resolver limitações de desempenho da CPU causadas por outros fatores",
    "CPUcoretemperature": "Temperatura do núcleo atingiu Tj,Max e foi limitada",
    "CPUCriticalTemperature": "Temperatura crítica da CPU",
    "CPUCircuitTemperature": "Pacote CPU/Bus Anular limitado pela atingimento de Tj,Max",
    "CPUCircuitCriticalTemperature": "CPU Package/Ring Bus atingiu a temperatura crítica",
    "CPUtemperatureoverheating": "Sobreaquecimento do CPU detectado, acionando redução automática de frequência para diminuir a temperatura e prevenir falhas de hardware",
    "CPUoverheatingtriggered": "A CPU ativará o mecanismo de resfriamento e ajustará voltagem e frequência para reduzir o consumo de energia e a temperatura",
    "CPUPowerSupplyOverheating": "CPU limitado devido a um superaquecimento grave da alimentação da placa-mãe",
    "CPUPowerSupplyLimitation": "CPU limitado devido ao superaquecimento da alimentação da placa-mãe",
    "CPUMaximumPowerLimitation": "O core está sujeito ao limite máximo de consumo de energia",
    "CPUCircuitPowerLimitation": "Pacote de CPU/Bus em anel atingiu o limite de potência",
    "CPUElectricalDesignLimitation": "Ativar restrições de design elétrico (incluindo limite de corrente ICCmax, limite de potência de pico PL4, limitação de tensão SVID, etc.)",
    "CPULongTermPowerLimitation": "Consumo energético prolongado da CPU atingiu o limite",
    "CPULongTermPowerinstantaneous": "O consumo de energia instantâneo da CPU atingiu o limite",
    "CPUPowerLimitation": "Mecanismo de degradação da frequência Turbo da CPU, normalmente restrito pelo BIOS ou software específico",
    "CPUPowerWallLimitation": "Limite de Potência da CPU",
    "CPUcurrentwalllimit": "Limite de Corrente da CPU",
    "AiAgent": "Agente GamePP (AI Agent)",
    "AgentDesc": "Voltar para a página inicial",
    "fnBeta": "Esta funcionalidade encontra-se atualmente na fase de teste por convite. Sua conta do GamePP ainda não recebeu acesso ao teste.",
    "getAIReport": "Obter relatório de IA",
    "waitingAi": "Aguardando a conclusão da geração do Relatório",
    "no15mins": "Duração do jogo inferior a 15 minutos, impossível obter relatório AI válido",
    "timeout": "Solicitação ao servidor expirou",
    "agentId": "ID do Agente:",
    "reDo": "Regenerar relatório",
    "text2": "Agente GamePP: Relatório de análise de IA online, o seguinte conteúdo foi gerado pela IA e é apenas para referência.",
    "amdAiagentTitle": "Agente GamePP: Relatório de Análise AMD Ryzen AI, o seguinte conteúdo foi gerado pela IA e é apenas para referência.",
    "noCurData": "Não há dados atuais disponíveis",
    "dataScreening": "Filtragem de dados",
    "dataScreeningDescription": "Este recurso tem como objetivo excluir estatísticas de dados de períodos de jogo não eficazes, como carregamento do mapa ou tempo ocioso na sala de espera. 0 indica que nenhuma exclusão será realizada.",
    "excessivelyHighParameter": "Parâmetros excessivos",
    "tooLowParameter": "Parâmetros muito baixos",
    "theMaximumValueIs": "Valor máximo é",
    "theMinimumValueIs": "Valor mínimo é",
    "exclude": "Excluir",
    "dataStatisticsAtThatTime": "Estatísticas de dados nesse momento",
    "itHasBeenGenerated": "Geração concluída,",
    "clickToView": "Clique para visualizar",
    "onlineAnalysis": "Análise online",
    "amdRyzenAILocalAnalysis": "AMD Ryzen AI Análise local",
    "useTerms": "Condições de Uso:",
    "term1": "1. Processador Ryzen AI Max ou Processador Ryzen Al 300 Series",
    "term2": "2.Versão do driver AMD NPU",
    "term3": "3. Ativar gráficos integrados",
    "conformsTo": "Conforme",
    "notInLineWith": "Inválido",
    "theVersionIsTooLow": "Versão desatualizada",
    "canNotUseAmdNpu": "Sua configuração não atende aos requisitos. A análise de entidade AMD NPU não pode ser utilizada。",
    "unusable": "Não disponível",
    "downloadTheFile": "Transferir ficheiro",
    "downloadSource": "Fonte de download:",
    "fileSize": "Tamanho do ficheiro: aprox. 8,34 GB",
    "cancelDownload": "Cancelar download",
    "filePath": "Localização do ficheiro",
    "generateAReport": "Gerar Relatório",
    "fileMissing": "Ficheiro faltando. Reinstalação necessária.",
    "downloading": "Transferência em andamento...",
    "theModelConfigurationLoadingFailed": "Não foi possível carregar a configuração do modelo",
    "theModelDirectoryDoesNotExist": "O diretório do modelo não existe",
    "thereIsAMistakeInReasoning": "Erro de dedução",
    "theInputExceedsTheModelLimit": "A entrada excede o limite do modelo",
    "selectModelNotSupport": "O modelo de download selecionado não é suportado",
    "delDirFail": "Falha ao eliminar o diretório do modelo existente",
    "failedCreateModelDir": "Falha ao criar diretório do modelo",
    "modelNotBeenFullyDownload": "O modelo não foi totalmente baixado",
    "agentIsThinking": "O agente Jiajia está a pensar",
    "reasoningModelFile": "Ficheiro de modelo para inferência",
    "modelReasoningTool": "Ferramenta de inferência do modelo"
  },
  "SelectSensor": {
    "DefaultSensor": "Sensor padrão",
    "Change": "Alterar",
    "FanSpeed": "Velocidade do ventilador",
    "MainGraphicsCard": "Placa Gráfica Principal",
    "SetAsMainGraphicsCard": "Definir como GPU principal",
    "GPUTemperature": "Temperatura da GPU",
    "GPUHeatPower": "Consumo térmico da GPU",
    "GPUTemperatureD3D": "Uso de D3D na GPU",
    "GPUTemperatureTOTAL": "Uso total da GPU",
    "GPUTemperatureCore": "Temperatura do Núcleo GPU Hotspot",
    "MotherboardTemperature": "Temperatura da Placa-Mãe",
    "MyAttention": "Meus Favoritos",
    "All": "Todo",
    "Unit": "Unidade:",
    "NoAttention": "Sensores não seguidos",
    "AttentionSensor": "Sensores monitorados (Beta)",
    "GoToAttention": "Ir para o Focus",
    "CancelAttention": "Deixar de seguir",
    "noThisSensor": "Sem sensor",
    "deviceAbout": "Relacionado com periféricos",
    "deviceBattery": "Nível da Bateria do Dispositivo Periférico",
    "testFunction": "Função de teste",
    "mouseEventRate": "Taxa de Polling",
    "relatedWithinTheGame": "Relacionado ao jogo",
    "winAbout": "Sistema",
    "trackDevicesBattery": "Rastrear nível de bateria dos dispositivos periféricos",
    "ingameRealtimeMouseRate": "Taxa de polling do rato atualmente utilizada em tempo real durante os jogos",
    "notfoundDevice": "Não foi encontrado nenhum dispositivo compatível",
    "deviceBatteryNeedMythcool": "Lista de dispositivos com suporte a visualização de bateria: (requer o uso do Myth.Cool)",
    "vkm1mouse": "Rato Valkyrie M1",
    "vkm2mouse": "Valkyrie M2 Mouse",
    "vk99keyboard": "Valkyrie 99 Teclado Magnético de Eixo",
    "logitechProWireless": "Logitech Pro Wireless",
    "logitechProXSUPERLIGHT": "Logitech PRO X SUPERLIGHT",
    "logitechProXSUPERLIGHT2": "Logitech PRO",
    "logitechPro2LIGHTSPEED": "Logitech PRO 2 LIGHTSPEED",
    "steelSeriesArctisGameBuds": "SteelSeries Arctis GameBuds",
    "steelSeriesArctis7PPlus": "SteelSeries Arctis 7P Plus",
    "razerV3": "Razer Valkyrie V3 Edição Profissional",
    "razerV2": "Razer Viper V2 Edição Profissional",
    "wireless": "Sem fio",
    "logitechNeedGhub": "Quando a Logitech não conseguir obter o modelo do dispositivo, é necessário fazer o download do GHUB",
    "chargingInProgress": "Carregamento",
    "inHibernation": "Em modo de suspensão"
  },
  "video": {
    "videoRecord": "Gravação de Vídeo",
    "recordVideo": "Vídeo gravado",
    "scheme": "Perfil",
    "suggestScheme": "Plano Recomendado",
    "text1": "Nesta configuração, o tamanho do vídeo de 1 minuto é de aproximadamente",
    "text2": "Esta função utilizará recursos adicionais do sistema",
    "low": "Baixo",
    "mid": "Início",
    "high": "Alto",
    "1080p": "Nativo",
    "RecordingFPS": "Gravar FPS",
    "bitRate": "Taxa de bits de vídeo",
    "videoResolution": "Resolução de Vídeo",
    "startStopRecord": "Iniciar/Parar Gravação",
    "instantReplay": "Reprodução Instantânea",
    "instantReplayTime": "Duração de reprodução instantânea",
    "showIngame": "Iniciar painel de controle durante o jogo",
    "CaptureMode": "Método de captura",
    "gameWindow": "Janela do Jogo",
    "desktopWindow": "Janela da área de trabalho",
    "fileSavePath": "Caminho de armazenamento de arquivos",
    "selectVideoSavePath": "Selecionar Caminho de Salvamento da Gravação",
    "diskFreeSpace": "Espaço livre no disco rígido:",
    "edit": "Modificar",
    "open": "Abrir",
    "displayMouse": "Mostrar Cursor do Mouse",
    "recordMicrophone": "Gravar microfone",
    "gameGraphics": "Exibição Original do Jogo"
  },
  "Setting": {
    "common": "Geral",
    "personal": "Personalização",
    "messageNotification": "Notificações",
    "sensorReading": "Leitura do Sensor",
    "OLEDscreen": "Proteção contra Burn-in OLED",
    "performanceStatistics": "Estatísticas de Desempenho",
    "shortcut": "Atalho",
    "ingameSetting": "Salvar configurações do jogo",
    "other": "Outro",
    "otherSettings": "Outras Configurações",
    "GeneralSetting": "Configurações Gerais",
    "softwareVersion": "Versão do software",
    "checkForUpdates": "Verificar atualizações",
    "updateNow": "Atualizar agora",
    "currentVersion": "Versão Atual",
    "latestVersion": "Versão mais recente",
    "isLatestVersion": "A versão atual já é a mais recente.",
    "functionModuleUpdate": "Atualização do Módulo de Funcionalidade",
    "alwaysUpdateModules": "Manter todos os módulos funcionais instalados na última versão",
    "lang": "Idioma",
    "bootstrap": "Iniciar automaticamente",
    "powerOn_m1": "Iniciar",
    "powerOn_m2": "Iniciar automaticamente após segundos",
    "defaultDelay": "40 segundos (padrão)",
    "followSystemScale": "Seguir escala do sistema",
    "privacySettings": "Configurações de Privacidade",
    "JoinGamePPPlan": "Junte-se ao Programa de Melhoria da Experiência do Usuário GamePP",
    "personalizedSetting": "Personalização",
    "restoreDefault": "Restaurar configurações padrão",
    "color": "Cor",
    "picture": "Imagem",
    "video": "Vídeo",
    "browse": "Navegar",
    "clear": "Limpar",
    "mp4VideoOrPNGImagesCanBeUploaded": "É possível carregar vídeos MP4 ou imagens PNG",
    "transparency": "Transparência",
    "backgroundColor": "Cor de Fundo",
    "textFont": "Fonte do texto principal",
    "message": "Mensagem",
    "enableInGameNotifications": "Ativar notificações no jogo",
    "messagePosition": "Posição de exibição no jogo",
    "leftTop": "Canto superior esquerdo",
    "leftCenter": "Centro à esquerda",
    "leftBottom": "Canto Inferior Esquerdo",
    "rightTop": "Canto superior direito",
    "rightCenter": "Centro Direito",
    "rightBottom": "Canto inferior direito",
    "noticeContent": "Conteúdo da notificação",
    "gameInjection": "Injeção de Jogo",
    "ingameShow": "Mostrar no jogo",
    "inGameMonitoring": "Monitoramento no jogo",
    "gameFilter": "Filtro de jogo",
    "start": "Iniciar",
    "endMarkStatistics": "Estatísticas de Marca de Fim",
    "readHwinfoFail": "HWINFO Falha ao ler as informações de hardware",
    "dataSaveDesktop": "Dados foram salvos na área de transferência e no arquivo da área de trabalho",
    "TheSensorCacheCleared": "Dados de cache do sensor excluídos",
    "defaultSensor": "Sensor Padrão",
    "setSensor": "Selecionar Sensor",
    "refreshTime": "Hora de atualização dos dados",
    "recommend": "Padrão",
    "sensorMsg": "Quanto mais curto o intervalo de tempo, maior será o consumo de recursos. Selecione com cuidado.",
    "exportData": "Exportar dados",
    "exportHwData": "Exportar dados de informação de hardware",
    "sensorError": "Leitura anormal do sensor",
    "clearCache": "Limpar cache",
    "littleTips": "Dica: O relatório de desempenho será gerado 2 minutos após o início do jogo",
    "disableAutoShow": "Desativar pop-up automático da janela de estatísticas de desempenho",
    "AutoClosePopUpWindow_m1": "A janela de estatísticas de desempenho será fechada automaticamente após o período definido:",
    "AutoClosePopUpWindow_m2": "segundos",
    "abnormalShutdownReport": "Relatório de desligamento anormal",
    "showWeaAndAddress": "Mostrar clima e informações de localização",
    "autoScreenShots": "Capturar automaticamente a tela do jogo ao marcar",
    "keepRecent": "Número de registos recentes a manter:",
    "noLimit": "Ilimitado",
    "enableInGameSettingsSaving": "Ativar Salvamento de Configurações no Jogo",
    "debugMode": "Modo de Depuração",
    "enableDisableDebugMode": "Ativar/desativar modo de depuração",
    "audioCompatibilityMode": "Modo de Compatibilidade de Áudio",
    "quickClose": "Fechamento Rápido",
    "closeTheGameQuickly": "Fechar rapidamente o processo do jogo",
    "cancel": "Cancelar",
    "confirm": "Confirmar",
    "MoveInterval_m1": "O monitoramento do desktop e no jogo se moverá ligeiramente:",
    "MoveInterval_m2": "minutos",
    "text3": "Após sair do jogo, a janela de relatório de desempenho não será exibida, apenas os registros históricos serão mantidos",
    "text5": "Será gerado automaticamente um relatório após desligamento anormal. A ativação desta função consumirá recursos adicionais do sistema.",
    "text6": "O uso de funções de atalho pode entrar em conflito com outros atalhos de jogos, configure com cuidado.",
    "text7": "Defina o atalho do teclado como \"Nenhum\", use a tecla Retrocesso",
    "text8": "Manter o estado dos filtros, monitoramento em jogo e outras funções durante a execução do jogo com base no nome do processo",
    "text9": "Ativado registra continuamente logs de execução; desativado limpa os arquivos de log (Recomenda-se desativar)",
    "text10": "Após ativação, os sensores da placa-mãe não estarão disponíveis para resolver problemas de áudio causados pelo GamePP",
    "text11": "Pressione Alt+F4 duas vezes seguidas para sair rapidamente do jogo atual",
    "text12": "Deseja continuar? Este modo requer reiniciar o GamePP.",
    "openMainUI": "Mostrar o aplicativo",
    "setting": "Configurações",
    "feedback": "Feedback dos Problemas",
    "help": "Ajuda",
    "sensorReadingSetting": "Definições de leitura do sensor",
    "searchlanguage": "Idioma de pesquisa"
  },
  "GamePlusOne": {
    "year": "Ano",
    "month": "Mês",
    "day": "Dia",
    "success": "Sucesso",
    "fail": "Falha",
    "will": "Atual",
    "missedGame": "Jogos perdidos",
    "text1": "Valor, aprox. ￥",
    "text2": "Total de Jogos Obtidos",
    "text3": "Versão",
    "gamevalue": "Valor do Jogo",
    "gamevalue1": "Obter",
    "total": "Total Reclamado",
    "text4": "jogos, economia acumulada",
    "text6": "Produto, Valor",
    "Platformaccountmanagement": "Gestão de contas da plataforma",
    "Missed1": "(Não reclamado)",
    "Received2": "(Recebido com sucesso)",
    "Receivedsoon2": "Disponível agora",
    "Receivedsoon": "Disponível agora",
    "Missed": "Coleta Perdida",
    "Received": "Reclamação bem-sucedida",
    "Getaccount": "Obter conta",
    "Worth": "Valor",
    "Auto": "Automático",
    "Manual": "Manual",
    "Pleasechoose": "Selecione um jogo",
    "Receive": "Obter agora",
    "Selected": "Selecionado",
    "text5": "Jogos",
    "Automatic": "Auto reclamando...",
    "Collecting": "A recolher...",
    "ReceiveTimes": "Retiradas este mês",
    "Thefirst": "n.º",
    "Week": "Semana",
    "weekstotal": "Total de 53 semanas",
    "Return": "Início",
    "Solutionto": "Falha ao vincular conta - Solução",
    "accounts": "Número de contas vinculadas",
    "Addaccount": "Adicionar conta",
    "Clearcache": "Limpar cache",
    "Bindtime": "Tempo de vinculação",
    "Status": "Estado",
    "Normal": "Normal",
    "Invalid": "Inválido",
    "text7": "jogos, economia total acumulada",
    "Yuan": "Yuan",
    "untie": "Desvincular",
    "disable": "Desativar",
    "enable": "Ativar",
    "gamePlatform": "Plataforma de jogos",
    "goStorePage": "Ir para a página da loja",
    "receiveEnd": "Após o prazo limite",
    "loginPlatformAccount": "Conta da plataforma iniciada",
    "waitReceive": "Aguardando resgate",
    "receiveSuccess": "Recebido com sucesso",
    "accountInvalid": "Conta expirada",
    "alreadyOwn": "Já Possuído",
    "networkError": "Anomalia de Rede",
    "noGame": "Sem Jogo Base",
    "manualReceiveInterrupt": "Interrupção da Aquisição Manual",
    "receiving": "Reivindicando",
    "agree": "Eu concordo em participar no programa de reivindicação gratuita da GamePP.",
    "again": "Reivindicar novamente"
  },
  "shutdownTimer": {
    "timedShutdown": "Desligamento programado",
    "currentTime": "Horário Atual:",
    "setCountdown": "Configurar a contagem regressiva",
    "shutdownInSeconds": "Desligar em X segundos",
    "shutdownIn": "Após o desligamento",
    "goingToBe": "será",
    "executionPlan": "Plano de Execução",
    "startTheClock": "Iniciar temporizador",
    "stopTheClock": "Cancelar o plano",
    "isShuttingDown": "Executando plano de desligamento agendado:",
    "noplan": "Nenhum plano de desligamento atual",
    "hour": "Hora",
    "min": "Minuto",
    "sec": "Segundo",
    "ms": "Milissegundo",
    "year": "Ano",
    "month": "Mês",
    "day": "Dia",
    "hours": "Hora"
  },
  "screenshotpage": {
    "screenshot": "Captura de tela",
    "screenshotFormat": "Desenhado especificamente para captura de ecrãs de jogos, suporta a gravação nos formatos JPG/PNG/BMP, permite capturar rapidamente os ecrãs dos jogos e garante saída em alta resolução sem perda de qualidade",
    "Turnon": "Ativar captura automática, a cada",
    "seconds": "Segundo",
    "takeScreenshot": "Executar uma captura de tela automática",
    "screenshotSettings": "Esta configuração não tem efeito quando ativada no jogo",
    "saveGameFilterAndMonitoring": "Salvar os efeitos 'Filtro de jogo' e 'Monitoramento em jogo' na captura de tela",
    "disableScreenshotSound": "Desativar notificação sonora de capturas de tela",
    "imageFormat": "Formato de imagem",
    "recommended": "Recomendar",
    "viewingdetails": "Mantém os detalhes da qualidade da imagem, tamanho moderado, adequado para visualizar detalhes",
    "saveSpace": "Qualidade de imagem compactável, tamanho mínimo, economia de espaço",
    "ultraQuality": "Imagem em ultra alta definição sem compressão, tamanho de arquivo muito grande. Recomendado para jogadores que buscam a melhor qualidade visual nas partidas salvas.",
    "fileSavePath": "Caminho de salvamento do arquivo",
    "hardDiskSpace": "Espaço livre no disco rígido:",
    "minutes": "Minuto"
  },
  "DesktopMonitoring": {
    "desktopMonitoring": "Monitorização do Desktop",
    "SomeSensors": "Recomendamos alguns sensores para monitorização. Pode eliminá-los ou adicioná-los",
    "AddComponent": "Adicionar novo componente",
    "Type": "Tipo",
    "Remarks": "Observação",
    "AssociatedSensor": "Ligar Sensor",
    "Operation": "Operação",
    "Return": "Voltar",
    "TimeSelection": "Seleção de tempo:",
    "Format": "Formato:",
    "Rule": "Regra：",
    "Coordinate": "Coordenadas:",
    "CustomTextContent": "Conteúdo de texto personalizado:",
    "SystemTime": "Hora do sistema",
    "China": "China",
    "Britain": "Reino Unido",
    "America": "Estados Unidos",
    "Russia": "Rússia",
    "France": "França",
    "DateAndTime": "Data e hora",
    "Time": "Tempo",
    "Date": "Data",
    "Week": "Dia da semana",
    "DateAndTimeAndWeek": "Data+Hora+Dia da semana",
    "TimeAndWeek": "Hora+Dia da semana",
    "Hour12": "Formato de 12 horas",
    "Hour24": "formato de 24 horas",
    "SelectSensor": "Selecione o sensor:",
    "AssociatedSensor1": "Conectar o sensor:",
    "SensorUnit": "Unidade do sensor：",
    "Second": "Segundo:",
    "Corner": "Cantos arredondados:",
    "BackgroundColor": "Cor de fundo :",
    "ProgressColor": "Cor da progressão:",
    "Font": "Fonte：",
    "SelectFont": "Selecione a fonte",
    "FontSize": "Tamanho da fonte:",
    "Color": "Cor：",
    "Style": "Estilo:",
    "Bold": "Negrito",
    "Italic": "Itálico",
    "Shadow": "Sombra",
    "ShadowPosition": "Posição da Sombra：",
    "ShadowEffect": "Efeitos de Sombra：",
    "Blur": "Desfoque",
    "ShadowColor": "Cor da sombra：",
    "SelectFromLocalFiles": "Selecione a partir de ficheiros locais:",
    "UploadImageVideo": "Carregar imagens/vídeos",
    "UploadSVGFile": "Carregar arquivo SVG",
    "Width": "Largura:",
    "Height": "Alto: ",
    "Effect": "Efeito:",
    "Rotation": "Rotação:",
    "WhenTheSensorValue": "O valor do sensor é maior que",
    "conditions": "Quando a condição não é cumprida (não há rotação quando o valor do sensor é 0)",
    "Clockwise": "No sentido horário",
    "Counterclockwise": "Anti-horário",
    "QuickRotation": "Rotação rápida",
    "SlowRotation": "Rotação lenta",
    "StopRotation": "Parar a rotação",
    "StrokeColor": "Cor do contorno：",
    "Path": "Caminho",
    "Color1": "Cor",
    "ChangeColor": "Mudar cor",
    "When": "Quando",
    "SensorValue": "Valor do sensor maior ou igual a",
    "SensorValue1": "Valor do sensor menor ou igual a",
    "SensorValue2": "O valor do sensor é igual",
    "MonitoringSettings": "Configurações de monitoramento",
    "RestoreDefault": "Restaurar predefinições",
    "Monitor": "Monitor",
    "AreaSize": "Tamanho da área",
    "Background": "Fundo",
    "ImageVideo": "Imagens/Vídeos",
    "PureColor": "Cor sólida",
    "Select": "Selecione",
    "ImageVideoDisplayMode": "Modo de exibição de imagens/vídeo",
    "Transparency": "Transparência",
    "DisplayPosition": "Mostrar posição",
    "Stretch": "Estirar",
    "Fill": "Preencher",
    "Adapt": "Ajustar",
    "SelectThePosition": "Clique na célula da grelha para selecionar rapidamente a localização",
    "CurrentPosition": "Localização atual:",
    "DragLock": "Bloqueio de Arrastar",
    "LockMonitoringPosition": "Posição do monitor bloqueada (Depois de bloqueado, o monitor não pode ser arrastado)",
    "Unlockinterior": "Permitir arrastar elementos internos",
    "Font1": "Fonte",
    "GameSettings": "Configurações do jogo",
    "CloseDesktopMonitor": "Desative automaticamente o monitoramento do desktop quando o jogo estiver em execução.",
    "OLED": "Proteção contra Queimadura de OLED",
    "Display": "Mostrar",
    "PleaseEnterContent": "Insira o conteúdo",
    "NextStep": "Próximo",
    "Add": "Adicionar",
    "StylesForYou": "Recomendamos alguns estilos de monitorização. Pode selecioná-los e aplicá-los. Mais estilos serão adicionados no futuro.",
    "EditPlan": "Editar perfil",
    "MonitoringStylePlan": "Esquema de estilo de monitoramento",
    "AddDesktopMonitoring": "Adicionar monitorização do ambiente de trabalho",
    "TextLabel": "Rótulo de texto",
    "ImageVideo1": "Imagens, Vídeos",
    "SensorGraphics": "Gráficos do sensor",
    "SensorData": "Dados do sensor",
    "CustomText": "Texto personalizado",
    "DateTime": "Data e hora",
    "Image": "Imagem",
    "Video": "Vídeo",
    "SVG": "SVG",
    "ProgressBar": "Barra de progresso",
    "Graphics": "Gráficos",
    "UploadImage": "Carregar imagem",
    "UploadVideo": "Carregar vídeo",
    "RealTimeMonitoring": "Monitorização em tempo real de temperaturas e utilização da CPU/GPU, personalização de layout com arrastar e largar, definição de estilo personalizado – domine a performance e a estética do desktop",
    "Chart": "Gráfico",
    "Zigzagcolor": "Cor da linha (Início)",
    "Zigzagcolor1": "Cor da linha (Fim)",
    "Zigzagcolor2": "Cor da área do gráfico de linhas (Início)",
    "Zigzagcolor3": "Cor da área no gráfico de linhas (fim)",
    "CustomMonitoring": "Monitorização personalizada"
  }
}
//messageEnd 
 export default pt 