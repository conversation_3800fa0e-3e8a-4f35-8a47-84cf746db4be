<script setup lang="ts">
import shortcut from '@/components/eleCom/shortcut.vue';
</script>

<template>
    <div class="shortcut-box">
        <span class="shortcut-box-title">{{$t('Setting.shortcut')}}</span>
        <ul>
            <li>
                <span>{{$t('video.startStopRecord')}}</span>
                <shortcut style="color: white;" :id="44"></shortcut>
            </li>
            <li>
                <span>{{$t('video.instantReplay')}}</span>
                <shortcut style="color: white;" :id="47"></shortcut>
            </li>
            <li>
                <span>{{$t('video.showIngame')}}</span>
                <shortcut style="color: white;" :id="9"></shortcut>
            </li>
        </ul>
    </div>
</template>

<style scoped lang="scss">
.shortcut-box {
    position: absolute;
    top: 40px;
    left: 17px;
    width: 450px;
    height: 180px;
    border-radius: 4px;
    background: #2B2C37;
    box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.3);
    font-size: 12px;
    padding: 10px;

    .shortcut-box-title {
        color: #ffffff;
    }

    ul {
        display: flex;
        flex-flow: column nowrap;
        flex-direction: column;
        li {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #777777;
            padding: 8px 0;
        }
    }
}
</style>
